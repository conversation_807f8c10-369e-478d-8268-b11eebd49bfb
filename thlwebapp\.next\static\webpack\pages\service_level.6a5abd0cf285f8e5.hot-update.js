"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/service_level",{

/***/ "./components/service_level/SLTable.js":
/*!*********************************************!*\
  !*** ./components/service_level/SLTable.js ***!
  \*********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _ViewDetails__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./ViewDetails */ \"./components/service_level/ViewDetails.jsx\");\n/* harmony import */ var _services_apiConfig__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/services/apiConfig */ \"./services/apiConfig.js\");\n/* harmony import */ var _ReasonsDetails__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./ReasonsDetails */ \"./components/service_level/ReasonsDetails.jsx\");\n/* harmony import */ var _utils_getCookieData__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/utils/getCookieData */ \"./utils/getCookieData.js\");\n/* harmony import */ var _common_NoDataFound__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../common/NoDataFound */ \"./components/common/NoDataFound.jsx\");\n/* harmony import */ var _fluentui_react_components__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @fluentui/react-components */ \"./node_modules/@fluentui/react-components/lib/index.js\");\n/* harmony import */ var react_toastify__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! react-toastify */ \"./node_modules/react-toastify/dist/react-toastify.esm.mjs\");\n/* harmony import */ var js_cookie__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! js-cookie */ \"./node_modules/js-cookie/dist/js.cookie.mjs\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n//\n\nconst SLTable = (param)=>{\n    let { gridcolumnRefs, gridcolumnWidths, setIsTableRendered, checkedStates, customerSLData, userData, selectedProducts, seeAll, recordCount, setRecordsCount, searchBoxContent, slFilters, selectedMasterProductCode, selectedCustomer, toggle, selectedRows, setSelectedRows, isBulkUpdate, setIsBulkUpdate, bulkUpdateData, isOpen, setIsOpen, setBulkUpdateData, masterProducts, bulkDeleteOrdIds, setBulkDeleteOrdIds, setNoDataExists, setShowLoadingMessage, setAllReasonsSubreasons, allReasonsSubreasons, selectedReasons, setSelectedReasons, selectedSubReasons, setSelectedSubReasons, showLoadingMessage } = param;\n    var _checkedStates_columns, _checkedStates_columns1, _checkedStates_columns2, _checkedStates_columns3, _checkedStates_columns4, _checkedStates_columns5, _checkedStates_columns6, _columnTotals_totalValue;\n    _s();\n    let ADCompanyName = (userData === null || userData === void 0 ? void 0 : userData.companyName) || (userData === null || userData === void 0 ? void 0 : userData.ADCompanyName);\n    const [reasonsMasterList, setReasonsMasterList] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [parentReasonList, setParentReasonList] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [reasonsData, setReasonsData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [allSelectedProducts, setAllSelectedProducts] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isHeaderChecked, setIsHeaderChecked] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [columnTotals, setColumnTotals] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const setMapForReasonsParentsAndTheirCorrespondingChildren = ()=>{\n        setParentReasonList((prevList)=>{\n            const parentReasonIds = reasonsMasterList.filter((typeOfReason)=>typeOfReason.parent_id === null).map((typeOfReason)=>typeOfReason);\n            const uniqueParentIds = new Set([\n                ...prevList,\n                ...parentReasonIds\n            ]);\n            return Array.from(uniqueParentIds); // Update the state\n        });\n    };\n    const handleCheckboxChange = (data)=>{\n        setSelectedRows((prevSelected)=>{\n            let updatedSelected;\n            if (prevSelected.includes(data)) {\n                updatedSelected = prevSelected.filter((item)=>item !== data);\n            } else {\n                updatedSelected = [\n                    ...prevSelected,\n                    data\n                ];\n            }\n            const ordIds = updatedSelected.filter((product)=>product.CASES_ADDED_REASONS > 0).map((product)=>product.ORD_ID);\n            setBulkDeleteOrdIds(ordIds.length > 0 ? ordIds : []);\n            return updatedSelected;\n        });\n    };\n    const handleHeaderCheckboxChange = (data)=>{\n        const selectableRows = data.filter((product)=>(product.LOCKED_BY === null || product.LOCKED_BY === \"\") && product.CASES_DIFFERENCE != 0);\n        const ordIds = selectableRows.filter((product)=>product.CASES_ADDED_REASONS > 0).map((product)=>product.ORD_ID);\n        if (ordIds.length > 0) {\n            setBulkDeleteOrdIds(ordIds);\n        } else {\n            setBulkDeleteOrdIds([]);\n        }\n        if (selectedRows.length === selectableRows.length) {\n            // If all rows are selected, deselect all\n            setSelectedRows([]);\n            setIsHeaderChecked(false);\n        } else {\n            // If not all rows are selected, select all\n            setSelectedRows(selectableRows);\n            setIsHeaderChecked(true);\n        }\n    };\n    //#region getReasons\n    const fetchReasonData = async (orderId, customerName)=>{\n        setReasonsData([]);\n        const serverAddress = _services_apiConfig__WEBPACK_IMPORTED_MODULE_3__.apiConfig.serverAddress;\n        try {\n            const serviceLevelReasons = await fetch(\"\".concat(serverAddress, \"serviceLevel/get-service-level-reasons/\").concat(orderId, \"?customerName=\").concat(customerName), {\n                method: \"GET\",\n                headers: {\n                    Accept: \"application/json\",\n                    \"Content-Type\": \"application/json\"\n                },\n                credentials: \"include\"\n            }).then((res)=>{\n                if (res.status === 400) {\n                    react_toastify__WEBPACK_IMPORTED_MODULE_7__.toast.error(\"There was an error with your request. Please check your data and try again.\");\n                    return null;\n                } else if (res.status === 401) {\n                    react_toastify__WEBPACK_IMPORTED_MODULE_7__.toast.error(\"Your session has expired. Please log in again.\");\n                    setTimeout(async ()=>{\n                        const redirectUrl = \"/login?redirect=\".concat(encodeURIComponent(window.location.pathname));\n                        logoutHandler(instance, redirectUrl);\n                    }, 3000);\n                    return null;\n                }\n                if (res.status === 200) {\n                    return res.json();\n                }\n            }) // Ensure you parse the JSON\n            .then((data)=>{\n                if (!isBulkUpdate) {\n                    setReasonsData(data);\n                }\n            });\n        } catch (error) {\n            console.log(\"error in fetching\", error);\n        }\n    };\n    //#endregion\n    //#region getMasterReasons\n    const fetchReasonMaster = async ()=>{\n        // console.log(\"service Level Reasons Master code start\");\n        try {\n            const serverAddress = _services_apiConfig__WEBPACK_IMPORTED_MODULE_3__.apiConfig.serverAddress;\n            const user = (0,_utils_getCookieData__WEBPACK_IMPORTED_MODULE_5__.getCookieData)(\"user\");\n            const serviceLevelReasonsMaster = await fetch(\"\".concat(serverAddress, \"serviceLevel/get-service-level-reasons-master\"), {\n                method: \"GET\",\n                headers: {\n                    Accept: \"application/json\",\n                    \"Content-Type\": \"application/json\",\n                    Authorization: \"Bearer \".concat(user.token)\n                }\n            }).then((res)=>{\n                if (res.status === 400) {\n                    react_toastify__WEBPACK_IMPORTED_MODULE_7__.toast.error(\"There was an error with your request. Please check your data and try again.\");\n                    return null;\n                } else if (res.status === 401) {\n                    react_toastify__WEBPACK_IMPORTED_MODULE_7__.toast.error(\"Your session has expired. Please log in again.\");\n                    setTimeout(()=>{\n                        localStorage.removeItem(\"superUser\");\n                        js_cookie__WEBPACK_IMPORTED_MODULE_8__[\"default\"].remove(\"company\");\n                        js_cookie__WEBPACK_IMPORTED_MODULE_8__[\"default\"].remove(\"ADCompanyName\");\n                        localStorage.removeItem(\"id\");\n                        localStorage.removeItem(\"name\");\n                        localStorage.removeItem(\"role\");\n                        localStorage.removeItem(\"email\");\n                        js_cookie__WEBPACK_IMPORTED_MODULE_8__[\"default\"].remove(\"user\");\n                        js_cookie__WEBPACK_IMPORTED_MODULE_8__[\"default\"].remove(\"theme\");\n                        js_cookie__WEBPACK_IMPORTED_MODULE_8__[\"default\"].remove(\"token\");\n                        const redirectUrl = \"/login?redirect=\".concat(encodeURIComponent(window.location.pathname));\n                        logoutHandler(instance, redirectUrl);\n                    }, 3000);\n                    return null;\n                }\n                if (res.status === 200) {\n                    return res.json();\n                }\n            }) // Ensure you parse the JSON\n            .then((data)=>{\n                setReasonsMasterList(data);\n            });\n        } catch (err) {\n            console.log(\"error in fetching\", err);\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        setIsTableRendered((prev)=>!prev);\n    }, [\n        setIsTableRendered\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        fetchReasonMaster(); // To fetch the master list of reasons\n        setMapForReasonsParentsAndTheirCorrespondingChildren();\n    }, []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const cookieSelectedProducts = (slFilters === null || slFilters === void 0 ? void 0 : slFilters.selectedProducts) || [];\n        const stateSelectedProducts = selectedProducts || [];\n        const combinedSelectedProducts = [\n            ...cookieSelectedProducts.map((product)=>({\n                    label: product.productDescription,\n                    value: product.altFillId\n                })),\n            ...stateSelectedProducts.map((product)=>({\n                    label: product.label,\n                    value: product.value\n                }))\n        ];\n        const customerSLArray = Object.values(customerSLData);\n        let filteredProducts = combinedSelectedProducts.length ? customerSLArray.filter((product)=>combinedSelectedProducts.some((selectedProduct)=>selectedProduct.label === product.PRODUCT_DESCRIPTION && (!searchBoxContent || !!searchBoxContent && product.ORD_NUMBER.toString().includes(searchBoxContent)))) : customerSLArray.filter((product)=>!searchBoxContent || !!searchBoxContent && product.ORD_NUMBER.toString().includes(searchBoxContent));\n        if (selectedMasterProductCode !== \"all\") {\n            filteredProducts = filteredProducts.filter((prod)=>prod.MASTER_PRODUCT_CODE == selectedMasterProductCode);\n        }\n        if (selectedReasons.length > 0) {\n            filteredProducts = filteredProducts.filter((product)=>product.reasons && product.reasons.some((reason)=>selectedReasons.includes(reason.MAIN_REASON_ID)));\n        } else if (selectedSubReasons.length > 0) {\n            filteredProducts = filteredProducts.filter((product)=>product.reasons && product.reasons.some((reason)=>selectedSubReasons.includes(reason.SUB_REASON_ID)));\n        }\n        if (filteredProducts.length > 0) {\n            setAllSelectedProducts(filteredProducts);\n            const visibleRows = filteredProducts.filter((data)=>{\n                if (!seeAll && data.CASES_DIFFERENCE - data.CASES_ADDED_REASONS === 0) {\n                    return false;\n                }\n                return true;\n            });\n            const totals = visibleRows.reduce((acc, data)=>{\n                acc.casesOrdered += data.CASES_ORIGINAL || 0;\n                acc.casesDelivered += data.CASES_DELIVERED || 0;\n                acc.casesDifference += data.CASES_DIFFERENCE || 0;\n                acc.addedReasons += data.CASES_ADDED_REASONS || 0;\n                acc.totalValue += (data.CASE_SIZE || 0) * (data.UNIT_PRICE || 0) * (data.CASES_DIFFERENCE || 0);\n                return acc;\n            }, {\n                casesOrdered: 0,\n                casesDelivered: 0,\n                casesDifference: 0,\n                addedReasons: 0,\n                totalValue: 0\n            });\n            setColumnTotals(totals);\n        }\n        var _filteredProducts_length;\n        setRecordsCount((_filteredProducts_length = filteredProducts === null || filteredProducts === void 0 ? void 0 : filteredProducts.length) !== null && _filteredProducts_length !== void 0 ? _filteredProducts_length : 0);\n    }, [\n        toggle,\n        customerSLData,\n        selectedProducts,\n        slFilters,\n        searchBoxContent,\n        selectedMasterProductCode,\n        selectedReasons,\n        selectedSubReasons\n    ]);\n    const [selectedData, setSelectedData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const handleViewDetailsClick = (data)=>{\n        setSelectedRows([\n            data\n        ]);\n        setIsOpen(true);\n    };\n    var _columnTotals_casesOrdered, _columnTotals_casesDelivered, _columnTotals_casesDifference, _columnTotals_addedReasons, _columnTotals_totalValue1;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"!fontFamily-poppinsregular\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_toastify__WEBPACK_IMPORTED_MODULE_7__.ToastContainer, {\n                limit: 1\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                lineNumber: 326,\n                columnNumber: 7\n            }, undefined),\n            recordCount != 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                className: \"service-level-grid relative table-fixed w-full text-sm !fontFamily-poppinsregular\",\n                cellSpacing: 0,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                    className: \"sticky top-0 z-10 !w-10\",\n                                    ref: gridcolumnRefs.checkboxRef,\n                                    style: {\n                                        left: \"\".concat(0, \"px\")\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-full flex justify-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"checkbox\",\n                                            className: \"w-5 h-5 text-blue border-theme-blue2 rounded accent-skin-primary text-center\",\n                                            disabled: selectedMasterProductCode == \"all\" && masterProducts.length > 2 || selectedCustomer == \"All Customers\",\n                                            onChange: ()=>handleHeaderCheckboxChange(allSelectedProducts),\n                                            checked: isHeaderChecked\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                            lineNumber: 341,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                        lineNumber: 340,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                    lineNumber: 335,\n                                    columnNumber: 15\n                                }, undefined),\n                                checkedStates.columns.depotdate && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                    className: \"sticky top-0 z-10\",\n                                    ref: gridcolumnRefs.depotdate,\n                                    style: {\n                                        left: \"\".concat(gridcolumnWidths.checkboxWidth, \"px\")\n                                    },\n                                    children: \"Depot Date\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                    lineNumber: 357,\n                                    columnNumber: 17\n                                }, undefined),\n                                ADCompanyName == \"Integrated Service Solutions Ltd\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                    className: \"sticky top-0 z-10 !text-center\",\n                                    ref: gridcolumnRefs.serviceCustomers,\n                                    style: {\n                                        left: \"\".concat(gridcolumnWidths.checkboxWidth, \"px\")\n                                    },\n                                    children: [\n                                        \"Service Customers\",\n                                        \" \"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                    lineNumber: 365,\n                                    columnNumber: 69\n                                }, undefined),\n                                checkedStates.columns.weekNo && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                    className: \"sticky top-0  z-10\",\n                                    ref: gridcolumnRefs.weekNo,\n                                    style: {\n                                        left: \"\".concat(ADCompanyName == \"Integrated Service Solutions Ltd\" ? gridcolumnWidths.serviceCustomerswidth : gridcolumnWidths.depotdate, \"px\")\n                                    },\n                                    children: \"Week No\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                    lineNumber: 374,\n                                    columnNumber: 17\n                                }, undefined),\n                                checkedStates.columns.altfill && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                    className: \"sticky top-0  z-10\",\n                                    ref: gridcolumnRefs.altfill,\n                                    style: {\n                                        left: \"\".concat(gridcolumnWidths.weekNo, \"px\")\n                                    },\n                                    children: \"Alt Fill\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                    lineNumber: 385,\n                                    columnNumber: 17\n                                }, undefined),\n                                checkedStates.columns.customer && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                    className: \"!w-28 sticky top-0  z-10 \",\n                                    ref: gridcolumnRefs.customer,\n                                    style: {\n                                        left: \"\".concat(gridcolumnWidths.altfill, \"px\")\n                                    },\n                                    children: \"Customer\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                    lineNumber: 394,\n                                    columnNumber: 17\n                                }, undefined),\n                                checkedStates.columns.salesorder && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                    className: \"sticky top-0  z-10 \",\n                                    ref: gridcolumnRefs.salesorder,\n                                    style: {\n                                        left: \"\".concat(gridcolumnWidths.customer, \"px\")\n                                    },\n                                    children: \"Sales Order\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                    lineNumber: 403,\n                                    columnNumber: 17\n                                }, undefined),\n                                checkedStates.columns.salesOrderId && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                    className: \"sticky top-0  z-10 \",\n                                    ref: gridcolumnRefs.salesOrderId,\n                                    style: {\n                                        left: \"\".concat(gridcolumnWidths.customer, \"px\")\n                                    },\n                                    children: \"Order Det Id\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                    lineNumber: 412,\n                                    columnNumber: 17\n                                }, undefined),\n                                checkedStates.columns.product && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                    className: \"!w-80 sticky top-0  z-10 \",\n                                    ref: gridcolumnRefs.product,\n                                    style: {\n                                        left: \"\".concat(gridcolumnWidths.salesorder, \"px\")\n                                    },\n                                    children: \"Product\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                    lineNumber: 421,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                    className: \"top-0  z-10 !text-center\",\n                                    ref: gridcolumnRefs.casesize,\n                                    style: {\n                                        left: \"\".concat(gridcolumnWidths.product, \"px\")\n                                    },\n                                    children: \"Case Size\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                    lineNumber: 429,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                    className: \"sticky top-0 !text-center\",\n                                    children: \"Cases Ordered\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                    lineNumber: 436,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                    className: \"sticky top-0 !text-center\",\n                                    children: \"Cases Delivered\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                    lineNumber: 437,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                    className: \"sticky top-0 !text-center\",\n                                    children: \"Cases Different\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                    lineNumber: 438,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                    className: \"sticky top-0 !text-center\",\n                                    children: \"Added Reasons\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                    lineNumber: 439,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                    className: \"!w-28 sticky top-0 !text-center\",\n                                    children: \"Order Fulfillment %\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                    lineNumber: 440,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                    className: \"sticky top-0 !text-center\",\n                                    children: \"Unit Price\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                    lineNumber: 443,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                    className: \"sticky top-0 !text-center\",\n                                    children: \"Case Value\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                    lineNumber: 444,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                    className: \"sticky top-0 !text-center\",\n                                    children: \"Total Value\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                    lineNumber: 445,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                    className: \"sticky top-0 !text-center\",\n                                    children: \"Status\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                    lineNumber: 446,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                    className: \"!w-60 relative\",\n                                    children: [\n                                        \"Reason\",\n                                        \" \",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ReasonsDetails__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                            reasonsData: allReasonsSubreasons.reasons,\n                                            selectedReasons: selectedReasons,\n                                            selectedSubReasons: selectedSubReasons,\n                                            setSelectedReasons: setSelectedReasons,\n                                            setSelectedSubReasons: setSelectedSubReasons,\n                                            type: \"reasonsList\",\n                                            seeAll: seeAll\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                            lineNumber: 449,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                    lineNumber: 447,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                    className: \"!w-60 relative\",\n                                    children: [\n                                        \"Sub Reason\",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ReasonsDetails__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                            reasonsData: allReasonsSubreasons.subReasons,\n                                            selectedReasons: selectedReasons,\n                                            selectedSubReasons: selectedSubReasons,\n                                            setSelectedReasons: setSelectedReasons,\n                                            setSelectedSubReasons: setSelectedSubReasons,\n                                            type: \"subReasonsList\",\n                                            seeAll: seeAll\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                            lineNumber: 461,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                    lineNumber: 459,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                    className: \"!w-52\",\n                                    children: \"Comments\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                    lineNumber: 471,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                    className: \"!text-center\",\n                                    children: \"Actions\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                    lineNumber: 472,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                            lineNumber: 334,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                        lineNumber: 333,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                className: \"font-bold bg-[#f3f8ff]\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                        className: \"text-center sticky top-0 font-bold text-sm\",\n                                        style: {\n                                            left: \"\".concat(0, \"px\")\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                        lineNumber: 478,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    (checkedStates === null || checkedStates === void 0 ? void 0 : (_checkedStates_columns = checkedStates.columns) === null || _checkedStates_columns === void 0 ? void 0 : _checkedStates_columns.depotdate) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                        className: \"text-center sticky top-0 font-bold text-sm\",\n                                        style: {\n                                            left: \"\".concat(gridcolumnWidths.checkboxWidth, \"px\")\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                        lineNumber: 483,\n                                        columnNumber: 19\n                                    }, undefined),\n                                    ADCompanyName == \"Integrated Service Solutions Ltd\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                        className: \"text-center sticky top-0 font-bold text-sm\",\n                                        style: {\n                                            left: \"\".concat(gridcolumnWidths.depotDate, \"px\")\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                        lineNumber: 488,\n                                        columnNumber: 71\n                                    }, undefined),\n                                    (checkedStates === null || checkedStates === void 0 ? void 0 : (_checkedStates_columns1 = checkedStates.columns) === null || _checkedStates_columns1 === void 0 ? void 0 : _checkedStates_columns1.weekNo) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                        className: \"sticky top-0 font-bold text-center text-sm\",\n                                        style: {\n                                            left: \"\".concat(gridcolumnWidths.serviceCustomerswidth, \"px\")\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                        lineNumber: 494,\n                                        columnNumber: 19\n                                    }, undefined),\n                                    (checkedStates === null || checkedStates === void 0 ? void 0 : (_checkedStates_columns2 = checkedStates.columns) === null || _checkedStates_columns2 === void 0 ? void 0 : _checkedStates_columns2.altfill) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                        className: \"sticky top-0 font-bold text-sm\",\n                                        style: {\n                                            left: \"\".concat(gridcolumnWidths.weekNo, \"px\")\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                        lineNumber: 500,\n                                        columnNumber: 19\n                                    }, undefined),\n                                    (checkedStates === null || checkedStates === void 0 ? void 0 : (_checkedStates_columns3 = checkedStates.columns) === null || _checkedStates_columns3 === void 0 ? void 0 : _checkedStates_columns3.customer) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                        className: \"sticky top-0 font-bold text-sm\",\n                                        style: {\n                                            left: \"\".concat(gridcolumnWidths.altfill, \"px\")\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                        lineNumber: 506,\n                                        columnNumber: 19\n                                    }, undefined),\n                                    (checkedStates === null || checkedStates === void 0 ? void 0 : (_checkedStates_columns4 = checkedStates.columns) === null || _checkedStates_columns4 === void 0 ? void 0 : _checkedStates_columns4.salesorder) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                        className: \"sticky top-0 font-bold text-sm\",\n                                        style: {\n                                            left: \"\".concat(gridcolumnWidths.customer, \"px\")\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                        lineNumber: 512,\n                                        columnNumber: 19\n                                    }, undefined),\n                                    (checkedStates === null || checkedStates === void 0 ? void 0 : (_checkedStates_columns5 = checkedStates.columns) === null || _checkedStates_columns5 === void 0 ? void 0 : _checkedStates_columns5.salesOrderId) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                        className: \"sticky top-0 font-bold text-sm\",\n                                        style: {\n                                            left: \"\".concat(gridcolumnWidths.customer, \"px\")\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                        lineNumber: 518,\n                                        columnNumber: 19\n                                    }, undefined),\n                                    (checkedStates === null || checkedStates === void 0 ? void 0 : (_checkedStates_columns6 = checkedStates.columns) === null || _checkedStates_columns6 === void 0 ? void 0 : _checkedStates_columns6.product) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                        className: \"sticky top-0 font-bold text-sm\",\n                                        style: {\n                                            left: \"\".concat(gridcolumnWidths.salesorder, \"px\")\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                        lineNumber: 524,\n                                        columnNumber: 19\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                        className: \"text-center top-0 font-bold text-sm\",\n                                        style: {\n                                            left: \"\".concat(gridcolumnWidths.product, \"px\")\n                                        },\n                                        children: \"TOTAL\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                        lineNumber: 529,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                        className: \"!text-center sticky top-0 font-bold text-sm\",\n                                        children: (_columnTotals_casesOrdered = columnTotals.casesOrdered) !== null && _columnTotals_casesOrdered !== void 0 ? _columnTotals_casesOrdered : \"-\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                        lineNumber: 536,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                        className: \"!text-center sticky top-0 font-bold text-sm\",\n                                        children: (_columnTotals_casesDelivered = columnTotals.casesDelivered) !== null && _columnTotals_casesDelivered !== void 0 ? _columnTotals_casesDelivered : \"-\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                        lineNumber: 539,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                        className: \"!text-center sticky top-0 font-bold text-sm\",\n                                        children: (_columnTotals_casesDifference = columnTotals.casesDifference) !== null && _columnTotals_casesDifference !== void 0 ? _columnTotals_casesDifference : \"-\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                        lineNumber: 542,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                        className: \"!text-center sticky top-0 font-bold text-sm\",\n                                        children: (_columnTotals_addedReasons = columnTotals.addedReasons) !== null && _columnTotals_addedReasons !== void 0 ? _columnTotals_addedReasons : \"-\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                        lineNumber: 545,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                        className: \"sticky top-0 font-bold text-sm\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                        lineNumber: 548,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                        className: \"sticky top-0 font-bold text-sm\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                        lineNumber: 549,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                        className: \"sticky top-0 font-bold text-sm\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                        lineNumber: 550,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                        className: \"!text-center sticky top-0 font-bold text-sm\",\n                                        children: ((_columnTotals_totalValue1 = columnTotals.totalValue) !== null && _columnTotals_totalValue1 !== void 0 ? _columnTotals_totalValue1 : \"-\") ? \"\\xa3\".concat((_columnTotals_totalValue = columnTotals.totalValue) === null || _columnTotals_totalValue === void 0 ? void 0 : _columnTotals_totalValue.toFixed(2)) : \"-\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                        lineNumber: 551,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                        className: \"sticky top-0 font-bold text-sm\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                        lineNumber: 557,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                        className: \"sticky top-0 font-bold text-sm\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                        lineNumber: 558,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                        className: \"sticky top-0 font-bold text-sm\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                        lineNumber: 559,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                        className: \"sticky top-0 font-bold text-sm\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                        lineNumber: 560,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                        className: \"sticky top-0 font-bold text-sm\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                        lineNumber: 561,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                lineNumber: 477,\n                                columnNumber: 15\n                            }, undefined),\n                            allSelectedProducts.map((data, index)=>{\n                                var _data_reasons, _checkedStates_columns, _checkedStates_columns1, _checkedStates_columns2, _checkedStates_columns3, _checkedStates_columns4, _checkedStates_columns5, _checkedStates_columns6, _data_SERVICE_LEVEL_PERCENT, _data_UNIT_PRICE, _this, _this1, _data_reasons_, _data_reasons_1, _data_reasons_2;\n                                if (!seeAll && data.CASES_DIFFERENCE - data.CASES_ADDED_REASONS == 0) return;\n                                const tooltip = data.LOCKED_BY ? \"\".concat(data.LOCKED_BY, \" is currently editing this order.\") : data.NEW_LINE_FLAG == 1 ? \"This is an additional order.\" : \"\";\n                                const isSelected = selectedRows === null || selectedRows === void 0 ? void 0 : selectedRows.includes(data);\n                                const rowHighlight = \"\".concat(isSelected ? \"bg-locked-products\" : ((_data_reasons = data.reasons) === null || _data_reasons === void 0 ? void 0 : _data_reasons.length) > 0 && data.CASES_DIFFERENCE - data.CASES_ADDED_REASONS > 0 ? \"bg-needsupdate-status\" : !!data.LOCKED_BY ? \"bg-locked-products\" : data.NEW_LINE_FLAG == 1 ? \"bg-volumechange-status\" : \"\");\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                            className: \"sticky top-0 font-normal text-sm \".concat(rowHighlight ? \"!\".concat(rowHighlight) : \"\"),\n                                            style: {\n                                                left: \"\".concat(0, \"px\")\n                                            },\n                                            title: tooltip,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-full flex justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"checkbox\",\n                                                    title: \"\".concat(data.NEW_LINE_FLAG == 1 ? \"This is an additional order.\" : \"\"),\n                                                    className: \"w-5 h-5 text-blue border-theme-blue2 rounded accent-skin-primary\",\n                                                    onChange: ()=>handleCheckboxChange(data),\n                                                    disabled: selectedMasterProductCode === \"all\" && masterProducts.length > 2 || selectedCustomer === \"All Customers\" || data.LOCKED_BY !== null && data.LOCKED_BY !== \"\" || data.CASES_DIFFERENCE == 0,\n                                                    // disabled={data.CASES_ADDED_REASONS!=0}\n                                                    checked: selectedRows.includes(data)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                                    lineNumber: 599,\n                                                    columnNumber: 23\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                                lineNumber: 598,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                            lineNumber: 591,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        (checkedStates === null || checkedStates === void 0 ? void 0 : (_checkedStates_columns = checkedStates.columns) === null || _checkedStates_columns === void 0 ? void 0 : _checkedStates_columns.depotdate) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                            className: \"sticky top-0 font-normal text-sm \".concat(rowHighlight ? \"!\".concat(rowHighlight) : \"\"),\n                                            style: {\n                                                left: \"\".concat(gridcolumnWidths.checkboxWidth, \"px\")\n                                            },\n                                            title: tooltip,\n                                            children: data.DEPOT_DATE && new Date(data.DEPOT_DATE).toLocaleDateString(\"en-GB\")\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                            lineNumber: 621,\n                                            columnNumber: 21\n                                        }, undefined),\n                                        ADCompanyName == \"Integrated Service Solutions Ltd\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                            className: \"sticky top-0 font-normal text-sm \".concat(rowHighlight ? \"!\".concat(rowHighlight) : \"\"),\n                                            style: {\n                                                left: \"\".concat(gridcolumnWidths.checkboxWidth, \"px\")\n                                            },\n                                            title: tooltip,\n                                            children: data.SERVICE_CUSTOMERS && data.SERVICE_CUSTOMERS\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                            lineNumber: 632,\n                                            columnNumber: 73\n                                        }, undefined),\n                                        (checkedStates === null || checkedStates === void 0 ? void 0 : (_checkedStates_columns1 = checkedStates.columns) === null || _checkedStates_columns1 === void 0 ? void 0 : _checkedStates_columns1.weekNo) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                            className: \"sticky top-0 font-normal text-center text-sm \".concat(rowHighlight ? \"!\".concat(rowHighlight) : \"\"),\n                                            style: {\n                                                left: \"\".concat(gridcolumnWidths.depotdate, \"px\")\n                                            },\n                                            title: tooltip,\n                                            children: data === null || data === void 0 ? void 0 : data.FISCAL_WEEK\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                            lineNumber: 643,\n                                            columnNumber: 21\n                                        }, undefined),\n                                        (checkedStates === null || checkedStates === void 0 ? void 0 : (_checkedStates_columns2 = checkedStates.columns) === null || _checkedStates_columns2 === void 0 ? void 0 : _checkedStates_columns2.altfill) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                            className: \"sticky top-0 font-normal  text-sm \".concat(rowHighlight ? \"!\".concat(rowHighlight) : \"\"),\n                                            style: {\n                                                left: \"\".concat(gridcolumnWidths.weekNo, \"px\")\n                                            },\n                                            title: tooltip,\n                                            children: data.ALTFILID\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                            lineNumber: 654,\n                                            columnNumber: 21\n                                        }, undefined),\n                                        (checkedStates === null || checkedStates === void 0 ? void 0 : (_checkedStates_columns3 = checkedStates.columns) === null || _checkedStates_columns3 === void 0 ? void 0 : _checkedStates_columns3.customer) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                            className: \"sticky top-0 font-normal  text-sm \".concat(rowHighlight ? \"!\".concat(rowHighlight) : \"\"),\n                                            style: {\n                                                left: \"\".concat(gridcolumnWidths.altfill, \"px\")\n                                            },\n                                            title: tooltip,\n                                            children: data.CUSTOMER\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                            lineNumber: 665,\n                                            columnNumber: 21\n                                        }, undefined),\n                                        (checkedStates === null || checkedStates === void 0 ? void 0 : (_checkedStates_columns4 = checkedStates.columns) === null || _checkedStates_columns4 === void 0 ? void 0 : _checkedStates_columns4.salesorder) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                            className: \"sticky top-0 font-normal text-sm \".concat(rowHighlight ? \"!\".concat(rowHighlight) : \"\"),\n                                            style: {\n                                                left: \"\".concat(gridcolumnWidths.customer, \"px\")\n                                            },\n                                            title: tooltip,\n                                            children: data.ORD_NUMBER\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                            lineNumber: 676,\n                                            columnNumber: 21\n                                        }, undefined),\n                                        (checkedStates === null || checkedStates === void 0 ? void 0 : (_checkedStates_columns5 = checkedStates.columns) === null || _checkedStates_columns5 === void 0 ? void 0 : _checkedStates_columns5.salesOrderId) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                            className: \"sticky top-0 font-normal text-sm \".concat(rowHighlight ? \"!\".concat(rowHighlight) : \"\"),\n                                            style: {\n                                                left: \"\".concat(gridcolumnWidths.customer, \"px\")\n                                            },\n                                            title: tooltip,\n                                            children: data.ORD_ID\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                            lineNumber: 687,\n                                            columnNumber: 21\n                                        }, undefined),\n                                        (checkedStates === null || checkedStates === void 0 ? void 0 : (_checkedStates_columns6 = checkedStates.columns) === null || _checkedStates_columns6 === void 0 ? void 0 : _checkedStates_columns6.product) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                            className: \"!w-80 sticky top-0 font-normal text-sm \".concat(rowHighlight ? \"!\".concat(rowHighlight) : \"\"),\n                                            style: {\n                                                left: \"\".concat(gridcolumnWidths.salesorder, \"px\")\n                                            },\n                                            title: tooltip,\n                                            children: data.PRODUCT_DESCRIPTION\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                            lineNumber: 698,\n                                            columnNumber: 21\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                            className: \"top-0 text-center font-normal text-sm \".concat(rowHighlight ? \"!\".concat(rowHighlight) : \"\"),\n                                            style: {\n                                                left: \"\".concat(gridcolumnWidths.product, \"px\")\n                                            },\n                                            title: tooltip,\n                                            children: data.CASE_SIZE\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                            lineNumber: 708,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"!text-center text-sm \".concat(rowHighlight ? \"!\".concat(rowHighlight) : \"\"),\n                                            title: tooltip,\n                                            children: data.CASES_ORIGINAL\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                            lineNumber: 717,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"!text-center text-sm \".concat(rowHighlight ? \"!\".concat(rowHighlight) : \"\"),\n                                            title: tooltip,\n                                            children: data.CASES_DELIVERED\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                            lineNumber: 725,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"!text-center text-sm font-bold \".concat(rowHighlight ? \"!\".concat(rowHighlight) : \"\"),\n                                            title: tooltip,\n                                            children: data.CASES_DIFFERENCE - data.CASES_ADDED_REASONS\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                            lineNumber: 733,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"!text-center text-sm \".concat(rowHighlight ? \"!\".concat(rowHighlight) : \"\"),\n                                            title: tooltip,\n                                            children: data.CASES_ADDED_REASONS\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                            lineNumber: 741,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"!text-center text-sm \".concat(rowHighlight ? \"!\".concat(rowHighlight) : \"\"),\n                                            title: tooltip,\n                                            children: \"\".concat((_data_SERVICE_LEVEL_PERCENT = data.SERVICE_LEVEL_PERCENT) === null || _data_SERVICE_LEVEL_PERCENT === void 0 ? void 0 : _data_SERVICE_LEVEL_PERCENT.toFixed(2), \"%\")\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                            lineNumber: 749,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"!text-center text-sm \".concat(rowHighlight ? \"!\".concat(rowHighlight) : \"\"),\n                                            title: tooltip,\n                                            children: [\n                                                \"\\xa3\",\n                                                (_data_UNIT_PRICE = data.UNIT_PRICE) === null || _data_UNIT_PRICE === void 0 ? void 0 : _data_UNIT_PRICE.toFixed(2)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                            lineNumber: 757,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"!text-center text-sm \".concat(rowHighlight ? \"!\".concat(rowHighlight) : \"\"),\n                                            title: tooltip,\n                                            children: \"\\xa3\".concat((_this = data.CASE_SIZE * data.UNIT_PRICE) === null || _this === void 0 ? void 0 : _this.toFixed(2))\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                            lineNumber: 765,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"!text-center text-sm \".concat(rowHighlight ? \"!\".concat(rowHighlight) : \"\"),\n                                            title: tooltip,\n                                            children: \"\\xa3\".concat((_this1 = data.CASE_SIZE * data.UNIT_PRICE * data.CASES_DIFFERENCE) === null || _this1 === void 0 ? void 0 : _this1.toFixed(2))\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                            lineNumber: 773,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"!text-center text-sm \".concat(rowHighlight ? \"!\".concat(rowHighlight) : \"\"),\n                                            title: tooltip,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"rounded-lg capitalize px-2 py-1 !text-center \".concat(data.ORD_STATUS === \"Cancelled\" ? \"bg-[#ff2929] text-white\" : data.ORD_STATUS === \"Open\" ? \"bg-[#54C5ED] text-white\" : data.ORD_STATUS === \"Invoiced\" ? \"bg-[#FFAE00] text-white\" : data.ORD_STATUS === \"Delivered\" ? \"bg-[#3EAB58] text-white\" : data.ORD_STATUS === \"Picked\" ? \"bg-[#FF6C09] text-white\" : \"bg-qtydiff-status !text-gray-700\" // Default style for any other status\n                                                ),\n                                                title: tooltip,\n                                                children: data.ORD_STATUS.toLowerCase()\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                                lineNumber: 791,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                            lineNumber: 785,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"relative \".concat(rowHighlight ? \"!\".concat(rowHighlight) : \"\"),\n                                            title: tooltip,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: (_data_reasons_ = data.reasons[0]) === null || _data_reasons_ === void 0 ? void 0 : _data_reasons_.MAIN_REASON\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                                    lineNumber: 817,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ReasonsDetails__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                    reasonsData: data.reasons,\n                                                    type: \"reasonsDetails\",\n                                                    seeAll: seeAll\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                                    lineNumber: 818,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                            lineNumber: 810,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"!text-left text-sm capitalize \".concat(rowHighlight ? \"!\".concat(rowHighlight) : \"\"),\n                                            title: tooltip,\n                                            children: (_data_reasons_1 = data.reasons[0]) === null || _data_reasons_1 === void 0 ? void 0 : _data_reasons_1.SUB_REASON\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                            lineNumber: 825,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"!text-left text-sm capitalize \".concat(rowHighlight ? \"!\".concat(rowHighlight) : \"\"),\n                                            title: tooltip,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-full text-truncate2L \",\n                                                children: (_data_reasons_2 = data.reasons[0]) === null || _data_reasons_2 === void 0 ? void 0 : _data_reasons_2.COMMENT\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                                lineNumber: 839,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                            lineNumber: 833,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"!text-center text-sm !bg-[#f3f8ff] \".concat(rowHighlight ? \"!\".concat(rowHighlight) : \"\"),\n                                            title: \"\".concat(!!data.LOCKED_BY ? \"You cannot edit the order while someone is already working on it.\" : \"\"),\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>{\n                                                    handleViewDetailsClick(data);\n                                                    setIsBulkUpdate(false);\n                                                },\n                                                className: \"cursor-pointer\",\n                                                disabled: !!data.LOCKED_BY || data.CASES_DIFFERENCE == 0 || selectedRows.length > 0,\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fluentui_react_components__WEBPACK_IMPORTED_MODULE_9__.Tooltip, {\n                                                    content: \"View Order Details\",\n                                                    relationship: \"label\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        xmlns: \"http://www.w3.org/2000/svg\",\n                                                        viewBox: \"0 0 512 512\",\n                                                        className: \" w-5 h-5 !text-skin-primary \",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            fill: \"currentcolor\",\n                                                            d: \"M480 448c0 17.7-14.3 32-32 32l-256 0c-17.7 0-32-14.3-32-32l0-80-32 0 0 80c0 35.3 28.7 64 64 64l256 0c35.3 0 64-28.7 64-64l0-284.1c0-12.7-5.1-24.9-14.1-33.9L382.1 14.1c-9-9-21.2-14.1-33.9-14.1L192 0c-35.3 0-64 28.7-64 64l0 192 32 0 0-192c0-17.7 14.3-32 32-32l128 0 0 112c0 26.5 21.5 48 48 48l112 0 0 256zm-.5-288L368 160c-8.8 0-16-7.2-16-16l0-111.5c2.8 .7 5.4 2.1 7.4 4.2L475.3 152.6c2.1 2.1 3.5 4.6 4.2 7.4zM283.3 212.7c-6.2-6.2-16.4-6.2-22.6 0s-6.2 16.4 0 22.6L329.4 304 16 304c-8.8 0-16 7.2-16 16s7.2 16 16 16l313.4 0-68.7 68.7c-6.2 6.2-6.2 16.4 0 22.6s16.4 6.2 22.6 0l96-96c6.2-6.2 6.2-16.4 0-22.6l-96-96z\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                                            lineNumber: 875,\n                                                            columnNumber: 27\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                                        lineNumber: 870,\n                                                        columnNumber: 25\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                                    lineNumber: 866,\n                                                    columnNumber: 23\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                                lineNumber: 854,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                            lineNumber: 844,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, index, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                    lineNumber: 590,\n                                    columnNumber: 17\n                                }, undefined);\n                            })\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                        lineNumber: 475,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                lineNumber: 329,\n                columnNumber: 9\n            }, undefined),\n            isOpen && selectedRows.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ViewDetails__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                data: selectedRows,\n                setData: setSelectedRows,\n                setAllSelectedProducts: setAllSelectedProducts,\n                setMapForReasonsParentsAndTheirCorrespondingChildren: setMapForReasonsParentsAndTheirCorrespondingChildren,\n                reasonsMasterList: reasonsMasterList,\n                parentReasonList: parentReasonList,\n                reasonsData: reasonsData,\n                fetchReasonData: fetchReasonData,\n                userData: userData,\n                isOpen: isOpen,\n                setIsOpen: setIsOpen,\n                bulkUpdateData: bulkUpdateData,\n                isBulkUpdate: isBulkUpdate,\n                setReasonsData: setReasonsData,\n                setBulkUpdateData: setBulkUpdateData,\n                setIsHeaderChecked: setIsHeaderChecked,\n                setSelectedRows: setSelectedRows\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                lineNumber: 890,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n        lineNumber: 325,\n        columnNumber: 5\n    }, undefined);\n};\n_s(SLTable, \"vX4vuRMTc2tBY6elYTRITygxPGY=\");\n_c = SLTable;\n/* harmony default export */ __webpack_exports__[\"default\"] = (SLTable);\nvar _c;\n$RefreshReg$(_c, \"SLTable\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevExports = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevExports) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports on update so we can compare the boundary\n                // signatures.\n                module.hot.dispose(function (data) {\n                    data.prevExports = currentExports;\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevExports !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevExports, currentExports)) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevExports !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/service_level/SLTable.js\n"));

/***/ })

});