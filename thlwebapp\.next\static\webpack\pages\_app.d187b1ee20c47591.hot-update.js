"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/_app",{

/***/ "./pages/_app.js":
/*!***********************!*\
  !*** ./pages/_app.js ***!
  \***********************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ App; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _styles_globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/styles/globals.css */ \"./styles/globals.css\");\n/* harmony import */ var _styles_globals_css__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_styles_globals_css__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _utils_auth_msalProvider__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/utils/auth/msalProvider */ \"./utils/auth/msalProvider.jsx\");\n/* harmony import */ var _utils_loaders_loadingContext__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/utils/loaders/loadingContext */ \"./utils/loaders/loadingContext.js\");\n/* harmony import */ var _utils_loaders_overlaySpinner__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/utils/loaders/overlaySpinner */ \"./utils/loaders/overlaySpinner.js\");\n/* harmony import */ var _utils_secureThemeContext__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/utils/secureThemeContext */ \"./utils/secureThemeContext.js\");\n/* harmony import */ var _utils_rolePermissionsContext__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/utils/rolePermissionsContext */ \"./utils/rolePermissionsContext.js\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nfunction App(param) {\n    let { Component, pageProps } = param;\n    var _pageProps_userData;\n    _s();\n    const [isOpen, setIsOpen] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(true);\n    const isOnline = useNetwork();\n    function useNetwork() {\n        if (true) {\n            // Client-side-only code\n            const [isOnline, setNetwork] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(window.navigator.onLine);\n            (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n                window.addEventListener(\"offline\", ()=>setNetwork(window.navigator.onLine));\n                window.addEventListener(\"online\", ()=>setNetwork(window.navigator.onLine));\n            });\n            return isOnline;\n        }\n    }\n    ;\n    const closeModal = ()=>{\n        setIsOpen(false);\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        if (isOnline) {\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                classNam: \"no-connection\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        children: \"No Internet Connection\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\_app.js\",\n                        lineNumber: 36,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"noConnectionAlertBox\", {\n                        isOpen: isOpen,\n                        closeModal: closeModal\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\_app.js\",\n                        lineNumber: 37,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\_app.js\",\n                lineNumber: 35,\n                columnNumber: 7\n            }, this);\n        }\n    }, []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_utils_auth_msalProvider__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_utils_secureThemeContext__WEBPACK_IMPORTED_MODULE_6__.SecureThemeProvider, {\n            initialTheme: (_pageProps_userData = pageProps.userData) === null || _pageProps_userData === void 0 ? void 0 : _pageProps_userData.theme,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_utils_rolePermissionsContext__WEBPACK_IMPORTED_MODULE_7__.PermissionsProvider, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_utils_loaders_loadingContext__WEBPACK_IMPORTED_MODULE_4__.LoadingProvider, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_utils_loaders_overlaySpinner__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {}, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\_app.js\",\n                            lineNumber: 47,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Component, {\n                            ...pageProps\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\_app.js\",\n                            lineNumber: 48,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\_app.js\",\n                    lineNumber: 46,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\_app.js\",\n                lineNumber: 45,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\_app.js\",\n            lineNumber: 44,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\_app.js\",\n        lineNumber: 43,\n        columnNumber: 5\n    }, this);\n}\n_s(App, \"ZN1wroM70TG0wUXJ8uHLTOJ6UUs=\", true);\n_c = App;\nvar _c;\n$RefreshReg$(_c, \"App\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevExports = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevExports) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports on update so we can compare the boundary\n                // signatures.\n                module.hot.dispose(function (data) {\n                    data.prevExports = currentExports;\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevExports !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevExports, currentExports)) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevExports !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./pages/_app.js\n"));

/***/ })

});