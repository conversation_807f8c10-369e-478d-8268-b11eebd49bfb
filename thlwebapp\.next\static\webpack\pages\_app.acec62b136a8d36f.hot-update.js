"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/_app",{

/***/ "./pages/_app.js":
/*!***********************!*\
  !*** ./pages/_app.js ***!
  \***********************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ App; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _styles_globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/styles/globals.css */ \"./styles/globals.css\");\n/* harmony import */ var _styles_globals_css__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_styles_globals_css__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _utils_auth_msalProvider__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/utils/auth/msalProvider */ \"./utils/auth/msalProvider.jsx\");\n/* harmony import */ var _utils_loaders_loadingContext__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/utils/loaders/loadingContext */ \"./utils/loaders/loadingContext.js\");\n/* harmony import */ var _utils_loaders_overlaySpinner__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/utils/loaders/overlaySpinner */ \"./utils/loaders/overlaySpinner.js\");\n/* harmony import */ var _utils_secureThemeContext__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/utils/secureThemeContext */ \"./utils/secureThemeContext.js\");\n/* harmony import */ var _utils_rolePermissionsContext__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/utils/rolePermissionsContext */ \"./utils/rolePermissionsContext.js\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nfunction App(param) {\n    let { Component, pageProps } = param;\n    var _pageProps_userData;\n    _s();\n    const [isOpen, setIsOpen] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(true);\n    const isOnline = useNetwork();\n    function useNetwork() {\n        if (true) {\n            // Client-side-only code\n            const [isOnline, setNetwork] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(window.navigator.onLine);\n            (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n                window.addEventListener(\"offline\", ()=>setNetwork(window.navigator.onLine));\n                window.addEventListener(\"online\", ()=>setNetwork(window.navigator.onLine));\n            });\n            return isOnline;\n        }\n    }\n    ;\n    const closeModal = ()=>{\n        setIsOpen(false);\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        if (isOnline) {\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                classNam: \"no-connection\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        children: \"No Internet Connection\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\_app.js\",\n                        lineNumber: 36,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"noConnectionAlertBox\", {\n                        isOpen: isOpen,\n                        closeModal: closeModal\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\_app.js\",\n                        lineNumber: 37,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\_app.js\",\n                lineNumber: 35,\n                columnNumber: 7\n            }, this);\n        }\n    }, []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_utils_auth_msalProvider__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_utils_secureThemeContext__WEBPACK_IMPORTED_MODULE_6__.SecureThemeProvider, {\n            initialTheme: (_pageProps_userData = pageProps.userData) === null || _pageProps_userData === void 0 ? void 0 : _pageProps_userData.theme,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_utils_rolePermissionsContext__WEBPACK_IMPORTED_MODULE_7__.PermissionsProvider, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_utils_loaders_loadingContext__WEBPACK_IMPORTED_MODULE_4__.LoadingProvider, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_utils_loaders_overlaySpinner__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {}, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\_app.js\",\n                            lineNumber: 47,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Component, {\n                            ...pageProps\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\_app.js\",\n                            lineNumber: 48,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\_app.js\",\n                    lineNumber: 46,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\_app.js\",\n                lineNumber: 45,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\_app.js\",\n            lineNumber: 44,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\_app.js\",\n        lineNumber: 43,\n        columnNumber: 5\n    }, this);\n}\n_s(App, \"ZN1wroM70TG0wUXJ8uHLTOJ6UUs=\", true);\n_c = App;\nvar _c;\n$RefreshReg$(_c, \"App\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevExports = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevExports) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports on update so we can compare the boundary\n                // signatures.\n                module.hot.dispose(function (data) {\n                    data.prevExports = currentExports;\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevExports !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevExports, currentExports)) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevExports !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./pages/_app.js\n"));

/***/ }),

/***/ "./utils/auth/authConfig.js":
/*!**********************************!*\
  !*** ./utils/auth/authConfig.js ***!
  \**********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   BASE_URL: function() { return /* binding */ BASE_URL; },\n/* harmony export */   loginRequest: function() { return /* binding */ loginRequest; },\n/* harmony export */   msalConfig: function() { return /* binding */ msalConfig; }\n/* harmony export */ });\nconst BASE_URL = \"\".concat(\"http://localhost:3000\", \"/login\");\nconst msalConfig = {\n    auth: {\n        clientId: \"bafc3c7b-820b-4d2d-9a96-9162c4b2c78a\",\n        authority: \"https://login.microsoftonline.com/\".concat(\"6d90d24f-9602-49e8-8903-eb86dce9656a\"),\n        redirectUri: \"/\"\n    },\n    cache: {\n        cacheLocation: \"sessionStorage\",\n        storeAuthStateInCookie: false\n    }\n};\nconst loginRequest = {\n    scopes: [\n        \"user.read\"\n    ]\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevExports = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevExports) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports on update so we can compare the boundary\n                // signatures.\n                module.hot.dispose(function (data) {\n                    data.prevExports = currentExports;\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevExports !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevExports, currentExports)) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevExports !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi91dGlscy9hdXRoL2F1dGhDb25maWcuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQU8sTUFBTUEsV0FBVyxHQUFvQyxPQUFqQ0MsdUJBQWdDLEVBQUMsVUFBUTtBQUU3RCxNQUFNRyxhQUFhO0lBQ3hCQyxNQUFNO1FBQ0pDLFVBQVVMLHNDQUFpQztRQUMzQ08sV0FBVyxxQ0FBdUUsT0FBbENQLHNDQUFpQztRQUNqRlMsYUFBYTtJQUNmO0lBQ0FDLE9BQU87UUFDTEMsZUFBZTtRQUNmQyx3QkFBd0I7SUFDMUI7QUFDRixFQUFFO0FBRUssTUFBTUMsZUFBZTtJQUMxQkMsUUFBUTtRQUFDO0tBQVk7QUFDdkIsRUFBRSIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi91dGlscy9hdXRoL2F1dGhDb25maWcuanM/YWM5ZCJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgY29uc3QgQkFTRV9VUkwgPSBgJHtwcm9jZXNzLmVudi5ORVhUX1BVQkxJQ19CQVNFX1VSTH0vbG9naW5gO1xyXG5cclxuZXhwb3J0IGNvbnN0IG1zYWxDb25maWcgPSB7XHJcbiAgYXV0aDoge1xyXG4gICAgY2xpZW50SWQ6IHByb2Nlc3MuZW52Lk5FWFRfUFVCTElDX0NMSUVOVF9JRCxcclxuICAgIGF1dGhvcml0eTogYGh0dHBzOi8vbG9naW4ubWljcm9zb2Z0b25saW5lLmNvbS8ke3Byb2Nlc3MuZW52Lk5FWFRfUFVCTElDX1RFTkFOVF9JRH1gLFxyXG4gICAgcmVkaXJlY3RVcmk6IFwiL1wiLFxyXG4gIH0sXHJcbiAgY2FjaGU6IHtcclxuICAgIGNhY2hlTG9jYXRpb246IFwic2Vzc2lvblN0b3JhZ2VcIiwgLy8gQ2hhbmdlZCBmcm9tIGxvY2FsU3RvcmFnZVxyXG4gICAgc3RvcmVBdXRoU3RhdGVJbkNvb2tpZTogZmFsc2UsXHJcbiAgfSxcclxufTtcclxuXHJcbmV4cG9ydCBjb25zdCBsb2dpblJlcXVlc3QgPSB7XHJcbiAgc2NvcGVzOiBbXCJ1c2VyLnJlYWRcIl0sXHJcbn07XHJcbiJdLCJuYW1lcyI6WyJCQVNFX1VSTCIsInByb2Nlc3MiLCJlbnYiLCJORVhUX1BVQkxJQ19CQVNFX1VSTCIsIm1zYWxDb25maWciLCJhdXRoIiwiY2xpZW50SWQiLCJORVhUX1BVQkxJQ19DTElFTlRfSUQiLCJhdXRob3JpdHkiLCJORVhUX1BVQkxJQ19URU5BTlRfSUQiLCJyZWRpcmVjdFVyaSIsImNhY2hlIiwiY2FjaGVMb2NhdGlvbiIsInN0b3JlQXV0aFN0YXRlSW5Db29raWUiLCJsb2dpblJlcXVlc3QiLCJzY29wZXMiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///./utils/auth/authConfig.js\n"));

/***/ }),

/***/ "./utils/secureThemeContext.js":
/*!*************************************!*\
  !*** ./utils/secureThemeContext.js ***!
  \*************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SecureThemeProvider: function() { return /* binding */ SecureThemeProvider; },\n/* harmony export */   useSecureTheme: function() { return /* binding */ useSecureTheme; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n\nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\nconst SecureThemeContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)();\nconst SecureThemeProvider = (param)=>{\n    let { children, initialTheme = \"#022D71\" } = param;\n    _s();\n    const [themeColor, setThemeColor] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(initialTheme);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    // Initialize theme from session data\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const initializeTheme = async ()=>{\n            try {\n                const apiBase = \"http://localhost:8081\" || 0;\n                // Get theme from session via API call\n                const response = await fetch(\"\".concat(apiBase, \"/api/auth/me\"), {\n                    method: \"GET\",\n                    credentials: \"include\"\n                });\n                if (response.ok) {\n                    const { user } = await response.json();\n                    if (user === null || user === void 0 ? void 0 : user.theme) {\n                        setThemeColor(user.theme);\n                    }\n                }\n            } catch (error) {\n                console.error(\"Error loading theme:\", error);\n            } finally{\n                setIsLoading(false);\n            }\n        };\n        initializeTheme();\n    }, []);\n    // Apply theme to CSS variables\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if ( true && themeColor) {\n            const existingStyleElement = document.getElementById(\"theme-style\");\n            if (existingStyleElement) {\n                existingStyleElement.remove();\n            }\n            const $style = document.createElement(\"style\");\n            $style.id = \"theme-style\";\n            document.head.appendChild($style);\n            // Convert hex to RGB\n            const getRGBColor = (hex, type)=>{\n                let color = hex.replace(/#/g, \"\");\n                var r = parseInt(color.substr(0, 2), 16);\n                var g = parseInt(color.substr(2, 2), 16);\n                var b = parseInt(color.substr(4, 2), 16);\n                return \"--color-\".concat(type, \": \").concat(r, \", \").concat(g, \", \").concat(b, \";\");\n            };\n            const getAccessibleColor = (hex)=>{\n                let color = hex.replace(/#/g, \"\");\n                var r = parseInt(color.substr(0, 2), 16);\n                var g = parseInt(color.substr(2, 2), 16);\n                var b = parseInt(color.substr(4, 2), 16);\n                var yiq = (r * 299 + g * 587 + b * 114) / 1000;\n                return yiq >= 128 ? \"#000000\" : \"#FFFFFF\";\n            };\n            const primaryColor = getRGBColor(themeColor, \"primary\");\n            const textColor = getRGBColor(getAccessibleColor(themeColor), \"a11y\");\n            $style.innerHTML = \":root {\".concat(primaryColor, \" \").concat(textColor, \"}\");\n        }\n    }, [\n        themeColor\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SecureThemeContext.Provider, {\n        value: {\n            themeColor,\n            setThemeColor,\n            isLoading\n        },\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\utils\\\\secureThemeContext.js\",\n        lineNumber: 75,\n        columnNumber: 5\n    }, undefined);\n};\n_s(SecureThemeProvider, \"zU1TgpWkr8xem8az6440BL4hsGI=\");\n_c = SecureThemeProvider;\nconst useSecureTheme = ()=>{\n    _s1();\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(SecureThemeContext);\n    if (!context) {\n        throw new Error(\"useSecureTheme must be used within a SecureThemeProvider\");\n    }\n    return context;\n};\n_s1(useSecureTheme, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nvar _c;\n$RefreshReg$(_c, \"SecureThemeProvider\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevExports = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevExports) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports on update so we can compare the boundary\n                // signatures.\n                module.hot.dispose(function (data) {\n                    data.prevExports = currentExports;\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevExports !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevExports, currentExports)) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevExports !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./utils/secureThemeContext.js\n"));

/***/ })

});