/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "pages/_app";
exports.ids = ["pages/_app"];
exports.modules = {

/***/ "./pages/_app.js":
/*!***********************!*\
  !*** ./pages/_app.js ***!
  \***********************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ App)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _styles_globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/styles/globals.css */ \"./styles/globals.css\");\n/* harmony import */ var _styles_globals_css__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_styles_globals_css__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _utils_auth_msalProvider__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/utils/auth/msalProvider */ \"./utils/auth/msalProvider.jsx\");\n/* harmony import */ var _utils_loaders_loadingContext__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/utils/loaders/loadingContext */ \"./utils/loaders/loadingContext.js\");\n/* harmony import */ var _utils_loaders_overlaySpinner__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/utils/loaders/overlaySpinner */ \"./utils/loaders/overlaySpinner.js\");\n/* harmony import */ var _utils_secureThemeContext__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/utils/secureThemeContext */ \"./utils/secureThemeContext.js\");\n/* harmony import */ var _utils_rolePermissionsContext__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/utils/rolePermissionsContext */ \"./utils/rolePermissionsContext.js\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_utils_auth_msalProvider__WEBPACK_IMPORTED_MODULE_3__]);\n_utils_auth_msalProvider__WEBPACK_IMPORTED_MODULE_3__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\n\n\n\n\n\nfunction App({ Component, pageProps }) {\n    const [isOpen, setIsOpen] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(true);\n    const isOnline = useNetwork();\n    function useNetwork() {\n        if (false) {}\n    }\n    ;\n    const closeModal = ()=>{\n        setIsOpen(false);\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        if (isOnline) {\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                classNam: \"no-connection\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        children: \"No Internet Connection\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\_app.js\",\n                        lineNumber: 36,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"noConnectionAlertBox\", {\n                        isOpen: isOpen,\n                        closeModal: closeModal\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\_app.js\",\n                        lineNumber: 37,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\_app.js\",\n                lineNumber: 35,\n                columnNumber: 7\n            }, this);\n        }\n    }, []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_utils_auth_msalProvider__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_utils_secureThemeContext__WEBPACK_IMPORTED_MODULE_6__.SecureThemeProvider, {\n            initialTheme: pageProps.userData?.theme,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_utils_rolePermissionsContext__WEBPACK_IMPORTED_MODULE_7__.PermissionsProvider, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_utils_loaders_loadingContext__WEBPACK_IMPORTED_MODULE_4__.LoadingProvider, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_utils_loaders_overlaySpinner__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {}, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\_app.js\",\n                            lineNumber: 47,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Component, {\n                            ...pageProps\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\_app.js\",\n                            lineNumber: 48,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\_app.js\",\n                    lineNumber: 46,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\_app.js\",\n                lineNumber: 45,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\_app.js\",\n            lineNumber: 44,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\_app.js\",\n        lineNumber: 43,\n        columnNumber: 5\n    }, this);\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./pages/_app.js\n");

/***/ }),

/***/ "./utils/auth/authConfig.js":
/*!**********************************!*\
  !*** ./utils/auth/authConfig.js ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   BASE_URL: () => (/* binding */ BASE_URL),\n/* harmony export */   loginRequest: () => (/* binding */ loginRequest),\n/* harmony export */   msalConfig: () => (/* binding */ msalConfig)\n/* harmony export */ });\nconst BASE_URL = `${\"http://localhost:3000\"}/login`;\nconst msalConfig = {\n    auth: {\n        clientId: \"bafc3c7b-820b-4d2d-9a96-9162c4b2c78a\",\n        authority: `https://login.microsoftonline.com/${\"6d90d24f-9602-49e8-8903-eb86dce9656a\"}`,\n        redirectUri: \"/\"\n    },\n    cache: {\n        cacheLocation: \"sessionStorage\",\n        storeAuthStateInCookie: false\n    }\n};\nconst loginRequest = {\n    scopes: [\n        \"user.read\"\n    ]\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi91dGlscy9hdXRoL2F1dGhDb25maWcuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQU8sTUFBTUEsV0FBVyxDQUFDLEVBQUVDLHVCQUFnQyxDQUFDLE1BQU0sQ0FBQyxDQUFDO0FBRTdELE1BQU1HLGFBQWE7SUFDeEJDLE1BQU07UUFDSkMsVUFBVUwsc0NBQWlDO1FBQzNDTyxXQUFXLENBQUMsa0NBQWtDLEVBQUVQLHNDQUFpQyxDQUFDLENBQUM7UUFDbkZTLGFBQWE7SUFDZjtJQUNBQyxPQUFPO1FBQ0xDLGVBQWU7UUFDZkMsd0JBQXdCO0lBQzFCO0FBQ0YsRUFBRTtBQUVLLE1BQU1DLGVBQWU7SUFDMUJDLFFBQVE7UUFBQztLQUFZO0FBQ3ZCLEVBQUUiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly90aGx3ZWJhcHAvLi91dGlscy9hdXRoL2F1dGhDb25maWcuanM/YWM5ZCJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgY29uc3QgQkFTRV9VUkwgPSBgJHtwcm9jZXNzLmVudi5ORVhUX1BVQkxJQ19CQVNFX1VSTH0vbG9naW5gO1xyXG5cclxuZXhwb3J0IGNvbnN0IG1zYWxDb25maWcgPSB7XHJcbiAgYXV0aDoge1xyXG4gICAgY2xpZW50SWQ6IHByb2Nlc3MuZW52Lk5FWFRfUFVCTElDX0NMSUVOVF9JRCxcclxuICAgIGF1dGhvcml0eTogYGh0dHBzOi8vbG9naW4ubWljcm9zb2Z0b25saW5lLmNvbS8ke3Byb2Nlc3MuZW52Lk5FWFRfUFVCTElDX1RFTkFOVF9JRH1gLFxyXG4gICAgcmVkaXJlY3RVcmk6IFwiL1wiLFxyXG4gIH0sXHJcbiAgY2FjaGU6IHtcclxuICAgIGNhY2hlTG9jYXRpb246IFwic2Vzc2lvblN0b3JhZ2VcIiwgLy8gQ2hhbmdlZCBmcm9tIGxvY2FsU3RvcmFnZVxyXG4gICAgc3RvcmVBdXRoU3RhdGVJbkNvb2tpZTogZmFsc2UsXHJcbiAgfSxcclxufTtcclxuXHJcbmV4cG9ydCBjb25zdCBsb2dpblJlcXVlc3QgPSB7XHJcbiAgc2NvcGVzOiBbXCJ1c2VyLnJlYWRcIl0sXHJcbn07XHJcbiJdLCJuYW1lcyI6WyJCQVNFX1VSTCIsInByb2Nlc3MiLCJlbnYiLCJORVhUX1BVQkxJQ19CQVNFX1VSTCIsIm1zYWxDb25maWciLCJhdXRoIiwiY2xpZW50SWQiLCJORVhUX1BVQkxJQ19DTElFTlRfSUQiLCJhdXRob3JpdHkiLCJORVhUX1BVQkxJQ19URU5BTlRfSUQiLCJyZWRpcmVjdFVyaSIsImNhY2hlIiwiY2FjaGVMb2NhdGlvbiIsInN0b3JlQXV0aFN0YXRlSW5Db29raWUiLCJsb2dpblJlcXVlc3QiLCJzY29wZXMiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///./utils/auth/authConfig.js\n");

/***/ }),

/***/ "./utils/auth/msalProvider.jsx":
/*!*************************************!*\
  !*** ./utils/auth/msalProvider.jsx ***!
  \*************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _azure_msal_browser__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @azure/msal-browser */ \"@azure/msal-browser\");\n/* harmony import */ var _azure_msal_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @azure/msal-react */ \"@azure/msal-react\");\n/* harmony import */ var _authConfig__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./authConfig */ \"./utils/auth/authConfig.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_4__);\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_azure_msal_browser__WEBPACK_IMPORTED_MODULE_1__, _azure_msal_react__WEBPACK_IMPORTED_MODULE_2__]);\n([_azure_msal_browser__WEBPACK_IMPORTED_MODULE_1__, _azure_msal_react__WEBPACK_IMPORTED_MODULE_2__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n// components/MsalProvider.tsx\n\n\n\n\n\nconst MsalAuthProvider = ({ children })=>{\n    const msalInstance = new _azure_msal_browser__WEBPACK_IMPORTED_MODULE_1__.PublicClientApplication(_authConfig__WEBPACK_IMPORTED_MODULE_3__.msalConfig);\n    const handlePopup = (event)=>{\n        if (event instanceof Event && event.isTrusted) {\n            msalInstance.handlePopupPromise().catch((error)=>{\n                console.error(\"Error handling popup:\", error);\n            });\n        }\n    };\n    react__WEBPACK_IMPORTED_MODULE_4___default().useEffect(()=>{\n        window.addEventListener(_azure_msal_browser__WEBPACK_IMPORTED_MODULE_1__.EventType.LOGIN_SUCCESS, handlePopup);\n        window.addEventListener(_azure_msal_browser__WEBPACK_IMPORTED_MODULE_1__.EventType.ACQUIRE_TOKEN_SUCCESS, handlePopup);\n        return ()=>{\n            window.removeEventListener(_azure_msal_browser__WEBPACK_IMPORTED_MODULE_1__.EventType.LOGIN_SUCCESS, handlePopup);\n            window.removeEventListener(_azure_msal_browser__WEBPACK_IMPORTED_MODULE_1__.EventType.ACQUIRE_TOKEN_SUCCESS, handlePopup);\n        };\n    }, []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_azure_msal_react__WEBPACK_IMPORTED_MODULE_2__.MsalProvider, {\n        instance: msalInstance,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\utils\\\\auth\\\\msalProvider.jsx\",\n        lineNumber: 27,\n        columnNumber: 10\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (MsalAuthProvider);\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./utils/auth/msalProvider.jsx\n");

/***/ }),

/***/ "./utils/loaders/loadingContext.js":
/*!*****************************************!*\
  !*** ./utils/loaders/loadingContext.js ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   LoadingProvider: () => (/* binding */ LoadingProvider),\n/* harmony export */   useLoading: () => (/* binding */ useLoading)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n// context/LoadingContext.js\n\n\nconst LoadingContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)();\nconst LoadingProvider = ({ children })=>{\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(LoadingContext.Provider, {\n        value: {\n            isLoading,\n            setIsLoading\n        },\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\utils\\\\loaders\\\\loadingContext.js\",\n        lineNumber: 10,\n        columnNumber: 5\n    }, undefined);\n};\nconst useLoading = ()=>{\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(LoadingContext);\n    if (!context) {\n        throw new Error(\"useLoading must be used within a LoadingProvider\");\n    }\n    return context;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi91dGlscy9sb2FkZXJzL2xvYWRpbmdDb250ZXh0LmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7OztBQUFBLDRCQUE0Qjs7QUFDdUM7QUFFbkUsTUFBTUksK0JBQWlCSCxvREFBYUE7QUFFN0IsTUFBTUksa0JBQWtCLENBQUMsRUFBRUMsUUFBUSxFQUFFO0lBQzFDLE1BQU0sQ0FBQ0MsV0FBV0MsYUFBYSxHQUFHTCwrQ0FBUUEsQ0FBQztJQUUzQyxxQkFDRSw4REFBQ0MsZUFBZUssUUFBUTtRQUFDQyxPQUFPO1lBQUVIO1lBQVdDO1FBQWE7a0JBQ3ZERjs7Ozs7O0FBR1AsRUFBRTtBQUVLLE1BQU1LLGFBQWE7SUFDeEIsTUFBTUMsVUFBVVYsaURBQVVBLENBQUNFO0lBQzNCLElBQUksQ0FBQ1EsU0FBUztRQUNaLE1BQU0sSUFBSUMsTUFBTTtJQUNsQjtJQUNBLE9BQU9EO0FBQ1QsRUFBRSIsInNvdXJjZXMiOlsid2VicGFjazovL3RobHdlYmFwcC8uL3V0aWxzL2xvYWRlcnMvbG9hZGluZ0NvbnRleHQuanM/OTA5ZiJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBjb250ZXh0L0xvYWRpbmdDb250ZXh0LmpzXHJcbmltcG9ydCBSZWFjdCwgeyBjcmVhdGVDb250ZXh0LCB1c2VDb250ZXh0LCB1c2VTdGF0ZSB9IGZyb20gXCJyZWFjdFwiO1xyXG5cclxuY29uc3QgTG9hZGluZ0NvbnRleHQgPSBjcmVhdGVDb250ZXh0KCk7XHJcblxyXG5leHBvcnQgY29uc3QgTG9hZGluZ1Byb3ZpZGVyID0gKHsgY2hpbGRyZW4gfSkgPT4ge1xyXG4gIGNvbnN0IFtpc0xvYWRpbmcsIHNldElzTG9hZGluZ10gPSB1c2VTdGF0ZShmYWxzZSk7XHJcblxyXG4gIHJldHVybiAoXHJcbiAgICA8TG9hZGluZ0NvbnRleHQuUHJvdmlkZXIgdmFsdWU9e3sgaXNMb2FkaW5nLCBzZXRJc0xvYWRpbmcgfX0+XHJcbiAgICAgIHtjaGlsZHJlbn1cclxuICAgIDwvTG9hZGluZ0NvbnRleHQuUHJvdmlkZXI+XHJcbiAgKTtcclxufTtcclxuXHJcbmV4cG9ydCBjb25zdCB1c2VMb2FkaW5nID0gKCkgPT4ge1xyXG4gIGNvbnN0IGNvbnRleHQgPSB1c2VDb250ZXh0KExvYWRpbmdDb250ZXh0KTtcclxuICBpZiAoIWNvbnRleHQpIHtcclxuICAgIHRocm93IG5ldyBFcnJvcihcInVzZUxvYWRpbmcgbXVzdCBiZSB1c2VkIHdpdGhpbiBhIExvYWRpbmdQcm92aWRlclwiKTtcclxuICB9XHJcbiAgcmV0dXJuIGNvbnRleHQ7XHJcbn07XHJcbiJdLCJuYW1lcyI6WyJSZWFjdCIsImNyZWF0ZUNvbnRleHQiLCJ1c2VDb250ZXh0IiwidXNlU3RhdGUiLCJMb2FkaW5nQ29udGV4dCIsIkxvYWRpbmdQcm92aWRlciIsImNoaWxkcmVuIiwiaXNMb2FkaW5nIiwic2V0SXNMb2FkaW5nIiwiUHJvdmlkZXIiLCJ2YWx1ZSIsInVzZUxvYWRpbmciLCJjb250ZXh0IiwiRXJyb3IiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///./utils/loaders/loadingContext.js\n");

/***/ }),

/***/ "./utils/loaders/overlaySpinner.js":
/*!*****************************************!*\
  !*** ./utils/loaders/overlaySpinner.js ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react_loader_spinner__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-loader-spinner */ \"react-loader-spinner\");\n/* harmony import */ var react_loader_spinner__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react_loader_spinner__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _loadingContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./loadingContext */ \"./utils/loaders/loadingContext.js\");\n// components/OverlaySpinner.js\n\n\n\n\nconst OverlaySpinner = ()=>{\n    const { isLoading } = (0,_loadingContext__WEBPACK_IMPORTED_MODULE_3__.useLoading)();\n    return isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        style: {\n            display: \"flex\",\n            alignItems: \"center\",\n            justifyContent: \"center\",\n            height: \"100vh\",\n            width: \"100vw\",\n            position: \"fixed\",\n            backgroundColor: \"white\",\n            zIndex: 999999\n        },\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_loader_spinner__WEBPACK_IMPORTED_MODULE_2__.ThreeCircles, {\n            color: \"#002D73\",\n            height: 50,\n            width: 50,\n            visible: isLoading,\n            ariaLabel: \"oval-loading\",\n            secondaryColor: \"#0066FF\",\n            strokeWidth: 2,\n            strokeWidthSecondary: 2\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\utils\\\\loaders\\\\overlaySpinner.js\",\n            lineNumber: 21,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\utils\\\\loaders\\\\overlaySpinner.js\",\n        lineNumber: 9,\n        columnNumber: 5\n    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {}, void 0, false);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (OverlaySpinner);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./utils/loaders/overlaySpinner.js\n");

/***/ }),

/***/ "./utils/rolePermissionsContext.js":
/*!*****************************************!*\
  !*** ./utils/rolePermissionsContext.js ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   PermissionsProvider: () => (/* binding */ PermissionsProvider),\n/* harmony export */   usePermissions: () => (/* binding */ usePermissions)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n\n\nconst PermissionsContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)();\nconst PermissionsProvider = ({ children })=>{\n    const [permissions, setPermissions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const updatePermissions = (newPermissions)=>{\n        setPermissions(newPermissions);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(PermissionsContext.Provider, {\n        value: {\n            permissions,\n            updatePermissions\n        },\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\utils\\\\rolePermissionsContext.js\",\n        lineNumber: 13,\n        columnNumber: 5\n    }, undefined);\n};\nconst usePermissions = ()=>{\n    return (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(PermissionsContext);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi91dGlscy9yb2xlUGVybWlzc2lvbnNDb250ZXh0LmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7QUFBNEQ7QUFFNUQsTUFBTUcsbUNBQXFCSCxvREFBYUE7QUFFakMsTUFBTUksc0JBQXNCLENBQUMsRUFBRUMsUUFBUSxFQUFFO0lBQzlDLE1BQU0sQ0FBQ0MsYUFBYUMsZUFBZSxHQUFHTCwrQ0FBUUEsQ0FBQyxDQUFDO0lBRWhELE1BQU1NLG9CQUFvQixDQUFDQztRQUN6QkYsZUFBZUU7SUFDakI7SUFFQSxxQkFDRSw4REFBQ04sbUJBQW1CTyxRQUFRO1FBQUNDLE9BQU87WUFBRUw7WUFBYUU7UUFBa0I7a0JBQ2xFSDs7Ozs7O0FBR1AsRUFBRTtBQUVLLE1BQU1PLGlCQUFpQjtJQUM1QixPQUFPWCxpREFBVUEsQ0FBQ0U7QUFDcEIsRUFBRSIsInNvdXJjZXMiOlsid2VicGFjazovL3RobHdlYmFwcC8uL3V0aWxzL3JvbGVQZXJtaXNzaW9uc0NvbnRleHQuanM/ZjdhYiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBjcmVhdGVDb250ZXh0LCB1c2VDb250ZXh0LCB1c2VTdGF0ZSB9IGZyb20gJ3JlYWN0JztcclxuXHJcbmNvbnN0IFBlcm1pc3Npb25zQ29udGV4dCA9IGNyZWF0ZUNvbnRleHQoKTtcclxuXHJcbmV4cG9ydCBjb25zdCBQZXJtaXNzaW9uc1Byb3ZpZGVyID0gKHsgY2hpbGRyZW4gfSkgPT4ge1xyXG4gIGNvbnN0IFtwZXJtaXNzaW9ucywgc2V0UGVybWlzc2lvbnNdID0gdXNlU3RhdGUoe30pO1xyXG5cclxuICBjb25zdCB1cGRhdGVQZXJtaXNzaW9ucyA9IChuZXdQZXJtaXNzaW9ucykgPT4ge1xyXG4gICAgc2V0UGVybWlzc2lvbnMobmV3UGVybWlzc2lvbnMpO1xyXG4gIH07XHJcblxyXG4gIHJldHVybiAoXHJcbiAgICA8UGVybWlzc2lvbnNDb250ZXh0LlByb3ZpZGVyIHZhbHVlPXt7IHBlcm1pc3Npb25zLCB1cGRhdGVQZXJtaXNzaW9ucyB9fT5cclxuICAgICAge2NoaWxkcmVufVxyXG4gICAgPC9QZXJtaXNzaW9uc0NvbnRleHQuUHJvdmlkZXI+XHJcbiAgKTtcclxufTtcclxuXHJcbmV4cG9ydCBjb25zdCB1c2VQZXJtaXNzaW9ucyA9ICgpID0+IHtcclxuICByZXR1cm4gdXNlQ29udGV4dChQZXJtaXNzaW9uc0NvbnRleHQpO1xyXG59O1xyXG4iXSwibmFtZXMiOlsiY3JlYXRlQ29udGV4dCIsInVzZUNvbnRleHQiLCJ1c2VTdGF0ZSIsIlBlcm1pc3Npb25zQ29udGV4dCIsIlBlcm1pc3Npb25zUHJvdmlkZXIiLCJjaGlsZHJlbiIsInBlcm1pc3Npb25zIiwic2V0UGVybWlzc2lvbnMiLCJ1cGRhdGVQZXJtaXNzaW9ucyIsIm5ld1Blcm1pc3Npb25zIiwiUHJvdmlkZXIiLCJ2YWx1ZSIsInVzZVBlcm1pc3Npb25zIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///./utils/rolePermissionsContext.js\n");

/***/ }),

/***/ "./utils/secureThemeContext.js":
/*!*************************************!*\
  !*** ./utils/secureThemeContext.js ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SecureThemeProvider: () => (/* binding */ SecureThemeProvider),\n/* harmony export */   useSecureTheme: () => (/* binding */ useSecureTheme)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n\n\nconst SecureThemeContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)();\nconst SecureThemeProvider = ({ children, initialTheme = \"#022D71\" })=>{\n    const [themeColor, setThemeColor] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(initialTheme);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    // Initialize theme from session data\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const initializeTheme = async ()=>{\n            try {\n                const apiBase = \"http://localhost:8081\" || 0;\n                // Get theme from session via API call\n                const response = await fetch(`${apiBase}/api/auth/me`, {\n                    method: \"GET\",\n                    credentials: \"include\"\n                });\n                if (response.ok) {\n                    const { user } = await response.json();\n                    if (user?.theme) {\n                        setThemeColor(user.theme);\n                    }\n                }\n            } catch (error) {\n                console.error(\"Error loading theme:\", error);\n            } finally{\n                setIsLoading(false);\n            }\n        };\n        initializeTheme();\n    }, []);\n    // Apply theme to CSS variables\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (false) {}\n    }, [\n        themeColor\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SecureThemeContext.Provider, {\n        value: {\n            themeColor,\n            setThemeColor,\n            isLoading\n        },\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\utils\\\\secureThemeContext.js\",\n        lineNumber: 75,\n        columnNumber: 5\n    }, undefined);\n};\nconst useSecureTheme = ()=>{\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(SecureThemeContext);\n    if (!context) {\n        throw new Error(\"useSecureTheme must be used within a SecureThemeProvider\");\n    }\n    return context;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi91dGlscy9zZWN1cmVUaGVtZUNvbnRleHQuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7OztBQUF1RTtBQUV2RSxNQUFNSSxtQ0FBcUJKLG9EQUFhQTtBQUVqQyxNQUFNSyxzQkFBc0IsQ0FBQyxFQUFFQyxRQUFRLEVBQUVDLGVBQWUsU0FBUyxFQUFFO0lBQ3hFLE1BQU0sQ0FBQ0MsWUFBWUMsY0FBYyxHQUFHUCwrQ0FBUUEsQ0FBQ0s7SUFDN0MsTUFBTSxDQUFDRyxXQUFXQyxhQUFhLEdBQUdULCtDQUFRQSxDQUFDO0lBRTNDLHFDQUFxQztJQUNyQ0MsZ0RBQVNBLENBQUM7UUFDUixNQUFNUyxrQkFBa0I7WUFDdEIsSUFBSTtnQkFDRixNQUFNQyxVQUFVQyx1QkFBb0MsSUFBSTtnQkFDeEQsc0NBQXNDO2dCQUN0QyxNQUFNRyxXQUFXLE1BQU1DLE1BQU0sQ0FBQyxFQUFFTCxRQUFRLFlBQVksQ0FBQyxFQUFFO29CQUNyRE0sUUFBUTtvQkFDUkMsYUFBYTtnQkFDZjtnQkFFQSxJQUFJSCxTQUFTSSxFQUFFLEVBQUU7b0JBQ2YsTUFBTSxFQUFFQyxJQUFJLEVBQUUsR0FBRyxNQUFNTCxTQUFTTSxJQUFJO29CQUNwQyxJQUFJRCxNQUFNRSxPQUFPO3dCQUNmZixjQUFjYSxLQUFLRSxLQUFLO29CQUMxQjtnQkFDRjtZQUNGLEVBQUUsT0FBT0MsT0FBTztnQkFDZEMsUUFBUUQsS0FBSyxDQUFDLHdCQUF3QkE7WUFDeEMsU0FBVTtnQkFDUmQsYUFBYTtZQUNmO1FBQ0Y7UUFFQUM7SUFDRixHQUFHLEVBQUU7SUFFTCwrQkFBK0I7SUFDL0JULGdEQUFTQSxDQUFDO1FBQ1IsSUFBSSxLQUEyQ0ssRUFBRSxFQWdDaEQ7SUFDSCxHQUFHO1FBQUNBO0tBQVc7SUFHZixxQkFDRSw4REFBQ0osbUJBQW1CK0MsUUFBUTtRQUFDQyxPQUFPO1lBQ2xDNUM7WUFDQUM7WUFDQUM7UUFDRjtrQkFDR0o7Ozs7OztBQUdQLEVBQUU7QUFFSyxNQUFNK0MsaUJBQWlCO0lBQzVCLE1BQU1DLFVBQVVyRCxpREFBVUEsQ0FBQ0c7SUFDM0IsSUFBSSxDQUFDa0QsU0FBUztRQUNaLE1BQU0sSUFBSUMsTUFBTTtJQUNsQjtJQUNBLE9BQU9EO0FBQ1QsRUFBRSIsInNvdXJjZXMiOlsid2VicGFjazovL3RobHdlYmFwcC8uL3V0aWxzL3NlY3VyZVRoZW1lQ29udGV4dC5qcz9hOTY3Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IGNyZWF0ZUNvbnRleHQsIHVzZUNvbnRleHQsIHVzZVN0YXRlLCB1c2VFZmZlY3QgfSBmcm9tICdyZWFjdCc7XHJcblxyXG5jb25zdCBTZWN1cmVUaGVtZUNvbnRleHQgPSBjcmVhdGVDb250ZXh0KCk7XHJcblxyXG5leHBvcnQgY29uc3QgU2VjdXJlVGhlbWVQcm92aWRlciA9ICh7IGNoaWxkcmVuLCBpbml0aWFsVGhlbWUgPSAnIzAyMkQ3MScgfSkgPT4ge1xyXG4gIGNvbnN0IFt0aGVtZUNvbG9yLCBzZXRUaGVtZUNvbG9yXSA9IHVzZVN0YXRlKGluaXRpYWxUaGVtZSk7XHJcbiAgY29uc3QgW2lzTG9hZGluZywgc2V0SXNMb2FkaW5nXSA9IHVzZVN0YXRlKHRydWUpO1xyXG5cclxuICAvLyBJbml0aWFsaXplIHRoZW1lIGZyb20gc2Vzc2lvbiBkYXRhXHJcbiAgdXNlRWZmZWN0KCgpID0+IHtcclxuICAgIGNvbnN0IGluaXRpYWxpemVUaGVtZSA9IGFzeW5jICgpID0+IHtcclxuICAgICAgdHJ5IHtcclxuICAgICAgICBjb25zdCBhcGlCYXNlID0gcHJvY2Vzcy5lbnYuTkVYVF9QVUJMSUNfQVBJX0JBU0VfVVJMIHx8ICdodHRwOi8vbG9jYWxob3N0OjgwODEnO1xyXG4gICAgICAgIC8vIEdldCB0aGVtZSBmcm9tIHNlc3Npb24gdmlhIEFQSSBjYWxsXHJcbiAgICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBmZXRjaChgJHthcGlCYXNlfS9hcGkvYXV0aC9tZWAsIHtcclxuICAgICAgICAgIG1ldGhvZDogJ0dFVCcsXHJcbiAgICAgICAgICBjcmVkZW50aWFsczogJ2luY2x1ZGUnLFxyXG4gICAgICAgIH0pO1xyXG5cclxuICAgICAgICBpZiAocmVzcG9uc2Uub2spIHtcclxuICAgICAgICAgIGNvbnN0IHsgdXNlciB9ID0gYXdhaXQgcmVzcG9uc2UuanNvbigpO1xyXG4gICAgICAgICAgaWYgKHVzZXI/LnRoZW1lKSB7XHJcbiAgICAgICAgICAgIHNldFRoZW1lQ29sb3IodXNlci50aGVtZSk7XHJcbiAgICAgICAgICB9XHJcbiAgICAgICAgfVxyXG4gICAgICB9IGNhdGNoIChlcnJvcikge1xyXG4gICAgICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIGxvYWRpbmcgdGhlbWU6JywgZXJyb3IpO1xyXG4gICAgICB9IGZpbmFsbHkge1xyXG4gICAgICAgIHNldElzTG9hZGluZyhmYWxzZSk7XHJcbiAgICAgIH1cclxuICAgIH07XHJcblxyXG4gICAgaW5pdGlhbGl6ZVRoZW1lKCk7XHJcbiAgfSwgW10pO1xyXG5cclxuICAvLyBBcHBseSB0aGVtZSB0byBDU1MgdmFyaWFibGVzXHJcbiAgdXNlRWZmZWN0KCgpID0+IHtcclxuICAgIGlmICh0eXBlb2Ygd2luZG93ICE9PSBcInVuZGVmaW5lZFwiICYmIHRoZW1lQ29sb3IpIHtcclxuICAgICAgY29uc3QgZXhpc3RpbmdTdHlsZUVsZW1lbnQgPSBkb2N1bWVudC5nZXRFbGVtZW50QnlJZChcInRoZW1lLXN0eWxlXCIpO1xyXG4gICAgICBpZiAoZXhpc3RpbmdTdHlsZUVsZW1lbnQpIHtcclxuICAgICAgICBleGlzdGluZ1N0eWxlRWxlbWVudC5yZW1vdmUoKTtcclxuICAgICAgfVxyXG5cclxuICAgICAgY29uc3QgJHN0eWxlID0gZG9jdW1lbnQuY3JlYXRlRWxlbWVudChcInN0eWxlXCIpO1xyXG4gICAgICAkc3R5bGUuaWQgPSBcInRoZW1lLXN0eWxlXCI7XHJcbiAgICAgIGRvY3VtZW50LmhlYWQuYXBwZW5kQ2hpbGQoJHN0eWxlKTtcclxuXHJcbiAgICAgIC8vIENvbnZlcnQgaGV4IHRvIFJHQlxyXG4gICAgICBjb25zdCBnZXRSR0JDb2xvciA9IChoZXgsIHR5cGUpID0+IHtcclxuICAgICAgICBsZXQgY29sb3IgPSBoZXgucmVwbGFjZSgvIy9nLCBcIlwiKTtcclxuICAgICAgICB2YXIgciA9IHBhcnNlSW50KGNvbG9yLnN1YnN0cigwLCAyKSwgMTYpO1xyXG4gICAgICAgIHZhciBnID0gcGFyc2VJbnQoY29sb3Iuc3Vic3RyKDIsIDIpLCAxNik7XHJcbiAgICAgICAgdmFyIGIgPSBwYXJzZUludChjb2xvci5zdWJzdHIoNCwgMiksIDE2KTtcclxuICAgICAgICByZXR1cm4gYC0tY29sb3ItJHt0eXBlfTogJHtyfSwgJHtnfSwgJHtifTtgO1xyXG4gICAgICB9O1xyXG5cclxuICAgICAgY29uc3QgZ2V0QWNjZXNzaWJsZUNvbG9yID0gKGhleCkgPT4ge1xyXG4gICAgICAgIGxldCBjb2xvciA9IGhleC5yZXBsYWNlKC8jL2csIFwiXCIpO1xyXG4gICAgICAgIHZhciByID0gcGFyc2VJbnQoY29sb3Iuc3Vic3RyKDAsIDIpLCAxNik7XHJcbiAgICAgICAgdmFyIGcgPSBwYXJzZUludChjb2xvci5zdWJzdHIoMiwgMiksIDE2KTtcclxuICAgICAgICB2YXIgYiA9IHBhcnNlSW50KGNvbG9yLnN1YnN0cig0LCAyKSwgMTYpO1xyXG4gICAgICAgIHZhciB5aXEgPSAociAqIDI5OSArIGcgKiA1ODcgKyBiICogMTE0KSAvIDEwMDA7XHJcbiAgICAgICAgcmV0dXJuIHlpcSA+PSAxMjggPyBcIiMwMDAwMDBcIiA6IFwiI0ZGRkZGRlwiO1xyXG4gICAgICB9O1xyXG5cclxuICAgICAgY29uc3QgcHJpbWFyeUNvbG9yID0gZ2V0UkdCQ29sb3IodGhlbWVDb2xvciwgXCJwcmltYXJ5XCIpO1xyXG4gICAgICBjb25zdCB0ZXh0Q29sb3IgPSBnZXRSR0JDb2xvcihnZXRBY2Nlc3NpYmxlQ29sb3IodGhlbWVDb2xvciksIFwiYTExeVwiKTtcclxuXHJcbiAgICAgICRzdHlsZS5pbm5lckhUTUwgPSBgOnJvb3QgeyR7cHJpbWFyeUNvbG9yfSAke3RleHRDb2xvcn19YDtcclxuICAgIH1cclxuICB9LCBbdGhlbWVDb2xvcl0pO1xyXG5cclxuXHJcbiAgcmV0dXJuIChcclxuICAgIDxTZWN1cmVUaGVtZUNvbnRleHQuUHJvdmlkZXIgdmFsdWU9e3sgXHJcbiAgICAgIHRoZW1lQ29sb3IsIFxyXG4gICAgICBzZXRUaGVtZUNvbG9yLFxyXG4gICAgICBpc0xvYWRpbmcgXHJcbiAgICB9fT5cclxuICAgICAge2NoaWxkcmVufVxyXG4gICAgPC9TZWN1cmVUaGVtZUNvbnRleHQuUHJvdmlkZXI+XHJcbiAgKTtcclxufTtcclxuXHJcbmV4cG9ydCBjb25zdCB1c2VTZWN1cmVUaGVtZSA9ICgpID0+IHtcclxuICBjb25zdCBjb250ZXh0ID0gdXNlQ29udGV4dChTZWN1cmVUaGVtZUNvbnRleHQpO1xyXG4gIGlmICghY29udGV4dCkge1xyXG4gICAgdGhyb3cgbmV3IEVycm9yKCd1c2VTZWN1cmVUaGVtZSBtdXN0IGJlIHVzZWQgd2l0aGluIGEgU2VjdXJlVGhlbWVQcm92aWRlcicpO1xyXG4gIH1cclxuICByZXR1cm4gY29udGV4dDtcclxufTsgIl0sIm5hbWVzIjpbImNyZWF0ZUNvbnRleHQiLCJ1c2VDb250ZXh0IiwidXNlU3RhdGUiLCJ1c2VFZmZlY3QiLCJTZWN1cmVUaGVtZUNvbnRleHQiLCJTZWN1cmVUaGVtZVByb3ZpZGVyIiwiY2hpbGRyZW4iLCJpbml0aWFsVGhlbWUiLCJ0aGVtZUNvbG9yIiwic2V0VGhlbWVDb2xvciIsImlzTG9hZGluZyIsInNldElzTG9hZGluZyIsImluaXRpYWxpemVUaGVtZSIsImFwaUJhc2UiLCJwcm9jZXNzIiwiZW52IiwiTkVYVF9QVUJMSUNfQVBJX0JBU0VfVVJMIiwicmVzcG9uc2UiLCJmZXRjaCIsIm1ldGhvZCIsImNyZWRlbnRpYWxzIiwib2siLCJ1c2VyIiwianNvbiIsInRoZW1lIiwiZXJyb3IiLCJjb25zb2xlIiwiZXhpc3RpbmdTdHlsZUVsZW1lbnQiLCJkb2N1bWVudCIsImdldEVsZW1lbnRCeUlkIiwicmVtb3ZlIiwiJHN0eWxlIiwiY3JlYXRlRWxlbWVudCIsImlkIiwiaGVhZCIsImFwcGVuZENoaWxkIiwiZ2V0UkdCQ29sb3IiLCJoZXgiLCJ0eXBlIiwiY29sb3IiLCJyZXBsYWNlIiwiciIsInBhcnNlSW50Iiwic3Vic3RyIiwiZyIsImIiLCJnZXRBY2Nlc3NpYmxlQ29sb3IiLCJ5aXEiLCJwcmltYXJ5Q29sb3IiLCJ0ZXh0Q29sb3IiLCJpbm5lckhUTUwiLCJQcm92aWRlciIsInZhbHVlIiwidXNlU2VjdXJlVGhlbWUiLCJjb250ZXh0IiwiRXJyb3IiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///./utils/secureThemeContext.js\n");

/***/ }),

/***/ "./styles/globals.css":
/*!****************************!*\
  !*** ./styles/globals.css ***!
  \****************************/
/***/ (() => {



/***/ }),

/***/ "react":
/*!************************!*\
  !*** external "react" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("react");

/***/ }),

/***/ "react-loader-spinner":
/*!***************************************!*\
  !*** external "react-loader-spinner" ***!
  \***************************************/
/***/ ((module) => {

"use strict";
module.exports = require("react-loader-spinner");

/***/ }),

/***/ "react/jsx-dev-runtime":
/*!****************************************!*\
  !*** external "react/jsx-dev-runtime" ***!
  \****************************************/
/***/ ((module) => {

"use strict";
module.exports = require("react/jsx-dev-runtime");

/***/ }),

/***/ "@azure/msal-browser":
/*!**************************************!*\
  !*** external "@azure/msal-browser" ***!
  \**************************************/
/***/ ((module) => {

"use strict";
module.exports = import("@azure/msal-browser");;

/***/ }),

/***/ "@azure/msal-react":
/*!************************************!*\
  !*** external "@azure/msal-react" ***!
  \************************************/
/***/ ((module) => {

"use strict";
module.exports = import("@azure/msal-react");;

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = (__webpack_exec__("./pages/_app.js"));
module.exports = __webpack_exports__;

})();