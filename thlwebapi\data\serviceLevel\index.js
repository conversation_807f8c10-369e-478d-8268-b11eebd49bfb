"use strict";
const utils = require("../utils");
const config = require("../../config");
const sql = require("mssql");
const logger = require("../../utils/logger");
const socket = require("../../utils/socket");

const getSLCustomers = async (
  name,
  email,
  start_date,
  end_date,
  ADCompanyName,
  userCompany = "efcltd",
  serviceCustomer="All Service Customers"
) => {
  let transaction;
  try {
    let pool = await sql.connect(config.sql);
    transaction = new sql.Transaction(pool);
    await transaction.begin();
    const sqlQueries = await utils.loadSqlQueries("serviceLevel");
    let sLCustomers;
    if (
      userCompany == "efcltd" ||
      userCompany == "flrs" ||
      userCompany == "thl"
    ) {
      sLCustomers = await pool
        .request(transaction)
        .input("start_date", sql.SmallDateTime, start_date)
        .input("end_date", sql.SmallDateTime, end_date)
        .query(sqlQueries.getEFCCustomers);
    } else if (userCompany == "dpsltd") {
      let deptCode = ADCompanyName == "DPS MS" ? 9 : 1;
      if(deptCode == 9 ){
        sLCustomers = await pool
        .request(transaction)
        .input("start_date", sql.SmallDateTime, start_date)
        .input("end_date", sql.SmallDateTime, end_date)
        .input("deptCode", sql.Int, deptCode)
        .query(sqlQueries.getDPSMSCustomers);
      }else{
        sLCustomers = await pool
        .request(transaction)
        .input("start_date", sql.SmallDateTime, start_date)
        .input("end_date", sql.SmallDateTime, end_date)
        // .input("deptCode", sql.Int, deptCode)
        .query(sqlQueries.getDPSCustomers);
      }
    } else if (userCompany == "fpp-ltd") {
      sLCustomers = await pool
        .request(transaction)
        .input("start_date", sql.SmallDateTime, start_date)
        .input("end_date", sql.SmallDateTime, end_date)
        .query(sqlQueries.getFPPCustomers);
    } else if (userCompany == "issproduce") {
      sLCustomers = await pool
        .request(transaction)
        .input("start_date", sql.SmallDateTime, start_date)
        .input("end_date", sql.SmallDateTime, end_date)
        .input("serviceCustomer",sql.VarChar,serviceCustomer)
        .query(sqlQueries.getISSCustomers);
    }
    await transaction.commit();
    return sLCustomers.recordset;
  } catch (error) {
    console.error("Error fetching service level customers", error);
    logger.error({
      username: name,
      type: "error",
      description: `User with email ${email} failed to fetch service level customers: "${error.message}"`,
      module_id: 4,
    });
    if (transaction) {
      await transaction.rollback();
    }
    return error.message;
  }
};
const getSLServiceCustomers = async (name, email, start_date, end_date,cust_code) => {
  let transaction;
  try {
    let pool = await sql.connect(config.sql);
    transaction = new sql.Transaction(pool);
    await transaction.begin();
    const sqlQueries = await utils.loadSqlQueries("serviceLevel");
    const sLServiceCustomers = await pool
      .request(transaction)
      .input("start_date", sql.SmallDateTime, start_date)
      .input("end_date", sql.SmallDateTime, end_date)
      .input("cust_code", sql.VarChar, cust_code)
      .query(sqlQueries.getISSServiceCustomers);
    await transaction.commit();
    return sLServiceCustomers.recordset;
  } catch (error) {
    console.error("Error fetching service level service customers", error);
    logger.error({
      username: name,
      type: "error",
      description: `User with email ${email} failed to fetch service level service customers: "${error.message}"`,
      module_id: 4,
    });
    if (transaction) {
      await transaction.rollback();
    }
    return error.message;
  }
};

const getSLDataByCustomer = async (
  cust_code,
  start_date,
  end_date,
  name,
  email,
  seeAll,
  orderTypeId,
  ADCompanyName,
  orderID = null,
  userCompany = "efcltd",
  serviceCustomer = "All Service Customers"
) => {
  let transaction;

  try {
    let pool = await sql.connect(config.sql);
    transaction = new sql.Transaction(pool);
    await transaction.begin();
    const sqlQueries = await utils.loadSqlQueries("serviceLevel");
    let SLByCustomer;

    if (
      userCompany == "efcltd" ||
      userCompany == "flrs" ||
      userCompany == "thl"
    ) {
      SLByCustomer = await pool
        .request(transaction)
        .input("cust_code", sql.VarChar, cust_code)
        .input("start_date", sql.SmallDateTime, start_date)
        .input("end_date", sql.SmallDateTime, end_date)
        .input("get_all", sql.Bit, seeAll)
        .input("orderTypeId", sql.Int, orderTypeId)
        .input("orderID", sql.VarBinary, orderID)
        .query(sqlQueries.getEFCSLByCustomer);
    } else if (userCompany == "dpsltd") {
      let deptCode = ADCompanyName == "DPS MS" ? 9 : 1;

    if(deptCode == 9){
        SLByCustomer = await pool
        .request(transaction)
        .input("cust_code", sql.VarChar, cust_code)
        .input("start_date", sql.SmallDateTime, start_date)
        .input("end_date", sql.SmallDateTime, end_date)
        .input("get_all", sql.Bit, seeAll)
        .input("orderTypeId", sql.Int, orderTypeId)
        .input("orderID", sql.VarBinary, orderID)
        .input("deptCode", sql.Int, deptCode)
        .query(sqlQueries.getDPSMSSLByCustomer);
      }else{
        SLByCustomer = await pool
        .request(transaction)
        .input("cust_code", sql.VarChar, cust_code)
        .input("start_date", sql.SmallDateTime, start_date)
        .input("end_date", sql.SmallDateTime, end_date)
        .input("get_all", sql.Bit, seeAll)
        .input("orderTypeId", sql.Int, orderTypeId)
        .input("orderID", sql.VarBinary, orderID)
        //.input("deptCode", sql.Int, deptCode)
        .query(sqlQueries.getDPSSLByCustomer);
      }
      
    } else if (userCompany == "fpp-ltd") {
      SLByCustomer = await pool
        .request(transaction)
        .input("cust_code", sql.VarChar, cust_code)
        .input("start_date", sql.SmallDateTime, start_date)
        .input("end_date", sql.SmallDateTime, end_date)
        .input("get_all", sql.Bit, seeAll)
        .input("orderTypeId", sql.Int, orderTypeId)
        .input("orderID", sql.VarBinary, orderID)
        .query(sqlQueries.getFPPSLByCustomer);
    } else if (userCompany == "issproduce") {
      SLByCustomer = await pool
        .request(transaction)
        .input("cust_code", sql.VarChar, cust_code)
        .input("start_date", sql.SmallDateTime, start_date)
        .input("end_date", sql.SmallDateTime, end_date)
        .input("get_all", sql.Bit, seeAll)
        .input("orderTypeId", sql.Int, orderTypeId)
        .input("orderID", sql.VarBinary, orderID)
        .input("serviceCustomer", sql.VarChar, serviceCustomer)
        .query(sqlQueries.getISSSLByCustomer);
    }

    await transaction.commit();
    return SLByCustomer.recordset;
  } catch (error) {
    logger.error({
      username: name,
      type: "error",
      description: `User with email ${email} failed to fetch service level data for customer: "${error.message}"`,
      module_id: 4,
    });
    if (transaction) {
      await transaction.rollback();
    }
    console.error("Error on sl by customer:", error);
    return error.message;
  }
};

const getIntialSLData = async (
  name,
  start_date,
  end_date,
  email,
  cust_code,
  seeAll,
  orderTypeId,
  company,
  ADCompanyName,
  serviceCustomer
) => {
  let transaction;

  try {
    let pool = await sql.connect(config.sql);
    transaction = new sql.Transaction(pool);
    await transaction.begin();
    let allInitialSLData = {};
    const sLCustomers = await getSLCustomers(
      name,
      email,
      start_date,
      end_date,
      ADCompanyName,
      company,
      serviceCustomer
    );
    if (company == "issproduce") {
      const sLServiceCustomers = await getSLServiceCustomers(
        name,
        email,
        start_date,
        end_date,
        cust_code
      );
      allInitialSLData.sLServiceCustomers = sLServiceCustomers;
    }

    const sLDataByCustomer = await getSLDataByCustomer(
      cust_code && cust_code !== "null" && cust_code !== ""
        ? cust_code
        : sLCustomers[0]?.value,
      start_date,
      end_date,
      name,
      email,
      seeAll,
      orderTypeId,
      ADCompanyName,
      null,
      company,
      serviceCustomer
    );
    allInitialSLData.sLCustomers = sLCustomers;
    allInitialSLData.sLDataByCustomer = sLDataByCustomer;
    allInitialSLData.start_date = start_date;
    allInitialSLData.end_date = end_date;
    await transaction.commit();
    return allInitialSLData;
  } catch (error) {
    logger.error({
      username: name,
      type: "error",
      description: `User with email ${email} failed to fetch initial service level data: "${error.message}"`,
      module_id: 4,
    });
    if (transaction) {
      await transaction.rollback();
    }
    console.error("Error:", error);
  }
};

const getServiceLevelReasons = async (orderId) => {
  try {
    let pool = await sql.connect(config.sql);
    const sqlQueries = await utils.loadSqlQueries("serviceLevel");
    let serviceLevel = await pool
      .request()
      .input("orderId", sql.Int, orderId)
      .query(sqlQueries.getServiceLevelReasons);
    return serviceLevel.recordset;
  } catch (err) {
    console.log("error fetching service level reasons ", err);
    logger.error({
      username: null,
      type: "error",
      description: `Failed to get service level reasons: ${err}`,
      module_id: 4,
    });
    return err.message;
  }
};

const getServiceLevelAuditReasons = async (orderId) => {
  try {
    let pool = await sql.connect(config.sql);
    const sqlQueries = await utils.loadSqlQueries("serviceLevel");
    let serviceLevel = await pool
      .request()
      .input("orderId", sql.Int, orderId)
      .query(sqlQueries.getServiceLevelAuditReasons);
    return serviceLevel.recordset;
  } catch (err) {
    console.log("error fetching service level reasons ", err);
    logger.error({
      username: null,
      type: "error",
      description: `Failed to get service level reasons: ${err}`,
      module_id: 4,
    });
    return err.message;
  }
};

const getServiceLevelResonsMaster = async () => {
  try {
    let pool = await sql.connect(config.sql);
    const sqlQueries = await utils.loadSqlQueries("serviceLevel");
    let serviceLevel = await pool
      .request()
      .query(sqlQueries.getServiceLevelResonsMaster);
    return serviceLevel.recordset;
  } catch (err) {
    console.log("error fetching service level reasons ", err);
    logger.error({
      username: null,
      type: "error",
      description: `Failed to get service level reasons master: ${err}`,
      module_id: 4,
    });
    return err.message;
  }
};
const addNewReason = async (payload) => {
  try {
    let pool = await sql.connect(config.sql);
    const sqlQueries = await utils.loadSqlQueries("serviceLevel");

    let result = [];
    let serviceLevel;
    for (const item of payload) {
      serviceLevel = await pool
        .request()
        .input("quantity", sql.Int, parseInt(item.quantity))
        .input("orderId", sql.VarChar, item.orderId.toString())
        .input("reasons", sql.Int, parseInt(item.reasons))
        .input("subReason", sql.Int, parseInt(item.subReason))
        .input("comment", sql.VarChar, item.comment)
        .input("added_by", sql.VarChar, item.addedBy)
        .input("cust_code", sql.VarChar, item.custCode)
        .query(sqlQueries.addNewReason);
      const newResultItem = {
        ...serviceLevel.recordset[0],
        reasonsLabel: item.reasonsLabel,
        subReasonLabel: item.subReasonLabel,
      };

      result.push(newResultItem);
      logger.info({
        username: item.addedByName,
        type: "success",
        description: `User with email ${item.addedBy} added new reason for order ID ${item.orderId}: Quantity - ${item.quantity}, Reason - ${item.reasonsLabel}, SubReason - ${item.subReasonLabel}, Comment - "${item.comment}"`,
        item_id: serviceLevel.recordset[0].id,
        module_id: 4,
      });
    }

    return result;
  } catch (err) {
    console.log("error fetching service level reasons ", err);
    logger.error({
      username: null,
      type: "error",
      description: `Failed to get service level reasons master: ${err}`,
      module_id: 4,
    });
    return err.message;
  }
};
const editReason = async (data) => {
  try {
    let pool = await sql.connect(config.sql);
    const sqlQueries = await utils.loadSqlQueries("serviceLevel");
    let result = [];
    let serviceLevel;
    for (const item of data) {
      let previousReasonsById = await pool
        .request()
        .input("id", sql.Int, item.id)
        .query(sqlQueries.getReasonsById);
      let previousReasons = previousReasonsById.recordset[0];

      let changes = [];

      if (previousReasons.quantity !== item.quantity) {
        changes.push(
          `Quantity changed from ${previousReasons.quantity} to ${item.quantity}`
        );
      }

      if (previousReasons.reason_id !== item.reasons) {
        changes.push(
          `Reason changed from ${previousReasons.reason_name} to ${item.reasonsLabel}`
        );
      }

      if (previousReasons.subreason_id !== item.subReason) {
        changes.push(
          `SubReason changed from ${previousReasons.subreason_name} to ${item.subReasonLabel}`
        );
      }

      if (previousReasons.comment !== item.comment) {
        changes.push(
          `Comment changed from "${previousReasons.comment}" to "${item.comment}"`
        );
      }

      let description = `Updated reason for order ID ${item.orderId}: `;
      if (changes.length > 0) {
        description += changes.join(", ");
      } else {
        description += "No changes detected.";
      }

      serviceLevel = await pool
        .request()
        .input("quantity", sql.Int, parseInt(item.quantity))
        .input("orderId", sql.VarChar, item.orderId.toString())
        .input("reasons", sql.Int, parseInt(item.reasons))
        .input("subReason", sql.Int, parseInt(item.subReason))
        .input("comment", sql.VarChar, item.comment)
        .input("id", sql.Int, item.id)
        .input("updatedBy", sql.VarChar, item.updatedBy)
        .query(sqlQueries.editReason);

      logger.info({
        username: item.updatedBy,
        type: "success",
        description: description,
        module_id: 4,
      });
    }
    return serviceLevel.recordset;
  } catch (err) {
    console.log("error editing service level reasons ", err);
    logger.error({
      username: null,
      type: "error",
      description: `Failed to get service level reasons master: ${err}`,
      module_id: 4,
    });
    return err.message;
  }
};

const deleteReason = async (data) => {
  try {
    let pool = await sql.connect(config.sql);
    const sqlQueries = await utils.loadSqlQueries("serviceLevel");
    let serviceLevel;

    if (Array.isArray(data.id)) {
      for (const id of data.id) {
        let previousReasonsById = await pool
          .request()
          .input("id", sql.Int, id)
          .query(sqlQueries.getReasonsById);
        let previousReasons = previousReasonsById.recordset[0];

        serviceLevel = await pool
          .request()
          .input("deleted_by", sql.VarChar, data.deletedBy)
          .input("id", sql.Int, id)
          .query(sqlQueries.deleteReason);
        logger.info({
          username: data.deletedByName,
          type: "success",
          description: `User with email ${data.deletedBy} deleted reason for order ID ${previousReasons.order_id}: Reason - "${previousReasons.reason_name}", SubReason - "${previousReasons.subreason_name}", Comment - "${previousReasons.comment}`,
          module_id: 4,
        });
      }
    }
    return { status: true };
  } catch (err) {
    console.log("error deleting reason", err);
    logger.error({
      username: null,
      type: "error",
      description: `Failed to get service level reasons master: ${err}`,
      module_id: 4,
    });
    return err.message;
  }
};
const deleteBulkReason = async (data, email) => {
  try {
    let pool = await sql.connect(config.sql);
    const sqlQueries = await utils.loadSqlQueries("serviceLevel");
    let serviceLevel;

    const reasonIds = [];
    for (const id of data.orderIds) {
      {
        let previousReasonsById = await pool
          .request()
          .input("order_id", sql.Int, id)
          .query(sqlQueries.getReasonsByOrdId);
        let previousReasons = previousReasonsById.recordset;

        for (const previousReasonData of previousReasons) {
          serviceLevel = await pool
            .request()
            .input("deleted_by", sql.VarChar, data.deletedBy)
            .input("id", sql.Int, previousReasonData.id)
            .query(sqlQueries.deleteReason);
          reasonIds.push(previousReasonData.id);

          logger.info({
            username: data.deletedByName,
            type: "success",
            description: `User with email ${data.deletedBy} deleted reason for order ID ${previousReasonData.order_id}: Reason - "${previousReasonData.reason_name}", SubReason - "${previousReasonData.subreason_name}", Comment - "${previousReasonData.comment}`,
            module_id: 4,
          });
        }
      }
    }
    let result = {
      orderId: data.orderIds,
      id: reasonIds,
      deletedBy: data.deletedBy,
      deletedByName: data.deletedByName,
    };

    return result;
  } catch (err) {
    console.log("error deleting reason", err);
    logger.error({
      username: null,
      type: "error",
      description: `Failed to get service level reasons master: ${err}`,
      module_id: 4,
    });
    return err.message;
  }
};

const removeSLLocks = async (email, custCode = [], orderId = []) => {
  let transaction;
  try {
    const io = socket.getIO();
    const payload = { email, custCode, orderId };
    io.emit("locksRemoved", payload);

    let pool = await sql.connect(config.sql);
    transaction = new sql.Transaction(pool);
    await transaction.begin();
    const sqlQueries = await utils.loadSqlQueries("serviceLevel");
    const removedLock = await transaction
      .request()
      .input("user_id", sql.VarChar, email)
      .query(sqlQueries.deleteSLLocks);
    await transaction.commit();
    return { response: true };
  } catch (error) {
    console.error("error", error);
    logger.error({
      username: name,
      type: "error",
      description: `User with email ${email} failed to fetch whatif locks: "${error.message}"`,
      item_id: null,
      module_id: 4,
    });
    if (transaction) {
      await transaction.rollback();
    }
  }
};

const addSLLock = async (payload) => {
  let transaction;
  try {
    let pool = await sql.connect(config.sql);
    const sqlQueries = await utils.loadSqlQueries("serviceLevel");
    transaction = new sql.Transaction(pool);
    await transaction.begin();

    const deleteLock = await transaction
      .request()
      .input("user_id", sql.VarChar, payload.email)
      .query(sqlQueries.deleteSLLocks);

    const addedLock = await transaction
      .request()
      .input("custcode", sql.VarChar, payload.custCode)
      .input("order_id", sql.VarChar, payload.orderId)
      .input("user_id", sql.VarChar, payload.email)
      .input("user_name", sql.VarChar, payload.name)
      .query(sqlQueries.addSLLock);

    await transaction.commit();
    return { response: true };
  } catch (error) {
    logger.error({
      username: payload.name,
      type: "error",
      description: `User with email ${payload.email} failed to add a lock: "${error.message}"`,
      item_id: null,
      module_id: 4,
    });
    if (transaction) {
      await transaction.rollback();
    }
    return { response: null };
  } finally {
    if (transaction) {
      transaction = null;
    }
  }
};

module.exports = {
  getSLCustomers,
  getIntialSLData,
  getSLDataByCustomer,
  getServiceLevelReasons,
  getServiceLevelResonsMaster,
  getServiceLevelAuditReasons,
  addNewReason,
  editReason,
  deleteReason,
  addSLLock,
  removeSLLocks,
  deleteBulkReason,
};
