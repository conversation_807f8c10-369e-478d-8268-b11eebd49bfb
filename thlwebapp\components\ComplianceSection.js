import React, { useRef } from "react";
import { useRouter } from "next/router";
import { useState, useEffect, Fragment } from "react";
import { apiConfig } from "@/services/apiConfig";
import { ThreeCircles } from "react-loader-spinner";
import { ToastContainer, toast } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import { addProphetAjaxCall } from "@/utils/ajaxHandler";
import { useMsal } from "@azure/msal-react";
import { Dialog, Transition } from "@headlessui/react";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faInfo, faXmark } from "@fortawesome/free-solid-svg-icons";
import Cookies from "js-cookie";
import { useLoading } from "@/utils/loaders/loadingContext";
import { logout } from "@/utils/secureStorage";

const ComplianceSection = ({
  data,
  onPrev,
  onSubmit,
  isEdit,
  setNavType,
  navType,
}) => {
  const router = useRouter();
  const supplierData = data;
  const { supplierId } = router.query;
  const [redTractor, setRedTractor] = useState("");
  const [pucCode, setPucCode] = useState("");
  const [chileCertificateNumber, setChileCertificateNumber] = useState("");
  const [organicCertificateNumber, setOrganicCertificateNumber] = useState("");
  const [globalGapNumber, setGlobalGapNumber] = useState("");
  const [customerSiteCode, setCustomerSiteCode] = useState("");
  const [selectedRole, setSelectedRole] = useState([]);
  const [loading, setLoading] = useState(false);
  const { setIsLoading } = useLoading();
  const [role, setRole] = useState("");

  const [produceSupplier, setProduceSupplier] = useState("");
  const [formChange, setFormChange] = useState(false);
  const [checkRole, setIsCheckRole] = useState([]);
  const [isRedTractorChange, setIsRedTractorChange] = useState(false);
  const [isGlobalGapNumberChange, setIsGlobalGapNumberChange] = useState(false);

  const [redTractorErrorVisibility, setRedTractorErrorVisibility] =
    useState(false);
  const [pucCodeErrorVisibility, setPucCodeErrorVisibility] = useState(false);
  const [customerSiteCodeErrorVisibility, setCustomerSiteCodeErrorVisibility] =
    useState(false);
  const [globalGapNumberErrorVisibility, setGlobalGapNumberErrorVisibility] =
    useState(false);
  const [
    chileCertificateNumberErrorVisibility,
    setChileCertificateNumberErrorVisibility,
  ] = useState(false);
  const [
    organicCertificateNumberErrorVisibility,
    setOrganicCertificateNumberErrorVisibility,
  ] = useState(false);

  const { instance, accounts } = useMsal();

  const [isCancelled, setIsCancelled] = useState(false);
  const [isContinue, setIsContinue] = useState(false);

  const [isOpen, setIsOpen] = useState(false);
  const [isCompliance, setIsCompliance] = useState("Complete");
  const [status, setStatus] = useState("");
  const [validationCheck, setValidationCheck] = useState(false);

  const [isSupplierAccount, setIsSupplierAccount] = useState("");
  const [country, setCountry] = useState("");
  const [countryCode, setCountryCode] = useState("");
  const [generatedProphets, setGeneratedProphets] = useState([]);
  const [isMandatoryField, setIsMandatoryField] = useState({
    redTractorIsRequired: false,
    ggnIsRequired: false,
    pucIsRequired: false,
    chileIsRequired: false,
  });
  const [allowedSections, setAllowedSections] = useState([]);
  const [prophetsIds, setProphetIds] = useState();

  let isComplianced = "";
  const [isCodeSystemGenerated, setIsCodeSystemGenerated] = useState(true);

  function handleRedTractorChange(e) {
    setFormChange(true);
    setIsRedTractorChange(true);
    setRedTractor(e.target.value);
    if (e.target.value.length > 50) {
      setRedTractorErrorVisibility(true);
    } else {
      setRedTractorErrorVisibility(false);
    }
  }

  function handlePucCodeChange(e) {
    setFormChange(true);
    setPucCode(e.target.value);
    if (e.target.value.length > 50) {
      setPucCodeErrorVisibility(true);
    } else {
      setPucCodeErrorVisibility(false);
    }
  }

  function handleChileCertChange(e) {
    setFormChange(true);
    setChileCertificateNumber(e.target.value);
    if (e.target.value.length > 50) {
      setChileCertificateNumberErrorVisibility(true);
    } else {
      setChileCertificateNumberErrorVisibility(false);
    }
  }

  function handleOrganicCertificateNumberChange(e) {
    setFormChange(true);
    setOrganicCertificateNumber(e.target.value);
    if (e.target.value.length > 50) {
      setOrganicCertificateNumberErrorVisibility(true);
    } else {
      setOrganicCertificateNumberErrorVisibility(false);
    }
  }

  function handleGlobalGapNumberChange(e) {
    setFormChange(true);
    setIsGlobalGapNumberChange(true);
    let inputValue = e.target.value;
    if (inputValue.length > 13) {
      inputValue = inputValue.slice(0, 13);
    }
    setGlobalGapNumber(inputValue);
    if (produceSupplier) {
      if (e.target.value.length > 13 || e.target.value === "") {
        setGlobalGapNumberErrorVisibility(true);
      } else {
        setGlobalGapNumberErrorVisibility(false);
      }
    }
  }

  function handleCustomerSiteCodeChange(e) {
    setFormChange(true);
    setCustomerSiteCode(e.target.value);
    if (e.target.value.length > 50) {
      setCustomerSiteCodeErrorVisibility(true);
    } else {
      setCustomerSiteCodeErrorVisibility(false);
    }
  }

  const handleValidate = (step, e, data, isContinue = false) => {
    let errorCount = 0;
    const roles = JSON.parse(data?.role_ids ?? "[]");
    const role_ids = roles?.map((item) => item?.role_id);

    if (role_ids?.includes(2) || role_ids?.includes(3)) {
      if (data.country_code == "UK") {
        if (!redTractor) {
          setRedTractorErrorVisibility(true);
          errorCount++;
        }
      } else if (data.country_code == "ZA") {
        if (!globalGapNumber) {
          setGlobalGapNumberErrorVisibility(true);
          errorCount++;
        }
        if (!pucCode) {
          setPucCodeErrorVisibility(true);
          errorCount++;
        }
      } else if (data.country_code == "CL") {
        if (!globalGapNumber) {
          setGlobalGapNumberErrorVisibility(true);
          errorCount++;
        }
        if (!chileCertificateNumber) {
          setChileCertificateNumberErrorVisibility(true);
          errorCount++;
        }
      } else {
        if (!globalGapNumber) {
          setGlobalGapNumberErrorVisibility(true);
          errorCount++;
        } else {
          setGlobalGapNumberErrorVisibility(false);
          //errorCount++;
        }
      }
    }

    if (errorCount > 0) {
      setIsOpen(true);
      setNavType(step);
      isComplianced = "Incomplete";
      setIsCompliance("Incomplete");
    } else {
      isComplianced = "Complete";
      handleSubmit(step, data);
      //return;
    }

    if (isCancelled) {
      isComplianced = "";
      setIsCancelled(false);
      //return;
    }

    if (isContinue) {
      setIsCompliance("Incomplete");
      handleSubmit(step, data);
      setIsOpen(false);
    }
  };
  const closeModal = (e) => {
    if (e) {
      e.preventDefault();
    }
    setIsCancelled(true);
    setIsOpen(false);
  };

  const handleContinueSubmit = (e, data) => {
    setFormChange(true);
    e.preventDefault();
    setIsContinue(true);
    handleValidate(navType, e, data, true);
    setIsCompliance("Incomplete");
  };

  // console.log(data.country_code);
  let prophetData = {};
  const handleSubmit = (step, data) => {
    let serverAddress = apiConfig.serverAddress;

    const prophetIds = data?.prophets_id_code;

    if (
      isCodeSystemGenerated &&
      prophetIds &&
      isSupplierAccount == "false" &&
      data?.country_code &&
      ((data?.country_code === "UK" && redTractor && isRedTractorChange) ||
        (globalGapNumber && isGlobalGapNumberChange))
    ) {
      let generatePhophetCode = "";
      const prophet = JSON.parse(prophetIds ?? "[]");

      if (data?.country_code === "UK" && redTractor && isRedTractorChange) {
        generatePhophetCode = redTractor?.slice(-6).padStart(6, "X");
      } else if (
        data?.country_code !== "UK" &&
        globalGapNumber &&
        isGlobalGapNumberChange
      ) {
        generatePhophetCode = globalGapNumber?.slice(-6).padStart(6, "X");
      }

      // prophetData = {
      //   prophet_id: prophet[0]?.prophet_id,
      //   prophet_code: generatePhophetCode.toString().trim(),
      //   supplier_id: parseInt(supplierId),
      // };

      prophetData = {
        prophet_id: prophet[0]?.prophet_id,
        prophet_code: generatePhophetCode.toString().trim().toUpperCase(),
        supplier_id: parseInt(supplierId),
      };
      setGeneratedProphets([prophetData]);
    }

    // if (produceSupplier) {
    //   if (
    //     globalGapNumber === null ||
    //     globalGapNumber === "" ||
    //     globalGapNumber?.length > 50 ||
    //     checkRole?.includes(1)
    //   ) {
    //     isComplianced = "Incomplete";
    //   } else {
    //     isComplianced = "Complete";
    //   }
    // } else {
    //   if (globalGapNumber) {
    //     isComplianced = "Complete";
    //   } else {
    //     isComplianced = "Incomplete";
    //   }
    // }
    setLoading(true);
    if (formChange) {
      let currentStatus;
      if (status == 3 || status == 4 || status == 1) {
        currentStatus = 4;
      } else if (status == 2) {
        currentStatus = 2;
      } else {
        currentStatus = 3;
      }

      localStorage.setItem("isFormNew", false);
      fetch(`${serverAddress}suppliers/update-supplier/${supplierId}`, {
        method: "PUT",
        headers: {
          Accept: "application/json",
          "Content-Type": "application/json",
        },
        credentials: "include",
        body: JSON.stringify({
          sectionName: "complianceSection",
          red_tractor: redTractor,
          puc_code: pucCode,
          chile_certificate_number: chileCertificateNumber,
          organic_certificate_number: organicCertificateNumber,
          global_gap_number: globalGapNumber,
          customer_site_code: customerSiteCode,
          compliance: isComplianced,
          updated_date: new Date().toISOString(),
          status: currentStatus,
          technical: supplierData[0].technical,
          procurement: supplierData[0].procurement,
          financial: supplierData[0].financial,
          allowedSections: allowedSections,
          roleIds:role,
          prophet_id: prophetsIds,
          requestor_email: supplierData[0]?.requestor_email,
          requestor_name: supplierData[0]?.requestor_name,
        }),
      })
        .then(async(res) => {
          if (res.status === 400) {
            toast.error(
              "There was an error with your request. Please check your data and try again."
            );
          } else if (res.status === 401) {
            toast.error("Your session has expired. Please log in again.");
            setTimeout(async() => {
              await logout();
              router.push("/login");
            }, 3000);
          }
          if (res.status === 409) {
            return res.json().then((data) => {
              toast.error(data?.msg, { position: "top-right" });
              setLoading(false);
            });
          }
          if (res.status === 200) {
            if (step == "sap") {
              if (isEdit) {
                if (
                  isSupplierAccount == "false" &&
                  Object.keys(prophetData).length !== 0
                ) {
                  const result = addProphetAjaxCall([prophetData], user);
                  result.then((data) => {
                    if (data?.data) {
                      localStorage.removeItem("isEdit");
                      router.back();
                    }
                  });
                }
              } else {
                localStorage.setItem("curr", 3);
                if (
                  isSupplierAccount == "false" &&
                  Object.keys(prophetData).length !== 0
                ) {
                  const result = addProphetAjaxCall([prophetData], user);
                  result.then((data) => {
                    if (data?.data) {
                      onSubmit();
                    }
                  });
                } else {
                  onSubmit();
                }
              }
            } else {
              router.push({ pathname: "/suppliers" });
            }
            return res.json();
          }
          return Promise.reject(res);
        })
        .catch((err) => {
          setLoading(false);
          toast.error(
            `Error saving data in compliance forms file: ${err.statusText}`,
            {
              position: "top-right",
            }
          );
          return err;
        });
    } else {
      if (step == "sap") {
        if (isEdit) {
          localStorage.removeItem("isEdit");
          router.back();
        } else {
          onSubmit();
        }
      } else {
        router.push({ pathname: "/suppliers" });
      }
    }
  };

  useEffect(() => {
    const prophetsIdsCookie = Cookies.get("prophets");
    const prophetsIds = prophetsIdsCookie ? parseInt(prophetsIdsCookie, 10) : null;
    setProphetIds(prophetsIds);
    if (typeof window !== "undefined") {
      const sectionsString = localStorage.getItem("allowedSections");
      if (sectionsString) {
        const parsedSections = sectionsString.split(",");
        setAllowedSections(parsedSections);
      }
    }
    setRedTractor(data[0]?.red_tractor);
    setPucCode(data[0]?.puc_code);
    setChileCertificateNumber(data[0]?.chile_certificate_number);
    setOrganicCertificateNumber(data[0]?.organic_certificate_number);
    setGlobalGapNumber(data[0]?.global_gap_number);
    setCustomerSiteCode(data[0]?.customer_site_code);
    setStatus(data[0]?.status ?? "");
    setCountry(data[0]?.country_name);
    setCountryCode(data[0]?.country_code);
    setIsCodeSystemGenerated(data[0]?.is_code_system_generated);

    const roles = JSON.parse(data[0]?.role_ids ?? "[]");
    const role_ids = roles?.map((item) => item?.role_id);
    setRole(role_ids);

    setIsCheckRole(role_ids);
    if (role_ids.lenth == 1 && role_ids?.includes(4)) {
      setIsMandatoryField({
        redTractorIsRequired: false,
        pucIsRequired: false,
        ggnIsRequired: false,
        chileIsRequired: false,
      });
    } else if (role_ids?.includes(2) || role_ids?.includes(3)) {
      if (data[0].country_code === "UK") {
        setIsMandatoryField({
          redTractorIsRequired: true,
          pucIsRequired: false,
          ggnIsRequired: false,
          chileIsRequired: false,
        });
      } else if (data[0].country_code == "ZA") {
        setIsMandatoryField({
          ggnIsRequired: true,
          pucIsRequired: true,
          chileIsRequired: false,
          redTractorIsRequired: false,
        });
      } else if (data[0].country_code == "CL") {
        setIsMandatoryField({
          ggnIsRequired: true,
          pucIsRequired: false,
          chileIsRequired: true,
          redTractorIsRequired: false,
        });
      } else {
        setIsMandatoryField({
          ggnIsRequired: true,
          pucIsRequired: false,
          chileIsRequired: false,
          redTractorIsRequired: false,
        });
      }
    }

    const rolesId = data[0]?.role_ids;
    const parsedRoles = JSON.parse(rolesId ?? "[]");

    const supplierAccountExist = parsedRoles.find((ele) => ele.role_id == 1);
    if (supplierAccountExist) {
      setIsSupplierAccount("true");
    } else {
      setIsSupplierAccount("false");
    }
    const roleData = data[0]?.role_names;
    const rolesArray = roleData.replace(/'/g, "").split(", ");

    setSelectedRole(rolesArray);

    setIsCheckRole(role_ids);
    setProduceSupplier(data[0]?.product_supplier);
    const supplierName = data[0]?.name;
    const prophetIds = data[0]?.prophets_id_code;

    setIsLoading(false);
  }, [supplierId, data[0]?.country_code]);

  return (
    <>
      <ToastContainer limit={1} />
      {loading ? (
        <div
          style={{
            display: "flex",
            alignItems: "center",
            justifyContent: "center",
            height: "100vh",
          }}
        >
          <ThreeCircles
            color="#002D73"
            height={50}
            width={50}
            visible={true}
            ariaLabel="oval-loading"
            secondaryColor="#0066FF"
            strokeWidth={2}
            strokeWidthSecondary={2}
          />
        </div>
      ) : (
        <div className="relative panel-container bg-white rounded-lg w-[95%] 2xl:w-[calc(100%-70px)] p-4 pb-0">
          <div className="m-5 mb-0">
            <div className="flex flex-row mb-5">
              <div className="flex flex-col w-1/2 pe-8 border-e-[1px] border-light-gray">
                <div className="mb-0">
                  <div className="mb-3">
                    <h4 className="formtitle pb-1 border-b border-light-gray">
                      Regional Certification
                    </h4>
                  </div>
                  {/* <div className="grid grid-cols-2 gap-4">
                      <div className="flex flex-col">
                        <label className="labels mb-1">Red tractor (UK) 
                        {data[0].country_code == 'UK' && <span className="text-red-500">*</span>}</label>
                        <input
                          type="text"
                          name="red_tractor"
                          maxLength={50}
                          value={redTractor}
                          onChange={handleRedTractorChange}
                          style={{
                            borderColor: redTractorErrorVisibility
                              ? "#f56565"
                              : "#d1d5db",
                          }}
                          className="w-full px-2 2xl:px-3 rounded-md border"
                        />
                        {redTractorErrorVisibility ? (
                          <p className="text-red-500">
                            Please enter a valid number less than 50 characters.
                          </p>
                        ) : (
                          void 0
                        )}
                      </div>

                      <div className="flex flex-col">
                        <label className="labels mb-1">Puc code (RSA) 
                        {data[0].country_code == 'ZA' && <span className="text-red-500">*</span>}</label>
                        <input
                          type="text"
                          name="puc_code"
                          maxLength={50}
                          value={pucCode}
                          onChange={handlePucCodeChange}
                          style={{
                            borderColor: pucCodeErrorVisibility
                              ? "#f56565"
                              : "#d1d5db",
                          }}
                          className="w-full px-2 2xl:px-3 border rounded-md"
                        />
                        {pucCodeErrorVisibility ? (
                          <p className="text-red-500">
                            Please enter a valid code less than 50 characters.
                          </p>
                        ) : (
                          void 0
                        )}
                      </div>

                      <div className="flex flex-col">
                        <label className="labels mb-1">Chile cert 
                        {data[0].country_code == 'CL' && <span className="text-red-500">*</span>}</label>
                        <input
                          type="text"
                          name="chile_cert"
                          maxLength={50}
                          value={chileCertificateNumber}
                          onChange={handleChileCertChange}
                          style={{
                            borderColor: chileCertificateNumberErrorVisibility
                              ? "#f56565"
                              : "#d1d5db",
                          }}
                          className="w-full px-2 2xl:px-3 border rounded-md"
                        />
                        {chileCertificateNumberErrorVisibility ? (
                          <p className="text-red-500">
                            Please enter a valid number less than 50 characters.
                          </p>
                        ) : (
                          void 0
                        )}
                      </div>
                    </div> */}
                </div>
                <div className="grid grid-cols-2 gap-4">
                  <div className="flex flex-col">
                    <label className="labels mb-1">
                      Red Tractor (UK)
                      {isMandatoryField.redTractorIsRequired ? (
                        <span className="text-red-500">*</span>
                      ) : (
                        ""
                      )}
                    </label>
                    <input
                      type="text"
                      name="red_tractor"
                      maxLength={50}
                      value={redTractor}
                      onChange={handleRedTractorChange}
                      style={{
                        borderColor: redTractorErrorVisibility
                          ? "#f56565"
                          : "#d1d5db",
                      }}
                      className="w-full px-2 2xl:px-3 rounded-md border"
                    />
                    {redTractorErrorVisibility ? (
                      <p className="text-red-500">
                        Please enter a valid number less than 50 characters.
                      </p>
                    ) : (
                      void 0
                    )}
                  </div>

                  <div className="flex flex-col">
                    <label className="labels mb-1">
                      Puc Code (RSA)
                      {isMandatoryField.pucIsRequired ? (
                        <span className="text-red-500">*</span>
                      ) : (
                        ""
                      )}
                    </label>
                    <input
                      type="text"
                      name="puc_code"
                      maxLength={25}
                      value={pucCode}
                      onChange={handlePucCodeChange}
                      style={{
                        borderColor: pucCodeErrorVisibility
                          ? "#f56565"
                          : "#d1d5db",
                      }}
                      className="w-full px-2 2xl:px-3 border rounded-md"
                    />
                    {pucCodeErrorVisibility ? (
                      <p className="text-red-500">
                        Please enter a valid code less than 50 characters.
                      </p>
                    ) : (
                      void 0
                    )}
                  </div>

                  <div className="flex flex-col">
                    <label className="labels mb-1">
                      Chile Cert
                      {isMandatoryField.chileIsRequired ? (
                        <span className="text-red-500">*</span>
                      ) : (
                        ""
                      )}
                    </label>
                    <input
                      type="text"
                      name="chile_cert"
                      maxLength={50}
                      value={chileCertificateNumber}
                      onChange={handleChileCertChange}
                      style={{
                        borderColor: chileCertificateNumberErrorVisibility
                          ? "#f56565"
                          : "#d1d5db",
                      }}
                      className="w-full px-2 2xl:px-3 border rounded-md"
                    />
                    {chileCertificateNumberErrorVisibility ? (
                      <p className="text-red-500">
                        Please enter a valid number less than 50 characters.
                      </p>
                    ) : (
                      void 0
                    )}
                  </div>
                </div>

                <div className="mt-6 mb-3">
                  <h4 className="formtitle pb-1 border-b border-light-gray">
                    Organic Certification
                  </h4>
                </div>
                <div className="grid grid-cols-2 gap-4">
                  <div className="flex flex-col">
                    <label className="labels mb-1">
                      Organic Certification Number
                    </label>
                    <input
                      type="text"
                      name="organic_certification_number"
                      maxLength={50}
                      value={organicCertificateNumber}
                      onChange={handleOrganicCertificateNumberChange}
                      style={{
                        borderColor: organicCertificateNumberErrorVisibility
                          ? "#f56565"
                          : "#d1d5db",
                      }}
                      className="w-full px-2 2xl:px-3 border rounded-md"
                    />
                    {organicCertificateNumberErrorVisibility ? (
                      <p className="text-red-500">
                        Please enter a valid number less than 50 characters.
                      </p>
                    ) : (
                      void 0
                    )}
                  </div>
                </div>
              </div>
              <div className="flex w-1/2 ps-8">
                <div className="mb-6 w-full">
                  <div className="mb-3">
                    <h4 className="formtitle pb-1 border-b border-light-gray">
                      Other Details
                    </h4>
                  </div>

                  <div className="grid grid-cols-2 gap-4">
                    <div className="flex flex-col">
                      <label className="labels mb-1">
                        Global Gap Number{" "}
                        {isMandatoryField.ggnIsRequired ? (
                          <span className="text-red-500">*</span>
                        ) : (
                          ""
                        )}
                      </label>
                      <input
                        type="number"
                        name="global_gap_number"
                        maxLength={13}
                        value={globalGapNumber}
                        onChange={handleGlobalGapNumberChange}
                        style={{
                          borderColor: globalGapNumberErrorVisibility
                            ? "#f56565"
                            : "#d1d5db",
                        }}
                        className="w-full px-2 2xl:px-3 border rounded-md"
                        inputMode="numeric"
                      />
                      {globalGapNumberErrorVisibility ? (
                        <p className="text-red-500">
                          Please enter a valid ggn number.
                        </p>
                      ) : (
                        void 0
                      )}
                    </div>
                    <div className="flex flex-col">
                      <label className="labels mb-1">Customer Site Code</label>
                      <input
                        type="text"
                        name="customer_site_code"
                        maxLength={50}
                        value={customerSiteCode}
                        onChange={handleCustomerSiteCodeChange}
                        style={{
                          borderColor: customerSiteCodeErrorVisibility
                            ? "#f56565"
                            : "#d1d5db",
                        }}
                        className="w-full px-2 2xl:px-3 border rounded-md"
                      />
                      {customerSiteCodeErrorVisibility ? (
                        <p className="text-red-500">
                          Please enter a valid code less than 50 characters.
                        </p>
                      ) : (
                        void 0
                      )}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div className="flex justify-between border-t border-light-gray py-5 bg-white">
            <div>
              <button
                className="border border-skin-primary text-skin-primary rounded-md py-1 px-8"
                onClick={onPrev}
              >
                Previous
              </button>
            </div>
            <div>
              <button
                className="border border-skin-primary text-skin-primary me-10 py-1 px-8 font-medium rounded-md"
                onClick={(e) => handleValidate("sae", e, data[0])}
              >
                Save & Exit
              </button>
              <button
                type="button"
                className="border border-skin-primary bg-skin-primary text-white rounded-md py-1 px-8 font-medium"
                onClick={(e) => handleValidate("sap", e, data[0])}
              >
                Save & Proceed
              </button>
            </div>
          </div>
        </div>
      )}
      <Transition appear show={isOpen} as={Fragment}>
        <Dialog as="div" className="relative z-10" onClose={closeModal}>
          <Transition.Child
            as={Fragment}
            enter="ease-out duration-300"
            enterFrom="opacity-0"
            enterTo="opacity-100"
            leave="ease-in duration-200"
            leaveFrom="opacity-100"
            leaveTo="opacity-0"
          >
            <div className="fixed inset-0 bg-black bg-opacity-25 backdrop-blur-sm" />
          </Transition.Child>

          <div className="fixed inset-0 overflow-y-auto">
            <div className="flex items-center justify-center min-h-full p-4 text-center">
              <Transition.Child
                as={Fragment}
                enter="ease-out duration-300"
                enterFrom="opacity-0 scale-95"
                enterTo="opacity-100 scale-100"
                leave="ease-in duration-200"
                leaveFrom="opacity-100 scale-100"
                leaveTo="opacity-0 scale-95"
              >
                <Dialog.Panel className=" w-[45%] transform overflow-hidden rounded-xl bg-white  text-left align-middle shadow-xl transition-all">
                  {/* <!-- Modal content --> */}
                  <div className="relative bg-white rounded-lg shadow">
                    {/* <!-- Modal header --> */}
                    <div className="flex items-start justify-between p-8 rounded-t">
                      <h3 className="flex flex-row text-xl font-semibold text-gray-900  items-center">
                        <span className="flex justify-center items-center border border-skin-primary text-skin-primary w-[25px] h-[25px] text-center leading-5 rounded-full text-base me-4">
                          <FontAwesomeIcon icon={faInfo} />{" "}
                        </span>{" "}
                        Warning
                      </h3>
                      <button
                        onClick={closeModal}
                        type="button"
                        className="text-black bg-transparent hover:bg-gray-200 hover:text-gray-900 rounded-lg text-xl w-8 h-8 ml-auto inline-flex justify-center items-center "
                        data-modal-hide="default-modal"
                      >
                        <FontAwesomeIcon
                          icon={faXmark}
                          className="text-skin-primary"
                        />{" "}
                      </button>
                    </div>
                    {/* <!-- Modal body --> */}
                    <div className="p-8 py-0 space-y-6">
                      <p className="text-base xl:text-md 2xl:text-lg leading-relaxed mt-2">
                        Mandatory information missing. Do you want to continue?
                      </p>
                    </div>
                    {/* <!-- Modal footer --> */}
                    <div className="flex items-end p-6 space-x-2 justify-end">
                      <button
                        onClick={closeModal}
                        data-modal-hide="default-modal"
                        type="button"
                        className="border border-skin-primary text-skin-primary focus:ring-4 focus:outline-none focus:ring-blue-300 font-medium rounded-md text-md px-6 py-2 text-center "
                      >
                        Cancel
                      </button>
                      <button
                        onClick={(e) => handleContinueSubmit(e, data[0])}
                        data-modal-hide="default-modal"
                        type="button"
                        className="text-white bg-skin-primary hover:bg-skin-primary focus:ring-4 focus:outline-none focus:ring-blue-300 font-medium rounded-md text-md px-6 py-2 text-center "
                      >
                        Continue
                      </button>
                    </div>
                  </div>
                </Dialog.Panel>
              </Transition.Child>
            </div>
          </div>
        </Dialog>
      </Transition>
    </>
  );
};

export default ComplianceSection;
