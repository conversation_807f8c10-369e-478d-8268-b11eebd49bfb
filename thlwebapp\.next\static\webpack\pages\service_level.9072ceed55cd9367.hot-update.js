"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/service_level",{

/***/ "./components/service_level/SLTable.js":
/*!*********************************************!*\
  !*** ./components/service_level/SLTable.js ***!
  \*********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _ViewDetails__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./ViewDetails */ \"./components/service_level/ViewDetails.jsx\");\n/* harmony import */ var _services_apiConfig__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/services/apiConfig */ \"./services/apiConfig.js\");\n/* harmony import */ var _ReasonsDetails__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./ReasonsDetails */ \"./components/service_level/ReasonsDetails.jsx\");\n/* harmony import */ var _utils_getCookieData__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/utils/getCookieData */ \"./utils/getCookieData.js\");\n/* harmony import */ var _common_NoDataFound__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../common/NoDataFound */ \"./components/common/NoDataFound.jsx\");\n/* harmony import */ var _fluentui_react_components__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @fluentui/react-components */ \"./node_modules/@fluentui/react-components/lib/index.js\");\n/* harmony import */ var react_toastify__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! react-toastify */ \"./node_modules/react-toastify/dist/react-toastify.esm.mjs\");\n/* harmony import */ var js_cookie__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! js-cookie */ \"./node_modules/js-cookie/dist/js.cookie.mjs\");\n/* harmony import */ var _utils_secureStorage__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/utils/secureStorage */ \"./utils/secureStorage.js\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n//\n\n\nconst SLTable = (param)=>{\n    let { gridcolumnRefs, gridcolumnWidths, setIsTableRendered, checkedStates, customerSLData, userData, selectedProducts, seeAll, recordCount, setRecordsCount, searchBoxContent, slFilters, selectedMasterProductCode, selectedCustomer, toggle, selectedRows, setSelectedRows, isBulkUpdate, setIsBulkUpdate, bulkUpdateData, isOpen, setIsOpen, setBulkUpdateData, masterProducts, bulkDeleteOrdIds, setBulkDeleteOrdIds, setNoDataExists, setShowLoadingMessage, setAllReasonsSubreasons, allReasonsSubreasons, selectedReasons, setSelectedReasons, selectedSubReasons, setSelectedSubReasons, showLoadingMessage } = param;\n    var _checkedStates_columns, _checkedStates_columns1, _checkedStates_columns2, _checkedStates_columns3, _checkedStates_columns4, _checkedStates_columns5, _checkedStates_columns6, _columnTotals_totalValue;\n    _s();\n    let ADCompanyName = (userData === null || userData === void 0 ? void 0 : userData.companyName) || (userData === null || userData === void 0 ? void 0 : userData.ADCompanyName);\n    const [reasonsMasterList, setReasonsMasterList] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [parentReasonList, setParentReasonList] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [reasonsData, setReasonsData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [allSelectedProducts, setAllSelectedProducts] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isHeaderChecked, setIsHeaderChecked] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [columnTotals, setColumnTotals] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const setMapForReasonsParentsAndTheirCorrespondingChildren = ()=>{\n        setParentReasonList((prevList)=>{\n            const parentReasonIds = reasonsMasterList.filter((typeOfReason)=>typeOfReason.parent_id === null).map((typeOfReason)=>typeOfReason);\n            const uniqueParentIds = new Set([\n                ...prevList,\n                ...parentReasonIds\n            ]);\n            return Array.from(uniqueParentIds); // Update the state\n        });\n    };\n    const handleCheckboxChange = (data)=>{\n        setSelectedRows((prevSelected)=>{\n            let updatedSelected;\n            if (prevSelected.includes(data)) {\n                updatedSelected = prevSelected.filter((item)=>item !== data);\n            } else {\n                updatedSelected = [\n                    ...prevSelected,\n                    data\n                ];\n            }\n            const ordIds = updatedSelected.filter((product)=>product.CASES_ADDED_REASONS > 0).map((product)=>product.ORD_ID);\n            setBulkDeleteOrdIds(ordIds.length > 0 ? ordIds : []);\n            return updatedSelected;\n        });\n    };\n    const handleHeaderCheckboxChange = (data)=>{\n        const selectableRows = data.filter((product)=>(product.LOCKED_BY === null || product.LOCKED_BY === \"\") && product.CASES_DIFFERENCE != 0);\n        const ordIds = selectableRows.filter((product)=>product.CASES_ADDED_REASONS > 0).map((product)=>product.ORD_ID);\n        if (ordIds.length > 0) {\n            setBulkDeleteOrdIds(ordIds);\n        } else {\n            setBulkDeleteOrdIds([]);\n        }\n        if (selectedRows.length === selectableRows.length) {\n            // If all rows are selected, deselect all\n            setSelectedRows([]);\n            setIsHeaderChecked(false);\n        } else {\n            // If not all rows are selected, select all\n            setSelectedRows(selectableRows);\n            setIsHeaderChecked(true);\n        }\n    };\n    //#region getReasons\n    const fetchReasonData = async (orderId, customerName)=>{\n        setReasonsData([]);\n        const serverAddress = _services_apiConfig__WEBPACK_IMPORTED_MODULE_3__.apiConfig.serverAddress;\n        try {\n            const serviceLevelReasons = await fetch(\"\".concat(serverAddress, \"serviceLevel/get-service-level-reasons/\").concat(orderId, \"?customerName=\").concat(customerName), {\n                method: \"GET\",\n                headers: {\n                    Accept: \"application/json\",\n                    \"Content-Type\": \"application/json\"\n                },\n                credentials: \"include\"\n            }).then((res)=>{\n                if (res.status === 400) {\n                    react_toastify__WEBPACK_IMPORTED_MODULE_7__.toast.error(\"There was an error with your request. Please check your data and try again.\");\n                    return null;\n                } else if (res.status === 401) {\n                    react_toastify__WEBPACK_IMPORTED_MODULE_7__.toast.error(\"Your session has expired. Please log in again.\");\n                    setTimeout(async ()=>{\n                        await (0,_utils_secureStorage__WEBPACK_IMPORTED_MODULE_9__.logout)();\n                        const redirectUrl = \"/login?redirect=\".concat(encodeURIComponent(window.location.pathname));\n                        router.push(redirectUrl);\n                    }, 3000);\n                    return null;\n                }\n                if (res.status === 200) {\n                    return res.json();\n                }\n            }) // Ensure you parse the JSON\n            .then((data)=>{\n                if (!isBulkUpdate) {\n                    setReasonsData(data);\n                }\n            });\n        } catch (error) {\n            console.log(\"error in fetching\", error);\n            react_toastify__WEBPACK_IMPORTED_MODULE_7__.toast.error(\"Error fetching reasons:\".concat(error.message), {\n                position: \"top-right\"\n            });\n        }\n    };\n    //#endregion\n    //#region getMasterReasons\n    const fetchReasonMaster = async ()=>{\n        // console.log(\"service Level Reasons Master code start\");\n        try {\n            const serverAddress = _services_apiConfig__WEBPACK_IMPORTED_MODULE_3__.apiConfig.serverAddress;\n            const serviceLevelReasonsMaster = await fetch(\"\".concat(serverAddress, \"serviceLevel/get-service-level-reasons-master\"), {\n                method: \"GET\",\n                headers: {\n                    Accept: \"application/json\",\n                    \"Content-Type\": \"application/json\"\n                },\n                credentials: \"include\"\n            }).then((res)=>{\n                if (res.status === 400) {\n                    react_toastify__WEBPACK_IMPORTED_MODULE_7__.toast.error(\"There was an error with your request. Please check your data and try again.\");\n                    return null;\n                } else if (res.status === 401) {\n                    react_toastify__WEBPACK_IMPORTED_MODULE_7__.toast.error(\"Your session has expired. Please log in again.\");\n                    setTimeout(()=>{\n                        localStorage.removeItem(\"superUser\");\n                        js_cookie__WEBPACK_IMPORTED_MODULE_8__[\"default\"].remove(\"company\");\n                        js_cookie__WEBPACK_IMPORTED_MODULE_8__[\"default\"].remove(\"ADCompanyName\");\n                        localStorage.removeItem(\"id\");\n                        localStorage.removeItem(\"name\");\n                        localStorage.removeItem(\"role\");\n                        localStorage.removeItem(\"email\");\n                        js_cookie__WEBPACK_IMPORTED_MODULE_8__[\"default\"].remove(\"user\");\n                        js_cookie__WEBPACK_IMPORTED_MODULE_8__[\"default\"].remove(\"theme\");\n                        js_cookie__WEBPACK_IMPORTED_MODULE_8__[\"default\"].remove(\"token\");\n                        const redirectUrl = \"/login?redirect=\".concat(encodeURIComponent(window.location.pathname));\n                        logoutHandler(instance, redirectUrl);\n                    }, 3000);\n                    return null;\n                }\n                if (res.status === 200) {\n                    return res.json();\n                }\n            }) // Ensure you parse the JSON\n            .then((data)=>{\n                setReasonsMasterList(data);\n            });\n        } catch (err) {\n            console.log(\"error in fetching\", err);\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        setIsTableRendered((prev)=>!prev);\n    }, [\n        setIsTableRendered\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        fetchReasonMaster(); // To fetch the master list of reasons\n        setMapForReasonsParentsAndTheirCorrespondingChildren();\n    }, []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const cookieSelectedProducts = (slFilters === null || slFilters === void 0 ? void 0 : slFilters.selectedProducts) || [];\n        const stateSelectedProducts = selectedProducts || [];\n        const combinedSelectedProducts = [\n            ...cookieSelectedProducts.map((product)=>({\n                    label: product.productDescription,\n                    value: product.altFillId\n                })),\n            ...stateSelectedProducts.map((product)=>({\n                    label: product.label,\n                    value: product.value\n                }))\n        ];\n        const customerSLArray = Object.values(customerSLData);\n        let filteredProducts = combinedSelectedProducts.length ? customerSLArray.filter((product)=>combinedSelectedProducts.some((selectedProduct)=>selectedProduct.label === product.PRODUCT_DESCRIPTION && (!searchBoxContent || !!searchBoxContent && product.ORD_NUMBER.toString().includes(searchBoxContent)))) : customerSLArray.filter((product)=>!searchBoxContent || !!searchBoxContent && product.ORD_NUMBER.toString().includes(searchBoxContent));\n        if (selectedMasterProductCode !== \"all\") {\n            filteredProducts = filteredProducts.filter((prod)=>prod.MASTER_PRODUCT_CODE == selectedMasterProductCode);\n        }\n        if (selectedReasons.length > 0) {\n            filteredProducts = filteredProducts.filter((product)=>product.reasons && product.reasons.some((reason)=>selectedReasons.includes(reason.MAIN_REASON_ID)));\n        } else if (selectedSubReasons.length > 0) {\n            filteredProducts = filteredProducts.filter((product)=>product.reasons && product.reasons.some((reason)=>selectedSubReasons.includes(reason.SUB_REASON_ID)));\n        }\n        if (filteredProducts.length > 0) {\n            setAllSelectedProducts(filteredProducts);\n            const visibleRows = filteredProducts.filter((data)=>{\n                if (!seeAll && data.CASES_DIFFERENCE - data.CASES_ADDED_REASONS === 0) {\n                    return false;\n                }\n                return true;\n            });\n            const totals = visibleRows.reduce((acc, data)=>{\n                acc.casesOrdered += data.CASES_ORIGINAL || 0;\n                acc.casesDelivered += data.CASES_DELIVERED || 0;\n                acc.casesDifference += data.CASES_DIFFERENCE || 0;\n                acc.addedReasons += data.CASES_ADDED_REASONS || 0;\n                acc.totalValue += (data.CASE_SIZE || 0) * (data.UNIT_PRICE || 0) * (data.CASES_DIFFERENCE || 0);\n                return acc;\n            }, {\n                casesOrdered: 0,\n                casesDelivered: 0,\n                casesDifference: 0,\n                addedReasons: 0,\n                totalValue: 0\n            });\n            setColumnTotals(totals);\n        }\n        var _filteredProducts_length;\n        setRecordsCount((_filteredProducts_length = filteredProducts === null || filteredProducts === void 0 ? void 0 : filteredProducts.length) !== null && _filteredProducts_length !== void 0 ? _filteredProducts_length : 0);\n    }, [\n        toggle,\n        customerSLData,\n        selectedProducts,\n        slFilters,\n        searchBoxContent,\n        selectedMasterProductCode,\n        selectedReasons,\n        selectedSubReasons\n    ]);\n    const [selectedData, setSelectedData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const handleViewDetailsClick = (data)=>{\n        setSelectedRows([\n            data\n        ]);\n        setIsOpen(true);\n    };\n    var _columnTotals_casesOrdered, _columnTotals_casesDelivered, _columnTotals_casesDifference, _columnTotals_addedReasons, _columnTotals_totalValue1;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"!fontFamily-poppinsregular\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_toastify__WEBPACK_IMPORTED_MODULE_7__.ToastContainer, {\n                limit: 1\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                lineNumber: 329,\n                columnNumber: 7\n            }, undefined),\n            recordCount != 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                className: \"service-level-grid relative table-fixed w-full text-sm !fontFamily-poppinsregular\",\n                cellSpacing: 0,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                    className: \"sticky top-0 z-10 !w-10\",\n                                    ref: gridcolumnRefs.checkboxRef,\n                                    style: {\n                                        left: \"\".concat(0, \"px\")\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-full flex justify-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"checkbox\",\n                                            className: \"w-5 h-5 text-blue border-theme-blue2 rounded accent-skin-primary text-center\",\n                                            disabled: selectedMasterProductCode == \"all\" && masterProducts.length > 2 || selectedCustomer == \"All Customers\",\n                                            onChange: ()=>handleHeaderCheckboxChange(allSelectedProducts),\n                                            checked: isHeaderChecked\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                            lineNumber: 344,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                        lineNumber: 343,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                    lineNumber: 338,\n                                    columnNumber: 15\n                                }, undefined),\n                                checkedStates.columns.depotdate && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                    className: \"sticky top-0 z-10\",\n                                    ref: gridcolumnRefs.depotdate,\n                                    style: {\n                                        left: \"\".concat(gridcolumnWidths.checkboxWidth, \"px\")\n                                    },\n                                    children: \"Depot Date\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                    lineNumber: 360,\n                                    columnNumber: 17\n                                }, undefined),\n                                ADCompanyName == \"Integrated Service Solutions Ltd\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                    className: \"sticky top-0 z-10 !text-center\",\n                                    ref: gridcolumnRefs.serviceCustomers,\n                                    style: {\n                                        left: \"\".concat(gridcolumnWidths.checkboxWidth, \"px\")\n                                    },\n                                    children: [\n                                        \"Service Customers\",\n                                        \" \"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                    lineNumber: 368,\n                                    columnNumber: 69\n                                }, undefined),\n                                checkedStates.columns.weekNo && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                    className: \"sticky top-0  z-10\",\n                                    ref: gridcolumnRefs.weekNo,\n                                    style: {\n                                        left: \"\".concat(ADCompanyName == \"Integrated Service Solutions Ltd\" ? gridcolumnWidths.serviceCustomerswidth : gridcolumnWidths.depotdate, \"px\")\n                                    },\n                                    children: \"Week No\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                    lineNumber: 377,\n                                    columnNumber: 17\n                                }, undefined),\n                                checkedStates.columns.altfill && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                    className: \"sticky top-0  z-10\",\n                                    ref: gridcolumnRefs.altfill,\n                                    style: {\n                                        left: \"\".concat(gridcolumnWidths.weekNo, \"px\")\n                                    },\n                                    children: \"Alt Fill\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                    lineNumber: 388,\n                                    columnNumber: 17\n                                }, undefined),\n                                checkedStates.columns.customer && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                    className: \"!w-28 sticky top-0  z-10 \",\n                                    ref: gridcolumnRefs.customer,\n                                    style: {\n                                        left: \"\".concat(gridcolumnWidths.altfill, \"px\")\n                                    },\n                                    children: \"Customer\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                    lineNumber: 397,\n                                    columnNumber: 17\n                                }, undefined),\n                                checkedStates.columns.salesorder && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                    className: \"sticky top-0  z-10 \",\n                                    ref: gridcolumnRefs.salesorder,\n                                    style: {\n                                        left: \"\".concat(gridcolumnWidths.customer, \"px\")\n                                    },\n                                    children: \"Sales Order\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                    lineNumber: 406,\n                                    columnNumber: 17\n                                }, undefined),\n                                checkedStates.columns.salesOrderId && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                    className: \"sticky top-0  z-10 \",\n                                    ref: gridcolumnRefs.salesOrderId,\n                                    style: {\n                                        left: \"\".concat(gridcolumnWidths.customer, \"px\")\n                                    },\n                                    children: \"Order Det Id\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                    lineNumber: 415,\n                                    columnNumber: 17\n                                }, undefined),\n                                checkedStates.columns.product && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                    className: \"!w-80 sticky top-0  z-10 \",\n                                    ref: gridcolumnRefs.product,\n                                    style: {\n                                        left: \"\".concat(gridcolumnWidths.salesorder, \"px\")\n                                    },\n                                    children: \"Product\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                    lineNumber: 424,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                    className: \"top-0  z-10 !text-center\",\n                                    ref: gridcolumnRefs.casesize,\n                                    style: {\n                                        left: \"\".concat(gridcolumnWidths.product, \"px\")\n                                    },\n                                    children: \"Case Size\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                    lineNumber: 432,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                    className: \"sticky top-0 !text-center\",\n                                    children: \"Cases Ordered\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                    lineNumber: 439,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                    className: \"sticky top-0 !text-center\",\n                                    children: \"Cases Delivered\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                    lineNumber: 440,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                    className: \"sticky top-0 !text-center\",\n                                    children: \"Cases Different\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                    lineNumber: 441,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                    className: \"sticky top-0 !text-center\",\n                                    children: \"Added Reasons\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                    lineNumber: 442,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                    className: \"!w-28 sticky top-0 !text-center\",\n                                    children: \"Order Fulfillment %\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                    lineNumber: 443,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                    className: \"sticky top-0 !text-center\",\n                                    children: \"Unit Price\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                    lineNumber: 446,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                    className: \"sticky top-0 !text-center\",\n                                    children: \"Case Value\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                    lineNumber: 447,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                    className: \"sticky top-0 !text-center\",\n                                    children: \"Total Value\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                    lineNumber: 448,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                    className: \"sticky top-0 !text-center\",\n                                    children: \"Status\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                    lineNumber: 449,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                    className: \"!w-60 relative\",\n                                    children: [\n                                        \"Reason\",\n                                        \" \",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ReasonsDetails__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                            reasonsData: allReasonsSubreasons.reasons,\n                                            selectedReasons: selectedReasons,\n                                            selectedSubReasons: selectedSubReasons,\n                                            setSelectedReasons: setSelectedReasons,\n                                            setSelectedSubReasons: setSelectedSubReasons,\n                                            type: \"reasonsList\",\n                                            seeAll: seeAll\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                            lineNumber: 452,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                    lineNumber: 450,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                    className: \"!w-60 relative\",\n                                    children: [\n                                        \"Sub Reason\",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ReasonsDetails__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                            reasonsData: allReasonsSubreasons.subReasons,\n                                            selectedReasons: selectedReasons,\n                                            selectedSubReasons: selectedSubReasons,\n                                            setSelectedReasons: setSelectedReasons,\n                                            setSelectedSubReasons: setSelectedSubReasons,\n                                            type: \"subReasonsList\",\n                                            seeAll: seeAll\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                            lineNumber: 464,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                    lineNumber: 462,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                    className: \"!w-52\",\n                                    children: \"Comments\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                    lineNumber: 474,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                    className: \"!text-center\",\n                                    children: \"Actions\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                    lineNumber: 475,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                            lineNumber: 337,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                        lineNumber: 336,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                className: \"font-bold bg-[#f3f8ff]\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                        className: \"text-center sticky top-0 font-bold text-sm\",\n                                        style: {\n                                            left: \"\".concat(0, \"px\")\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                        lineNumber: 481,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    (checkedStates === null || checkedStates === void 0 ? void 0 : (_checkedStates_columns = checkedStates.columns) === null || _checkedStates_columns === void 0 ? void 0 : _checkedStates_columns.depotdate) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                        className: \"text-center sticky top-0 font-bold text-sm\",\n                                        style: {\n                                            left: \"\".concat(gridcolumnWidths.checkboxWidth, \"px\")\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                        lineNumber: 486,\n                                        columnNumber: 19\n                                    }, undefined),\n                                    ADCompanyName == \"Integrated Service Solutions Ltd\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                        className: \"text-center sticky top-0 font-bold text-sm\",\n                                        style: {\n                                            left: \"\".concat(gridcolumnWidths.depotDate, \"px\")\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                        lineNumber: 491,\n                                        columnNumber: 71\n                                    }, undefined),\n                                    (checkedStates === null || checkedStates === void 0 ? void 0 : (_checkedStates_columns1 = checkedStates.columns) === null || _checkedStates_columns1 === void 0 ? void 0 : _checkedStates_columns1.weekNo) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                        className: \"sticky top-0 font-bold text-center text-sm\",\n                                        style: {\n                                            left: \"\".concat(gridcolumnWidths.serviceCustomerswidth, \"px\")\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                        lineNumber: 497,\n                                        columnNumber: 19\n                                    }, undefined),\n                                    (checkedStates === null || checkedStates === void 0 ? void 0 : (_checkedStates_columns2 = checkedStates.columns) === null || _checkedStates_columns2 === void 0 ? void 0 : _checkedStates_columns2.altfill) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                        className: \"sticky top-0 font-bold text-sm\",\n                                        style: {\n                                            left: \"\".concat(gridcolumnWidths.weekNo, \"px\")\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                        lineNumber: 503,\n                                        columnNumber: 19\n                                    }, undefined),\n                                    (checkedStates === null || checkedStates === void 0 ? void 0 : (_checkedStates_columns3 = checkedStates.columns) === null || _checkedStates_columns3 === void 0 ? void 0 : _checkedStates_columns3.customer) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                        className: \"sticky top-0 font-bold text-sm\",\n                                        style: {\n                                            left: \"\".concat(gridcolumnWidths.altfill, \"px\")\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                        lineNumber: 509,\n                                        columnNumber: 19\n                                    }, undefined),\n                                    (checkedStates === null || checkedStates === void 0 ? void 0 : (_checkedStates_columns4 = checkedStates.columns) === null || _checkedStates_columns4 === void 0 ? void 0 : _checkedStates_columns4.salesorder) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                        className: \"sticky top-0 font-bold text-sm\",\n                                        style: {\n                                            left: \"\".concat(gridcolumnWidths.customer, \"px\")\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                        lineNumber: 515,\n                                        columnNumber: 19\n                                    }, undefined),\n                                    (checkedStates === null || checkedStates === void 0 ? void 0 : (_checkedStates_columns5 = checkedStates.columns) === null || _checkedStates_columns5 === void 0 ? void 0 : _checkedStates_columns5.salesOrderId) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                        className: \"sticky top-0 font-bold text-sm\",\n                                        style: {\n                                            left: \"\".concat(gridcolumnWidths.customer, \"px\")\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                        lineNumber: 521,\n                                        columnNumber: 19\n                                    }, undefined),\n                                    (checkedStates === null || checkedStates === void 0 ? void 0 : (_checkedStates_columns6 = checkedStates.columns) === null || _checkedStates_columns6 === void 0 ? void 0 : _checkedStates_columns6.product) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                        className: \"sticky top-0 font-bold text-sm\",\n                                        style: {\n                                            left: \"\".concat(gridcolumnWidths.salesorder, \"px\")\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                        lineNumber: 527,\n                                        columnNumber: 19\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                        className: \"text-center top-0 font-bold text-sm\",\n                                        style: {\n                                            left: \"\".concat(gridcolumnWidths.product, \"px\")\n                                        },\n                                        children: \"TOTAL\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                        lineNumber: 532,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                        className: \"!text-center sticky top-0 font-bold text-sm\",\n                                        children: (_columnTotals_casesOrdered = columnTotals.casesOrdered) !== null && _columnTotals_casesOrdered !== void 0 ? _columnTotals_casesOrdered : \"-\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                        lineNumber: 539,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                        className: \"!text-center sticky top-0 font-bold text-sm\",\n                                        children: (_columnTotals_casesDelivered = columnTotals.casesDelivered) !== null && _columnTotals_casesDelivered !== void 0 ? _columnTotals_casesDelivered : \"-\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                        lineNumber: 542,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                        className: \"!text-center sticky top-0 font-bold text-sm\",\n                                        children: (_columnTotals_casesDifference = columnTotals.casesDifference) !== null && _columnTotals_casesDifference !== void 0 ? _columnTotals_casesDifference : \"-\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                        lineNumber: 545,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                        className: \"!text-center sticky top-0 font-bold text-sm\",\n                                        children: (_columnTotals_addedReasons = columnTotals.addedReasons) !== null && _columnTotals_addedReasons !== void 0 ? _columnTotals_addedReasons : \"-\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                        lineNumber: 548,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                        className: \"sticky top-0 font-bold text-sm\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                        lineNumber: 551,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                        className: \"sticky top-0 font-bold text-sm\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                        lineNumber: 552,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                        className: \"sticky top-0 font-bold text-sm\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                        lineNumber: 553,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                        className: \"!text-center sticky top-0 font-bold text-sm\",\n                                        children: ((_columnTotals_totalValue1 = columnTotals.totalValue) !== null && _columnTotals_totalValue1 !== void 0 ? _columnTotals_totalValue1 : \"-\") ? \"\\xa3\".concat((_columnTotals_totalValue = columnTotals.totalValue) === null || _columnTotals_totalValue === void 0 ? void 0 : _columnTotals_totalValue.toFixed(2)) : \"-\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                        lineNumber: 554,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                        className: \"sticky top-0 font-bold text-sm\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                        lineNumber: 560,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                        className: \"sticky top-0 font-bold text-sm\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                        lineNumber: 561,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                        className: \"sticky top-0 font-bold text-sm\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                        lineNumber: 562,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                        className: \"sticky top-0 font-bold text-sm\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                        lineNumber: 563,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                        className: \"sticky top-0 font-bold text-sm\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                        lineNumber: 564,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                lineNumber: 480,\n                                columnNumber: 15\n                            }, undefined),\n                            allSelectedProducts.map((data, index)=>{\n                                var _data_reasons, _checkedStates_columns, _checkedStates_columns1, _checkedStates_columns2, _checkedStates_columns3, _checkedStates_columns4, _checkedStates_columns5, _checkedStates_columns6, _data_SERVICE_LEVEL_PERCENT, _data_UNIT_PRICE, _this, _this1, _data_reasons_, _data_reasons_1, _data_reasons_2;\n                                if (!seeAll && data.CASES_DIFFERENCE - data.CASES_ADDED_REASONS == 0) return;\n                                const tooltip = data.LOCKED_BY ? \"\".concat(data.LOCKED_BY, \" is currently editing this order.\") : data.NEW_LINE_FLAG == 1 ? \"This is an additional order.\" : \"\";\n                                const isSelected = selectedRows === null || selectedRows === void 0 ? void 0 : selectedRows.includes(data);\n                                const rowHighlight = \"\".concat(isSelected ? \"bg-locked-products\" : ((_data_reasons = data.reasons) === null || _data_reasons === void 0 ? void 0 : _data_reasons.length) > 0 && data.CASES_DIFFERENCE - data.CASES_ADDED_REASONS > 0 ? \"bg-needsupdate-status\" : !!data.LOCKED_BY ? \"bg-locked-products\" : data.NEW_LINE_FLAG == 1 ? \"bg-volumechange-status\" : \"\");\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                            className: \"sticky top-0 font-normal text-sm \".concat(rowHighlight ? \"!\".concat(rowHighlight) : \"\"),\n                                            style: {\n                                                left: \"\".concat(0, \"px\")\n                                            },\n                                            title: tooltip,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-full flex justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"checkbox\",\n                                                    title: \"\".concat(data.NEW_LINE_FLAG == 1 ? \"This is an additional order.\" : \"\"),\n                                                    className: \"w-5 h-5 text-blue border-theme-blue2 rounded accent-skin-primary\",\n                                                    onChange: ()=>handleCheckboxChange(data),\n                                                    disabled: selectedMasterProductCode === \"all\" && masterProducts.length > 2 || selectedCustomer === \"All Customers\" || data.LOCKED_BY !== null && data.LOCKED_BY !== \"\" || data.CASES_DIFFERENCE == 0,\n                                                    // disabled={data.CASES_ADDED_REASONS!=0}\n                                                    checked: selectedRows.includes(data)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                                    lineNumber: 602,\n                                                    columnNumber: 23\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                                lineNumber: 601,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                            lineNumber: 594,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        (checkedStates === null || checkedStates === void 0 ? void 0 : (_checkedStates_columns = checkedStates.columns) === null || _checkedStates_columns === void 0 ? void 0 : _checkedStates_columns.depotdate) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                            className: \"sticky top-0 font-normal text-sm \".concat(rowHighlight ? \"!\".concat(rowHighlight) : \"\"),\n                                            style: {\n                                                left: \"\".concat(gridcolumnWidths.checkboxWidth, \"px\")\n                                            },\n                                            title: tooltip,\n                                            children: data.DEPOT_DATE && new Date(data.DEPOT_DATE).toLocaleDateString(\"en-GB\")\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                            lineNumber: 624,\n                                            columnNumber: 21\n                                        }, undefined),\n                                        ADCompanyName == \"Integrated Service Solutions Ltd\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                            className: \"sticky top-0 font-normal text-sm \".concat(rowHighlight ? \"!\".concat(rowHighlight) : \"\"),\n                                            style: {\n                                                left: \"\".concat(gridcolumnWidths.checkboxWidth, \"px\")\n                                            },\n                                            title: tooltip,\n                                            children: data.SERVICE_CUSTOMERS && data.SERVICE_CUSTOMERS\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                            lineNumber: 635,\n                                            columnNumber: 73\n                                        }, undefined),\n                                        (checkedStates === null || checkedStates === void 0 ? void 0 : (_checkedStates_columns1 = checkedStates.columns) === null || _checkedStates_columns1 === void 0 ? void 0 : _checkedStates_columns1.weekNo) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                            className: \"sticky top-0 font-normal text-center text-sm \".concat(rowHighlight ? \"!\".concat(rowHighlight) : \"\"),\n                                            style: {\n                                                left: \"\".concat(gridcolumnWidths.depotdate, \"px\")\n                                            },\n                                            title: tooltip,\n                                            children: data === null || data === void 0 ? void 0 : data.FISCAL_WEEK\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                            lineNumber: 646,\n                                            columnNumber: 21\n                                        }, undefined),\n                                        (checkedStates === null || checkedStates === void 0 ? void 0 : (_checkedStates_columns2 = checkedStates.columns) === null || _checkedStates_columns2 === void 0 ? void 0 : _checkedStates_columns2.altfill) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                            className: \"sticky top-0 font-normal  text-sm \".concat(rowHighlight ? \"!\".concat(rowHighlight) : \"\"),\n                                            style: {\n                                                left: \"\".concat(gridcolumnWidths.weekNo, \"px\")\n                                            },\n                                            title: tooltip,\n                                            children: data.ALTFILID\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                            lineNumber: 657,\n                                            columnNumber: 21\n                                        }, undefined),\n                                        (checkedStates === null || checkedStates === void 0 ? void 0 : (_checkedStates_columns3 = checkedStates.columns) === null || _checkedStates_columns3 === void 0 ? void 0 : _checkedStates_columns3.customer) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                            className: \"sticky top-0 font-normal  text-sm \".concat(rowHighlight ? \"!\".concat(rowHighlight) : \"\"),\n                                            style: {\n                                                left: \"\".concat(gridcolumnWidths.altfill, \"px\")\n                                            },\n                                            title: tooltip,\n                                            children: data.CUSTOMER\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                            lineNumber: 668,\n                                            columnNumber: 21\n                                        }, undefined),\n                                        (checkedStates === null || checkedStates === void 0 ? void 0 : (_checkedStates_columns4 = checkedStates.columns) === null || _checkedStates_columns4 === void 0 ? void 0 : _checkedStates_columns4.salesorder) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                            className: \"sticky top-0 font-normal text-sm \".concat(rowHighlight ? \"!\".concat(rowHighlight) : \"\"),\n                                            style: {\n                                                left: \"\".concat(gridcolumnWidths.customer, \"px\")\n                                            },\n                                            title: tooltip,\n                                            children: data.ORD_NUMBER\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                            lineNumber: 679,\n                                            columnNumber: 21\n                                        }, undefined),\n                                        (checkedStates === null || checkedStates === void 0 ? void 0 : (_checkedStates_columns5 = checkedStates.columns) === null || _checkedStates_columns5 === void 0 ? void 0 : _checkedStates_columns5.salesOrderId) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                            className: \"sticky top-0 font-normal text-sm \".concat(rowHighlight ? \"!\".concat(rowHighlight) : \"\"),\n                                            style: {\n                                                left: \"\".concat(gridcolumnWidths.customer, \"px\")\n                                            },\n                                            title: tooltip,\n                                            children: data.ORD_ID\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                            lineNumber: 690,\n                                            columnNumber: 21\n                                        }, undefined),\n                                        (checkedStates === null || checkedStates === void 0 ? void 0 : (_checkedStates_columns6 = checkedStates.columns) === null || _checkedStates_columns6 === void 0 ? void 0 : _checkedStates_columns6.product) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                            className: \"!w-80 sticky top-0 font-normal text-sm \".concat(rowHighlight ? \"!\".concat(rowHighlight) : \"\"),\n                                            style: {\n                                                left: \"\".concat(gridcolumnWidths.salesorder, \"px\")\n                                            },\n                                            title: tooltip,\n                                            children: data.PRODUCT_DESCRIPTION\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                            lineNumber: 701,\n                                            columnNumber: 21\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                            className: \"top-0 text-center font-normal text-sm \".concat(rowHighlight ? \"!\".concat(rowHighlight) : \"\"),\n                                            style: {\n                                                left: \"\".concat(gridcolumnWidths.product, \"px\")\n                                            },\n                                            title: tooltip,\n                                            children: data.CASE_SIZE\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                            lineNumber: 711,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"!text-center text-sm \".concat(rowHighlight ? \"!\".concat(rowHighlight) : \"\"),\n                                            title: tooltip,\n                                            children: data.CASES_ORIGINAL\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                            lineNumber: 720,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"!text-center text-sm \".concat(rowHighlight ? \"!\".concat(rowHighlight) : \"\"),\n                                            title: tooltip,\n                                            children: data.CASES_DELIVERED\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                            lineNumber: 728,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"!text-center text-sm font-bold \".concat(rowHighlight ? \"!\".concat(rowHighlight) : \"\"),\n                                            title: tooltip,\n                                            children: data.CASES_DIFFERENCE - data.CASES_ADDED_REASONS\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                            lineNumber: 736,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"!text-center text-sm \".concat(rowHighlight ? \"!\".concat(rowHighlight) : \"\"),\n                                            title: tooltip,\n                                            children: data.CASES_ADDED_REASONS\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                            lineNumber: 744,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"!text-center text-sm \".concat(rowHighlight ? \"!\".concat(rowHighlight) : \"\"),\n                                            title: tooltip,\n                                            children: \"\".concat((_data_SERVICE_LEVEL_PERCENT = data.SERVICE_LEVEL_PERCENT) === null || _data_SERVICE_LEVEL_PERCENT === void 0 ? void 0 : _data_SERVICE_LEVEL_PERCENT.toFixed(2), \"%\")\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                            lineNumber: 752,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"!text-center text-sm \".concat(rowHighlight ? \"!\".concat(rowHighlight) : \"\"),\n                                            title: tooltip,\n                                            children: [\n                                                \"\\xa3\",\n                                                (_data_UNIT_PRICE = data.UNIT_PRICE) === null || _data_UNIT_PRICE === void 0 ? void 0 : _data_UNIT_PRICE.toFixed(2)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                            lineNumber: 760,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"!text-center text-sm \".concat(rowHighlight ? \"!\".concat(rowHighlight) : \"\"),\n                                            title: tooltip,\n                                            children: \"\\xa3\".concat((_this = data.CASE_SIZE * data.UNIT_PRICE) === null || _this === void 0 ? void 0 : _this.toFixed(2))\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                            lineNumber: 768,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"!text-center text-sm \".concat(rowHighlight ? \"!\".concat(rowHighlight) : \"\"),\n                                            title: tooltip,\n                                            children: \"\\xa3\".concat((_this1 = data.CASE_SIZE * data.UNIT_PRICE * data.CASES_DIFFERENCE) === null || _this1 === void 0 ? void 0 : _this1.toFixed(2))\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                            lineNumber: 776,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"!text-center text-sm \".concat(rowHighlight ? \"!\".concat(rowHighlight) : \"\"),\n                                            title: tooltip,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"rounded-lg capitalize px-2 py-1 !text-center \".concat(data.ORD_STATUS === \"Cancelled\" ? \"bg-[#ff2929] text-white\" : data.ORD_STATUS === \"Open\" ? \"bg-[#54C5ED] text-white\" : data.ORD_STATUS === \"Invoiced\" ? \"bg-[#FFAE00] text-white\" : data.ORD_STATUS === \"Delivered\" ? \"bg-[#3EAB58] text-white\" : data.ORD_STATUS === \"Picked\" ? \"bg-[#FF6C09] text-white\" : \"bg-qtydiff-status !text-gray-700\" // Default style for any other status\n                                                ),\n                                                title: tooltip,\n                                                children: data.ORD_STATUS.toLowerCase()\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                                lineNumber: 794,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                            lineNumber: 788,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"relative \".concat(rowHighlight ? \"!\".concat(rowHighlight) : \"\"),\n                                            title: tooltip,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: (_data_reasons_ = data.reasons[0]) === null || _data_reasons_ === void 0 ? void 0 : _data_reasons_.MAIN_REASON\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                                    lineNumber: 820,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ReasonsDetails__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                    reasonsData: data.reasons,\n                                                    type: \"reasonsDetails\",\n                                                    seeAll: seeAll\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                                    lineNumber: 821,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                            lineNumber: 813,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"!text-left text-sm capitalize \".concat(rowHighlight ? \"!\".concat(rowHighlight) : \"\"),\n                                            title: tooltip,\n                                            children: (_data_reasons_1 = data.reasons[0]) === null || _data_reasons_1 === void 0 ? void 0 : _data_reasons_1.SUB_REASON\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                            lineNumber: 828,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"!text-left text-sm capitalize \".concat(rowHighlight ? \"!\".concat(rowHighlight) : \"\"),\n                                            title: tooltip,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-full text-truncate2L \",\n                                                children: (_data_reasons_2 = data.reasons[0]) === null || _data_reasons_2 === void 0 ? void 0 : _data_reasons_2.COMMENT\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                                lineNumber: 842,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                            lineNumber: 836,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"!text-center text-sm !bg-[#f3f8ff] \".concat(rowHighlight ? \"!\".concat(rowHighlight) : \"\"),\n                                            title: \"\".concat(!!data.LOCKED_BY ? \"You cannot edit the order while someone is already working on it.\" : \"\"),\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>{\n                                                    handleViewDetailsClick(data);\n                                                    setIsBulkUpdate(false);\n                                                },\n                                                className: \"cursor-pointer\",\n                                                disabled: !!data.LOCKED_BY || data.CASES_DIFFERENCE == 0 || selectedRows.length > 0,\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fluentui_react_components__WEBPACK_IMPORTED_MODULE_10__.Tooltip, {\n                                                    content: \"View Order Details\",\n                                                    relationship: \"label\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        xmlns: \"http://www.w3.org/2000/svg\",\n                                                        viewBox: \"0 0 512 512\",\n                                                        className: \" w-5 h-5 !text-skin-primary \",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            fill: \"currentcolor\",\n                                                            d: \"M480 448c0 17.7-14.3 32-32 32l-256 0c-17.7 0-32-14.3-32-32l0-80-32 0 0 80c0 35.3 28.7 64 64 64l256 0c35.3 0 64-28.7 64-64l0-284.1c0-12.7-5.1-24.9-14.1-33.9L382.1 14.1c-9-9-21.2-14.1-33.9-14.1L192 0c-35.3 0-64 28.7-64 64l0 192 32 0 0-192c0-17.7 14.3-32 32-32l128 0 0 112c0 26.5 21.5 48 48 48l112 0 0 256zm-.5-288L368 160c-8.8 0-16-7.2-16-16l0-111.5c2.8 .7 5.4 2.1 7.4 4.2L475.3 152.6c2.1 2.1 3.5 4.6 4.2 7.4zM283.3 212.7c-6.2-6.2-16.4-6.2-22.6 0s-6.2 16.4 0 22.6L329.4 304 16 304c-8.8 0-16 7.2-16 16s7.2 16 16 16l313.4 0-68.7 68.7c-6.2 6.2-6.2 16.4 0 22.6s16.4 6.2 22.6 0l96-96c6.2-6.2 6.2-16.4 0-22.6l-96-96z\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                                            lineNumber: 878,\n                                                            columnNumber: 27\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                                        lineNumber: 873,\n                                                        columnNumber: 25\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                                    lineNumber: 869,\n                                                    columnNumber: 23\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                                lineNumber: 857,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                            lineNumber: 847,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, index, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                    lineNumber: 593,\n                                    columnNumber: 17\n                                }, undefined);\n                            })\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                        lineNumber: 478,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                lineNumber: 332,\n                columnNumber: 9\n            }, undefined),\n            isOpen && selectedRows.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ViewDetails__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                data: selectedRows,\n                setData: setSelectedRows,\n                setAllSelectedProducts: setAllSelectedProducts,\n                setMapForReasonsParentsAndTheirCorrespondingChildren: setMapForReasonsParentsAndTheirCorrespondingChildren,\n                reasonsMasterList: reasonsMasterList,\n                parentReasonList: parentReasonList,\n                reasonsData: reasonsData,\n                fetchReasonData: fetchReasonData,\n                userData: userData,\n                isOpen: isOpen,\n                setIsOpen: setIsOpen,\n                bulkUpdateData: bulkUpdateData,\n                isBulkUpdate: isBulkUpdate,\n                setReasonsData: setReasonsData,\n                setBulkUpdateData: setBulkUpdateData,\n                setIsHeaderChecked: setIsHeaderChecked,\n                setSelectedRows: setSelectedRows\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                lineNumber: 893,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n        lineNumber: 328,\n        columnNumber: 5\n    }, undefined);\n};\n_s(SLTable, \"vX4vuRMTc2tBY6elYTRITygxPGY=\");\n_c = SLTable;\n/* harmony default export */ __webpack_exports__[\"default\"] = (SLTable);\nvar _c;\n$RefreshReg$(_c, \"SLTable\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevExports = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevExports) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports on update so we can compare the boundary\n                // signatures.\n                module.hot.dispose(function (data) {\n                    data.prevExports = currentExports;\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevExports !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevExports, currentExports)) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevExports !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/service_level/SLTable.js\n"));

/***/ })

});