"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/suppliers",{

/***/ "./utils/exportExcel.js":
/*!******************************!*\
  !*** ./utils/exportExcel.js ***!
  \******************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var exceljs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! exceljs */ \"./node_modules/exceljs/dist/exceljs.min.js\");\n/* harmony import */ var exceljs__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(exceljs__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react_toastify__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react-toastify */ \"./node_modules/react-toastify/dist/react-toastify.esm.mjs\");\n/* harmony import */ var react_toastify_dist_ReactToastify_css__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-toastify/dist/ReactToastify.css */ \"./node_modules/react-toastify/dist/ReactToastify.css\");\n/* harmony import */ var react_toastify_dist_ReactToastify_css__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react_toastify_dist_ReactToastify_css__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _services_apiConfig__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/services/apiConfig */ \"./services/apiConfig.js\");\n/* harmony import */ var _auth_auth__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./auth/auth */ \"./utils/auth/auth.js\");\n\n\n\n\n\n// todo:move the supplierCode column from the excel file to first column\nconst exportExcel = async function(data, isInternal, token, company, userData, prophet_id, requestor_email, isMultiple) {\n    let isProductExtract = arguments.length > 8 && arguments[8] !== void 0 ? arguments[8] : false, onProductSubmit = arguments.length > 9 && arguments[9] !== void 0 ? arguments[9] : false, productEmailParagraph = arguments.length > 10 && arguments[10] !== void 0 ? arguments[10] : \"\", productEmailCommentPlaceholder = arguments.length > 11 && arguments[11] !== void 0 ? arguments[11] : \"\", request_no = arguments.length > 12 && arguments[12] !== void 0 ? arguments[12] : \"\", varietyRequest = arguments.length > 13 && arguments[13] !== void 0 ? arguments[13] : false;\n    let serverAddress = _services_apiConfig__WEBPACK_IMPORTED_MODULE_3__.apiConfig.serverAddress;\n    let isEmergencyAndFinanceNotCompleteObj = [];\n    let supplierNames = [];\n    const workbook = new (exceljs__WEBPACK_IMPORTED_MODULE_0___default().Workbook)();\n    if (isInternal) {\n        data.forEach((sheetData, index1)=>{\n            if (sheetData.length === 0) {\n                console.error(\"sheetData is empty for index:\", index1);\n                return;\n            }\n            const sheetName = sheetData[0] || \"Sheet\".concat(index1 + 1);\n            let worksheet;\n            if (index1 < 9) {\n                if (Array.isArray(sheetName)) {\n                    const actualSheetName = sheetName[0];\n                    worksheet = workbook.addWorksheet(actualSheetName);\n                } else {\n                    worksheet = workbook.addWorksheet(sheetName);\n                }\n            }\n            if (sheetData.length > 1) {\n                let headers;\n                if (index1 < 9) {\n                    headers = Object.keys(sheetData[1]);\n                    worksheet.addRow(headers);\n                }\n                sheetData.slice(1).forEach((row, internalIndex)=>{\n                    if (index1 < 9) {\n                        const rowData = headers.map((header)=>row[header] || \"\");\n                        if (index1 != 4 && index1 != 3) {\n                            worksheet.addRow(rowData);\n                        } else if (index1 == 4) {\n                            worksheet.addRow(rowData);\n                        } else if (index1 == 3 && rowData[1] != \"\") {\n                            worksheet.addRow(rowData);\n                        }\n                    }\n                    if (index1 === 9) {\n                        supplierNames.push({\n                            supplierName: row === null || row === void 0 ? void 0 : row.supplierName,\n                            supplierCode: row === null || row === void 0 ? void 0 : row.supplierCode\n                        });\n                        if (row.isEmergencyAndFinanceNotComplete) {\n                            let isEmergencyAndFinanceNotCompleteSupplier = {\n                                isEmergencyAndFinanceNotComplete: row.isEmergencyAndFinanceNotComplete,\n                                supplierName: row.supplierName\n                            };\n                            isEmergencyAndFinanceNotCompleteObj.push(isEmergencyAndFinanceNotCompleteSupplier);\n                        }\n                        fetch(\"\".concat(serverAddress, \"suppliers/update-supplier/\").concat(row.id), {\n                            method: \"PUT\",\n                            headers: {\n                                Authorization: \"Bearer \".concat(token),\n                                Accept: \"application/json\",\n                                \"Content-Type\": \"application/json\"\n                            },\n                            body: JSON.stringify({\n                                sectionName: \"updateStatus\",\n                                type: \"exportExcel\",\n                                status: 5,\n                                exported: true,\n                                updated_date: new Date().toISOString(),\n                                company_name: row.supplierName,\n                                to: \"Internal\"\n                            })\n                        }).then((res)=>{\n                            if (res.status === 200) {\n                                return res.json();\n                            }\n                            // if (res.status === 401){\n                            //   toast.error(\"Your session has expired. Please log in again.\");\n                            //   setTimeout(() => {\n                            //     const redirectUrl = `/login?redirect=${encodeURIComponent(\n                            //       window.location.pathname\n                            //     )}`;\n                            //     logoutHandler(instance, redirectUrl);\n                            //   }, 3000);\n                            //   return null;\n                            // }\n                            return Promise.reject(res);\n                        }).then((json)=>{\n                            if (json.status == 200) {\n                                return true;\n                            }\n                        }).catch((error)=>{\n                            console.log(error);\n                        });\n                    }\n                });\n            }\n        });\n    } else if (isProductExtract) {\n        if (data.length === 0) {\n            console.error(\"sheetData is empty for index:\");\n            return;\n        }\n        data.forEach((sheetData)=>{\n            var _Object;\n            const sheetName = sheetData[0] || \"Sheet\".concat(index + 1);\n            let worksheet;\n            if (Array.isArray(sheetName)) {\n                const actualSheetName = sheetName[0];\n                worksheet = workbook.addWorksheet(actualSheetName);\n            } else {\n                worksheet = workbook.addWorksheet(sheetName);\n            }\n            // console.log(\"Object.keys(sheetData[1])\",Object?.keys(sheetData[1])? Object?.keys(sheetData[1]) : 'not there');\n            let headers = ((_Object = Object) === null || _Object === void 0 ? void 0 : _Object.keys(sheetData[1])) ? Object.keys(sheetData[1]) : \"null\";\n            if (isMultiple) {\n                headers = headers.slice(0, -1);\n            }\n            worksheet.addRow(headers);\n            sheetData.slice(1).forEach((row, internalIndex)=>{\n                let rowData;\n                rowData = headers.map((header)=>row[header] || \"\");\n                worksheet.addRow(rowData);\n            });\n        });\n    } else {\n        if (data.length === 0) {\n            console.error(\"sheetData is empty for index:\");\n            return;\n        }\n        data.forEach((row, index1)=>{\n            const sheetName = row[0] || \"Sheet\".concat(index1 + 1);\n            let worksheet;\n            if (Array.isArray(sheetName)) {\n                const actualSheetName = sheetName[0];\n                worksheet = workbook.addWorksheet(actualSheetName);\n            } else {\n                worksheet = workbook.addWorksheet(sheetName);\n            }\n            let headers;\n            if (index1 == 1) {\n                headers = Object.keys(row[1]).slice(0, -2);\n            } else {\n                headers = Object.keys(row[1]);\n            }\n            worksheet.addRow(headers);\n            for(let i = 1; i < row.length; i++){\n                if (index1 == 0 && i > 0) {\n                    supplierNames.push({\n                        supplierName: row[i][\"Supplier name\"],\n                        supplierCode: row[i][\"Supplier code\"]\n                    });\n                }\n                let rowData;\n                if (index1 == 1) {\n                    rowData = headers.map((header)=>row[i][header] || \"\").slice(0, -2);\n                } else {\n                    rowData = headers.map((header)=>row[i][header] || \"\");\n                }\n                worksheet.addRow(rowData);\n                if (row[i].isEmergencyAndFinanceNotComplete) {\n                    let isEmergencyAndFinanceNotCompleteSupplier = {\n                        isEmergencyAndFinanceNotComplete: row[i].isEmergencyAndFinanceNotComplete,\n                        supplierName: row[i][\"Supplier name\"]\n                    };\n                    isEmergencyAndFinanceNotCompleteObj.push(isEmergencyAndFinanceNotCompleteSupplier);\n                }\n                if (index1 == 1) {\n                    fetch(\"\".concat(serverAddress, \"suppliers/update-supplier/\").concat(row[i].id), {\n                        method: \"PUT\",\n                        headers: {\n                            Authorization: \"Bearer \".concat(token),\n                            Accept: \"application/json\",\n                            \"Content-Type\": \"application/json\"\n                        },\n                        body: JSON.stringify({\n                            sectionName: \"updateStatus\",\n                            type: \"exportExcel\",\n                            status: 5,\n                            exported: true,\n                            updated_date: new Date().toISOString(),\n                            company_name: row[\"Supplier name\"],\n                            to: \"ISS\"\n                        })\n                    }).then((res)=>{\n                        if (res.status === 200) {\n                            return res.json();\n                        }\n                        // if (res.status === 401){\n                        //   toast.error(\"Your session has expired. Please log in again.\");\n                        //   setTimeout(() => {\n                        //     const redirectUrl = `/login?redirect=${encodeURIComponent(\n                        //       window.location.pathname\n                        //     )}`;\n                        //     logoutHandler(instance, redirectUrl);\n                        //   }, 3000);\n                        //   return null;\n                        // }\n                        return Promise.reject(res);\n                    }).then((json)=>{\n                        if (json.status == 200) {\n                            return true;\n                        }\n                    }).catch((error)=>{\n                        console.log(error);\n                    });\n                }\n            }\n        });\n    }\n    const buffer = await workbook.xlsx.writeBuffer();\n    const blob = new Blob([\n        buffer\n    ], {\n        type: \"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet\"\n    });\n    const blobUrl = URL.createObjectURL(blob);\n    const a = document.createElement(\"a\");\n    a.style.display = \"none\";\n    a.href = blobUrl;\n    const now = new Date();\n    const timestamp = \"\".concat(now.getFullYear(), \"-\").concat((now.getMonth() + 1).toString().padStart(2, \"0\"), \"-\").concat(now.getDate().toString().padStart(2, \"0\"), \"_\").concat(now.getHours().toString().padStart(2, \"0\"), \"-\").concat(now.getMinutes().toString().padStart(2, \"0\"), \"-\").concat(now.getSeconds().toString().padStart(2, \"0\"));\n    if (isInternal) {\n        a.download = \"internal_export_\".concat(timestamp, \"_\").concat(data[0].length - 1 === 1 ? \"S\" : \"G\", \".xlsx\");\n    } else if (isProductExtract) {\n        if (request_no && !varietyRequest) {\n            a.download = \"\".concat(request_no, \"_product_export.xlsx\");\n        } else if (request_no && varietyRequest) {\n            a.download = \"\".concat(request_no, \"_export.xlsx\");\n        } else {\n            a.download = \"product_export.xlsx\";\n        }\n    } else {\n        a.download = \"iss_export_\".concat(timestamp, \"_\").concat(data.length === 1 ? \"S\" : \"G\", \".xlsx\");\n    }\n    let fileName;\n    if (isInternal) {\n        fileName = \"internal_export_\".concat(timestamp, \"_\").concat(data[0].length - 1 === 1 ? \"S\" : \"G\", \".xlsx\");\n    } else if (isProductExtract && !varietyRequest) {\n        fileName = request_no ? \"\".concat(request_no, \"_product_export.xlsx\") : \"product_export.xlsx\";\n    } else if (varietyRequest) {\n        fileName = request_no ? \"\".concat(request_no, \"_NV_export.xlsx\") : \"NV_export.xlsx\";\n    } else {\n        fileName = \"iss_export_\".concat(timestamp, \"_\").concat(data.length === 1 ? \"S\" : \"G\", \".xlsx\");\n    }\n    document.body.appendChild(a);\n    a.click();\n    // Clean up\n    URL.revokeObjectURL(blobUrl);\n    document.body.removeChild(a);\n    react_toastify__WEBPACK_IMPORTED_MODULE_1__.toast.success(\"Request details extracted successfully.\", {\n        toastId: 22,\n        position: \"top-right\",\n        autoClose: 3000,\n        pauseOnHover: false\n    });\n    if (!varietyRequest) {\n        const formData = new FormData();\n        formData.append(\"file\", blob, fileName);\n        formData.append(\"company\", company);\n        formData.append(\"prophet_id\", prophet_id);\n        formData.append(\"name\", userData === null || userData === void 0 ? void 0 : userData.name);\n        formData.append(\"isInternal\", isInternal);\n        formData.append(\"exporterEmail\", userData === null || userData === void 0 ? void 0 : userData.email);\n        formData.append(\"requestorEmail\", requestor_email);\n        formData.append(\"isProductRequest\", isProductExtract);\n        formData.append(\"supplierNames\", JSON.stringify(supplierNames));\n        formData.append(\"onProductSubmit\", onProductSubmit);\n        formData.append(\"request_no\", request_no);\n        formData.append(\"productEmailCommentPlaceholder\", productEmailCommentPlaceholder);\n        formData.append(\"productEmailParagraph\", productEmailParagraph);\n        const serializedData = JSON.stringify(isEmergencyAndFinanceNotCompleteObj);\n        formData.append(\"isEmergencyAndFinanceNotCompleteObj\", serializedData);\n        const response = await fetch(\"\".concat(serverAddress, \"email/send-email\"), {\n            method: \"POST\",\n            body: formData,\n            headers: {\n                Authorization: \"Bearer \".concat(token)\n            }\n        });\n        if (response.ok) {\n            react_toastify__WEBPACK_IMPORTED_MODULE_1__.toast.success(\"Email sent\", {\n                toastId: 22,\n                position: \"top-right\",\n                autoClose: 3000,\n                pauseOnHover: false\n            });\n        } else if (response.status === 401) {\n            // console.log(\"error YES 401\");\n            react_toastify__WEBPACK_IMPORTED_MODULE_1__.toast.error(\"Failed to send email\", {\n                toastId: 22,\n                position: \"top-right\",\n                autoClose: 3000,\n                pauseOnHover: false\n            });\n            return response.status;\n        } else {\n            react_toastify__WEBPACK_IMPORTED_MODULE_1__.toast.error(\"Failed to send email\", {\n                toastId: 22,\n                position: \"top-right\",\n                autoClose: 3000,\n                pauseOnHover: false\n            });\n        }\n    }\n    return true;\n};\n/* harmony default export */ __webpack_exports__[\"default\"] = (exportExcel);\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevExports = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevExports) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports on update so we can compare the boundary\n                // signatures.\n                module.hot.dispose(function (data) {\n                    data.prevExports = currentExports;\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevExports !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevExports, currentExports)) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevExports !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./utils/exportExcel.js\n"));

/***/ })

});