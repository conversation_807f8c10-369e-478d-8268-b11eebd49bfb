"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/products",{

/***/ "./pages/products.js":
/*!***************************!*\
  !*** ./pages/products.js ***!
  \***************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __N_SSP: function() { return /* binding */ __N_SSP; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _components_Layout__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/Layout */ \"./components/Layout.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _fortawesome_react_fontawesome__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @fortawesome/react-fontawesome */ \"./node_modules/@fortawesome/react-fontawesome/index.es.js\");\n/* harmony import */ var _fortawesome_free_solid_svg_icons__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! @fortawesome/free-solid-svg-icons */ \"./node_modules/@fortawesome/free-solid-svg-icons/index.mjs\");\n/* harmony import */ var ag_grid_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ag-grid-react */ \"./node_modules/ag-grid-react/lib/main.js\");\n/* harmony import */ var ag_grid_react__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(ag_grid_react__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var ag_grid_community_styles_ag_grid_css__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ag-grid-community/styles//ag-grid.css */ \"./node_modules/ag-grid-community/styles/ag-grid.css\");\n/* harmony import */ var ag_grid_community_styles_ag_grid_css__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(ag_grid_community_styles_ag_grid_css__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var ag_grid_community_styles_ag_theme_alpine_css__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ag-grid-community/styles//ag-theme-alpine.css */ \"./node_modules/ag-grid-community/styles/ag-theme-alpine.css\");\n/* harmony import */ var ag_grid_community_styles_ag_theme_alpine_css__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(ag_grid_community_styles_ag_theme_alpine_css__WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_7__);\n/* harmony import */ var react_toastify__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! react-toastify */ \"./node_modules/react-toastify/dist/react-toastify.esm.mjs\");\n/* harmony import */ var react_toastify_dist_ReactToastify_css__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! react-toastify/dist/ReactToastify.css */ \"./node_modules/react-toastify/dist/ReactToastify.css\");\n/* harmony import */ var react_toastify_dist_ReactToastify_css__WEBPACK_IMPORTED_MODULE_9___default = /*#__PURE__*/__webpack_require__.n(react_toastify_dist_ReactToastify_css__WEBPACK_IMPORTED_MODULE_9__);\n/* harmony import */ var _utils_loaders_loadingContext__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/utils/loaders/loadingContext */ \"./utils/loaders/loadingContext.js\");\n/* harmony import */ var react_select__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! react-select */ \"./node_modules/react-select/dist/react-select.esm.js\");\n/* harmony import */ var _utils_rolePermissionsContext__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/utils/rolePermissionsContext */ \"./utils/rolePermissionsContext.js\");\n/* harmony import */ var _utils_renderer_productStatusRenderer__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/utils/renderer/productStatusRenderer */ \"./utils/renderer/productStatusRenderer.js\");\n/* harmony import */ var _utils_renderer_productActionRenderer__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/utils/renderer/productActionRenderer */ \"./utils/renderer/productActionRenderer.js\");\n/* harmony import */ var _services_apiConfig__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/services/apiConfig */ \"./services/apiConfig.js\");\n/* harmony import */ var js_cookie__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! js-cookie */ \"./node_modules/js-cookie/dist/js.cookie.mjs\");\n/* harmony import */ var _azure_msal_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @azure/msal-react */ \"./node_modules/@azure/msal-react/dist/index.js\");\n/* harmony import */ var _components_ProductDialog__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! @/components/ProductDialog */ \"./components/ProductDialog.js\");\n/* harmony import */ var _utils_secureStorage__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! @/utils/secureStorage */ \"./utils/secureStorage.js\");\n/* harmony import */ var _utils_extractCompanyFromEmail__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! @/utils/extractCompanyFromEmail */ \"./utils/extractCompanyFromEmail.js\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst customSelectStyles = {\n    // Default style\n    control: (base)=>({\n            ...base,\n            height: \"28px\",\n            minHeight: \"28px\"\n        }),\n    // Style when the condition is true\n    option: (base, param)=>{\n        let { data } = param;\n        return {\n            ...base,\n            color: data.is_new == true ? \"red\" : \"\"\n        };\n    },\n    valueContainer: (provided, state)=>({\n            ...provided,\n            height: \"26px\",\n            width: \"300px\",\n            padding: \"0 6px\"\n        }),\n    input: (provided, state)=>({\n            ...provided,\n            margin: \"0px\"\n        }),\n    indicatorSeparator: (state)=>({\n            display: \"none\"\n        }),\n    indicatorsContainer: (provided, state)=>({\n            ...provided,\n            height: \"28px\"\n        })\n};\nconst Products = (param)=>{\n    let { userData, PreviousPage, pageTypeId } = param;\n    _s();\n    console.log(\"userData, PreviousPage, pageTypeId\", userData, PreviousPage, pageTypeId);\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_7__.useRouter)();\n    const [pageSize, setPageSize] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(15);\n    const gridRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)();\n    const [isRMChecked, setIsRMChecked] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [isFGChecked, setIsFGChecked] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [isNVChecked, setIsNVChecked] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const { setIsLoading } = (0,_utils_loaders_loadingContext__WEBPACK_IMPORTED_MODULE_10__.useLoading)();\n    const [rowData, setRowData] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [company, setCompany] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"\");\n    const [blockScreen, setBlockScreen] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [typeId, setTypeId] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(pageTypeId);\n    const [isDialogOpen, setIsDialogOpen] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [isIssUser, setIsIssUser] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [isIssProcurementTeamUser, setIsIssProcurementTeamUser] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [isIssAdmin, setisIssAdmin] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [nvStatus, setNVStatus] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [statusOptions] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([\n        {\n            value: \"Draft\",\n            label: \"Draft\"\n        },\n        {\n            value: \"Pending Review\",\n            label: \"Pending Review\"\n        },\n        {\n            value: \"Rejected\",\n            label: \"Rejected\"\n        },\n        {\n            value: \"ISS to Setup\",\n            label: \"ISS to Setup\"\n        },\n        {\n            value: \"Setup Completed\",\n            label: \"Setup Completed\"\n        },\n        {\n            value: \"Cancelled\",\n            label: \"Cancelled\"\n        }\n    ]);\n    const [filteredRowData, setFilteredRowData] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [checkedValue, setCheckedValue] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [incompleteToast, setIncompleteToast] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [searchInput, setSearchInput] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"\");\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        // const userCompany = Cookies.get(\"company\");\n        const userCompany = userData === null || userData === void 0 ? void 0 : userData.company;\n        if (userCompany === \"issproduce\") {\n            setIsIssUser(true);\n        }\n        console.log(userData);\n        if (userData.department_id === 2) {\n            setIsIssProcurementTeamUser(true);\n            console.log(\"checking if it is an admin\", userData.role);\n            if (userData.role_id === 1) {\n                console.log(\"is admin\");\n                setisIssAdmin(true);\n            }\n        }\n        console.log(\"isIssProcurementTeamUser\", isIssProcurementTeamUser);\n    }, []);\n    const openDialog = ()=>{\n        setIsDialogOpen(true);\n    };\n    const closeDialog = ()=>{\n        setIsDialogOpen(false);\n    };\n    // const [isOpenOption, setIsOpenOption] = useState(false);\n    const [selectedRequestType, setSelectedRequestType] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(PreviousPage);\n    const handleTypeIdChange = (e)=>{\n        setSearchInput(\"\");\n        const filterTextBox = document.getElementById(\"filter-text-box\");\n        filterTextBox.value = \"\";\n        gridRef.current.api.setQuickFilter(\"\");\n        setNVStatus([]);\n        setTypeId(parseInt(e.target.value));\n        if (e.target.value === \"1\") {\n            setSelectedRequestType(\"rawMaterialRequest\");\n        } else if (e.target.value === \"3\") {\n            setSelectedRequestType(\"newVarietyRequest\");\n        } else if (e.target.value === \"4\") {\n            setSelectedRequestType(\"packagingform\");\n        }\n    };\n    // const closeOptionModal = () => {\n    //   setIsOpenOption(false);\n    // };\n    const handleRequestType = (type)=>{\n        setSelectedRequestType(type);\n    };\n    const handleFormType = ()=>{\n        if (selectedRequestType) {\n            localStorage.setItem(\"formType\", selectedRequestType);\n            console.log(selectedRequestType);\n            if (selectedRequestType === \"rawMaterialRequest\") {\n                router.push({\n                    pathname: \"/raw-material-request/add\"\n                });\n            // } else if (selectedRequestType == \"finishedProductRequest\") {\n            //   router.push({\n            //     pathname: `/finished-product-request/add` });\n            } else if (selectedRequestType == \"newVarietyRequest\") {\n                router.push({\n                    pathname: \"/variety/add\"\n                });\n            } else if (selectedRequestType == \"packagingform\") {\n                // router.push({ pathname: `/packaging/add` });\n                router.push({\n                    pathname: \"/packaging-form/add\"\n                }); //TODO\n            }\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        if (incompleteToast) {\n            react_toastify__WEBPACK_IMPORTED_MODULE_8__.toast.error(\"Kindly Submit the Request to Export it.\", {\n                position: \"top-right\",\n                autoClose: 1000,\n                hideProgressBar: false,\n                closeOnClick: true,\n                pauseOnHover: false,\n                draggable: true,\n                progress: undefined,\n                theme: \"light\"\n            });\n        }\n    }, [\n        incompleteToast\n    ]);\n    const [productsExtractData, setProductsExtractData] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([\n        [\n            \"Product Extract Data\"\n        ]\n    ]);\n    const [altfilExtractData, setAltfilExtractData] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([\n        [\n            \"Altfil Extract Data\"\n        ]\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        if (productsExtractData.length > 0) {\n            setCheckedValue(true);\n        } else {\n            setCheckedValue(false);\n        }\n    }, [\n        productsExtractData\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        if (typeof document !== \"undefined\") {\n            document.title = \"Products\";\n        }\n        setIsLoading(false);\n        getData().then((data)=>{\n            console.log(data);\n            const formattedData = data === null || data === void 0 ? void 0 : data.map((row)=>({\n                    id: row.id,\n                    code: row.code,\n                    action_id: row.action_id,\n                    request_no: row.request_no,\n                    type: row.product_type,\n                    product_type_name: row.product_type_label,\n                    product_type_id: row.product_type_id,\n                    reason: (row === null || row === void 0 ? void 0 : row.reason_description) ? row === null || row === void 0 ? void 0 : row.reason_description : \"Not Entered\",\n                    delivery_date: row.delivery_date ? new Date(row.delivery_date).toISOString().split(\"T\")[0] : \"Not Entered\",\n                    // delivery_date: new Date(row.delivery_date),\n                    product_code: row.master_product_code ? row.master_product_code : \"Not Entered\",\n                    product_description: row.product_description ? row.product_description : \"Not Entered\",\n                    originator: row.originator ? row.originator : row.originator_name,\n                    originator_email: row.originator_email,\n                    coo: row.coo ? row.coo : \"Not Entered\",\n                    status: row.status_label,\n                    master_product_code: row === null || row === void 0 ? void 0 : row.master_product_code,\n                    count_or_size: row === null || row === void 0 ? void 0 : row.count_or_size,\n                    units_in_outer: row === null || row === void 0 ? void 0 : row.units_in_outer,\n                    cases_per_pallet: row === null || row === void 0 ? void 0 : row.cases_per_pallet,\n                    outer_net_weight: row === null || row === void 0 ? void 0 : row.outer_net_weight,\n                    outer_gross_weight: row === null || row === void 0 ? void 0 : row.outer_gross_weight,\n                    sub_product_code: row === null || row === void 0 ? void 0 : row.sub_product_code,\n                    temperature_grade: row === null || row === void 0 ? void 0 : row.temperature_grade,\n                    intrastat_commodity_code: row === null || row === void 0 ? void 0 : row.intrastat_commodity_code,\n                    temperature_grade_name: row === null || row === void 0 ? void 0 : row.temperature_grade_name,\n                    intrastat_commodity_code_name: row === null || row === void 0 ? void 0 : row.intrastat_commodity_code_name,\n                    mark_variety_name: row === null || row === void 0 ? void 0 : row.mark_variety_name,\n                    intrastat_commodity_code_id: row === null || row === void 0 ? void 0 : row.intrastat_commodity_code_id,\n                    sort_group_id: row === null || row === void 0 ? void 0 : row.group_id,\n                    company: row === null || row === void 0 ? void 0 : row.company_name,\n                    temperature_grade_id: row === null || row === void 0 ? void 0 : row.temperature_grade_id,\n                    userText4: row === null || row === void 0 ? void 0 : row.userText4,\n                    userText5: row === null || row === void 0 ? void 0 : row.userText5,\n                    userText6: row === null || row === void 0 ? void 0 : row.userText6\n                }));\n            setCompany(formattedData[0].company);\n            setRowData(formattedData);\n            setFilteredRowData(formattedData);\n        }).catch((error)=>{\n            return error;\n        });\n    }, [\n        typeId\n    ]);\n    function getData() {\n        setRowData([]);\n        setFilteredRowData([]);\n        let serverAddress = _services_apiConfig__WEBPACK_IMPORTED_MODULE_14__.apiConfig.serverAddress;\n        const company_name = (userData === null || userData === void 0 ? void 0 : userData.company) || (0,_utils_extractCompanyFromEmail__WEBPACK_IMPORTED_MODULE_19__.extractCompanyFromEmail)(userData === null || userData === void 0 ? void 0 : userData.email);\n        const AdCompany = (userData === null || userData === void 0 ? void 0 : userData.companyName) || (userData === null || userData === void 0 ? void 0 : userData.ADCompanyName);\n        return fetch(\"\".concat(serverAddress, \"products/get-products/\").concat(company_name == \"dpsltd\" && AdCompany == \"DPS MS\" ? \"DPS MS\" : company_name, \"\\n      /\").concat(typeId), {\n            method: \"GET\",\n            headers: {\n                \"Content-Type\": \"application/json\"\n            },\n            credentials: \"include\"\n        }).then(async (res)=>{\n            if (res.status == 502) {\n                setBlockScreen(true);\n                return;\n            }\n            setBlockScreen(false);\n            if (res.status === 200) {\n                return res.json();\n            }\n            if (res.status === 400) {\n                react_toastify__WEBPACK_IMPORTED_MODULE_8__.toast.error(\"There was an error with your request. Please check your data and try again.\");\n            } else if (res.status === 401) {\n                react_toastify__WEBPACK_IMPORTED_MODULE_8__.toast.error(\"Your session has expired. Please log in again.\");\n                setTimeout(async ()=>{\n                    await (0,_utils_secureStorage__WEBPACK_IMPORTED_MODULE_18__.logout)();\n                    router.push(\"/login\");\n                }, 3000);\n            }\n            throw new Error(\"Failed to fetch data\");\n        }).catch((error)=>{\n            console.error(error);\n        });\n    }\n    const handlePageSizeChange = (event)=>{\n        const newPageSize = parseInt(event.target.value, 15);\n        setPageSize(newPageSize);\n        gridRef.current.api.paginationSetPageSize(newPageSize);\n    };\n    const handleStatusChange = (selectedOptions)=>{\n        setNVStatus(selectedOptions);\n        filterData(selectedOptions);\n    };\n    const filterData = (statuses)=>{\n        if (statuses.length == 0) {\n            setFilteredRowData(rowData);\n            return;\n        }\n        const filteredData = rowData.filter((row)=>statuses.some((status)=>status.value === row.status));\n        setFilteredRowData(filteredData.length > 0 ? filteredData : []);\n    };\n    // const clearFilters = () => {\n    //   setSelectedStatuses([]);\n    //   setFilteredRowData(rowData);\n    // };\n    const defaultColDef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useMemo)(()=>({\n            sortable: true,\n            filter: false,\n            resizable: true,\n            flex: 1,\n            suppressMenu: false\n        }));\n    const gridOptions = {\n        responsive: true\n    };\n    const columnDefs = [\n        {\n            headerName: \"Request No.\",\n            field: \"request_no\",\n            // cellRenderer: nameRenderer,\n            suppressMenu: true,\n            suppressSizeToFit: true,\n            suppressSizeToFit: false,\n            cellClass: \"ag-grid-checkbox-cell\",\n            flex: \"2%\",\n            filter: true\n        },\n        {\n            headerName: \"Reason\",\n            field: \"reason\",\n            flex: \"2%\",\n            cellRenderer: _utils_renderer_productStatusRenderer__WEBPACK_IMPORTED_MODULE_12__[\"default\"],\n            hide: typeId == 1 || 4 ? false : 0,\n            cellStyle: (params)=>{\n                if (params.value == \"Not Entered\") {\n                    return {\n                        color: \"#B31312\"\n                    };\n                }\n                return null;\n            }\n        },\n        {\n            headerName: \"Delivery Date\",\n            field: \"delivery_date\",\n            flex: \"2%\",\n            hide: typeId == 1 || 4 ? false : 0,\n            cellRenderer: (params)=>{\n                if (params.value === \"Not Entered\") {\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-[#B31312]\",\n                        children: \"Not Entered\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\products.js\",\n                        lineNumber: 384,\n                        columnNumber: 18\n                    }, undefined);\n                } else {\n                    // params.value\n                    // Format the date from yyyy-mm-dd to dd/mm/yyyy\n                    const dateParts = params.value.split(\"-\");\n                    const formattedDate = dateParts.reverse().join(\"/\");\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: formattedDate\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\products.js\",\n                        lineNumber: 391,\n                        columnNumber: 18\n                    }, undefined);\n                }\n            }\n        },\n        {\n            headerName: \"Product Code\",\n            field: \"product_code\",\n            flex: \"2%\",\n            // cellClass: (params) => {\n            //   return params.value === \"Not Entered\" ? 'not-entered' : '';\n            // }\n            cellStyle: (params)=>{\n                if (params.value == \"Not Entered\") {\n                    return {\n                        color: \"#B31312\"\n                    };\n                }\n                return null;\n            }\n        },\n        {\n            // headerName: \"Product description\",\n            // field: \"product_description\",\n            headerName: \"Product Description\",\n            field: \"product_description\",\n            // cellRenderer: statusRenderer,\n            flex: \"3%\",\n            cellStyle: (params)=>{\n                if (params.value == \"Not Entered\") {\n                    return {\n                        color: \"#B31312\"\n                    };\n                }\n                return null;\n            }\n        },\n        {\n            headerName: \"Originator\",\n            field: \"originator\",\n            headerName: \"Originator\",\n            field: \"originator\",\n            // cellRenderer: statusRenderer,\n            flex: \"2%\",\n            flex: \"2%\"\n        },\n        // {\n        //   headerName: \"COO\",\n        //   field: \"coo\",\n        //   headerName: \"COO\",\n        //   field: \"coo\",\n        //   // cellRenderer: statusRenderer,\n        //   flex: \"2%\",\n        //   cellStyle: (params) => {\n        //     if (params.value == \"Not Entered\") {\n        //       return { color: \"#B31312\" };\n        //     }\n        //     return null;\n        //   },\n        // },\n        {\n            headerName: \"Status\",\n            field: \"status\",\n            cellRenderer: _utils_renderer_productStatusRenderer__WEBPACK_IMPORTED_MODULE_12__[\"default\"],\n            cellStyle: ()=>({\n                    justifyContent: \"center\"\n                }),\n            flex: \"2%\",\n            hide: false\n        },\n        {\n            // field: \"Action(s)\",\n            field: typeId == 3 ? \"Action(s)\" : \"Action(s)\",\n            cellRenderer: (params)=>(0,_utils_renderer_productActionRenderer__WEBPACK_IMPORTED_MODULE_13__[\"default\"])(params, userData, userData.token, company, typeId, setIsLoading, isIssUser),\n            flex: \"2%\",\n            cellStyle: ()=>({}),\n            cellStyle: {\n                justifyContent: \"end\",\n                paddingRight: \"10px\"\n            },\n            sortable: false\n        },\n        {\n            field: \"role_num\",\n            hide: true\n        }\n    ];\n    const handleGridReady = (params)=>{\n        params.api.setColumnDefs(columnDefs);\n    };\n    const onFilterTextBoxChanged = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(()=>{\n        gridRef.current.api.setQuickFilter(document.getElementById(\"filter-text-box\").value);\n        setSearchInput(document.getElementById(\"filter-text-box\").value);\n    }, []);\n    const handleFilterProductType = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)((e)=>{\n        if (e.target.value == \"RM\") {\n            if (e.target.checked) {\n                setIsRMChecked(true);\n                setIsFGChecked(false);\n                setIsNVChecked(false);\n                gridRef.current.api.setQuickFilter(\"RM\");\n            } else {\n                setIsRMChecked(false);\n                gridRef.current.api.setQuickFilter(\"\");\n            }\n        } else if (e.target.value == \"FG\") {\n            if (e.target.checked) {\n                setIsFGChecked(true);\n                setIsRMChecked(false);\n                setIsNVChecked(false);\n                gridRef.current.api.setQuickFilter(\"FG\");\n            } else {\n                setIsFGChecked(false);\n                gridRef.current.api.setQuickFilter(\"\");\n            }\n        } else if (e.target.value == \"NV\") {\n            if (e.target.checked) {\n                setIsNVChecked(true);\n                setIsFGChecked(false);\n                setIsRMChecked(false);\n                gridRef.current.api.setQuickFilter(\"NV\");\n            } else {\n                setIsNVChecked(true);\n                gridRef.current.api.setQuickFilter(\"\");\n            }\n        } else {\n            setIsRMChecked(false);\n            setIsFGChecked(false);\n            setIsNVChecked(false);\n            gridRef.current.api.setQuickFilter(\"\");\n        }\n    }, []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_toastify__WEBPACK_IMPORTED_MODULE_8__.ToastContainer, {\n                limit: 1\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\products.js\",\n                lineNumber: 531,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Layout__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                userData: userData,\n                blockScreen: blockScreen,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mr-20 md:mr-12 lg:mr-14\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-row md:flex-col lg:flex-row justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"border border-light-gray rounded-md px-3 py-1\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"flex items-center gap-2 cursor-pointer\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                type: \"radio\",\n                                                                name: \"requestType\",\n                                                                value: \"1\",\n                                                                className: \"form-radio w-5 h-5 cursor-pointer\",\n                                                                checked: typeId === 1,\n                                                                onChange: handleTypeIdChange\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\products.js\",\n                                                                lineNumber: 539,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"labels\",\n                                                                children: \"Raw Materials\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\products.js\",\n                                                                lineNumber: 547,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\products.js\",\n                                                        lineNumber: 538,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\products.js\",\n                                                    lineNumber: 537,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"border border-light-gray rounded-md px-3 py-1\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"flex items-center gap-2 cursor-pointer\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                type: \"radio\",\n                                                                name: \"requestType\",\n                                                                value: \"3\",\n                                                                className: \"form-radio w-5 h-5 cursor-pointer\",\n                                                                checked: typeId === 3,\n                                                                onChange: handleTypeIdChange\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\products.js\",\n                                                                lineNumber: 563,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"labels\",\n                                                                children: \"New Variety\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\products.js\",\n                                                                lineNumber: 571,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\products.js\",\n                                                        lineNumber: 562,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\products.js\",\n                                                    lineNumber: 561,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                isIssUser && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"border border-light-gray rounded-md px-3 py-1\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"flex items-center gap-2 cursor-pointer\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                type: \"radio\",\n                                                                name: \"requestType\",\n                                                                value: \"4\",\n                                                                className: \"form-radio w-5 h-5 cursor-pointer\",\n                                                                checked: typeId === 4,\n                                                                onChange: handleTypeIdChange\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\products.js\",\n                                                                lineNumber: 577,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"labels\",\n                                                                children: \"Packaging\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\products.js\",\n                                                                lineNumber: 585,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\products.js\",\n                                                        lineNumber: 576,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\products.js\",\n                                                    lineNumber: 575,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\products.js\",\n                                            lineNumber: 536,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"labels\",\n                                            style: {\n                                                marginLeft: \"10px\"\n                                            },\n                                            children: typeId === 3 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_select__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                isMulti: true,\n                                                options: statusOptions,\n                                                onChange: handleStatusChange,\n                                                placeholder: \"Select Status...\",\n                                                className: \"basic-multi-select\",\n                                                classNamePrefix: \"select\",\n                                                value: nvStatus,\n                                                isSearchable: false,\n                                                styles: customSelectStyles\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\products.js\",\n                                                lineNumber: 592,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\products.js\",\n                                            lineNumber: 590,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center px-3 w-auto md:w-fit lg:w-auto md:py-2 lg:py-1 md:mb-3 lg:mb-0\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\products.js\",\n                                            lineNumber: 608,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\products.js\",\n                                    lineNumber: 535,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"relative block w-[47vh] text-gray-400 mt-0 pt-0\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"absolute px-4 py-1 pt-[0.4rem] 2xl:pt-1.5 text-black\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fortawesome_react_fontawesome__WEBPACK_IMPORTED_MODULE_3__.FontAwesomeIcon, {\n                                                        icon: _fortawesome_free_solid_svg_icons__WEBPACK_IMPORTED_MODULE_21__.faSearch,\n                                                        className: \"fw-bold\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\products.js\",\n                                                        lineNumber: 639,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\products.js\",\n                                                    lineNumber: 638,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"text\",\n                                                    id: \"filter-text-box\",\n                                                    placeholder: \"Search...\",\n                                                    onInput: onFilterTextBoxChanged,\n                                                    value: searchInput,\n                                                    className: \"block w-full px-4 pl-10 text-gray-500 placeholder-gray-400 searchbar border rounded-lg appearance-none form-input focus:outline-none\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\products.js\",\n                                                    lineNumber: 641,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\products.js\",\n                                            lineNumber: 637,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ProductDialog__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                            isOpen: openDialog,\n                                            onClose: closeDialog,\n                                            handleFormType: handleFormType,\n                                            selectedRequestType: selectedRequestType,\n                                            isIssUser: isIssUser,\n                                            isIssProcurmentUser: isIssProcurementTeamUser,\n                                            handleRequestType: handleRequestType,\n                                            admin: isIssAdmin\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\products.js\",\n                                            lineNumber: 660,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\products.js\",\n                                    lineNumber: 636,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\products.js\",\n                            lineNumber: 534,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"my-5\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative ag-theme-alpine !rounded-md\",\n                                style: {\n                                    height: \"calc(100vh - 151px)\"\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ag_grid_react__WEBPACK_IMPORTED_MODULE_4__.AgGridReact, {\n                                        rowData: filteredRowData,\n                                        ref: gridRef,\n                                        columnDefs: columnDefs,\n                                        defaultColDef: defaultColDef,\n                                        suppressRowClickSelection: true,\n                                        pagination: true,\n                                        paginationPageSize: pageSize,\n                                        onPageSizeChanged: handlePageSizeChange,\n                                        tooltipShowDelay: 0,\n                                        tooltipHideDelay: 1000,\n                                        onGridReady: handleGridReady,\n                                        gridOptions: gridOptions\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\products.js\",\n                                        lineNumber: 677,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-start mt-2 pagination-style\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            htmlFor: \"page-size-select pagination\",\n                                            className: \"inputs\",\n                                            children: [\n                                                \"Show\",\n                                                \" \",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                    id: \"page-size-select\",\n                                                    onChange: handlePageSizeChange,\n                                                    value: pageSize,\n                                                    className: \"focus:outline-none\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: 10,\n                                                            children: \"10\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\products.js\",\n                                                            lineNumber: 700,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: 15,\n                                                            children: \"15\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\products.js\",\n                                                            lineNumber: 701,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: 25,\n                                                            children: \"25\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\products.js\",\n                                                            lineNumber: 702,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: 50,\n                                                            children: \"50\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\products.js\",\n                                                            lineNumber: 703,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: 100,\n                                                            children: \"100\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\products.js\",\n                                                            lineNumber: 704,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\products.js\",\n                                                    lineNumber: 694,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                \" \",\n                                                \"Entries\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\products.js\",\n                                            lineNumber: 692,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\products.js\",\n                                        lineNumber: 691,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\products.js\",\n                                lineNumber: 673,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\products.js\",\n                            lineNumber: 672,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\products.js\",\n                    lineNumber: 533,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\products.js\",\n                lineNumber: 532,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true);\n};\n_s(Products, \"x02n8KTM2A42sUdrK7Zft5e7jZs=\", false, function() {\n    return [\n        next_router__WEBPACK_IMPORTED_MODULE_7__.useRouter,\n        _utils_loaders_loadingContext__WEBPACK_IMPORTED_MODULE_10__.useLoading\n    ];\n});\n_c = Products;\nvar __N_SSP = true;\n/* harmony default export */ __webpack_exports__[\"default\"] = (Products);\nvar _c;\n$RefreshReg$(_c, \"Products\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevExports = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevExports) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports on update so we can compare the boundary\n                // signatures.\n                module.hot.dispose(function (data) {\n                    data.prevExports = currentExports;\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevExports !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevExports, currentExports)) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevExports !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./pages/products.js\n"));

/***/ })

});