"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/service_level",{

/***/ "./components/service_level/SLTable.js":
/*!*********************************************!*\
  !*** ./components/service_level/SLTable.js ***!
  \*********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _ViewDetails__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./ViewDetails */ \"./components/service_level/ViewDetails.jsx\");\n/* harmony import */ var _services_apiConfig__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/services/apiConfig */ \"./services/apiConfig.js\");\n/* harmony import */ var _ReasonsDetails__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./ReasonsDetails */ \"./components/service_level/ReasonsDetails.jsx\");\n/* harmony import */ var _utils_getCookieData__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/utils/getCookieData */ \"./utils/getCookieData.js\");\n/* harmony import */ var _common_NoDataFound__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../common/NoDataFound */ \"./components/common/NoDataFound.jsx\");\n/* harmony import */ var _fluentui_react_components__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @fluentui/react-components */ \"./node_modules/@fluentui/react-components/lib/index.js\");\n/* harmony import */ var react_toastify__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! react-toastify */ \"./node_modules/react-toastify/dist/react-toastify.esm.mjs\");\n/* harmony import */ var js_cookie__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! js-cookie */ \"./node_modules/js-cookie/dist/js.cookie.mjs\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n//\n\nconst SLTable = (param)=>{\n    let { gridcolumnRefs, gridcolumnWidths, setIsTableRendered, checkedStates, customerSLData, userData, selectedProducts, seeAll, recordCount, setRecordsCount, searchBoxContent, slFilters, selectedMasterProductCode, selectedCustomer, toggle, selectedRows, setSelectedRows, isBulkUpdate, setIsBulkUpdate, bulkUpdateData, isOpen, setIsOpen, setBulkUpdateData, masterProducts, bulkDeleteOrdIds, setBulkDeleteOrdIds, setNoDataExists, setShowLoadingMessage, setAllReasonsSubreasons, allReasonsSubreasons, selectedReasons, setSelectedReasons, selectedSubReasons, setSelectedSubReasons, showLoadingMessage } = param;\n    var _checkedStates_columns, _checkedStates_columns1, _checkedStates_columns2, _checkedStates_columns3, _checkedStates_columns4, _checkedStates_columns5, _checkedStates_columns6, _columnTotals_totalValue;\n    _s();\n    let ADCompanyName = js_cookie__WEBPACK_IMPORTED_MODULE_8__[\"default\"].get(\"ADCompanyName\");\n    const [reasonsMasterList, setReasonsMasterList] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [parentReasonList, setParentReasonList] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [reasonsData, setReasonsData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [allSelectedProducts, setAllSelectedProducts] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isHeaderChecked, setIsHeaderChecked] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [columnTotals, setColumnTotals] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const setMapForReasonsParentsAndTheirCorrespondingChildren = ()=>{\n        setParentReasonList((prevList)=>{\n            const parentReasonIds = reasonsMasterList.filter((typeOfReason)=>typeOfReason.parent_id === null).map((typeOfReason)=>typeOfReason);\n            const uniqueParentIds = new Set([\n                ...prevList,\n                ...parentReasonIds\n            ]);\n            return Array.from(uniqueParentIds); // Update the state\n        });\n    };\n    const handleCheckboxChange = (data)=>{\n        setSelectedRows((prevSelected)=>{\n            let updatedSelected;\n            if (prevSelected.includes(data)) {\n                updatedSelected = prevSelected.filter((item)=>item !== data);\n            } else {\n                updatedSelected = [\n                    ...prevSelected,\n                    data\n                ];\n            }\n            const ordIds = updatedSelected.filter((product)=>product.CASES_ADDED_REASONS > 0).map((product)=>product.ORD_ID);\n            setBulkDeleteOrdIds(ordIds.length > 0 ? ordIds : []);\n            return updatedSelected;\n        });\n    };\n    const handleHeaderCheckboxChange = (data)=>{\n        const selectableRows = data.filter((product)=>(product.LOCKED_BY === null || product.LOCKED_BY === \"\") && product.CASES_DIFFERENCE != 0);\n        const ordIds = selectableRows.filter((product)=>product.CASES_ADDED_REASONS > 0).map((product)=>product.ORD_ID);\n        if (ordIds.length > 0) {\n            setBulkDeleteOrdIds(ordIds);\n        } else {\n            setBulkDeleteOrdIds([]);\n        }\n        if (selectedRows.length === selectableRows.length) {\n            // If all rows are selected, deselect all\n            setSelectedRows([]);\n            setIsHeaderChecked(false);\n        } else {\n            // If not all rows are selected, select all\n            setSelectedRows(selectableRows);\n            setIsHeaderChecked(true);\n        }\n    };\n    //#region getReasons\n    const fetchReasonData = async (orderId, customerName)=>{\n        setReasonsData([]);\n        const serverAddress = _services_apiConfig__WEBPACK_IMPORTED_MODULE_3__.apiConfig.serverAddress;\n        const user = (0,_utils_getCookieData__WEBPACK_IMPORTED_MODULE_5__.getCookieData)(\"user\");\n        try {\n            const serviceLevelReasons = await fetch(\"\".concat(serverAddress, \"serviceLevel/get-service-level-reasons/\").concat(orderId, \"?customerName=\").concat(customerName), {\n                method: \"GET\",\n                headers: {\n                    Accept: \"application/json\",\n                    \"Content-Type\": \"application/json\",\n                    Authorization: \"Bearer \".concat(user.token)\n                }\n            }).then((res)=>{\n                if (res.status === 400) {\n                    react_toastify__WEBPACK_IMPORTED_MODULE_7__.toast.error(\"There was an error with your request. Please check your data and try again.\");\n                    return null;\n                } else if (res.status === 401) {\n                    react_toastify__WEBPACK_IMPORTED_MODULE_7__.toast.error(\"Your session has expired. Please log in again.\");\n                    setTimeout(()=>{\n                        localStorage.removeItem(\"superUser\");\n                        js_cookie__WEBPACK_IMPORTED_MODULE_8__[\"default\"].remove(\"company\");\n                        js_cookie__WEBPACK_IMPORTED_MODULE_8__[\"default\"].remove(\"ADCompanyName\");\n                        localStorage.removeItem(\"id\");\n                        localStorage.removeItem(\"name\");\n                        localStorage.removeItem(\"role\");\n                        localStorage.removeItem(\"email\");\n                        js_cookie__WEBPACK_IMPORTED_MODULE_8__[\"default\"].remove(\"user\");\n                        js_cookie__WEBPACK_IMPORTED_MODULE_8__[\"default\"].remove(\"theme\");\n                        js_cookie__WEBPACK_IMPORTED_MODULE_8__[\"default\"].remove(\"token\");\n                        const redirectUrl = \"/login?redirect=\".concat(encodeURIComponent(window.location.pathname));\n                        logoutHandler(instance, redirectUrl);\n                    }, 3000);\n                    return null;\n                }\n                if (res.status === 200) {\n                    return res.json();\n                }\n            }) // Ensure you parse the JSON\n            .then((data)=>{\n                if (!isBulkUpdate) {\n                    setReasonsData(data);\n                }\n            });\n        } catch (error) {\n            console.log(\"error in fetching\", error);\n        }\n    };\n    //#endregion\n    //#region getMasterReasons\n    const fetchReasonMaster = async ()=>{\n        // console.log(\"service Level Reasons Master code start\");\n        try {\n            const serverAddress = _services_apiConfig__WEBPACK_IMPORTED_MODULE_3__.apiConfig.serverAddress;\n            const user = (0,_utils_getCookieData__WEBPACK_IMPORTED_MODULE_5__.getCookieData)(\"user\");\n            const serviceLevelReasonsMaster = await fetch(\"\".concat(serverAddress, \"serviceLevel/get-service-level-reasons-master\"), {\n                method: \"GET\",\n                headers: {\n                    Accept: \"application/json\",\n                    \"Content-Type\": \"application/json\",\n                    Authorization: \"Bearer \".concat(user.token)\n                }\n            }).then((res)=>{\n                if (res.status === 400) {\n                    react_toastify__WEBPACK_IMPORTED_MODULE_7__.toast.error(\"There was an error with your request. Please check your data and try again.\");\n                    return null;\n                } else if (res.status === 401) {\n                    react_toastify__WEBPACK_IMPORTED_MODULE_7__.toast.error(\"Your session has expired. Please log in again.\");\n                    setTimeout(()=>{\n                        localStorage.removeItem(\"superUser\");\n                        js_cookie__WEBPACK_IMPORTED_MODULE_8__[\"default\"].remove(\"company\");\n                        js_cookie__WEBPACK_IMPORTED_MODULE_8__[\"default\"].remove(\"ADCompanyName\");\n                        localStorage.removeItem(\"id\");\n                        localStorage.removeItem(\"name\");\n                        localStorage.removeItem(\"role\");\n                        localStorage.removeItem(\"email\");\n                        js_cookie__WEBPACK_IMPORTED_MODULE_8__[\"default\"].remove(\"user\");\n                        js_cookie__WEBPACK_IMPORTED_MODULE_8__[\"default\"].remove(\"theme\");\n                        js_cookie__WEBPACK_IMPORTED_MODULE_8__[\"default\"].remove(\"token\");\n                        const redirectUrl = \"/login?redirect=\".concat(encodeURIComponent(window.location.pathname));\n                        logoutHandler(instance, redirectUrl);\n                    }, 3000);\n                    return null;\n                }\n                if (res.status === 200) {\n                    return res.json();\n                }\n            }) // Ensure you parse the JSON\n            .then((data)=>{\n                setReasonsMasterList(data);\n            });\n        } catch (err) {\n            console.log(\"error in fetching\", err);\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        setIsTableRendered((prev)=>!prev);\n    }, [\n        setIsTableRendered\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        fetchReasonMaster(); // To fetch the master list of reasons\n        setMapForReasonsParentsAndTheirCorrespondingChildren();\n    }, []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const cookieSelectedProducts = (slFilters === null || slFilters === void 0 ? void 0 : slFilters.selectedProducts) || [];\n        const stateSelectedProducts = selectedProducts || [];\n        const combinedSelectedProducts = [\n            ...cookieSelectedProducts.map((product)=>({\n                    label: product.productDescription,\n                    value: product.altFillId\n                })),\n            ...stateSelectedProducts.map((product)=>({\n                    label: product.label,\n                    value: product.value\n                }))\n        ];\n        const customerSLArray = Object.values(customerSLData);\n        let filteredProducts = combinedSelectedProducts.length ? customerSLArray.filter((product)=>combinedSelectedProducts.some((selectedProduct)=>selectedProduct.label === product.PRODUCT_DESCRIPTION && (!searchBoxContent || !!searchBoxContent && product.ORD_NUMBER.toString().includes(searchBoxContent)))) : customerSLArray.filter((product)=>!searchBoxContent || !!searchBoxContent && product.ORD_NUMBER.toString().includes(searchBoxContent));\n        if (selectedMasterProductCode !== \"all\") {\n            filteredProducts = filteredProducts.filter((prod)=>prod.MASTER_PRODUCT_CODE == selectedMasterProductCode);\n        }\n        if (selectedReasons.length > 0) {\n            filteredProducts = filteredProducts.filter((product)=>product.reasons && product.reasons.some((reason)=>selectedReasons.includes(reason.MAIN_REASON_ID)));\n        } else if (selectedSubReasons.length > 0) {\n            filteredProducts = filteredProducts.filter((product)=>product.reasons && product.reasons.some((reason)=>selectedSubReasons.includes(reason.SUB_REASON_ID)));\n        }\n        if (filteredProducts.length > 0) {\n            setAllSelectedProducts(filteredProducts);\n            const visibleRows = filteredProducts.filter((data)=>{\n                if (!seeAll && data.CASES_DIFFERENCE - data.CASES_ADDED_REASONS === 0) {\n                    return false;\n                }\n                return true;\n            });\n            const totals = visibleRows.reduce((acc, data)=>{\n                acc.casesOrdered += data.CASES_ORIGINAL || 0;\n                acc.casesDelivered += data.CASES_DELIVERED || 0;\n                acc.casesDifference += data.CASES_DIFFERENCE || 0;\n                acc.addedReasons += data.CASES_ADDED_REASONS || 0;\n                acc.totalValue += (data.CASE_SIZE || 0) * (data.UNIT_PRICE || 0) * (data.CASES_DIFFERENCE || 0);\n                return acc;\n            }, {\n                casesOrdered: 0,\n                casesDelivered: 0,\n                casesDifference: 0,\n                addedReasons: 0,\n                totalValue: 0\n            });\n            setColumnTotals(totals);\n        }\n        var _filteredProducts_length;\n        setRecordsCount((_filteredProducts_length = filteredProducts === null || filteredProducts === void 0 ? void 0 : filteredProducts.length) !== null && _filteredProducts_length !== void 0 ? _filteredProducts_length : 0);\n    }, [\n        toggle,\n        customerSLData,\n        selectedProducts,\n        slFilters,\n        searchBoxContent,\n        selectedMasterProductCode,\n        selectedReasons,\n        selectedSubReasons\n    ]);\n    const [selectedData, setSelectedData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const handleViewDetailsClick = (data)=>{\n        setSelectedRows([\n            data\n        ]);\n        setIsOpen(true);\n    };\n    var _columnTotals_casesOrdered, _columnTotals_casesDelivered, _columnTotals_casesDifference, _columnTotals_addedReasons, _columnTotals_totalValue1;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"!fontFamily-poppinsregular\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_toastify__WEBPACK_IMPORTED_MODULE_7__.ToastContainer, {\n                limit: 1\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                lineNumber: 336,\n                columnNumber: 7\n            }, undefined),\n            recordCount != 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                className: \"service-level-grid relative table-fixed w-full text-sm !fontFamily-poppinsregular\",\n                cellSpacing: 0,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                    className: \"sticky top-0 z-10 !w-10\",\n                                    ref: gridcolumnRefs.checkboxRef,\n                                    style: {\n                                        left: \"\".concat(0, \"px\")\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-full flex justify-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"checkbox\",\n                                            className: \"w-5 h-5 text-blue border-theme-blue2 rounded accent-skin-primary text-center\",\n                                            disabled: selectedMasterProductCode == \"all\" && masterProducts.length > 2 || selectedCustomer == \"All Customers\",\n                                            onChange: ()=>handleHeaderCheckboxChange(allSelectedProducts),\n                                            checked: isHeaderChecked\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                            lineNumber: 351,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                        lineNumber: 350,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                    lineNumber: 345,\n                                    columnNumber: 15\n                                }, undefined),\n                                checkedStates.columns.depotdate && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                    className: \"sticky top-0 z-10\",\n                                    ref: gridcolumnRefs.depotdate,\n                                    style: {\n                                        left: \"\".concat(gridcolumnWidths.checkboxWidth, \"px\")\n                                    },\n                                    children: \"Depot Date\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                    lineNumber: 367,\n                                    columnNumber: 17\n                                }, undefined),\n                                ADCompanyName == \"Integrated Service Solutions Ltd\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                    className: \"sticky top-0 z-10 !text-center\",\n                                    ref: gridcolumnRefs.serviceCustomers,\n                                    style: {\n                                        left: \"\".concat(gridcolumnWidths.checkboxWidth, \"px\")\n                                    },\n                                    children: [\n                                        \"Service Customers\",\n                                        \" \"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                    lineNumber: 375,\n                                    columnNumber: 69\n                                }, undefined),\n                                checkedStates.columns.weekNo && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                    className: \"sticky top-0  z-10\",\n                                    ref: gridcolumnRefs.weekNo,\n                                    style: {\n                                        left: \"\".concat(ADCompanyName == \"Integrated Service Solutions Ltd\" ? gridcolumnWidths.serviceCustomerswidth : gridcolumnWidths.depotdate, \"px\")\n                                    },\n                                    children: \"Week No\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                    lineNumber: 384,\n                                    columnNumber: 17\n                                }, undefined),\n                                checkedStates.columns.altfill && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                    className: \"sticky top-0  z-10\",\n                                    ref: gridcolumnRefs.altfill,\n                                    style: {\n                                        left: \"\".concat(gridcolumnWidths.weekNo, \"px\")\n                                    },\n                                    children: \"Alt Fill\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                    lineNumber: 395,\n                                    columnNumber: 17\n                                }, undefined),\n                                checkedStates.columns.customer && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                    className: \"!w-28 sticky top-0  z-10 \",\n                                    ref: gridcolumnRefs.customer,\n                                    style: {\n                                        left: \"\".concat(gridcolumnWidths.altfill, \"px\")\n                                    },\n                                    children: \"Customer\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                    lineNumber: 404,\n                                    columnNumber: 17\n                                }, undefined),\n                                checkedStates.columns.salesorder && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                    className: \"sticky top-0  z-10 \",\n                                    ref: gridcolumnRefs.salesorder,\n                                    style: {\n                                        left: \"\".concat(gridcolumnWidths.customer, \"px\")\n                                    },\n                                    children: \"Sales Order\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                    lineNumber: 413,\n                                    columnNumber: 17\n                                }, undefined),\n                                checkedStates.columns.salesOrderId && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                    className: \"sticky top-0  z-10 \",\n                                    ref: gridcolumnRefs.salesOrderId,\n                                    style: {\n                                        left: \"\".concat(gridcolumnWidths.customer, \"px\")\n                                    },\n                                    children: \"Order Det Id\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                    lineNumber: 422,\n                                    columnNumber: 17\n                                }, undefined),\n                                checkedStates.columns.product && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                    className: \"!w-80 sticky top-0  z-10 \",\n                                    ref: gridcolumnRefs.product,\n                                    style: {\n                                        left: \"\".concat(gridcolumnWidths.salesorder, \"px\")\n                                    },\n                                    children: \"Product\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                    lineNumber: 431,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                    className: \"top-0  z-10 !text-center\",\n                                    ref: gridcolumnRefs.casesize,\n                                    style: {\n                                        left: \"\".concat(gridcolumnWidths.product, \"px\")\n                                    },\n                                    children: \"Case Size\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                    lineNumber: 439,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                    className: \"sticky top-0 !text-center\",\n                                    children: \"Cases Ordered\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                    lineNumber: 446,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                    className: \"sticky top-0 !text-center\",\n                                    children: \"Cases Delivered\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                    lineNumber: 447,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                    className: \"sticky top-0 !text-center\",\n                                    children: \"Cases Different\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                    lineNumber: 448,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                    className: \"sticky top-0 !text-center\",\n                                    children: \"Added Reasons\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                    lineNumber: 449,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                    className: \"!w-28 sticky top-0 !text-center\",\n                                    children: \"Order Fulfillment %\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                    lineNumber: 450,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                    className: \"sticky top-0 !text-center\",\n                                    children: \"Unit Price\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                    lineNumber: 453,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                    className: \"sticky top-0 !text-center\",\n                                    children: \"Case Value\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                    lineNumber: 454,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                    className: \"sticky top-0 !text-center\",\n                                    children: \"Total Value\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                    lineNumber: 455,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                    className: \"sticky top-0 !text-center\",\n                                    children: \"Status\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                    lineNumber: 456,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                    className: \"!w-60 relative\",\n                                    children: [\n                                        \"Reason\",\n                                        \" \",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ReasonsDetails__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                            reasonsData: allReasonsSubreasons.reasons,\n                                            selectedReasons: selectedReasons,\n                                            selectedSubReasons: selectedSubReasons,\n                                            setSelectedReasons: setSelectedReasons,\n                                            setSelectedSubReasons: setSelectedSubReasons,\n                                            type: \"reasonsList\",\n                                            seeAll: seeAll\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                            lineNumber: 459,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                    lineNumber: 457,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                    className: \"!w-60 relative\",\n                                    children: [\n                                        \"Sub Reason\",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ReasonsDetails__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                            reasonsData: allReasonsSubreasons.subReasons,\n                                            selectedReasons: selectedReasons,\n                                            selectedSubReasons: selectedSubReasons,\n                                            setSelectedReasons: setSelectedReasons,\n                                            setSelectedSubReasons: setSelectedSubReasons,\n                                            type: \"subReasonsList\",\n                                            seeAll: seeAll\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                            lineNumber: 471,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                    lineNumber: 469,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                    className: \"!w-52\",\n                                    children: \"Comments\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                    lineNumber: 481,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                    className: \"!text-center\",\n                                    children: \"Actions\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                    lineNumber: 482,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                            lineNumber: 344,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                        lineNumber: 343,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                className: \"font-bold bg-[#f3f8ff]\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                        className: \"text-center sticky top-0 font-bold text-sm\",\n                                        style: {\n                                            left: \"\".concat(0, \"px\")\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                        lineNumber: 488,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    (checkedStates === null || checkedStates === void 0 ? void 0 : (_checkedStates_columns = checkedStates.columns) === null || _checkedStates_columns === void 0 ? void 0 : _checkedStates_columns.depotdate) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                        className: \"text-center sticky top-0 font-bold text-sm\",\n                                        style: {\n                                            left: \"\".concat(gridcolumnWidths.checkboxWidth, \"px\")\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                        lineNumber: 493,\n                                        columnNumber: 19\n                                    }, undefined),\n                                    ADCompanyName == \"Integrated Service Solutions Ltd\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                        className: \"text-center sticky top-0 font-bold text-sm\",\n                                        style: {\n                                            left: \"\".concat(gridcolumnWidths.depotDate, \"px\")\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                        lineNumber: 498,\n                                        columnNumber: 71\n                                    }, undefined),\n                                    (checkedStates === null || checkedStates === void 0 ? void 0 : (_checkedStates_columns1 = checkedStates.columns) === null || _checkedStates_columns1 === void 0 ? void 0 : _checkedStates_columns1.weekNo) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                        className: \"sticky top-0 font-bold text-center text-sm\",\n                                        style: {\n                                            left: \"\".concat(gridcolumnWidths.serviceCustomerswidth, \"px\")\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                        lineNumber: 504,\n                                        columnNumber: 19\n                                    }, undefined),\n                                    (checkedStates === null || checkedStates === void 0 ? void 0 : (_checkedStates_columns2 = checkedStates.columns) === null || _checkedStates_columns2 === void 0 ? void 0 : _checkedStates_columns2.altfill) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                        className: \"sticky top-0 font-bold text-sm\",\n                                        style: {\n                                            left: \"\".concat(gridcolumnWidths.weekNo, \"px\")\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                        lineNumber: 510,\n                                        columnNumber: 19\n                                    }, undefined),\n                                    (checkedStates === null || checkedStates === void 0 ? void 0 : (_checkedStates_columns3 = checkedStates.columns) === null || _checkedStates_columns3 === void 0 ? void 0 : _checkedStates_columns3.customer) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                        className: \"sticky top-0 font-bold text-sm\",\n                                        style: {\n                                            left: \"\".concat(gridcolumnWidths.altfill, \"px\")\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                        lineNumber: 516,\n                                        columnNumber: 19\n                                    }, undefined),\n                                    (checkedStates === null || checkedStates === void 0 ? void 0 : (_checkedStates_columns4 = checkedStates.columns) === null || _checkedStates_columns4 === void 0 ? void 0 : _checkedStates_columns4.salesorder) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                        className: \"sticky top-0 font-bold text-sm\",\n                                        style: {\n                                            left: \"\".concat(gridcolumnWidths.customer, \"px\")\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                        lineNumber: 522,\n                                        columnNumber: 19\n                                    }, undefined),\n                                    (checkedStates === null || checkedStates === void 0 ? void 0 : (_checkedStates_columns5 = checkedStates.columns) === null || _checkedStates_columns5 === void 0 ? void 0 : _checkedStates_columns5.salesOrderId) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                        className: \"sticky top-0 font-bold text-sm\",\n                                        style: {\n                                            left: \"\".concat(gridcolumnWidths.customer, \"px\")\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                        lineNumber: 528,\n                                        columnNumber: 19\n                                    }, undefined),\n                                    (checkedStates === null || checkedStates === void 0 ? void 0 : (_checkedStates_columns6 = checkedStates.columns) === null || _checkedStates_columns6 === void 0 ? void 0 : _checkedStates_columns6.product) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                        className: \"sticky top-0 font-bold text-sm\",\n                                        style: {\n                                            left: \"\".concat(gridcolumnWidths.salesorder, \"px\")\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                        lineNumber: 534,\n                                        columnNumber: 19\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                        className: \"text-center top-0 font-bold text-sm\",\n                                        style: {\n                                            left: \"\".concat(gridcolumnWidths.product, \"px\")\n                                        },\n                                        children: \"TOTAL\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                        lineNumber: 539,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                        className: \"!text-center sticky top-0 font-bold text-sm\",\n                                        children: (_columnTotals_casesOrdered = columnTotals.casesOrdered) !== null && _columnTotals_casesOrdered !== void 0 ? _columnTotals_casesOrdered : \"-\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                        lineNumber: 546,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                        className: \"!text-center sticky top-0 font-bold text-sm\",\n                                        children: (_columnTotals_casesDelivered = columnTotals.casesDelivered) !== null && _columnTotals_casesDelivered !== void 0 ? _columnTotals_casesDelivered : \"-\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                        lineNumber: 549,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                        className: \"!text-center sticky top-0 font-bold text-sm\",\n                                        children: (_columnTotals_casesDifference = columnTotals.casesDifference) !== null && _columnTotals_casesDifference !== void 0 ? _columnTotals_casesDifference : \"-\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                        lineNumber: 552,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                        className: \"!text-center sticky top-0 font-bold text-sm\",\n                                        children: (_columnTotals_addedReasons = columnTotals.addedReasons) !== null && _columnTotals_addedReasons !== void 0 ? _columnTotals_addedReasons : \"-\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                        lineNumber: 555,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                        className: \"sticky top-0 font-bold text-sm\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                        lineNumber: 558,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                        className: \"sticky top-0 font-bold text-sm\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                        lineNumber: 559,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                        className: \"sticky top-0 font-bold text-sm\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                        lineNumber: 560,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                        className: \"!text-center sticky top-0 font-bold text-sm\",\n                                        children: ((_columnTotals_totalValue1 = columnTotals.totalValue) !== null && _columnTotals_totalValue1 !== void 0 ? _columnTotals_totalValue1 : \"-\") ? \"\\xa3\".concat((_columnTotals_totalValue = columnTotals.totalValue) === null || _columnTotals_totalValue === void 0 ? void 0 : _columnTotals_totalValue.toFixed(2)) : \"-\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                        lineNumber: 561,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                        className: \"sticky top-0 font-bold text-sm\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                        lineNumber: 567,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                        className: \"sticky top-0 font-bold text-sm\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                        lineNumber: 568,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                        className: \"sticky top-0 font-bold text-sm\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                        lineNumber: 569,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                        className: \"sticky top-0 font-bold text-sm\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                        lineNumber: 570,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                        className: \"sticky top-0 font-bold text-sm\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                        lineNumber: 571,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                lineNumber: 487,\n                                columnNumber: 15\n                            }, undefined),\n                            allSelectedProducts.map((data, index)=>{\n                                var _data_reasons, _checkedStates_columns, _checkedStates_columns1, _checkedStates_columns2, _checkedStates_columns3, _checkedStates_columns4, _checkedStates_columns5, _checkedStates_columns6, _data_SERVICE_LEVEL_PERCENT, _data_UNIT_PRICE, _this, _this1, _data_reasons_, _data_reasons_1, _data_reasons_2;\n                                if (!seeAll && data.CASES_DIFFERENCE - data.CASES_ADDED_REASONS == 0) return;\n                                const tooltip = data.LOCKED_BY ? \"\".concat(data.LOCKED_BY, \" is currently editing this order.\") : data.NEW_LINE_FLAG == 1 ? \"This is an additional order.\" : \"\";\n                                const isSelected = selectedRows === null || selectedRows === void 0 ? void 0 : selectedRows.includes(data);\n                                const rowHighlight = \"\".concat(isSelected ? \"bg-locked-products\" : ((_data_reasons = data.reasons) === null || _data_reasons === void 0 ? void 0 : _data_reasons.length) > 0 && data.CASES_DIFFERENCE - data.CASES_ADDED_REASONS > 0 ? \"bg-needsupdate-status\" : !!data.LOCKED_BY ? \"bg-locked-products\" : data.NEW_LINE_FLAG == 1 ? \"bg-volumechange-status\" : \"\");\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                            className: \"sticky top-0 font-normal text-sm \".concat(rowHighlight ? \"!\".concat(rowHighlight) : \"\"),\n                                            style: {\n                                                left: \"\".concat(0, \"px\")\n                                            },\n                                            title: tooltip,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-full flex justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"checkbox\",\n                                                    title: \"\".concat(data.NEW_LINE_FLAG == 1 ? \"This is an additional order.\" : \"\"),\n                                                    className: \"w-5 h-5 text-blue border-theme-blue2 rounded accent-skin-primary\",\n                                                    onChange: ()=>handleCheckboxChange(data),\n                                                    disabled: selectedMasterProductCode === \"all\" && masterProducts.length > 2 || selectedCustomer === \"All Customers\" || data.LOCKED_BY !== null && data.LOCKED_BY !== \"\" || data.CASES_DIFFERENCE == 0,\n                                                    // disabled={data.CASES_ADDED_REASONS!=0}\n                                                    checked: selectedRows.includes(data)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                                    lineNumber: 609,\n                                                    columnNumber: 23\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                                lineNumber: 608,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                            lineNumber: 601,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        (checkedStates === null || checkedStates === void 0 ? void 0 : (_checkedStates_columns = checkedStates.columns) === null || _checkedStates_columns === void 0 ? void 0 : _checkedStates_columns.depotdate) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                            className: \"sticky top-0 font-normal text-sm \".concat(rowHighlight ? \"!\".concat(rowHighlight) : \"\"),\n                                            style: {\n                                                left: \"\".concat(gridcolumnWidths.checkboxWidth, \"px\")\n                                            },\n                                            title: tooltip,\n                                            children: data.DEPOT_DATE && new Date(data.DEPOT_DATE).toLocaleDateString(\"en-GB\")\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                            lineNumber: 631,\n                                            columnNumber: 21\n                                        }, undefined),\n                                        ADCompanyName == \"Integrated Service Solutions Ltd\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                            className: \"sticky top-0 font-normal text-sm \".concat(rowHighlight ? \"!\".concat(rowHighlight) : \"\"),\n                                            style: {\n                                                left: \"\".concat(gridcolumnWidths.checkboxWidth, \"px\")\n                                            },\n                                            title: tooltip,\n                                            children: data.SERVICE_CUSTOMERS && data.SERVICE_CUSTOMERS\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                            lineNumber: 642,\n                                            columnNumber: 73\n                                        }, undefined),\n                                        (checkedStates === null || checkedStates === void 0 ? void 0 : (_checkedStates_columns1 = checkedStates.columns) === null || _checkedStates_columns1 === void 0 ? void 0 : _checkedStates_columns1.weekNo) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                            className: \"sticky top-0 font-normal text-center text-sm \".concat(rowHighlight ? \"!\".concat(rowHighlight) : \"\"),\n                                            style: {\n                                                left: \"\".concat(gridcolumnWidths.depotdate, \"px\")\n                                            },\n                                            title: tooltip,\n                                            children: data === null || data === void 0 ? void 0 : data.FISCAL_WEEK\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                            lineNumber: 653,\n                                            columnNumber: 21\n                                        }, undefined),\n                                        (checkedStates === null || checkedStates === void 0 ? void 0 : (_checkedStates_columns2 = checkedStates.columns) === null || _checkedStates_columns2 === void 0 ? void 0 : _checkedStates_columns2.altfill) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                            className: \"sticky top-0 font-normal  text-sm \".concat(rowHighlight ? \"!\".concat(rowHighlight) : \"\"),\n                                            style: {\n                                                left: \"\".concat(gridcolumnWidths.weekNo, \"px\")\n                                            },\n                                            title: tooltip,\n                                            children: data.ALTFILID\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                            lineNumber: 664,\n                                            columnNumber: 21\n                                        }, undefined),\n                                        (checkedStates === null || checkedStates === void 0 ? void 0 : (_checkedStates_columns3 = checkedStates.columns) === null || _checkedStates_columns3 === void 0 ? void 0 : _checkedStates_columns3.customer) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                            className: \"sticky top-0 font-normal  text-sm \".concat(rowHighlight ? \"!\".concat(rowHighlight) : \"\"),\n                                            style: {\n                                                left: \"\".concat(gridcolumnWidths.altfill, \"px\")\n                                            },\n                                            title: tooltip,\n                                            children: data.CUSTOMER\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                            lineNumber: 675,\n                                            columnNumber: 21\n                                        }, undefined),\n                                        (checkedStates === null || checkedStates === void 0 ? void 0 : (_checkedStates_columns4 = checkedStates.columns) === null || _checkedStates_columns4 === void 0 ? void 0 : _checkedStates_columns4.salesorder) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                            className: \"sticky top-0 font-normal text-sm \".concat(rowHighlight ? \"!\".concat(rowHighlight) : \"\"),\n                                            style: {\n                                                left: \"\".concat(gridcolumnWidths.customer, \"px\")\n                                            },\n                                            title: tooltip,\n                                            children: data.ORD_NUMBER\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                            lineNumber: 686,\n                                            columnNumber: 21\n                                        }, undefined),\n                                        (checkedStates === null || checkedStates === void 0 ? void 0 : (_checkedStates_columns5 = checkedStates.columns) === null || _checkedStates_columns5 === void 0 ? void 0 : _checkedStates_columns5.salesOrderId) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                            className: \"sticky top-0 font-normal text-sm \".concat(rowHighlight ? \"!\".concat(rowHighlight) : \"\"),\n                                            style: {\n                                                left: \"\".concat(gridcolumnWidths.customer, \"px\")\n                                            },\n                                            title: tooltip,\n                                            children: data.ORD_ID\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                            lineNumber: 697,\n                                            columnNumber: 21\n                                        }, undefined),\n                                        (checkedStates === null || checkedStates === void 0 ? void 0 : (_checkedStates_columns6 = checkedStates.columns) === null || _checkedStates_columns6 === void 0 ? void 0 : _checkedStates_columns6.product) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                            className: \"!w-80 sticky top-0 font-normal text-sm \".concat(rowHighlight ? \"!\".concat(rowHighlight) : \"\"),\n                                            style: {\n                                                left: \"\".concat(gridcolumnWidths.salesorder, \"px\")\n                                            },\n                                            title: tooltip,\n                                            children: data.PRODUCT_DESCRIPTION\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                            lineNumber: 708,\n                                            columnNumber: 21\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                            className: \"top-0 text-center font-normal text-sm \".concat(rowHighlight ? \"!\".concat(rowHighlight) : \"\"),\n                                            style: {\n                                                left: \"\".concat(gridcolumnWidths.product, \"px\")\n                                            },\n                                            title: tooltip,\n                                            children: data.CASE_SIZE\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                            lineNumber: 718,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"!text-center text-sm \".concat(rowHighlight ? \"!\".concat(rowHighlight) : \"\"),\n                                            title: tooltip,\n                                            children: data.CASES_ORIGINAL\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                            lineNumber: 727,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"!text-center text-sm \".concat(rowHighlight ? \"!\".concat(rowHighlight) : \"\"),\n                                            title: tooltip,\n                                            children: data.CASES_DELIVERED\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                            lineNumber: 735,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"!text-center text-sm font-bold \".concat(rowHighlight ? \"!\".concat(rowHighlight) : \"\"),\n                                            title: tooltip,\n                                            children: data.CASES_DIFFERENCE - data.CASES_ADDED_REASONS\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                            lineNumber: 743,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"!text-center text-sm \".concat(rowHighlight ? \"!\".concat(rowHighlight) : \"\"),\n                                            title: tooltip,\n                                            children: data.CASES_ADDED_REASONS\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                            lineNumber: 751,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"!text-center text-sm \".concat(rowHighlight ? \"!\".concat(rowHighlight) : \"\"),\n                                            title: tooltip,\n                                            children: \"\".concat((_data_SERVICE_LEVEL_PERCENT = data.SERVICE_LEVEL_PERCENT) === null || _data_SERVICE_LEVEL_PERCENT === void 0 ? void 0 : _data_SERVICE_LEVEL_PERCENT.toFixed(2), \"%\")\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                            lineNumber: 759,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"!text-center text-sm \".concat(rowHighlight ? \"!\".concat(rowHighlight) : \"\"),\n                                            title: tooltip,\n                                            children: [\n                                                \"\\xa3\",\n                                                (_data_UNIT_PRICE = data.UNIT_PRICE) === null || _data_UNIT_PRICE === void 0 ? void 0 : _data_UNIT_PRICE.toFixed(2)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                            lineNumber: 767,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"!text-center text-sm \".concat(rowHighlight ? \"!\".concat(rowHighlight) : \"\"),\n                                            title: tooltip,\n                                            children: \"\\xa3\".concat((_this = data.CASE_SIZE * data.UNIT_PRICE) === null || _this === void 0 ? void 0 : _this.toFixed(2))\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                            lineNumber: 775,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"!text-center text-sm \".concat(rowHighlight ? \"!\".concat(rowHighlight) : \"\"),\n                                            title: tooltip,\n                                            children: \"\\xa3\".concat((_this1 = data.CASE_SIZE * data.UNIT_PRICE * data.CASES_DIFFERENCE) === null || _this1 === void 0 ? void 0 : _this1.toFixed(2))\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                            lineNumber: 783,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"!text-center text-sm \".concat(rowHighlight ? \"!\".concat(rowHighlight) : \"\"),\n                                            title: tooltip,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"rounded-lg capitalize px-2 py-1 !text-center \".concat(data.ORD_STATUS === \"Cancelled\" ? \"bg-[#ff2929] text-white\" : data.ORD_STATUS === \"Open\" ? \"bg-[#54C5ED] text-white\" : data.ORD_STATUS === \"Invoiced\" ? \"bg-[#FFAE00] text-white\" : data.ORD_STATUS === \"Delivered\" ? \"bg-[#3EAB58] text-white\" : data.ORD_STATUS === \"Picked\" ? \"bg-[#FF6C09] text-white\" : \"bg-qtydiff-status !text-gray-700\" // Default style for any other status\n                                                ),\n                                                title: tooltip,\n                                                children: data.ORD_STATUS.toLowerCase()\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                                lineNumber: 801,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                            lineNumber: 795,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"relative \".concat(rowHighlight ? \"!\".concat(rowHighlight) : \"\"),\n                                            title: tooltip,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: (_data_reasons_ = data.reasons[0]) === null || _data_reasons_ === void 0 ? void 0 : _data_reasons_.MAIN_REASON\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                                    lineNumber: 827,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ReasonsDetails__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                    reasonsData: data.reasons,\n                                                    type: \"reasonsDetails\",\n                                                    seeAll: seeAll\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                                    lineNumber: 828,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                            lineNumber: 820,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"!text-left text-sm capitalize \".concat(rowHighlight ? \"!\".concat(rowHighlight) : \"\"),\n                                            title: tooltip,\n                                            children: (_data_reasons_1 = data.reasons[0]) === null || _data_reasons_1 === void 0 ? void 0 : _data_reasons_1.SUB_REASON\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                            lineNumber: 835,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"!text-left text-sm capitalize \".concat(rowHighlight ? \"!\".concat(rowHighlight) : \"\"),\n                                            title: tooltip,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-full text-truncate2L \",\n                                                children: (_data_reasons_2 = data.reasons[0]) === null || _data_reasons_2 === void 0 ? void 0 : _data_reasons_2.COMMENT\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                                lineNumber: 849,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                            lineNumber: 843,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"!text-center text-sm !bg-[#f3f8ff] \".concat(rowHighlight ? \"!\".concat(rowHighlight) : \"\"),\n                                            title: \"\".concat(!!data.LOCKED_BY ? \"You cannot edit the order while someone is already working on it.\" : \"\"),\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>{\n                                                    handleViewDetailsClick(data);\n                                                    setIsBulkUpdate(false);\n                                                },\n                                                className: \"cursor-pointer\",\n                                                disabled: !!data.LOCKED_BY || data.CASES_DIFFERENCE == 0 || selectedRows.length > 0,\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fluentui_react_components__WEBPACK_IMPORTED_MODULE_9__.Tooltip, {\n                                                    content: \"View Order Details\",\n                                                    relationship: \"label\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        xmlns: \"http://www.w3.org/2000/svg\",\n                                                        viewBox: \"0 0 512 512\",\n                                                        className: \" w-5 h-5 !text-skin-primary \",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            fill: \"currentcolor\",\n                                                            d: \"M480 448c0 17.7-14.3 32-32 32l-256 0c-17.7 0-32-14.3-32-32l0-80-32 0 0 80c0 35.3 28.7 64 64 64l256 0c35.3 0 64-28.7 64-64l0-284.1c0-12.7-5.1-24.9-14.1-33.9L382.1 14.1c-9-9-21.2-14.1-33.9-14.1L192 0c-35.3 0-64 28.7-64 64l0 192 32 0 0-192c0-17.7 14.3-32 32-32l128 0 0 112c0 26.5 21.5 48 48 48l112 0 0 256zm-.5-288L368 160c-8.8 0-16-7.2-16-16l0-111.5c2.8 .7 5.4 2.1 7.4 4.2L475.3 152.6c2.1 2.1 3.5 4.6 4.2 7.4zM283.3 212.7c-6.2-6.2-16.4-6.2-22.6 0s-6.2 16.4 0 22.6L329.4 304 16 304c-8.8 0-16 7.2-16 16s7.2 16 16 16l313.4 0-68.7 68.7c-6.2 6.2-6.2 16.4 0 22.6s16.4 6.2 22.6 0l96-96c6.2-6.2 6.2-16.4 0-22.6l-96-96z\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                                            lineNumber: 885,\n                                                            columnNumber: 27\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                                        lineNumber: 880,\n                                                        columnNumber: 25\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                                    lineNumber: 876,\n                                                    columnNumber: 23\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                                lineNumber: 864,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                            lineNumber: 854,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, index, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                    lineNumber: 600,\n                                    columnNumber: 17\n                                }, undefined);\n                            })\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                        lineNumber: 485,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                lineNumber: 339,\n                columnNumber: 9\n            }, undefined),\n            isOpen && selectedRows.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ViewDetails__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                data: selectedRows,\n                setData: setSelectedRows,\n                setAllSelectedProducts: setAllSelectedProducts,\n                setMapForReasonsParentsAndTheirCorrespondingChildren: setMapForReasonsParentsAndTheirCorrespondingChildren,\n                reasonsMasterList: reasonsMasterList,\n                parentReasonList: parentReasonList,\n                reasonsData: reasonsData,\n                fetchReasonData: fetchReasonData,\n                userData: userData,\n                isOpen: isOpen,\n                setIsOpen: setIsOpen,\n                bulkUpdateData: bulkUpdateData,\n                isBulkUpdate: isBulkUpdate,\n                setReasonsData: setReasonsData,\n                setBulkUpdateData: setBulkUpdateData,\n                setIsHeaderChecked: setIsHeaderChecked,\n                setSelectedRows: setSelectedRows\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                lineNumber: 900,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n        lineNumber: 335,\n        columnNumber: 5\n    }, undefined);\n};\n_s(SLTable, \"vX4vuRMTc2tBY6elYTRITygxPGY=\");\n_c = SLTable;\n/* harmony default export */ __webpack_exports__[\"default\"] = (SLTable);\nvar _c;\n$RefreshReg$(_c, \"SLTable\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevExports = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevExports) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports on update so we can compare the boundary\n                // signatures.\n                module.hot.dispose(function (data) {\n                    data.prevExports = currentExports;\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevExports !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevExports, currentExports)) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevExports !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/service_level/SLTable.js\n"));

/***/ })

});