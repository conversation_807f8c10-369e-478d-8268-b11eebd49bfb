"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/login",{

/***/ "./components/LoginSection.js":
/*!************************************!*\
  !*** ./components/LoginSection.js ***!
  \************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/image */ \"./node_modules/next/image.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_image__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _public_images_microsoft_icon_png__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../public/images/microsoft-icon.png */ \"./public/images/microsoft-icon.png\");\n/* harmony import */ var _public_images_dps_logo_png__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../public/images/dps-logo.png */ \"./public/images/dps-logo.png\");\n/* harmony import */ var _public_images_user_account_png__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../public/images/user-account.png */ \"./public/images/user-account.png\");\n/* harmony import */ var _azure_msal_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @azure/msal-react */ \"./node_modules/@azure/msal-react/dist/index.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_7__);\n/* harmony import */ var _utils_loaders_loadingContext__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/utils/loaders/loadingContext */ \"./utils/loaders/loadingContext.js\");\n/* harmony import */ var js_cookie__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! js-cookie */ \"./node_modules/js-cookie/dist/js.cookie.mjs\");\n/* harmony import */ var _utils_auth_authConfig__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/utils/auth/authConfig */ \"./utils/auth/authConfig.js\");\n/* harmony import */ var _utils_auth_auth__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/utils/auth/auth */ \"./utils/auth/auth.js\");\n/* harmony import */ var _utils_userContext__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/utils/userContext */ \"./utils/userContext.js\");\n/* harmony import */ var _services_apiConfig__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/services/apiConfig */ \"./services/apiConfig.js\");\n/* harmony import */ var react_toastify__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! react-toastify */ \"./node_modules/react-toastify/dist/react-toastify.esm.mjs\");\n/* harmony import */ var react_toastify_dist_ReactToastify_css__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! react-toastify/dist/ReactToastify.css */ \"./node_modules/react-toastify/dist/ReactToastify.css\");\n/* harmony import */ var react_toastify_dist_ReactToastify_css__WEBPACK_IMPORTED_MODULE_15___default = /*#__PURE__*/__webpack_require__.n(react_toastify_dist_ReactToastify_css__WEBPACK_IMPORTED_MODULE_15__);\n/* harmony import */ var _utils_themeContext__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @/utils/themeContext */ \"./utils/themeContext.js\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst LoginSection = ()=>{\n    _s();\n    const { userDetails, updateToken } = (0,_utils_userContext__WEBPACK_IMPORTED_MODULE_12__.useUser)();\n    const { instance, accounts } = (0,_azure_msal_react__WEBPACK_IMPORTED_MODULE_6__.useMsal)();\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_7__.useRouter)();\n    const { redirect } = router.query;\n    const { setIsLoading } = (0,_utils_loaders_loadingContext__WEBPACK_IMPORTED_MODULE_8__.useLoading)();\n    const [accountExist, setAccountExist] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const { setThemeColor } = (0,_utils_themeContext__WEBPACK_IMPORTED_MODULE_16__.useTheme)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        setAccountExist((accounts === null || accounts === void 0 ? void 0 : accounts.length) > 0);\n    }, [\n        accounts\n    ]);\n    function extractCompanyName(email) {\n        const domain = email === null || email === void 0 ? void 0 : email.split(\"@\")[1];\n        const companyName = domain === null || domain === void 0 ? void 0 : domain.split(\".\")[0];\n        return companyName;\n    }\n    const getUserDetails = async (accessToken)=>{\n        try {\n            const response = await fetch(\"https://graph.microsoft.com/v1.0/me?$select=displayName,mail,companyName\", {\n                headers: {\n                    Authorization: \"Bearer \".concat(accessToken),\n                    \"Content-Type\": \"application/json\"\n                }\n            });\n            const data = await response.json();\n            return data;\n        } catch (error) {\n            console.error(\"Error fetching user data:\", error);\n            return null;\n        }\n    };\n    const signInHandler = async ()=>{\n        setIsLoading(true);\n        try {\n            const request = {\n                prompt: \"select_account\",\n                scopes: [\n                    \"User.Read\",\n                    \"Directory.Read.All\"\n                ]\n            };\n            const loginObj = await instance.loginPopup(request);\n            instance.setActiveAccount(loginObj.account);\n            scheduleTokenRenewal(loginObj);\n            js_cookie__WEBPACK_IMPORTED_MODULE_9__[\"default\"].set(\"token\", loginObj === null || loginObj === void 0 ? void 0 : loginObj.accessToken, {\n                expires: 365\n            });\n            let result = await (0,_utils_auth_auth__WEBPACK_IMPORTED_MODULE_11__.setUserCookie)(loginObj, instance, accounts[0], updateToken);\n            if (loginObj) {\n                var _loginObj_account;\n                const userData = await getUserDetails(loginObj.accessToken);\n                if (userData) {\n                    js_cookie__WEBPACK_IMPORTED_MODULE_9__[\"default\"].set(\"ADCompanyName\", userData.companyName);\n                } else {\n                    console.warn(\"Company name not found in AD.\");\n                }\n                const company = extractCompanyName(loginObj === null || loginObj === void 0 ? void 0 : (_loginObj_account = loginObj.account) === null || _loginObj_account === void 0 ? void 0 : _loginObj_account.username);\n                // const company = extractCompanyName(\"<EMAIL>\");\n                // const company = extractCompanyName(\"<EMAIL>\");\n                // const company = extractCompanyName(\"<EMAIL>\");\n                // localStorage.setItem(\"domain\", company);\n                js_cookie__WEBPACK_IMPORTED_MODULE_9__[\"default\"].set(\"company\", company);\n                const serverAddress = _services_apiConfig__WEBPACK_IMPORTED_MODULE_13__.apiConfig.serverAddress;\n                await fetch(\"\".concat(serverAddress, \"users/getTheme\"), {\n                    method: \"POST\",\n                    headers: {\n                        Accept: \"application/json\",\n                        \"Content-Type\": \"application/json\"\n                    },\n                    body: JSON.stringify({\n                        company: company,\n                        ADCompany: userData.companyName\n                    })\n                }).then((res)=>{\n                    // if(res.status === 400 || res.status === 401){\n                    //   setTimeout(function(){\n                    //     logoutHandler(instance);\n                    //     router.push('/login');\n                    //   }, 1000);\n                    // }\n                    if (res.status === 200) {\n                        return res.json();\n                    }\n                    return Promise.reject(res);\n                }).then((json)=>{\n                    let res = json;\n                    if (res) {\n                        js_cookie__WEBPACK_IMPORTED_MODULE_9__[\"default\"].set(\"theme\", JSON.stringify(res === null || res === void 0 ? void 0 : res.themeColour), {\n                            expires: 365\n                        });\n                        // localStorage.setItem(\"company\", company);\n                        js_cookie__WEBPACK_IMPORTED_MODULE_9__[\"default\"].set(\"company\", company);\n                        setThemeColor(res === null || res === void 0 ? void 0 : res.themeColour);\n                    } else {\n                        react_toastify__WEBPACK_IMPORTED_MODULE_14__.toast.error(\"Unauthorized User. Please try again.\", {\n                            position: \"top-right\"\n                        });\n                    }\n                }).catch((error)=>{\n                    react_toastify__WEBPACK_IMPORTED_MODULE_14__.toast.error(error.statusText, {\n                        position: \"top-right\"\n                    });\n                });\n                await fetch(\"\".concat(serverAddress, \"logs/logLogin\"), {\n                    method: \"POST\",\n                    headers: {\n                        \"Content-Type\": \"application/json\"\n                    },\n                    body: JSON.stringify({\n                        userData: loginObj === null || loginObj === void 0 ? void 0 : loginObj.account,\n                        company: userData.companyName\n                    })\n                });\n            }\n            if (redirect) {\n                // setIsLoading(false);\n                router.replace(redirect);\n            } else {\n                router.replace(\"/suppliers\");\n            }\n        } catch (error) {\n            //console.error(\"Error during login:\", error);\n            //toast.error(error.message, {\n            //  position: \"top-right\"\n            //});\n            setIsLoading(false);\n            return;\n        }\n    };\n    const useThisAccountHandler = async ()=>{\n        setIsLoading(true);\n        // Ensure active account is set or pass account in request\n        const activeAccount = instance.getActiveAccount();\n        if (!activeAccount) {\n            console.error(\"No active account found!\");\n            return;\n        }\n        const loginObj = await instance.acquireTokenSilent({\n            scopes: [\n                \"User.Read\"\n            ],\n            account: activeAccount,\n            forceRefresh: true\n        });\n        (0,_utils_auth_auth__WEBPACK_IMPORTED_MODULE_11__.setUserCookie)(loginObj, instance, accounts[0]);\n        instance.setActiveAccount(loginObj.account);\n        scheduleTokenRenewal(loginObj);\n        js_cookie__WEBPACK_IMPORTED_MODULE_9__[\"default\"].set(\"token\", loginObj === null || loginObj === void 0 ? void 0 : loginObj.accessToken, {\n            expires: 365\n        });\n        let result = await (0,_utils_auth_auth__WEBPACK_IMPORTED_MODULE_11__.setUserCookie)(loginObj, instance, accounts[0], updateToken);\n        if (loginObj) {\n            var _loginObj_account;\n            const userData = await getUserDetails(loginObj.accessToken);\n            if (userData) {\n                // Cookies.set(\"company\", userData.companyName);\n                js_cookie__WEBPACK_IMPORTED_MODULE_9__[\"default\"].set(\"ADCompanyName\", userData.companyName);\n            } else {\n                console.warn(\"Company name not found in AD.\");\n            }\n            const company = extractCompanyName(loginObj === null || loginObj === void 0 ? void 0 : (_loginObj_account = loginObj.account) === null || _loginObj_account === void 0 ? void 0 : _loginObj_account.username);\n            // const company = extractCompanyName(\"<EMAIL>\");\n            // const company = extractCompanyName(\"<EMAIL>\");\n            // const company = extractCompanyName(\"<EMAIL>\");\n            // localStorage.setItem(\"domain\", company);\n            js_cookie__WEBPACK_IMPORTED_MODULE_9__[\"default\"].set(\"company\", company);\n            const serverAddress = _services_apiConfig__WEBPACK_IMPORTED_MODULE_13__.apiConfig.serverAddress;\n            fetch(\"\".concat(serverAddress, \"users/getTheme\"), {\n                method: \"POST\",\n                headers: {\n                    Accept: \"application/json\",\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    company: company,\n                    ADCompany: userData.companyName\n                })\n            }).then((res)=>{\n                // if(res.status === 400 || res.status === 401){\n                //   setTimeout(function(){\n                //     logoutHandler(instance);\n                //     router.push('/login');\n                //   }, 1000);\n                // }\n                if (res.status === 200) {\n                    return res.json();\n                }\n                return Promise.reject(res);\n            }).then((json)=>{\n                let res = json;\n                if (res) {\n                    js_cookie__WEBPACK_IMPORTED_MODULE_9__[\"default\"].set(\"theme\", JSON.stringify(res === null || res === void 0 ? void 0 : res.themeColour), {\n                        expires: 365\n                    });\n                    // localStorage.setItem(\"company\", company);\n                    js_cookie__WEBPACK_IMPORTED_MODULE_9__[\"default\"].set(\"company\", company);\n                    setThemeColor(res === null || res === void 0 ? void 0 : res.themeColour);\n                } else {\n                    react_toastify__WEBPACK_IMPORTED_MODULE_14__.toast.error(\"Unauthorized User. Please try again.\", {\n                        position: \"top-right\"\n                    });\n                }\n            }).catch((error)=>{\n                react_toastify__WEBPACK_IMPORTED_MODULE_14__.toast.error(error.statusText, {\n                    position: \"top-right\"\n                });\n            });\n            fetch(\"\".concat(serverAddress, \"logs/logLogin\"), {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    userData: loginObj.account,\n                    company: userData.companyName\n                })\n            });\n        }\n        if (redirect) {\n            router.push(redirect);\n        } else {\n            router.push(\"/suppliers\");\n        }\n    };\n    function scheduleTokenRenewal(loginObj) {\n        setTimeout(async ()=>{\n            try {\n                // Ensure active account is set or pass account in request\n                const activeAccount = instance.getActiveAccount();\n                if (!activeAccount) {\n                    console.error(\"No active account found!\");\n                    return;\n                }\n                const tokenResponse = await instance.acquireTokenSilent({\n                    scopes: [\n                        \"User.Read\"\n                    ],\n                    account: activeAccount,\n                    forceRefresh: true\n                });\n                // Refresh the access token in the cookie\n                js_cookie__WEBPACK_IMPORTED_MODULE_9__[\"default\"].set(\"token\", tokenResponse.accessToken, {\n                    expires: 365\n                });\n                // ReSchedule the next token renewal\n                scheduleTokenRenewal(tokenResponse);\n            } catch (error) {\n                console.error(\"Silent token acquisition failed: \", error);\n                signInHandler();\n            }\n        }, loginObj.expiresOn.getTime() - Date.now() - 60000);\n    }\n    // const router = useRouter();\n    // const [ userdata, setUserdata ] = useState(\"\");\n    // const [errorMessage, setErrorMessage] = useState(\"\");\n    // useEffect(() => {\n    //   const userdetails = Cookies.get('user');\n    //   const sessionEmail = sessionStorage.getItem('useremail');\n    //   if(userdetails && sessionEmail){\n    //     router.push('/suppliers');\n    //   }\n    //   if(userdetails){\n    //     const cookieParse = JSON.parse(userdetails);\n    //     setUserdata(cookieParse);\n    //   } else {\n    //     setUserdata('');\n    //   }\n    //   if(router.query?.message == 'text')\n    //   {\n    //     setErrorMessage(\"Authentication is not allowed\");\n    //   }\n    // }, []);\n    // const { login } = useAuth();\n    // msalInstance.handleRedirectPromise();\n    // const handleLogin = async () => {\n    //   await login();\n    // };\n    // Cookies.remove('userdatails');\n    // const setUserDetails = () => {\n    //   sessionStorage.setItem('useremail', userdata.email_id);\n    //   router.push('suppliers');\n    // }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-end\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\LoginSection.js\",\n                    lineNumber: 334,\n                    columnNumber: 11\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-col items-center justify-center h-full\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"font-semibold text-2xl xl:text-4xl 2xl:text-5xl py-2\",\n                            children: \"Get Started Now\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\LoginSection.js\",\n                            lineNumber: 336,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-base xl:text-lg 2xl:text-xl my-1\",\n                            children: \"Please login to access your account\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\LoginSection.js\",\n                            lineNumber: 339,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-row items-center rounded-md cursor-pointer\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                className: \"flex items-center rounded-md border border-gray-300 p-4 gap-5 lg:gap-9 mt-4\",\n                                onClick: signInHandler,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                        src: _public_images_microsoft_icon_png__WEBPACK_IMPORTED_MODULE_3__[\"default\"],\n                                        className: \"\",\n                                        width: 20,\n                                        height: 20,\n                                        alt: \"Microsoft Logo\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\LoginSection.js\",\n                                        lineNumber: 347,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-lg text-center lg:text-left\",\n                                        children: \"Sign in with Microsoft\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\LoginSection.js\",\n                                        lineNumber: 354,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\LoginSection.js\",\n                                lineNumber: 343,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\LoginSection.js\",\n                            lineNumber: 342,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\LoginSection.js\",\n                    lineNumber: 335,\n                    columnNumber: 11\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\LoginSection.js\",\n            lineNumber: 330,\n            columnNumber: 9\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\LoginSection.js\",\n        lineNumber: 329,\n        columnNumber: 5\n    }, undefined);\n};\n_s(LoginSection, \"Yi+toZE7kHZ33aw2v8QxPSUp7vE=\", false, function() {\n    return [\n        _utils_userContext__WEBPACK_IMPORTED_MODULE_12__.useUser,\n        _azure_msal_react__WEBPACK_IMPORTED_MODULE_6__.useMsal,\n        next_router__WEBPACK_IMPORTED_MODULE_7__.useRouter,\n        _utils_loaders_loadingContext__WEBPACK_IMPORTED_MODULE_8__.useLoading,\n        _utils_themeContext__WEBPACK_IMPORTED_MODULE_16__.useTheme\n    ];\n});\n_c = LoginSection;\n/* harmony default export */ __webpack_exports__[\"default\"] = (LoginSection);\nvar _c;\n$RefreshReg$(_c, \"LoginSection\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevExports = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevExports) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports on update so we can compare the boundary\n                // signatures.\n                module.hot.dispose(function (data) {\n                    data.prevExports = currentExports;\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevExports !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevExports, currentExports)) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevExports !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************\n//# sourceURL=webpack-internal:///./components/LoginSection.js\n"));

/***/ })

});