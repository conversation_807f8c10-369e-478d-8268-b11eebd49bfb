"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/variety/[productId]/edit",{

/***/ "./pages/variety/[productId]/edit/index.js":
/*!*************************************************!*\
  !*** ./pages/variety/[productId]/edit/index.js ***!
  \*************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __N_SSP: function() { return /* binding */ __N_SSP; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _components_Layout__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/Layout */ \"./components/Layout.js\");\n/* harmony import */ var _services_apiConfig__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/services/apiConfig */ \"./services/apiConfig.js\");\n/* harmony import */ var js_cookie__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! js-cookie */ \"./node_modules/js-cookie/dist/js.cookie.mjs\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var react_loader_spinner__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! react-loader-spinner */ \"./node_modules/react-loader-spinner/dist/module.js\");\n/* harmony import */ var react_toastify__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! react-toastify */ \"./node_modules/react-toastify/dist/react-toastify.esm.mjs\");\n/* harmony import */ var react_toastify_dist_ReactToastify_css__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! react-toastify/dist/ReactToastify.css */ \"./node_modules/react-toastify/dist/ReactToastify.css\");\n/* harmony import */ var react_toastify_dist_ReactToastify_css__WEBPACK_IMPORTED_MODULE_8___default = /*#__PURE__*/__webpack_require__.n(react_toastify_dist_ReactToastify_css__WEBPACK_IMPORTED_MODULE_8__);\n/* harmony import */ var _components_NewVarietyRequest__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/NewVarietyRequest */ \"./components/NewVarietyRequest.js\");\n/* harmony import */ var _utils_extractCompanyFromEmail__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/utils/extractCompanyFromEmail */ \"./utils/extractCompanyFromEmail.js\");\n/* harmony import */ var _utils_secureStorage__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/utils/secureStorage */ \"./utils/secureStorage.js\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\nconst index = (param)=>{\n    let { userData } = param;\n    _s();\n    const [dropdowns, setDropdowns] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(null);\n    const [newVarietyData, setNewVarietyData] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(null);\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_4__.useRouter)();\n    const { productId } = router.query;\n    (0,react__WEBPACK_IMPORTED_MODULE_5__.useEffect)(()=>{\n        // const company = Cookies.get(\"company\");\n        const company = (userData === null || userData === void 0 ? void 0 : userData.company) || (0,_utils_extractCompanyFromEmail__WEBPACK_IMPORTED_MODULE_10__.extractCompanyFromEmail)(userData === null || userData === void 0 ? void 0 : userData.email);\n        let prophetId = 1;\n        if (company == \"dpsltd\") {\n            prophetId = 1;\n        } else if (company == \"efcltd\") {\n            prophetId = 3;\n        } else if (company == \"fpp-ltd\") {\n            prophetId = 4;\n        }\n        const fetchData = async ()=>{\n            const serverAddress = _services_apiConfig__WEBPACK_IMPORTED_MODULE_2__.apiConfig.serverAddress;\n            try {\n                const allDropDowns = [\n                    \"masterProductCode\"\n                ];\n                const dropdownsRequest = await fetch(\"\".concat(serverAddress, \"products/get-products-dropdowns-list?prophetId=\").concat(prophetId), {\n                    method: \"POST\",\n                    headers: {\n                        Accept: \"application/json\",\n                        \"Content-Type\": \"application/json\"\n                    },\n                    credentials: \"include\",\n                    body: JSON.stringify(allDropDowns)\n                });\n                // console.log(\"dropdownsRequest\",dropdownsRequest);\n                if (dropdownsRequest.status === 401) {\n                    react_toastify__WEBPACK_IMPORTED_MODULE_7__.toast.error(\"Your session has expired. Please log in again.\");\n                    setTimeout(async ()=>{\n                        await (0,_utils_secureStorage__WEBPACK_IMPORTED_MODULE_11__.logout)();\n                        const redirectUrl = \"/login?redirect=\".concat(encodeURIComponent(window.location.pathname));\n                        router.push(redirectUrl);\n                    }, 3000);\n                    return null;\n                }\n                const newVarietyRequest = fetch(\"\".concat(serverAddress, \"products/get-nv-product-by-id/\").concat(productId), {\n                    method: \"GET\",\n                    headers: {\n                        Accept: \"application/json\",\n                        \"Content-Type\": \"application/json\"\n                    },\n                    credentials: \"include\"\n                });\n                if (newVarietyRequest.status === 401) {\n                    react_toastify__WEBPACK_IMPORTED_MODULE_7__.toast.error(\"Your session has expired. Please log in again.\");\n                    setTimeout(async ()=>{\n                        await (0,_utils_secureStorage__WEBPACK_IMPORTED_MODULE_11__.logout)();\n                        const redirectUrl = \"/login?redirect=\".concat(encodeURIComponent(window.location.pathname));\n                        router.push(redirectUrl);\n                    }, 3000);\n                    return null;\n                }\n                if (newVarietyRequest.status === 400) {\n                    react_toastify__WEBPACK_IMPORTED_MODULE_7__.toast.error(\"There was an error with your request. Please check your data and try again.\");\n                }\n                const [dropdownsResponse, newVarietyResponse] = await Promise.all([\n                    dropdownsRequest,\n                    newVarietyRequest\n                ]);\n                const allDropdownsList = await dropdownsResponse.json();\n                const newVarietyData = await newVarietyResponse.json();\n                setDropdowns(allDropdownsList);\n                setNewVarietyData(newVarietyData);\n            } catch (error) {\n                console.error(\"Error fetching data\", error);\n            }\n        };\n        if (productId) {\n            fetchData();\n        }\n    }, [\n        productId\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Layout__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n        userData: userData,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_toastify__WEBPACK_IMPORTED_MODULE_7__.ToastContainer, {\n                limit: 1\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\variety\\\\[productId]\\\\edit\\\\index.js\",\n                lineNumber: 114,\n                columnNumber: 7\n            }, undefined),\n            !dropdowns && !newVarietyData ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    display: \"flex\",\n                    alignItems: \"center\",\n                    justifyContent: \"center\",\n                    height: \"100vh\"\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_loader_spinner__WEBPACK_IMPORTED_MODULE_6__.ThreeCircles, {\n                    color: \"#002D73\",\n                    height: 50,\n                    width: 50,\n                    visible: true,\n                    ariaLabel: \"oval-loading\",\n                    secondaryColor: \"#0066FF\",\n                    strokeWidth: 2,\n                    strokeWidthSecondary: 2\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\variety\\\\[productId]\\\\edit\\\\index.js\",\n                    lineNumber: 124,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\variety\\\\[productId]\\\\edit\\\\index.js\",\n                lineNumber: 116,\n                columnNumber: 9\n            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_NewVarietyRequest__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                dropdowns: dropdowns,\n                userData: userData,\n                newVarietyData: newVarietyData[0],\n                pageType: \"update\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\variety\\\\[productId]\\\\edit\\\\index.js\",\n                lineNumber: 136,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\variety\\\\[productId]\\\\edit\\\\index.js\",\n        lineNumber: 113,\n        columnNumber: 5\n    }, undefined);\n};\n_s(index, \"Hed4/xYb5dPQ/rk452hUP3Z4GUA=\", false, function() {\n    return [\n        next_router__WEBPACK_IMPORTED_MODULE_4__.useRouter\n    ];\n});\nvar __N_SSP = true;\n/* harmony default export */ __webpack_exports__[\"default\"] = (index);\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevExports = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevExports) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports on update so we can compare the boundary\n                // signatures.\n                module.hot.dispose(function (data) {\n                    data.prevExports = currentExports;\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevExports !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevExports, currentExports)) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevExports !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./pages/variety/[productId]/edit/index.js\n"));

/***/ })

});