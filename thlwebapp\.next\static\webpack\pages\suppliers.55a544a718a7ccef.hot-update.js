"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/suppliers",{

/***/ "./utils/exportExcel.js":
/*!******************************!*\
  !*** ./utils/exportExcel.js ***!
  \******************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var exceljs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! exceljs */ \"./node_modules/exceljs/dist/exceljs.min.js\");\n/* harmony import */ var exceljs__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(exceljs__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react_toastify__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react-toastify */ \"./node_modules/react-toastify/dist/react-toastify.esm.mjs\");\n/* harmony import */ var react_toastify_dist_ReactToastify_css__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-toastify/dist/ReactToastify.css */ \"./node_modules/react-toastify/dist/ReactToastify.css\");\n/* harmony import */ var react_toastify_dist_ReactToastify_css__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react_toastify_dist_ReactToastify_css__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _services_apiConfig__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/services/apiConfig */ \"./services/apiConfig.js\");\n\n\n\n\n// todo:move the supplierCode column from the excel file to first column\nconst exportExcel = async function(data, isInternal, token, company, userData, prophet_id, requestor_email, isMultiple) {\n    let isProductExtract = arguments.length > 8 && arguments[8] !== void 0 ? arguments[8] : false, onProductSubmit = arguments.length > 9 && arguments[9] !== void 0 ? arguments[9] : false, productEmailParagraph = arguments.length > 10 && arguments[10] !== void 0 ? arguments[10] : \"\", productEmailCommentPlaceholder = arguments.length > 11 && arguments[11] !== void 0 ? arguments[11] : \"\", request_no = arguments.length > 12 && arguments[12] !== void 0 ? arguments[12] : \"\", varietyRequest = arguments.length > 13 && arguments[13] !== void 0 ? arguments[13] : false;\n    let serverAddress = _services_apiConfig__WEBPACK_IMPORTED_MODULE_3__.apiConfig.serverAddress;\n    let isEmergencyAndFinanceNotCompleteObj = [];\n    let supplierNames = [];\n    const workbook = new (exceljs__WEBPACK_IMPORTED_MODULE_0___default().Workbook)();\n    if (isInternal) {\n        data.forEach((sheetData, index1)=>{\n            if (sheetData.length === 0) {\n                console.error(\"sheetData is empty for index:\", index1);\n                return;\n            }\n            const sheetName = sheetData[0] || \"Sheet\".concat(index1 + 1);\n            let worksheet;\n            if (index1 < 9) {\n                if (Array.isArray(sheetName)) {\n                    const actualSheetName = sheetName[0];\n                    worksheet = workbook.addWorksheet(actualSheetName);\n                } else {\n                    worksheet = workbook.addWorksheet(sheetName);\n                }\n            }\n            if (sheetData.length > 1) {\n                let headers;\n                if (index1 < 9) {\n                    headers = Object.keys(sheetData[1]);\n                    worksheet.addRow(headers);\n                }\n                sheetData.slice(1).forEach((row, internalIndex)=>{\n                    if (index1 < 9) {\n                        const rowData = headers.map((header)=>row[header] || \"\");\n                        if (index1 != 4 && index1 != 3) {\n                            worksheet.addRow(rowData);\n                        } else if (index1 == 4) {\n                            worksheet.addRow(rowData);\n                        } else if (index1 == 3 && rowData[1] != \"\") {\n                            worksheet.addRow(rowData);\n                        }\n                    }\n                    if (index1 === 9) {\n                        supplierNames.push({\n                            supplierName: row === null || row === void 0 ? void 0 : row.supplierName,\n                            supplierCode: row === null || row === void 0 ? void 0 : row.supplierCode\n                        });\n                        if (row.isEmergencyAndFinanceNotComplete) {\n                            let isEmergencyAndFinanceNotCompleteSupplier = {\n                                isEmergencyAndFinanceNotComplete: row.isEmergencyAndFinanceNotComplete,\n                                supplierName: row.supplierName\n                            };\n                            isEmergencyAndFinanceNotCompleteObj.push(isEmergencyAndFinanceNotCompleteSupplier);\n                        }\n                        fetch(\"\".concat(serverAddress, \"suppliers/update-supplier/\").concat(row.id), {\n                            method: \"PUT\",\n                            headers: {\n                                Accept: \"application/json\",\n                                \"Content-Type\": \"application/json\"\n                            },\n                            credentials: \"include\",\n                            body: JSON.stringify({\n                                sectionName: \"updateStatus\",\n                                type: \"exportExcel\",\n                                status: 5,\n                                exported: true,\n                                updated_date: new Date().toISOString(),\n                                company_name: row.supplierName,\n                                to: \"Internal\"\n                            })\n                        }).then((res)=>{\n                            if (res.status === 200) {\n                                return res.json();\n                            }\n                            if (res.status === 401) {\n                                react_toastify__WEBPACK_IMPORTED_MODULE_1__.toast.error(\"Your session has expired. Please log in again.\");\n                                setTimeout(()=>{\n                                    logout();\n                                    window.location.reload();\n                                }, 3000);\n                                return null;\n                            }\n                            return Promise.reject(res);\n                        }).then((json)=>{\n                            if (json.status == 200) {\n                                return true;\n                            }\n                        }).catch((error)=>{\n                            console.log(error);\n                        });\n                    }\n                });\n            }\n        });\n    } else if (isProductExtract) {\n        if (data.length === 0) {\n            console.error(\"sheetData is empty for index:\");\n            return;\n        }\n        data.forEach((sheetData)=>{\n            var _Object;\n            const sheetName = sheetData[0] || \"Sheet\".concat(index + 1);\n            let worksheet;\n            if (Array.isArray(sheetName)) {\n                const actualSheetName = sheetName[0];\n                worksheet = workbook.addWorksheet(actualSheetName);\n            } else {\n                worksheet = workbook.addWorksheet(sheetName);\n            }\n            // console.log(\"Object.keys(sheetData[1])\",Object?.keys(sheetData[1])? Object?.keys(sheetData[1]) : 'not there');\n            let headers = ((_Object = Object) === null || _Object === void 0 ? void 0 : _Object.keys(sheetData[1])) ? Object.keys(sheetData[1]) : \"null\";\n            if (isMultiple) {\n                headers = headers.slice(0, -1);\n            }\n            worksheet.addRow(headers);\n            sheetData.slice(1).forEach((row, internalIndex)=>{\n                let rowData;\n                rowData = headers.map((header)=>row[header] || \"\");\n                worksheet.addRow(rowData);\n            });\n        });\n    } else {\n        if (data.length === 0) {\n            console.error(\"sheetData is empty for index:\");\n            return;\n        }\n        data.forEach((row, index1)=>{\n            const sheetName = row[0] || \"Sheet\".concat(index1 + 1);\n            let worksheet;\n            if (Array.isArray(sheetName)) {\n                const actualSheetName = sheetName[0];\n                worksheet = workbook.addWorksheet(actualSheetName);\n            } else {\n                worksheet = workbook.addWorksheet(sheetName);\n            }\n            let headers;\n            if (index1 == 1) {\n                headers = Object.keys(row[1]).slice(0, -2);\n            } else {\n                headers = Object.keys(row[1]);\n            }\n            worksheet.addRow(headers);\n            for(let i = 1; i < row.length; i++){\n                if (index1 == 0 && i > 0) {\n                    supplierNames.push({\n                        supplierName: row[i][\"Supplier name\"],\n                        supplierCode: row[i][\"Supplier code\"]\n                    });\n                }\n                let rowData;\n                if (index1 == 1) {\n                    rowData = headers.map((header)=>row[i][header] || \"\").slice(0, -2);\n                } else {\n                    rowData = headers.map((header)=>row[i][header] || \"\");\n                }\n                worksheet.addRow(rowData);\n                if (row[i].isEmergencyAndFinanceNotComplete) {\n                    let isEmergencyAndFinanceNotCompleteSupplier = {\n                        isEmergencyAndFinanceNotComplete: row[i].isEmergencyAndFinanceNotComplete,\n                        supplierName: row[i][\"Supplier name\"]\n                    };\n                    isEmergencyAndFinanceNotCompleteObj.push(isEmergencyAndFinanceNotCompleteSupplier);\n                }\n                if (index1 == 1) {\n                    fetch(\"\".concat(serverAddress, \"suppliers/update-supplier/\").concat(row[i].id), {\n                        method: \"PUT\",\n                        headers: {\n                            Accept: \"application/json\",\n                            \"Content-Type\": \"application/json\"\n                        },\n                        credentials: \"include\",\n                        body: JSON.stringify({\n                            sectionName: \"updateStatus\",\n                            type: \"exportExcel\",\n                            status: 5,\n                            exported: true,\n                            updated_date: new Date().toISOString(),\n                            company_name: row[\"Supplier name\"],\n                            to: \"ISS\"\n                        })\n                    }).then((res)=>{\n                        if (res.status === 200) {\n                            return res.json();\n                        }\n                        // if (res.status === 401){\n                        //   toast.error(\"Your session has expired. Please log in again.\");\n                        //   setTimeout(() => {\n                        //     const redirectUrl = `/login?redirect=${encodeURIComponent(\n                        //       window.location.pathname\n                        //     )}`;\n                        //     logoutHandler(instance, redirectUrl);\n                        //   }, 3000);\n                        //   return null;\n                        // }\n                        return Promise.reject(res);\n                    }).then((json)=>{\n                        if (json.status == 200) {\n                            return true;\n                        }\n                    }).catch((error)=>{\n                        console.log(error);\n                    });\n                }\n            }\n        });\n    }\n    const buffer = await workbook.xlsx.writeBuffer();\n    const blob = new Blob([\n        buffer\n    ], {\n        type: \"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet\"\n    });\n    const blobUrl = URL.createObjectURL(blob);\n    const a = document.createElement(\"a\");\n    a.style.display = \"none\";\n    a.href = blobUrl;\n    const now = new Date();\n    const timestamp = \"\".concat(now.getFullYear(), \"-\").concat((now.getMonth() + 1).toString().padStart(2, \"0\"), \"-\").concat(now.getDate().toString().padStart(2, \"0\"), \"_\").concat(now.getHours().toString().padStart(2, \"0\"), \"-\").concat(now.getMinutes().toString().padStart(2, \"0\"), \"-\").concat(now.getSeconds().toString().padStart(2, \"0\"));\n    if (isInternal) {\n        a.download = \"internal_export_\".concat(timestamp, \"_\").concat(data[0].length - 1 === 1 ? \"S\" : \"G\", \".xlsx\");\n    } else if (isProductExtract) {\n        if (request_no && !varietyRequest) {\n            a.download = \"\".concat(request_no, \"_product_export.xlsx\");\n        } else if (request_no && varietyRequest) {\n            a.download = \"\".concat(request_no, \"_export.xlsx\");\n        } else {\n            a.download = \"product_export.xlsx\";\n        }\n    } else {\n        a.download = \"iss_export_\".concat(timestamp, \"_\").concat(data.length === 1 ? \"S\" : \"G\", \".xlsx\");\n    }\n    let fileName;\n    console.log(\"fle name\", fileName);\n    if (isInternal) {\n        fileName = \"internal_export_\".concat(timestamp, \"_\").concat(data[0].length - 1 === 1 ? \"S\" : \"G\", \".xlsx\");\n    } else if (isProductExtract && !varietyRequest) {\n        fileName = request_no ? \"\".concat(request_no, \"_product_export.xlsx\") : \"product_export.xlsx\";\n    } else if (varietyRequest) {\n        fileName = request_no ? \"\".concat(request_no, \"_NV_export.xlsx\") : \"NV_export.xlsx\";\n    } else {\n        fileName = \"iss_export_\".concat(timestamp, \"_\").concat(data.length === 1 ? \"S\" : \"G\", \".xlsx\");\n    }\n    console.log(\"fle name --- \\n\", fileName);\n    document.body.appendChild(a);\n    a.click();\n    // Clean up\n    URL.revokeObjectURL(blobUrl);\n    document.body.removeChild(a);\n    react_toastify__WEBPACK_IMPORTED_MODULE_1__.toast.success(\"Request details extracted successfully.\", {\n        toastId: 22,\n        position: \"top-right\",\n        autoClose: 3000,\n        pauseOnHover: false\n    });\n    if (!varietyRequest) {\n        const formData = new FormData();\n        formData.append(\"file\", blob, fileName);\n        formData.append(\"company\", company);\n        formData.append(\"prophet_id\", prophet_id);\n        formData.append(\"name\", userData === null || userData === void 0 ? void 0 : userData.name);\n        formData.append(\"isInternal\", isInternal);\n        formData.append(\"exporterEmail\", userData === null || userData === void 0 ? void 0 : userData.email);\n        formData.append(\"requestorEmail\", requestor_email);\n        formData.append(\"isProductRequest\", isProductExtract);\n        formData.append(\"supplierNames\", JSON.stringify(supplierNames));\n        formData.append(\"onProductSubmit\", onProductSubmit);\n        formData.append(\"request_no\", request_no);\n        formData.append(\"productEmailCommentPlaceholder\", productEmailCommentPlaceholder);\n        formData.append(\"productEmailParagraph\", productEmailParagraph);\n        const serializedData = JSON.stringify(isEmergencyAndFinanceNotCompleteObj);\n        formData.append(\"isEmergencyAndFinanceNotCompleteObj\", serializedData);\n        const response = await fetch(\"\".concat(serverAddress, \"email/send-email\"), {\n            method: \"POST\",\n            body: formData,\n            credentials: \"include\"\n        });\n        if (response.ok) {\n            react_toastify__WEBPACK_IMPORTED_MODULE_1__.toast.success(\"Email sent\", {\n                toastId: 22,\n                position: \"top-right\",\n                autoClose: 3000,\n                pauseOnHover: false\n            });\n        } else if (response.status === 401) {\n            // console.log(\"error YES 401\");\n            react_toastify__WEBPACK_IMPORTED_MODULE_1__.toast.error(\"Failed to send email\", {\n                toastId: 22,\n                position: \"top-right\",\n                autoClose: 3000,\n                pauseOnHover: false\n            });\n            return response.status;\n        } else {\n            react_toastify__WEBPACK_IMPORTED_MODULE_1__.toast.error(\"Failed to send email\", {\n                toastId: 22,\n                position: \"top-right\",\n                autoClose: 3000,\n                pauseOnHover: false\n            });\n        }\n    }\n    return true;\n};\n/* harmony default export */ __webpack_exports__[\"default\"] = (exportExcel);\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevExports = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevExports) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports on update so we can compare the boundary\n                // signatures.\n                module.hot.dispose(function (data) {\n                    data.prevExports = currentExports;\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevExports !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevExports, currentExports)) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevExports !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./utils/exportExcel.js\n"));

/***/ })

});