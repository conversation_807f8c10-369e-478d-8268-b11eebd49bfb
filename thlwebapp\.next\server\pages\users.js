/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "pages/users";
exports.ids = ["pages/users"];
exports.modules = {

/***/ "./public/images/dps-logo.png":
/*!************************************!*\
  !*** ./public/images/dps-logo.png ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ({\"src\":\"/_next/static/media/dps-logo.b393787d.png\",\"height\":70,\"width\":69,\"blurDataURL\":\"/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Fdps-logo.b393787d.png&w=8&q=70\",\"blurWidth\":8,\"blurHeight\":8});//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9wdWJsaWMvaW1hZ2VzL2Rwcy1sb2dvLnBuZyIsIm1hcHBpbmdzIjoiOzs7O0FBQUEsaUVBQWUsQ0FBQyxrTUFBa00iLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly90aGx3ZWJhcHAvLi9wdWJsaWMvaW1hZ2VzL2Rwcy1sb2dvLnBuZz8xYWFkIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IHtcInNyY1wiOlwiL19uZXh0L3N0YXRpYy9tZWRpYS9kcHMtbG9nby5iMzkzNzg3ZC5wbmdcIixcImhlaWdodFwiOjcwLFwid2lkdGhcIjo2OSxcImJsdXJEYXRhVVJMXCI6XCIvX25leHQvaW1hZ2U/dXJsPSUyRl9uZXh0JTJGc3RhdGljJTJGbWVkaWElMkZkcHMtbG9nby5iMzkzNzg3ZC5wbmcmdz04JnE9NzBcIixcImJsdXJXaWR0aFwiOjgsXCJibHVySGVpZ2h0XCI6OH07Il0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///./public/images/dps-logo.png\n");

/***/ }),

/***/ "./public/images/dps_ms_logo.png":
/*!***************************************!*\
  !*** ./public/images/dps_ms_logo.png ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ({\"src\":\"/_next/static/media/dps_ms_logo.51a82b5a.png\",\"height\":3543,\"width\":3543,\"blurDataURL\":\"/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Fdps_ms_logo.51a82b5a.png&w=8&q=70\",\"blurWidth\":8,\"blurHeight\":8});//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9wdWJsaWMvaW1hZ2VzL2Rwc19tc19sb2dvLnBuZyIsIm1hcHBpbmdzIjoiOzs7O0FBQUEsaUVBQWUsQ0FBQyw0TUFBNE0iLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly90aGx3ZWJhcHAvLi9wdWJsaWMvaW1hZ2VzL2Rwc19tc19sb2dvLnBuZz8yNjQyIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IHtcInNyY1wiOlwiL19uZXh0L3N0YXRpYy9tZWRpYS9kcHNfbXNfbG9nby41MWE4MmI1YS5wbmdcIixcImhlaWdodFwiOjM1NDMsXCJ3aWR0aFwiOjM1NDMsXCJibHVyRGF0YVVSTFwiOlwiL19uZXh0L2ltYWdlP3VybD0lMkZfbmV4dCUyRnN0YXRpYyUyRm1lZGlhJTJGZHBzX21zX2xvZ28uNTFhODJiNWEucG5nJnc9OCZxPTcwXCIsXCJibHVyV2lkdGhcIjo4LFwiYmx1ckhlaWdodFwiOjh9OyJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///./public/images/dps_ms_logo.png\n");

/***/ }),

/***/ "./public/images/efc_logo.jpg":
/*!************************************!*\
  !*** ./public/images/efc_logo.jpg ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ({\"src\":\"/_next/static/media/efc_logo.a9de8d40.jpg\",\"height\":495,\"width\":700,\"blurDataURL\":\"/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Fefc_logo.a9de8d40.jpg&w=8&q=70\",\"blurWidth\":8,\"blurHeight\":6});//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9wdWJsaWMvaW1hZ2VzL2VmY19sb2dvLmpwZyIsIm1hcHBpbmdzIjoiOzs7O0FBQUEsaUVBQWUsQ0FBQyxvTUFBb00iLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly90aGx3ZWJhcHAvLi9wdWJsaWMvaW1hZ2VzL2VmY19sb2dvLmpwZz9mYWFkIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IHtcInNyY1wiOlwiL19uZXh0L3N0YXRpYy9tZWRpYS9lZmNfbG9nby5hOWRlOGQ0MC5qcGdcIixcImhlaWdodFwiOjQ5NSxcIndpZHRoXCI6NzAwLFwiYmx1ckRhdGFVUkxcIjpcIi9fbmV4dC9pbWFnZT91cmw9JTJGX25leHQlMkZzdGF0aWMlMkZtZWRpYSUyRmVmY19sb2dvLmE5ZGU4ZDQwLmpwZyZ3PTgmcT03MFwiLFwiYmx1cldpZHRoXCI6OCxcImJsdXJIZWlnaHRcIjo2fTsiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///./public/images/efc_logo.jpg\n");

/***/ }),

/***/ "./public/images/fpp_logo.png":
/*!************************************!*\
  !*** ./public/images/fpp_logo.png ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ({\"src\":\"/_next/static/media/fpp_logo.c4b4812b.png\",\"height\":396,\"width\":335,\"blurDataURL\":\"/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Ffpp_logo.c4b4812b.png&w=7&q=70\",\"blurWidth\":7,\"blurHeight\":8});//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9wdWJsaWMvaW1hZ2VzL2ZwcF9sb2dvLnBuZyIsIm1hcHBpbmdzIjoiOzs7O0FBQUEsaUVBQWUsQ0FBQyxvTUFBb00iLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly90aGx3ZWJhcHAvLi9wdWJsaWMvaW1hZ2VzL2ZwcF9sb2dvLnBuZz9mYjllIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IHtcInNyY1wiOlwiL19uZXh0L3N0YXRpYy9tZWRpYS9mcHBfbG9nby5jNGI0ODEyYi5wbmdcIixcImhlaWdodFwiOjM5NixcIndpZHRoXCI6MzM1LFwiYmx1ckRhdGFVUkxcIjpcIi9fbmV4dC9pbWFnZT91cmw9JTJGX25leHQlMkZzdGF0aWMlMkZtZWRpYSUyRmZwcF9sb2dvLmM0YjQ4MTJiLnBuZyZ3PTcmcT03MFwiLFwiYmx1cldpZHRoXCI6NyxcImJsdXJIZWlnaHRcIjo4fTsiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///./public/images/fpp_logo.png\n");

/***/ }),

/***/ "./public/images/iss_logo.jpg":
/*!************************************!*\
  !*** ./public/images/iss_logo.jpg ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ({\"src\":\"/_next/static/media/iss_logo.2dbf60f2.jpg\",\"height\":551,\"width\":1021,\"blurDataURL\":\"/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Fiss_logo.2dbf60f2.jpg&w=8&q=70\",\"blurWidth\":8,\"blurHeight\":4});//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9wdWJsaWMvaW1hZ2VzL2lzc19sb2dvLmpwZyIsIm1hcHBpbmdzIjoiOzs7O0FBQUEsaUVBQWUsQ0FBQyxxTUFBcU0iLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly90aGx3ZWJhcHAvLi9wdWJsaWMvaW1hZ2VzL2lzc19sb2dvLmpwZz9iYjlhIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IHtcInNyY1wiOlwiL19uZXh0L3N0YXRpYy9tZWRpYS9pc3NfbG9nby4yZGJmNjBmMi5qcGdcIixcImhlaWdodFwiOjU1MSxcIndpZHRoXCI6MTAyMSxcImJsdXJEYXRhVVJMXCI6XCIvX25leHQvaW1hZ2U/dXJsPSUyRl9uZXh0JTJGc3RhdGljJTJGbWVkaWElMkZpc3NfbG9nby4yZGJmNjBmMi5qcGcmdz04JnE9NzBcIixcImJsdXJXaWR0aFwiOjgsXCJibHVySGVpZ2h0XCI6NH07Il0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///./public/images/iss_logo.jpg\n");

/***/ }),

/***/ "./public/images/logout-icon.png":
/*!***************************************!*\
  !*** ./public/images/logout-icon.png ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ({\"src\":\"/_next/static/media/logout-icon.4f82abe3.png\",\"height\":22,\"width\":25,\"blurDataURL\":\"/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Flogout-icon.4f82abe3.png&w=8&q=70\",\"blurWidth\":8,\"blurHeight\":7});//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9wdWJsaWMvaW1hZ2VzL2xvZ291dC1pY29uLnBuZyIsIm1hcHBpbmdzIjoiOzs7O0FBQUEsaUVBQWUsQ0FBQyx3TUFBd00iLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly90aGx3ZWJhcHAvLi9wdWJsaWMvaW1hZ2VzL2xvZ291dC1pY29uLnBuZz8xNzY4Il0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IHtcInNyY1wiOlwiL19uZXh0L3N0YXRpYy9tZWRpYS9sb2dvdXQtaWNvbi40ZjgyYWJlMy5wbmdcIixcImhlaWdodFwiOjIyLFwid2lkdGhcIjoyNSxcImJsdXJEYXRhVVJMXCI6XCIvX25leHQvaW1hZ2U/dXJsPSUyRl9uZXh0JTJGc3RhdGljJTJGbWVkaWElMkZsb2dvdXQtaWNvbi40ZjgyYWJlMy5wbmcmdz04JnE9NzBcIixcImJsdXJXaWR0aFwiOjgsXCJibHVySGVpZ2h0XCI6N307Il0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///./public/images/logout-icon.png\n");

/***/ }),

/***/ "./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2Fusers&preferredRegion=&absolutePagePath=.%2Fpages%5Cusers.js&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2Fusers&preferredRegion=&absolutePagePath=.%2Fpages%5Cusers.js&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   getServerSideProps: () => (/* binding */ getServerSideProps),\n/* harmony export */   getStaticPaths: () => (/* binding */ getStaticPaths),\n/* harmony export */   getStaticProps: () => (/* binding */ getStaticProps),\n/* harmony export */   reportWebVitals: () => (/* binding */ reportWebVitals),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   unstable_getServerProps: () => (/* binding */ unstable_getServerProps),\n/* harmony export */   unstable_getServerSideProps: () => (/* binding */ unstable_getServerSideProps),\n/* harmony export */   unstable_getStaticParams: () => (/* binding */ unstable_getStaticParams),\n/* harmony export */   unstable_getStaticPaths: () => (/* binding */ unstable_getStaticPaths),\n/* harmony export */   unstable_getStaticProps: () => (/* binding */ unstable_getStaticProps)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/pages/module.compiled */ \"./node_modules/next/dist/server/future/route-modules/pages/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/build/templates/helpers */ \"./node_modules/next/dist/build/templates/helpers.js\");\n/* harmony import */ var private_next_pages_document__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! private-next-pages/_document */ \"./pages/_document.js\");\n/* harmony import */ var private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! private-next-pages/_app */ \"./pages/_app.js\");\n/* harmony import */ var _pages_users_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./pages\\users.js */ \"./pages/users.js\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__, _pages_users_js__WEBPACK_IMPORTED_MODULE_5__]);\n([private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__, _pages_users_js__WEBPACK_IMPORTED_MODULE_5__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n// @ts-ignore this need to be imported from next/dist to be external\n\n\n\n// Import the app and document modules.\n// @ts-expect-error - replaced by webpack/turbopack loader\n\n// @ts-expect-error - replaced by webpack/turbopack loader\n\n// Import the userland code.\n// @ts-expect-error - replaced by webpack/turbopack loader\n\nconst PagesRouteModule = next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__.PagesRouteModule;\n// Re-export the component (should be the default export).\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_users_js__WEBPACK_IMPORTED_MODULE_5__, \"default\"));\n// Re-export methods.\nconst getStaticProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_users_js__WEBPACK_IMPORTED_MODULE_5__, \"getStaticProps\");\nconst getStaticPaths = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_users_js__WEBPACK_IMPORTED_MODULE_5__, \"getStaticPaths\");\nconst getServerSideProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_users_js__WEBPACK_IMPORTED_MODULE_5__, \"getServerSideProps\");\nconst config = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_users_js__WEBPACK_IMPORTED_MODULE_5__, \"config\");\nconst reportWebVitals = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_users_js__WEBPACK_IMPORTED_MODULE_5__, \"reportWebVitals\");\n// Re-export legacy methods.\nconst unstable_getStaticProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_users_js__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getStaticProps\");\nconst unstable_getStaticPaths = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_users_js__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getStaticPaths\");\nconst unstable_getStaticParams = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_users_js__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getStaticParams\");\nconst unstable_getServerProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_users_js__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getServerProps\");\nconst unstable_getServerSideProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_users_js__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getServerSideProps\");\n// Create and export the route module that will be consumed.\nconst routeModule = new PagesRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.PAGES,\n        page: \"/users\",\n        pathname: \"/users\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\"\n    },\n    components: {\n        App: private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n        Document: private_next_pages_document__WEBPACK_IMPORTED_MODULE_3__[\"default\"]\n    },\n    userland: _pages_users_js__WEBPACK_IMPORTED_MODULE_5__\n});\n\n//# sourceMappingURL=pages.js.map\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2Fusers&preferredRegion=&absolutePagePath=.%2Fpages%5Cusers.js&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!\n");

/***/ }),

/***/ "./components/Layout.js":
/*!******************************!*\
  !*** ./components/Layout.js ***!
  \******************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _Navbar__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./Navbar */ \"./components/Navbar.js\");\n/* harmony import */ var _Sidebar__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./Sidebar */ \"./components/Sidebar.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/navigation */ \"./node_modules/next/navigation.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(next_navigation__WEBPACK_IMPORTED_MODULE_5__);\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_Navbar__WEBPACK_IMPORTED_MODULE_2__, _Sidebar__WEBPACK_IMPORTED_MODULE_3__]);\n([_Navbar__WEBPACK_IMPORTED_MODULE_2__, _Sidebar__WEBPACK_IMPORTED_MODULE_3__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\n\n\nconst Layout = ({ children, userData, blockScreen })=>{\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_4__.useRouter)();\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_5__.usePathname)();\n    const [showDesktopViewMessage, setShowDesktopViewMessage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Redirect to login if no userData\n        if (!userData) {\n            router.push(\"/login\");\n            return;\n        }\n        const handleResize = ()=>{\n            if (window.innerWidth <= 767) {\n                setShowDesktopViewMessage(true);\n            } else {\n                setShowDesktopViewMessage(false);\n            }\n        };\n        window.addEventListener(\"resize\", handleResize);\n        handleResize();\n        return ()=>window.removeEventListener(\"resize\", handleResize);\n    }, [\n        userData,\n        router\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        id: \"wrapper\",\n        children: showDesktopViewMessage ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"desktop-view-message\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"message-content\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        children: \"Please Open in Desktop View\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\Layout.js\",\n                        lineNumber: 38,\n                        columnNumber: 13\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        children: \"This website is best viewed on a desktop or laptop.\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\Layout.js\",\n                        lineNumber: 39,\n                        columnNumber: 13\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\Layout.js\",\n                lineNumber: 37,\n                columnNumber: 11\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\Layout.js\",\n            lineNumber: 36,\n            columnNumber: 9\n        }, undefined) : blockScreen ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"block-view-message\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"message-content\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            children: \"Service Unavailable\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\Layout.js\",\n                            lineNumber: 46,\n                            columnNumber: 15\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            children: \"We are currently experiencing issues and are working to resolve them as quickly as possible. Please check back later.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\Layout.js\",\n                            lineNumber: 47,\n                            columnNumber: 15\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\Layout.js\",\n                    lineNumber: 45,\n                    columnNumber: 13\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\Layout.js\",\n                lineNumber: 44,\n                columnNumber: 11\n            }, undefined)\n        }, void 0, false) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Sidebar__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                    userData: userData\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\Layout.js\",\n                    lineNumber: 56,\n                    columnNumber: 11\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"page-container\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Navbar__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                            userData: userData\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\Layout.js\",\n                            lineNumber: 58,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"main-root\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: `relative md:ml-[55px] lg:ml-[60px] py-2 2xl:h-[calc(100%-60px)] ${pathname == \"/whatif\" || pathname == \"/service_level\" ? \"w-[100%-70px] px-0 pl-3 mt-[45px]\" : \"w-full px-8 mt-[60px]\"}`,\n                                children: children\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\Layout.js\",\n                                lineNumber: 60,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\Layout.js\",\n                            lineNumber: 59,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\Layout.js\",\n                    lineNumber: 57,\n                    columnNumber: 11\n                }, undefined)\n            ]\n        }, void 0, true)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\Layout.js\",\n        lineNumber: 34,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Layout);\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/Layout.js\n");

/***/ }),

/***/ "./components/Navbar.js":
/*!******************************!*\
  !*** ./components/Navbar.js ***!
  \******************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   themes: () => (/* binding */ themes)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _fortawesome_react_fontawesome__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @fortawesome/react-fontawesome */ \"@fortawesome/react-fontawesome\");\n/* harmony import */ var _fortawesome_react_fontawesome__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_fortawesome_react_fontawesome__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _fortawesome_free_solid_svg_icons__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @fortawesome/free-solid-svg-icons */ \"@fortawesome/free-solid-svg-icons\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/link */ \"./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var _azure_msal_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @azure/msal-react */ \"@azure/msal-react\");\n/* harmony import */ var js_cookie__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! js-cookie */ \"js-cookie\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_fortawesome_free_solid_svg_icons__WEBPACK_IMPORTED_MODULE_3__, _azure_msal_react__WEBPACK_IMPORTED_MODULE_6__, js_cookie__WEBPACK_IMPORTED_MODULE_7__]);\n([_fortawesome_free_solid_svg_icons__WEBPACK_IMPORTED_MODULE_3__, _azure_msal_react__WEBPACK_IMPORTED_MODULE_6__, js_cookie__WEBPACK_IMPORTED_MODULE_7__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\n\n\n\n\nconst themes = [\n    {\n        bg: \"#022d71\",\n        text: \"#022d71\",\n        name: \"DPS\"\n    },\n    {\n        bg: \"#2e9b28\",\n        text: \"#2e9b28\",\n        name: \"EFC\"\n    },\n    {\n        bg: \"#a91e23\",\n        text: \"#a91e23\",\n        name: \"M&S\"\n    },\n    {\n        bg: \"#3d6546\",\n        text: \"#3d6546\",\n        name: \"FPP\"\n    }\n];\nconst Navbar = ({ userData })=>{\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_5__.useRouter)();\n    const [pageName, setPageName] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [currentRoute, setCurrentRoute] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [isUpdating, setIsUpdating] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const currentPath = router.pathname;\n        setCurrentRoute(currentPath);\n        if (currentPath === \"/finished-product-request/add\") {\n            js_cookie__WEBPACK_IMPORTED_MODULE_7__[\"default\"].set(\"PreviousPage\", \"FG\");\n            setPageName(\"Finished Goods Request\");\n        } else if (currentPath === \"/raw-material-request/add\") {\n            setPageName(\"Raw Material Request\");\n            js_cookie__WEBPACK_IMPORTED_MODULE_7__[\"default\"].set(\"PreviousPage\", \"RM\");\n        } else if (currentPath === \"/packaging-form/add\") {\n            setPageName(\"Packaging Request\");\n        } else if (currentPath === \"/variety/add\") {\n            js_cookie__WEBPACK_IMPORTED_MODULE_7__[\"default\"].set(\"PreviousPage\", \"NV\");\n            setPageName(\"New Variety Request\");\n        } else if (currentPath === \"/variety/[productId]/edit\") {\n            js_cookie__WEBPACK_IMPORTED_MODULE_7__[\"default\"].set(\"PreviousPage\", \"NV\");\n            setPageName(\"Edit New Variety Request\");\n        } else if (currentPath === \"/packaging-form/[productId]/edit\") {\n            js_cookie__WEBPACK_IMPORTED_MODULE_7__[\"default\"].set(\"PreviousPage\", \"PK\");\n            setPageName(\"Edit Packaging Request\");\n        } else if (currentPath === \"/raw-material-request/[productId]/edit\") {\n            js_cookie__WEBPACK_IMPORTED_MODULE_7__[\"default\"].set(\"PreviousPage\", \"RM\");\n            setPageName(\"Edit Raw Material Request\");\n        } else if (currentPath === \"/finished-product-request/[productId]/edit\") {\n            js_cookie__WEBPACK_IMPORTED_MODULE_7__[\"default\"].set(\"PreviousPage\", \"FG\");\n            setPageName(\"Edit Finished Goods Request\");\n        } else if (currentPath?.endsWith(\"/edit\")) {\n            setPageName(`Edit Supplier`);\n        } else if (currentPath?.endsWith(\"/add\")) {\n            setPageName(\"Add Supplier\");\n        } else if (currentPath?.endsWith(\"/edit/forms\")) {\n            setPageName(`Supplier Form`);\n        } else if (currentPath?.endsWith(\"/confirm\")) {\n            setPageName(`Confirm Details for Supplier`);\n        } else if (currentPath === \"/suppliers\") {\n            setPageName(\"Suppliers\");\n        } else if (currentPath === \"/users\") {\n            setPageName(\"User Management\");\n        } else if (currentPath === \"/viewlogs\") {\n            setPageName(\"View Logs\");\n        } else if (currentPath === \"/products\") {\n            js_cookie__WEBPACK_IMPORTED_MODULE_7__[\"default\"].set(\"PreviousPage\", \"RM\");\n            setPageName(\"Products\");\n        } else if (currentPath === \"/variety\") {\n            setPageName(\"Variety\");\n            js_cookie__WEBPACK_IMPORTED_MODULE_7__[\"default\"].set(\"PreviousPage\", \"NV\");\n        } else if (currentPath === \"/finishedProductRequest\") {\n            js_cookie__WEBPACK_IMPORTED_MODULE_7__[\"default\"].set(\"PreviousPage\", \"FG\");\n            setPageName(\"Finished Product Request\");\n        } else if (currentPath === \"/rawMaterialRequest\") {\n            js_cookie__WEBPACK_IMPORTED_MODULE_7__[\"default\"].set(\"PreviousPage\", \"RM\");\n            setPageName(\"Raw Material Request\");\n        } else if (currentPath === \"/whatif\") {\n            setPageName(\"Whatif\");\n        } else if (currentPath === \"/service_level\" || currentPath === \"/service_level/reports/masterForcast\") {\n            setPageName(\"Service Level\");\n        }\n    }, [\n        router.pathname\n    ]);\n    const baseCompanyOptions = [\n        {\n            value: \"dpsltd\",\n            label: \"DPS\"\n        },\n        {\n            value: \"dpsltdms\",\n            label: \"DPS M&S\"\n        },\n        {\n            value: \"efcltd\",\n            label: \"EFC\"\n        },\n        {\n            value: \"fpp-ltd\",\n            label: \"FPP\"\n        }\n    ];\n    const companyOptions = [\n        ...baseCompanyOptions,\n        ...userData?.role_id === 6 ? [\n            {\n                value: \"issproduce\",\n                label: \"ISS\"\n            },\n            {\n                value: \"flrs\",\n                label: \"FLRS\",\n                disabled: true\n            },\n            {\n                value: \"thl\",\n                label: \"THL\",\n                disabled: true\n            }\n        ] : []\n    ];\n    const [selectedCompany, setSelectedCompany] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(userData?.ADCompanyName === \"DPS MS\" ? \"dpsltdms\" : userData?.company || \"\");\n    const handleCompanyChange = async (event)=>{\n        const company = event.target.value;\n        if (isUpdating) return; // Prevent multiple rapid clicks\n        setIsUpdating(true);\n        try {\n            console.log(\"Updating company to:\", company);\n            const apiBase = \"http://localhost:8081\" || 0;\n            // Call the API to update company in session\n            const response = await fetch(`${apiBase}/api/auth/update-company`, {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                credentials: \"include\",\n                body: JSON.stringify({\n                    company\n                })\n            });\n            if (!response.ok) {\n                const errorData = await response.json();\n                throw new Error(errorData.error || \"Failed to update company\");\n            }\n            const result = await response.json();\n            console.log(\"Company updated successfully:\", result);\n            // Update local state\n            setSelectedCompany(company);\n            // Reload the page to reflect changes throughout the application\n            router.reload();\n        } catch (error) {\n            console.error(\"Error updating company:\", error);\n            alert(\"Failed to update company. Please try again.\");\n            // Reset the select to previous value on error\n            event.target.value = selectedCompany;\n        } finally{\n            setIsUpdating(false);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"titlebar\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex flex-row justify-between w-full bg-skin-primary h-14\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-row w-full items-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"page-heading cursor-pointer\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fortawesome_react_fontawesome__WEBPACK_IMPORTED_MODULE_2__.FontAwesomeIcon, {\n                                    icon: _fortawesome_free_solid_svg_icons__WEBPACK_IMPORTED_MODULE_3__.faChevronLeft,\n                                    className: \"pageName text-white\",\n                                    onClick: ()=>router.back()\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\Navbar.js\",\n                                    lineNumber: 159,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_4___default()), {\n                                    href: currentRoute,\n                                    className: \"ml-4 2xl:text-lg font-poppinssemibold pageName text-white tracking-wide\",\n                                    children: pageName\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\Navbar.js\",\n                                    lineNumber: 164,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\Navbar.js\",\n                            lineNumber: 158,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\Navbar.js\",\n                        lineNumber: 157,\n                        columnNumber: 11\n                    }, undefined),\n                    (userData?.role_id == 5 || userData?.role_id == 6) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-row justify-end w-1/2 items-center mr-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                value: selectedCompany,\n                                onChange: handleCompanyChange,\n                                disabled: isUpdating,\n                                className: \"bg-white text-black rounded disabled:opacity-50\",\n                                children: companyOptions.map((opt)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: opt.value,\n                                        disabled: opt.disabled,\n                                        children: opt.label\n                                    }, opt.value, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\Navbar.js\",\n                                        lineNumber: 181,\n                                        columnNumber: 19\n                                    }, undefined))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\Navbar.js\",\n                                lineNumber: 174,\n                                columnNumber: 15\n                            }, undefined),\n                            isUpdating && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"ml-2 text-white text-sm\",\n                                children: \"Updating...\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\Navbar.js\",\n                                lineNumber: 191,\n                                columnNumber: 17\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\Navbar.js\",\n                        lineNumber: 173,\n                        columnNumber: 13\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\Navbar.js\",\n                lineNumber: 156,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\Navbar.js\",\n            lineNumber: 155,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\Navbar.js\",\n        lineNumber: 154,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Navbar);\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/Navbar.js\n");

/***/ }),

/***/ "./components/SideBarLinks.js":
/*!************************************!*\
  !*** ./components/SideBarLinks.js ***!
  \************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ SideBarLinks)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _fortawesome_free_regular_svg_icons__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @fortawesome/free-regular-svg-icons */ \"@fortawesome/free-regular-svg-icons\");\n/* harmony import */ var _fortawesome_free_solid_svg_icons__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @fortawesome/free-solid-svg-icons */ \"@fortawesome/free-solid-svg-icons\");\n/* harmony import */ var _fortawesome_react_fontawesome__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @fortawesome/react-fontawesome */ \"@fortawesome/react-fontawesome\");\n/* harmony import */ var _fortawesome_react_fontawesome__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(_fortawesome_react_fontawesome__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/link */ \"./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var _utils_loaders_loadingContext__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/utils/loaders/loadingContext */ \"./utils/loaders/loadingContext.js\");\n/* harmony import */ var _utils_securePermissions__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/utils/securePermissions */ \"./utils/securePermissions.js\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_fortawesome_free_regular_svg_icons__WEBPACK_IMPORTED_MODULE_1__, _fortawesome_free_solid_svg_icons__WEBPACK_IMPORTED_MODULE_2__]);\n([_fortawesome_free_regular_svg_icons__WEBPACK_IMPORTED_MODULE_1__, _fortawesome_free_solid_svg_icons__WEBPACK_IMPORTED_MODULE_2__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n\n\n\nfunction SideBarLinks({ userData, currentPathname, company, ADCompany }) {\n    const { setIsLoading } = (0,_utils_loaders_loadingContext__WEBPACK_IMPORTED_MODULE_6__.useLoading)();\n    const { permissions, isLoading: permissionsLoading } = (0,_utils_securePermissions__WEBPACK_IMPORTED_MODULE_7__.useSecurePermissions)(userData);\n    // Helper function to determine active state\n    const isActive = (path)=>{\n        return currentPathname?.startsWith(path);\n    };\n    const handleLinkClick = (e, path)=>{\n        if (isActive(path)) {\n            e.preventDefault();\n            window.location.reload();\n        } else {\n            setIsLoading(true);\n        }\n    };\n    if (permissionsLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            children: \"Loading permissions...\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\SideBarLinks.js\",\n            lineNumber: 48,\n            columnNumber: 12\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n        className: \"nav navbar-nav text-center\",\n        children: [\n            permissions.canViewSuppliers && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                className: \"flex justify-center mb-3\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_4___default()), {\n                    href: \"/suppliers\",\n                    title: \"Suppliers\",\n                    className: `!px-4 !py-3 bg-white rounded-md text-center ${isActive(\"/supplier\") ? \"opacity-100\" : \"opacity-70\"}`,\n                    onClick: (e)=>handleLinkClick(e, \"/suppliers\"),\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fortawesome_react_fontawesome__WEBPACK_IMPORTED_MODULE_3__.FontAwesomeIcon, {\n                        icon: _fortawesome_free_solid_svg_icons__WEBPACK_IMPORTED_MODULE_2__.faTruck,\n                        size: \"lg\",\n                        className: \"text-skin-primary\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\SideBarLinks.js\",\n                        lineNumber: 64,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\SideBarLinks.js\",\n                    lineNumber: 56,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\SideBarLinks.js\",\n                lineNumber: 55,\n                columnNumber: 9\n            }, this),\n            permissions.canViewProducts && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                className: \"flex justify-center mb-3\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_4___default()), {\n                    href: \"/products\",\n                    title: \"Products\",\n                    className: `!px-4 !py-3 bg-white rounded-md text-center ${isActive(\"/products\") ? \"opacity-100\" : \"opacity-70\"}`,\n                    onClick: (e)=>handleLinkClick(e, \"/products\"),\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fortawesome_react_fontawesome__WEBPACK_IMPORTED_MODULE_3__.FontAwesomeIcon, {\n                        icon: _fortawesome_free_solid_svg_icons__WEBPACK_IMPORTED_MODULE_2__.faBoxArchive,\n                        size: \"lg\",\n                        className: \"text-skin-primary\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\SideBarLinks.js\",\n                        lineNumber: 84,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\SideBarLinks.js\",\n                    lineNumber: 76,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\SideBarLinks.js\",\n                lineNumber: 75,\n                columnNumber: 9\n            }, this),\n            permissions.canViewWhatif && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                className: \"flex justify-center mb-3\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_4___default()), {\n                    href: \"/whatif\",\n                    title: \"What if\",\n                    className: `!px-4 !py-3 bg-white rounded-md text-center ${isActive(\"/whatif\") ? \"opacity-100\" : \"opacity-70\"}`,\n                    onClick: (e)=>handleLinkClick(e, \"/whatif\"),\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fortawesome_react_fontawesome__WEBPACK_IMPORTED_MODULE_3__.FontAwesomeIcon, {\n                        icon: _fortawesome_free_solid_svg_icons__WEBPACK_IMPORTED_MODULE_2__.faChartLine,\n                        size: \"lg\",\n                        className: \"text-skin-primary\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\SideBarLinks.js\",\n                        lineNumber: 104,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\SideBarLinks.js\",\n                    lineNumber: 96,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\SideBarLinks.js\",\n                lineNumber: 95,\n                columnNumber: 9\n            }, this),\n            permissions.canViewServiceLevel && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                className: \"flex justify-center mb-3\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_4___default()), {\n                    href: \"/service_level\",\n                    title: \"Service Level\",\n                    className: `!px-4 !py-3 bg-white rounded-md text-center ${isActive(\"/service_level\") ? \"opacity-100\" : \"opacity-70\"}`,\n                    onClick: (e)=>handleLinkClick(e, \"/service_level\"),\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fortawesome_react_fontawesome__WEBPACK_IMPORTED_MODULE_3__.FontAwesomeIcon, {\n                        icon: _fortawesome_free_solid_svg_icons__WEBPACK_IMPORTED_MODULE_2__.faFileCircleQuestion,\n                        size: \"lg\",\n                        className: \"text-skin-primary\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\SideBarLinks.js\",\n                        lineNumber: 124,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\SideBarLinks.js\",\n                    lineNumber: 116,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\SideBarLinks.js\",\n                lineNumber: 115,\n                columnNumber: 9\n            }, this),\n            permissions.canViewUsers && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                className: \"flex justify-center mb-3\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_4___default()), {\n                    href: \"/users\",\n                    title: \"Users\",\n                    className: `!px-4 !py-3 bg-white rounded-md text-center ${isActive(\"/users\") ? \"opacity-100\" : \"opacity-70\"}`,\n                    onClick: (e)=>handleLinkClick(e, \"/users\"),\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fortawesome_react_fontawesome__WEBPACK_IMPORTED_MODULE_3__.FontAwesomeIcon, {\n                        icon: _fortawesome_free_solid_svg_icons__WEBPACK_IMPORTED_MODULE_2__.faUsersLine,\n                        size: \"lg\",\n                        className: \"text-skin-primary\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\SideBarLinks.js\",\n                        lineNumber: 144,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\SideBarLinks.js\",\n                    lineNumber: 136,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\SideBarLinks.js\",\n                lineNumber: 135,\n                columnNumber: 9\n            }, this),\n            permissions.canViewLogs && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                className: \"flex justify-center mb-3\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_4___default()), {\n                    href: \"/viewlogs\",\n                    title: \"Logs\",\n                    className: `!px-4 !py-3 bg-white rounded-md text-center ${isActive(\"/viewlogs\") ? \"opacity-100\" : \"opacity-70\"}`,\n                    onClick: (e)=>handleLinkClick(e, \"/viewlogs\"),\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fortawesome_react_fontawesome__WEBPACK_IMPORTED_MODULE_3__.FontAwesomeIcon, {\n                        icon: _fortawesome_free_solid_svg_icons__WEBPACK_IMPORTED_MODULE_2__.faList,\n                        size: \"lg\",\n                        className: \"text-skin-primary\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\SideBarLinks.js\",\n                        lineNumber: 164,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\SideBarLinks.js\",\n                    lineNumber: 156,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\SideBarLinks.js\",\n                lineNumber: 155,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\SideBarLinks.js\",\n        lineNumber: 52,\n        columnNumber: 5\n    }, this);\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/SideBarLinks.js\n");

/***/ }),

/***/ "./components/Sidebar.js":
/*!*******************************!*\
  !*** ./components/Sidebar.js ***!
  \*******************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Sidebar)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _public_images_dps_logo_png__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../public/images/dps-logo.png */ \"./public/images/dps-logo.png\");\n/* harmony import */ var _public_images_efc_logo_jpg__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../public/images/efc_logo.jpg */ \"./public/images/efc_logo.jpg\");\n/* harmony import */ var _public_images_dps_ms_logo_png__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../public/images/dps_ms_logo.png */ \"./public/images/dps_ms_logo.png\");\n/* harmony import */ var _public_images_fpp_logo_png__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../public/images/fpp_logo.png */ \"./public/images/fpp_logo.png\");\n/* harmony import */ var _public_images_iss_logo_jpg__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../public/images/iss_logo.jpg */ \"./public/images/iss_logo.jpg\");\n/* harmony import */ var _public_images_logout_icon_png__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ../public/images/logout-icon.png */ \"./public/images/logout-icon.png\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! next/link */ \"./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_9___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_9__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! next/image */ \"./node_modules/next/image.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_10___default = /*#__PURE__*/__webpack_require__.n(next_image__WEBPACK_IMPORTED_MODULE_10__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! next/navigation */ \"./node_modules/next/navigation.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_11___default = /*#__PURE__*/__webpack_require__.n(next_navigation__WEBPACK_IMPORTED_MODULE_11__);\n/* harmony import */ var _azure_msal_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @azure/msal-react */ \"@azure/msal-react\");\n/* harmony import */ var react_loader_spinner__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! react-loader-spinner */ \"react-loader-spinner\");\n/* harmony import */ var react_loader_spinner__WEBPACK_IMPORTED_MODULE_13___default = /*#__PURE__*/__webpack_require__.n(react_loader_spinner__WEBPACK_IMPORTED_MODULE_13__);\n/* harmony import */ var _SideBarLinks__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! ./SideBarLinks */ \"./components/SideBarLinks.js\");\n/* harmony import */ var _utils_auth_useSecureAuth__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @/utils/auth/useSecureAuth */ \"./utils/auth/useSecureAuth.js\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_azure_msal_react__WEBPACK_IMPORTED_MODULE_12__, _SideBarLinks__WEBPACK_IMPORTED_MODULE_14__, _utils_auth_useSecureAuth__WEBPACK_IMPORTED_MODULE_15__]);\n([_azure_msal_react__WEBPACK_IMPORTED_MODULE_12__, _SideBarLinks__WEBPACK_IMPORTED_MODULE_14__, _utils_auth_useSecureAuth__WEBPACK_IMPORTED_MODULE_15__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction Sidebar({ userData: propUserData }) {\n    const [userData, setUserData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [company, setCompany] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [ADCompany, setADCompany] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const { secureLogout } = (0,_utils_auth_useSecureAuth__WEBPACK_IMPORTED_MODULE_15__.useSecureAuth)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!propUserData) {\n            // If no userData provided, redirect to login\n            window.location.href = \"/login\";\n            return;\n        }\n        setUserData(propUserData);\n        setCompany(propUserData.company);\n        setADCompany(propUserData.ADCompanyName || propUserData.companyName);\n    }, [\n        propUserData\n    ]);\n    const { instance } = (0,_azure_msal_react__WEBPACK_IMPORTED_MODULE_12__.useMsal)();\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const currentPathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_11__.usePathname)();\n    const getLogo = (company)=>{\n        if (!company) return _public_images_dps_logo_png__WEBPACK_IMPORTED_MODULE_3__[\"default\"]; // Default fallback\n        switch(company){\n            case \"dpsltd\":\n                return _public_images_dps_logo_png__WEBPACK_IMPORTED_MODULE_3__[\"default\"];\n            case \"DPS MS\":\n                return _public_images_dps_ms_logo_png__WEBPACK_IMPORTED_MODULE_5__[\"default\"];\n            case \"efcltd\":\n                return _public_images_efc_logo_jpg__WEBPACK_IMPORTED_MODULE_4__[\"default\"];\n            case \"fpp-ltd\":\n                return _public_images_fpp_logo_png__WEBPACK_IMPORTED_MODULE_6__[\"default\"];\n            case \"thl\":\n                return _public_images_efc_logo_jpg__WEBPACK_IMPORTED_MODULE_4__[\"default\"];\n            case \"issproduce\":\n                return _public_images_iss_logo_jpg__WEBPACK_IMPORTED_MODULE_7__[\"default\"];\n            case \"flrs\":\n                return _public_images_efc_logo_jpg__WEBPACK_IMPORTED_MODULE_4__[\"default\"];\n            default:\n                return _public_images_dps_logo_png__WEBPACK_IMPORTED_MODULE_3__[\"default\"]; // Default fallback\n        }\n    };\n    const getLogoSizeClass = (company)=>{\n        if (!company) return \"h-14 w-100\";\n        switch(company){\n            case \"dpsltd\":\n                return \"!h-16 !w-auto\";\n            default:\n                return \"h-14 w-100\";\n        }\n    };\n    const handleLogout = async ()=>{\n        try {\n            await secureLogout();\n        } catch (error) {\n            console.error(\"Secure logout error:\", error);\n            // Force redirect even if logout fails\n            window.location.href = \"/login\";\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n            id: \"sidemenu\",\n            className: \"navbar navbar-default sidebar bg-skin-primary\",\n            role: \"navigation\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"container-fluid h-full\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_9___default()), {\n                        href: \"/suppliers\",\n                        title: \"Home\",\n                        className: \"z-50\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"brand\",\n                            children: company ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_10___default()), {\n                                src: getLogo(company == \"dpsltd\" && ADCompany == \"DPS MS\" ? \"DPS MS\" : company),\n                                alt: \"logo\",\n                                className: getLogoSizeClass(company == \"dpsltd\" && ADCompany == \"DPS MS\" ? \"DPS MS\" : company)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\Sidebar.js\",\n                                lineNumber: 95,\n                                columnNumber: 17\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_loader_spinner__WEBPACK_IMPORTED_MODULE_13__.Oval, {\n                                    color: \"#002D73\",\n                                    height: 20,\n                                    width: 20,\n                                    visible: true,\n                                    ariaLabel: \"oval-loading\",\n                                    secondaryColor: \"#0066FF\",\n                                    strokeWidth: 2,\n                                    strokeWidthSecondary: 2\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\Sidebar.js\",\n                                    lineNumber: 110,\n                                    columnNumber: 19\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\Sidebar.js\",\n                                lineNumber: 109,\n                                columnNumber: 17\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\Sidebar.js\",\n                            lineNumber: 93,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\Sidebar.js\",\n                        lineNumber: 92,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        id: \"bs-sidebar-navbar-collapse-1\",\n                        className: \"pt-10 w-100 text-center flex flex-col justify-between flex-end items-stretch h-full\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col w-full\",\n                                children: userData && company && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_SideBarLinks__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                    userData: userData,\n                                    currentPathname: currentPathname,\n                                    company: company,\n                                    ADCompany: ADCompany\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\Sidebar.js\",\n                                    lineNumber: 130,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\Sidebar.js\",\n                                lineNumber: 128,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-center flex-col items-center gap-4 my-4 mb-20 cursor-pointer\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        title: \"IT Training Material\",\n                                        href: `${process.env.NEXT_PUBLIC_TRAINING_MATERIAL}`,\n                                        target: \"_blank\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            xmlns: \"http://www.w3.org/2000/svg\",\n                                            viewBox: \"0 0 448 512\",\n                                            fill: \"#FFFF\",\n                                            className: \"w-6\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                d: \"M0 88C0 39.4 39.4 0 88 0L392 0c30.9 0 56 25.1 56 56l0 288c0 22.3-13.1 41.6-32 50.6l0 69.4 8 0c13.3 0 24 10.7 24 24s-10.7 24-24 24L80 512c-44.2 0-80-35.8-80-80c0-2.7 .1-5.4 .4-8L0 424 0 88zM80 400c-17.7 0-32 14.3-32 32s14.3 32 32 32l288 0 0-64L80 400zM48 358.7c9.8-4.3 20.6-6.7 32-6.7l312 0c4.4 0 8-3.6 8-8l0-288c0-4.4-3.6-8-8-8L88 48C65.9 48 48 65.9 48 88l0 270.7zM160 112l8.8-17.7c2.9-5.9 11.4-5.9 14.3 0L192 112l17.7 8.8c5.9 2.9 5.9 11.4 0 14.3L192 144l-8.8 17.7c-2.9 5.9-11.4 5.9-14.3 0L160 144l-17.7-8.8c-5.9-2.9-5.9-11.4 0-14.3L160 112zM264 216l16.6-38.8c2.8-6.5 11.9-6.5 14.7 0L312 216l38.8 16.6c6.5 2.8 6.5 11.9 0 14.7L312 264l-16.6 38.8c-2.8 6.5-11.9 6.5-14.7 0L264 264l-38.8-16.6c-6.5-2.8-6.5-11.9 0-14.7L264 216z\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\Sidebar.js\",\n                                                lineNumber: 150,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\Sidebar.js\",\n                                            lineNumber: 144,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\Sidebar.js\",\n                                        lineNumber: 139,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        title: \"Logout\",\n                                        onClick: handleLogout,\n                                        className: \"cursor-pointer\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_10___default()), {\n                                            src: _public_images_logout_icon_png__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n                                            alt: \"logout\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\Sidebar.js\",\n                                            lineNumber: 158,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\Sidebar.js\",\n                                        lineNumber: 153,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\Sidebar.js\",\n                                lineNumber: 138,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\Sidebar.js\",\n                        lineNumber: 124,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\Sidebar.js\",\n                lineNumber: 91,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\Sidebar.js\",\n            lineNumber: 86,\n            columnNumber: 7\n        }, this)\n    }, void 0, false);\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/Sidebar.js\n");

/***/ }),

/***/ "./pages/_app.js":
/*!***********************!*\
  !*** ./pages/_app.js ***!
  \***********************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ App)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _styles_globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/styles/globals.css */ \"./styles/globals.css\");\n/* harmony import */ var _styles_globals_css__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_styles_globals_css__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _utils_auth_msalProvider__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/utils/auth/msalProvider */ \"./utils/auth/msalProvider.jsx\");\n/* harmony import */ var _utils_loaders_loadingContext__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/utils/loaders/loadingContext */ \"./utils/loaders/loadingContext.js\");\n/* harmony import */ var _utils_loaders_overlaySpinner__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/utils/loaders/overlaySpinner */ \"./utils/loaders/overlaySpinner.js\");\n/* harmony import */ var _utils_secureThemeContext__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/utils/secureThemeContext */ \"./utils/secureThemeContext.js\");\n/* harmony import */ var _utils_rolePermissionsContext__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/utils/rolePermissionsContext */ \"./utils/rolePermissionsContext.js\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_utils_auth_msalProvider__WEBPACK_IMPORTED_MODULE_3__]);\n_utils_auth_msalProvider__WEBPACK_IMPORTED_MODULE_3__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\n\n\n\n\n\nfunction App({ Component, pageProps }) {\n    const [isOpen, setIsOpen] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(true);\n    const isOnline = useNetwork();\n    function useNetwork() {\n        if (false) {}\n    }\n    ;\n    const closeModal = ()=>{\n        setIsOpen(false);\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        if (isOnline) {\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                classNam: \"no-connection\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        children: \"No Internet Connection\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\_app.js\",\n                        lineNumber: 36,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"noConnectionAlertBox\", {\n                        isOpen: isOpen,\n                        closeModal: closeModal\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\_app.js\",\n                        lineNumber: 37,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\_app.js\",\n                lineNumber: 35,\n                columnNumber: 7\n            }, this);\n        }\n    }, []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_utils_auth_msalProvider__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_utils_secureThemeContext__WEBPACK_IMPORTED_MODULE_6__.SecureThemeProvider, {\n            initialTheme: pageProps.userData?.theme,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_utils_rolePermissionsContext__WEBPACK_IMPORTED_MODULE_7__.PermissionsProvider, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_utils_loaders_loadingContext__WEBPACK_IMPORTED_MODULE_4__.LoadingProvider, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_utils_loaders_overlaySpinner__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {}, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\_app.js\",\n                            lineNumber: 47,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Component, {\n                            ...pageProps\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\_app.js\",\n                            lineNumber: 48,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\_app.js\",\n                    lineNumber: 46,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\_app.js\",\n                lineNumber: 45,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\_app.js\",\n            lineNumber: 44,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\_app.js\",\n        lineNumber: 43,\n        columnNumber: 5\n    }, this);\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./pages/_app.js\n");

/***/ }),

/***/ "./pages/_document.js":
/*!****************************!*\
  !*** ./pages/_document.js ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Document)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_document__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/document */ \"./node_modules/next/document.js\");\n/* harmony import */ var next_document__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_document__WEBPACK_IMPORTED_MODULE_1__);\n\n\nfunction Document() {\n    return(// <Html lang=\"en\" className=\"dark\">\n    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_document__WEBPACK_IMPORTED_MODULE_1__.Html, {\n        lang: \"en\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_document__WEBPACK_IMPORTED_MODULE_1__.Head, {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\_document.js\",\n                lineNumber: 7,\n                columnNumber: 8\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_document__WEBPACK_IMPORTED_MODULE_1__.Main, {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\_document.js\",\n                        lineNumber: 9,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_document__WEBPACK_IMPORTED_MODULE_1__.NextScript, {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\_document.js\",\n                        lineNumber: 10,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\_document.js\",\n                lineNumber: 8,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\_document.js\",\n        lineNumber: 6,\n        columnNumber: 3\n    }, this));\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9wYWdlcy9fZG9jdW1lbnQuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7O0FBQTZEO0FBRTlDLFNBQVNJO0lBQ3RCLE9BQ0Usb0NBQW9DO2tCQUN0Qyw4REFBQ0osK0NBQUlBO1FBQUNLLE1BQUs7OzBCQUNOLDhEQUFDSiwrQ0FBSUE7Ozs7OzBCQUNOLDhEQUFDSzs7a0NBQ0MsOERBQUNKLCtDQUFJQTs7Ozs7a0NBQ0wsOERBQUNDLHFEQUFVQTs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFJbkIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly90aGx3ZWJhcHAvLi9wYWdlcy9fZG9jdW1lbnQuanM/NTM4YiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBIdG1sLCBIZWFkLCBNYWluLCBOZXh0U2NyaXB0IH0gZnJvbSBcIm5leHQvZG9jdW1lbnRcIjtcclxuXHJcbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIERvY3VtZW50KCkge1xyXG4gIHJldHVybiAoXHJcbiAgICAvLyA8SHRtbCBsYW5nPVwiZW5cIiBjbGFzc05hbWU9XCJkYXJrXCI+XHJcbiAgPEh0bWwgbGFuZz1cImVuXCI+XHJcbiAgICAgICA8SGVhZCAvPlxyXG4gICAgICA8Ym9keT5cclxuICAgICAgICA8TWFpbiAvPlxyXG4gICAgICAgIDxOZXh0U2NyaXB0IC8+XHJcbiAgICAgIDwvYm9keT5cclxuICAgIDwvSHRtbD5cclxuICApO1xyXG59XHJcbiJdLCJuYW1lcyI6WyJIdG1sIiwiSGVhZCIsIk1haW4iLCJOZXh0U2NyaXB0IiwiRG9jdW1lbnQiLCJsYW5nIiwiYm9keSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///./pages/_document.js\n");

/***/ }),

/***/ "./pages/users.js":
/*!************************!*\
  !*** ./pages/users.js ***!
  \************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   getServerSideProps: () => (/* binding */ getServerSideProps)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _components_Layout__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/Layout */ \"./components/Layout.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _fortawesome_react_fontawesome__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @fortawesome/react-fontawesome */ \"@fortawesome/react-fontawesome\");\n/* harmony import */ var _fortawesome_react_fontawesome__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(_fortawesome_react_fontawesome__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _fortawesome_free_solid_svg_icons__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @fortawesome/free-solid-svg-icons */ \"@fortawesome/free-solid-svg-icons\");\n/* harmony import */ var ag_grid_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ag-grid-react */ \"ag-grid-react\");\n/* harmony import */ var ag_grid_react__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(ag_grid_react__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var ag_grid_community_styles_ag_grid_css__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ag-grid-community/styles//ag-grid.css */ \"./node_modules/ag-grid-community/styles/ag-grid.css\");\n/* harmony import */ var ag_grid_community_styles_ag_grid_css__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(ag_grid_community_styles_ag_grid_css__WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var ag_grid_community_styles_ag_theme_alpine_css__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ag-grid-community/styles//ag-theme-alpine.css */ \"./node_modules/ag-grid-community/styles/ag-theme-alpine.css\");\n/* harmony import */ var ag_grid_community_styles_ag_theme_alpine_css__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(ag_grid_community_styles_ag_theme_alpine_css__WEBPACK_IMPORTED_MODULE_7__);\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_8___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_8__);\n/* harmony import */ var _services_apiConfig__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/services/apiConfig */ \"./services/apiConfig.js\");\n/* harmony import */ var _utils_renderer_roleRenderer__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/utils/renderer/roleRenderer */ \"./utils/renderer/roleRenderer.js\");\n/* harmony import */ var _utils_renderer_departmentRenderer__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/utils/renderer/departmentRenderer */ \"./utils/renderer/departmentRenderer.js\");\n/* harmony import */ var react_toastify__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! react-toastify */ \"react-toastify\");\n/* harmony import */ var react_toastify_dist_ReactToastify_css__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! react-toastify/dist/ReactToastify.css */ \"./node_modules/react-toastify/dist/ReactToastify.css\");\n/* harmony import */ var react_toastify_dist_ReactToastify_css__WEBPACK_IMPORTED_MODULE_13___default = /*#__PURE__*/__webpack_require__.n(react_toastify_dist_ReactToastify_css__WEBPACK_IMPORTED_MODULE_13__);\n/* harmony import */ var _utils_loaders_loadingContext__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/utils/loaders/loadingContext */ \"./utils/loaders/loadingContext.js\");\n/* harmony import */ var _utils_secureStorage__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @/utils/secureStorage */ \"./utils/secureStorage.js\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_components_Layout__WEBPACK_IMPORTED_MODULE_1__, _fortawesome_free_solid_svg_icons__WEBPACK_IMPORTED_MODULE_4__, react_toastify__WEBPACK_IMPORTED_MODULE_12__]);\n([_components_Layout__WEBPACK_IMPORTED_MODULE_1__, _fortawesome_free_solid_svg_icons__WEBPACK_IMPORTED_MODULE_4__, react_toastify__WEBPACK_IMPORTED_MODULE_12__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst users = ({ userData })=>{\n    const { setIsLoading } = (0,_utils_loaders_loadingContext__WEBPACK_IMPORTED_MODULE_14__.useLoading)();\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_8__.useRouter)();\n    const [pageSize, setPageSize] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(15);\n    const [rowData, setRowData] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [email, setEmail] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"\");\n    const [role, setRole] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"\");\n    const [department, setDepartment] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"\");\n    const [editData, setEditData] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)({});\n    const [validEmail, setValidEmail] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [validRole, setValidRole] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [validDepartment, setValidDepartment] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [isEdit, setIsEdit] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [userId, setUserId] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(0);\n    const [salesDepartment, setSalesDepartment] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [procurementDepartment, setProcurementDepartment] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [financialDepartment, setFinancialDepartment] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [technicalDepartment, setTechnicalDepartment] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [isCommonError, setCommonError] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"\");\n    const gridRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)();\n    const emailRegex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$/;\n    const IconsRenderer = (props)=>{\n        let updatedData;\n        const handleDelete = async (event)=>{\n            const allData = props.data;\n            if (allData.userId == userData.user_id) {\n                react_toastify__WEBPACK_IMPORTED_MODULE_12__.toast.error(\"Logged in user can't delete their own email\", {\n                    position: \"top-right\"\n                });\n                return false;\n            }\n            try {\n                const response = await fetch(`${\"http://localhost:8081\" || 0}/api/users/delete-user/${allData.userId}`, {\n                    method: \"DELETE\",\n                    headers: {\n                        Accept: \"application/json\",\n                        \"Content-Type\": \"application/json\"\n                    },\n                    credentials: \"include\",\n                    body: JSON.stringify({\n                        username: userData.name,\n                        useremail: userData.email\n                    })\n                });\n                if (response.status === 400) {\n                    react_toastify__WEBPACK_IMPORTED_MODULE_12__.toast.error(\"There was an error with your request. Please check your data and try again.\");\n                } else if (response.status === 401) {\n                    react_toastify__WEBPACK_IMPORTED_MODULE_12__.toast.error(\"Your session has expired. Please log in again.\");\n                    setTimeout(async ()=>{\n                        await (0,_utils_secureStorage__WEBPACK_IMPORTED_MODULE_15__.logout)();\n                        router.push(\"/login\");\n                    }, 3000);\n                } else if (response.status === 200 || response.status === 409) {\n                    const json = await response.json();\n                    if (json.status === 200) {\n                        updatedData = [\n                            ...rowData\n                        ];\n                        const index = updatedData.indexOf(allData);\n                        updatedData.splice(index, 1);\n                        props.api.applyTransaction({\n                            remove: updatedData\n                        });\n                        setRowData(updatedData);\n                        react_toastify__WEBPACK_IMPORTED_MODULE_12__.toast.success(json.message, {\n                            position: \"top-right\"\n                        });\n                    }\n                } else {\n                    throw new Error(\"Failed to delete user\");\n                }\n            } catch (error) {\n                console.error(\"Delete user error:\", error);\n                react_toastify__WEBPACK_IMPORTED_MODULE_12__.toast.error(\"Failed to delete user\", {\n                    position: \"top-right\"\n                });\n            }\n        };\n        const handleEdit = ()=>{\n            const editedData = props.data;\n            setEditData(editedData);\n            updatedData = [\n                ...rowData\n            ];\n            const index = updatedData.indexOf(editedData);\n            updatedData.splice(index, 1);\n            props.api.applyTransaction({\n                remove: updatedData\n            });\n            setRowData(updatedData);\n            setEmail(editedData.email);\n            setRole(editedData.role);\n            setDepartment(editedData.department);\n            setUserId(editedData.userId);\n            setIsEdit(true);\n        };\n        const isEditDisabled = isEdit || (props.data.role === 6 || props.data.role === 5) && props.data.userId !== userData.user_id;\n        const isDeleteDisabled = isEdit || props.data.role === 6 || props.data.role === 5 || props.data.userId === userData.user_id;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex flex-row gap-4 justify-center text-skin-primary\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    onClick: handleEdit,\n                    disabled: isEdit || userData.role_id != 6 && isEditDisabled,\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fortawesome_react_fontawesome__WEBPACK_IMPORTED_MODULE_3__.FontAwesomeIcon, {\n                        icon: _fortawesome_free_solid_svg_icons__WEBPACK_IMPORTED_MODULE_4__.faPenToSquare\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\users.js\",\n                        lineNumber: 146,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\users.js\",\n                    lineNumber: 142,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    onClick: handleDelete,\n                    disabled: userData.role_id != 6 && isDeleteDisabled,\n                    className: \"text-red-500\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fortawesome_react_fontawesome__WEBPACK_IMPORTED_MODULE_3__.FontAwesomeIcon, {\n                        icon: _fortawesome_free_solid_svg_icons__WEBPACK_IMPORTED_MODULE_4__.faTrash\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\users.js\",\n                        lineNumber: 153,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\users.js\",\n                    lineNumber: 148,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\users.js\",\n            lineNumber: 141,\n            columnNumber: 7\n        }, undefined);\n    };\n    // useEffect(() => {\n    //   const roleIdFromCookies = cookies.role_id;\n    //   if (roleIdFromCookies !== 1) {\n    //     router.push('/unauthorized'); // Assuming you're using Next.js router\n    //   }\n    // }, []);\n    const handleSelectDepartment = (e)=>{\n        if (e.target.value == 1) {\n            if (e.target.checked) {\n                setSalesDepartment(true);\n                setProcurementDepartment(false);\n                setFinancialDepartment(false);\n                setTechnicalDepartment(false);\n            } else {\n                setSalesDepartment(false);\n                setProcurementDepartment(false);\n                setFinancialDepartment(false);\n                setTechnicalDepartment(false);\n            }\n        } else if (e.target.value == 2) {\n            if (e.target.checked) {\n                setSalesDepartment(false);\n                setProcurementDepartment(true);\n                setFinancialDepartment(false);\n                setTechnicalDepartment(false);\n            } else {\n                setSalesDepartment(false);\n                setProcurementDepartment(false);\n                setFinancialDepartment(false);\n                setTechnicalDepartment(false);\n            }\n        } else if (e.target.value == 3) {\n            if (e.target.checked) {\n                setSalesDepartment(false);\n                setProcurementDepartment(false);\n                setFinancialDepartment(true);\n                setTechnicalDepartment(false);\n            } else {\n                setSalesDepartment(false);\n                setProcurementDepartment(false);\n                setFinancialDepartment(false);\n                setTechnicalDepartment(false);\n            }\n        } else if (e.target.value == 5) {\n            if (e.target.checked) {\n                setSalesDepartment(false);\n                setProcurementDepartment(false);\n                setFinancialDepartment(false);\n                setTechnicalDepartment(true);\n            } else {\n                setSalesDepartment(false);\n                setProcurementDepartment(false);\n                setFinancialDepartment(false);\n                setTechnicalDepartment(true);\n            }\n        } else {\n            setSalesDepartment(false);\n            setProcurementDepartment(false);\n            setFinancialDepartment(false);\n            setTechnicalDepartment(false);\n        }\n    };\n    async function handleSubmit() {\n        setIsEdit(false);\n        if (email && role) {\n            try {\n                const apiBase = \"http://localhost:8081\" || 0;\n                const url = isEdit ? `${apiBase}/api/users/update-user/${userId}` : `${apiBase}/api/users/add-user`;\n                const method = isEdit ? \"PUT\" : \"POST\";\n                const response = await fetch(url, {\n                    method: method,\n                    headers: {\n                        Accept: \"application/json\",\n                        \"Content-Type\": \"application/json\"\n                    },\n                    credentials: \"include\",\n                    body: JSON.stringify({\n                        role_id: role,\n                        email: email?.trim(),\n                        user: {\n                            username: userData.name,\n                            useremail: userData.email\n                        },\n                        department_id: department\n                    })\n                });\n                if (response.status === 400 || response.status === 401) {\n                    setCommonError(\"Session expired or invalid request\");\n                    if (response.status === 401) {\n                        react_toastify__WEBPACK_IMPORTED_MODULE_12__.toast.error(\"Your session has expired. Please log in again.\");\n                        setTimeout(async ()=>{\n                            await (0,_utils_secureStorage__WEBPACK_IMPORTED_MODULE_15__.logout)();\n                            router.push(\"/login\");\n                        }, 3000);\n                    }\n                    return;\n                }\n                if (response.status === 200 || response.status === 409) {\n                    const json = await response.json();\n                    if (isEdit) {\n                        const newItem = {\n                            email: json[0].email,\n                            role: json[0].role_id,\n                            userId: json[0].user_id,\n                            department: json[0].department_id\n                        };\n                        setRowData([\n                            ...rowData,\n                            newItem\n                        ]);\n                        setEmail(\"\");\n                        setRole(\"\");\n                        setDepartment(\"\");\n                        react_toastify__WEBPACK_IMPORTED_MODULE_12__.toast.success(\"User successfully updated\", {\n                            position: \"top-right\"\n                        });\n                    } else {\n                        if (json.data === \"exists\") {\n                            react_toastify__WEBPACK_IMPORTED_MODULE_12__.toast.error(\"User already exists.\", {\n                                position: \"top-right\"\n                            });\n                            return;\n                        }\n                        if (json.length > 0) {\n                            const newItem = {\n                                email: email,\n                                role: role,\n                                userId: json[0].id,\n                                department: department\n                            };\n                            setRowData([\n                                ...rowData,\n                                newItem\n                            ]);\n                            setEmail(\"\");\n                            react_toastify__WEBPACK_IMPORTED_MODULE_12__.toast.success(\"User successfully created\", {\n                                position: \"top-right\"\n                            });\n                        }\n                    }\n                } else {\n                    throw new Error(\"Failed to save user\");\n                }\n            } catch (error) {\n                console.error(\"Save user error:\", error);\n                react_toastify__WEBPACK_IMPORTED_MODULE_12__.toast.error(\"Failed to save user\", {\n                    position: \"top-right\"\n                });\n            }\n        } else {\n            setValidEmail(true);\n            setValidRole(true);\n            setValidDepartment(true);\n        }\n    }\n    function handleCancel() {\n        setIsEdit(false);\n        if (isEdit) {\n            const newItem = {\n                email: editData.email,\n                role: editData.role,\n                userId: editData.userId,\n                department: editData.department\n            };\n            setRowData([\n                ...rowData,\n                newItem\n            ]);\n        }\n        setEmail(\"\");\n        setRole(\"\");\n        setDepartment(\"\");\n        setEditData({});\n        setValidDepartment(false);\n        setValidRole(false);\n        setValidEmail(false);\n    }\n    const handleUserValidation = ()=>{\n        if (email && emailRegex.test(email)) {\n            setValidEmail(false);\n        } else {\n            setValidEmail(true);\n        }\n        if (validRole) {\n            setValidRole(true);\n        } else {\n            setValidRole(false);\n        }\n        if (validDepartment) {\n            setValidDepartment(true);\n        } else {\n            setValidDepartment(false);\n        }\n        return true;\n    };\n    async function getData() {\n        try {\n            const response = await fetch(`${\"http://localhost:8081\" || 0}/api/users/get-users`, {\n                method: \"GET\",\n                headers: {\n                    Accept: \"application/json\",\n                    \"Content-Type\": \"application/json\"\n                },\n                credentials: \"include\"\n            });\n            if (response.status === 400 || response.status === 401) {\n                setCommonError(\"Session expired\");\n                if (response.status === 401) {\n                    react_toastify__WEBPACK_IMPORTED_MODULE_12__.toast.error(\"Your session has expired. Please log in again.\");\n                    setTimeout(async ()=>{\n                        await (0,_utils_secureStorage__WEBPACK_IMPORTED_MODULE_15__.logout)();\n                        router.push(\"/login\");\n                    }, 3000);\n                }\n                return null;\n            }\n            if (response.status === 200) {\n                return await response.json();\n            }\n            throw new Error(\"Failed to fetch data\");\n        } catch (error) {\n            console.error(\"Get users error:\", error);\n            react_toastify__WEBPACK_IMPORTED_MODULE_12__.toast.error(\"Failed to fetch data\", {\n                position: \"top-right\"\n            });\n            return null;\n        }\n    }\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        setIsLoading(false);\n        if (typeof document !== \"undefined\") {\n            document.title = \"Users\";\n        }\n        if (false) {}\n        getData().then((data)=>{\n            if (data) {\n                const formattedData = data?.map((row)=>({\n                        email: row.email,\n                        role: row.role_id,\n                        userId: row.user_id,\n                        department: row.department_id\n                    }));\n                setRowData(formattedData);\n            }\n        });\n    }, []);\n    const handlePageSizeChange = (event)=>{\n        const newPageSize = parseInt(event.target.value, 15);\n        setPageSize(newPageSize);\n        gridRef.current.api.paginationSetPageSize(newPageSize);\n    };\n    const defaultColDef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useMemo)(()=>({\n            sortable: true,\n            filter: false,\n            resizable: true,\n            flex: 1,\n            suppressMenu: false\n        }));\n    const columnDefs = [\n        {\n            headerName: \"Email\",\n            field: \"email\",\n            suppressMenu: true,\n            suppressSizeToFit: true,\n            flex: \"6%\"\n        },\n        {\n            headerName: \"Role\",\n            field: \"role\",\n            cellRenderer: _utils_renderer_roleRenderer__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n            flex: \"4%\",\n            hide: false\n        },\n        {\n            headerName: \"Department\",\n            field: \"department\",\n            cellRenderer: _utils_renderer_departmentRenderer__WEBPACK_IMPORTED_MODULE_11__[\"default\"],\n            flex: \"4%\",\n            hide: false\n        },\n        {\n            field: \"\",\n            cellRenderer: IconsRenderer,\n            flex: \"4%\",\n            cellStyle: ()=>({})\n        },\n        {\n            field: \"userId\",\n            //cellRenderer: IconsRenderer,\n            flex: \"4%\",\n            cellStyle: ()=>({}),\n            hide: true,\n            suppressFiltersToolPanel: true\n        }\n    ];\n    const onFilterTextBoxChanged = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(()=>{\n        gridRef.current.api.setQuickFilter(document.getElementById(\"filter-text-box\").value);\n    }, []);\n    const handleGridReady = (params)=>{\n        params.api.setColumnDefs(columnDefs);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_toastify__WEBPACK_IMPORTED_MODULE_12__.ToastContainer, {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\users.js\",\n                lineNumber: 483,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Layout__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                userData: userData,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-row justify-between w-[95%] gap-8 pe-1\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-[50%]\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-row md:flex-col lg:flex-row items-end\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex gap-6 w-full justify-end\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"relative block w-[47vh] text-gray-400 focus-within:text-gray-600 mt-0 pt-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"absolute px-4 py-1 text-black\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fortawesome_react_fontawesome__WEBPACK_IMPORTED_MODULE_3__.FontAwesomeIcon, {\n                                                        icon: _fortawesome_free_solid_svg_icons__WEBPACK_IMPORTED_MODULE_4__.faSearch,\n                                                        className: \"fw-bold\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\users.js\",\n                                                        lineNumber: 491,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\users.js\",\n                                                    lineNumber: 490,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"text\",\n                                                    id: \"filter-text-box\",\n                                                    placeholder: \"Search\",\n                                                    onInput: onFilterTextBoxChanged,\n                                                    className: \"block w-full px-4 pl-10 text-gray-500 placeholder-gray-400 searchbar border rounded-lg appearance-none form-input focus:outline-none\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\users.js\",\n                                                    lineNumber: 493,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\users.js\",\n                                            lineNumber: 489,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\users.js\",\n                                        lineNumber: 488,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\users.js\",\n                                    lineNumber: 487,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"my-5\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative ag-theme-alpine\",\n                                        style: {\n                                            height: \"calc(100vh - 150px)\"\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ag_grid_react__WEBPACK_IMPORTED_MODULE_5__.AgGridReact, {\n                                                rowData: rowData,\n                                                ref: gridRef,\n                                                columnDefs: columnDefs,\n                                                defaultColDef: defaultColDef,\n                                                suppressRowClickSelection: true,\n                                                rowSelection: \"multiple\",\n                                                pagination: true,\n                                                paginationPageSize: pageSize,\n                                                onPageSizeChanged: handlePageSizeChange,\n                                                tooltipShowDelay: 0,\n                                                tooltipHideDelay: 1000,\n                                                onGridReady: handleGridReady\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\users.js\",\n                                                lineNumber: 508,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-start mt-2 pagination-style\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    htmlFor: \"page-size-select pagination\",\n                                                    className: \"inputs\",\n                                                    children: [\n                                                        \"Show\",\n                                                        \" \",\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                            id: \"page-size-select\",\n                                                            onChange: handlePageSizeChange,\n                                                            value: pageSize,\n                                                            className: \"focus:outline-none\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                    value: 10,\n                                                                    children: \"10\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\users.js\",\n                                                                    lineNumber: 534,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                    value: 15,\n                                                                    children: \"15\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\users.js\",\n                                                                    lineNumber: 535,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                    value: 25,\n                                                                    children: \"25\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\users.js\",\n                                                                    lineNumber: 536,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                    value: 50,\n                                                                    children: \"50\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\users.js\",\n                                                                    lineNumber: 537,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                    value: 100,\n                                                                    children: \"100\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\users.js\",\n                                                                    lineNumber: 538,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\users.js\",\n                                                            lineNumber: 528,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        \" \",\n                                                        \"entries\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\users.js\",\n                                                    lineNumber: 523,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\users.js\",\n                                                lineNumber: 522,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\users.js\",\n                                        lineNumber: 504,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\users.js\",\n                                    lineNumber: 503,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\users.js\",\n                            lineNumber: 486,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-[50%] mt-14\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative panel-container contentsectionbg rounded-lg w-full 2xl:w-[calc(100%-70px)] p-4 pb-0 shadow-lg\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"m-3 mb-0\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mb-3\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                className: \"formtitle pb-1\",\n                                                children: \"Assign user role \"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\users.js\",\n                                                lineNumber: 550,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\users.js\",\n                                            lineNumber: 549,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex flex-col mb-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"labels mb-1\",\n                                                    children: [\n                                                        \"Email \",\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-red-500\",\n                                                            children: \"*\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\users.js\",\n                                                            lineNumber: 554,\n                                                            columnNumber: 27\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\users.js\",\n                                                    lineNumber: 553,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"email\",\n                                                    maxLength: 80,\n                                                    name: \"Email\",\n                                                    value: email,\n                                                    onChange: (e)=>{\n                                                        setEmail(e.target.value), handleUserValidation;\n                                                    },\n                                                    className: \"w-full px-2 2xl:px-3 border border-light-gray rounded-md searchbar\",\n                                                    onBlur: handleUserValidation\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\users.js\",\n                                                    lineNumber: 556,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                validEmail && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-red-500\",\n                                                    children: \"Please enter valid email address\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\users.js\",\n                                                    lineNumber: 568,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\users.js\",\n                                            lineNumber: 552,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex flex-col mb-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"me-5 labels\",\n                                                    children: [\n                                                        \"Role \",\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-red-500\",\n                                                            children: \"*\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\users.js\",\n                                                            lineNumber: 575,\n                                                            columnNumber: 26\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\users.js\",\n                                                    lineNumber: 574,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex mt-1\",\n                                                    children: [\n                                                        userData?.role_id == 6 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                            id: \"superadmin-radio\",\n                                                                            type: \"radio\",\n                                                                            value: \"1\",\n                                                                            checked: role == 6,\n                                                                            onChange: ()=>{\n                                                                                setRole(6), handleUserValidation;\n                                                                            },\n                                                                            onBlur: handleUserValidation,\n                                                                            className: \"ml-0 w-4 h-4 text-blue-600 bg-gray-100 border-light-gray focus:ring-blue-500 focus:ring-2\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\users.js\",\n                                                                            lineNumber: 582,\n                                                                            columnNumber: 29\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                            htmlFor: \"superadmin-radio\",\n                                                                            className: \"p-0 ml-2 me-5 text-blackcolor\",\n                                                                            children: \"Super Admin\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\users.js\",\n                                                                            lineNumber: 593,\n                                                                            columnNumber: 29\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\users.js\",\n                                                                    lineNumber: 581,\n                                                                    columnNumber: 27\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\users.js\",\n                                                                lineNumber: 580,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        }, void 0, false),\n                                                        (userData?.role_id == 5 || userData?.role_id == 6) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                        id: \"thladmin-radio\",\n                                                                        type: \"radio\",\n                                                                        value: \"1\",\n                                                                        checked: role == 5,\n                                                                        onChange: ()=>{\n                                                                            setRole(5), handleUserValidation;\n                                                                        },\n                                                                        disabled: userData?.role_id == 5 ? true : false,\n                                                                        onBlur: handleUserValidation,\n                                                                        className: \"ml-0 w-4 h-4 text-blue-600 bg-gray-100 border-light-gray focus:ring-blue-500 focus:ring-2\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\users.js\",\n                                                                        lineNumber: 606,\n                                                                        columnNumber: 27\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        htmlFor: \"thladmin-radio\",\n                                                                        className: \"p-0 ml-2 me-5 text-blackcolor\",\n                                                                        children: \"THL Admin\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\users.js\",\n                                                                        lineNumber: 618,\n                                                                        columnNumber: 27\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\users.js\",\n                                                                lineNumber: 605,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\users.js\",\n                                                            lineNumber: 604,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                        id: \"admin-radio\",\n                                                                        type: \"radio\",\n                                                                        value: \"1\",\n                                                                        checked: role == 1,\n                                                                        onChange: ()=>{\n                                                                            setRole(1), handleUserValidation;\n                                                                        },\n                                                                        onBlur: handleUserValidation,\n                                                                        className: \"ml-0 w-4 h-4 text-blue-600 bg-gray-100 border-light-gray focus:ring-blue-500 focus:ring-2 hover:cursor-pointer\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\users.js\",\n                                                                        lineNumber: 629,\n                                                                        columnNumber: 25\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        htmlFor: \"admin-radio\",\n                                                                        className: \"p-0 ml-2 me-5 text-blackcolor hover:cursor-pointer\",\n                                                                        children: \"Administrator\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\users.js\",\n                                                                        lineNumber: 640,\n                                                                        columnNumber: 25\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\users.js\",\n                                                                lineNumber: 628,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\users.js\",\n                                                            lineNumber: 627,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                    id: \"approver-radio\",\n                                                                    type: \"radio\",\n                                                                    value: \"2\",\n                                                                    checked: role == 2,\n                                                                    onChange: ()=>{\n                                                                        setRole(2), handleUserValidation;\n                                                                    },\n                                                                    onBlur: handleUserValidation,\n                                                                    className: \"ml-0 w-4 h-4 text-blue-600 bg-gray-100 border-light-gray focus:ring-blue-500 focus:ring-2\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\users.js\",\n                                                                    lineNumber: 649,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    htmlFor: \"approver-radio\",\n                                                                    className: \"p-0 ml-2 me-5 text-blackcolor\",\n                                                                    children: \"Approver\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\users.js\",\n                                                                    lineNumber: 660,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\users.js\",\n                                                            lineNumber: 648,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                        id: \"user-radio\",\n                                                                        type: \"radio\",\n                                                                        value: \"4\",\n                                                                        checked: role == 4,\n                                                                        onChange: ()=>{\n                                                                            setRole(4), handleUserValidation;\n                                                                        },\n                                                                        onBlur: handleUserValidation,\n                                                                        className: \"ml-0 w-4 h-4 text-blue-600 bg-gray-100 border-light-gray focus:ring-blue-500 focus:ring-2 hover:cursor-pointer\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\users.js\",\n                                                                        lineNumber: 669,\n                                                                        columnNumber: 25\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        htmlFor: \"user-radio\",\n                                                                        className: \"p-0 ml-2 me-5 text-blackcolor hover:cursor-pointer\",\n                                                                        children: \"User\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\users.js\",\n                                                                        lineNumber: 680,\n                                                                        columnNumber: 25\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\users.js\",\n                                                                lineNumber: 668,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\users.js\",\n                                                            lineNumber: 667,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\users.js\",\n                                                    lineNumber: 577,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                validRole && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-red-500\",\n                                                    children: \"Select role\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\users.js\",\n                                                    lineNumber: 690,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\users.js\",\n                                            lineNumber: 573,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex flex-col mb-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"me-5 labels\",\n                                                    children: [\n                                                        \"Department \",\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-red-500\",\n                                                            children: \"*\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\users.js\",\n                                                            lineNumber: 695,\n                                                            columnNumber: 32\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\users.js\",\n                                                    lineNumber: 694,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex mt-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                    id: \"department-sales\",\n                                                                    type: \"radio\",\n                                                                    value: 1,\n                                                                    checked: department == 1,\n                                                                    onChange: (e)=>{\n                                                                        setDepartment(1);\n                                                                        handleSelectDepartment(e);\n                                                                    },\n                                                                    onBlur: handleUserValidation,\n                                                                    className: \"ml-0 w-4 h-4 text-blue-600 bg-gray-100 border-light-gray focus:ring-blue-500 focus:ring-2 hover:cursor-pointer\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\users.js\",\n                                                                    lineNumber: 699,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    htmlFor: \"department-sales\",\n                                                                    className: \"p-0 ml-2 me-5 text-blackcolor hover:cursor-pointer\",\n                                                                    children: \"Sales\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\users.js\",\n                                                                    lineNumber: 711,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\users.js\",\n                                                            lineNumber: 698,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                    id: \"department-procurement\",\n                                                                    type: \"radio\",\n                                                                    value: 2,\n                                                                    checked: department == 2,\n                                                                    onChange: (e)=>{\n                                                                        setDepartment(2);\n                                                                        handleSelectDepartment(e);\n                                                                    },\n                                                                    onBlur: handleUserValidation,\n                                                                    className: \"ml-0 w-4 h-4 text-blue-600 bg-gray-100 border-light-gray focus:ring-blue-500 focus:ring-2 hover:cursor-pointer\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\users.js\",\n                                                                    lineNumber: 719,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    htmlFor: \"department-procurement\",\n                                                                    className: \"p-0 ml-2 me-5 text-blackcolor hover:cursor-pointer\",\n                                                                    children: \"Procurement\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\users.js\",\n                                                                    lineNumber: 731,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\users.js\",\n                                                            lineNumber: 718,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                    id: \"department-financial\",\n                                                                    type: \"radio\",\n                                                                    value: 3,\n                                                                    checked: department == 3,\n                                                                    onChange: (e)=>{\n                                                                        setDepartment(3);\n                                                                        handleSelectDepartment(e);\n                                                                    },\n                                                                    onBlur: handleUserValidation,\n                                                                    className: \"ml-0 w-4 h-4 text-blue-600 bg-gray-100 border-light-gray focus:ring-blue-500 focus:ring-2 hover:cursor-pointer\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\users.js\",\n                                                                    lineNumber: 739,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    htmlFor: \"department-financial\",\n                                                                    className: \"p-0 ml-2 me-5 text-blackcolor hover:cursor-pointer\",\n                                                                    children: \"Financial\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\users.js\",\n                                                                    lineNumber: 751,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\users.js\",\n                                                            lineNumber: 738,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                    id: \"department-technical\",\n                                                                    type: \"radio\",\n                                                                    value: 5,\n                                                                    checked: department == 5,\n                                                                    onChange: (e)=>{\n                                                                        setDepartment(5);\n                                                                        handleSelectDepartment(e);\n                                                                    },\n                                                                    onBlur: handleUserValidation,\n                                                                    className: \"ml-0 w-4 h-4 text-blue-600 bg-gray-100 border-light-gray focus:ring-blue-500 focus:ring-2 hover:cursor-pointer\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\users.js\",\n                                                                    lineNumber: 759,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    htmlFor: \"department-technical\",\n                                                                    className: \"p-0 ml-2 me-5 text-blackcolor hover:cursor-pointer\",\n                                                                    children: \"Technical\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\users.js\",\n                                                                    lineNumber: 771,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\users.js\",\n                                                            lineNumber: 758,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                    id: \"department-logistics\",\n                                                                    type: \"radio\",\n                                                                    value: 8,\n                                                                    checked: department == 8,\n                                                                    onChange: (e)=>{\n                                                                        setDepartment(8);\n                                                                        handleSelectDepartment(e);\n                                                                    },\n                                                                    onBlur: handleUserValidation,\n                                                                    className: \"ml-0 w-4 h-4 text-blue-600 bg-gray-100 border-light-gray focus:ring-blue-500 focus:ring-2 hover:cursor-pointer\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\users.js\",\n                                                                    lineNumber: 779,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    htmlFor: \"department-logistics\",\n                                                                    className: \"p-0 ml-2 me-5 text-blackcolor hover:cursor-pointer\",\n                                                                    children: \"Logistics\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\users.js\",\n                                                                    lineNumber: 791,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\users.js\",\n                                                            lineNumber: 778,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\users.js\",\n                                                    lineNumber: 697,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                validDepartment && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-red-500\",\n                                                    children: \"Select Departement\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\users.js\",\n                                                    lineNumber: 820,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\users.js\",\n                                            lineNumber: 693,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex justify-end pb-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    className: \"border border-skin-primary text-skin-primary me-10 py-1 px-8 font-medium rounded-md\",\n                                                    onClick: handleCancel,\n                                                    children: \"Cancel\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\users.js\",\n                                                    lineNumber: 824,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    className: \"border border-skin-primary bg-skin-primary text-white rounded-md py-1 px-8 font-medium\",\n                                                    onClick: handleSubmit,\n                                                    disabled: email && emailRegex.test(email) && role && department ? false : true,\n                                                    children: \"Save\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\users.js\",\n                                                    lineNumber: 830,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\users.js\",\n                                            lineNumber: 823,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\users.js\",\n                                    lineNumber: 548,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\users.js\",\n                                lineNumber: 547,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\users.js\",\n                            lineNumber: 546,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\users.js\",\n                    lineNumber: 485,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\users.js\",\n                lineNumber: 484,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (users);\nconst getServerSideProps = async (context)=>{\n    try {\n        // Use secure session validation\n        const sessionId = context.req.cookies.thl_session;\n        if (!sessionId) {\n            return {\n                redirect: {\n                    destination: `/login?redirect=${encodeURIComponent(context.resolvedUrl)}`,\n                    permanent: false\n                }\n            };\n        }\n        // Validate session with our backend API\n        const apiBase = \"http://localhost:8081\" || 0;\n        try {\n            const response = await fetch(`${apiBase}/api/auth/me`, {\n                method: \"GET\",\n                headers: {\n                    \"Cookie\": `thl_session=${sessionId}`,\n                    \"Content-Type\": \"application/json\"\n                }\n            });\n            if (!response.ok) {\n                // Session invalid or expired - redirect to login\n                return {\n                    redirect: {\n                        destination: `/login?redirect=${encodeURIComponent(context.resolvedUrl)}`,\n                        permanent: false\n                    }\n                };\n            }\n            const { user } = await response.json();\n            // Check if user has permission to access users page\n            // Only role_id 1 (Admin), 5 (THL Admin), and 6 (Super Admin) can access users page\n            const allowedRoles = [\n                1,\n                5,\n                6\n            ];\n            if (!allowedRoles.includes(user.role_id)) {\n                return {\n                    redirect: {\n                        destination: \"/unauthorized\",\n                        permanent: false\n                    }\n                };\n            }\n            return {\n                props: {\n                    userData: user\n                }\n            };\n        } catch (fetchError) {\n            console.error(\"Session validation failed:\", fetchError);\n            return {\n                redirect: {\n                    destination: `/login?redirect=${encodeURIComponent(context.resolvedUrl)}`,\n                    permanent: false\n                }\n            };\n        }\n    } catch (error) {\n        console.error(\"Authentication error:\", error);\n        return {\n            redirect: {\n                destination: `/login?redirect=${encodeURIComponent(context.resolvedUrl)}`,\n                permanent: false\n            }\n        };\n    }\n};\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./pages/users.js\n");

/***/ }),

/***/ "./services/apiConfig.js":
/*!*******************************!*\
  !*** ./services/apiConfig.js ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   apiConfig: () => (/* binding */ apiConfig)\n/* harmony export */ });\nlet apiConfig = {\n    // serverAddress: `http://************:9000/api/`,\n    //serverAddress: `http://**************:9000/api/`,\n    serverAddress: `${\"http://localhost:8081\"}/api/`,\n    socketAddress: `${\"http://localhost:8081\"}`\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9zZXJ2aWNlcy9hcGlDb25maWcuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFPLElBQUlBLFlBQVk7SUFDckIsa0RBQWtEO0lBQ2xELG1EQUFtRDtJQUNuREMsZUFBZSxDQUFDLEVBQUVDLHVCQUFvQyxDQUFDLEtBQUssQ0FBQztJQUM3REcsZUFBZSxDQUFDLEVBQUVILHVCQUFvQyxDQUFDLENBQUM7QUFHMUQsRUFBRSIsInNvdXJjZXMiOlsid2VicGFjazovL3RobHdlYmFwcC8uL3NlcnZpY2VzL2FwaUNvbmZpZy5qcz9kYWY2Il0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBsZXQgYXBpQ29uZmlnID0ge1xyXG4gIC8vIHNlcnZlckFkZHJlc3M6IGBodHRwOi8vMTMuNDAuNjMuMjE5OjkwMDAvYXBpL2AsXHJcbiAgLy9zZXJ2ZXJBZGRyZXNzOiBgaHR0cDovLzE5Mi4xNjguMjUuMTM3OjkwMDAvYXBpL2AsXHJcbiAgc2VydmVyQWRkcmVzczogYCR7cHJvY2Vzcy5lbnYuTkVYVF9QVUJMSUNfQVBJX0JBU0VfVVJMfS9hcGkvYCxcclxuICBzb2NrZXRBZGRyZXNzOiBgJHtwcm9jZXNzLmVudi5ORVhUX1BVQkxJQ19BUElfQkFTRV9VUkx9YCxcclxuICAvLyBzZXJ2ZXJBZGRyZXNzOiBgaHR0cDovL2xvY2FsaG9zdDoxNDMzL2FwaS9gLFxyXG4gIC8vIHNlcnZlckFkZHJlc3M6IGBodHRwOi8vbG9jYWxob3N0OjE0MzMvYXBpL2AsXHJcbn07XHJcbiJdLCJuYW1lcyI6WyJhcGlDb25maWciLCJzZXJ2ZXJBZGRyZXNzIiwicHJvY2VzcyIsImVudiIsIk5FWFRfUFVCTElDX0FQSV9CQVNFX1VSTCIsInNvY2tldEFkZHJlc3MiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///./services/apiConfig.js\n");

/***/ }),

/***/ "./utils/auth/authConfig.js":
/*!**********************************!*\
  !*** ./utils/auth/authConfig.js ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   BASE_URL: () => (/* binding */ BASE_URL),\n/* harmony export */   loginRequest: () => (/* binding */ loginRequest),\n/* harmony export */   msalConfig: () => (/* binding */ msalConfig)\n/* harmony export */ });\nconst BASE_URL = `${\"http://localhost:3000\"}/login`;\nconst msalConfig = {\n    auth: {\n        clientId: \"bafc3c7b-820b-4d2d-9a96-9162c4b2c78a\",\n        authority: `https://login.microsoftonline.com/${\"6d90d24f-9602-49e8-8903-eb86dce9656a\"}`,\n        redirectUri: \"/\"\n    },\n    cache: {\n        cacheLocation: \"sessionStorage\",\n        storeAuthStateInCookie: false\n    }\n};\nconst loginRequest = {\n    scopes: [\n        \"user.read\"\n    ]\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi91dGlscy9hdXRoL2F1dGhDb25maWcuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQU8sTUFBTUEsV0FBVyxDQUFDLEVBQUVDLHVCQUFnQyxDQUFDLE1BQU0sQ0FBQyxDQUFDO0FBRTdELE1BQU1HLGFBQWE7SUFDeEJDLE1BQU07UUFDSkMsVUFBVUwsc0NBQWlDO1FBQzNDTyxXQUFXLENBQUMsa0NBQWtDLEVBQUVQLHNDQUFpQyxDQUFDLENBQUM7UUFDbkZTLGFBQWE7SUFDZjtJQUNBQyxPQUFPO1FBQ0xDLGVBQWU7UUFDZkMsd0JBQXdCO0lBQzFCO0FBQ0YsRUFBRTtBQUVLLE1BQU1DLGVBQWU7SUFDMUJDLFFBQVE7UUFBQztLQUFZO0FBQ3ZCLEVBQUUiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly90aGx3ZWJhcHAvLi91dGlscy9hdXRoL2F1dGhDb25maWcuanM/YWM5ZCJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgY29uc3QgQkFTRV9VUkwgPSBgJHtwcm9jZXNzLmVudi5ORVhUX1BVQkxJQ19CQVNFX1VSTH0vbG9naW5gO1xyXG5cclxuZXhwb3J0IGNvbnN0IG1zYWxDb25maWcgPSB7XHJcbiAgYXV0aDoge1xyXG4gICAgY2xpZW50SWQ6IHByb2Nlc3MuZW52Lk5FWFRfUFVCTElDX0NMSUVOVF9JRCxcclxuICAgIGF1dGhvcml0eTogYGh0dHBzOi8vbG9naW4ubWljcm9zb2Z0b25saW5lLmNvbS8ke3Byb2Nlc3MuZW52Lk5FWFRfUFVCTElDX1RFTkFOVF9JRH1gLFxyXG4gICAgcmVkaXJlY3RVcmk6IFwiL1wiLFxyXG4gIH0sXHJcbiAgY2FjaGU6IHtcclxuICAgIGNhY2hlTG9jYXRpb246IFwic2Vzc2lvblN0b3JhZ2VcIiwgLy8gQ2hhbmdlZCBmcm9tIGxvY2FsU3RvcmFnZVxyXG4gICAgc3RvcmVBdXRoU3RhdGVJbkNvb2tpZTogZmFsc2UsXHJcbiAgfSxcclxufTtcclxuXHJcbmV4cG9ydCBjb25zdCBsb2dpblJlcXVlc3QgPSB7XHJcbiAgc2NvcGVzOiBbXCJ1c2VyLnJlYWRcIl0sXHJcbn07XHJcbiJdLCJuYW1lcyI6WyJCQVNFX1VSTCIsInByb2Nlc3MiLCJlbnYiLCJORVhUX1BVQkxJQ19CQVNFX1VSTCIsIm1zYWxDb25maWciLCJhdXRoIiwiY2xpZW50SWQiLCJORVhUX1BVQkxJQ19DTElFTlRfSUQiLCJhdXRob3JpdHkiLCJORVhUX1BVQkxJQ19URU5BTlRfSUQiLCJyZWRpcmVjdFVyaSIsImNhY2hlIiwiY2FjaGVMb2NhdGlvbiIsInN0b3JlQXV0aFN0YXRlSW5Db29raWUiLCJsb2dpblJlcXVlc3QiLCJzY29wZXMiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///./utils/auth/authConfig.js\n");

/***/ }),

/***/ "./utils/auth/msalProvider.jsx":
/*!*************************************!*\
  !*** ./utils/auth/msalProvider.jsx ***!
  \*************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _azure_msal_browser__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @azure/msal-browser */ \"@azure/msal-browser\");\n/* harmony import */ var _azure_msal_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @azure/msal-react */ \"@azure/msal-react\");\n/* harmony import */ var _authConfig__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./authConfig */ \"./utils/auth/authConfig.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_4__);\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_azure_msal_browser__WEBPACK_IMPORTED_MODULE_1__, _azure_msal_react__WEBPACK_IMPORTED_MODULE_2__]);\n([_azure_msal_browser__WEBPACK_IMPORTED_MODULE_1__, _azure_msal_react__WEBPACK_IMPORTED_MODULE_2__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n// components/MsalProvider.tsx\n\n\n\n\n\nconst MsalAuthProvider = ({ children })=>{\n    const msalInstance = new _azure_msal_browser__WEBPACK_IMPORTED_MODULE_1__.PublicClientApplication(_authConfig__WEBPACK_IMPORTED_MODULE_3__.msalConfig);\n    const handlePopup = (event)=>{\n        if (event instanceof Event && event.isTrusted) {\n            msalInstance.handlePopupPromise().catch((error)=>{\n                console.error(\"Error handling popup:\", error);\n            });\n        }\n    };\n    react__WEBPACK_IMPORTED_MODULE_4___default().useEffect(()=>{\n        window.addEventListener(_azure_msal_browser__WEBPACK_IMPORTED_MODULE_1__.EventType.LOGIN_SUCCESS, handlePopup);\n        window.addEventListener(_azure_msal_browser__WEBPACK_IMPORTED_MODULE_1__.EventType.ACQUIRE_TOKEN_SUCCESS, handlePopup);\n        return ()=>{\n            window.removeEventListener(_azure_msal_browser__WEBPACK_IMPORTED_MODULE_1__.EventType.LOGIN_SUCCESS, handlePopup);\n            window.removeEventListener(_azure_msal_browser__WEBPACK_IMPORTED_MODULE_1__.EventType.ACQUIRE_TOKEN_SUCCESS, handlePopup);\n        };\n    }, []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_azure_msal_react__WEBPACK_IMPORTED_MODULE_2__.MsalProvider, {\n        instance: msalInstance,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\utils\\\\auth\\\\msalProvider.jsx\",\n        lineNumber: 27,\n        columnNumber: 10\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (MsalAuthProvider);\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./utils/auth/msalProvider.jsx\n");

/***/ }),

/***/ "./utils/auth/useSecureAuth.js":
/*!*************************************!*\
  !*** ./utils/auth/useSecureAuth.js ***!
  \*************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useSecureAuth: () => (/* binding */ useSecureAuth)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _azure_msal_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @azure/msal-react */ \"@azure/msal-react\");\n/* harmony import */ var _utils_secureStorage__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/utils/secureStorage */ \"./utils/secureStorage.js\");\n/* harmony import */ var react_toastify__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react-toastify */ \"react-toastify\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_azure_msal_react__WEBPACK_IMPORTED_MODULE_2__, react_toastify__WEBPACK_IMPORTED_MODULE_4__]);\n([_azure_msal_react__WEBPACK_IMPORTED_MODULE_2__, react_toastify__WEBPACK_IMPORTED_MODULE_4__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\n\nconst useSecureAuth = ()=>{\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const [isAuthenticated, setIsAuthenticated] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(true);\n    const { instance } = (0,_azure_msal_react__WEBPACK_IMPORTED_MODULE_2__.useMsal)();\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_1__.useRouter)();\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        checkAuthStatus();\n    }, []);\n    const checkAuthStatus = async ()=>{\n        try {\n            const currentUser = await (0,_utils_secureStorage__WEBPACK_IMPORTED_MODULE_3__.getCurrentUser)();\n            if (currentUser) {\n                setUser(currentUser);\n                setIsAuthenticated(true);\n            }\n        } catch (error) {\n            console.error(\"Auth check failed:\", error);\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const secureLogin = async (redirect)=>{\n        setIsLoading(true);\n        try {\n            const requestedScope = `api://${\"bafc3c7b-820b-4d2d-9a96-9162c4b2c78a\"}/access_as_user`;\n            const request = {\n                prompt: \"select_account\",\n                scopes: [\n                    requestedScope\n                ]\n            };\n            const loginObj = await instance.loginPopup(request);\n            instance.setActiveAccount(loginObj.account);\n            // Acquire a separate Graph token for company details\n            const graphToken = await instance.acquireTokenSilent({\n                scopes: [\n                    \"User.Read\"\n                ],\n                account: loginObj.account\n            });\n            // Send both tokens to backend - the backend handles all session management\n            const result = await (0,_utils_secureStorage__WEBPACK_IMPORTED_MODULE_3__.login)(loginObj.accessToken, {\n                graphToken: graphToken.accessToken\n            });\n            if (!result.success) {\n                instance.clearCache();\n                throw new Error(result.error || \"Login failed\");\n            }\n            setUser(result.user);\n            setIsAuthenticated(true);\n            // Log login event - simplified to use session data\n            await logLoginEvent(loginObj.account, result.user);\n            instance.clearCache();\n            if (redirect) {\n                router.replace(redirect);\n            } else {\n                router.replace(\"/suppliers\");\n            }\n            react_toastify__WEBPACK_IMPORTED_MODULE_4__.toast.success(\"Login successful!\", {\n                position: \"top-right\"\n            });\n        } catch (error) {\n            console.error(\"Secure login failed:\", error);\n            react_toastify__WEBPACK_IMPORTED_MODULE_4__.toast.error(error.message || \"Login failed\", {\n                position: \"top-right\"\n            });\n            setIsAuthenticated(false);\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const secureLoginExistingAccount = async (redirect)=>{\n        setIsLoading(true);\n        try {\n            const activeAccount = instance.getActiveAccount();\n            if (!activeAccount) {\n                throw new Error(\"No active account found!\");\n            }\n            const loginObj = await instance.acquireTokenSilent({\n                scopes: [\n                    `api://${\"bafc3c7b-820b-4d2d-9a96-9162c4b2c78a\"}/access_as_user`\n                ],\n                account: activeAccount,\n                forceRefresh: true\n            });\n            // Acquire Graph token\n            const graphToken = await instance.acquireTokenSilent({\n                scopes: [\n                    \"User.Read\"\n                ],\n                account: activeAccount\n            });\n            const result = await (0,_utils_secureStorage__WEBPACK_IMPORTED_MODULE_3__.login)(loginObj.accessToken, {\n                graphToken: graphToken.accessToken\n            });\n            if (!result.success) {\n                throw new Error(result.error || \"Login failed\");\n            }\n            setUser(result.user);\n            setIsAuthenticated(true);\n            await logLoginEvent(activeAccount, result.user);\n            if (redirect) {\n                router.push(redirect);\n            } else {\n                router.push(\"/suppliers\");\n            }\n        } catch (error) {\n            console.error(\"Secure login failed:\", error);\n            react_toastify__WEBPACK_IMPORTED_MODULE_4__.toast.error(error.message || \"Login failed\", {\n                position: \"top-right\"\n            });\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    // Simplified login logging - no localStorage needed\n    const logLoginEvent = async (account, userData)=>{\n        try {\n            const apiBase = \"http://localhost:8081\" || 0;\n            await fetch(`${apiBase}/api/logs/logLogin`, {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                credentials: \"include\",\n                body: JSON.stringify({\n                    userData: account,\n                    company: userData?.ADCompanyName || userData?.companyName\n                })\n            });\n        } catch (error) {\n            console.error(\"Login logging failed:\", error);\n        }\n    };\n    const secureLogout = async ()=>{\n        try {\n            await (0,_utils_secureStorage__WEBPACK_IMPORTED_MODULE_3__.logout)();\n            setUser(null);\n            setIsAuthenticated(false);\n            instance.clearCache();\n            instance.logoutPopup();\n            router.push(\"/login\");\n        } catch (error) {\n            console.error(\"Logout failed:\", error);\n            instance.clearCache();\n            (0,_utils_secureStorage__WEBPACK_IMPORTED_MODULE_3__.clearClientStorage)();\n            setUser(null);\n            setIsAuthenticated(false);\n            instance.logoutPopup();\n        }\n    };\n    return {\n        user,\n        isAuthenticated,\n        isLoading,\n        secureLogin,\n        secureLoginExistingAccount,\n        secureLogout,\n        checkAuthStatus\n    };\n};\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./utils/auth/useSecureAuth.js\n");

/***/ }),

/***/ "./utils/loaders/loadingContext.js":
/*!*****************************************!*\
  !*** ./utils/loaders/loadingContext.js ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   LoadingProvider: () => (/* binding */ LoadingProvider),\n/* harmony export */   useLoading: () => (/* binding */ useLoading)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n// context/LoadingContext.js\n\n\nconst LoadingContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)();\nconst LoadingProvider = ({ children })=>{\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(LoadingContext.Provider, {\n        value: {\n            isLoading,\n            setIsLoading\n        },\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\utils\\\\loaders\\\\loadingContext.js\",\n        lineNumber: 10,\n        columnNumber: 5\n    }, undefined);\n};\nconst useLoading = ()=>{\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(LoadingContext);\n    if (!context) {\n        throw new Error(\"useLoading must be used within a LoadingProvider\");\n    }\n    return context;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi91dGlscy9sb2FkZXJzL2xvYWRpbmdDb250ZXh0LmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7OztBQUFBLDRCQUE0Qjs7QUFDdUM7QUFFbkUsTUFBTUksK0JBQWlCSCxvREFBYUE7QUFFN0IsTUFBTUksa0JBQWtCLENBQUMsRUFBRUMsUUFBUSxFQUFFO0lBQzFDLE1BQU0sQ0FBQ0MsV0FBV0MsYUFBYSxHQUFHTCwrQ0FBUUEsQ0FBQztJQUUzQyxxQkFDRSw4REFBQ0MsZUFBZUssUUFBUTtRQUFDQyxPQUFPO1lBQUVIO1lBQVdDO1FBQWE7a0JBQ3ZERjs7Ozs7O0FBR1AsRUFBRTtBQUVLLE1BQU1LLGFBQWE7SUFDeEIsTUFBTUMsVUFBVVYsaURBQVVBLENBQUNFO0lBQzNCLElBQUksQ0FBQ1EsU0FBUztRQUNaLE1BQU0sSUFBSUMsTUFBTTtJQUNsQjtJQUNBLE9BQU9EO0FBQ1QsRUFBRSIsInNvdXJjZXMiOlsid2VicGFjazovL3RobHdlYmFwcC8uL3V0aWxzL2xvYWRlcnMvbG9hZGluZ0NvbnRleHQuanM/OTA5ZiJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBjb250ZXh0L0xvYWRpbmdDb250ZXh0LmpzXHJcbmltcG9ydCBSZWFjdCwgeyBjcmVhdGVDb250ZXh0LCB1c2VDb250ZXh0LCB1c2VTdGF0ZSB9IGZyb20gXCJyZWFjdFwiO1xyXG5cclxuY29uc3QgTG9hZGluZ0NvbnRleHQgPSBjcmVhdGVDb250ZXh0KCk7XHJcblxyXG5leHBvcnQgY29uc3QgTG9hZGluZ1Byb3ZpZGVyID0gKHsgY2hpbGRyZW4gfSkgPT4ge1xyXG4gIGNvbnN0IFtpc0xvYWRpbmcsIHNldElzTG9hZGluZ10gPSB1c2VTdGF0ZShmYWxzZSk7XHJcblxyXG4gIHJldHVybiAoXHJcbiAgICA8TG9hZGluZ0NvbnRleHQuUHJvdmlkZXIgdmFsdWU9e3sgaXNMb2FkaW5nLCBzZXRJc0xvYWRpbmcgfX0+XHJcbiAgICAgIHtjaGlsZHJlbn1cclxuICAgIDwvTG9hZGluZ0NvbnRleHQuUHJvdmlkZXI+XHJcbiAgKTtcclxufTtcclxuXHJcbmV4cG9ydCBjb25zdCB1c2VMb2FkaW5nID0gKCkgPT4ge1xyXG4gIGNvbnN0IGNvbnRleHQgPSB1c2VDb250ZXh0KExvYWRpbmdDb250ZXh0KTtcclxuICBpZiAoIWNvbnRleHQpIHtcclxuICAgIHRocm93IG5ldyBFcnJvcihcInVzZUxvYWRpbmcgbXVzdCBiZSB1c2VkIHdpdGhpbiBhIExvYWRpbmdQcm92aWRlclwiKTtcclxuICB9XHJcbiAgcmV0dXJuIGNvbnRleHQ7XHJcbn07XHJcbiJdLCJuYW1lcyI6WyJSZWFjdCIsImNyZWF0ZUNvbnRleHQiLCJ1c2VDb250ZXh0IiwidXNlU3RhdGUiLCJMb2FkaW5nQ29udGV4dCIsIkxvYWRpbmdQcm92aWRlciIsImNoaWxkcmVuIiwiaXNMb2FkaW5nIiwic2V0SXNMb2FkaW5nIiwiUHJvdmlkZXIiLCJ2YWx1ZSIsInVzZUxvYWRpbmciLCJjb250ZXh0IiwiRXJyb3IiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///./utils/loaders/loadingContext.js\n");

/***/ }),

/***/ "./utils/loaders/overlaySpinner.js":
/*!*****************************************!*\
  !*** ./utils/loaders/overlaySpinner.js ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react_loader_spinner__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-loader-spinner */ \"react-loader-spinner\");\n/* harmony import */ var react_loader_spinner__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react_loader_spinner__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _loadingContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./loadingContext */ \"./utils/loaders/loadingContext.js\");\n// components/OverlaySpinner.js\n\n\n\n\nconst OverlaySpinner = ()=>{\n    const { isLoading } = (0,_loadingContext__WEBPACK_IMPORTED_MODULE_3__.useLoading)();\n    return isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        style: {\n            display: \"flex\",\n            alignItems: \"center\",\n            justifyContent: \"center\",\n            height: \"100vh\",\n            width: \"100vw\",\n            position: \"fixed\",\n            backgroundColor: \"white\",\n            zIndex: 999999\n        },\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_loader_spinner__WEBPACK_IMPORTED_MODULE_2__.ThreeCircles, {\n            color: \"#002D73\",\n            height: 50,\n            width: 50,\n            visible: isLoading,\n            ariaLabel: \"oval-loading\",\n            secondaryColor: \"#0066FF\",\n            strokeWidth: 2,\n            strokeWidthSecondary: 2\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\utils\\\\loaders\\\\overlaySpinner.js\",\n            lineNumber: 21,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\utils\\\\loaders\\\\overlaySpinner.js\",\n        lineNumber: 9,\n        columnNumber: 5\n    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {}, void 0, false);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (OverlaySpinner);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./utils/loaders/overlaySpinner.js\n");

/***/ }),

/***/ "./utils/renderer/departmentRenderer.js":
/*!**********************************************!*\
  !*** ./utils/renderer/departmentRenderer.js ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n\n\nconst colorMap = {\n    1: {\n        background: \"rgba(62, 171, 88, 0.2)\",\n        text: \"#3EAB58\"\n    },\n    2: {\n        background: \"rgba(0, 102, 255, 0.2)\",\n        text: \"#0066FF\"\n    },\n    3: {\n        background: \"rgb(211, 211, 211, 0.7)\",\n        text: \"#000000\"\n    },\n    8: {\n        background: \"rgba(102, 100, 227, 0.2)\",\n        text: \"#592cf2\"\n    },\n    5: {\n        background: \"rgba(255, 212, 169, 0.5)\",\n        text: \"#b37858\"\n    },\n    default: {\n        background: \"transparent\",\n        text: \"#9A9A9A\"\n    }\n};\nconst departmentRenderer = (params)=>{\n    const department = params.value;\n    const { background, text } = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>colorMap[department] || colorMap.default, [\n        department\n    ]);\n    const spanStyle = {\n        backgroundColor: background,\n        width: \"95px\",\n        textAlign: \"center\",\n        display: \"inline-block\",\n        verticalAlign: \"middle\",\n        lineHeight: \"24px\",\n        height: \"32px\",\n        color: text,\n        padding: \"6px\",\n        borderRadius: \"10px\"\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n        style: spanStyle,\n        children: department == 1 ? \"Sales\" : department == 2 ? \"Procurement\" : department == 3 ? \"Financial\" : department == 5 ? \"Technical\" : department == 8 ? \"Logistics\" : \"\"\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\utils\\\\renderer\\\\departmentRenderer.js\",\n        lineNumber: 51,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (departmentRenderer);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./utils/renderer/departmentRenderer.js\n");

/***/ }),

/***/ "./utils/renderer/roleRenderer.js":
/*!****************************************!*\
  !*** ./utils/renderer/roleRenderer.js ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n\n\nconst colorMap = {\n    1: {\n        background: \"rgba(62, 171, 88, 0.2)\",\n        text: \"#3EAB58\"\n    },\n    2: {\n        background: \"rgba(0, 102, 255, 0.2)\",\n        text: \"#0066FF\"\n    },\n    4: {\n        background: \"rgb(211, 211, 211, 0.7)\",\n        text: \"#000000\"\n    },\n    5: {\n        background: \"rgba(62, 171, 88, 0.2)\",\n        text: \"#3EAB58\"\n    },\n    6: {\n        background: \"rgba(62, 171, 88, 0.2)\",\n        text: \"#3EAB58\"\n    },\n    default: {\n        background: \"rgba(154, 154, 154, 0.2)\",\n        text: \"#9A9A9A\"\n    }\n};\nconst roleRenderer = (params)=>{\n    const role = params.value;\n    const { background, text } = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>colorMap[role] || colorMap.default, [\n        role\n    ]);\n    // if (role === 4) {\n    //   return null; // Return null to hide the component\n    // }\n    const spanStyle = {\n        backgroundColor: background,\n        width: \"95px\",\n        textAlign: \"center\",\n        display: \"inline-block\",\n        verticalAlign: \"middle\",\n        lineHeight: \"24px\",\n        height: \"32px\",\n        color: text,\n        padding: \"6px\",\n        borderRadius: \"10px\"\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n        style: spanStyle,\n        children: role == 1 ? \"Administrator\" : role === 2 ? \"Approver\" : role === 5 ? \"THL Admin\" : role === 6 ? \"Super Admin\" : \"User\"\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\utils\\\\renderer\\\\roleRenderer.js\",\n        lineNumber: 54,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (roleRenderer);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./utils/renderer/roleRenderer.js\n");

/***/ }),

/***/ "./utils/rolePermissionsContext.js":
/*!*****************************************!*\
  !*** ./utils/rolePermissionsContext.js ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   PermissionsProvider: () => (/* binding */ PermissionsProvider),\n/* harmony export */   usePermissions: () => (/* binding */ usePermissions)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n\n\nconst PermissionsContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)();\nconst PermissionsProvider = ({ children })=>{\n    const [permissions, setPermissions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const updatePermissions = (newPermissions)=>{\n        setPermissions(newPermissions);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(PermissionsContext.Provider, {\n        value: {\n            permissions,\n            updatePermissions\n        },\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\utils\\\\rolePermissionsContext.js\",\n        lineNumber: 13,\n        columnNumber: 5\n    }, undefined);\n};\nconst usePermissions = ()=>{\n    return (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(PermissionsContext);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi91dGlscy9yb2xlUGVybWlzc2lvbnNDb250ZXh0LmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7QUFBNEQ7QUFFNUQsTUFBTUcsbUNBQXFCSCxvREFBYUE7QUFFakMsTUFBTUksc0JBQXNCLENBQUMsRUFBRUMsUUFBUSxFQUFFO0lBQzlDLE1BQU0sQ0FBQ0MsYUFBYUMsZUFBZSxHQUFHTCwrQ0FBUUEsQ0FBQyxDQUFDO0lBRWhELE1BQU1NLG9CQUFvQixDQUFDQztRQUN6QkYsZUFBZUU7SUFDakI7SUFFQSxxQkFDRSw4REFBQ04sbUJBQW1CTyxRQUFRO1FBQUNDLE9BQU87WUFBRUw7WUFBYUU7UUFBa0I7a0JBQ2xFSDs7Ozs7O0FBR1AsRUFBRTtBQUVLLE1BQU1PLGlCQUFpQjtJQUM1QixPQUFPWCxpREFBVUEsQ0FBQ0U7QUFDcEIsRUFBRSIsInNvdXJjZXMiOlsid2VicGFjazovL3RobHdlYmFwcC8uL3V0aWxzL3JvbGVQZXJtaXNzaW9uc0NvbnRleHQuanM/ZjdhYiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBjcmVhdGVDb250ZXh0LCB1c2VDb250ZXh0LCB1c2VTdGF0ZSB9IGZyb20gJ3JlYWN0JztcclxuXHJcbmNvbnN0IFBlcm1pc3Npb25zQ29udGV4dCA9IGNyZWF0ZUNvbnRleHQoKTtcclxuXHJcbmV4cG9ydCBjb25zdCBQZXJtaXNzaW9uc1Byb3ZpZGVyID0gKHsgY2hpbGRyZW4gfSkgPT4ge1xyXG4gIGNvbnN0IFtwZXJtaXNzaW9ucywgc2V0UGVybWlzc2lvbnNdID0gdXNlU3RhdGUoe30pO1xyXG5cclxuICBjb25zdCB1cGRhdGVQZXJtaXNzaW9ucyA9IChuZXdQZXJtaXNzaW9ucykgPT4ge1xyXG4gICAgc2V0UGVybWlzc2lvbnMobmV3UGVybWlzc2lvbnMpO1xyXG4gIH07XHJcblxyXG4gIHJldHVybiAoXHJcbiAgICA8UGVybWlzc2lvbnNDb250ZXh0LlByb3ZpZGVyIHZhbHVlPXt7IHBlcm1pc3Npb25zLCB1cGRhdGVQZXJtaXNzaW9ucyB9fT5cclxuICAgICAge2NoaWxkcmVufVxyXG4gICAgPC9QZXJtaXNzaW9uc0NvbnRleHQuUHJvdmlkZXI+XHJcbiAgKTtcclxufTtcclxuXHJcbmV4cG9ydCBjb25zdCB1c2VQZXJtaXNzaW9ucyA9ICgpID0+IHtcclxuICByZXR1cm4gdXNlQ29udGV4dChQZXJtaXNzaW9uc0NvbnRleHQpO1xyXG59O1xyXG4iXSwibmFtZXMiOlsiY3JlYXRlQ29udGV4dCIsInVzZUNvbnRleHQiLCJ1c2VTdGF0ZSIsIlBlcm1pc3Npb25zQ29udGV4dCIsIlBlcm1pc3Npb25zUHJvdmlkZXIiLCJjaGlsZHJlbiIsInBlcm1pc3Npb25zIiwic2V0UGVybWlzc2lvbnMiLCJ1cGRhdGVQZXJtaXNzaW9ucyIsIm5ld1Blcm1pc3Npb25zIiwiUHJvdmlkZXIiLCJ2YWx1ZSIsInVzZVBlcm1pc3Npb25zIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///./utils/rolePermissionsContext.js\n");

/***/ }),

/***/ "./utils/securePermissions.js":
/*!************************************!*\
  !*** ./utils/securePermissions.js ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   PAGE_PERMISSIONS: () => (/* binding */ PAGE_PERMISSIONS),\n/* harmony export */   calculatePermissions: () => (/* binding */ calculatePermissions),\n/* harmony export */   useSecurePermissions: () => (/* binding */ useSecurePermissions),\n/* harmony export */   validatePagePermissions: () => (/* binding */ validatePagePermissions)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n\n// Centralized permission logic that matches SideBarLinks.js\nconst calculatePermissions = (userData, company, availableModules)=>{\n    if (!userData) return {};\n    const { role_id, department_id } = userData;\n    const modules = availableModules || [];\n    return {\n        // Module-based permissions\n        canViewSuppliers: modules.includes(\"supplier\"),\n        canViewProducts: modules.includes(\"products\") && department_id !== 5,\n        canViewWhatif: modules.includes(\"whatif\") && [\n            \"efcltd\",\n            \"flrs\",\n            \"thl\"\n        ].includes(company) && [\n            1,\n            2\n        ].includes(department_id),\n        canViewServiceLevel: modules.includes(\"serviceLevel\") && [\n            1,\n            2\n        ].includes(department_id),\n        canViewUsers: modules.includes(\"users\") && [\n            1,\n            5,\n            6\n        ].includes(role_id),\n        canViewLogs: modules.includes(\"logs\") && [\n            1,\n            5,\n            6\n        ].includes(role_id),\n        // Role-based permissions\n        isAdmin: role_id === 1,\n        isSuperAdmin: role_id === 6,\n        isTHLAdmin: role_id === 5,\n        isApprover: role_id === 2,\n        canEditSuppliers: [\n            1,\n            2\n        ].includes(role_id),\n        canManageUsers: [\n            1,\n            5,\n            6\n        ].includes(role_id),\n        // Department-based permissions\n        isCommercial: [\n            1,\n            2\n        ].includes(department_id),\n        isProduction: department_id === 5,\n        // Raw data for custom checks\n        role_id,\n        department_id,\n        company\n    };\n};\n// Secure permission hook\nconst useSecurePermissions = (userData)=>{\n    const [permissions, setPermissions] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)({});\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(true);\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        const calculateUserPermissions = async ()=>{\n            if (!userData) {\n                setPermissions({});\n                setIsLoading(false);\n                return;\n            }\n            try {\n                // Get available modules from environment\n                const availableModules = \"supplier,products,whatif,serviceLevel,users,logs\"?.split(\",\") || 0;\n                // Get company from secure user data\n                const company = userData.company;\n                // Calculate permissions\n                const userPermissions = calculatePermissions(userData, company, availableModules);\n                setPermissions(userPermissions);\n            } catch (error) {\n                console.error(\"Error calculating permissions:\", error);\n                setPermissions({});\n            } finally{\n                setIsLoading(false);\n            }\n        };\n        calculateUserPermissions();\n    }, [\n        userData\n    ]);\n    return {\n        permissions,\n        isLoading\n    };\n};\n// Server-side permission validation\nconst validatePagePermissions = (userData, requiredPermissions)=>{\n    if (!userData) return false;\n    const availableModules = \"supplier,products,whatif,serviceLevel,users,logs\"?.split(\",\") || 0;\n    const permissions = calculatePermissions(userData, userData.company, availableModules);\n    // Check if user has all required permissions\n    return requiredPermissions.every((permission)=>{\n        if (typeof permission === \"string\") {\n            return permissions[permission] === true;\n        }\n        if (typeof permission === \"object\") {\n            const { type, values } = permission;\n            if (type === \"role_id\") {\n                return values.includes(userData.role_id);\n            }\n            if (type === \"department_id\") {\n                return values.includes(userData.department_id);\n            }\n            if (type === \"company\") {\n                return values.includes(userData.company);\n            }\n        }\n        return false;\n    });\n};\n// Page-specific permission definitions\nconst PAGE_PERMISSIONS = {\n    suppliers: [\n        \"canViewSuppliers\"\n    ],\n    products: [\n        \"canViewProducts\"\n    ],\n    whatif: [\n        \"canViewWhatif\"\n    ],\n    serviceLevel: [\n        \"canViewServiceLevel\"\n    ],\n    users: [\n        \"canViewUsers\"\n    ],\n    logs: [\n        \"canViewLogs\"\n    ],\n    // Custom permission combinations\n    \"supplier-edit\": [\n        \"canViewSuppliers\",\n        \"canEditSuppliers\"\n    ],\n    \"admin-only\": [\n        {\n            type: \"role_id\",\n            values: [\n                1\n            ]\n        }\n    ],\n    \"super-admin-only\": [\n        {\n            type: \"role_id\",\n            values: [\n                1,\n                6\n            ]\n        }\n    ]\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi91dGlscy9zZWN1cmVQZXJtaXNzaW9ucy5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7QUFBNEM7QUFFNUMsNERBQTREO0FBQ3JELE1BQU1FLHVCQUF1QixDQUFDQyxVQUFVQyxTQUFTQztJQUN0RCxJQUFJLENBQUNGLFVBQVUsT0FBTyxDQUFDO0lBRXZCLE1BQU0sRUFBRUcsT0FBTyxFQUFFQyxhQUFhLEVBQUUsR0FBR0o7SUFDbkMsTUFBTUssVUFBVUgsb0JBQW9CLEVBQUU7SUFFdEMsT0FBTztRQUNMLDJCQUEyQjtRQUMzQkksa0JBQWtCRCxRQUFRRSxRQUFRLENBQUM7UUFDbkNDLGlCQUFpQkgsUUFBUUUsUUFBUSxDQUFDLGVBQWVILGtCQUFrQjtRQUNuRUssZUFBZUosUUFBUUUsUUFBUSxDQUFDLGFBQ2xCO1lBQUM7WUFBVTtZQUFRO1NBQU0sQ0FBQ0EsUUFBUSxDQUFDTixZQUNuQztZQUFDO1lBQUc7U0FBRSxDQUFDTSxRQUFRLENBQUNIO1FBQzlCTSxxQkFBcUJMLFFBQVFFLFFBQVEsQ0FBQyxtQkFDbEI7WUFBQztZQUFHO1NBQUUsQ0FBQ0EsUUFBUSxDQUFDSDtRQUNwQ08sY0FBY04sUUFBUUUsUUFBUSxDQUFDLFlBQ2xCO1lBQUM7WUFBRztZQUFHO1NBQUUsQ0FBQ0EsUUFBUSxDQUFDSjtRQUNoQ1MsYUFBYVAsUUFBUUUsUUFBUSxDQUFDLFdBQ2xCO1lBQUM7WUFBRztZQUFHO1NBQUUsQ0FBQ0EsUUFBUSxDQUFDSjtRQUUvQix5QkFBeUI7UUFDekJVLFNBQVNWLFlBQVk7UUFDckJXLGNBQWNYLFlBQVk7UUFDMUJZLFlBQVlaLFlBQVk7UUFDeEJhLFlBQVliLFlBQVk7UUFDeEJjLGtCQUFrQjtZQUFDO1lBQUc7U0FBRSxDQUFDVixRQUFRLENBQUNKO1FBQ2xDZSxnQkFBZ0I7WUFBQztZQUFHO1lBQUc7U0FBRSxDQUFDWCxRQUFRLENBQUNKO1FBRW5DLCtCQUErQjtRQUMvQmdCLGNBQWM7WUFBQztZQUFHO1NBQUUsQ0FBQ1osUUFBUSxDQUFDSDtRQUM5QmdCLGNBQWNoQixrQkFBa0I7UUFFaEMsNkJBQTZCO1FBQzdCRDtRQUNBQztRQUNBSDtJQUNGO0FBQ0YsRUFBRTtBQUVGLHlCQUF5QjtBQUNsQixNQUFNb0IsdUJBQXVCLENBQUNyQjtJQUNuQyxNQUFNLENBQUNzQixhQUFhQyxlQUFlLEdBQUcxQiwrQ0FBUUEsQ0FBQyxDQUFDO0lBQ2hELE1BQU0sQ0FBQzJCLFdBQVdDLGFBQWEsR0FBRzVCLCtDQUFRQSxDQUFDO0lBRTNDQyxnREFBU0EsQ0FBQztRQUNSLE1BQU00QiwyQkFBMkI7WUFDL0IsSUFBSSxDQUFDMUIsVUFBVTtnQkFDYnVCLGVBQWUsQ0FBQztnQkFDaEJFLGFBQWE7Z0JBQ2I7WUFDRjtZQUVBLElBQUk7Z0JBQ0YseUNBQXlDO2dCQUN6QyxNQUFNdkIsbUJBQW1CeUIsa0RBQXlDLEVBQUVHLE1BQU0sUUFBUSxDQUFFO2dCQUVwRixvQ0FBb0M7Z0JBQ3BDLE1BQU03QixVQUFVRCxTQUFTQyxPQUFPO2dCQUVoQyx3QkFBd0I7Z0JBQ3hCLE1BQU04QixrQkFBa0JoQyxxQkFBcUJDLFVBQVVDLFNBQVNDO2dCQUVoRXFCLGVBQWVRO1lBQ2pCLEVBQUUsT0FBT0MsT0FBTztnQkFDZEMsUUFBUUQsS0FBSyxDQUFDLGtDQUFrQ0E7Z0JBQ2hEVCxlQUFlLENBQUM7WUFDbEIsU0FBVTtnQkFDUkUsYUFBYTtZQUNmO1FBQ0Y7UUFFQUM7SUFDRixHQUFHO1FBQUMxQjtLQUFTO0lBRWIsT0FBTztRQUFFc0I7UUFBYUU7SUFBVTtBQUNsQyxFQUFFO0FBRUYsb0NBQW9DO0FBQzdCLE1BQU1VLDBCQUEwQixDQUFDbEMsVUFBVW1DO0lBQ2hELElBQUksQ0FBQ25DLFVBQVUsT0FBTztJQUV0QixNQUFNRSxtQkFBbUJ5QixrREFBeUMsRUFBRUcsTUFBTSxRQUFRLENBQUU7SUFDcEYsTUFBTVIsY0FBY3ZCLHFCQUFxQkMsVUFBVUEsU0FBU0MsT0FBTyxFQUFFQztJQUVyRSw2Q0FBNkM7SUFDN0MsT0FBT2lDLG9CQUFvQkMsS0FBSyxDQUFDQyxDQUFBQTtRQUMvQixJQUFJLE9BQU9BLGVBQWUsVUFBVTtZQUNsQyxPQUFPZixXQUFXLENBQUNlLFdBQVcsS0FBSztRQUNyQztRQUVBLElBQUksT0FBT0EsZUFBZSxVQUFVO1lBQ2xDLE1BQU0sRUFBRUMsSUFBSSxFQUFFQyxNQUFNLEVBQUUsR0FBR0Y7WUFFekIsSUFBSUMsU0FBUyxXQUFXO2dCQUN0QixPQUFPQyxPQUFPaEMsUUFBUSxDQUFDUCxTQUFTRyxPQUFPO1lBQ3pDO1lBRUEsSUFBSW1DLFNBQVMsaUJBQWlCO2dCQUM1QixPQUFPQyxPQUFPaEMsUUFBUSxDQUFDUCxTQUFTSSxhQUFhO1lBQy9DO1lBRUEsSUFBSWtDLFNBQVMsV0FBVztnQkFDdEIsT0FBT0MsT0FBT2hDLFFBQVEsQ0FBQ1AsU0FBU0MsT0FBTztZQUN6QztRQUNGO1FBRUEsT0FBTztJQUNUO0FBQ0YsRUFBRTtBQUVGLHVDQUF1QztBQUNoQyxNQUFNdUMsbUJBQW1CO0lBQzlCQyxXQUFXO1FBQUM7S0FBbUI7SUFDL0JDLFVBQVU7UUFBQztLQUFrQjtJQUM3QkMsUUFBUTtRQUFDO0tBQWdCO0lBQ3pCQyxjQUFjO1FBQUM7S0FBc0I7SUFDckNDLE9BQU87UUFBQztLQUFlO0lBQ3ZCQyxNQUFNO1FBQUM7S0FBYztJQUVyQixpQ0FBaUM7SUFDakMsaUJBQWlCO1FBQUM7UUFBb0I7S0FBbUI7SUFDekQsY0FBYztRQUFDO1lBQUVSLE1BQU07WUFBV0MsUUFBUTtnQkFBQzthQUFFO1FBQUM7S0FBRTtJQUNoRCxvQkFBb0I7UUFBQztZQUFFRCxNQUFNO1lBQVdDLFFBQVE7Z0JBQUM7Z0JBQUc7YUFBRTtRQUFDO0tBQUU7QUFDM0QsRUFBRSIsInNvdXJjZXMiOlsid2VicGFjazovL3RobHdlYmFwcC8uL3V0aWxzL3NlY3VyZVBlcm1pc3Npb25zLmpzP2E5YWUiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgdXNlU3RhdGUsIHVzZUVmZmVjdCB9IGZyb20gJ3JlYWN0JztcclxuXHJcbi8vIENlbnRyYWxpemVkIHBlcm1pc3Npb24gbG9naWMgdGhhdCBtYXRjaGVzIFNpZGVCYXJMaW5rcy5qc1xyXG5leHBvcnQgY29uc3QgY2FsY3VsYXRlUGVybWlzc2lvbnMgPSAodXNlckRhdGEsIGNvbXBhbnksIGF2YWlsYWJsZU1vZHVsZXMpID0+IHtcclxuICBpZiAoIXVzZXJEYXRhKSByZXR1cm4ge307XHJcblxyXG4gIGNvbnN0IHsgcm9sZV9pZCwgZGVwYXJ0bWVudF9pZCB9ID0gdXNlckRhdGE7XHJcbiAgY29uc3QgbW9kdWxlcyA9IGF2YWlsYWJsZU1vZHVsZXMgfHwgW107XHJcblxyXG4gIHJldHVybiB7XHJcbiAgICAvLyBNb2R1bGUtYmFzZWQgcGVybWlzc2lvbnNcclxuICAgIGNhblZpZXdTdXBwbGllcnM6IG1vZHVsZXMuaW5jbHVkZXMoXCJzdXBwbGllclwiKSxcclxuICAgIGNhblZpZXdQcm9kdWN0czogbW9kdWxlcy5pbmNsdWRlcyhcInByb2R1Y3RzXCIpICYmIGRlcGFydG1lbnRfaWQgIT09IDUsXHJcbiAgICBjYW5WaWV3V2hhdGlmOiBtb2R1bGVzLmluY2x1ZGVzKFwid2hhdGlmXCIpICYmIFxyXG4gICAgICAgICAgICAgICAgICBbXCJlZmNsdGRcIiwgXCJmbHJzXCIsIFwidGhsXCJdLmluY2x1ZGVzKGNvbXBhbnkpICYmXHJcbiAgICAgICAgICAgICAgICAgIFsxLCAyXS5pbmNsdWRlcyhkZXBhcnRtZW50X2lkKSxcclxuICAgIGNhblZpZXdTZXJ2aWNlTGV2ZWw6IG1vZHVsZXMuaW5jbHVkZXMoXCJzZXJ2aWNlTGV2ZWxcIikgJiYgXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIFsxLCAyXS5pbmNsdWRlcyhkZXBhcnRtZW50X2lkKSxcclxuICAgIGNhblZpZXdVc2VyczogbW9kdWxlcy5pbmNsdWRlcyhcInVzZXJzXCIpICYmIFxyXG4gICAgICAgICAgICAgICAgIFsxLCA1LCA2XS5pbmNsdWRlcyhyb2xlX2lkKSxcclxuICAgIGNhblZpZXdMb2dzOiBtb2R1bGVzLmluY2x1ZGVzKFwibG9nc1wiKSAmJiBcclxuICAgICAgICAgICAgICAgIFsxLCA1LCA2XS5pbmNsdWRlcyhyb2xlX2lkKSxcclxuXHJcbiAgICAvLyBSb2xlLWJhc2VkIHBlcm1pc3Npb25zXHJcbiAgICBpc0FkbWluOiByb2xlX2lkID09PSAxLFxyXG4gICAgaXNTdXBlckFkbWluOiByb2xlX2lkID09PSA2LFxyXG4gICAgaXNUSExBZG1pbjogcm9sZV9pZCA9PT0gNSxcclxuICAgIGlzQXBwcm92ZXI6IHJvbGVfaWQgPT09IDIsXHJcbiAgICBjYW5FZGl0U3VwcGxpZXJzOiBbMSwgMl0uaW5jbHVkZXMocm9sZV9pZCksXHJcbiAgICBjYW5NYW5hZ2VVc2VyczogWzEsIDUsIDZdLmluY2x1ZGVzKHJvbGVfaWQpLFxyXG5cclxuICAgIC8vIERlcGFydG1lbnQtYmFzZWQgcGVybWlzc2lvbnNcclxuICAgIGlzQ29tbWVyY2lhbDogWzEsIDJdLmluY2x1ZGVzKGRlcGFydG1lbnRfaWQpLFxyXG4gICAgaXNQcm9kdWN0aW9uOiBkZXBhcnRtZW50X2lkID09PSA1LFxyXG5cclxuICAgIC8vIFJhdyBkYXRhIGZvciBjdXN0b20gY2hlY2tzXHJcbiAgICByb2xlX2lkLFxyXG4gICAgZGVwYXJ0bWVudF9pZCxcclxuICAgIGNvbXBhbnlcclxuICB9O1xyXG59O1xyXG5cclxuLy8gU2VjdXJlIHBlcm1pc3Npb24gaG9va1xyXG5leHBvcnQgY29uc3QgdXNlU2VjdXJlUGVybWlzc2lvbnMgPSAodXNlckRhdGEpID0+IHtcclxuICBjb25zdCBbcGVybWlzc2lvbnMsIHNldFBlcm1pc3Npb25zXSA9IHVzZVN0YXRlKHt9KTtcclxuICBjb25zdCBbaXNMb2FkaW5nLCBzZXRJc0xvYWRpbmddID0gdXNlU3RhdGUodHJ1ZSk7XHJcblxyXG4gIHVzZUVmZmVjdCgoKSA9PiB7XHJcbiAgICBjb25zdCBjYWxjdWxhdGVVc2VyUGVybWlzc2lvbnMgPSBhc3luYyAoKSA9PiB7XHJcbiAgICAgIGlmICghdXNlckRhdGEpIHtcclxuICAgICAgICBzZXRQZXJtaXNzaW9ucyh7fSk7XHJcbiAgICAgICAgc2V0SXNMb2FkaW5nKGZhbHNlKTtcclxuICAgICAgICByZXR1cm47XHJcbiAgICAgIH1cclxuXHJcbiAgICAgIHRyeSB7XHJcbiAgICAgICAgLy8gR2V0IGF2YWlsYWJsZSBtb2R1bGVzIGZyb20gZW52aXJvbm1lbnRcclxuICAgICAgICBjb25zdCBhdmFpbGFibGVNb2R1bGVzID0gcHJvY2Vzcy5lbnYuTkVYVF9QVUJMSUNfQVZBSUxBQkxFX01PRFVMRVM/LnNwbGl0KFwiLFwiKSB8fCBbXTtcclxuICAgICAgICBcclxuICAgICAgICAvLyBHZXQgY29tcGFueSBmcm9tIHNlY3VyZSB1c2VyIGRhdGFcclxuICAgICAgICBjb25zdCBjb21wYW55ID0gdXNlckRhdGEuY29tcGFueTtcclxuICAgICAgICBcclxuICAgICAgICAvLyBDYWxjdWxhdGUgcGVybWlzc2lvbnNcclxuICAgICAgICBjb25zdCB1c2VyUGVybWlzc2lvbnMgPSBjYWxjdWxhdGVQZXJtaXNzaW9ucyh1c2VyRGF0YSwgY29tcGFueSwgYXZhaWxhYmxlTW9kdWxlcyk7XHJcbiAgICAgICAgXHJcbiAgICAgICAgc2V0UGVybWlzc2lvbnModXNlclBlcm1pc3Npb25zKTtcclxuICAgICAgfSBjYXRjaCAoZXJyb3IpIHtcclxuICAgICAgICBjb25zb2xlLmVycm9yKCdFcnJvciBjYWxjdWxhdGluZyBwZXJtaXNzaW9uczonLCBlcnJvcik7XHJcbiAgICAgICAgc2V0UGVybWlzc2lvbnMoe30pO1xyXG4gICAgICB9IGZpbmFsbHkge1xyXG4gICAgICAgIHNldElzTG9hZGluZyhmYWxzZSk7XHJcbiAgICAgIH1cclxuICAgIH07XHJcblxyXG4gICAgY2FsY3VsYXRlVXNlclBlcm1pc3Npb25zKCk7XHJcbiAgfSwgW3VzZXJEYXRhXSk7XHJcblxyXG4gIHJldHVybiB7IHBlcm1pc3Npb25zLCBpc0xvYWRpbmcgfTtcclxufTtcclxuXHJcbi8vIFNlcnZlci1zaWRlIHBlcm1pc3Npb24gdmFsaWRhdGlvblxyXG5leHBvcnQgY29uc3QgdmFsaWRhdGVQYWdlUGVybWlzc2lvbnMgPSAodXNlckRhdGEsIHJlcXVpcmVkUGVybWlzc2lvbnMpID0+IHtcclxuICBpZiAoIXVzZXJEYXRhKSByZXR1cm4gZmFsc2U7XHJcbiAgXHJcbiAgY29uc3QgYXZhaWxhYmxlTW9kdWxlcyA9IHByb2Nlc3MuZW52Lk5FWFRfUFVCTElDX0FWQUlMQUJMRV9NT0RVTEVTPy5zcGxpdChcIixcIikgfHwgW107XHJcbiAgY29uc3QgcGVybWlzc2lvbnMgPSBjYWxjdWxhdGVQZXJtaXNzaW9ucyh1c2VyRGF0YSwgdXNlckRhdGEuY29tcGFueSwgYXZhaWxhYmxlTW9kdWxlcyk7XHJcbiAgXHJcbiAgLy8gQ2hlY2sgaWYgdXNlciBoYXMgYWxsIHJlcXVpcmVkIHBlcm1pc3Npb25zXHJcbiAgcmV0dXJuIHJlcXVpcmVkUGVybWlzc2lvbnMuZXZlcnkocGVybWlzc2lvbiA9PiB7XHJcbiAgICBpZiAodHlwZW9mIHBlcm1pc3Npb24gPT09ICdzdHJpbmcnKSB7XHJcbiAgICAgIHJldHVybiBwZXJtaXNzaW9uc1twZXJtaXNzaW9uXSA9PT0gdHJ1ZTtcclxuICAgIH1cclxuICAgIFxyXG4gICAgaWYgKHR5cGVvZiBwZXJtaXNzaW9uID09PSAnb2JqZWN0Jykge1xyXG4gICAgICBjb25zdCB7IHR5cGUsIHZhbHVlcyB9ID0gcGVybWlzc2lvbjtcclxuICAgICAgXHJcbiAgICAgIGlmICh0eXBlID09PSAncm9sZV9pZCcpIHtcclxuICAgICAgICByZXR1cm4gdmFsdWVzLmluY2x1ZGVzKHVzZXJEYXRhLnJvbGVfaWQpO1xyXG4gICAgICB9XHJcbiAgICAgIFxyXG4gICAgICBpZiAodHlwZSA9PT0gJ2RlcGFydG1lbnRfaWQnKSB7XHJcbiAgICAgICAgcmV0dXJuIHZhbHVlcy5pbmNsdWRlcyh1c2VyRGF0YS5kZXBhcnRtZW50X2lkKTtcclxuICAgICAgfVxyXG4gICAgICBcclxuICAgICAgaWYgKHR5cGUgPT09ICdjb21wYW55Jykge1xyXG4gICAgICAgIHJldHVybiB2YWx1ZXMuaW5jbHVkZXModXNlckRhdGEuY29tcGFueSk7XHJcbiAgICAgIH1cclxuICAgIH1cclxuICAgIFxyXG4gICAgcmV0dXJuIGZhbHNlO1xyXG4gIH0pO1xyXG59O1xyXG5cclxuLy8gUGFnZS1zcGVjaWZpYyBwZXJtaXNzaW9uIGRlZmluaXRpb25zXHJcbmV4cG9ydCBjb25zdCBQQUdFX1BFUk1JU1NJT05TID0ge1xyXG4gIHN1cHBsaWVyczogWydjYW5WaWV3U3VwcGxpZXJzJ10sXHJcbiAgcHJvZHVjdHM6IFsnY2FuVmlld1Byb2R1Y3RzJ10sXHJcbiAgd2hhdGlmOiBbJ2NhblZpZXdXaGF0aWYnXSxcclxuICBzZXJ2aWNlTGV2ZWw6IFsnY2FuVmlld1NlcnZpY2VMZXZlbCddLFxyXG4gIHVzZXJzOiBbJ2NhblZpZXdVc2VycyddLFxyXG4gIGxvZ3M6IFsnY2FuVmlld0xvZ3MnXSxcclxuICBcclxuICAvLyBDdXN0b20gcGVybWlzc2lvbiBjb21iaW5hdGlvbnNcclxuICAnc3VwcGxpZXItZWRpdCc6IFsnY2FuVmlld1N1cHBsaWVycycsICdjYW5FZGl0U3VwcGxpZXJzJ10sXHJcbiAgJ2FkbWluLW9ubHknOiBbeyB0eXBlOiAncm9sZV9pZCcsIHZhbHVlczogWzFdIH1dLFxyXG4gICdzdXBlci1hZG1pbi1vbmx5JzogW3sgdHlwZTogJ3JvbGVfaWQnLCB2YWx1ZXM6IFsxLCA2XSB9XSxcclxufTsgIl0sIm5hbWVzIjpbInVzZVN0YXRlIiwidXNlRWZmZWN0IiwiY2FsY3VsYXRlUGVybWlzc2lvbnMiLCJ1c2VyRGF0YSIsImNvbXBhbnkiLCJhdmFpbGFibGVNb2R1bGVzIiwicm9sZV9pZCIsImRlcGFydG1lbnRfaWQiLCJtb2R1bGVzIiwiY2FuVmlld1N1cHBsaWVycyIsImluY2x1ZGVzIiwiY2FuVmlld1Byb2R1Y3RzIiwiY2FuVmlld1doYXRpZiIsImNhblZpZXdTZXJ2aWNlTGV2ZWwiLCJjYW5WaWV3VXNlcnMiLCJjYW5WaWV3TG9ncyIsImlzQWRtaW4iLCJpc1N1cGVyQWRtaW4iLCJpc1RITEFkbWluIiwiaXNBcHByb3ZlciIsImNhbkVkaXRTdXBwbGllcnMiLCJjYW5NYW5hZ2VVc2VycyIsImlzQ29tbWVyY2lhbCIsImlzUHJvZHVjdGlvbiIsInVzZVNlY3VyZVBlcm1pc3Npb25zIiwicGVybWlzc2lvbnMiLCJzZXRQZXJtaXNzaW9ucyIsImlzTG9hZGluZyIsInNldElzTG9hZGluZyIsImNhbGN1bGF0ZVVzZXJQZXJtaXNzaW9ucyIsInByb2Nlc3MiLCJlbnYiLCJORVhUX1BVQkxJQ19BVkFJTEFCTEVfTU9EVUxFUyIsInNwbGl0IiwidXNlclBlcm1pc3Npb25zIiwiZXJyb3IiLCJjb25zb2xlIiwidmFsaWRhdGVQYWdlUGVybWlzc2lvbnMiLCJyZXF1aXJlZFBlcm1pc3Npb25zIiwiZXZlcnkiLCJwZXJtaXNzaW9uIiwidHlwZSIsInZhbHVlcyIsIlBBR0VfUEVSTUlTU0lPTlMiLCJzdXBwbGllcnMiLCJwcm9kdWN0cyIsIndoYXRpZiIsInNlcnZpY2VMZXZlbCIsInVzZXJzIiwibG9ncyJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///./utils/securePermissions.js\n");

/***/ }),

/***/ "./utils/secureStorage.js":
/*!********************************!*\
  !*** ./utils/secureStorage.js ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   clearClientStorage: () => (/* binding */ clearClientStorage),\n/* harmony export */   getCurrentUser: () => (/* binding */ getCurrentUser),\n/* harmony export */   isAuthenticated: () => (/* binding */ isAuthenticated),\n/* harmony export */   login: () => (/* binding */ login),\n/* harmony export */   logout: () => (/* binding */ logout),\n/* harmony export */   refreshSession: () => (/* binding */ refreshSession)\n/* harmony export */ });\n// Simple module with functions - no classes\nconst apiBase = \"http://localhost:8081\" || 0;\n// Get current user from server session\nconst getCurrentUser = async ()=>{\n    try {\n        const response = await fetch(`${apiBase}/api/auth/me`, {\n            credentials: \"include\",\n            headers: {\n                \"Content-Type\": \"application/json\"\n            }\n        });\n        if (response.ok) {\n            const data = await response.json();\n            return data.user;\n        }\n        return null;\n    } catch (error) {\n        console.error(\"Failed to get current user:\", error);\n        return null;\n    }\n};\n// Login with MSAL token\nconst login = async (token, additionalData = {})=>{\n    try {\n        const response = await fetch(`${apiBase}/api/auth/login`, {\n            method: \"POST\",\n            headers: {\n                \"Authorization\": `Bearer ${token}`,\n                \"Content-Type\": \"application/json\"\n            },\n            credentials: \"include\",\n            body: JSON.stringify(additionalData)\n        });\n        if (response.ok) {\n            const data = await response.json();\n            return {\n                success: true,\n                user: data.user\n            };\n        } else {\n            const error = await response.json();\n            return {\n                success: false,\n                error: error.message\n            };\n        }\n    } catch (error) {\n        console.error(\"Login failed:\", error);\n        return {\n            success: false,\n            error: error.message\n        };\n    }\n};\n// Logout\nconst logout = async ()=>{\n    try {\n        await fetch(`${apiBase}/api/auth/logout`, {\n            method: \"POST\",\n            credentials: \"include\"\n        });\n        // Clear client-side data\n        clearClientStorage();\n        return true;\n    } catch (error) {\n        console.error(\"Logout failed:\", error);\n        return false;\n    }\n};\n// Clear all client-side storage\nconst clearClientStorage = ()=>{\n    const keysToRemove = [\n        \"superUser\",\n        \"company\",\n        \"id\",\n        \"name\",\n        \"role\",\n        \"email\",\n        \"allowedSections\"\n    ];\n    keysToRemove.forEach((key)=>localStorage.removeItem(key));\n};\n// Check if user is authenticated\nconst isAuthenticated = async ()=>{\n    const user = await getCurrentUser();\n    return !!user;\n};\n// Refresh session (extend expiry)\nconst refreshSession = async ()=>{\n    try {\n        const response = await fetch(`${apiBase}/api/auth/refresh`, {\n            method: \"POST\",\n            credentials: \"include\"\n        });\n        return response.ok;\n    } catch (error) {\n        console.error(\"Session refresh failed:\", error);\n        return false;\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi91dGlscy9zZWN1cmVTdG9yYWdlLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7OztBQUFBLDRDQUE0QztBQUU1QyxNQUFNQSxVQUFVQyx1QkFBb0MsSUFBSTtBQUV4RCx1Q0FBdUM7QUFDaEMsTUFBTUcsaUJBQWlCO0lBQzVCLElBQUk7UUFDRixNQUFNQyxXQUFXLE1BQU1DLE1BQU0sQ0FBQyxFQUFFTixRQUFRLFlBQVksQ0FBQyxFQUFFO1lBQ3JETyxhQUFhO1lBQ2JDLFNBQVM7Z0JBQ1AsZ0JBQWdCO1lBQ2xCO1FBQ0Y7UUFFQSxJQUFJSCxTQUFTSSxFQUFFLEVBQUU7WUFDZixNQUFNQyxPQUFPLE1BQU1MLFNBQVNNLElBQUk7WUFDaEMsT0FBT0QsS0FBS0UsSUFBSTtRQUNsQjtRQUVBLE9BQU87SUFDVCxFQUFFLE9BQU9DLE9BQU87UUFDZEMsUUFBUUQsS0FBSyxDQUFDLCtCQUErQkE7UUFDN0MsT0FBTztJQUNUO0FBQ0YsRUFBRTtBQUVGLHdCQUF3QjtBQUNqQixNQUFNRSxRQUFRLE9BQU9DLE9BQU9DLGlCQUFpQixDQUFDLENBQUM7SUFDcEQsSUFBSTtRQUNGLE1BQU1aLFdBQVcsTUFBTUMsTUFBTSxDQUFDLEVBQUVOLFFBQVEsZUFBZSxDQUFDLEVBQUU7WUFDeERrQixRQUFRO1lBQ1JWLFNBQVM7Z0JBQ1AsaUJBQWlCLENBQUMsT0FBTyxFQUFFUSxNQUFNLENBQUM7Z0JBQ2xDLGdCQUFnQjtZQUNsQjtZQUNBVCxhQUFhO1lBQ2JZLE1BQU1DLEtBQUtDLFNBQVMsQ0FBQ0o7UUFDdkI7UUFFQSxJQUFJWixTQUFTSSxFQUFFLEVBQUU7WUFDZixNQUFNQyxPQUFPLE1BQU1MLFNBQVNNLElBQUk7WUFDaEMsT0FBTztnQkFBRVcsU0FBUztnQkFBTVYsTUFBTUYsS0FBS0UsSUFBSTtZQUFDO1FBQzFDLE9BQU87WUFDTCxNQUFNQyxRQUFRLE1BQU1SLFNBQVNNLElBQUk7WUFDakMsT0FBTztnQkFBRVcsU0FBUztnQkFBT1QsT0FBT0EsTUFBTVUsT0FBTztZQUFDO1FBQ2hEO0lBQ0YsRUFBRSxPQUFPVixPQUFPO1FBQ2RDLFFBQVFELEtBQUssQ0FBQyxpQkFBaUJBO1FBQy9CLE9BQU87WUFBRVMsU0FBUztZQUFPVCxPQUFPQSxNQUFNVSxPQUFPO1FBQUM7SUFDaEQ7QUFDRixFQUFFO0FBRUYsU0FBUztBQUNGLE1BQU1DLFNBQVM7SUFDcEIsSUFBSTtRQUNGLE1BQU1sQixNQUFNLENBQUMsRUFBRU4sUUFBUSxnQkFBZ0IsQ0FBQyxFQUFFO1lBQ3hDa0IsUUFBUTtZQUNSWCxhQUFhO1FBQ2Y7UUFFQSx5QkFBeUI7UUFDekJrQjtRQUVBLE9BQU87SUFDVCxFQUFFLE9BQU9aLE9BQU87UUFDZEMsUUFBUUQsS0FBSyxDQUFDLGtCQUFrQkE7UUFDaEMsT0FBTztJQUNUO0FBQ0YsRUFBRTtBQUVGLGdDQUFnQztBQUN6QixNQUFNWSxxQkFBcUI7SUFDaEMsTUFBTUMsZUFBZTtRQUFDO1FBQWE7UUFBVztRQUFNO1FBQVE7UUFBUTtRQUFTO0tBQWtCO0lBQy9GQSxhQUFhQyxPQUFPLENBQUNDLENBQUFBLE1BQU9DLGFBQWFDLFVBQVUsQ0FBQ0Y7QUFDdEQsRUFBRTtBQUVGLGlDQUFpQztBQUMxQixNQUFNRyxrQkFBa0I7SUFDN0IsTUFBTW5CLE9BQU8sTUFBTVI7SUFDbkIsT0FBTyxDQUFDLENBQUNRO0FBQ1gsRUFBRTtBQUVGLGtDQUFrQztBQUMzQixNQUFNb0IsaUJBQWlCO0lBQzVCLElBQUk7UUFDRixNQUFNM0IsV0FBVyxNQUFNQyxNQUFNLENBQUMsRUFBRU4sUUFBUSxpQkFBaUIsQ0FBQyxFQUFFO1lBQzFEa0IsUUFBUTtZQUNSWCxhQUFhO1FBQ2Y7UUFFQSxPQUFPRixTQUFTSSxFQUFFO0lBQ3BCLEVBQUUsT0FBT0ksT0FBTztRQUNkQyxRQUFRRCxLQUFLLENBQUMsMkJBQTJCQTtRQUN6QyxPQUFPO0lBQ1Q7QUFDRixFQUFFIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vdGhsd2ViYXBwLy4vdXRpbHMvc2VjdXJlU3RvcmFnZS5qcz8wYWIzIl0sInNvdXJjZXNDb250ZW50IjpbIi8vIFNpbXBsZSBtb2R1bGUgd2l0aCBmdW5jdGlvbnMgLSBubyBjbGFzc2VzXHJcblxyXG5jb25zdCBhcGlCYXNlID0gcHJvY2Vzcy5lbnYuTkVYVF9QVUJMSUNfQVBJX0JBU0VfVVJMIHx8ICdodHRwOi8vbG9jYWxob3N0OjgwODEnO1xyXG5cclxuLy8gR2V0IGN1cnJlbnQgdXNlciBmcm9tIHNlcnZlciBzZXNzaW9uXHJcbmV4cG9ydCBjb25zdCBnZXRDdXJyZW50VXNlciA9IGFzeW5jICgpID0+IHtcclxuICB0cnkge1xyXG4gICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBmZXRjaChgJHthcGlCYXNlfS9hcGkvYXV0aC9tZWAsIHtcclxuICAgICAgY3JlZGVudGlhbHM6ICdpbmNsdWRlJyxcclxuICAgICAgaGVhZGVyczoge1xyXG4gICAgICAgICdDb250ZW50LVR5cGUnOiAnYXBwbGljYXRpb24vanNvbicsXHJcbiAgICAgIH0sXHJcbiAgICB9KTtcclxuXHJcbiAgICBpZiAocmVzcG9uc2Uub2spIHtcclxuICAgICAgY29uc3QgZGF0YSA9IGF3YWl0IHJlc3BvbnNlLmpzb24oKTtcclxuICAgICAgcmV0dXJuIGRhdGEudXNlcjtcclxuICAgIH1cclxuICAgIFxyXG4gICAgcmV0dXJuIG51bGw7XHJcbiAgfSBjYXRjaCAoZXJyb3IpIHtcclxuICAgIGNvbnNvbGUuZXJyb3IoJ0ZhaWxlZCB0byBnZXQgY3VycmVudCB1c2VyOicsIGVycm9yKTtcclxuICAgIHJldHVybiBudWxsO1xyXG4gIH1cclxufTtcclxuXHJcbi8vIExvZ2luIHdpdGggTVNBTCB0b2tlblxyXG5leHBvcnQgY29uc3QgbG9naW4gPSBhc3luYyAodG9rZW4sIGFkZGl0aW9uYWxEYXRhID0ge30pID0+IHtcclxuICB0cnkge1xyXG4gICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBmZXRjaChgJHthcGlCYXNlfS9hcGkvYXV0aC9sb2dpbmAsIHtcclxuICAgICAgbWV0aG9kOiAnUE9TVCcsXHJcbiAgICAgIGhlYWRlcnM6IHtcclxuICAgICAgICAnQXV0aG9yaXphdGlvbic6IGBCZWFyZXIgJHt0b2tlbn1gLFxyXG4gICAgICAgICdDb250ZW50LVR5cGUnOiAnYXBwbGljYXRpb24vanNvbicsXHJcbiAgICAgIH0sXHJcbiAgICAgIGNyZWRlbnRpYWxzOiAnaW5jbHVkZScsXHJcbiAgICAgIGJvZHk6IEpTT04uc3RyaW5naWZ5KGFkZGl0aW9uYWxEYXRhKSwgLy8gVGhpcyB3aWxsIGluY2x1ZGUgdGhlIGdyYXBoVG9rZW5cclxuICAgIH0pO1xyXG5cclxuICAgIGlmIChyZXNwb25zZS5vaykge1xyXG4gICAgICBjb25zdCBkYXRhID0gYXdhaXQgcmVzcG9uc2UuanNvbigpO1xyXG4gICAgICByZXR1cm4geyBzdWNjZXNzOiB0cnVlLCB1c2VyOiBkYXRhLnVzZXIgfTtcclxuICAgIH0gZWxzZSB7XHJcbiAgICAgIGNvbnN0IGVycm9yID0gYXdhaXQgcmVzcG9uc2UuanNvbigpO1xyXG4gICAgICByZXR1cm4geyBzdWNjZXNzOiBmYWxzZSwgZXJyb3I6IGVycm9yLm1lc3NhZ2UgfTtcclxuICAgIH1cclxuICB9IGNhdGNoIChlcnJvcikge1xyXG4gICAgY29uc29sZS5lcnJvcignTG9naW4gZmFpbGVkOicsIGVycm9yKTtcclxuICAgIHJldHVybiB7IHN1Y2Nlc3M6IGZhbHNlLCBlcnJvcjogZXJyb3IubWVzc2FnZSB9O1xyXG4gIH1cclxufTtcclxuXHJcbi8vIExvZ291dFxyXG5leHBvcnQgY29uc3QgbG9nb3V0ID0gYXN5bmMgKCkgPT4ge1xyXG4gIHRyeSB7XHJcbiAgICBhd2FpdCBmZXRjaChgJHthcGlCYXNlfS9hcGkvYXV0aC9sb2dvdXRgLCB7XHJcbiAgICAgIG1ldGhvZDogJ1BPU1QnLFxyXG4gICAgICBjcmVkZW50aWFsczogJ2luY2x1ZGUnLFxyXG4gICAgfSk7XHJcbiAgICBcclxuICAgIC8vIENsZWFyIGNsaWVudC1zaWRlIGRhdGFcclxuICAgIGNsZWFyQ2xpZW50U3RvcmFnZSgpO1xyXG4gICAgXHJcbiAgICByZXR1cm4gdHJ1ZTtcclxuICB9IGNhdGNoIChlcnJvcikge1xyXG4gICAgY29uc29sZS5lcnJvcignTG9nb3V0IGZhaWxlZDonLCBlcnJvcik7XHJcbiAgICByZXR1cm4gZmFsc2U7XHJcbiAgfVxyXG59O1xyXG5cclxuLy8gQ2xlYXIgYWxsIGNsaWVudC1zaWRlIHN0b3JhZ2VcclxuZXhwb3J0IGNvbnN0IGNsZWFyQ2xpZW50U3RvcmFnZSA9ICgpID0+IHtcclxuICBjb25zdCBrZXlzVG9SZW1vdmUgPSBbJ3N1cGVyVXNlcicsICdjb21wYW55JywgJ2lkJywgJ25hbWUnLCAncm9sZScsICdlbWFpbCcsICdhbGxvd2VkU2VjdGlvbnMnXTtcclxuICBrZXlzVG9SZW1vdmUuZm9yRWFjaChrZXkgPT4gbG9jYWxTdG9yYWdlLnJlbW92ZUl0ZW0oa2V5KSk7XHJcbn07XHJcblxyXG4vLyBDaGVjayBpZiB1c2VyIGlzIGF1dGhlbnRpY2F0ZWRcclxuZXhwb3J0IGNvbnN0IGlzQXV0aGVudGljYXRlZCA9IGFzeW5jICgpID0+IHtcclxuICBjb25zdCB1c2VyID0gYXdhaXQgZ2V0Q3VycmVudFVzZXIoKTtcclxuICByZXR1cm4gISF1c2VyO1xyXG59O1xyXG5cclxuLy8gUmVmcmVzaCBzZXNzaW9uIChleHRlbmQgZXhwaXJ5KVxyXG5leHBvcnQgY29uc3QgcmVmcmVzaFNlc3Npb24gPSBhc3luYyAoKSA9PiB7XHJcbiAgdHJ5IHtcclxuICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgZmV0Y2goYCR7YXBpQmFzZX0vYXBpL2F1dGgvcmVmcmVzaGAsIHtcclxuICAgICAgbWV0aG9kOiAnUE9TVCcsXHJcbiAgICAgIGNyZWRlbnRpYWxzOiAnaW5jbHVkZScsXHJcbiAgICB9KTtcclxuICAgIFxyXG4gICAgcmV0dXJuIHJlc3BvbnNlLm9rO1xyXG4gIH0gY2F0Y2ggKGVycm9yKSB7XHJcbiAgICBjb25zb2xlLmVycm9yKCdTZXNzaW9uIHJlZnJlc2ggZmFpbGVkOicsIGVycm9yKTtcclxuICAgIHJldHVybiBmYWxzZTtcclxuICB9XHJcbn07ICJdLCJuYW1lcyI6WyJhcGlCYXNlIiwicHJvY2VzcyIsImVudiIsIk5FWFRfUFVCTElDX0FQSV9CQVNFX1VSTCIsImdldEN1cnJlbnRVc2VyIiwicmVzcG9uc2UiLCJmZXRjaCIsImNyZWRlbnRpYWxzIiwiaGVhZGVycyIsIm9rIiwiZGF0YSIsImpzb24iLCJ1c2VyIiwiZXJyb3IiLCJjb25zb2xlIiwibG9naW4iLCJ0b2tlbiIsImFkZGl0aW9uYWxEYXRhIiwibWV0aG9kIiwiYm9keSIsIkpTT04iLCJzdHJpbmdpZnkiLCJzdWNjZXNzIiwibWVzc2FnZSIsImxvZ291dCIsImNsZWFyQ2xpZW50U3RvcmFnZSIsImtleXNUb1JlbW92ZSIsImZvckVhY2giLCJrZXkiLCJsb2NhbFN0b3JhZ2UiLCJyZW1vdmVJdGVtIiwiaXNBdXRoZW50aWNhdGVkIiwicmVmcmVzaFNlc3Npb24iXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///./utils/secureStorage.js\n");

/***/ }),

/***/ "./utils/secureThemeContext.js":
/*!*************************************!*\
  !*** ./utils/secureThemeContext.js ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SecureThemeProvider: () => (/* binding */ SecureThemeProvider),\n/* harmony export */   useSecureTheme: () => (/* binding */ useSecureTheme)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n\n\nconst SecureThemeContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)();\nconst SecureThemeProvider = ({ children, initialTheme = \"#022D71\" })=>{\n    const [themeColor, setThemeColor] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(initialTheme);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    // Initialize theme from session data\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const initializeTheme = async ()=>{\n            try {\n                const apiBase = \"http://localhost:8081\" || 0;\n                // Get theme from session via API call\n                const response = await fetch(`${apiBase}/api/auth/me`, {\n                    method: \"GET\",\n                    credentials: \"include\"\n                });\n                if (response.ok) {\n                    const { user } = await response.json();\n                    if (user?.theme) {\n                        setThemeColor(user.theme);\n                    }\n                }\n            } catch (error) {\n                console.error(\"Error loading theme:\", error);\n            } finally{\n                setIsLoading(false);\n            }\n        };\n        initializeTheme();\n    }, []);\n    // Apply theme to CSS variables\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (false) {}\n    }, [\n        themeColor\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SecureThemeContext.Provider, {\n        value: {\n            themeColor,\n            setThemeColor,\n            isLoading\n        },\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\utils\\\\secureThemeContext.js\",\n        lineNumber: 75,\n        columnNumber: 5\n    }, undefined);\n};\nconst useSecureTheme = ()=>{\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(SecureThemeContext);\n    if (!context) {\n        throw new Error(\"useSecureTheme must be used within a SecureThemeProvider\");\n    }\n    return context;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./utils/secureThemeContext.js\n");

/***/ }),

/***/ "./styles/globals.css":
/*!****************************!*\
  !*** ./styles/globals.css ***!
  \****************************/
/***/ (() => {



/***/ }),

/***/ "@fortawesome/react-fontawesome":
/*!*************************************************!*\
  !*** external "@fortawesome/react-fontawesome" ***!
  \*************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("@fortawesome/react-fontawesome");

/***/ }),

/***/ "ag-grid-react":
/*!********************************!*\
  !*** external "ag-grid-react" ***!
  \********************************/
/***/ ((module) => {

"use strict";
module.exports = require("ag-grid-react");

/***/ }),

/***/ "next/dist/compiled/next-server/pages.runtime.dev.js":
/*!**********************************************************************!*\
  !*** external "next/dist/compiled/next-server/pages.runtime.dev.js" ***!
  \**********************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/pages.runtime.dev.js");

/***/ }),

/***/ "./request-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist\\client\\components\\request-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\request-async-storage.external.js");

/***/ }),

/***/ "./static-generation-async-storage.external":
/*!*********************************************************************************************!*\
  !*** external "next/dist\\client\\components\\static-generation-async-storage.external.js" ***!
  \*********************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\static-generation-async-storage.external.js");

/***/ }),

/***/ "react":
/*!************************!*\
  !*** external "react" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("react");

/***/ }),

/***/ "react-dom":
/*!****************************!*\
  !*** external "react-dom" ***!
  \****************************/
/***/ ((module) => {

"use strict";
module.exports = require("react-dom");

/***/ }),

/***/ "react-loader-spinner":
/*!***************************************!*\
  !*** external "react-loader-spinner" ***!
  \***************************************/
/***/ ((module) => {

"use strict";
module.exports = require("react-loader-spinner");

/***/ }),

/***/ "react/jsx-dev-runtime":
/*!****************************************!*\
  !*** external "react/jsx-dev-runtime" ***!
  \****************************************/
/***/ ((module) => {

"use strict";
module.exports = require("react/jsx-dev-runtime");

/***/ }),

/***/ "@azure/msal-browser":
/*!**************************************!*\
  !*** external "@azure/msal-browser" ***!
  \**************************************/
/***/ ((module) => {

"use strict";
module.exports = import("@azure/msal-browser");;

/***/ }),

/***/ "@azure/msal-react":
/*!************************************!*\
  !*** external "@azure/msal-react" ***!
  \************************************/
/***/ ((module) => {

"use strict";
module.exports = import("@azure/msal-react");;

/***/ }),

/***/ "@fortawesome/free-regular-svg-icons":
/*!******************************************************!*\
  !*** external "@fortawesome/free-regular-svg-icons" ***!
  \******************************************************/
/***/ ((module) => {

"use strict";
module.exports = import("@fortawesome/free-regular-svg-icons");;

/***/ }),

/***/ "@fortawesome/free-solid-svg-icons":
/*!****************************************************!*\
  !*** external "@fortawesome/free-solid-svg-icons" ***!
  \****************************************************/
/***/ ((module) => {

"use strict";
module.exports = import("@fortawesome/free-solid-svg-icons");;

/***/ }),

/***/ "js-cookie":
/*!****************************!*\
  !*** external "js-cookie" ***!
  \****************************/
/***/ ((module) => {

"use strict";
module.exports = import("js-cookie");;

/***/ }),

/***/ "react-toastify":
/*!*********************************!*\
  !*** external "react-toastify" ***!
  \*********************************/
/***/ ((module) => {

"use strict";
module.exports = import("react-toastify");;

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc","vendor-chunks/react-toastify","vendor-chunks/ag-grid-community"], () => (__webpack_exec__("./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2Fusers&preferredRegion=&absolutePagePath=.%2Fpages%5Cusers.js&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!")));
module.exports = __webpack_exports__;

})();