"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/service_level",{

/***/ "./components/SideBarLinks.js":
/*!************************************!*\
  !*** ./components/SideBarLinks.js ***!
  \************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ SideBarLinks; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _fortawesome_free_solid_svg_icons__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @fortawesome/free-solid-svg-icons */ \"./node_modules/@fortawesome/free-solid-svg-icons/index.mjs\");\n/* harmony import */ var _fortawesome_react_fontawesome__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @fortawesome/react-fontawesome */ \"./node_modules/@fortawesome/react-fontawesome/index.es.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _utils_loaders_loadingContext__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/utils/loaders/loadingContext */ \"./utils/loaders/loadingContext.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\nfunction SideBarLinks(param) {\n    let { isSuppliersActive, userData, company, isUsersActive, isLogsActive, isWhatifActive, isServiceLevelActive, isProductsActive, ADCompany, currentPathname } = param;\n    var _process_env_NEXT_PUBLIC_AVAILABLE_MODULES;\n    _s();\n    const availableModules = (_process_env_NEXT_PUBLIC_AVAILABLE_MODULES = \"supplier,products,whatif,serviceLevel,users,logs\") === null || _process_env_NEXT_PUBLIC_AVAILABLE_MODULES === void 0 ? void 0 : _process_env_NEXT_PUBLIC_AVAILABLE_MODULES.split(\",\");\n    const { setIsLoading } = (0,_utils_loaders_loadingContext__WEBPACK_IMPORTED_MODULE_4__.useLoading)();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n        className: \"nav navbar-nav\",\n        children: availableModules && availableModules.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n            children: [\n                availableModules.includes(\"supplier\") && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                    className: \"flex justify-center mb-3\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                        href: \"/suppliers\",\n                        title: \"Supplier\",\n                        className: \"!px-4 !py-3 bg-white rounded-md text-center \".concat(isSuppliersActive ? \"opacity-100\" : \"opacity-70\"),\n                        onClick: (e)=>{\n                            if (isSuppliersActive && (currentPathname === null || currentPathname === void 0 ? void 0 : currentPathname.startsWith(\"/suppliers\"))) {\n                                e.preventDefault();\n                                window.location.reload();\n                            } else {\n                                setIsLoading(true);\n                            }\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fortawesome_react_fontawesome__WEBPACK_IMPORTED_MODULE_1__.FontAwesomeIcon, {\n                            icon: _fortawesome_free_solid_svg_icons__WEBPACK_IMPORTED_MODULE_5__.faTruck,\n                            size: \"lg\",\n                            className: \"text-skin-primary\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\SideBarLinks.js\",\n                            lineNumber: 63,\n                            columnNumber: 17\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\SideBarLinks.js\",\n                        lineNumber: 45,\n                        columnNumber: 15\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\SideBarLinks.js\",\n                    lineNumber: 44,\n                    columnNumber: 13\n                }, this),\n                console.log(\"dept\", userData === null || userData === void 0 ? void 0 : userData.department_id),\n                availableModules.includes(\"products\") && !(userData === null || userData === void 0 ? void 0 : userData.department_id) == 5 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                    className: \"flex justify-center mb-3\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                        href: \"/products\",\n                        title: \"Products\",\n                        className: \"!px-4 !py-3 bg-white rounded-md text-center \".concat(isProductsActive ? \"opacity-100\" : \"opacity-70\"),\n                        onClick: (e)=>{\n                            if (isProductsActive) {\n                                e.preventDefault();\n                                window.location.reload();\n                            } else {\n                                setIsLoading(true);\n                            }\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fortawesome_react_fontawesome__WEBPACK_IMPORTED_MODULE_1__.FontAwesomeIcon, {\n                            icon: _fortawesome_free_solid_svg_icons__WEBPACK_IMPORTED_MODULE_5__.faBoxArchive,\n                            size: \"lg\",\n                            className: \"text-skin-primary\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\SideBarLinks.js\",\n                            lineNumber: 89,\n                            columnNumber: 17\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\SideBarLinks.js\",\n                        lineNumber: 74,\n                        columnNumber: 15\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\SideBarLinks.js\",\n                    lineNumber: 73,\n                    columnNumber: 13\n                }, this),\n                (company == \"efcltd\" || company == \"flrs\" || company == \"thl\") && ((userData === null || userData === void 0 ? void 0 : userData.department_id) == 1 || (userData === null || userData === void 0 ? void 0 : userData.department_id) == 2) && availableModules.includes(\"whatif\") && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                    className: \"flex justify-center mb-3\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                        href: \"/whatif\",\n                        title: \"What if\",\n                        className: \"!px-4 !py-3 bg-white rounded-md text-center \".concat(isWhatifActive ? \"opacity-100\" : \"opacity-70\"),\n                        onClick: (e)=>{\n                            if (isWhatifActive) {\n                                e.preventDefault();\n                                window.location.reload();\n                            } else {\n                                setIsLoading(true);\n                            }\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fortawesome_react_fontawesome__WEBPACK_IMPORTED_MODULE_1__.FontAwesomeIcon, {\n                            icon: _fortawesome_free_solid_svg_icons__WEBPACK_IMPORTED_MODULE_5__.faChartLine,\n                            size: \"lg\",\n                            className: \"text-skin-primary\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\SideBarLinks.js\",\n                            lineNumber: 116,\n                            columnNumber: 19\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\SideBarLinks.js\",\n                        lineNumber: 101,\n                        columnNumber: 17\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\SideBarLinks.js\",\n                    lineNumber: 100,\n                    columnNumber: 15\n                }, this),\n                ((userData === null || userData === void 0 ? void 0 : userData.department_id) == 1 || (userData === null || userData === void 0 ? void 0 : userData.department_id) == 2) && availableModules.includes(\"serviceLevel\") && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                    className: \"flex justify-center mb-3\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                        href: \"/service_level\",\n                        title: \"Service Level\",\n                        className: \"!px-4 !py-3 bg-white rounded-md text-center \".concat(isServiceLevelActive ? \"opacity-100\" : \"opacity-70\"),\n                        onClick: (e)=>{\n                            if (isServiceLevelActive) {\n                                e.preventDefault();\n                                window.location.reload();\n                            } else {\n                                setIsLoading(true);\n                            }\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fortawesome_react_fontawesome__WEBPACK_IMPORTED_MODULE_1__.FontAwesomeIcon, {\n                            icon: _fortawesome_free_solid_svg_icons__WEBPACK_IMPORTED_MODULE_5__.faFileCircleQuestion,\n                            size: \"lg\",\n                            className: \"text-skin-primary\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\SideBarLinks.js\",\n                            lineNumber: 143,\n                            columnNumber: 19\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\SideBarLinks.js\",\n                        lineNumber: 128,\n                        columnNumber: 17\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\SideBarLinks.js\",\n                    lineNumber: 127,\n                    columnNumber: 15\n                }, this),\n                ((userData === null || userData === void 0 ? void 0 : userData.role_id) === 1 || (userData === null || userData === void 0 ? void 0 : userData.role_id) === 5 || (userData === null || userData === void 0 ? void 0 : userData.role_id) === 6) && availableModules.includes(\"users\") && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                    className: \"flex justify-center mb-3\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                        href: \"/users\",\n                        title: \"Users\",\n                        className: \"!px-4 !py-3 bg-white rounded-md text-center \".concat(isUsersActive ? \"opacity-100\" : \"opacity-70\"),\n                        onClick: (e)=>{\n                            if (isUsersActive) {\n                                e.preventDefault();\n                                window.location.reload();\n                            } else {\n                                setIsLoading(true);\n                            }\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fortawesome_react_fontawesome__WEBPACK_IMPORTED_MODULE_1__.FontAwesomeIcon, {\n                            icon: _fortawesome_free_solid_svg_icons__WEBPACK_IMPORTED_MODULE_5__.faUsersLine,\n                            size: \"lg\",\n                            className: \"text-skin-primary\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\SideBarLinks.js\",\n                            lineNumber: 173,\n                            columnNumber: 19\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\SideBarLinks.js\",\n                        lineNumber: 157,\n                        columnNumber: 17\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\SideBarLinks.js\",\n                    lineNumber: 156,\n                    columnNumber: 15\n                }, this),\n                ((userData === null || userData === void 0 ? void 0 : userData.role_id) === 1 || (userData === null || userData === void 0 ? void 0 : userData.role_id) === 5 || (userData === null || userData === void 0 ? void 0 : userData.role_id) === 6) && availableModules.includes(\"logs\") && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                    className: \"flex justify-center mb-3\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                        href: \"/viewlogs\",\n                        title: \"Logs\",\n                        className: \"!px-4 !py-3 bg-white rounded-md text-center \".concat(isLogsActive ? \"opacity-100\" : \"opacity-70\"),\n                        onClick: (e)=>{\n                            if (isLogsActive) {\n                                e.preventDefault();\n                                window.location.reload();\n                            } else {\n                                setIsLoading(true);\n                            }\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fortawesome_react_fontawesome__WEBPACK_IMPORTED_MODULE_1__.FontAwesomeIcon, {\n                            icon: _fortawesome_free_solid_svg_icons__WEBPACK_IMPORTED_MODULE_5__.faList,\n                            size: \"lg\",\n                            className: \"text-skin-primary\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\SideBarLinks.js\",\n                            lineNumber: 202,\n                            columnNumber: 19\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\SideBarLinks.js\",\n                        lineNumber: 186,\n                        columnNumber: 17\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\SideBarLinks.js\",\n                    lineNumber: 185,\n                    columnNumber: 15\n                }, this)\n            ]\n        }, void 0, true)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\SideBarLinks.js\",\n        lineNumber: 40,\n        columnNumber: 5\n    }, this);\n}\n_s(SideBarLinks, \"HE5iXj3OUhzAOBpDJUVF6nKZb8k=\", false, function() {\n    return [\n        _utils_loaders_loadingContext__WEBPACK_IMPORTED_MODULE_4__.useLoading\n    ];\n});\n_c = SideBarLinks;\nvar _c;\n$RefreshReg$(_c, \"SideBarLinks\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevExports = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevExports) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports on update so we can compare the boundary\n                // signatures.\n                module.hot.dispose(function (data) {\n                    data.prevExports = currentExports;\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevExports !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevExports, currentExports)) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevExports !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/SideBarLinks.js\n"));

/***/ })

});