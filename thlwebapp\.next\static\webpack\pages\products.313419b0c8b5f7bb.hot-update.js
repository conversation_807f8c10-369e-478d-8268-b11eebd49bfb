"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/products",{

/***/ "./pages/products.js":
/*!***************************!*\
  !*** ./pages/products.js ***!
  \***************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __N_SSP: function() { return /* binding */ __N_SSP; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _components_Layout__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/Layout */ \"./components/Layout.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _fortawesome_react_fontawesome__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @fortawesome/react-fontawesome */ \"./node_modules/@fortawesome/react-fontawesome/index.es.js\");\n/* harmony import */ var _fortawesome_free_solid_svg_icons__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! @fortawesome/free-solid-svg-icons */ \"./node_modules/@fortawesome/free-solid-svg-icons/index.mjs\");\n/* harmony import */ var ag_grid_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ag-grid-react */ \"./node_modules/ag-grid-react/lib/main.js\");\n/* harmony import */ var ag_grid_react__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(ag_grid_react__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var ag_grid_community_styles_ag_grid_css__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ag-grid-community/styles//ag-grid.css */ \"./node_modules/ag-grid-community/styles/ag-grid.css\");\n/* harmony import */ var ag_grid_community_styles_ag_grid_css__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(ag_grid_community_styles_ag_grid_css__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var ag_grid_community_styles_ag_theme_alpine_css__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ag-grid-community/styles//ag-theme-alpine.css */ \"./node_modules/ag-grid-community/styles/ag-theme-alpine.css\");\n/* harmony import */ var ag_grid_community_styles_ag_theme_alpine_css__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(ag_grid_community_styles_ag_theme_alpine_css__WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_7__);\n/* harmony import */ var react_toastify__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! react-toastify */ \"./node_modules/react-toastify/dist/react-toastify.esm.mjs\");\n/* harmony import */ var react_toastify_dist_ReactToastify_css__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! react-toastify/dist/ReactToastify.css */ \"./node_modules/react-toastify/dist/ReactToastify.css\");\n/* harmony import */ var react_toastify_dist_ReactToastify_css__WEBPACK_IMPORTED_MODULE_9___default = /*#__PURE__*/__webpack_require__.n(react_toastify_dist_ReactToastify_css__WEBPACK_IMPORTED_MODULE_9__);\n/* harmony import */ var _utils_loaders_loadingContext__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/utils/loaders/loadingContext */ \"./utils/loaders/loadingContext.js\");\n/* harmony import */ var react_select__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! react-select */ \"./node_modules/react-select/dist/react-select.esm.js\");\n/* harmony import */ var _utils_rolePermissionsContext__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/utils/rolePermissionsContext */ \"./utils/rolePermissionsContext.js\");\n/* harmony import */ var _utils_renderer_productStatusRenderer__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/utils/renderer/productStatusRenderer */ \"./utils/renderer/productStatusRenderer.js\");\n/* harmony import */ var _utils_renderer_productActionRenderer__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/utils/renderer/productActionRenderer */ \"./utils/renderer/productActionRenderer.js\");\n/* harmony import */ var _services_apiConfig__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/services/apiConfig */ \"./services/apiConfig.js\");\n/* harmony import */ var js_cookie__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! js-cookie */ \"./node_modules/js-cookie/dist/js.cookie.mjs\");\n/* harmony import */ var _azure_msal_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @azure/msal-react */ \"./node_modules/@azure/msal-react/dist/index.js\");\n/* harmony import */ var _components_ProductDialog__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! @/components/ProductDialog */ \"./components/ProductDialog.js\");\n/* harmony import */ var _utils_secureStorage__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! @/utils/secureStorage */ \"./utils/secureStorage.js\");\n/* harmony import */ var _utils_extractCompanyFromEmail__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! @/utils/extractCompanyFromEmail */ \"./utils/extractCompanyFromEmail.js\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst customSelectStyles = {\n    // Default style\n    control: (base)=>({\n            ...base,\n            height: \"28px\",\n            minHeight: \"28px\"\n        }),\n    // Style when the condition is true\n    option: (base, param)=>{\n        let { data } = param;\n        return {\n            ...base,\n            color: data.is_new == true ? \"red\" : \"\"\n        };\n    },\n    valueContainer: (provided, state)=>({\n            ...provided,\n            height: \"26px\",\n            width: \"300px\",\n            padding: \"0 6px\"\n        }),\n    input: (provided, state)=>({\n            ...provided,\n            margin: \"0px\"\n        }),\n    indicatorSeparator: (state)=>({\n            display: \"none\"\n        }),\n    indicatorsContainer: (provided, state)=>({\n            ...provided,\n            height: \"28px\"\n        })\n};\nconst Products = (param)=>{\n    let { userData, PreviousPage, pageTypeId } = param;\n    _s();\n    console.log(\"userData, PreviousPage, pageTypeId\", userData, PreviousPage, pageTypeId);\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_7__.useRouter)();\n    const [pageSize, setPageSize] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(15);\n    const gridRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)();\n    const [isRMChecked, setIsRMChecked] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [isFGChecked, setIsFGChecked] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [isNVChecked, setIsNVChecked] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const { setIsLoading } = (0,_utils_loaders_loadingContext__WEBPACK_IMPORTED_MODULE_10__.useLoading)();\n    const [rowData, setRowData] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [company, setCompany] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"\");\n    const [blockScreen, setBlockScreen] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [typeId, setTypeId] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(pageTypeId);\n    const [isDialogOpen, setIsDialogOpen] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [isIssUser, setIsIssUser] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [isIssProcurementTeamUser, setIsIssProcurementTeamUser] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [isIssAdmin, setisIssAdmin] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [nvStatus, setNVStatus] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [statusOptions] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([\n        {\n            value: \"Draft\",\n            label: \"Draft\"\n        },\n        {\n            value: \"Pending Review\",\n            label: \"Pending Review\"\n        },\n        {\n            value: \"Rejected\",\n            label: \"Rejected\"\n        },\n        {\n            value: \"ISS to Setup\",\n            label: \"ISS to Setup\"\n        },\n        {\n            value: \"Setup Completed\",\n            label: \"Setup Completed\"\n        },\n        {\n            value: \"Cancelled\",\n            label: \"Cancelled\"\n        }\n    ]);\n    const [filteredRowData, setFilteredRowData] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [checkedValue, setCheckedValue] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [incompleteToast, setIncompleteToast] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [searchInput, setSearchInput] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"\");\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        // const userCompany = Cookies.get(\"company\");\n        if (userCompany === \"issproduce\") {\n            setIsIssUser(true);\n        }\n        console.log(userData);\n        if (userData.department_id === 2) {\n            setIsIssProcurementTeamUser(true);\n            console.log(\"checking if it is an admin\", userData.role);\n            if (userData.role_id === 1) {\n                console.log(\"is admin\");\n                setisIssAdmin(true);\n            }\n        }\n        console.log(\"isIssProcurementTeamUser\", isIssProcurementTeamUser);\n    }, []);\n    const openDialog = ()=>{\n        setIsDialogOpen(true);\n    };\n    const closeDialog = ()=>{\n        setIsDialogOpen(false);\n    };\n    // const [isOpenOption, setIsOpenOption] = useState(false);\n    const [selectedRequestType, setSelectedRequestType] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(PreviousPage);\n    const handleTypeIdChange = (e)=>{\n        setSearchInput(\"\");\n        const filterTextBox = document.getElementById(\"filter-text-box\");\n        filterTextBox.value = \"\";\n        gridRef.current.api.setQuickFilter(\"\");\n        setNVStatus([]);\n        setTypeId(parseInt(e.target.value));\n        if (e.target.value === \"1\") {\n            setSelectedRequestType(\"rawMaterialRequest\");\n        } else if (e.target.value === \"3\") {\n            setSelectedRequestType(\"newVarietyRequest\");\n        } else if (e.target.value === \"4\") {\n            setSelectedRequestType(\"packagingform\");\n        }\n    };\n    // const closeOptionModal = () => {\n    //   setIsOpenOption(false);\n    // };\n    const handleRequestType = (type)=>{\n        setSelectedRequestType(type);\n    };\n    const handleFormType = ()=>{\n        if (selectedRequestType) {\n            localStorage.setItem(\"formType\", selectedRequestType);\n            console.log(selectedRequestType);\n            if (selectedRequestType === \"rawMaterialRequest\") {\n                router.push({\n                    pathname: \"/raw-material-request/add\"\n                });\n            // } else if (selectedRequestType == \"finishedProductRequest\") {\n            //   router.push({\n            //     pathname: `/finished-product-request/add` });\n            } else if (selectedRequestType == \"newVarietyRequest\") {\n                router.push({\n                    pathname: \"/variety/add\"\n                });\n            } else if (selectedRequestType == \"packagingform\") {\n                // router.push({ pathname: `/packaging/add` });\n                router.push({\n                    pathname: \"/packaging-form/add\"\n                }); //TODO\n            }\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        if (incompleteToast) {\n            react_toastify__WEBPACK_IMPORTED_MODULE_8__.toast.error(\"Kindly Submit the Request to Export it.\", {\n                position: \"top-right\",\n                autoClose: 1000,\n                hideProgressBar: false,\n                closeOnClick: true,\n                pauseOnHover: false,\n                draggable: true,\n                progress: undefined,\n                theme: \"light\"\n            });\n        }\n    }, [\n        incompleteToast\n    ]);\n    const [productsExtractData, setProductsExtractData] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([\n        [\n            \"Product Extract Data\"\n        ]\n    ]);\n    const [altfilExtractData, setAltfilExtractData] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([\n        [\n            \"Altfil Extract Data\"\n        ]\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        if (productsExtractData.length > 0) {\n            setCheckedValue(true);\n        } else {\n            setCheckedValue(false);\n        }\n    }, [\n        productsExtractData\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        if (typeof document !== \"undefined\") {\n            document.title = \"Products\";\n        }\n        setIsLoading(false);\n        getData().then((data)=>{\n            console.log(data);\n            const formattedData = data === null || data === void 0 ? void 0 : data.map((row)=>({\n                    id: row.id,\n                    code: row.code,\n                    action_id: row.action_id,\n                    request_no: row.request_no,\n                    type: row.product_type,\n                    product_type_name: row.product_type_label,\n                    product_type_id: row.product_type_id,\n                    reason: (row === null || row === void 0 ? void 0 : row.reason_description) ? row === null || row === void 0 ? void 0 : row.reason_description : \"Not Entered\",\n                    delivery_date: row.delivery_date ? new Date(row.delivery_date).toISOString().split(\"T\")[0] : \"Not Entered\",\n                    // delivery_date: new Date(row.delivery_date),\n                    product_code: row.master_product_code ? row.master_product_code : \"Not Entered\",\n                    product_description: row.product_description ? row.product_description : \"Not Entered\",\n                    originator: row.originator ? row.originator : row.originator_name,\n                    originator_email: row.originator_email,\n                    coo: row.coo ? row.coo : \"Not Entered\",\n                    status: row.status_label,\n                    master_product_code: row === null || row === void 0 ? void 0 : row.master_product_code,\n                    count_or_size: row === null || row === void 0 ? void 0 : row.count_or_size,\n                    units_in_outer: row === null || row === void 0 ? void 0 : row.units_in_outer,\n                    cases_per_pallet: row === null || row === void 0 ? void 0 : row.cases_per_pallet,\n                    outer_net_weight: row === null || row === void 0 ? void 0 : row.outer_net_weight,\n                    outer_gross_weight: row === null || row === void 0 ? void 0 : row.outer_gross_weight,\n                    sub_product_code: row === null || row === void 0 ? void 0 : row.sub_product_code,\n                    temperature_grade: row === null || row === void 0 ? void 0 : row.temperature_grade,\n                    intrastat_commodity_code: row === null || row === void 0 ? void 0 : row.intrastat_commodity_code,\n                    temperature_grade_name: row === null || row === void 0 ? void 0 : row.temperature_grade_name,\n                    intrastat_commodity_code_name: row === null || row === void 0 ? void 0 : row.intrastat_commodity_code_name,\n                    mark_variety_name: row === null || row === void 0 ? void 0 : row.mark_variety_name,\n                    intrastat_commodity_code_id: row === null || row === void 0 ? void 0 : row.intrastat_commodity_code_id,\n                    sort_group_id: row === null || row === void 0 ? void 0 : row.group_id,\n                    company: row === null || row === void 0 ? void 0 : row.company_name,\n                    temperature_grade_id: row === null || row === void 0 ? void 0 : row.temperature_grade_id,\n                    userText4: row === null || row === void 0 ? void 0 : row.userText4,\n                    userText5: row === null || row === void 0 ? void 0 : row.userText5,\n                    userText6: row === null || row === void 0 ? void 0 : row.userText6\n                }));\n            setCompany(formattedData[0].company);\n            setRowData(formattedData);\n            setFilteredRowData(formattedData);\n        }).catch((error)=>{\n            return error;\n        });\n    }, [\n        typeId\n    ]);\n    function getData() {\n        setRowData([]);\n        setFilteredRowData([]);\n        let serverAddress = _services_apiConfig__WEBPACK_IMPORTED_MODULE_14__.apiConfig.serverAddress;\n        const company_name = (userData === null || userData === void 0 ? void 0 : userData.company) || (0,_utils_extractCompanyFromEmail__WEBPACK_IMPORTED_MODULE_19__.extractCompanyFromEmail)(userData === null || userData === void 0 ? void 0 : userData.email);\n        const AdCompany = (userData === null || userData === void 0 ? void 0 : userData.companyName) || (userData === null || userData === void 0 ? void 0 : userData.ADCompanyName);\n        return fetch(\"\".concat(serverAddress, \"products/get-products/\").concat(company_name == \"dpsltd\" && AdCompany == \"DPS MS\" ? \"DPS MS\" : company_name, \"\\n      /\").concat(typeId), {\n            method: \"GET\",\n            headers: {\n                \"Content-Type\": \"application/json\"\n            },\n            credentials: \"include\"\n        }).then(async (res)=>{\n            if (res.status == 502) {\n                setBlockScreen(true);\n                return;\n            }\n            setBlockScreen(false);\n            if (res.status === 200) {\n                return res.json();\n            }\n            if (res.status === 400) {\n                react_toastify__WEBPACK_IMPORTED_MODULE_8__.toast.error(\"There was an error with your request. Please check your data and try again.\");\n            } else if (res.status === 401) {\n                react_toastify__WEBPACK_IMPORTED_MODULE_8__.toast.error(\"Your session has expired. Please log in again.\");\n                setTimeout(async ()=>{\n                    await (0,_utils_secureStorage__WEBPACK_IMPORTED_MODULE_18__.logout)();\n                    router.push(\"/login\");\n                }, 3000);\n            }\n            throw new Error(\"Failed to fetch data\");\n        }).catch((error)=>{\n            console.error(error);\n        });\n    }\n    const handlePageSizeChange = (event)=>{\n        const newPageSize = parseInt(event.target.value, 15);\n        setPageSize(newPageSize);\n        gridRef.current.api.paginationSetPageSize(newPageSize);\n    };\n    const handleStatusChange = (selectedOptions)=>{\n        setNVStatus(selectedOptions);\n        filterData(selectedOptions);\n    };\n    const filterData = (statuses)=>{\n        if (statuses.length == 0) {\n            setFilteredRowData(rowData);\n            return;\n        }\n        const filteredData = rowData.filter((row)=>statuses.some((status)=>status.value === row.status));\n        setFilteredRowData(filteredData.length > 0 ? filteredData : []);\n    };\n    // const clearFilters = () => {\n    //   setSelectedStatuses([]);\n    //   setFilteredRowData(rowData);\n    // };\n    const defaultColDef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useMemo)(()=>({\n            sortable: true,\n            filter: false,\n            resizable: true,\n            flex: 1,\n            suppressMenu: false\n        }));\n    const gridOptions = {\n        responsive: true\n    };\n    const columnDefs = [\n        {\n            headerName: \"Request No.\",\n            field: \"request_no\",\n            // cellRenderer: nameRenderer,\n            suppressMenu: true,\n            suppressSizeToFit: true,\n            suppressSizeToFit: false,\n            cellClass: \"ag-grid-checkbox-cell\",\n            flex: \"2%\",\n            filter: true\n        },\n        {\n            headerName: \"Reason\",\n            field: \"reason\",\n            flex: \"2%\",\n            cellRenderer: _utils_renderer_productStatusRenderer__WEBPACK_IMPORTED_MODULE_12__[\"default\"],\n            hide: typeId == 1 || 4 ? false : 0,\n            cellStyle: (params)=>{\n                if (params.value == \"Not Entered\") {\n                    return {\n                        color: \"#B31312\"\n                    };\n                }\n                return null;\n            }\n        },\n        {\n            headerName: \"Delivery Date\",\n            field: \"delivery_date\",\n            flex: \"2%\",\n            hide: typeId == 1 || 4 ? false : 0,\n            cellRenderer: (params)=>{\n                if (params.value === \"Not Entered\") {\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-[#B31312]\",\n                        children: \"Not Entered\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\products.js\",\n                        lineNumber: 384,\n                        columnNumber: 18\n                    }, undefined);\n                } else {\n                    // params.value\n                    // Format the date from yyyy-mm-dd to dd/mm/yyyy\n                    const dateParts = params.value.split(\"-\");\n                    const formattedDate = dateParts.reverse().join(\"/\");\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: formattedDate\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\products.js\",\n                        lineNumber: 391,\n                        columnNumber: 18\n                    }, undefined);\n                }\n            }\n        },\n        {\n            headerName: \"Product Code\",\n            field: \"product_code\",\n            flex: \"2%\",\n            // cellClass: (params) => {\n            //   return params.value === \"Not Entered\" ? 'not-entered' : '';\n            // }\n            cellStyle: (params)=>{\n                if (params.value == \"Not Entered\") {\n                    return {\n                        color: \"#B31312\"\n                    };\n                }\n                return null;\n            }\n        },\n        {\n            // headerName: \"Product description\",\n            // field: \"product_description\",\n            headerName: \"Product Description\",\n            field: \"product_description\",\n            // cellRenderer: statusRenderer,\n            flex: \"3%\",\n            cellStyle: (params)=>{\n                if (params.value == \"Not Entered\") {\n                    return {\n                        color: \"#B31312\"\n                    };\n                }\n                return null;\n            }\n        },\n        {\n            headerName: \"Originator\",\n            field: \"originator\",\n            headerName: \"Originator\",\n            field: \"originator\",\n            // cellRenderer: statusRenderer,\n            flex: \"2%\",\n            flex: \"2%\"\n        },\n        // {\n        //   headerName: \"COO\",\n        //   field: \"coo\",\n        //   headerName: \"COO\",\n        //   field: \"coo\",\n        //   // cellRenderer: statusRenderer,\n        //   flex: \"2%\",\n        //   cellStyle: (params) => {\n        //     if (params.value == \"Not Entered\") {\n        //       return { color: \"#B31312\" };\n        //     }\n        //     return null;\n        //   },\n        // },\n        {\n            headerName: \"Status\",\n            field: \"status\",\n            cellRenderer: _utils_renderer_productStatusRenderer__WEBPACK_IMPORTED_MODULE_12__[\"default\"],\n            cellStyle: ()=>({\n                    justifyContent: \"center\"\n                }),\n            flex: \"2%\",\n            hide: false\n        },\n        {\n            // field: \"Action(s)\",\n            field: typeId == 3 ? \"Action(s)\" : \"Action(s)\",\n            cellRenderer: (params)=>(0,_utils_renderer_productActionRenderer__WEBPACK_IMPORTED_MODULE_13__[\"default\"])(params, userData, userData.token, company, typeId, setIsLoading, isIssUser),\n            flex: \"2%\",\n            cellStyle: ()=>({}),\n            cellStyle: {\n                justifyContent: \"end\",\n                paddingRight: \"10px\"\n            },\n            sortable: false\n        },\n        {\n            field: \"role_num\",\n            hide: true\n        }\n    ];\n    const handleGridReady = (params)=>{\n        params.api.setColumnDefs(columnDefs);\n    };\n    const onFilterTextBoxChanged = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(()=>{\n        gridRef.current.api.setQuickFilter(document.getElementById(\"filter-text-box\").value);\n        setSearchInput(document.getElementById(\"filter-text-box\").value);\n    }, []);\n    const handleFilterProductType = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)((e)=>{\n        if (e.target.value == \"RM\") {\n            if (e.target.checked) {\n                setIsRMChecked(true);\n                setIsFGChecked(false);\n                setIsNVChecked(false);\n                gridRef.current.api.setQuickFilter(\"RM\");\n            } else {\n                setIsRMChecked(false);\n                gridRef.current.api.setQuickFilter(\"\");\n            }\n        } else if (e.target.value == \"FG\") {\n            if (e.target.checked) {\n                setIsFGChecked(true);\n                setIsRMChecked(false);\n                setIsNVChecked(false);\n                gridRef.current.api.setQuickFilter(\"FG\");\n            } else {\n                setIsFGChecked(false);\n                gridRef.current.api.setQuickFilter(\"\");\n            }\n        } else if (e.target.value == \"NV\") {\n            if (e.target.checked) {\n                setIsNVChecked(true);\n                setIsFGChecked(false);\n                setIsRMChecked(false);\n                gridRef.current.api.setQuickFilter(\"NV\");\n            } else {\n                setIsNVChecked(true);\n                gridRef.current.api.setQuickFilter(\"\");\n            }\n        } else {\n            setIsRMChecked(false);\n            setIsFGChecked(false);\n            setIsNVChecked(false);\n            gridRef.current.api.setQuickFilter(\"\");\n        }\n    }, []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_toastify__WEBPACK_IMPORTED_MODULE_8__.ToastContainer, {\n                limit: 1\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\products.js\",\n                lineNumber: 531,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Layout__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                userData: userData,\n                blockScreen: blockScreen,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mr-20 md:mr-12 lg:mr-14\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-row md:flex-col lg:flex-row justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"border border-light-gray rounded-md px-3 py-1\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"flex items-center gap-2 cursor-pointer\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                type: \"radio\",\n                                                                name: \"requestType\",\n                                                                value: \"1\",\n                                                                className: \"form-radio w-5 h-5 cursor-pointer\",\n                                                                checked: typeId === 1,\n                                                                onChange: handleTypeIdChange\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\products.js\",\n                                                                lineNumber: 539,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"labels\",\n                                                                children: \"Raw Materials\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\products.js\",\n                                                                lineNumber: 547,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\products.js\",\n                                                        lineNumber: 538,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\products.js\",\n                                                    lineNumber: 537,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"border border-light-gray rounded-md px-3 py-1\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"flex items-center gap-2 cursor-pointer\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                type: \"radio\",\n                                                                name: \"requestType\",\n                                                                value: \"3\",\n                                                                className: \"form-radio w-5 h-5 cursor-pointer\",\n                                                                checked: typeId === 3,\n                                                                onChange: handleTypeIdChange\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\products.js\",\n                                                                lineNumber: 563,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"labels\",\n                                                                children: \"New Variety\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\products.js\",\n                                                                lineNumber: 571,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\products.js\",\n                                                        lineNumber: 562,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\products.js\",\n                                                    lineNumber: 561,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                isIssUser && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"border border-light-gray rounded-md px-3 py-1\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"flex items-center gap-2 cursor-pointer\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                type: \"radio\",\n                                                                name: \"requestType\",\n                                                                value: \"4\",\n                                                                className: \"form-radio w-5 h-5 cursor-pointer\",\n                                                                checked: typeId === 4,\n                                                                onChange: handleTypeIdChange\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\products.js\",\n                                                                lineNumber: 577,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"labels\",\n                                                                children: \"Packaging\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\products.js\",\n                                                                lineNumber: 585,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\products.js\",\n                                                        lineNumber: 576,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\products.js\",\n                                                    lineNumber: 575,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\products.js\",\n                                            lineNumber: 536,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"labels\",\n                                            style: {\n                                                marginLeft: \"10px\"\n                                            },\n                                            children: typeId === 3 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_select__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                isMulti: true,\n                                                options: statusOptions,\n                                                onChange: handleStatusChange,\n                                                placeholder: \"Select Status...\",\n                                                className: \"basic-multi-select\",\n                                                classNamePrefix: \"select\",\n                                                value: nvStatus,\n                                                isSearchable: false,\n                                                styles: customSelectStyles\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\products.js\",\n                                                lineNumber: 592,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\products.js\",\n                                            lineNumber: 590,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center px-3 w-auto md:w-fit lg:w-auto md:py-2 lg:py-1 md:mb-3 lg:mb-0\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\products.js\",\n                                            lineNumber: 608,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\products.js\",\n                                    lineNumber: 535,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"relative block w-[47vh] text-gray-400 mt-0 pt-0\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"absolute px-4 py-1 pt-[0.4rem] 2xl:pt-1.5 text-black\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fortawesome_react_fontawesome__WEBPACK_IMPORTED_MODULE_3__.FontAwesomeIcon, {\n                                                        icon: _fortawesome_free_solid_svg_icons__WEBPACK_IMPORTED_MODULE_21__.faSearch,\n                                                        className: \"fw-bold\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\products.js\",\n                                                        lineNumber: 639,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\products.js\",\n                                                    lineNumber: 638,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"text\",\n                                                    id: \"filter-text-box\",\n                                                    placeholder: \"Search...\",\n                                                    onInput: onFilterTextBoxChanged,\n                                                    value: searchInput,\n                                                    className: \"block w-full px-4 pl-10 text-gray-500 placeholder-gray-400 searchbar border rounded-lg appearance-none form-input focus:outline-none\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\products.js\",\n                                                    lineNumber: 641,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\products.js\",\n                                            lineNumber: 637,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ProductDialog__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                            isOpen: openDialog,\n                                            onClose: closeDialog,\n                                            handleFormType: handleFormType,\n                                            selectedRequestType: selectedRequestType,\n                                            isIssUser: isIssUser,\n                                            isIssProcurmentUser: isIssProcurementTeamUser,\n                                            handleRequestType: handleRequestType,\n                                            admin: isIssAdmin\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\products.js\",\n                                            lineNumber: 660,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\products.js\",\n                                    lineNumber: 636,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\products.js\",\n                            lineNumber: 534,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"my-5\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative ag-theme-alpine !rounded-md\",\n                                style: {\n                                    height: \"calc(100vh - 151px)\"\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ag_grid_react__WEBPACK_IMPORTED_MODULE_4__.AgGridReact, {\n                                        rowData: filteredRowData,\n                                        ref: gridRef,\n                                        columnDefs: columnDefs,\n                                        defaultColDef: defaultColDef,\n                                        suppressRowClickSelection: true,\n                                        pagination: true,\n                                        paginationPageSize: pageSize,\n                                        onPageSizeChanged: handlePageSizeChange,\n                                        tooltipShowDelay: 0,\n                                        tooltipHideDelay: 1000,\n                                        onGridReady: handleGridReady,\n                                        gridOptions: gridOptions\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\products.js\",\n                                        lineNumber: 677,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-start mt-2 pagination-style\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            htmlFor: \"page-size-select pagination\",\n                                            className: \"inputs\",\n                                            children: [\n                                                \"Show\",\n                                                \" \",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                    id: \"page-size-select\",\n                                                    onChange: handlePageSizeChange,\n                                                    value: pageSize,\n                                                    className: \"focus:outline-none\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: 10,\n                                                            children: \"10\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\products.js\",\n                                                            lineNumber: 700,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: 15,\n                                                            children: \"15\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\products.js\",\n                                                            lineNumber: 701,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: 25,\n                                                            children: \"25\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\products.js\",\n                                                            lineNumber: 702,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: 50,\n                                                            children: \"50\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\products.js\",\n                                                            lineNumber: 703,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: 100,\n                                                            children: \"100\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\products.js\",\n                                                            lineNumber: 704,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\products.js\",\n                                                    lineNumber: 694,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                \" \",\n                                                \"Entries\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\products.js\",\n                                            lineNumber: 692,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\products.js\",\n                                        lineNumber: 691,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\products.js\",\n                                lineNumber: 673,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\products.js\",\n                            lineNumber: 672,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\products.js\",\n                    lineNumber: 533,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\products.js\",\n                lineNumber: 532,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true);\n};\n_s(Products, \"x02n8KTM2A42sUdrK7Zft5e7jZs=\", false, function() {\n    return [\n        next_router__WEBPACK_IMPORTED_MODULE_7__.useRouter,\n        _utils_loaders_loadingContext__WEBPACK_IMPORTED_MODULE_10__.useLoading\n    ];\n});\n_c = Products;\nvar __N_SSP = true;\n/* harmony default export */ __webpack_exports__[\"default\"] = (Products);\nvar _c;\n$RefreshReg$(_c, \"Products\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevExports = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevExports) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports on update so we can compare the boundary\n                // signatures.\n                module.hot.dispose(function (data) {\n                    data.prevExports = currentExports;\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevExports !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevExports, currentExports)) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevExports !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./pages/products.js\n"));

/***/ })

});