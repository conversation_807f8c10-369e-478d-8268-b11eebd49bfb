import React, { useEffect, useState } from "react";
import ViewDetails from "./ViewDetails";
import { apiConfig } from "@/services/apiConfig";
import ReasonsDetails from "./ReasonsDetails";
import { getCookieData } from "@/utils/getCookieData";
import NoDataFound from "../common/NoDataFound";
import { Tooltip } from "@fluentui/react-components";
import { toast, ToastContainer } from "react-toastify";
//
import Cookies from "js-cookie";
import { logout } from "@/utils/secureStorage";
import { useRouter } from "next/router";
const SLTable = ({
  gridcolumnRefs,
  gridcolumnWidths,
  setIsTableRendered,
  checkedStates,
  customerSLData,
  userData,
  selectedProducts,
  seeAll,
  recordCount,
  setRecordsCount,
  searchBoxContent,
  slFilters,
  selectedMasterProductCode,
  selectedCustomer,
  toggle,
  selectedRows,
  setSelectedRows,
  isBulkUpdate,
  setIsBulkUpdate,
  bulkUpdateData,
  isOpen,
  setIsOpen,
  setBulkUpdateData,
  masterProducts,
  bulkDeleteOrdIds,
  setBulkDeleteOrdIds,
  setNoDataExists,
  setShowLoadingMessage,
  setAllReasonsSubreasons,
  allReasonsSubreasons,
  selectedReasons,
  setSelectedReasons,
  selectedSubReasons,
  setSelectedSubReasons,
  showLoadingMessage,
}) => {
  const router = useRouter();
    let ADCompanyName=userData?.companyName||userData?.ADCompanyName
  const [reasonsMasterList, setReasonsMasterList] = useState([]);
  const [parentReasonList, setParentReasonList] = useState([]);
  const [reasonsData, setReasonsData] = useState([]);
  const [allSelectedProducts, setAllSelectedProducts] = useState([]);
  const [isHeaderChecked, setIsHeaderChecked] = useState(false);
  const [columnTotals, setColumnTotals] = useState({});

  const setMapForReasonsParentsAndTheirCorrespondingChildren = () => {
    setParentReasonList((prevList) => {
      const parentReasonIds = reasonsMasterList
        .filter((typeOfReason) => typeOfReason.parent_id === null)
        .map((typeOfReason) => typeOfReason);
      const uniqueParentIds = new Set([...prevList, ...parentReasonIds]);

      return Array.from(uniqueParentIds); // Update the state
    });
  };

  const handleCheckboxChange = (data) => {
    setSelectedRows((prevSelected) => {
      let updatedSelected;

      if (prevSelected.includes(data)) {
        updatedSelected = prevSelected.filter((item) => item !== data);
      } else {
        updatedSelected = [...prevSelected, data];
      }

      const ordIds = updatedSelected
        .filter((product) => product.CASES_ADDED_REASONS > 0)
        .map((product) => product.ORD_ID);

      setBulkDeleteOrdIds(ordIds.length > 0 ? ordIds : []);
      return updatedSelected;
    });
  };

  const handleHeaderCheckboxChange = (data) => {
    const selectableRows = data.filter(
      (product) =>
        (product.LOCKED_BY === null || product.LOCKED_BY === "") &&
        product.CASES_DIFFERENCE != 0
    );

    const ordIds = selectableRows
      .filter((product) => product.CASES_ADDED_REASONS > 0)
      .map((product) => product.ORD_ID);
    if (ordIds.length > 0) {
      setBulkDeleteOrdIds(ordIds);
    } else {
      setBulkDeleteOrdIds([]);
    }

    if (selectedRows.length === selectableRows.length) {
      // If all rows are selected, deselect all
      setSelectedRows([]);
      setIsHeaderChecked(false);
    } else {
      // If not all rows are selected, select all
      setSelectedRows(selectableRows);
      setIsHeaderChecked(true);
    }
  };

  //#region getReasons
  const fetchReasonData = async (orderId, customerName) => {
    setReasonsData([]);
    const serverAddress = apiConfig.serverAddress;
    try {
      const serviceLevelReasons = await fetch(
        `${serverAddress}servicelevel/get-service-level-reasons/${orderId}?customerName=${customerName}`,
        {
          method: "GET",
          headers: {
            Accept: "application/json",
            "Content-Type": "application/json",
          },
          credentials: "include",
        }
      )
        .then((res) => {
          if (res.status === 400) {
            toast.error(
              "There was an error with your request. Please check your data and try again."
            );
            return null;
          } else if (res.status === 401) {
            toast.error("Your session has expired. Please log in again.");
            setTimeout(async() => {
              await logout();
              const redirectUrl = `/login?redirect=${encodeURIComponent(
                window.location.pathname
              )}`;
              router.push(redirectUrl);
            }, 3000);
            return null;
          }
          if (res.status === 200) {
            return res.json();
          }
        }) // Ensure you parse the JSON
        .then((data) => {
          if (!isBulkUpdate) {
            setReasonsData(data);
          }
        });
    } catch (error) {
      console.log("error in fetching", error);
      toast.error(`Error fetching reasons:${error.message}`, {
        position: "top-right",
      });
    }
  };
  //#endregion
  //#region getMasterReasons
  const fetchReasonMaster = async () => {
    // console.log("service Level Reasons Master code start");
    try {
      const serverAddress = apiConfig.serverAddress;
      const serviceLevelReasonsMaster = await fetch(
        `${serverAddress}servicelevel/get-service-level-reasons-master`,
        {
          method: "GET",
          headers: {
            Accept: "application/json",
            "Content-Type": "application/json",
          },
          credentials: "include",
        }
      )
        .then((res) => {
          if (res.status === 400) {
            toast.error(
              "There was an error with your request. Please check your data and try again."
            );
            return null;
          } else if (res.status === 401) {
            toast.error("Your session has expired. Please log in again.");
            setTimeout(async() => {
              await logout();
              const redirectUrl = `/login?redirect=${encodeURIComponent(
                window.location.pathname
              )}`;
              router.push(redirectUrl);
            }, 3000);
            return null;
          }
          if (res.status === 200) {
            return res.json();
          }
        }) // Ensure you parse the JSON
        .then((data) => {
          setReasonsMasterList(data);
        });
    } catch (err) {
      console.log("error in fetching", err);
    }
  };
  useEffect(() => {
    setIsTableRendered((prev) => !prev);
  }, [setIsTableRendered]);

  useEffect(() => {
    fetchReasonMaster(); // To fetch the master list of reasons
    setMapForReasonsParentsAndTheirCorrespondingChildren();
  }, []);
  useEffect(() => {
    const cookieSelectedProducts = slFilters?.selectedProducts || [];
    const stateSelectedProducts = selectedProducts || [];
    const combinedSelectedProducts = [
      ...cookieSelectedProducts.map((product) => ({
        label: product.productDescription,
        value: product.altFillId,
      })),
      ...stateSelectedProducts.map((product) => ({
        label: product.label,
        value: product.value,
      })),
    ];

    const customerSLArray = Object.values(customerSLData);
    let filteredProducts = combinedSelectedProducts.length
      ? customerSLArray.filter((product) =>
          combinedSelectedProducts.some(
            (selectedProduct) =>
              selectedProduct.label === product.PRODUCT_DESCRIPTION &&
              (!searchBoxContent ||
                (!!searchBoxContent &&
                  product.ORD_NUMBER.toString().includes(searchBoxContent)))
          )
        )
      : customerSLArray.filter(
          (product) =>
            !searchBoxContent ||
            (!!searchBoxContent &&
              product.ORD_NUMBER.toString().includes(searchBoxContent))
        );

    if (selectedMasterProductCode !== "all") {
      filteredProducts = filteredProducts.filter(
        (prod) => prod.MASTER_PRODUCT_CODE == selectedMasterProductCode
      );
    }

    if (selectedReasons.length > 0) {
      filteredProducts = filteredProducts.filter(
        (product) =>
          product.reasons &&
          product.reasons.some((reason) =>
            selectedReasons.includes(reason.MAIN_REASON_ID)
          )
      );
    } else if (selectedSubReasons.length > 0) {
      filteredProducts = filteredProducts.filter(
        (product) =>
          product.reasons &&
          product.reasons.some((reason) =>
            selectedSubReasons.includes(reason.SUB_REASON_ID)
          )
      );
    }

    if (filteredProducts.length > 0) {
      setAllSelectedProducts(filteredProducts);
      const visibleRows = filteredProducts.filter((data) => {
        if (!seeAll && data.CASES_DIFFERENCE - data.CASES_ADDED_REASONS === 0) {
          return false;
        }
        return true;
      });
      const totals = visibleRows.reduce(
        (acc, data) => {
          acc.casesOrdered += data.CASES_ORIGINAL || 0;
          acc.casesDelivered += data.CASES_DELIVERED || 0;
          acc.casesDifference += data.CASES_DIFFERENCE || 0;
          acc.addedReasons += data.CASES_ADDED_REASONS || 0;
          acc.totalValue +=
            (data.CASE_SIZE || 0) *
            (data.UNIT_PRICE || 0) *
            (data.CASES_DIFFERENCE || 0);
          return acc;
        },
        {
          casesOrdered: 0,
          casesDelivered: 0,
          casesDifference: 0,
          addedReasons: 0,
          totalValue: 0,
        }
      );
      setColumnTotals(totals);
    }
    setRecordsCount(filteredProducts?.length ?? 0);
  }, [
    toggle,
    customerSLData,
    selectedProducts,
    slFilters,
    searchBoxContent,
    selectedMasterProductCode,
    selectedReasons,
    selectedSubReasons,
  ]);
  const [selectedData, setSelectedData] = useState(null);
  const handleViewDetailsClick = (data) => {
    setSelectedRows([data]);
    setIsOpen(true);
  };
  return (
    <div className="!fontFamily-poppinsregular">
      <ToastContainer limit={1} /> 
      {/* {recordCount == 0 && <NoDataFound />} */}
      {recordCount != 0 && (
        <table
          className="service-level-grid relative table-fixed w-full text-sm !fontFamily-poppinsregular"
          cellSpacing={0}
        >
          <thead>
            <tr>
              <th
                className="sticky top-0 z-10 !w-10"
                ref={gridcolumnRefs.checkboxRef}
                style={{ left: `${0}px` }}
              >
                <div className="w-full flex justify-center">
                  <input
                    type="checkbox"
                    className="w-5 h-5 text-blue border-theme-blue2 rounded accent-skin-primary text-center"
                    disabled={
                      (selectedMasterProductCode == "all" &&
                        masterProducts.length > 2) ||
                      selectedCustomer == "All Customers"
                    }
                    onChange={() =>
                      handleHeaderCheckboxChange(allSelectedProducts)
                    }
                    checked={isHeaderChecked}
                  />
                </div>
              </th>
              {checkedStates.columns.depotdate && (
                <th
                  className="sticky top-0 z-10"
                  ref={gridcolumnRefs.depotdate}
                  style={{ left: `${gridcolumnWidths.checkboxWidth}px` }}
                >
                  Depot Date
                </th>
              )}
                {ADCompanyName=="Integrated Service Solutions Ltd"&&<th
                  className="sticky top-0 z-10 !text-center"
                  ref={gridcolumnRefs.serviceCustomers}
                  style={{ left: `${gridcolumnWidths.checkboxWidth}px` }}
                >
                  Service Customers{" "}
                </th>}
                {/* //!for service customers */}
              {checkedStates.columns.weekNo && (
                <th
                  className="sticky top-0  z-10"
                  ref={gridcolumnRefs.weekNo}
                  style={{
                    left: `${ADCompanyName=="Integrated Service Solutions Ltd"?gridcolumnWidths.serviceCustomerswidth:gridcolumnWidths.depotdate}px`,
                  }}
                >
                  Week No
                </th>
              )}
              {checkedStates.columns.altfill && (
                <th
                  className="sticky top-0  z-10"
                  ref={gridcolumnRefs.altfill}
                  style={{ left: `${gridcolumnWidths.weekNo}px` }}
                >
                  Alt Fill
                </th>
              )}
              {checkedStates.columns.customer && (
                <th
                  className="!w-28 sticky top-0  z-10 "
                  ref={gridcolumnRefs.customer}
                  style={{ left: `${gridcolumnWidths.altfill}px` }}
                >
                  Customer
                </th>
              )}
              {checkedStates.columns.salesorder && (
                <th
                  className="sticky top-0  z-10 "
                  ref={gridcolumnRefs.salesorder}
                  style={{ left: `${gridcolumnWidths.customer}px` }}
                >
                  Sales Order
                </th>
              )}
              {checkedStates.columns.salesOrderId && (
                <th
                  className="sticky top-0  z-10 "
                  ref={gridcolumnRefs.salesOrderId}
                  style={{ left: `${gridcolumnWidths.customer}px` }}
                >
                  Order Det Id
                </th>
              )}
              {checkedStates.columns.product && (
                <th
                  className="!w-80 sticky top-0  z-10 "
                  ref={gridcolumnRefs.product}
                  style={{ left: `${gridcolumnWidths.salesorder}px` }}
                >
                  Product
                </th>
              )}
              <th
                className="top-0  z-10 !text-center"
                ref={gridcolumnRefs.casesize}
                style={{ left: `${gridcolumnWidths.product}px` }}
              >
                Case Size
              </th>
              <th className="sticky top-0 !text-center">Cases Ordered</th>
              <th className="sticky top-0 !text-center">Cases Delivered</th>
              <th className="sticky top-0 !text-center">Cases Different</th>
              <th className="sticky top-0 !text-center">Added Reasons</th>
              <th className="!w-28 sticky top-0 !text-center">
                Order Fulfillment %
              </th>
              <th className="sticky top-0 !text-center">Unit Price</th>
              <th className="sticky top-0 !text-center">Case Value</th>
              <th className="sticky top-0 !text-center">Total Value</th>
              <th className="sticky top-0 !text-center">Status</th>
              <th className="!w-60 relative">
                Reason{" "}
                <ReasonsDetails
                  reasonsData={allReasonsSubreasons.reasons}
                  selectedReasons={selectedReasons}
                  selectedSubReasons={selectedSubReasons}
                  setSelectedReasons={setSelectedReasons}
                  setSelectedSubReasons={setSelectedSubReasons}
                  type="reasonsList"
                  seeAll={seeAll}
                />
              </th>
              <th className="!w-60 relative">
                Sub Reason
                <ReasonsDetails
                  reasonsData={allReasonsSubreasons.subReasons}
                  selectedReasons={selectedReasons}
                  selectedSubReasons={selectedSubReasons}
                  setSelectedReasons={setSelectedReasons}
                  setSelectedSubReasons={setSelectedSubReasons}
                  type="subReasonsList"
                  seeAll={seeAll}
                />
              </th>
              <th className="!w-52">Comments</th>
              <th className="!text-center">Actions</th>
            </tr>
          </thead>
          <tbody>
            {
              <tr className="font-bold bg-[#f3f8ff]">
                <th
                  className="text-center sticky top-0 font-bold text-sm"
                  style={{ left: `${0}px` }}
                ></th>
                {checkedStates?.columns?.depotdate && (
                  <th
                    className="text-center sticky top-0 font-bold text-sm"
                    style={{ left: `${gridcolumnWidths.checkboxWidth}px` }}
                  ></th>
                )}
                  {ADCompanyName=="Integrated Service Solutions Ltd"&&<th
                    className="text-center sticky top-0 font-bold text-sm"
                    style={{ left: `${gridcolumnWidths.depotDate}px` }}
                  ></th> }
                  {/* //!for service customers */}
                {checkedStates?.columns?.weekNo && (
                  <th
                    className="sticky top-0 font-bold text-center text-sm"
                    style={{ left: `${gridcolumnWidths.serviceCustomerswidth}px` }}
                  ></th>
                )}
                {checkedStates?.columns?.altfill && (
                  <th
                    className="sticky top-0 font-bold text-sm"
                    style={{ left: `${gridcolumnWidths.weekNo}px` }}
                  ></th>
                )}
                {checkedStates?.columns?.customer && (
                  <th
                    className="sticky top-0 font-bold text-sm"
                    style={{ left: `${gridcolumnWidths.altfill}px` }}
                  ></th>
                )}
                {checkedStates?.columns?.salesorder && (
                  <th
                    className="sticky top-0 font-bold text-sm"
                    style={{ left: `${gridcolumnWidths.customer}px` }}
                  ></th>
                )}
                {checkedStates?.columns?.salesOrderId && (
                  <th
                    className="sticky top-0 font-bold text-sm"
                    style={{ left: `${gridcolumnWidths.customer}px` }}
                  ></th>
                )}
                {checkedStates?.columns?.product && (
                  <th
                    className="sticky top-0 font-bold text-sm"
                    style={{ left: `${gridcolumnWidths.salesorder}px` }}
                  ></th>
                )}
                <th
                  className="text-center top-0 font-bold text-sm"
                  style={{ left: `${gridcolumnWidths.product}px` }}
                >
                  TOTAL
                </th>
                {/* )} */}
                <td className="!text-center sticky top-0 font-bold text-sm">
                  {columnTotals.casesOrdered ?? "-"}
                </td>
                <td className="!text-center sticky top-0 font-bold text-sm">
                  {columnTotals.casesDelivered ?? "-"}
                </td>
                <td className="!text-center sticky top-0 font-bold text-sm">
                  {columnTotals.casesDifference ?? "-"}
                </td>
                <td className="!text-center sticky top-0 font-bold text-sm">
                  {columnTotals.addedReasons ?? "-"}
                </td>
                <td className="sticky top-0 font-bold text-sm"></td>
                <td className="sticky top-0 font-bold text-sm"></td>
                <td className="sticky top-0 font-bold text-sm"></td>
                <td className="!text-center sticky top-0 font-bold text-sm">
                  {columnTotals.totalValue ?? "-"
                    ? `£${columnTotals.totalValue?.toFixed(2)}`
                    : "-"}
                </td>

                <td className="sticky top-0 font-bold text-sm"></td>
                <td className="sticky top-0 font-bold text-sm"></td>
                <td className="sticky top-0 font-bold text-sm"></td>
                <td className="sticky top-0 font-bold text-sm"></td>
                <td className="sticky top-0 font-bold text-sm"></td>
              </tr>
            }
            {allSelectedProducts.map((data, index) => {
              if (
                !seeAll &&
                data.CASES_DIFFERENCE - data.CASES_ADDED_REASONS == 0
              )
                return;

              const tooltip = data.LOCKED_BY
                ? `${data.LOCKED_BY} is currently editing this order.`
                : data.NEW_LINE_FLAG == 1
                ? "This is an additional order."
                : "";
              const isSelected = selectedRows?.includes(data);
              const rowHighlight = `${
                isSelected
                  ? "bg-locked-products"
                  : data.reasons?.length > 0 &&
                    data.CASES_DIFFERENCE - data.CASES_ADDED_REASONS > 0
                  ? "bg-needsupdate-status"
                  : !!data.LOCKED_BY
                  ? "bg-locked-products"
                  : data.NEW_LINE_FLAG == 1
                  ? "bg-volumechange-status"
                  : ""
              }`;
              return (
                <tr key={index}>
                  <th
                    className={`sticky top-0 font-normal text-sm ${
                      rowHighlight ? `!${rowHighlight}` : ""
                    }`}
                    style={{ left: `${0}px` }}
                    title={tooltip}
                  >
                    <div className="w-full flex justify-center">
                      <input
                        type="checkbox"
                        title={`${
                          data.NEW_LINE_FLAG == 1
                            ? "This is an additional order."
                            : ""
                        }`}
                        className="w-5 h-5 text-blue border-theme-blue2 rounded accent-skin-primary"
                        onChange={() => handleCheckboxChange(data)}
                        disabled={
                          (selectedMasterProductCode === "all" &&
                            masterProducts.length > 2) ||
                          selectedCustomer === "All Customers" ||
                          (data.LOCKED_BY !== null && data.LOCKED_BY !== "") ||
                          data.CASES_DIFFERENCE == 0
                        }
                        // disabled={data.CASES_ADDED_REASONS!=0}
                        checked={selectedRows.includes(data)}
                      />
                    </div>
                  </th>
                  {checkedStates?.columns?.depotdate && (
                    <th
                      className={`sticky top-0 font-normal text-sm ${
                        rowHighlight ? `!${rowHighlight}` : ""
                      }`}
                      style={{ left: `${gridcolumnWidths.checkboxWidth}px` }}
                      title={tooltip}
                    >
                      {data.DEPOT_DATE &&
                        new Date(data.DEPOT_DATE).toLocaleDateString("en-GB")}
                    </th>
                  )}
                    {ADCompanyName=="Integrated Service Solutions Ltd"&&<th
                      className={`sticky top-0 font-normal text-sm ${
                        rowHighlight ? `!${rowHighlight}` : ""
                      }`}
                      style={{ left: `${gridcolumnWidths.checkboxWidth}px` }}
                      title={tooltip}
                    >
                      {data.SERVICE_CUSTOMERS && data.SERVICE_CUSTOMERS}
                    </th>}
                    {/* //!for service customers */}
                  {checkedStates?.columns?.weekNo && (
                    <th
                      className={`sticky top-0 font-normal text-center text-sm ${
                        rowHighlight ? `!${rowHighlight}` : ""
                      }`}
                      style={{ left: `${gridcolumnWidths.depotdate}px` }}
                      title={tooltip}
                    >
                      {data?.FISCAL_WEEK}
                    </th>
                  )}
                  {checkedStates?.columns?.altfill && (
                    <th
                      className={`sticky top-0 font-normal  text-sm ${
                        rowHighlight ? `!${rowHighlight}` : ""
                      }`}
                      style={{ left: `${gridcolumnWidths.weekNo}px` }}
                      title={tooltip}
                    >
                      {data.ALTFILID}
                    </th>
                  )}
                  {checkedStates?.columns?.customer && (
                    <th
                      className={`sticky top-0 font-normal  text-sm ${
                        rowHighlight ? `!${rowHighlight}` : ""
                      }`}
                      style={{ left: `${gridcolumnWidths.altfill}px` }}
                      title={tooltip}
                    >
                      {data.CUSTOMER}
                    </th>
                  )}
                  {checkedStates?.columns?.salesorder && (
                    <th
                      className={`sticky top-0 font-normal text-sm ${
                        rowHighlight ? `!${rowHighlight}` : ""
                      }`}
                      style={{ left: `${gridcolumnWidths.customer}px` }}
                      title={tooltip}
                    >
                      {data.ORD_NUMBER}
                    </th>
                  )}
                  {checkedStates?.columns?.salesOrderId && (
                    <th
                      className={`sticky top-0 font-normal text-sm ${
                        rowHighlight ? `!${rowHighlight}` : ""
                      }`}
                      style={{ left: `${gridcolumnWidths.customer}px` }}
                      title={tooltip}
                    >
                      {data.ORD_ID}
                    </th>
                  )}
                  {checkedStates?.columns?.product && (
                    <th
                      className={`!w-80 sticky top-0 font-normal text-sm ${
                        rowHighlight ? `!${rowHighlight}` : ""
                      }`}
                      style={{ left: `${gridcolumnWidths.salesorder}px` }}
                      title={tooltip}
                    >
                      {data.PRODUCT_DESCRIPTION}
                    </th>
                  )}
                  <th
                    className={`top-0 text-center font-normal text-sm ${
                      rowHighlight ? `!${rowHighlight}` : ""
                    }`}
                    style={{ left: `${gridcolumnWidths.product}px` }}
                    title={tooltip}
                  >
                    {data.CASE_SIZE}
                  </th>
                  <td
                    className={`!text-center text-sm ${
                      rowHighlight ? `!${rowHighlight}` : ""
                    }`}
                    title={tooltip}
                  >
                    {data.CASES_ORIGINAL}
                  </td>
                  <td
                    className={`!text-center text-sm ${
                      rowHighlight ? `!${rowHighlight}` : ""
                    }`}
                    title={tooltip}
                  >
                    {data.CASES_DELIVERED}
                  </td>
                  <td
                    className={`!text-center text-sm font-bold ${
                      rowHighlight ? `!${rowHighlight}` : ""
                    }`}
                    title={tooltip}
                  >
                    {data.CASES_DIFFERENCE - data.CASES_ADDED_REASONS}
                  </td>
                  <td
                    className={`!text-center text-sm ${
                      rowHighlight ? `!${rowHighlight}` : ""
                    }`}
                    title={tooltip}
                  >
                    {data.CASES_ADDED_REASONS}
                  </td>
                  <td
                    className={`!text-center text-sm ${
                      rowHighlight ? `!${rowHighlight}` : ""
                    }`}
                    title={tooltip}
                  >
                    {`${data.SERVICE_LEVEL_PERCENT?.toFixed(2)}%`}
                  </td>
                  <td
                    className={`!text-center text-sm ${
                      rowHighlight ? `!${rowHighlight}` : ""
                    }`}
                    title={tooltip}
                  >
                    £{data.UNIT_PRICE?.toFixed(2)}
                  </td>
                  <td
                    className={`!text-center text-sm ${
                      rowHighlight ? `!${rowHighlight}` : ""
                    }`}
                    title={tooltip}
                  >
                    {`£${(data.CASE_SIZE * data.UNIT_PRICE)?.toFixed(2)}`}
                  </td>
                  <td
                    className={`!text-center text-sm ${
                      rowHighlight ? `!${rowHighlight}` : ""
                    }`}
                    title={tooltip}
                  >
                    {`£${(
                      data.CASE_SIZE *
                      data.UNIT_PRICE *
                      data.CASES_DIFFERENCE
                    )?.toFixed(2)}`}
                  </td>
                  <td
                    className={`!text-center text-sm ${
                      rowHighlight ? `!${rowHighlight}` : ""
                    }`}
                    title={tooltip}
                  >
                    <div
                      className={`rounded-lg capitalize px-2 py-1 !text-center ${
                        data.ORD_STATUS === "Cancelled"
                          ? "bg-[#ff2929] text-white"
                          : data.ORD_STATUS === "Open"
                          ? "bg-[#54C5ED] text-white"
                          : data.ORD_STATUS === "Invoiced"
                          ? "bg-[#FFAE00] text-white"
                          : data.ORD_STATUS === "Delivered"
                          ? "bg-[#3EAB58] text-white"
                          : data.ORD_STATUS === "Picked"
                          ? "bg-[#FF6C09] text-white"
                          : "bg-qtydiff-status !text-gray-700" // Default style for any other status
                      }`}
                      title={tooltip}
                    >
                      {data.ORD_STATUS.toLowerCase()}
                    </div>
                  </td>
                  <td
                    className={`relative ${
                      rowHighlight ? `!${rowHighlight}` : ""
                    }`}
                    title={tooltip}
                  >
                    {/* <div > */}
                    <span>{data.reasons[0]?.MAIN_REASON}</span>
                    <ReasonsDetails
                      reasonsData={data.reasons}
                      type="reasonsDetails"
                      seeAll={seeAll}
                    />
                    {/* </div> */}
                  </td>
                  <td
                    className={`!text-left text-sm capitalize ${
                      rowHighlight ? `!${rowHighlight}` : ""
                    }`}
                    title={tooltip}
                  >
                    {data.reasons[0]?.SUB_REASON}
                  </td>
                  <td
                    className={`!text-left text-sm capitalize ${
                      rowHighlight ? `!${rowHighlight}` : ""
                    }`}
                    title={tooltip}
                  >
                    <div className="w-full text-truncate2L ">
                      {data.reasons[0]?.COMMENT}
                    </div>
                    {/* {data.reasons[0]?.COMMENT} */}
                  </td>
                  <td
                    className={`!text-center text-sm !bg-[#f3f8ff] ${
                      rowHighlight ? `!${rowHighlight}` : ""
                    }`}
                    title={`${
                      !!data.LOCKED_BY
                        ? "You cannot edit the order while someone is already working on it."
                        : ""
                    }`}
                  >
                    <button
                      onClick={() => {
                        handleViewDetailsClick(data);
                        setIsBulkUpdate(false);
                      }}
                      className="cursor-pointer"
                      disabled={
                        !!data.LOCKED_BY ||
                        data.CASES_DIFFERENCE == 0 ||
                        selectedRows.length > 0
                      }
                    >
                      <Tooltip
                        content="View Order Details"
                        relationship="label"
                      >
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          viewBox="0 0 512 512"
                          className=" w-5 h-5 !text-skin-primary "
                        >
                          <path
                            fill="currentcolor"
                            d="M480 448c0 17.7-14.3 32-32 32l-256 0c-17.7 0-32-14.3-32-32l0-80-32 0 0 80c0 35.3 28.7 64 64 64l256 0c35.3 0 64-28.7 64-64l0-284.1c0-12.7-5.1-24.9-14.1-33.9L382.1 14.1c-9-9-21.2-14.1-33.9-14.1L192 0c-35.3 0-64 28.7-64 64l0 192 32 0 0-192c0-17.7 14.3-32 32-32l128 0 0 112c0 26.5 21.5 48 48 48l112 0 0 256zm-.5-288L368 160c-8.8 0-16-7.2-16-16l0-111.5c2.8 .7 5.4 2.1 7.4 4.2L475.3 152.6c2.1 2.1 3.5 4.6 4.2 7.4zM283.3 212.7c-6.2-6.2-16.4-6.2-22.6 0s-6.2 16.4 0 22.6L329.4 304 16 304c-8.8 0-16 7.2-16 16s7.2 16 16 16l313.4 0-68.7 68.7c-6.2 6.2-6.2 16.4 0 22.6s16.4 6.2 22.6 0l96-96c6.2-6.2 6.2-16.4 0-22.6l-96-96z"
                          />
                        </svg>
                      </Tooltip>
                    </button>
                  </td>
                </tr>
              );
            })}
          </tbody>
        </table>
      )}
      {isOpen && selectedRows.length > 0 && (
        <ViewDetails
          data={selectedRows}
          setData={setSelectedRows}
          setAllSelectedProducts={setAllSelectedProducts}
          setMapForReasonsParentsAndTheirCorrespondingChildren={
            setMapForReasonsParentsAndTheirCorrespondingChildren
          }
          reasonsMasterList={reasonsMasterList}
          parentReasonList={parentReasonList}
          reasonsData={reasonsData}
          fetchReasonData={fetchReasonData}
          userData={userData}
          isOpen={isOpen}
          setIsOpen={setIsOpen}
          bulkUpdateData={bulkUpdateData}
          isBulkUpdate={isBulkUpdate}
          setReasonsData={setReasonsData}
          setBulkUpdateData={setBulkUpdateData}
          setIsHeaderChecked={setIsHeaderChecked}
          setSelectedRows={setSelectedRows}
        />
      )}
    </div>
  );
};

export default SLTable;
