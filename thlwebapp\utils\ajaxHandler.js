import React, { useState } from "react";
import { apiConfig } from "@/services/apiConfig";
import { result } from "lodash";
import { useRouter } from "next/router";
import { ToastContainer, toast } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import Cookies from "js-cookie";
import { logout } from "./secureStorage";

//const router = useRouter();
const serverAddress=apiConfig.serverAddress;
const addRequestAjaxCall = async (data, routeUrl) => {
  console.log("addRequestAjaxCall", data, routeUrl);
  const resultFetch = await fetch(`${serverAddress}/api/suppliers/${routeUrl}`, {
    method: "POST",
    headers: {
      Accept: "application/json",
      "Content-Type": "application/json",
    },
    credentials: "include", // Use session authentication
    body: JSON.stringify(data),
  }).then((res) => {
    if (res.status === 400 || res.status === 401) {
      toast.error("Connection lost or Invalid Token", {
        position: "top-right",
      });
      setTimeout(function () {
        logout();
      }, 3000);
    }
    if (res.status === 200) {
      return res.json();
    }
    return Promise.reject(res);
  });

  return await resultFetch[0].id;
};

const updateRequestAjaxCall = async (data, routeUrl, id) => {
  fetch(`${serverAddress}suppliers/${routeUrl}/${id}`, {
    method: "PUT",
    headers: {
      Accept: "application/json",
      "Content-Type": "application/json",
    },
    credentials: "include", // Use session authentication
    body: JSON.stringify(data),
  })
    .then((res) => {
      if (res.status === 400 || res.status === 401) {
        toast.error("Connection lost or Invalid Token", {
          position: "top-right",
        });
        setTimeout(function () {
          logout();
        }, 3000);
      }
      if (res.status === 200) {
        return res.json();
      }
      return Promise.reject(res);
    })
    .catch((err) => {
      toast.error(`Supplier not copy successfully. please try again`, {
        position: "bottom-right",
      });
    });
};
let insertProphetCode;
const addProphetAjaxCall = (prophets) => {
  const resultFetch = fetch(`${serverAddress}suppliers/check-prophet-code`, {
    method: "POST",
    headers: {
      Accept: "application/json",
      "Content-Type": "application/json",
    },
    credentials: "include", // Use session authentication
    body: JSON.stringify(prophets),
  }).then((res) => {
    if (res.status === 200) {
      return res.json();
    }
    return Promise.reject(res);
  });

  const finalResult = resultFetch;

  const creteProphetCode = finalResult.then((result) => {
    if (result.data || result == "") {
      insertProphetCode = fetch(
        `${serverAddress}suppliers/create-prophet-code`,
        {
          method: "POST",
          headers: {
            Accept: "application/json",
            "Content-Type": "application/json",
          },
          credentials: "include", // Use session authentication
          body: JSON.stringify(prophets),
        }
      ).then((res) => {
        if (res.status === 200) {
          return res.json();
        }
        return Promise.reject(res);
      });
    }
    return insertProphetCode;
  });

  return creteProphetCode;
};

const getLinksByRoleAjaxCall = async (role_id) => {
  const resultFetch = await fetch(
    `${serverAddress}suppliers/get-supplier-links-by-role/${role_id}`,
    {
      method: "GET",
      headers: {
        Accept: "application/json",
        "Content-Type": "application/json",
      },
      credentials: "include", // Use session authentication
    }
  ).then((res) => {
    if (res.status === 200) {
      return res.json();
    }
    return Promise.reject(res);
  });

  const finalResult = await resultFetch;
  return finalResult;
};

module.exports = {
  addRequestAjaxCall,
  updateRequestAjaxCall,
  addProphetAjaxCall,
  getLinksByRoleAjaxCall,
};
