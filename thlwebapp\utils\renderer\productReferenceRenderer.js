import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import {
  faCopy,
  faPenToSquare,
  faXmark,
  faInfo,
} from "@fortawesome/free-solid-svg-icons";
import { Router, useRouter } from "next/router";
import Link from "next/link";
import { apiConfig } from "@/services/apiConfig";
import { useState, useEffect, Fragment } from "react";
import Cookies from "js-cookie";
import * as XLSX from "xlsx";
import exportExcelData from "../exportExcel";
import { ToastContainer, toast } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import { useUser } from "@/utils/userContext";
import { getCookieData } from "@/utils/getCookieData";
import { ThreeCircles } from "react-loader-spinner";
import { Dialog, Transition } from "@headlessui/react";

const productReferenceRenderer = (params, userData, isUpdateMode) => {
  // console.log("params", params);
  // console.log("userdata", userData);
  // console.log("value", value);
  const router = useRouter();
  const serverAddress = apiConfig.serverAddress;
  const [loading, setLoading] = useState(false);
  const [isCancelOpen, setIsCancelOpen] = useState(false);
  const [cancelledReasonapi, setCancelledReasonapi] = useState("");
  // const [disabledEditButton, setDisabledEditButton] = useState(false);
  // Logic to determine if the button should be disabled
  // useEffect(() => {
  //   // Example condition: disable if not in update mode or if some other condition is met
  //   if (isUpdateMode) {
  //     setDisabledEditButton(true);
  //   } else {
  //     setDisabledEditButton(false);
  //   }
  // }, [isUpdateMode]);
  
  const editProduct = (params) => {
    if (typeof window !== "undefined") {
        params.setCode(params.data.code);
        params.setDescription(params.data.label);
        params.setValue(params.data.value);
        params.setIsButtonDisabled(false);
        params.setIsUpdateMode(true); 
        // setDisabledEditButton(true);
        
    }
  };
  // console.log(params)

  return ( 
    <>
      {/* <ToastContainer limit={1} /> */}
      <div className="flex flex-row gap-4 justify-center text-blue-500">
        {params.data.is_new == true &&
            <button
            onClick={() => editProduct(params)}
            disabled = {isUpdateMode}
            >
            <FontAwesomeIcon
                icon={faPenToSquare}
                size="lg"
                title="Edit Product"
                className="text-skin-primary"
            />
            </button>
        }
      </div>
    </>
  );
};

export default productReferenceRenderer;
