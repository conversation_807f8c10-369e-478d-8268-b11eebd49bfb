"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/variety/[productId]/edit",{

/***/ "./pages/variety/[productId]/edit/index.js":
/*!*************************************************!*\
  !*** ./pages/variety/[productId]/edit/index.js ***!
  \*************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __N_SSP: function() { return /* binding */ __N_SSP; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _components_Layout__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/Layout */ \"./components/Layout.js\");\n/* harmony import */ var _services_apiConfig__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/services/apiConfig */ \"./services/apiConfig.js\");\n/* harmony import */ var js_cookie__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! js-cookie */ \"./node_modules/js-cookie/dist/js.cookie.mjs\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var react_loader_spinner__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! react-loader-spinner */ \"./node_modules/react-loader-spinner/dist/module.js\");\n/* harmony import */ var react_toastify__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! react-toastify */ \"./node_modules/react-toastify/dist/react-toastify.esm.mjs\");\n/* harmony import */ var react_toastify_dist_ReactToastify_css__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! react-toastify/dist/ReactToastify.css */ \"./node_modules/react-toastify/dist/ReactToastify.css\");\n/* harmony import */ var react_toastify_dist_ReactToastify_css__WEBPACK_IMPORTED_MODULE_8___default = /*#__PURE__*/__webpack_require__.n(react_toastify_dist_ReactToastify_css__WEBPACK_IMPORTED_MODULE_8__);\n/* harmony import */ var _components_NewVarietyRequest__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/NewVarietyRequest */ \"./components/NewVarietyRequest.js\");\n/* harmony import */ var _utils_extractCompanyFromEmail__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/utils/extractCompanyFromEmail */ \"./utils/extractCompanyFromEmail.js\");\n/* harmony import */ var _utils_secureStorage__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/utils/secureStorage */ \"./utils/secureStorage.js\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\nconst index = (param)=>{\n    let { userData } = param;\n    _s();\n    const [dropdowns, setDropdowns] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(null);\n    const [newVarietyData, setNewVarietyData] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(null);\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_4__.useRouter)();\n    const { productId } = router.query;\n    (0,react__WEBPACK_IMPORTED_MODULE_5__.useEffect)(()=>{\n        // const company = Cookies.get(\"company\");\n        const company = (userData === null || userData === void 0 ? void 0 : userData.company) || (0,_utils_extractCompanyFromEmail__WEBPACK_IMPORTED_MODULE_10__.extractCompanyFromEmail)(userData === null || userData === void 0 ? void 0 : userData.email);\n        let prophetId = 1;\n        if (company == \"dpsltd\") {\n            prophetId = 1;\n        } else if (company == \"efcltd\") {\n            prophetId = 3;\n        } else if (company == \"fpp-ltd\") {\n            prophetId = 4;\n        }\n        const fetchData = async ()=>{\n            const serverAddress = _services_apiConfig__WEBPACK_IMPORTED_MODULE_2__.apiConfig.serverAddress;\n            try {\n                const allDropDowns = [\n                    \"masterProductCode\"\n                ];\n                const dropdownsRequest = await fetch(\"\".concat(serverAddress, \"products/get-products-dropdowns-list?prophetId=\").concat(prophetId), {\n                    method: \"POST\",\n                    headers: {\n                        Accept: \"application/json\",\n                        \"Content-Type\": \"application/json\"\n                    },\n                    credentials: \"include\",\n                    body: JSON.stringify(allDropDowns)\n                });\n                // console.log(\"dropdownsRequest\",dropdownsRequest);\n                if (dropdownsRequest.status === 401) {\n                    react_toastify__WEBPACK_IMPORTED_MODULE_7__.toast.error(\"Your session has expired. Please log in again.\");\n                    setTimeout(async ()=>{\n                        await (0,_utils_secureStorage__WEBPACK_IMPORTED_MODULE_11__.logout)();\n                        const redirectUrl = \"/login?redirect=\".concat(encodeURIComponent(window.location.pathname));\n                        router.push(redirectUrl);\n                    }, 3000);\n                    return null;\n                }\n                const newVarietyRequest = fetch(\"\".concat(serverAddress, \"products/get-nv-product-by-id/\").concat(productId), {\n                    method: \"GET\",\n                    headers: {\n                        Accept: \"application/json\",\n                        \"Content-Type\": \"application/json\"\n                    },\n                    credentials: \"include\"\n                });\n                const [dropdownsResponse, newVarietyResponse] = await Promise.all([\n                    dropdownsRequest,\n                    newVarietyRequest\n                ]);\n                const allDropdownsList = await dropdownsResponse.json();\n                const newVarietyData = await newVarietyResponse.json();\n                setDropdowns(allDropdownsList);\n                setNewVarietyData(newVarietyData);\n            } catch (error) {\n                console.error(\"Error fetching data\", error);\n            }\n        };\n        if (productId) {\n            fetchData();\n        }\n    }, [\n        productId\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Layout__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n        userData: userData,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_toastify__WEBPACK_IMPORTED_MODULE_7__.ToastContainer, {\n                limit: 1\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\variety\\\\[productId]\\\\edit\\\\index.js\",\n                lineNumber: 98,\n                columnNumber: 7\n            }, undefined),\n            !dropdowns && !newVarietyData ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    display: \"flex\",\n                    alignItems: \"center\",\n                    justifyContent: \"center\",\n                    height: \"100vh\"\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_loader_spinner__WEBPACK_IMPORTED_MODULE_6__.ThreeCircles, {\n                    color: \"#002D73\",\n                    height: 50,\n                    width: 50,\n                    visible: true,\n                    ariaLabel: \"oval-loading\",\n                    secondaryColor: \"#0066FF\",\n                    strokeWidth: 2,\n                    strokeWidthSecondary: 2\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\variety\\\\[productId]\\\\edit\\\\index.js\",\n                    lineNumber: 108,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\variety\\\\[productId]\\\\edit\\\\index.js\",\n                lineNumber: 100,\n                columnNumber: 9\n            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_NewVarietyRequest__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                dropdowns: dropdowns,\n                userData: userData,\n                newVarietyData: newVarietyData[0],\n                pageType: \"update\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\variety\\\\[productId]\\\\edit\\\\index.js\",\n                lineNumber: 120,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\variety\\\\[productId]\\\\edit\\\\index.js\",\n        lineNumber: 97,\n        columnNumber: 5\n    }, undefined);\n};\n_s(index, \"Hed4/xYb5dPQ/rk452hUP3Z4GUA=\", false, function() {\n    return [\n        next_router__WEBPACK_IMPORTED_MODULE_4__.useRouter\n    ];\n});\nvar __N_SSP = true;\n/* harmony default export */ __webpack_exports__[\"default\"] = (index);\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevExports = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevExports) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports on update so we can compare the boundary\n                // signatures.\n                module.hot.dispose(function (data) {\n                    data.prevExports = currentExports;\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevExports !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevExports, currentExports)) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevExports !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./pages/variety/[productId]/edit/index.js\n"));

/***/ })

});