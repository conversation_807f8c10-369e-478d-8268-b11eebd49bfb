import {
  Dialog,
  DialogTrigger,
  DialogSurface,
  DialogTitle,
  DialogBody,
  DialogActions,
  DialogContent,
  Button,
} from "@fluentui/react-components";
import { FluentProvider, webLightTheme } from "@fluentui/react-components";

const RequestDialoge = ({
  isOpen,
  onClose,
  handleFormType,
  selectedRequestType,
  handleRequestType,
  isIssUser,
  isIssProcurmentUser,
  admin=0,
}) => {
  console.log("isIssUser",isIssUser);
  console.log("admin",admin);
  console.log("isIssProcurmentUser",!isIssProcurmentUser);
  return (
    <FluentProvider
      theme={webLightTheme}
      className="!bg-transparent"
      style={{ fontFamily: "poppinsregular" }}
    >
      <Dialog>
        <DialogTrigger disableButtonEnhancement>
          <button className="ml-2 px-3 py-1 p-[6px] border rounded-md bg-skin-primary text-white text-sm cursor-pointer" disabled={isIssUser && (!isIssProcurmentUser || admin)}> 
            {/* && admin}> */}
            Add Request
          </button>
        </DialogTrigger>
        <DialogSurface>
          <DialogBody>
            <DialogTitle>
              <div>
                <div className="flex flex-row justify-between items-baseline">
                  <h3 className="w-full">Request Type</h3>
                </div>
                <hr className="border border-gray-200"></hr>
              </div>
            </DialogTitle>
            <DialogContent className="flex flex-col">
              <label className="pt-6">
                Select the request type to proceed.
              </label>
              <div className="flex gap-6 w-auto mb-4 py-3">
                <div className={`flex gap-3 items-center border border-light-gray rounded-md px-3 py-1 ${
                    !isIssUser ? "" : "variety-disabled-block "
                  }`}>
                  <input
                    type="radio"
                    name="requesttype"
                    value="rawMaterialRequest"
                    id="raw-material"
                    className={`border rounded-md cursor-pointer w-5 h-5`}
                    checked={isIssUser ? false : selectedRequestType === "rawMaterialRequest"}
                    disabled={isIssUser}
                    onChange={isIssUser
                      ? handleRequestType("packagingform") // This would be the function to execute
                      : handleRequestType("rawMaterialRequest") // This would be the function to execute
                    }                  />
                  <label
                    htmlFor="raw-material"
                    className="font-bold cursor-pointer"
                  >
                    Raw Material
                  </label>
                </div>

                <div
                  className={`flex gap-3 items-center border border-light-gray rounded-md px-3 py-1 ${
                    !isIssUser ? "" : "variety-disabled-block "
                  }`}
                >
                  <input
                    type="radio"
                    name="requesttype"
                    value="newVarietyRequest"
                    id="new-variety"
                    disabled={isIssUser}
                    className={` border rounded-md cursor-pointer h-5 w-5`}
                    checked={isIssUser ? false : selectedRequestType === "newVarietyRequest"}
                    onChange={isIssUser ? () => handleRequestType("packagingform") : () => handleRequestType("newVarietyRequest")}
                  />
                  <label
                    htmlFor="new-variety"
                    className="font-bold cursor-pointer"
                  >
                    New Variety
                  </label>
                </div>
                {isIssProcurmentUser && <div
                  className={`flex gap-3 items-center border border-light-gray rounded-md px-3 py-1 ${
                    (isIssUser && isIssProcurmentUser) ? "" : "variety-disabled-block "
                  }`}
                >
                  <input
                    type="radio"
                    name="requesttype"
                    value="packagingform"
                    id="packaging"
                    disabled={!(isIssUser && isIssProcurmentUser)}
                    className={`border rounded-md cursor-pointer h-5 w-5`}
                    checked={isIssUser ? true : selectedRequestType === "packagingform"}
                    onChange={() => handleRequestType("packagingform")}
                  />
                  <label
                    htmlFor="packaging"
                    className="font-bold cursor-pointer"
                  >
                    Packaging
                  </label>
                </div>}

                {/* <div className="flex gap-3 items-center ">
                                <input
                                    type="radio"
                                    name="requesttype"
                                    value="finishedProductRequest"
                                    id="finished-product"
                                    className={`border rounded-md cursor-pointer`}
                                    checked={selectedRequestType === "finishedProductRequest"}
                                    onChange={() => handleRequestType("finishedProductRequest")}
                                />
                                <label htmlFor="finished-product" className="font-bold">
                                    Finished Product
                                </label>
                            </div> */}
                        </div>

                    </DialogContent>
                    <DialogActions>
                        <DialogTrigger disableButtonEnhancement>
                            <button className="ml-2 px-3 py-1 p-[6px] border rounded-md border-skin-primary text-skin-primary cursor-pointer ">Cancel</button>
                        </DialogTrigger>
                        <button
                        onClick={handleFormType}
                        className="ml-2 px-6 py-1 p-[6px] border rounded-md bg-skin-primary text-white cursor-pointer ">Continue</button>
                    </DialogActions>
                </DialogBody>
            </DialogSurface>
        </Dialog>
        </FluentProvider>
    )
}

export default RequestDialoge;
