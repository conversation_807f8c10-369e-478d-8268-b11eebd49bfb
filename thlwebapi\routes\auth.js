"use strict";

const express = require("express");
const { createUserSession, validateSession, destroySession } = require("../middleware/sessionAuth");
const sessionManager = require("../middleware/sessionManager");
const userData = require("../data/users");
const router = express.Router();

// Login endpoint - creates session after MSAL verification
router.post("/login", createUserSession, (req, res) => {
  // Session created successfully
  res.json({ 
    message: "Login successful",
    user: req.session.user
  });
});

// Get current user session
router.get("/me", validateSession, (req, res) => {
  console.log("req.session.user");
  res.json({ user: req.session.user });
});

// Update company and theme in session
router.post("/update-company", validateSession, async (req, res) => {
  try {
    const { company } = req.body;
    
    if (!company) {
      return res.status(400).json({ error: "Company is required" });
    }

    // Map company values to proper format and get theme/ADCompanyName
    let themeColor = '#022D71'; // Default theme
    let ADCompanyName = 'DPS'; // Default company name
    let actualCompany = company;

    switch (company) {
      case 'dpsltd':
        themeColor = '#022D71';
        ADCompanyName = 'DPS';
        actualCompany = 'dpsltd';
        break;
      case 'dpsltdms':
        themeColor = '#0d6bfc';
        ADCompanyName = 'DPS MS';
        actualCompany = 'dpsltd'; // Note: dpsltdms maps to dpsltd for company but different theme
        break;
      case 'efcltd':
        themeColor = '#3eab58';
        ADCompanyName = 'EFC';
        actualCompany = 'efcltd';
        break;
      case 'fpp-ltd':
        themeColor = '#3d6546';
        ADCompanyName = 'Fresh Produce Partners';
        actualCompany = 'fpp-ltd';
        break;
      case 'issproduce':
        themeColor = '#ABC400';
        ADCompanyName = 'Integrated Service Solutions Ltd';
        actualCompany = 'issproduce';
        break;
      default:
        return res.status(400).json({ error: "Invalid company" });
    }

    // Get theme from database (fallback to hardcoded if not found)
    try {
      const themeResult = await userData.getTheme(actualCompany, ADCompanyName);
      if (themeResult && themeResult.length > 0) {
        themeColor = themeResult[0].themeColour;
      }
    } catch (error) {
      console.error("Error fetching theme from database:", error);
      // Continue with hardcoded theme
    }

    // Update session data
    const updatedUserData = {
      ...req.session.user,
      company: actualCompany,
      ADCompanyName: ADCompanyName,
      companyName: ADCompanyName,
      theme: themeColor
    };

    // Update the session
    const updateSuccess = sessionManager.updateSession(req.session.id, updatedUserData);
    
    if (!updateSuccess) {
      return res.status(500).json({ error: "Failed to update session" });
    }

    // Update the request session object for immediate use
    req.session.user = updatedUserData;

    res.json({ 
      message: "Company updated successfully",
      user: updatedUserData
    });

  } catch (error) {
    console.error("Error updating company:", error);
    res.status(500).json({ error: "Internal server error" });
  }
});

// Logout endpoint
router.post("/logout", destroySession);

// Refresh session (extend expiry)
router.post("/refresh", validateSession, (req, res) => {
  // Session automatically refreshed by middleware
  res.json({ message: "Session refreshed" });
});

module.exports = router; 