"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/supplier/[supplierId]/edit/forms",{

/***/ "./components/GeneralSection.js":
/*!**************************************!*\
  !*** ./components/GeneralSection.js ***!
  \**************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _fortawesome_react_fontawesome__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @fortawesome/react-fontawesome */ \"./node_modules/@fortawesome/react-fontawesome/index.es.js\");\n/* harmony import */ var _fortawesome_free_solid_svg_icons__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! @fortawesome/free-solid-svg-icons */ \"./node_modules/@fortawesome/free-solid-svg-icons/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_Dialog_Transition_headlessui_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=Dialog,Transition!=!@headlessui/react */ \"__barrel_optimize__?names=Dialog,Transition!=!./node_modules/@headlessui/react/dist/headlessui.esm.js\");\n/* harmony import */ var ag_grid_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ag-grid-react */ \"./node_modules/ag-grid-react/lib/main.js\");\n/* harmony import */ var ag_grid_react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(ag_grid_react__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var ag_grid_community_styles_ag_grid_css__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ag-grid-community/styles//ag-grid.css */ \"./node_modules/ag-grid-community/styles/ag-grid.css\");\n/* harmony import */ var ag_grid_community_styles_ag_grid_css__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(ag_grid_community_styles_ag_grid_css__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var ag_grid_community_styles_ag_theme_alpine_css__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ag-grid-community/styles//ag-theme-alpine.css */ \"./node_modules/ag-grid-community/styles/ag-theme-alpine.css\");\n/* harmony import */ var ag_grid_community_styles_ag_theme_alpine_css__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(ag_grid_community_styles_ag_theme_alpine_css__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! next/link */ \"./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var _components_Layout__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/Layout */ \"./components/Layout.js\");\n/* harmony import */ var _components_Steps__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/Steps */ \"./components/Steps.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_9___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_9__);\n/* harmony import */ var _services_apiConfig__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/services/apiConfig */ \"./services/apiConfig.js\");\n/* harmony import */ var react_loader_spinner__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! react-loader-spinner */ \"./node_modules/react-loader-spinner/dist/module.js\");\n/* harmony import */ var react_toastify__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! react-toastify */ \"./node_modules/react-toastify/dist/react-toastify.esm.mjs\");\n/* harmony import */ var react_toastify_dist_ReactToastify_css__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! react-toastify/dist/ReactToastify.css */ \"./node_modules/react-toastify/dist/ReactToastify.css\");\n/* harmony import */ var react_toastify_dist_ReactToastify_css__WEBPACK_IMPORTED_MODULE_13___default = /*#__PURE__*/__webpack_require__.n(react_toastify_dist_ReactToastify_css__WEBPACK_IMPORTED_MODULE_13__);\n/* harmony import */ var _utils_userContext__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/utils/userContext */ \"./utils/userContext.js\");\n/* harmony import */ var _utils_getCookieData__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @/utils/getCookieData */ \"./utils/getCookieData.js\");\n/* harmony import */ var next_dist_server_body_streams__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! next/dist/server/body-streams */ \"./node_modules/next/dist/server/body-streams.js\");\n/* harmony import */ var next_dist_server_body_streams__WEBPACK_IMPORTED_MODULE_16___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_body_streams__WEBPACK_IMPORTED_MODULE_16__);\n/* harmony import */ var _utils_ajaxHandler__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! @/utils/ajaxHandler */ \"./utils/ajaxHandler.js\");\n/* harmony import */ var _utils_ajaxHandler__WEBPACK_IMPORTED_MODULE_17___default = /*#__PURE__*/__webpack_require__.n(_utils_ajaxHandler__WEBPACK_IMPORTED_MODULE_17__);\n/* harmony import */ var _utils_auth_auth__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! @/utils/auth/auth */ \"./utils/auth/auth.js\");\n/* harmony import */ var _azure_msal_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! @azure/msal-react */ \"./node_modules/@azure/msal-react/dist/index.js\");\n/* harmony import */ var _components_ValidationAlertBox__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! @/components/ValidationAlertBox */ \"./components/ValidationAlertBox.js\");\n/* harmony import */ var _utils_loaders_loadingContext__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! @/utils/loaders/loadingContext */ \"./utils/loaders/loadingContext.js\");\n/* harmony import */ var js_cookie__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! js-cookie */ \"./node_modules/js-cookie/dist/js.cookie.mjs\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst GeneralSection = (param)=>{\n    let { data, onSubmit, isEdit, dropdowns, setNavType, navType } = param;\n    var _dropdowns_countries;\n    _s();\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_9__.useRouter)();\n    const user = (0,_utils_getCookieData__WEBPACK_IMPORTED_MODULE_15__.getCookieData)(\"user\");\n    const { supplierId } = router.query;\n    const [tradingName, setTradingName] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [isTradingNameValid, setIsTradingNameValid] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true); //trading name validation\n    const [tradingNameError, setTradingNameError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [allowedSections, setAllowedSections] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [telephone, setTelephone] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [isTelephoneValid, setIsTelephoneValid] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true); //telephone validation\n    const [telephoneError, setTelephoneError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [email, setEmail] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [isEmailValid, setIsEmailValid] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true); //email validation.\n    const [addressLine1, setAddressLine1] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [isAddressLine1Valid, setIsAddressLine1Valid] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [addressLine1Error, setAddressLine1Error] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [isAddressLine2Valid, setIsAddressLine2Valid] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [addressLine2Error, setAddressLine2Error] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [addressLine2, setAddressLine2] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [addressLine3, setAddressLine3] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [addressLine4, setAddressLine4] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [countryError, setCountryError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [supplierName, setSupplierName] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [contactId, setContactId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isSupplierNameValid, setIsSupplierNameValid] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true); //supplier name validation part\n    const [supplierNameError, setSupplierNameError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [supplierEmail, setSupplierEmail] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [isSupplierEmailValid, setIsSupplierEmailValid] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [supplierTelephone, setSupplierTelephone] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [isSupplierTelephoneValid, setIsSupplierTelephoneValid] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true); //supplier telephone validation.\n    const [supplierTelephoneError, setSupplierTelephoneError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [TypeOfContact, setTypeOfContact] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [atLeastOneContactEntered, setAtLeastOneContactEntered] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isTypeofContactValid, setIsTypeofContactValid] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const gridRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const [contacts_Json, setcontacts_json] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [prophets, setProphets] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [postalCode, setPostalCode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [isPostalCodeValid, setIsPostalCodeValid] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [postalCodeError, setPostalCodeError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [country, setCountry] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [countryName, setCountryName] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isCommonError, setCommonError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const { instance, accounts } = (0,_azure_msal_react__WEBPACK_IMPORTED_MODULE_19__.useMsal)();\n    const [isOpen, setIsOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isCancelled, setIsCancelled] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isContinue, setIsContinue] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [formChange, setFormChange] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [countryChange, setCountryChange] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [status, setStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [isContactVisible, setIsContactVisible] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [role, setRole] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [ggn, setGGN] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [redTractor, setRedTractor] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [isSupplierAccount, setIsSupplierAccount] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [prophetObj, setProphetObj] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const { setIsLoading } = (0,_utils_loaders_loadingContext__WEBPACK_IMPORTED_MODULE_21__.useLoading)();\n    const [prophetsIds, setProphetIds] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)();\n    const validateTradingName = (name)=>{\n        const isValid = (name === null || name === void 0 ? void 0 : name.length) <= 50;\n        setIsTradingNameValid(isValid);\n        setTradingNameError(isValid ? \"\" : \"Trading name must be 50 characters or less.\");\n    };\n    const handleTradingNameChange = (event)=>{\n        setFormChange(true);\n        const newName = event.target.value;\n        setTradingName(newName);\n        validateTradingName(newName.trim());\n    };\n    const handleEmailChange = (event)=>{\n        setFormChange(true);\n        const newEmail = event.target.value;\n        setEmail(newEmail);\n        if (role === null || role === void 0 ? void 0 : role.includes(1)) {\n            setIsEmailValid(validateEmail(newEmail.trim()));\n        }\n    };\n    const validateEmail = (email)=>{\n        const emailRegex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$/;\n        // const emailRegex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,6}$/;\n        return emailRegex.test(email);\n    };\n    const validateTelephone = (telephone)=>{\n        const telephoneRegex = /^(?:\\+\\d{1,3}\\s?)?[\\d\\s]{9,15}$/;\n        const isValid = telephoneRegex.test(telephone);\n        if (role === null || role === void 0 ? void 0 : role.includes(1)) {\n            setIsTelephoneValid(isValid);\n            setTelephoneError(isValid ? \"\" : \"Please enter a valid telephone number.\");\n        }\n    };\n    const handleTelephoneChange = (event)=>{\n        setFormChange(true);\n        const newTelephone = event.target.value;\n        setTelephone(newTelephone);\n        validateTelephone(newTelephone.trim());\n    };\n    const validateAddressLine1 = (line)=>{\n        const isValid = (line === null || line === void 0 ? void 0 : line.length) <= 50;\n        setIsAddressLine1Valid(isValid);\n        setAddressLine1Error(isValid ? \"\" : \"Please add a small address of less than 50 characters.\");\n    };\n    const validateAddressLine2 = (line)=>{\n        const isValid = (line === null || line === void 0 ? void 0 : line.length) <= 50;\n        setIsAddressLine2Valid(isValid);\n        setAddressLine2Error(isValid ? \"\" : \"Please add a small address of less than 50 characters.\");\n    };\n    const handleAddressLine1Change = (event)=>{\n        setFormChange(true);\n        const newAddressLine1 = event.target.value;\n        setAddressLine1(newAddressLine1);\n        validateAddressLine1(newAddressLine1.trim());\n    };\n    const handleAddressLine2Change = (event)=>{\n        setFormChange(true);\n        const newAddressLine2 = event.target.value;\n        setAddressLine2(newAddressLine2);\n        validateAddressLine2(newAddressLine2.trim());\n    };\n    const handleAddressLine3Change = (event)=>{\n        setFormChange(true);\n        setAddressLine3(event.target.value);\n    };\n    const handleAddressLine4Change = (event)=>{\n        setFormChange(true);\n        setAddressLine4(event.target.value);\n    };\n    const handlePostalCodeChange = (event)=>{\n        setFormChange(true);\n        const newPostalCode = event.target.value.toUpperCase(); // postal code\n        setPostalCode(newPostalCode);\n        validatePostalCode(newPostalCode.trim());\n    };\n    const validatePostalCode = (code)=>{\n        // const regex =\n        //   /^[A-Z]{1,2}([0-9]{1,2}|[0-9][A-Z])\\s*[0-9][A-Z]{2}$|^([A-Z]\\d{2,3}\\s?[A-Z]{2})$/;\n        const regex = /^[A-Z]{1,2}([0-9]{1,2}|[0-9][A-Z])\\s*[0-9][A-Z]{2}$|^([A-Z]\\d{2,3}\\s?[A-Z]{2})$|^\\d{5}$/;\n        //const isValid = regex.test(code);\n        const isValid = postalCode !== null || postalCode !== \"\" || postalCode.length > 15 ? true : false;\n        setIsPostalCodeValid(isValid);\n        setPostalCodeError(isValid ? \"\" : \"Please enter a valid UK postal code (XX9 9XX format), zip code (ANN NAA format), or a 5-digit number.\");\n    };\n    const validateSupplierName = (name)=>{\n        const isValid = (name === null || name === void 0 ? void 0 : name.length) <= 50;\n        setIsSupplierNameValid(isValid);\n        setSupplierNameError(isValid ? \"\" : \"Supplier name must be 50 characters or less.\");\n    };\n    const handleSupplierNameChange = (event)=>{\n        setFormChange(true);\n        const newName = event.target.value;\n        setSupplierName(newName);\n        validateSupplierName(newName.trim());\n    };\n    const handleSupplierEmailChange = (event)=>{\n        if (role === null || role === void 0 ? void 0 : role.includes(1)) {\n            setFormChange(true);\n            const newSupplierEmail = event.target.value;\n            setSupplierEmail(newSupplierEmail);\n            setIsSupplierEmailValid(validEmail(newSupplierEmail.trim()));\n        }\n    };\n    const handleSupplierTelephoneChange = (event)=>{\n        setFormChange(true);\n        const newSupplierTelephone = event.target.value;\n        setSupplierTelephone(newSupplierTelephone);\n        validateSupplierTelephone(newSupplierTelephone.trim());\n    };\n    const validateSupplierTelephone = (telephone)=>{\n        const telephoneRegex = /^(?:\\+\\d{1,3}\\s?)?[\\d\\s]{9,15}$/;\n        const isValid = telephoneRegex.test(telephone);\n        setIsSupplierTelephoneValid(isValid);\n        setSupplierTelephoneError(isValid ? \"\" : \"Please enter a valid supplier telephone number.\");\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        var _data_, _data_1, _data_2, _data_3, _data_4, _data_5, _data_6, _data_7, _data_8, _data_9, _data_10, _data_11, _data_12, _data_13, _data_14, _data_15, _data_16;\n        const prophetsIdsCookie = js_cookie__WEBPACK_IMPORTED_MODULE_22__[\"default\"].get(\"prophets\");\n        const prophetsIds = prophetsIdsCookie ? parseInt(prophetsIdsCookie, 10) : null;\n        setProphetIds(prophetsIds);\n        if (true) {\n            const sectionsString = localStorage.getItem(\"allowedSections\");\n            if (sectionsString) {\n                const parsedSections = sectionsString.split(\",\");\n                setAllowedSections(parsedSections);\n            }\n        }\n        var _data__trading_name, _ref;\n        setTradingName((_ref = (_data__trading_name = (_data_ = data[0]) === null || _data_ === void 0 ? void 0 : _data_.trading_name) !== null && _data__trading_name !== void 0 ? _data__trading_name : (_data_1 = data[0]) === null || _data_1 === void 0 ? void 0 : _data_1.name) !== null && _ref !== void 0 ? _ref : \"\");\n        var _data__email_id;\n        setEmail((_data__email_id = (_data_2 = data[0]) === null || _data_2 === void 0 ? void 0 : _data_2.email_id) !== null && _data__email_id !== void 0 ? _data__email_id : \"\");\n        var _data__telephone;\n        setTelephone((_data__telephone = (_data_3 = data[0]) === null || _data_3 === void 0 ? void 0 : _data_3.telephone) !== null && _data__telephone !== void 0 ? _data__telephone : \"\");\n        var _data__address_line_1;\n        setAddressLine1((_data__address_line_1 = (_data_4 = data[0]) === null || _data_4 === void 0 ? void 0 : _data_4.address_line_1) !== null && _data__address_line_1 !== void 0 ? _data__address_line_1 : \"\");\n        var _data__address_line_2;\n        setAddressLine2((_data__address_line_2 = (_data_5 = data[0]) === null || _data_5 === void 0 ? void 0 : _data_5.address_line_2) !== null && _data__address_line_2 !== void 0 ? _data__address_line_2 : \"\");\n        var _data__address_line_3;\n        setAddressLine3((_data__address_line_3 = (_data_6 = data[0]) === null || _data_6 === void 0 ? void 0 : _data_6.address_line_3) !== null && _data__address_line_3 !== void 0 ? _data__address_line_3 : \"\");\n        var _data__address_line_4;\n        setAddressLine4((_data__address_line_4 = (_data_7 = data[0]) === null || _data_7 === void 0 ? void 0 : _data_7.address_line_4) !== null && _data__address_line_4 !== void 0 ? _data__address_line_4 : \"\");\n        var _data__postal_code;\n        setPostalCode((_data__postal_code = (_data_8 = data[0]) === null || _data_8 === void 0 ? void 0 : _data_8.postal_code) !== null && _data__postal_code !== void 0 ? _data__postal_code : \"\");\n        var _data__country_id;\n        setCountry((_data__country_id = (_data_9 = data[0]) === null || _data_9 === void 0 ? void 0 : _data_9.country_id) !== null && _data__country_id !== void 0 ? _data__country_id : \"\");\n        var _data__country_name;\n        setCountryName((_data__country_name = (_data_10 = data[0]) === null || _data_10 === void 0 ? void 0 : _data_10.country_name) !== null && _data__country_name !== void 0 ? _data__country_name : \"\");\n        var _data__status;\n        setStatus((_data__status = (_data_11 = data[0]) === null || _data_11 === void 0 ? void 0 : _data_11.status) !== null && _data__status !== void 0 ? _data__status : \"\");\n        setRedTractor((_data_12 = data[0]) === null || _data_12 === void 0 ? void 0 : _data_12.red_tractor);\n        setGGN((_data_13 = data[0]) === null || _data_13 === void 0 ? void 0 : _data_13.global_gap_number);\n        var _data__role_ids;\n        const role_parse = JSON.parse((_data__role_ids = data[0].role_ids) !== null && _data__role_ids !== void 0 ? _data__role_ids : \"[]\");\n        //const role_ids = role_parse.map()\n        const role_ids = role_parse === null || role_parse === void 0 ? void 0 : role_parse.map((roleId)=>roleId.role_id);\n        if ((role_ids === null || role_ids === void 0 ? void 0 : role_ids.includes(2)) || (role_ids === null || role_ids === void 0 ? void 0 : role_ids.includes(3)) || (role_ids === null || role_ids === void 0 ? void 0 : role_ids.includes(4))) {\n            setIsContactVisible(false);\n        } else {\n            setIsContactVisible(true);\n        }\n        //const role_ids = role_parse?.map((roleId) => roleId.role_id);\n        //console.log(role_ids)\n        setRole(role_ids);\n        const supplierAccountExist = role_ids === null || role_ids === void 0 ? void 0 : role_ids.includes(1);\n        if (supplierAccountExist) {\n            setIsSupplierAccount(\"true\");\n        } else {\n            setIsSupplierAccount(\"false\");\n        }\n        var _data__contacts_json;\n        const contacts_data_json = JSON.parse((_data__contacts_json = (_data_14 = data[0]) === null || _data_14 === void 0 ? void 0 : _data_14.contacts_json) !== null && _data__contacts_json !== void 0 ? _data__contacts_json : \"[]\");\n        const formattedContactsData = contacts_data_json === null || contacts_data_json === void 0 ? void 0 : contacts_data_json.map((row)=>({\n                id: row === null || row === void 0 ? void 0 : row.id,\n                supplierName: row === null || row === void 0 ? void 0 : row.name,\n                supplierEmail: row === null || row === void 0 ? void 0 : row.email_id,\n                supplierTelephone: row === null || row === void 0 ? void 0 : row.telephone,\n                TypeOfContact: row === null || row === void 0 ? void 0 : row.type_of_contact\n            }));\n        setcontacts_json(formattedContactsData);\n        //const existingProphetCode = checkExistingProphetCode(supplierId, user);\n        const supplierName = (_data_15 = data[0]) === null || _data_15 === void 0 ? void 0 : _data_15.name;\n        const prophetIds = (_data_16 = data[0]) === null || _data_16 === void 0 ? void 0 : _data_16.prophets_id_code;\n        setIsLoading(false);\n    }, []);\n    const validEmail = (email)=>{\n        const emailRegex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$/;\n        return emailRegex.test(email);\n    };\n    const savedata = (e)=>{\n        setFormChange(true);\n        e.preventDefault();\n        const newItem = {\n            supplierName: supplierName.trim(),\n            supplierEmail: supplierEmail.trim(),\n            supplierTelephone: supplierTelephone.trim(),\n            TypeOfContact: TypeOfContact.trim(),\n            id: contactId\n        };\n        let errorCount = 0;\n        if (supplierName == \"\") {\n            setIsSupplierNameValid(false);\n            errorCount++;\n        }\n        // if (supplierName.length <= 50) {\n        //   validateSupplierName(supplierName);\n        //   errorCount++;\n        // }\n        if (supplierEmail == \"\" || !validEmail(supplierEmail.trim())) {\n            setIsSupplierEmailValid(false);\n            errorCount++;\n        }\n        if (supplierTelephone == \"\") {\n            setIsSupplierTelephoneValid(false);\n            validateSupplierTelephone(supplierTelephone.trim());\n            errorCount++;\n        }\n        if (TypeOfContact == \"\") {\n            setIsTypeofContactValid(false);\n            errorCount++;\n        }\n        if (errorCount > 0) {\n            return;\n        }\n        setcontacts_json((prevContactsJson)=>{\n            if (!Array.isArray(prevContactsJson)) {\n                return [\n                    newItem\n                ];\n            }\n            return [\n                ...prevContactsJson,\n                newItem\n            ];\n        // setAtLeastOneContactEntered(true); // Update state to indicate at least one contact is entered\n        // return updatedContacts;\n        });\n        setSupplierEmail(\"\");\n        setSupplierName(\"\");\n        setSupplierTelephone(\"\");\n        setTypeOfContact(\"\");\n        setContactId(null);\n        setAtLeastOneContactEntered(true);\n    };\n    const IconsRenderer = (props)=>{\n        let updatedData;\n        const handleDelete = (e)=>{\n            setFormChange(true);\n            e.preventDefault();\n            const rowData = props.data;\n            updatedData = [\n                ...contacts_Json\n            ];\n            const index = updatedData === null || updatedData === void 0 ? void 0 : updatedData.indexOf(rowData);\n            updatedData.splice(index, 1);\n            props.api.applyTransaction({\n                remove: updatedData\n            });\n            setcontacts_json(updatedData);\n        };\n        const handleEdit = (e)=>{\n            setFormChange(true);\n            e.preventDefault();\n            const rowData = props === null || props === void 0 ? void 0 : props.data;\n            updatedData = [\n                ...contacts_Json\n            ];\n            const index = updatedData.indexOf(rowData);\n            updatedData.splice(index, 1);\n            props.api.applyTransaction({\n                remove: updatedData\n            });\n            setcontacts_json(updatedData);\n            setContactId(rowData === null || rowData === void 0 ? void 0 : rowData.id);\n            setSupplierName(rowData === null || rowData === void 0 ? void 0 : rowData.supplierName);\n            setSupplierEmail(rowData === null || rowData === void 0 ? void 0 : rowData.supplierEmail);\n            setSupplierTelephone(rowData === null || rowData === void 0 ? void 0 : rowData.supplierTelephone);\n            var _rowData_TypeOfContact;\n            setTypeOfContact((_rowData_TypeOfContact = rowData === null || rowData === void 0 ? void 0 : rowData.TypeOfContact) !== null && _rowData_TypeOfContact !== void 0 ? _rowData_TypeOfContact : \"\");\n            if (!isSupplierNameValid) {\n                setIsSupplierNameValid(true);\n            }\n            if (!isSupplierEmailValid) {\n                setIsSupplierEmailValid(true);\n            }\n            if (!isSupplierTelephoneValid) {\n                setIsSupplierTelephoneValid(true);\n            }\n            if (!isTypeofContactValid) {\n                setIsTypeofContactValid(true);\n            }\n        };\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex flex-row gap-4 justify-center text-skin-primary\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    onClick: handleEdit,\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fortawesome_react_fontawesome__WEBPACK_IMPORTED_MODULE_2__.FontAwesomeIcon, {\n                        icon: _fortawesome_free_solid_svg_icons__WEBPACK_IMPORTED_MODULE_23__.faPenToSquare\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\GeneralSection.js\",\n                        lineNumber: 440,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\GeneralSection.js\",\n                    lineNumber: 439,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    onClick: handleDelete,\n                    className: \"text-red-500\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fortawesome_react_fontawesome__WEBPACK_IMPORTED_MODULE_2__.FontAwesomeIcon, {\n                        icon: _fortawesome_free_solid_svg_icons__WEBPACK_IMPORTED_MODULE_23__.faTrash\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\GeneralSection.js\",\n                        lineNumber: 443,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\GeneralSection.js\",\n                    lineNumber: 442,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\GeneralSection.js\",\n            lineNumber: 438,\n            columnNumber: 7\n        }, undefined);\n    };\n    const defaultColDef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>({\n            //sortable: true,\n            filter: false,\n            resizable: true,\n            flex: 1,\n            suppressMenu: false\n        }));\n    // const CustomCellRenderer = (params) => {\n    //   const truncatedText =\n    //     params?.value && params?.value?.length > 12\n    //       ? params?.value?.substring(0, 12) + \"...\"\n    //       : params?.value;\n    //   return <span title={params?.value}>{truncatedText}</span>;\n    // };\n    const CustomTooltipComponent = (param)=>/*#__PURE__*/ {\n        let { value } = param;\n        return (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            title: value,\n            children: value\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\GeneralSection.js\",\n            lineNumber: 466,\n            columnNumber: 5\n        }, undefined);\n    };\n    const columnDefs = [\n        {\n            headerName: \"Name\",\n            field: \"supplierName\",\n            tooltipComponent: CustomTooltipComponent,\n            headerClass: \"header-with-border\",\n            cellStyle: {\n                display: \"flex\"\n            },\n            flex: \"2%\"\n        },\n        {\n            headerName: \"Email ID\",\n            field: \"supplierEmail\",\n            tooltipComponent: CustomTooltipComponent,\n            headerClass: \"header-with-border\",\n            cellStyle: {\n                display: \"flex\"\n            },\n            flex: \"2%\"\n        },\n        {\n            headerName: \"Telephone\",\n            field: \"supplierTelephone\",\n            tooltipComponent: CustomTooltipComponent,\n            headerClass: \"header-with-border\",\n            cellStyle: {\n                display: \"flex\"\n            },\n            flex: \"2%\"\n        },\n        {\n            headerName: \"Type of Contact\",\n            field: \"TypeOfContact\",\n            tooltipComponent: CustomTooltipComponent,\n            headerClass: \"header-with-border\",\n            cellStyle: {\n                display: \"flex\"\n            },\n            flex: \"2%\"\n        },\n        {\n            field: \"\",\n            cellRenderer: IconsRenderer,\n            headerClass: \"header-with-border\",\n            cellStyle: {\n                display: \"flex\",\n                justifyContent: \"end\"\n            },\n            flex: \"1%\"\n        }\n    ];\n    function handleTypeOfContactChange(e) {\n        setFormChange(true);\n        setTypeOfContact(e.target.value);\n        setIsTypeofContactValid(true);\n    }\n    const isFormValid = ()=>{\n        // Check validity of all required fields\n        const isTradingNameValid = (tradingName === null || tradingName === void 0 ? void 0 : tradingName.length) <= 50;\n        const isEmailValid = validateEmail(email.trim());\n        const isTelephoneValid = validateTelephone(telephone.trim());\n        const isPostalCodeValid = (postalCode === null || postalCode === void 0 ? void 0 : postalCode.length) <= 8;\n        const isSupplierNameValid = (supplierName === null || supplierName === void 0 ? void 0 : supplierName.length) <= 50;\n        const isSupplierEmailValid = validEmail(supplierEmail.trim());\n        const isSupplierTelephoneValid = validateSupplierTelephone(supplierTelephone.trim());\n        // Check if at least one contact is entered\n        const isAtLeastOneContactEntered = atLeastOneContactEntered;\n        // Set state to update UI based on validity\n        setIsTradingNameValid(isTradingNameValid);\n        setIsEmailValid(isEmailValid);\n        setIsTelephoneValid(isTelephoneValid);\n        setIsPostalCodeValid(isPostalCodeValid);\n        setIsSupplierNameValid(isSupplierNameValid);\n        setIsSupplierEmailValid(isSupplierEmailValid);\n        setIsSupplierTelephoneValid(isSupplierTelephoneValid);\n        // Return true only if all required fields are valid and at least one contact is entered\n        return isTradingNameValid && isEmailValid && isTelephoneValid && isPostalCodeValid && isSupplierNameValid && isSupplierEmailValid && isSupplierTelephoneValid && isAtLeastOneContactEntered;\n    };\n    const handleValidate = (step, isContinue)=>{\n        var _data_;\n        let errorCount = 0;\n        var _data__role_ids;\n        const roles = JSON.parse((_data__role_ids = (_data_ = data[0]) === null || _data_ === void 0 ? void 0 : _data_.role_ids) !== null && _data__role_ids !== void 0 ? _data__role_ids : \"[]\");\n        const role_ids = roles === null || roles === void 0 ? void 0 : roles.map((item)=>item === null || item === void 0 ? void 0 : item.role_id);\n        if (!country) {\n            //alert(country);\n            setCountryError(\"Please select country.\");\n            react_toastify__WEBPACK_IMPORTED_MODULE_12__.toast.error(\"Cannot proceed without selecting a Country. Kindly select a Country\");\n            return;\n        }\n        if (tradingName === \"\") {\n            setIsTradingNameValid(false);\n            setTradingNameError(\"Trading name is required.\");\n            errorCount++;\n        }\n        if (role_ids === null || role_ids === void 0 ? void 0 : role_ids.includes(1)) {\n            if (email === \"\" || !isEmailValid) {\n                setIsEmailValid(false);\n                errorCount++;\n            }\n            if (telephone === \"\" || !isTelephoneValid) {\n                setIsTelephoneValid(false);\n                setTelephoneError(\"Telephone is required.\");\n                errorCount++;\n            }\n        }\n        if (addressLine1 === \"\") {\n            setIsAddressLine1Valid(false);\n            errorCount++;\n            setAddressLine1Error(\"Please add an address. \");\n        }\n        if (addressLine2 === \"\") {\n            setIsAddressLine2Valid(false);\n            errorCount++;\n            setAddressLine2Error(\"Please add an address line 2. \");\n        }\n        if (postalCode === \"\" || !isPostalCodeValid) {\n            setIsPostalCodeValid(false);\n            setPostalCodeError(\"Postal code is required.\");\n            errorCount++;\n        }\n        if (role === null || role === void 0 ? void 0 : role.includes(1)) {\n            if (!contacts_Json || contacts_Json && contacts_Json.length < 1) {\n                errorCount++;\n                react_toastify__WEBPACK_IMPORTED_MODULE_12__.toast.error(\"Please add atleast one contact.\");\n            //return;\n            }\n        }\n        if (errorCount > 0) {\n            setNavType(step);\n            setIsOpen(true);\n        } else {\n            handleSubmit(step, \"Complete\");\n        }\n        if (isCancelled) {\n            setIsCancelled(false);\n            return;\n        }\n        if (isContinue) {\n            handleSubmit(step);\n            setIsOpen(false);\n        }\n    };\n    const handleSubmit = function(step) {\n        let technical = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : \"Incomplete\";\n        //const isFormValidResult = isFormValid();\n        setLoading(true);\n        let serverAddress = _services_apiConfig__WEBPACK_IMPORTED_MODULE_10__.apiConfig.serverAddress;\n        if (formChange) {\n            var _data_, _data_1;\n            let currentStatus;\n            if (status == 3 || status == 4 || status == 1) {\n                currentStatus = 4;\n            } else if (status == 2) {\n                currentStatus = 2;\n            } else {\n                currentStatus = 3;\n            }\n            localStorage.setItem(\"isFormNew\", false);\n            fetch(\"\".concat(serverAddress, \"suppliers/update-supplier/\").concat(supplierId), {\n                method: \"PUT\",\n                headers: {\n                    Accept: \"application/json\",\n                    \"Content-Type\": \"application/json\",\n                    Authorization: \"Bearer \".concat(user === null || user === void 0 ? void 0 : user.token)\n                },\n                body: JSON.stringify({\n                    sectionName: \"generalSection\",\n                    trading_name: tradingName === null || tradingName === void 0 ? void 0 : tradingName.trim(),\n                    telephone: isTelephoneValid ? telephone === null || telephone === void 0 ? void 0 : telephone.trim() : \"\",\n                    email_id: isEmailValid ? email === null || email === void 0 ? void 0 : email.trim() : \"\",\n                    address_line_1: isAddressLine1Valid ? addressLine1 === null || addressLine1 === void 0 ? void 0 : addressLine1.trim() : \"\",\n                    address_line_2: isAddressLine2Valid ? addressLine2 === null || addressLine2 === void 0 ? void 0 : addressLine2.trim() : \"\",\n                    address_line_3: addressLine3 === null || addressLine3 === void 0 ? void 0 : addressLine3.trim(),\n                    address_line_4: addressLine4 === null || addressLine4 === void 0 ? void 0 : addressLine4.trim(),\n                    postal_code: isPostalCodeValid ? postalCode === null || postalCode === void 0 ? void 0 : postalCode.trim() : \"\",\n                    contacts_json: contacts_Json,\n                    country: country,\n                    country_name: countryName ? countryName : null,\n                    technical: technical,\n                    updated_date: new Date().toISOString(),\n                    userId: user === null || user === void 0 ? void 0 : user.user_id,\n                    status: currentStatus,\n                    compliance: data[0].compliance,\n                    procurement: data[0].procurement,\n                    financial: data[0].financial,\n                    allowedSections: allowedSections,\n                    roleIds: role,\n                    prophet_id: prophetsIds,\n                    requestor_email: (_data_ = data[0]) === null || _data_ === void 0 ? void 0 : _data_.requestor_email,\n                    requestor_name: (_data_1 = data[0]) === null || _data_1 === void 0 ? void 0 : _data_1.requestor_name\n                })\n            }).then((res)=>{\n                if (res.status === 400) {\n                    react_toastify__WEBPACK_IMPORTED_MODULE_12__.toast.error(\"There was an error with your request. Please check your data and try again.\");\n                } else if (res.status === 401) {\n                    react_toastify__WEBPACK_IMPORTED_MODULE_12__.toast.error(\"Your session has expired. Please log in again.\");\n                    setTimeout(()=>{\n                        localStorage.removeItem(\"superUser\");\n                        js_cookie__WEBPACK_IMPORTED_MODULE_22__[\"default\"].remove(\"company\");\n                        js_cookie__WEBPACK_IMPORTED_MODULE_22__[\"default\"].remove(\"ADCompanyName\");\n                        localStorage.removeItem(\"id\");\n                        localStorage.removeItem(\"name\");\n                        localStorage.removeItem(\"role\");\n                        localStorage.removeItem(\"email\");\n                        js_cookie__WEBPACK_IMPORTED_MODULE_22__[\"default\"].remove(\"user\");\n                        js_cookie__WEBPACK_IMPORTED_MODULE_22__[\"default\"].remove(\"theme\");\n                        js_cookie__WEBPACK_IMPORTED_MODULE_22__[\"default\"].remove(\"token\");\n                        const redirectUrl = \"/login?redirect=\".concat(encodeURIComponent(window.location.pathname));\n                        (0,_utils_auth_auth__WEBPACK_IMPORTED_MODULE_18__.logoutHandler)(instance, redirectUrl);\n                    }, 3000);\n                }\n                if (res.status === 200) {\n                    if (step == \"sap\") {\n                        if (isEdit) {\n                            var _data_;\n                            if (isSupplierAccount == \"false\" && countryChange && (((_data_ = data[0]) === null || _data_ === void 0 ? void 0 : _data_.prophets_id_code) || Object.keys(prophetData).length !== 0) && prophetObj.prophet_code != null) {\n                                const result = (0,_utils_ajaxHandler__WEBPACK_IMPORTED_MODULE_17__.addProphetAjaxCall)(prophetObj, user);\n                                result.then((data)=>{\n                                    if (data === null || data === void 0 ? void 0 : data.data) {\n                                        localStorage.removeItem(\"isEdit\");\n                                        router.back();\n                                    }\n                                });\n                            } else {\n                                localStorage.removeItem(\"isEdit\");\n                                router.back();\n                            }\n                        } else {\n                            var _data_1;\n                            if (isSupplierAccount == \"false\" && countryChange && (((_data_1 = data[0]) === null || _data_1 === void 0 ? void 0 : _data_1.prophets_id_code.length) > 0 || Object.keys(prophetData).length !== 0) && prophetObj[0].prophet_code != null) {\n                                const result = (0,_utils_ajaxHandler__WEBPACK_IMPORTED_MODULE_17__.addProphetAjaxCall)(prophetObj, user);\n                                result.then((data)=>{\n                                    if (data === null || data === void 0 ? void 0 : data.data) {\n                                        onSubmit();\n                                    }\n                                });\n                            } else {\n                                onSubmit();\n                            }\n                        }\n                    } else {\n                        router.push({\n                            pathname: \"/suppliers\"\n                        });\n                    }\n                // setLoading(false);\n                }\n                return Promise.reject(res);\n            }).catch((err)=>{\n                setLoading(false);\n                // toast.error(\n                //   `Error saving data in general forms file: ${err.statusText}`,\n                //   {\n                //     position: \"top-right\",\n                //   }\n                // );\n                return err;\n            });\n        } else {\n            if (step == \"sap\") {\n                if (isEdit) {\n                    localStorage.removeItem(\"isEdit\");\n                    router.back();\n                } else {\n                    onSubmit();\n                }\n            } else {\n                router.push({\n                    pathname: \"/suppliers\"\n                });\n            }\n        }\n    };\n    const handleSaveAndExit = ()=>{\n        if (!isFormValid()) {\n            return;\n        }\n        router.push(\"/confirmPage\");\n    };\n    const [prophetData, setProphetData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const handleCountryChange = (e)=>{\n        var _data_;\n        setFormChange(true);\n        setCountryChange(true);\n        if (country) {\n            setCountryError(\"\");\n        }\n        const prophetIds = (_data_ = data[0]) === null || _data_ === void 0 ? void 0 : _data_.prophets_id_code;\n        if (prophetIds && e.target.value && isSupplierAccount === \"false\") {\n            var _prophet_;\n            const selectedCountryName = e.target.options[e.target.selectedIndex].text;\n            const countryCode = selectedCountryName === \"United Kingdom\" ? \"UK\" : \"\";\n            const identifier = countryCode === \"UK\" ? redTractor : ggn;\n            const generateProphetCode = identifier && (identifier === null || identifier === void 0 ? void 0 : identifier.slice(-6).padStart(6, \"X\"));\n            const prophet = JSON.parse(prophetIds !== null && prophetIds !== void 0 ? prophetIds : \"[]\");\n            setProphetData({\n                prophet_id: (_prophet_ = prophet[0]) === null || _prophet_ === void 0 ? void 0 : _prophet_.prophet_id,\n                prophet_code: generateProphetCode && (generateProphetCode === null || generateProphetCode === void 0 ? void 0 : generateProphetCode.toString().trim().toUpperCase()),\n                supplier_id: parseInt(supplierId)\n            });\n            setProphetObj([\n                prophetData\n            ]);\n        }\n    };\n    const closeModal = (e)=>{\n        if (e) {\n            e.preventDefault();\n        }\n        setIsCancelled(true);\n        setIsOpen(false);\n    };\n    const handleContinueSubmit = ()=>{\n        handleValidate(navType, isContinue);\n        setIsOpen(false);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_toastify__WEBPACK_IMPORTED_MODULE_12__.ToastContainer, {\n                limit: 1\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\GeneralSection.js\",\n                lineNumber: 821,\n                columnNumber: 7\n            }, undefined),\n            loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    display: \"flex\",\n                    alignItems: \"center\",\n                    justifyContent: \"center\",\n                    height: \"calc(100vh - 100px)\",\n                    width: \"calc(100vw - 125px)\"\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_loader_spinner__WEBPACK_IMPORTED_MODULE_11__.ThreeCircles, {\n                    color: \"#002D73\",\n                    height: 50,\n                    width: 50,\n                    visible: true,\n                    ariaLabel: \"oval-loading\",\n                    secondaryColor: \"#0066FF\",\n                    strokeWidth: 2,\n                    strokeWidthSecondary: 2\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\GeneralSection.js\",\n                    lineNumber: 832,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\GeneralSection.js\",\n                lineNumber: 823,\n                columnNumber: 9\n            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative panel-container bg-white rounded-lg w-[93%] lg:w-[95%] 2xl:w-[calc(100%-70px)] p-4 pb-0\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex md:flex-row flex-col my-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"px-5 pe-8 mb-0 h-100vh border-e-[1px] border-light-gray \".concat((role === null || role === void 0 ? void 0 : role.includes(1)) ? \"md:w-1/2\" : \"md:w-full\"),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mb-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mb-3\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                    className: \"formtitle pb-1 border-b border-light-gray3\",\n                                                    children: \"Supplier Details\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\GeneralSection.js\",\n                                                    lineNumber: 853,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\GeneralSection.js\",\n                                                lineNumber: 852,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid \".concat((role === null || role === void 0 ? void 0 : role.includes(1)) ? \"lg:grid-cols-2\" : \"lg:grid-cols-3\", \" gap-4  \").concat((role === null || role === void 0 ? void 0 : role.includes(1)) ? \"grid-cols-1\" : \"grid-cols-3\"),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex flex-col\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"labels mb-1\",\n                                                                children: [\n                                                                    \"Trading Name \",\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-red-500\",\n                                                                        children: \"*\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\GeneralSection.js\",\n                                                                        lineNumber: 867,\n                                                                        columnNumber: 36\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\GeneralSection.js\",\n                                                                lineNumber: 866,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                type: \"text\",\n                                                                name: \"Trading_name\",\n                                                                maxLength: 50,\n                                                                value: tradingName,\n                                                                onChange: handleTradingNameChange,\n                                                                className: \"w-full py-1 px-2 2xl:px-3 2xl:py-3 border \".concat(isTradingNameValid ? \"border-light-gray\" : \"!border-red-500\", \" rounded-md\"),\n                                                                required: true,\n                                                                style: {\n                                                                    textTransform: \"capitalize\"\n                                                                },\n                                                                tabIndex: 1\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\GeneralSection.js\",\n                                                                lineNumber: 869,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            !isTradingNameValid && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-red-500 text-sm mt-1\",\n                                                                children: \"Please enter a valid name of max 50 chars.\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\GeneralSection.js\",\n                                                                lineNumber: 885,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\GeneralSection.js\",\n                                                        lineNumber: 865,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex flex-col\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"labels mb-1\",\n                                                                children: [\n                                                                    \"Email ID\",\n                                                                    (role === null || role === void 0 ? void 0 : role.includes(1)) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-red-500\",\n                                                                        children: \"*\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\GeneralSection.js\",\n                                                                        lineNumber: 895,\n                                                                        columnNumber: 25\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\GeneralSection.js\",\n                                                                lineNumber: 892,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                type: \"text\",\n                                                                name: \"email_id\",\n                                                                maxLength: 80,\n                                                                value: email,\n                                                                onChange: handleEmailChange,\n                                                                className: \"w-full py-1 px-2 2xl:px-3 2xl:py-3 border \".concat(isEmailValid ? \"border-light-gray\" : \"border-red-500\", \" rounded-md\"),\n                                                                required: true,\n                                                                tabIndex: 2\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\GeneralSection.js\",\n                                                                lineNumber: 898,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            !isEmailValid && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-red-500 text-sm mt-1\",\n                                                                children: \"Please enter a valid email address.\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\GeneralSection.js\",\n                                                                lineNumber: 911,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\GeneralSection.js\",\n                                                        lineNumber: 891,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex flex-col\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"labels mb-1\",\n                                                                children: [\n                                                                    \"Telephone\",\n                                                                    \" \",\n                                                                    (role === null || role === void 0 ? void 0 : role.includes(1)) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-red-500\",\n                                                                        children: \"*\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\GeneralSection.js\",\n                                                                        lineNumber: 921,\n                                                                        columnNumber: 25\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\GeneralSection.js\",\n                                                                lineNumber: 918,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                type: \"text\",\n                                                                name: \"telephone\",\n                                                                maxLength: 15,\n                                                                value: telephone,\n                                                                onChange: handleTelephoneChange,\n                                                                className: \"w-full py-1 px-2 2xl:px-3 2xl:py-3 border \".concat(isTelephoneValid ? \"border-light-gray\" : \"border-red-500\", \" rounded-md\"),\n                                                                required: true,\n                                                                tabIndex: 3\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\GeneralSection.js\",\n                                                                lineNumber: 924,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            !isTelephoneValid && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-red-500 text-sm mt-1\",\n                                                                children: telephoneError\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\GeneralSection.js\",\n                                                                lineNumber: 939,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\GeneralSection.js\",\n                                                        lineNumber: 917,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\GeneralSection.js\",\n                                                lineNumber: 858,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\GeneralSection.js\",\n                                        lineNumber: 851,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mb-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mb-3\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                    className: \"formtitle pb-2 border-b border-light-gray3\",\n                                                    children: \"Address\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\GeneralSection.js\",\n                                                    lineNumber: 949,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\GeneralSection.js\",\n                                                lineNumber: 948,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid \".concat((role === null || role === void 0 ? void 0 : role.includes(1)) ? \"lg:grid-cols-2\" : \"lg:grid-cols-3\", \" grid-cols-1 gap-4\"),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex flex-col\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"labels mb-1\",\n                                                                children: [\n                                                                    \"Address Line 1 \",\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-red-500\",\n                                                                        children: \"*\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\GeneralSection.js\",\n                                                                        lineNumber: 961,\n                                                                        columnNumber: 38\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\GeneralSection.js\",\n                                                                lineNumber: 960,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                type: \"text\",\n                                                                name: \"address_line_1\",\n                                                                maxLength: 50,\n                                                                value: addressLine1,\n                                                                onChange: handleAddressLine1Change,\n                                                                className: \"w-full py-1 px-2 2xl:px-3 2xl:py-3 border \".concat(isAddressLine1Valid ? \"border-light-gray\" : \"border-red-500\", \" rounded-md\"),\n                                                                required: true,\n                                                                tabIndex: 4,\n                                                                style: {\n                                                                    textTransform: \"capitalize\"\n                                                                }\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\GeneralSection.js\",\n                                                                lineNumber: 963,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            !isAddressLine1Valid && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-red-500 text-sm mt-1\",\n                                                                children: addressLine1Error\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\GeneralSection.js\",\n                                                                lineNumber: 979,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\GeneralSection.js\",\n                                                        lineNumber: 959,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex flex-col\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"labels mb-1\",\n                                                                children: [\n                                                                    \"Address Line 2 \",\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-red-500\",\n                                                                        children: \"*\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\GeneralSection.js\",\n                                                                        lineNumber: 987,\n                                                                        columnNumber: 38\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\GeneralSection.js\",\n                                                                lineNumber: 986,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                type: \"text\",\n                                                                name: \"address_line_2\",\n                                                                maxLength: 50,\n                                                                value: addressLine2,\n                                                                onChange: handleAddressLine2Change,\n                                                                className: \"w-full py-1 px-2 2xl:px-3 2xl:py-3 border border-light-gray rounded-md \".concat(isAddressLine2Valid ? \"border-light-gray\" : \"border-red-500\"),\n                                                                required: true,\n                                                                tabIndex: 5\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\GeneralSection.js\",\n                                                                lineNumber: 989,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            !isAddressLine2Valid && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-red-500 text-sm mt-1\",\n                                                                children: addressLine2Error\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\GeneralSection.js\",\n                                                                lineNumber: 1004,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\GeneralSection.js\",\n                                                        lineNumber: 985,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex flex-col\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"labels mb-1\",\n                                                                children: \"Address Line 3\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\GeneralSection.js\",\n                                                                lineNumber: 1011,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                type: \"text\",\n                                                                name: \"address_line_3\",\n                                                                maxLength: 50,\n                                                                value: addressLine3,\n                                                                onChange: handleAddressLine3Change,\n                                                                className: \"w-full py-1 px-2 2xl:px-3 2xl:py-3 border border-light-gray rounded-md\",\n                                                                tabIndex: 6\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\GeneralSection.js\",\n                                                                lineNumber: 1012,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\GeneralSection.js\",\n                                                        lineNumber: 1010,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex flex-col\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"labels mb-1\",\n                                                                children: \"Address Line 4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\GeneralSection.js\",\n                                                                lineNumber: 1024,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                type: \"text\",\n                                                                name: \"address_line_4\",\n                                                                maxLength: 50,\n                                                                value: addressLine4,\n                                                                onChange: handleAddressLine4Change,\n                                                                className: \"w-full py-1 px-2 2xl:px-3 2xl:py-3 border border-light-gray rounded-md\",\n                                                                tabIndex: 7\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\GeneralSection.js\",\n                                                                lineNumber: 1025,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\GeneralSection.js\",\n                                                        lineNumber: 1023,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex flex-col\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"labels mb-1\",\n                                                                required: true,\n                                                                children: [\n                                                                    \"Country \",\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-red-500\",\n                                                                        children: \"*\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\GeneralSection.js\",\n                                                                        lineNumber: 1038,\n                                                                        columnNumber: 31\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\GeneralSection.js\",\n                                                                lineNumber: 1037,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                                tabIndex: 8,\n                                                                type: \"text\",\n                                                                name: \"country\",\n                                                                value: country,\n                                                                onChange: (e)=>{\n                                                                    setFormChange(true);\n                                                                    const selectedCountryId = e.target.value;\n                                                                    const selectedCountryName = e.target.options[e.target.selectedIndex].text;\n                                                                    setCountry(selectedCountryId);\n                                                                    setCountryName(selectedCountryName);\n                                                                    handleCountryChange(e);\n                                                                },\n                                                                className: \"w-full px-2 2xl:px-3 border border-light-gray rounded-md\",\n                                                                required: true,\n                                                                onBlur: handleCountryChange,\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                        value: \"\",\n                                                                        disabled: true,\n                                                                        children: \"Select...\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\GeneralSection.js\",\n                                                                        lineNumber: 1059,\n                                                                        columnNumber: 23\n                                                                    }, undefined),\n                                                                    (dropdowns === null || dropdowns === void 0 ? void 0 : dropdowns.countries) && (dropdowns === null || dropdowns === void 0 ? void 0 : (_dropdowns_countries = dropdowns.countries) === null || _dropdowns_countries === void 0 ? void 0 : _dropdowns_countries.map((con, key)=>{\n                                                                        var _con_name;\n                                                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                            value: con.id,\n                                                                            defaultValue: (con === null || con === void 0 ? void 0 : (_con_name = con.name) === null || _con_name === void 0 ? void 0 : _con_name.trim()) == (countryName === null || countryName === void 0 ? void 0 : countryName.trim()) ? true : false,\n                                                                            children: con === null || con === void 0 ? void 0 : con.name\n                                                                        }, key, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\GeneralSection.js\",\n                                                                            lineNumber: 1065,\n                                                                            columnNumber: 29\n                                                                        }, undefined);\n                                                                    }))\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\GeneralSection.js\",\n                                                                lineNumber: 1041,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            countryError && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-red-500 text-sm mt-1\",\n                                                                children: countryError\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\GeneralSection.js\",\n                                                                lineNumber: 1080,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\GeneralSection.js\",\n                                                        lineNumber: 1036,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex flex-col\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"labels mb-1\",\n                                                                children: [\n                                                                    \"Postal Code/Zip Code\",\n                                                                    \" \",\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-red-500\",\n                                                                        children: \"*\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\GeneralSection.js\",\n                                                                        lineNumber: 1089,\n                                                                        columnNumber: 23\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\GeneralSection.js\",\n                                                                lineNumber: 1087,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                type: \"text\",\n                                                                name: \"postal_code\",\n                                                                maxLength: 10,\n                                                                value: postalCode,\n                                                                onChange: handlePostalCodeChange,\n                                                                className: \"w-full py-1 px-2 2xl:px-3 2xl:py-3 border \".concat(isPostalCodeValid ? \"border-light-gray\" : \"border-red-500\", \" rounded-md\"),\n                                                                required: true,\n                                                                tabIndex: 9\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\GeneralSection.js\",\n                                                                lineNumber: 1091,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            !isPostalCodeValid && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-red-500 text-sm mt-1\",\n                                                                children: postalCodeError\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\GeneralSection.js\",\n                                                                lineNumber: 1106,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\GeneralSection.js\",\n                                                        lineNumber: 1086,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\GeneralSection.js\",\n                                                lineNumber: 954,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\GeneralSection.js\",\n                                        lineNumber: 947,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\GeneralSection.js\",\n                                lineNumber: 846,\n                                columnNumber: 13\n                            }, undefined),\n                            (role === null || role === void 0 ? void 0 : role.includes(1)) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-x5 ps-8 mb-0 w-1/2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mb-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mb-3\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                    className: \"formtitle pb-1 border-b border-light-gray3\",\n                                                    children: \"Contact\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\GeneralSection.js\",\n                                                    lineNumber: 1118,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\GeneralSection.js\",\n                                                lineNumber: 1117,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex flex-row\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"grid lg:grid-cols-2 grid-cols-1 gap-4 mb-6 w-full\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex flex-col\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        className: \"labels mb-1\",\n                                                                        children: [\n                                                                            \"Name \",\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-red-500\",\n                                                                                children: \"*\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\GeneralSection.js\",\n                                                                                lineNumber: 1131,\n                                                                                columnNumber: 32\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\GeneralSection.js\",\n                                                                        lineNumber: 1130,\n                                                                        columnNumber: 25\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                        type: \"text\",\n                                                                        name: \"supplier_name\",\n                                                                        value: supplierName,\n                                                                        onChange: handleSupplierNameChange,\n                                                                        className: \"w-full py-1 px-2 2xl:px-3 2xl:py-3 border \".concat(isSupplierNameValid ? \"border-light-gray\" : \"!border-red-500\", \" rounded-md\"),\n                                                                        maxLength: 50,\n                                                                        required: true,\n                                                                        style: {\n                                                                            textTransform: \"capitalize\"\n                                                                        }\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\GeneralSection.js\",\n                                                                        lineNumber: 1133,\n                                                                        columnNumber: 25\n                                                                    }, undefined),\n                                                                    !isSupplierNameValid && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-red-500 text-sm mt-1\",\n                                                                        children: \"Please enter a name with max 50 chars.\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\GeneralSection.js\",\n                                                                        lineNumber: 1148,\n                                                                        columnNumber: 27\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\GeneralSection.js\",\n                                                                lineNumber: 1129,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex flex-col\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        className: \"labels mb-1\",\n                                                                        children: [\n                                                                            \"Email ID \",\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-red-500\",\n                                                                                children: \"*\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\GeneralSection.js\",\n                                                                                lineNumber: 1156,\n                                                                                columnNumber: 36\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\GeneralSection.js\",\n                                                                        lineNumber: 1155,\n                                                                        columnNumber: 25\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                        type: \"text\",\n                                                                        name: \"supplier_email_id\",\n                                                                        value: supplierEmail,\n                                                                        onChange: handleSupplierEmailChange,\n                                                                        className: \"w-full py-1 px-2 2xl:px-3 2xl:py-3 border \".concat(isSupplierEmailValid ? \"border-light-gray\" : \"!border-red-500\", \" rounded-md\"),\n                                                                        maxLength: 50,\n                                                                        required: true\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\GeneralSection.js\",\n                                                                        lineNumber: 1158,\n                                                                        columnNumber: 25\n                                                                    }, undefined),\n                                                                    !isSupplierEmailValid && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-red-500 text-sm mt-1\",\n                                                                        children: \"Please enter a valid email address.\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\GeneralSection.js\",\n                                                                        lineNumber: 1172,\n                                                                        columnNumber: 27\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\GeneralSection.js\",\n                                                                lineNumber: 1154,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex flex-col\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        className: \"labels mb-1\",\n                                                                        children: [\n                                                                            \"Telephone \",\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-red-500\",\n                                                                                children: \"*\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\GeneralSection.js\",\n                                                                                lineNumber: 1180,\n                                                                                columnNumber: 37\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\GeneralSection.js\",\n                                                                        lineNumber: 1179,\n                                                                        columnNumber: 25\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"relative\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                type: \"text\",\n                                                                                name: \"supplier_telephone\",\n                                                                                value: supplierTelephone,\n                                                                                onChange: handleSupplierTelephoneChange,\n                                                                                className: \"w-full py-1 px-2 2xl:px-3 2xl:py-3 border \".concat(isSupplierTelephoneValid ? \"border-light-gray\" : \"!border-red-500\", \" rounded-md\"),\n                                                                                maxLength: 15,\n                                                                                required: true\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\GeneralSection.js\",\n                                                                                lineNumber: 1183,\n                                                                                columnNumber: 27\n                                                                            }, undefined),\n                                                                            !isSupplierTelephoneValid && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                className: \"text-red-500 text-sm mt-1\",\n                                                                                children: \"Please enter a valid Telephone number.\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\GeneralSection.js\",\n                                                                                lineNumber: 1197,\n                                                                                columnNumber: 29\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\GeneralSection.js\",\n                                                                        lineNumber: 1182,\n                                                                        columnNumber: 25\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\GeneralSection.js\",\n                                                                lineNumber: 1178,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex flex-col\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        className: \"labels mb-1\",\n                                                                        children: [\n                                                                            \"Type of Contact\",\n                                                                            \" \",\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-red-500\",\n                                                                                children: \"*\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\GeneralSection.js\",\n                                                                                lineNumber: 1207,\n                                                                                columnNumber: 27\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\GeneralSection.js\",\n                                                                        lineNumber: 1205,\n                                                                        columnNumber: 25\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"relative\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                                                className: \"border \".concat(isTypeofContactValid ? \"border-light-gray\" : \"border-bright-red\", \" rounded-md px-2 2xl:px-3\"),\n                                                                                name: \"sendac group\",\n                                                                                onChange: handleTypeOfContactChange,\n                                                                                value: TypeOfContact,\n                                                                                style: {\n                                                                                    width: \"100%\"\n                                                                                },\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                        value: \"\",\n                                                                                        disabled: true,\n                                                                                        children: \"Select...\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\GeneralSection.js\",\n                                                                                        lineNumber: 1221,\n                                                                                        columnNumber: 29\n                                                                                    }, undefined),\n                                                                                    (dropdowns === null || dropdowns === void 0 ? void 0 : dropdowns.type_of_contacts) && (dropdowns === null || dropdowns === void 0 ? void 0 : dropdowns.type_of_contacts.map((contact, key)=>{\n                                                                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                            value: contact.name,\n                                                                                            children: contact.name\n                                                                                        }, key, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\GeneralSection.js\",\n                                                                                            lineNumber: 1228,\n                                                                                            columnNumber: 37\n                                                                                        }, undefined);\n                                                                                    }))\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\GeneralSection.js\",\n                                                                                lineNumber: 1210,\n                                                                                columnNumber: 27\n                                                                            }, undefined),\n                                                                            !isTypeofContactValid ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                className: \"text-red-500 text-sm mt-1 absolute bottom-[-20px]\",\n                                                                                children: \"Please select a type of contact.\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\GeneralSection.js\",\n                                                                                lineNumber: 1246,\n                                                                                columnNumber: 29\n                                                                            }, undefined) : \"\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\GeneralSection.js\",\n                                                                        lineNumber: 1209,\n                                                                        columnNumber: 25\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\GeneralSection.js\",\n                                                                lineNumber: 1204,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\GeneralSection.js\",\n                                                        lineNumber: 1128,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex w-[10%] items-end justify-center mb-6 \",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            onClick: savedata,\n                                                            tabIndex: 0,\n                                                            className: \"px-2 py-1 2xl:px-3.5 2xl:py-1 border border-skin-primary  text-skin-primary  rounded-md ml-8  cursor-pointer \".concat(!isTypeofContactValid && !isSupplierTelephoneValid ? \"mb-6\" : !isSupplierTelephoneValid ? \"mb-6\" : \"mb-0\"),\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fortawesome_react_fontawesome__WEBPACK_IMPORTED_MODULE_2__.FontAwesomeIcon, {\n                                                                icon: _fortawesome_free_solid_svg_icons__WEBPACK_IMPORTED_MODULE_23__.faFloppyDisk\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\GeneralSection.js\",\n                                                                lineNumber: 1267,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\GeneralSection.js\",\n                                                            lineNumber: 1256,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\GeneralSection.js\",\n                                                        lineNumber: 1255,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\GeneralSection.js\",\n                                                lineNumber: 1123,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\GeneralSection.js\",\n                                        lineNumber: 1116,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            height: 200,\n                                            width: \"100%\"\n                                        },\n                                        className: \"general_section\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ag_grid_react__WEBPACK_IMPORTED_MODULE_3__.AgGridReact, {\n                                                defaultColDef: defaultColDef,\n                                                columnDefs: columnDefs,\n                                                rowData: contacts_Json,\n                                                rowHeight: 25,\n                                                ref: gridRef\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\GeneralSection.js\",\n                                                lineNumber: 1276,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"style\", {\n                                                children: \"\\n          .ag-header .ag-header-cell.header-with-border {\\n            border-bottom: 1px solid #ccc; /* Style for header row */\\n          }\\n        \"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\GeneralSection.js\",\n                                                lineNumber: 1283,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\GeneralSection.js\",\n                                        lineNumber: 1272,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\GeneralSection.js\",\n                                lineNumber: 1115,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\GeneralSection.js\",\n                        lineNumber: 845,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-between border-t border-light-gray py-5 bg-white\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    className: \"border border-skin-primary text-skin-primary button px-8 rounded-md\",\n                                    onClick: ()=>router.push({\n                                            pathname: \"/supplier/\".concat(supplierId, \"/edit\")\n                                        }),\n                                    children: \"Previous\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\GeneralSection.js\",\n                                    lineNumber: 1303,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\GeneralSection.js\",\n                                lineNumber: 1302,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        className: \"border border-skin-primary text-skin-primary me-10 py-1 px-8 font-medium rounded-md\",\n                                        onClick: ()=>handleValidate(\"sae\"),\n                                        children: \"Save & Exit\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\GeneralSection.js\",\n                                        lineNumber: 1315,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        className: \"border  border-skin-primary text-white bg-skin-primary rounded-md py-1 px-8 font-medium\",\n                                        onClick: ()=>handleValidate(\"sap\"),\n                                        children: \"Save & Proceed\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\GeneralSection.js\",\n                                        lineNumber: 1321,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\GeneralSection.js\",\n                                lineNumber: 1314,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\GeneralSection.js\",\n                        lineNumber: 1301,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\GeneralSection.js\",\n                lineNumber: 844,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Dialog_Transition_headlessui_react__WEBPACK_IMPORTED_MODULE_24__.Transition, {\n                appear: true,\n                show: isOpen,\n                as: react__WEBPACK_IMPORTED_MODULE_1__.Fragment,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Dialog_Transition_headlessui_react__WEBPACK_IMPORTED_MODULE_24__.Dialog, {\n                    as: \"div\",\n                    className: \"relative z-10\",\n                    onClose: closeModal,\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Dialog_Transition_headlessui_react__WEBPACK_IMPORTED_MODULE_24__.Transition.Child, {\n                            as: react__WEBPACK_IMPORTED_MODULE_1__.Fragment,\n                            enter: \"ease-out duration-300\",\n                            enterFrom: \"opacity-0\",\n                            enterTo: \"opacity-100\",\n                            leave: \"ease-in duration-200\",\n                            leaveFrom: \"opacity-100\",\n                            leaveTo: \"opacity-0\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"fixed inset-0 bg-black bg-opacity-25 backdrop-blur-sm\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\GeneralSection.js\",\n                                lineNumber: 1342,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\GeneralSection.js\",\n                            lineNumber: 1333,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"fixed inset-0 overflow-y-auto\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-center min-h-full p-4 text-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Dialog_Transition_headlessui_react__WEBPACK_IMPORTED_MODULE_24__.Transition.Child, {\n                                    as: react__WEBPACK_IMPORTED_MODULE_1__.Fragment,\n                                    enter: \"ease-out duration-300\",\n                                    enterFrom: \"opacity-0 scale-95\",\n                                    enterTo: \"opacity-100 scale-100\",\n                                    leave: \"ease-in duration-200\",\n                                    leaveFrom: \"opacity-100 scale-100\",\n                                    leaveTo: \"opacity-0 scale-95\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Dialog_Transition_headlessui_react__WEBPACK_IMPORTED_MODULE_24__.Dialog.Panel, {\n                                        className: \" w-[45%] transform overflow-hidden rounded-xl bg-white  text-left align-middle shadow-xl transition-all\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative bg-white rounded-lg shadow\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-start justify-between p-8 rounded-t\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"flex flex-row text-xl font-semibold text-gray-900  items-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"flex justify-center items-center border border-skin-primary text-skin-primary w-[25px] h-[25px] text-center leading-5 rounded-full text-base me-4\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fortawesome_react_fontawesome__WEBPACK_IMPORTED_MODULE_2__.FontAwesomeIcon, {\n                                                                            icon: _fortawesome_free_solid_svg_icons__WEBPACK_IMPORTED_MODULE_23__.faInfo\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\GeneralSection.js\",\n                                                                            lineNumber: 1363,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        \" \"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\GeneralSection.js\",\n                                                                    lineNumber: 1362,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                \" \",\n                                                                \"Warning\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\GeneralSection.js\",\n                                                            lineNumber: 1361,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: closeModal,\n                                                            type: \"button\",\n                                                            className: \"text-black bg-transparent hover:bg-gray-200 hover:text-gray-900 rounded-lg text-xl w-8 h-8 ml-auto inline-flex justify-center items-center \",\n                                                            \"data-modal-hide\": \"default-modal\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fortawesome_react_fontawesome__WEBPACK_IMPORTED_MODULE_2__.FontAwesomeIcon, {\n                                                                    icon: _fortawesome_free_solid_svg_icons__WEBPACK_IMPORTED_MODULE_23__.faXmark,\n                                                                    className: \"text-skin-primary\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\GeneralSection.js\",\n                                                                    lineNumber: 1373,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                \" \"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\GeneralSection.js\",\n                                                            lineNumber: 1367,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\GeneralSection.js\",\n                                                    lineNumber: 1360,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"p-8 py-0 space-y-6\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-base xl:text-md 2xl:text-lg leading-relaxed mt-2\",\n                                                        children: \"Mandatory information missing. Do you want to continue?\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\GeneralSection.js\",\n                                                        lineNumber: 1381,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\GeneralSection.js\",\n                                                    lineNumber: 1380,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-end p-6 space-x-2 justify-end\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: closeModal,\n                                                            \"data-modal-hide\": \"default-modal\",\n                                                            type: \"button\",\n                                                            className: \"border border-skin-primary text-skin-primary focus:ring-4 focus:outline-none focus:ring-blue-300 font-medium rounded-md text-md px-6 py-2 text-center \",\n                                                            children: \"Cancel\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\GeneralSection.js\",\n                                                            lineNumber: 1387,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: handleContinueSubmit,\n                                                            \"data-modal-hide\": \"default-modal\",\n                                                            type: \"button\",\n                                                            className: \"text-white bg-skin-primary hover:bg-skin-primary focus:ring-4 focus:outline-none focus:ring-blue-300 font-medium rounded-md text-md px-6 py-2 text-center \",\n                                                            children: \"Continue\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\GeneralSection.js\",\n                                                            lineNumber: 1395,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\GeneralSection.js\",\n                                                    lineNumber: 1386,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\GeneralSection.js\",\n                                            lineNumber: 1358,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\GeneralSection.js\",\n                                        lineNumber: 1356,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\GeneralSection.js\",\n                                    lineNumber: 1347,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\GeneralSection.js\",\n                                lineNumber: 1346,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\GeneralSection.js\",\n                            lineNumber: 1345,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\GeneralSection.js\",\n                    lineNumber: 1332,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\GeneralSection.js\",\n                lineNumber: 1331,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true);\n};\n_s(GeneralSection, \"tb6cHrIaJraLW+sNsk7uYu/Fcj0=\", false, function() {\n    return [\n        next_router__WEBPACK_IMPORTED_MODULE_9__.useRouter,\n        _azure_msal_react__WEBPACK_IMPORTED_MODULE_19__.useMsal,\n        _utils_loaders_loadingContext__WEBPACK_IMPORTED_MODULE_21__.useLoading\n    ];\n});\n_c = GeneralSection;\n/* harmony default export */ __webpack_exports__[\"default\"] = (GeneralSection);\nvar _c;\n$RefreshReg$(_c, \"GeneralSection\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevExports = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevExports) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports on update so we can compare the boundary\n                // signatures.\n                module.hot.dispose(function (data) {\n                    data.prevExports = currentExports;\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevExports !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevExports, currentExports)) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevExports !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/GeneralSection.js\n"));

/***/ }),

/***/ "./components/Navbar.js":
/*!******************************!*\
  !*** ./components/Navbar.js ***!
  \******************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   themes: function() { return /* binding */ themes; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _fortawesome_react_fontawesome__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @fortawesome/react-fontawesome */ \"./node_modules/@fortawesome/react-fontawesome/index.es.js\");\n/* harmony import */ var _fortawesome_free_solid_svg_icons__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @fortawesome/free-solid-svg-icons */ \"./node_modules/@fortawesome/free-solid-svg-icons/index.mjs\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/link */ \"./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _azure_msal_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @azure/msal-react */ \"./node_modules/@azure/msal-react/dist/index.js\");\n/* harmony import */ var _utils_theme_theme_switcher__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/utils/theme/theme-switcher */ \"./utils/theme/theme-switcher.js\");\n/* harmony import */ var js_cookie__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! js-cookie */ \"./node_modules/js-cookie/dist/js.cookie.mjs\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\nconst themes = [\n    {\n        bg: \"#022d71\",\n        text: \"#022d71\",\n        name: \"DPS\"\n    },\n    {\n        bg: \"#2e9b28\",\n        text: \"#2e9b28\",\n        name: \"EFC\"\n    },\n    {\n        bg: \"#a91e23\",\n        text: \"#a91e23\",\n        name: \"M&S\"\n    },\n    {\n        bg: \"#3d6546\",\n        text: \"#3d6546\",\n        name: \"FPP\"\n    }\n];\nconst Navbar = (param)=>{\n    let { userData } = param;\n    _s();\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_4__.useRouter)();\n    const [pageName, setPageName] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [currentRoute, setCurrentRoute] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const currentPath = router.pathname;\n        setCurrentRoute(currentPath);\n        if (currentPath === \"/finished-product-request/add\") {\n            js_cookie__WEBPACK_IMPORTED_MODULE_7__[\"default\"].set(\"PreviousPage\", \"FG\");\n            setPageName(\"Finished Goods Request\");\n        } else if (currentPath === \"/raw-material-request/add\") {\n            setPageName(\"Raw Material Request\");\n            js_cookie__WEBPACK_IMPORTED_MODULE_7__[\"default\"].set(\"PreviousPage\", \"RM\");\n        } else if (currentPath === \"/packaging-form/add\") {\n            setPageName(\"Packaging Request\");\n        } else if (currentPath === \"/variety/add\") {\n            js_cookie__WEBPACK_IMPORTED_MODULE_7__[\"default\"].set(\"PreviousPage\", \"NV\");\n            setPageName(\"New Variety Request\");\n        } else if (currentPath === \"/variety/[productId]/edit\") {\n            js_cookie__WEBPACK_IMPORTED_MODULE_7__[\"default\"].set(\"PreviousPage\", \"NV\");\n            setPageName(\"Edit New Variety Request\");\n        } else if (currentPath === \"/packaging-form/[productId]/edit\") {\n            js_cookie__WEBPACK_IMPORTED_MODULE_7__[\"default\"].set(\"PreviousPage\", \"PK\");\n            setPageName(\"Edit Packaging Request\");\n        } else if (currentPath === \"/raw-material-request/[productId]/edit\") {\n            js_cookie__WEBPACK_IMPORTED_MODULE_7__[\"default\"].set(\"PreviousPage\", \"RM\");\n            setPageName(\"Edit Raw Material Request\");\n        } else if (currentPath === \"/finished-product-request/[productId]/edit\") {\n            js_cookie__WEBPACK_IMPORTED_MODULE_7__[\"default\"].set(\"PreviousPage\", \"FG\");\n            setPageName(\"Edit Finished Goods Request\");\n        } else if (currentPath === null || currentPath === void 0 ? void 0 : currentPath.endsWith(\"/edit\")) {\n            setPageName(\"Edit Supplier\");\n        } else if (currentPath === null || currentPath === void 0 ? void 0 : currentPath.endsWith(\"/add\")) {\n            setPageName(\"Add Supplier\");\n        } else if (currentPath === null || currentPath === void 0 ? void 0 : currentPath.endsWith(\"/edit/forms\")) {\n            setPageName(\"Supplier Form\");\n        } else if (currentPath === null || currentPath === void 0 ? void 0 : currentPath.endsWith(\"/confirm\")) {\n            setPageName(\"Confirm Details for Supplier\");\n        } else if (currentPath === \"/suppliers\") {\n            setPageName(\"Suppliers\");\n        } else if (currentPath === \"/users\") {\n            setPageName(\"User Management\");\n        } else if (currentPath === \"/viewlogs\") {\n            setPageName(\"View Logs\");\n        } else if (currentPath === \"/products\") {\n            js_cookie__WEBPACK_IMPORTED_MODULE_7__[\"default\"].set(\"PreviousPage\", \"PK\");\n            setPageName(\"Products\");\n        } else if (currentPath === \"/variety\") {\n            setPageName(\"Variety\");\n            js_cookie__WEBPACK_IMPORTED_MODULE_7__[\"default\"].set(\"PreviousPage\", \"NV\");\n        } else if (currentPath === \"/finishedProductRequest\") {\n            js_cookie__WEBPACK_IMPORTED_MODULE_7__[\"default\"].set(\"PreviousPage\", \"FG\");\n            setPageName(\"Finished Product Request\");\n        } else if (currentPath === \"/rawMaterialRequest\") {\n            js_cookie__WEBPACK_IMPORTED_MODULE_7__[\"default\"].set(\"PreviousPage\", \"RM\");\n            setPageName(\"Raw Material Request\");\n        } else if (currentPath === \"/whatif\") {\n            setPageName(\"Whatif\");\n        } else if (currentPath === \"/service_level\" || currentPath === \"/service_level/reports/masterForcast\") {\n            setPageName(\"Service Level\");\n        }\n    }, [\n        router.pathname\n    ]);\n    const baseCompanyOptions = [\n        {\n            value: \"dpsltd\",\n            label: \"DPS\"\n        },\n        {\n            value: \"dpsltdms\",\n            label: \"DPS M&S\"\n        },\n        {\n            value: \"efcltd\",\n            label: \"EFC\"\n        },\n        {\n            value: \"fpp-ltd\",\n            label: \"FPP\"\n        }\n    ];\n    const companyOptions = [\n        ...baseCompanyOptions,\n        ...(userData === null || userData === void 0 ? void 0 : userData.role_id) === 6 ? [\n            {\n                value: \"issproduce\",\n                label: \"ISS\"\n            },\n            {\n                value: \"flrs\",\n                label: \"FLRS\",\n                disabled: true\n            },\n            {\n                value: \"thl\",\n                label: \"THL\",\n                disabled: true\n            }\n        ] : []\n    ];\n    const [selectedCompany, setSelectedCompany] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(js_cookie__WEBPACK_IMPORTED_MODULE_7__[\"default\"].get(\"ADCompanyName\") == \"DPS MS\" ? \"dpsltdms\" : js_cookie__WEBPACK_IMPORTED_MODULE_7__[\"default\"].get(\"company\") || \"\");\n    const handleCompanyChange = (event)=>{\n        console.log(\"e.target.value\", event.target.value);\n        const company = event.target.value;\n        if (company == \"dpsltd\") {\n            js_cookie__WEBPACK_IMPORTED_MODULE_7__[\"default\"].set(\"theme\", \"#022D71\");\n            js_cookie__WEBPACK_IMPORTED_MODULE_7__[\"default\"].set(\"ADCompanyName\", \"DPS\");\n            setSelectedCompany(company);\n            js_cookie__WEBPACK_IMPORTED_MODULE_7__[\"default\"].set(\"company\", company);\n        } else if (company == \"dpsltdms\") {\n            js_cookie__WEBPACK_IMPORTED_MODULE_7__[\"default\"].set(\"theme\", \"#0d6bfc\");\n            js_cookie__WEBPACK_IMPORTED_MODULE_7__[\"default\"].set(\"ADCompanyName\", \"DPS MS\");\n            setSelectedCompany(\"dpsltd\");\n            js_cookie__WEBPACK_IMPORTED_MODULE_7__[\"default\"].set(\"company\", \"dpsltd\");\n        } else if (company == \"efcltd\") {\n            js_cookie__WEBPACK_IMPORTED_MODULE_7__[\"default\"].set(\"theme\", \"#3eab58\");\n            js_cookie__WEBPACK_IMPORTED_MODULE_7__[\"default\"].set(\"ADCompanyName\", \"EFC\");\n            setSelectedCompany(company);\n            js_cookie__WEBPACK_IMPORTED_MODULE_7__[\"default\"].set(\"company\", company);\n        } else if (company == \"fpp-ltd\") {\n            js_cookie__WEBPACK_IMPORTED_MODULE_7__[\"default\"].set(\"theme\", \"#3d6546\");\n            js_cookie__WEBPACK_IMPORTED_MODULE_7__[\"default\"].set(\"ADCompanyName\", \"Fresh Produce Partners\");\n            setSelectedCompany(company);\n            js_cookie__WEBPACK_IMPORTED_MODULE_7__[\"default\"].set(\"company\", company);\n        } else if (company == \"issproduce\") {\n            js_cookie__WEBPACK_IMPORTED_MODULE_7__[\"default\"].set(\"theme\", \"#ABC400\");\n            js_cookie__WEBPACK_IMPORTED_MODULE_7__[\"default\"].set(\"ADCompanyName\", \"Integrated Service Solutions Ltd\");\n            setSelectedCompany(company);\n            js_cookie__WEBPACK_IMPORTED_MODULE_7__[\"default\"].set(\"company\", company);\n        }\n        router.reload();\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"titlebar\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex flex-row justify-between w-full bg-skin-primary h-14\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-row w-full items-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"page-heading cursor-pointer\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fortawesome_react_fontawesome__WEBPACK_IMPORTED_MODULE_2__.FontAwesomeIcon, {\n                                        icon: _fortawesome_free_solid_svg_icons__WEBPACK_IMPORTED_MODULE_8__.faChevronLeft,\n                                        className: \"pageName text-white\",\n                                        onClick: ()=>router.back()\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\Navbar.js\",\n                                        lineNumber: 146,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                        href: currentRoute,\n                                        className: \"ml-4 2xl:text-lg font-poppinssemibold pageName text-white tracking-wide\",\n                                        children: pageName\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\Navbar.js\",\n                                        lineNumber: 151,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\Navbar.js\",\n                                lineNumber: 145,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_utils_theme_theme_switcher__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                className: \"ml-5\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\Navbar.js\",\n                                lineNumber: 158,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\Navbar.js\",\n                        lineNumber: 144,\n                        columnNumber: 11\n                    }, undefined),\n                    ((userData === null || userData === void 0 ? void 0 : userData.role_id) == 5 || (userData === null || userData === void 0 ? void 0 : userData.role_id) == 6) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-row justify-end w-1/2 items-center mr-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                            value: selectedCompany,\n                            onChange: handleCompanyChange,\n                            className: \"bg-white text-black rounded\",\n                            children: companyOptions.map((opt)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                    value: opt.value,\n                                    disabled: opt.disabled,\n                                    children: opt.label\n                                }, opt.value, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\Navbar.js\",\n                                    lineNumber: 168,\n                                    columnNumber: 19\n                                }, undefined))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\Navbar.js\",\n                            lineNumber: 162,\n                            columnNumber: 15\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\Navbar.js\",\n                        lineNumber: 161,\n                        columnNumber: 13\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\Navbar.js\",\n                lineNumber: 143,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\Navbar.js\",\n            lineNumber: 142,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\Navbar.js\",\n        lineNumber: 141,\n        columnNumber: 5\n    }, undefined);\n};\n_s(Navbar, \"8bgZbm+HleyJfidZ9YLsifOPi/U=\", false, function() {\n    return [\n        next_router__WEBPACK_IMPORTED_MODULE_4__.useRouter\n    ];\n});\n_c = Navbar;\n/* harmony default export */ __webpack_exports__[\"default\"] = (Navbar);\nvar _c;\n$RefreshReg$(_c, \"Navbar\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevExports = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevExports) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports on update so we can compare the boundary\n                // signatures.\n                module.hot.dispose(function (data) {\n                    data.prevExports = currentExports;\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevExports !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevExports, currentExports)) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevExports !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/Navbar.js\n"));

/***/ }),

/***/ "./components/SideBarLinks.js":
/*!************************************!*\
  !*** ./components/SideBarLinks.js ***!
  \************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ SideBarLinks; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _fortawesome_free_solid_svg_icons__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @fortawesome/free-solid-svg-icons */ \"./node_modules/@fortawesome/free-solid-svg-icons/index.mjs\");\n/* harmony import */ var _fortawesome_react_fontawesome__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @fortawesome/react-fontawesome */ \"./node_modules/@fortawesome/react-fontawesome/index.es.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _utils_loaders_loadingContext__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/utils/loaders/loadingContext */ \"./utils/loaders/loadingContext.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\nfunction SideBarLinks(param) {\n    let { isSuppliersActive, userData, company, isUsersActive, isLogsActive, isWhatifActive, isServiceLevelActive, isProductsActive, ADCompany, currentPathname } = param;\n    var _process_env_NEXT_PUBLIC_AVAILABLE_MODULES;\n    _s();\n    const availableModules = (_process_env_NEXT_PUBLIC_AVAILABLE_MODULES = \"supplier,products,whatif,serviceLevel,users,logs\") === null || _process_env_NEXT_PUBLIC_AVAILABLE_MODULES === void 0 ? void 0 : _process_env_NEXT_PUBLIC_AVAILABLE_MODULES.split(\",\");\n    const { setIsLoading } = (0,_utils_loaders_loadingContext__WEBPACK_IMPORTED_MODULE_4__.useLoading)();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n        className: \"nav navbar-nav\",\n        children: availableModules && availableModules.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n            children: [\n                availableModules.includes(\"supplier\") && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                    className: \"flex justify-center mb-3\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                        href: \"/suppliers\",\n                        title: \"Supplier\",\n                        className: \"!px-4 !py-3 bg-white rounded-md text-center \".concat(isSuppliersActive ? \"opacity-100\" : \"opacity-70\"),\n                        onClick: (e)=>{\n                            if (isSuppliersActive && (currentPathname === null || currentPathname === void 0 ? void 0 : currentPathname.startsWith(\"/suppliers\"))) {\n                                e.preventDefault();\n                                window.location.reload();\n                            } else {\n                                setIsLoading(true);\n                            }\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fortawesome_react_fontawesome__WEBPACK_IMPORTED_MODULE_1__.FontAwesomeIcon, {\n                            icon: _fortawesome_free_solid_svg_icons__WEBPACK_IMPORTED_MODULE_5__.faTruck,\n                            size: \"lg\",\n                            className: \"text-skin-primary\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\SideBarLinks.js\",\n                            lineNumber: 63,\n                            columnNumber: 17\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\SideBarLinks.js\",\n                        lineNumber: 45,\n                        columnNumber: 15\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\SideBarLinks.js\",\n                    lineNumber: 44,\n                    columnNumber: 13\n                }, this),\n                availableModules.includes(\"products\") && !(userData === null || userData === void 0 ? void 0 : userData.department_id) == 5 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                    className: \"flex justify-center mb-3\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                        href: \"/products\",\n                        title: \"Products\",\n                        className: \"!px-4 !py-3 bg-white rounded-md text-center \".concat(isProductsActive ? \"opacity-100\" : \"opacity-70\"),\n                        onClick: (e)=>{\n                            if (isProductsActive) {\n                                e.preventDefault();\n                                window.location.reload();\n                            } else {\n                                setIsLoading(true);\n                            }\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fortawesome_react_fontawesome__WEBPACK_IMPORTED_MODULE_1__.FontAwesomeIcon, {\n                            icon: _fortawesome_free_solid_svg_icons__WEBPACK_IMPORTED_MODULE_5__.faBoxArchive,\n                            size: \"lg\",\n                            className: \"text-skin-primary\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\SideBarLinks.js\",\n                            lineNumber: 88,\n                            columnNumber: 17\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\SideBarLinks.js\",\n                        lineNumber: 73,\n                        columnNumber: 15\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\SideBarLinks.js\",\n                    lineNumber: 72,\n                    columnNumber: 13\n                }, this),\n                (company == \"efcltd\" || company == \"flrs\" || company == \"thl\") && ((userData === null || userData === void 0 ? void 0 : userData.department_id) == 1 || (userData === null || userData === void 0 ? void 0 : userData.department_id) == 2) && availableModules.includes(\"whatif\") && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                    className: \"flex justify-center mb-3\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                        href: \"/whatif\",\n                        title: \"What if\",\n                        className: \"!px-4 !py-3 bg-white rounded-md text-center \".concat(isWhatifActive ? \"opacity-100\" : \"opacity-70\"),\n                        onClick: (e)=>{\n                            if (isWhatifActive) {\n                                e.preventDefault();\n                                window.location.reload();\n                            } else {\n                                setIsLoading(true);\n                            }\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fortawesome_react_fontawesome__WEBPACK_IMPORTED_MODULE_1__.FontAwesomeIcon, {\n                            icon: _fortawesome_free_solid_svg_icons__WEBPACK_IMPORTED_MODULE_5__.faChartLine,\n                            size: \"lg\",\n                            className: \"text-skin-primary\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\SideBarLinks.js\",\n                            lineNumber: 115,\n                            columnNumber: 19\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\SideBarLinks.js\",\n                        lineNumber: 100,\n                        columnNumber: 17\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\SideBarLinks.js\",\n                    lineNumber: 99,\n                    columnNumber: 15\n                }, this),\n                ((userData === null || userData === void 0 ? void 0 : userData.department_id) == 1 || (userData === null || userData === void 0 ? void 0 : userData.department_id) == 2) && availableModules.includes(\"serviceLevel\") && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                    className: \"flex justify-center mb-3\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                        href: \"/service_level\",\n                        title: \"Service Level\",\n                        className: \"!px-4 !py-3 bg-white rounded-md text-center \".concat(isServiceLevelActive ? \"opacity-100\" : \"opacity-70\"),\n                        onClick: (e)=>{\n                            if (isServiceLevelActive) {\n                                e.preventDefault();\n                                window.location.reload();\n                            } else {\n                                setIsLoading(true);\n                            }\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fortawesome_react_fontawesome__WEBPACK_IMPORTED_MODULE_1__.FontAwesomeIcon, {\n                            icon: _fortawesome_free_solid_svg_icons__WEBPACK_IMPORTED_MODULE_5__.faFileCircleQuestion,\n                            size: \"lg\",\n                            className: \"text-skin-primary\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\SideBarLinks.js\",\n                            lineNumber: 142,\n                            columnNumber: 19\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\SideBarLinks.js\",\n                        lineNumber: 127,\n                        columnNumber: 17\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\SideBarLinks.js\",\n                    lineNumber: 126,\n                    columnNumber: 15\n                }, this),\n                ((userData === null || userData === void 0 ? void 0 : userData.role_id) === 1 || (userData === null || userData === void 0 ? void 0 : userData.role_id) === 5 || (userData === null || userData === void 0 ? void 0 : userData.role_id) === 6) && availableModules.includes(\"users\") && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                    className: \"flex justify-center mb-3\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                        href: \"/users\",\n                        title: \"Users\",\n                        className: \"!px-4 !py-3 bg-white rounded-md text-center \".concat(isUsersActive ? \"opacity-100\" : \"opacity-70\"),\n                        onClick: (e)=>{\n                            if (isUsersActive) {\n                                e.preventDefault();\n                                window.location.reload();\n                            } else {\n                                setIsLoading(true);\n                            }\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fortawesome_react_fontawesome__WEBPACK_IMPORTED_MODULE_1__.FontAwesomeIcon, {\n                            icon: _fortawesome_free_solid_svg_icons__WEBPACK_IMPORTED_MODULE_5__.faUsersLine,\n                            size: \"lg\",\n                            className: \"text-skin-primary\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\SideBarLinks.js\",\n                            lineNumber: 172,\n                            columnNumber: 19\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\SideBarLinks.js\",\n                        lineNumber: 156,\n                        columnNumber: 17\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\SideBarLinks.js\",\n                    lineNumber: 155,\n                    columnNumber: 15\n                }, this),\n                ((userData === null || userData === void 0 ? void 0 : userData.role_id) === 1 || (userData === null || userData === void 0 ? void 0 : userData.role_id) === 5 || (userData === null || userData === void 0 ? void 0 : userData.role_id) === 6) && availableModules.includes(\"logs\") && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                    className: \"flex justify-center mb-3\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                        href: \"/viewlogs\",\n                        title: \"Logs\",\n                        className: \"!px-4 !py-3 bg-white rounded-md text-center \".concat(isLogsActive ? \"opacity-100\" : \"opacity-70\"),\n                        onClick: (e)=>{\n                            if (isLogsActive) {\n                                e.preventDefault();\n                                window.location.reload();\n                            } else {\n                                setIsLoading(true);\n                            }\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fortawesome_react_fontawesome__WEBPACK_IMPORTED_MODULE_1__.FontAwesomeIcon, {\n                            icon: _fortawesome_free_solid_svg_icons__WEBPACK_IMPORTED_MODULE_5__.faList,\n                            size: \"lg\",\n                            className: \"text-skin-primary\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\SideBarLinks.js\",\n                            lineNumber: 201,\n                            columnNumber: 19\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\SideBarLinks.js\",\n                        lineNumber: 185,\n                        columnNumber: 17\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\SideBarLinks.js\",\n                    lineNumber: 184,\n                    columnNumber: 15\n                }, this)\n            ]\n        }, void 0, true)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\SideBarLinks.js\",\n        lineNumber: 40,\n        columnNumber: 5\n    }, this);\n}\n_s(SideBarLinks, \"HE5iXj3OUhzAOBpDJUVF6nKZb8k=\", false, function() {\n    return [\n        _utils_loaders_loadingContext__WEBPACK_IMPORTED_MODULE_4__.useLoading\n    ];\n});\n_c = SideBarLinks;\nvar _c;\n$RefreshReg$(_c, \"SideBarLinks\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevExports = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevExports) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports on update so we can compare the boundary\n                // signatures.\n                module.hot.dispose(function (data) {\n                    data.prevExports = currentExports;\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevExports !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevExports, currentExports)) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevExports !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/SideBarLinks.js\n"));

/***/ }),

/***/ "./components/Sidebar.js":
/*!*******************************!*\
  !*** ./components/Sidebar.js ***!
  \*******************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ Sidebar; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _public_images_dps_logo_png__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../public/images/dps-logo.png */ \"./public/images/dps-logo.png\");\n/* harmony import */ var _public_images_efc_logo_jpg__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../public/images/efc_logo.jpg */ \"./public/images/efc_logo.jpg\");\n/* harmony import */ var _public_images_dps_ms_logo_png__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../public/images/dps_ms_logo.png */ \"./public/images/dps_ms_logo.png\");\n/* harmony import */ var _public_images_fpp_logo_png__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../public/images/fpp_logo.png */ \"./public/images/fpp_logo.png\");\n/* harmony import */ var _public_images_iss_logo_jpg__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../public/images/iss_logo.jpg */ \"./public/images/iss_logo.jpg\");\n/* harmony import */ var _public_images_nav_icon_png__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ../public/images/nav-icon.png */ \"./public/images/nav-icon.png\");\n/* harmony import */ var _public_images_user_png__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ../public/images/user.png */ \"./public/images/user.png\");\n/* harmony import */ var _public_images_loading_img_png__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ../public/images/loading_img.png */ \"./public/images/loading_img.png\");\n/* harmony import */ var _public_images_logout_icon_png__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ../public/images/logout-icon.png */ \"./public/images/logout-icon.png\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! next/link */ \"./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_12___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_12__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! next/image */ \"./node_modules/next/image.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_13___default = /*#__PURE__*/__webpack_require__.n(next_image__WEBPACK_IMPORTED_MODULE_13__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! next/navigation */ \"./node_modules/next/navigation.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_14___default = /*#__PURE__*/__webpack_require__.n(next_navigation__WEBPACK_IMPORTED_MODULE_14__);\n/* harmony import */ var _utils_auth_auth__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! ../utils/auth/auth */ \"./utils/auth/auth.js\");\n/* harmony import */ var _azure_msal_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @azure/msal-react */ \"./node_modules/@azure/msal-react/dist/index.js\");\n/* harmony import */ var js_cookie__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! js-cookie */ \"./node_modules/js-cookie/dist/js.cookie.mjs\");\n/* harmony import */ var react_loader_spinner__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! react-loader-spinner */ \"./node_modules/react-loader-spinner/dist/module.js\");\n/* harmony import */ var _SideBarLinks__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! ./SideBarLinks */ \"./components/SideBarLinks.js\");\n/* provided dependency */ var process = __webpack_require__(/*! process */ \"./node_modules/next/dist/build/polyfills/process.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction Sidebar() {\n    _s();\n    const [userData, setUserData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [company, setCompany] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [ADCompany, setADCompany] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const userCookie = js_cookie__WEBPACK_IMPORTED_MODULE_17__[\"default\"].get(\"user\");\n        const companyCookie = js_cookie__WEBPACK_IMPORTED_MODULE_17__[\"default\"].get(\"company\");\n        const ADcompanyCookie = js_cookie__WEBPACK_IMPORTED_MODULE_17__[\"default\"].get(\"ADCompanyName\");\n        if (userCookie) {\n            setUserData(JSON.parse(userCookie));\n        }\n        if (companyCookie) {\n            setCompany(companyCookie);\n        }\n        if (ADcompanyCookie) {\n            setADCompany(ADcompanyCookie);\n        }\n    }, []);\n    const { instance } = (0,_azure_msal_react__WEBPACK_IMPORTED_MODULE_16__.useMsal)();\n    // const userRoleData = getCookieData(\"user\");\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const currentPathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_14__.usePathname)();\n    const [isSuppliersActive, setIsSuppliersActive] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)((currentPathname === null || currentPathname === void 0 ? void 0 : currentPathname.startsWith(\"/suppliers\")) || (currentPathname === null || currentPathname === void 0 ? void 0 : currentPathname.startsWith(\"/supplier\")));\n    const [isUsersActive, setIsUsersActive] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(currentPathname === \"/users\");\n    const [isLogsActive, setIsLogsActive] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(currentPathname === \"/viewlogs\");\n    const [isProductsActive, setIsProductsActive] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(currentPathname === \"/products\");\n    const [isWhatifActive, setIsWhatifActive] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(currentPathname === \"/whatif\");\n    const [isServiceLevelActive, setIsServiceLevelActive] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(currentPathname === \"/service_level\");\n    const [isRawMaterialRequestActive, setIsRawMaterialRequestActive] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(currentPathname === \"/raw-material-request/add\");\n    const getLogo = (company)=>{\n        if (!company) return;\n        switch(company){\n            case \"dpsltd\":\n                return _public_images_dps_logo_png__WEBPACK_IMPORTED_MODULE_3__[\"default\"];\n            case \"DPS MS\":\n                return _public_images_dps_ms_logo_png__WEBPACK_IMPORTED_MODULE_5__[\"default\"];\n            case \"efcltd\":\n                return _public_images_efc_logo_jpg__WEBPACK_IMPORTED_MODULE_4__[\"default\"];\n            case \"fpp-ltd\":\n                return _public_images_fpp_logo_png__WEBPACK_IMPORTED_MODULE_6__[\"default\"];\n            case \"thl\":\n                return _public_images_efc_logo_jpg__WEBPACK_IMPORTED_MODULE_4__[\"default\"];\n            case \"issproduce\":\n                return _public_images_iss_logo_jpg__WEBPACK_IMPORTED_MODULE_7__[\"default\"];\n            case \"flrs\":\n                return _public_images_efc_logo_jpg__WEBPACK_IMPORTED_MODULE_4__[\"default\"];\n            default:\n                return;\n        }\n    };\n    const getLogoSizeClass = (company)=>{\n        if (!company) return \"h-14 w-100\";\n        switch(company){\n            case \"dpsltd\":\n                return \"!h-16 !w-auto\";\n            default:\n                return \"h-14 w-100\";\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n            id: \"sidemenu\",\n            className: \"navbar navbar-default sidebar bg-skin-primary\",\n            role: \"navigation\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"container-fluid h-full\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_12___default()), {\n                        href: \"/suppliers\",\n                        title: \"Home\",\n                        className: \"z-50\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"brand\",\n                            children: company ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_13___default()), {\n                                src: getLogo(company == \"dpsltd\" && ADCompany == \"DPS MS\" ? \"DPS MS\" : company),\n                                alt: \"logo\",\n                                className: getLogoSizeClass(company == \"dpsltd\" && ADCompany == \"DPS MS\" ? \"DPS MS\" : company)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\Sidebar.js\",\n                                lineNumber: 117,\n                                columnNumber: 17\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_loader_spinner__WEBPACK_IMPORTED_MODULE_18__.Oval, {\n                                    color: \"#002D73\",\n                                    height: 20,\n                                    width: 20,\n                                    visible: true,\n                                    ariaLabel: \"oval-loading\",\n                                    secondaryColor: \"#0066FF\",\n                                    strokeWidth: 2,\n                                    strokeWidthSecondary: 2\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\Sidebar.js\",\n                                    lineNumber: 132,\n                                    columnNumber: 19\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\Sidebar.js\",\n                                lineNumber: 131,\n                                columnNumber: 17\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\Sidebar.js\",\n                            lineNumber: 115,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\Sidebar.js\",\n                        lineNumber: 114,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        id: \"bs-sidebar-navbar-collapse-1\",\n                        className: \"pt-10 w-100 text-center flex flex-col justify-between flex-end items-stretch h-full\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col w-full\",\n                                children: userData && company && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_SideBarLinks__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                    isSuppliersActive: isSuppliersActive,\n                                    userData: userData,\n                                    currentPathname: currentPathname,\n                                    company: company,\n                                    isUsersActive: isUsersActive,\n                                    isLogsActive: isLogsActive,\n                                    isProductsActive: isProductsActive,\n                                    isWhatifActive: isWhatifActive,\n                                    isServiceLevelActive: isServiceLevelActive,\n                                    ADCompany: ADCompany\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\Sidebar.js\",\n                                    lineNumber: 152,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\Sidebar.js\",\n                                lineNumber: 150,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-center flex-col items-center gap-4 my-4 mb-20 cursor-pointer\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        title: \"IT Training Material\",\n                                        href: \"\".concat(process.env.NEXT_PUBLIC_TRAINING_MATERIAL),\n                                        target: \"_blank\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            xmlns: \"http://www.w3.org/2000/svg\",\n                                            viewBox: \"0 0 448 512\",\n                                            fill: \"#FFFF\",\n                                            className: \"w-6\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                d: \"M0 88C0 39.4 39.4 0 88 0L392 0c30.9 0 56 25.1 56 56l0 288c0 22.3-13.1 41.6-32 50.6l0 69.4 8 0c13.3 0 24 10.7 24 24s-10.7 24-24 24L80 512c-44.2 0-80-35.8-80-80c0-2.7 .1-5.4 .4-8L0 424 0 88zM80 400c-17.7 0-32 14.3-32 32s14.3 32 32 32l288 0 0-64L80 400zM48 358.7c9.8-4.3 20.6-6.7 32-6.7l312 0c4.4 0 8-3.6 8-8l0-288c0-4.4-3.6-8-8-8L88 48C65.9 48 48 65.9 48 88l0 270.7zM160 112l8.8-17.7c2.9-5.9 11.4-5.9 14.3 0L192 112l17.7 8.8c5.9 2.9 5.9 11.4 0 14.3L192 144l-8.8 17.7c-2.9 5.9-11.4 5.9-14.3 0L160 144l-17.7-8.8c-5.9-2.9-5.9-11.4 0-14.3L160 112zM264 216l16.6-38.8c2.8-6.5 11.9-6.5 14.7 0L312 216l38.8 16.6c6.5 2.8 6.5 11.9 0 14.7L312 264l-16.6 38.8c-2.8 6.5-11.9 6.5-14.7 0L264 264l-38.8-16.6c-6.5-2.8-6.5-11.9 0-14.7L264 216z\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\Sidebar.js\",\n                                                lineNumber: 178,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\Sidebar.js\",\n                                            lineNumber: 172,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\Sidebar.js\",\n                                        lineNumber: 167,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        title: \"Logout\",\n                                        onClick: ()=>{\n                                            localStorage.removeItem(\"superUser\");\n                                            js_cookie__WEBPACK_IMPORTED_MODULE_17__[\"default\"].remove(\"company\");\n                                            js_cookie__WEBPACK_IMPORTED_MODULE_17__[\"default\"].remove(\"ADCompanyName\");\n                                            localStorage.removeItem(\"id\");\n                                            localStorage.removeItem(\"name\");\n                                            localStorage.removeItem(\"role\");\n                                            localStorage.removeItem(\"email\");\n                                            js_cookie__WEBPACK_IMPORTED_MODULE_17__[\"default\"].remove(\"user\");\n                                            js_cookie__WEBPACK_IMPORTED_MODULE_17__[\"default\"].remove(\"theme\");\n                                            js_cookie__WEBPACK_IMPORTED_MODULE_17__[\"default\"].remove(\"token\");\n                                            const redirectUrl = \"/login?redirect=\".concat(encodeURIComponent(window.location.pathname));\n                                            (0,_utils_auth_auth__WEBPACK_IMPORTED_MODULE_15__.logoutHandler)(instance, redirectUrl);\n                                        },\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_13___default()), {\n                                            src: _public_images_logout_icon_png__WEBPACK_IMPORTED_MODULE_11__[\"default\"],\n                                            alt: \"logout\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\Sidebar.js\",\n                                            lineNumber: 200,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\Sidebar.js\",\n                                        lineNumber: 181,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\Sidebar.js\",\n                                lineNumber: 166,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\Sidebar.js\",\n                        lineNumber: 146,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\Sidebar.js\",\n                lineNumber: 113,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\Sidebar.js\",\n            lineNumber: 108,\n            columnNumber: 7\n        }, this)\n    }, void 0, false);\n}\n_s(Sidebar, \"CqfPHC0NzGwmyxMjqjmKLbgxaBY=\", false, function() {\n    return [\n        _azure_msal_react__WEBPACK_IMPORTED_MODULE_16__.useMsal,\n        next_router__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        next_navigation__WEBPACK_IMPORTED_MODULE_14__.usePathname\n    ];\n});\n_c = Sidebar;\nvar _c;\n$RefreshReg$(_c, \"Sidebar\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevExports = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevExports) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports on update so we can compare the boundary\n                // signatures.\n                module.hot.dispose(function (data) {\n                    data.prevExports = currentExports;\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevExports !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevExports, currentExports)) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevExports !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/Sidebar.js\n"));

/***/ }),

/***/ "./utils/auth/auth.js":
/*!****************************!*\
  !*** ./utils/auth/auth.js ***!
  \****************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getIdTokeSilently: function() { return /* binding */ getIdTokeSilently; },\n/* harmony export */   getToken: function() { return /* binding */ getToken; },\n/* harmony export */   logoutHandler: function() { return /* binding */ logoutHandler; },\n/* harmony export */   setUserCookie: function() { return /* binding */ setUserCookie; },\n/* harmony export */   useAuth: function() { return /* binding */ useAuth; },\n/* harmony export */   useUser: function() { return /* binding */ useUser; }\n/* harmony export */ });\n/* harmony import */ var _authConfig__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./authConfig */ \"./utils/auth/authConfig.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _services_apiConfig__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/services/apiConfig */ \"./services/apiConfig.js\");\n/* harmony import */ var js_cookie__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! js-cookie */ \"./node_modules/js-cookie/dist/js.cookie.mjs\");\n/* harmony import */ var react_toastify__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react-toastify */ \"./node_modules/react-toastify/dist/react-toastify.esm.mjs\");\n/* harmony import */ var react_toastify_dist_ReactToastify_css__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! react-toastify/dist/ReactToastify.css */ \"./node_modules/react-toastify/dist/ReactToastify.css\");\n/* harmony import */ var react_toastify_dist_ReactToastify_css__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(react_toastify_dist_ReactToastify_css__WEBPACK_IMPORTED_MODULE_5__);\n// auth.js\nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\n\n\n\nconst getToken = async ()=>{\n    try {\n        const accounts = msalInstance.getAllAccounts();\n        if (accounts.length === 0) {\n            throw new Error(\"No account found\");\n        }\n        const tokenResponse = await msalInstance.acquireTokenSilent({\n            scopes: _authConfig__WEBPACK_IMPORTED_MODULE_0__.loginRequest.scopes,\n            account: accounts[0]\n        });\n        return tokenResponse.accessToken;\n    } catch (error) {\n        console.error(\"Failed to get token:\", error);\n        throw error;\n    }\n};\nconst logoutHandler = async (instance, redirectUrl)=>{\n    instance.clearCache();\n    localStorage.removeItem(\"superUser\");\n    localStorage.removeItem(\"company\");\n    localStorage.removeItem(\"id\");\n    localStorage.removeItem(\"name\");\n    localStorage.removeItem(\"role\");\n    localStorage.removeItem(\"email\");\n    js_cookie__WEBPACK_IMPORTED_MODULE_3__[\"default\"].remove(\"user\");\n    js_cookie__WEBPACK_IMPORTED_MODULE_3__[\"default\"].remove(\"theme\");\n    js_cookie__WEBPACK_IMPORTED_MODULE_3__[\"default\"].remove(\"token\");\n    js_cookie__WEBPACK_IMPORTED_MODULE_3__[\"default\"].remove(\"company\");\n    js_cookie__WEBPACK_IMPORTED_MODULE_3__[\"default\"].remove(\"ADCompanyName\");\n    instance.logout({\n        postLogoutRedirectUri: redirectUrl || _authConfig__WEBPACK_IMPORTED_MODULE_0__.BASE_URL\n    }).catch((error)=>{\n        console.error(\"Logout error\", error);\n    });\n};\nconst getIdTokeSilently = async (instance, account)=>{\n    if (account) {\n        try {\n            // Provide the scopes you need for your application\n            const request = {\n                account: account,\n                scopes: [\n                    \"User.Read\"\n                ]\n            };\n            const response = await instance.acquireTokenSilent(request);\n            return response.accessToken;\n        } catch (error) {\n            console.error(\"Silent login error\", error);\n            return null;\n        }\n    }\n    return null;\n};\nconst setUserCookie = async (data, instance, account, updateToken)=>{\n    let serverAddress = _services_apiConfig__WEBPACK_IMPORTED_MODULE_2__.apiConfig.serverAddress;\n    fetch(\"\".concat(serverAddress, \"users/getUserByEmail\"), {\n        method: \"POST\",\n        headers: {\n            Authorization: \"Bearer \".concat(data.accessToken),\n            Accept: \"application/json\",\n            \"Content-Type\": \"application/json\"\n        },\n        body: JSON.stringify({\n            email: data.account.username,\n            name: data.account.name\n        })\n    }).then((res)=>{\n        // if(res.status === 400 || res.status === 401){\n        //   setTimeout(function(){\n        //     logoutHandler(instance);\n        //     router.push('/login');\n        //   }, 1000);\n        // }\n        if (res.status === 200) {\n            return res.json();\n        }\n        return Promise.reject(res);\n    }).then((json)=>{\n        let res = json;\n        if (res.length > 0) {\n            const userdetails = {\n                user_id: res[0].user_id,\n                name: data.account.name,\n                email: data.account.username,\n                role: res[0].role_id,\n                token: data.accessToken,\n                role_id: res[0].role_id,\n                department_id: res[0].department_id\n            };\n            localStorage.setItem(\"superUser\", res[0].role_id);\n            js_cookie__WEBPACK_IMPORTED_MODULE_3__[\"default\"].set(\"user\", JSON.stringify(userdetails), {\n                expires: 365\n            });\n            updateToken(userdetails);\n        // //const tokenStatus = updateToken(res, data.accessToken);\n        // if (tokenStatus) {\n        //   // sessionStorage.setItem(\"useremail\", res[0].email);\n        //   // router.push(\"/suppliers\");\n        // } else {\n        //   sessionStorage.clear();\n        //   router.push({ pathname: \"/login\", query: { message: \"text\" } });\n        // }\n        } else {\n            js_cookie__WEBPACK_IMPORTED_MODULE_3__[\"default\"].set(\"user\", JSON.stringify({\n                email: data.account.username,\n                name: data.account.name,\n                token: data.accessToken,\n                role: null\n            }));\n            updateToken({\n                email: data.account.username,\n                name: data.account.name\n            });\n        }\n    }).catch((error)=>{\n        react_toastify__WEBPACK_IMPORTED_MODULE_4__.toast.error(error.statusText, {\n            position: \"top-right\"\n        });\n    });\n    return true;\n};\nconst useAuth = ()=>{\n    _s();\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_1__.useRouter)();\n    const login = async ()=>{\n        try {\n            let msalResponse = await msalInstance.loginPopup(_authConfig__WEBPACK_IMPORTED_MODULE_0__.loginRequest);\n            getAccountDetails(msalResponse);\n        //router.push(\"/suppliers\");\n        } catch (error) {\n            console.error(\"Failed to log in:\", error);\n        }\n    };\n    const updateToken = (resData, token)=>{\n        let res = resData;\n        let serverAddress = _services_apiConfig__WEBPACK_IMPORTED_MODULE_2__.apiConfig.serverAddress;\n        let userData = {\n            id: res[0].user_id,\n            role_id: res[0].role_id,\n            first_name: res[0].first_name,\n            last_name: res[0].last_name,\n            email: res[0].email,\n            mobile_no: res[0].mobile_no,\n            password: res[0].password,\n            status: res[0].status,\n            created_date: res[0].created_date,\n            token: token\n        };\n        fetch(\"\".concat(serverAddress, \"users/update-user/\").concat(res[0].user_id), {\n            method: \"PUT\",\n            headers: {\n                //Authorization: `Bearer ${token}`,\n                Accept: \"application/json\",\n                \"Content-Type\": \"application/json\"\n            },\n            body: JSON.stringify(userData)\n        }).then((res)=>{\n            if (res.status === 200) {\n                return res.json();\n            }\n            return Promise.reject(res);\n        }).then((json)=>{\n            let res = json;\n            if (res.length > 0) {\n                return true;\n            } else {\n                return false;\n            }\n        });\n    };\n    const isLoggedIn = ()=>{\n        return !!msalInstance.getAllAccounts().length;\n    };\n    return {\n        login,\n        logout,\n        isLoggedIn\n    };\n};\n_s(useAuth, \"fN7XvhJ+p5oE6+Xlo0NJmXpxjC8=\", false, function() {\n    return [\n        next_router__WEBPACK_IMPORTED_MODULE_1__.useRouter\n    ];\n});\nconst useUser = ()=>{\n    _s1();\n    const [user, setUser] = useState(null);\n    const { instance } = useMsal();\n    const activeAccount = instance.getActiveAccount();\n    useEffect(()=>{\n        const getUser = async ()=>{\n            try {\n                if (activeAccount) {\n                    const accessToken = await instance.acquireTokenSilent({\n                        scopes: [\n                            \"User.Read\",\n                            \"Directory.Read.All\",\n                            \"User.ReadBasic.All\"\n                        ]\n                    });\n                    const userResponse = await fetch(\"https://graph.microsoft.com/v1.0/me\", {\n                        headers: {\n                            Authorization: \"Bearer \".concat(accessToken.accessToken)\n                        }\n                    });\n                    if (!userResponse.ok) {\n                        throw new Error(\"Request failed with status \".concat(userResponse.status));\n                    }\n                    const userData = await userResponse.json();\n                    setUser(userData);\n                }\n            } catch (error) {\n                console.error(\"Error fetching user data:\", error);\n            }\n        };\n        const intervalId = setInterval(getUser, 1000); // Refresh user data every second\n        return ()=>{\n            clearInterval(intervalId); // Clean up interval on component unmount\n        };\n    }, [\n        activeAccount,\n        instance\n    ]);\n    return user;\n};\n_s1(useUser, \"j5U2A8YVrGIZ6m01RV+TAEjV0cQ=\", true);\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevExports = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevExports) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports on update so we can compare the boundary\n                // signatures.\n                module.hot.dispose(function (data) {\n                    data.prevExports = currentExports;\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevExports !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevExports, currentExports)) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevExports !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./utils/auth/auth.js\n"));

/***/ })

});