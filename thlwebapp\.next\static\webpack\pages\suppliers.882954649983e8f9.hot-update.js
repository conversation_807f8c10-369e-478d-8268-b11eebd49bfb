"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/suppliers",{

/***/ "./pages/suppliers.js":
/*!****************************!*\
  !*** ./pages/suppliers.js ***!
  \****************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __N_SSP: function() { return /* binding */ __N_SSP; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _components_Layout__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/Layout */ \"./components/Layout.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _fortawesome_react_fontawesome__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @fortawesome/react-fontawesome */ \"./node_modules/@fortawesome/react-fontawesome/index.es.js\");\n/* harmony import */ var _fortawesome_free_solid_svg_icons__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! @fortawesome/free-solid-svg-icons */ \"./node_modules/@fortawesome/free-solid-svg-icons/index.mjs\");\n/* harmony import */ var ag_grid_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ag-grid-react */ \"./node_modules/ag-grid-react/lib/main.js\");\n/* harmony import */ var ag_grid_react__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(ag_grid_react__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var ag_grid_community_styles_ag_grid_css__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ag-grid-community/styles//ag-grid.css */ \"./node_modules/ag-grid-community/styles/ag-grid.css\");\n/* harmony import */ var ag_grid_community_styles_ag_grid_css__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(ag_grid_community_styles_ag_grid_css__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var ag_grid_community_styles_ag_theme_alpine_css__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ag-grid-community/styles//ag-theme-alpine.css */ \"./node_modules/ag-grid-community/styles/ag-theme-alpine.css\");\n/* harmony import */ var ag_grid_community_styles_ag_theme_alpine_css__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(ag_grid_community_styles_ag_theme_alpine_css__WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var _utils_renderer_actionRenderer__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../utils/renderer/actionRenderer */ \"./utils/renderer/actionRenderer.js\");\n/* harmony import */ var _utils_renderer_nameRenderer__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ../utils/renderer/nameRenderer */ \"./utils/renderer/nameRenderer.js\");\n/* harmony import */ var _utils_renderer_statusRenderer__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ../utils/renderer/statusRenderer */ \"./utils/renderer/statusRenderer.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! next/link */ \"./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_10___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_10__);\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_11___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_11__);\n/* harmony import */ var _services_apiConfig__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/services/apiConfig */ \"./services/apiConfig.js\");\n/* harmony import */ var js_cookie__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! js-cookie */ \"./node_modules/js-cookie/dist/js.cookie.mjs\");\n/* harmony import */ var react_toastify__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! react-toastify */ \"./node_modules/react-toastify/dist/react-toastify.esm.mjs\");\n/* harmony import */ var react_toastify_dist_ReactToastify_css__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! react-toastify/dist/ReactToastify.css */ \"./node_modules/react-toastify/dist/ReactToastify.css\");\n/* harmony import */ var react_toastify_dist_ReactToastify_css__WEBPACK_IMPORTED_MODULE_15___default = /*#__PURE__*/__webpack_require__.n(react_toastify_dist_ReactToastify_css__WEBPACK_IMPORTED_MODULE_15__);\n/* harmony import */ var _utils_loaders_loadingContext__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @/utils/loaders/loadingContext */ \"./utils/loaders/loadingContext.js\");\n/* harmony import */ var _utils_rolePermissionsContext__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! @/utils/rolePermissionsContext */ \"./utils/rolePermissionsContext.js\");\n/* harmony import */ var _barrel_optimize_names_Dialog_Transition_headlessui_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=Dialog,Transition!=!@headlessui/react */ \"__barrel_optimize__?names=Dialog,Transition!=!./node_modules/@headlessui/react/dist/headlessui.esm.js\");\n/* harmony import */ var _utils_exportExcel__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! ../utils/exportExcel */ \"./utils/exportExcel.js\");\n/* harmony import */ var _components_supplierCodeRenderer__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! @/components/supplierCodeRenderer */ \"./components/supplierCodeRenderer.js\");\n/* harmony import */ var _utils_fetchOptions__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! @/utils/fetchOptions */ \"./utils/fetchOptions.js\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n//import 'ag-grid-enterprise';\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst suppliers = (param)=>{\n    let { userData } = param;\n    _s();\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_11__.useRouter)();\n    const { permissions, updatePermissions } = (0,_utils_rolePermissionsContext__WEBPACK_IMPORTED_MODULE_17__.usePermissions)();\n    const [pageSize, setPageSize] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(15);\n    const [allRowData, setAllRowData] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [rowData, setRowData] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [isFiltered, setIsFiltered] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [isFilteredName, setIsFilteredName] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const gridRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)();\n    const { setIsLoading } = (0,_utils_loaders_loadingContext__WEBPACK_IMPORTED_MODULE_16__.useLoading)();\n    const [supplierRoles, setSupplierRoles] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [multipleFilterInternalData, setMultipleFilterInternalData] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [containsCancelledSupplier, setContainsCancelledSupplier] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [exportDisabled, setExportDisabled] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(true);\n    const [supplierCheckedValue, setSupplierCheckedValue] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [isCommonError, setCommonError] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"\");\n    const [isOpenOption, setIsOpenOption] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [isUnExportable, setIsUnExportable] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [unExportableSuppliernames, setUnExportableSupplierNames] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"\");\n    const [supplierUniqueCodeToast, setSupplierUniqueCodeToast] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [supplierCodeValid, setSupplierCodeValid] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(true);\n    const [selectedExportType, setSelectedExportType] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"\");\n    const [prophetId, setProphetId] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"\");\n    const [emailStatusPopup, setEmailStatusPopup] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [popupMessage, setPopUpMessage] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"\");\n    const [internalExportSuccess, setInternalExportSuccess] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [ISSExportSuccess, setISSExportSuccess] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [blockScreen, setBlockScreen] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [selectedStatus, setSelectedStatus] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"default\"); // State to track selected status\n    // Secure logout handler for session-based auth\n    const handleSecureLogout = async ()=>{\n        try {\n            const apiBase = \"http://localhost:8081\" || 0;\n            await fetch(\"\".concat(apiBase, \"/api/auth/logout\"), {\n                method: \"POST\",\n                credentials: \"include\"\n            });\n        } catch (error) {\n            console.error(\"Secure logout failed:\", error);\n        }\n        // Redirect to login\n        const redirectUrl = \"/login?redirect=\".concat(encodeURIComponent(window.location.pathname));\n        router.push(redirectUrl);\n    };\n    // Enhanced error handler for 401 responses\n    const handle401Error = function() {\n        let message = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : \"Your session has expired. Please log in again.\";\n        react_toastify__WEBPACK_IMPORTED_MODULE_14__.toast.error(message);\n        setTimeout(()=>{\n            handleSecureLogout();\n        }, 3000);\n    };\n    const closeOptionModal = ()=>{\n        setIsOpenOption(false);\n    };\n    const handleExportType = (e)=>{\n        setSelectedExportType(e.target.value);\n    };\n    const closeEmailPopup = ()=>{\n        setEmailStatusPopup(false);\n        if (internalExportSuccess && ISSExportSuccess) {\n            setStatusChange();\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        if (containsCancelledSupplier) {\n            react_toastify__WEBPACK_IMPORTED_MODULE_14__.toast.error(\"Cannot export cancelled supplier.\");\n            setExportDisabled(true);\n        }\n    }, [\n        containsCancelledSupplier\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        if (supplierUniqueCodeToast && !supplierCodeValid) {\n            react_toastify__WEBPACK_IMPORTED_MODULE_14__.toast.error(\"Supplier code is not unique and not valid, kindly make sure the supplier code is unique and valid.\");\n        } else if (supplierUniqueCodeToast) {\n            react_toastify__WEBPACK_IMPORTED_MODULE_14__.toast.error(\"Supplier code is not unique, kindly make sure the supplier code is unique.\");\n        } else if (!supplierCodeValid) {\n            react_toastify__WEBPACK_IMPORTED_MODULE_14__.toast.error(\"Supplier code is not valid\");\n        }\n    }, [\n        supplierUniqueCodeToast,\n        supplierCodeValid\n    ]);\n    // Helper function to extract company from email\n    const extractCompanyFromEmail = (email)=>{\n        if (!email) return \"unknown\";\n        const domain = email.split(\"@\")[1];\n        return domain.split(\".\")[0];\n    };\n    // Helper function to map company names to prophet IDs\n    const getCompanyProphetId = (adCompanyName)=>{\n        if (adCompanyName == \"FPP\" || adCompanyName == \"Fresh Produce Partners\") {\n            return 4;\n        } else if (adCompanyName == \"EFC\") {\n            return 3;\n        } else if (adCompanyName == \"DPS\") {\n            return 1;\n        } else if (adCompanyName == \"DPS MS\") {\n            return 2;\n        }\n        return 0;\n    };\n    const [company, setCompany] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"\");\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        if (typeof document !== \"undefined\") {\n            document.title = \"Suppliers\";\n        }\n        if (true) {\n            // Get company from userData instead of cookies\n            const companyFromUser = (userData === null || userData === void 0 ? void 0 : userData.company) || extractCompanyFromEmail(userData === null || userData === void 0 ? void 0 : userData.email);\n            if (companyFromUser) {\n                setCompany(companyFromUser);\n            }\n            localStorage.removeItem(\"current\");\n            localStorage.removeItem(\"allowedSections\");\n        }\n        js_cookie__WEBPACK_IMPORTED_MODULE_13__[\"default\"].remove(\"prophet\");\n        js_cookie__WEBPACK_IMPORTED_MODULE_13__[\"default\"].remove(\"prophets\");\n        setIsLoading(false);\n        getData(userData).then((data)=>{\n            if (data === null) {\n                return;\n            }\n            const formattedData = data === null || data === void 0 ? void 0 : data.map((row)=>{\n                const roleIds = (row === null || row === void 0 ? void 0 : row.role_ids) ? JSON.parse(row === null || row === void 0 ? void 0 : row.role_ids).map((ele)=>ele.role_id) : [];\n                const roleJson = JSON.parse(row === null || row === void 0 ? void 0 : row.role_json);\n                const formattedRow = {\n                    isActive: row === null || row === void 0 ? void 0 : row.is_active,\n                    prophets: (row === null || row === void 0 ? void 0 : row.prophet_ids) ? JSON.parse(row === null || row === void 0 ? void 0 : row.prophet_ids) : [],\n                    supplier_code: (row === null || row === void 0 ? void 0 : row.prophet_ids) ? JSON.parse(row === null || row === void 0 ? void 0 : row.prophet_ids)[0].prophet_code : [],\n                    company_name: (row === null || row === void 0 ? void 0 : row.name) ? row === null || row === void 0 ? void 0 : row.name : \"Not Entered\",\n                    country_code: row === null || row === void 0 ? void 0 : row.country_code,\n                    payment_type: row === null || row === void 0 ? void 0 : row.payment_type,\n                    payment_type_name: row === null || row === void 0 ? void 0 : row.payment_type_name,\n                    currency_name: row === null || row === void 0 ? void 0 : row.currency_name,\n                    currency_id: row === null || row === void 0 ? void 0 : row.iss_currency_id,\n                    global_gap_number: row === null || row === void 0 ? void 0 : row.global_gap_number,\n                    chile_certificate_number: row === null || row === void 0 ? void 0 : row.chile_certificate_number,\n                    red_tractor: row === null || row === void 0 ? void 0 : row.red_tractor,\n                    organic_certificate_number: row === null || row === void 0 ? void 0 : row.organic_certificate_number,\n                    puc_code: row === null || row === void 0 ? void 0 : row.puc_code,\n                    address_line_1: row === null || row === void 0 ? void 0 : row.address_line_1,\n                    address_line_2: row === null || row === void 0 ? void 0 : row.address_line_2,\n                    address_line_3: row === null || row === void 0 ? void 0 : row.address_line_3,\n                    address_line_4: row === null || row === void 0 ? void 0 : row.address_line_4,\n                    postal_code: row === null || row === void 0 ? void 0 : row.postal_code,\n                    id: row === null || row === void 0 ? void 0 : row.id,\n                    currency: (row === null || row === void 0 ? void 0 : row.currency) ? row === null || row === void 0 ? void 0 : row.currency : \"Not Entered\",\n                    currency_code: (row === null || row === void 0 ? void 0 : row.currency_code) ? row === null || row === void 0 ? void 0 : row.currency_code : \"\",\n                    Compliance: row === null || row === void 0 ? void 0 : row.compliance,\n                    Financials: row === null || row === void 0 ? void 0 : row.financial,\n                    General: row === null || row === void 0 ? void 0 : row.technical,\n                    Procurement: row === null || row === void 0 ? void 0 : row.procurement,\n                    requestor: row === null || row === void 0 ? void 0 : row.requestor_name,\n                    requestor_email: row === null || row === void 0 ? void 0 : row.requestor_email,\n                    companies: (row === null || row === void 0 ? void 0 : row.prophet_names) ? row === null || row === void 0 ? void 0 : row.prophet_names : \"Not Entered\",\n                    role: (row === null || row === void 0 ? void 0 : row.role_names) ? row === null || row === void 0 ? void 0 : row.role_names : \"Not Entered\",\n                    roleJson: (row === null || row === void 0 ? void 0 : row.role_json) ? JSON.parse(row === null || row === void 0 ? void 0 : row.role_json) : [],\n                    roleId: (row === null || row === void 0 ? void 0 : row.role_ids) ? JSON.parse(row === null || row === void 0 ? void 0 : row.role_ids) : [],\n                    roleIds: (row === null || row === void 0 ? void 0 : row.role_ids) ? JSON.parse(row === null || row === void 0 ? void 0 : row.role_ids).map((ele)=>{\n                        return ele.role_id;\n                    }) : [],\n                    supplier_type: row === null || row === void 0 ? void 0 : row.supplier_type_label,\n                    contacts_json: row === null || row === void 0 ? void 0 : row.contacts_json,\n                    distribution_points_json: (row === null || row === void 0 ? void 0 : row.distribution_points_json) ? JSON.parse(row === null || row === void 0 ? void 0 : row.distribution_points_json) : [],\n                    vat_number: row === null || row === void 0 ? void 0 : row.vat_number,\n                    payment_terms: row === null || row === void 0 ? void 0 : row.payment_terms,\n                    sort_bic: row === null || row === void 0 ? void 0 : row.decryptedSort_Bic,\n                    name_branch: row === null || row === void 0 ? void 0 : row.decryptedName_branch,\n                    account_number: row === null || row === void 0 ? void 0 : row.decryptedAccountNumber,\n                    vatable: row === null || row === void 0 ? void 0 : row.vatable,\n                    iss_ledger_code: row === null || row === void 0 ? void 0 : row.iss_ledger_code,\n                    internal_ledger_code: row === null || row === void 0 ? void 0 : row.internal_ledger_code,\n                    intermediatery_account_number: row === null || row === void 0 ? void 0 : row.decryptedIntermediatery_account_number,\n                    bacs_currency_code: row === null || row === void 0 ? void 0 : row.bacs_currency_code,\n                    has_iban: row === null || row === void 0 ? void 0 : row.has_iban,\n                    isProducerSupplier: row === null || row === void 0 ? void 0 : row.product_supplier,\n                    isEmergencyRequest: row === null || row === void 0 ? void 0 : row.emergency_request,\n                    supplier_group: row === null || row === void 0 ? void 0 : row.sendac_groups_json,\n                    supplierLinks: row === null || row === void 0 ? void 0 : row.supplier_links_json,\n                    status: row === null || row === void 0 ? void 0 : row.label,\n                    role_num: row === null || row === void 0 ? void 0 : row.role_nums,\n                    edi: row === null || row === void 0 ? void 0 : row.edi\n                };\n                if ((roleIds.includes(1) || (roleIds === null || roleIds === void 0 ? void 0 : roleIds.includes(6))) && (row === null || row === void 0 ? void 0 : row.emergency_request) && (row === null || row === void 0 ? void 0 : row.financial) !== \"Completed\") {\n                    formattedRow.isEmergencyAndFinanceNotComplete = true;\n                } else {\n                    formattedRow.isEmergencyAndFinanceNotComplete = false;\n                }\n                return formattedRow;\n            });\n            setAllRowData(formattedData);\n            const filteredData = formattedData.length > 0 ? formattedData === null || formattedData === void 0 ? void 0 : formattedData.filter((row)=>row.status !== \"Cancelled\" && row.status !== \"Exported\") : [];\n            setRowData(filteredData);\n            const fetchRolePermissions = async ()=>{\n                try {\n                    let serverAddress = _services_apiConfig__WEBPACK_IMPORTED_MODULE_12__.apiConfig.serverAddress;\n                    const response = await fetch(\"\".concat(serverAddress, \"suppliers/get-role-permissions\"), (0,_utils_fetchOptions__WEBPACK_IMPORTED_MODULE_20__.getFetchOptions)());\n                    if (!response.ok) {\n                        if (response.status === 401) {\n                            handle401Error();\n                            return;\n                        }\n                        throw new Error(\"Request failed with status \".concat(response.status));\n                    }\n                    const result = await response.json();\n                    const rolePermissions = {};\n                    for (const row of result){\n                        var _row_sections_split, _row_sections;\n                        const sectionsArray = row === null || row === void 0 ? void 0 : (_row_sections = row.sections) === null || _row_sections === void 0 ? void 0 : (_row_sections_split = _row_sections.split(\",\")) === null || _row_sections_split === void 0 ? void 0 : _row_sections_split.filter((section)=>(section === null || section === void 0 ? void 0 : section.trim()) !== \"\"); // Split sections string into an array and remove empty values\n                        rolePermissions[row === null || row === void 0 ? void 0 : row.role_id] = sectionsArray;\n                    }\n                    updatePermissions(rolePermissions);\n                } catch (error) {\n                    console.error(error);\n                }\n            };\n            fetchRolePermissions();\n        }).catch((error)=>{\n            console.log(error);\n        });\n    }, [\n        userData\n    ]); // Add userData as dependency\n    function getData() {\n        // Get company data from userData (session) instead of cookies\n        const company = (userData === null || userData === void 0 ? void 0 : userData.company) || extractCompanyFromEmail(userData === null || userData === void 0 ? void 0 : userData.email);\n        const ADCompanyName = (userData === null || userData === void 0 ? void 0 : userData.companyName) || (userData === null || userData === void 0 ? void 0 : userData.ADCompanyName);\n        let serverAddress = _services_apiConfig__WEBPACK_IMPORTED_MODULE_12__.apiConfig.serverAddress;\n        let prophetId = getCompanyProphetId(ADCompanyName);\n        console.log(\"prophetId\", prophetId);\n        console.log(\"company\", company);\n        console.log(\"ADCompanyName\", ADCompanyName);\n        console.log(\"userData\", userData);\n        return fetch(\"\".concat(serverAddress, \"suppliers/get-suppliers/\").concat(company, \"/\").concat(prophetId), (0,_utils_fetchOptions__WEBPACK_IMPORTED_MODULE_20__.getFetchOptions)()).then(async (res)=>{\n            if (res.status == 502) {\n                setBlockScreen(true);\n                return;\n            }\n            setBlockScreen(false);\n            if (res.status === 400) {\n                react_toastify__WEBPACK_IMPORTED_MODULE_14__.toast.error(\"There was an error with your request. Please check your data and try again.\");\n                return null;\n            } else if (res.status === 401) {\n                handle401Error();\n                return null;\n            }\n            if (res.status === 200) {\n                return res.json();\n            }\n            throw new Error(\"Failed to fetch data\");\n        }).catch((error)=>{\n            setCommonError(error.message);\n        });\n    }\n    function deleteAll() {\n        setIsLoading(true);\n        let serverAddress = _services_apiConfig__WEBPACK_IMPORTED_MODULE_12__.apiConfig.serverAddress;\n        return fetch(\"\".concat(serverAddress, \"suppliers/delete-all\"), (0,_utils_fetchOptions__WEBPACK_IMPORTED_MODULE_20__.getFetchOptions)(\"DELETE\")).then(async (res)=>{\n            if (res.status === 400) {\n                setIsLoading(false);\n                react_toastify__WEBPACK_IMPORTED_MODULE_14__.toast.error(\"There was an error with your request. Please check your data and try again.\");\n            } else if (res.status === 401) {\n                handle401Error();\n            }\n            if (res.status === 200) {\n                react_toastify__WEBPACK_IMPORTED_MODULE_14__.toast.info(\"Delete successfull\");\n                setIsLoading(false);\n                router.reload();\n            }\n            throw new Error(\"Failed to delete\");\n        }).catch((error)=>{\n            setIsLoading(false);\n            console.error(error);\n        });\n    }\n    const handlePageSizeChange = (event)=>{\n        const newPageSize = parseInt(event.target.value, 15);\n        setPageSize(newPageSize);\n        gridRef.current.api.paginationSetPageSize(newPageSize);\n    };\n    const defaultColDef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useMemo)(()=>({\n            sortable: true,\n            filter: false,\n            resizable: true,\n            flex: 1\n        }));\n    const gridOptions = {\n        responsive: true\n    };\n    const CustomCellRenderer = (params)=>{\n        const truncatedText = params === null || params === void 0 ? void 0 : params.value;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n            title: params === null || params === void 0 ? void 0 : params.value,\n            children: truncatedText\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\suppliers.js\",\n            lineNumber: 393,\n            columnNumber: 12\n        }, undefined);\n    };\n    const setStatusChange = ()=>{\n        setIsLoading(true);\n        setTimeout(function() {\n            getData().then((data)=>{\n                if (data === null) {\n                    return;\n                }\n                const formattedData = data === null || data === void 0 ? void 0 : data.map((row)=>{\n                    const roleIds = (row === null || row === void 0 ? void 0 : row.role_ids) ? JSON.parse(row === null || row === void 0 ? void 0 : row.role_ids).map((ele)=>ele.role_id) : [];\n                    const roleJson = JSON.parse(row === null || row === void 0 ? void 0 : row.role_json);\n                    const formattedRow = {\n                        isActive: row === null || row === void 0 ? void 0 : row.is_active,\n                        prophets: (row === null || row === void 0 ? void 0 : row.prophet_ids) ? JSON.parse(row === null || row === void 0 ? void 0 : row.prophet_ids) : [],\n                        supplier_code: (row === null || row === void 0 ? void 0 : row.prophet_ids) ? JSON.parse(row === null || row === void 0 ? void 0 : row.prophet_ids)[0].prophet_code : [],\n                        company_name: (row === null || row === void 0 ? void 0 : row.name) ? row === null || row === void 0 ? void 0 : row.name : \"Not Entered\",\n                        id: row === null || row === void 0 ? void 0 : row.id,\n                        currency: (row === null || row === void 0 ? void 0 : row.currency) ? row === null || row === void 0 ? void 0 : row.currency : \"Not Entered\",\n                        Compliance: row === null || row === void 0 ? void 0 : row.compliance,\n                        Financials: row === null || row === void 0 ? void 0 : row.financial,\n                        General: row === null || row === void 0 ? void 0 : row.technical,\n                        Procurement: row === null || row === void 0 ? void 0 : row.procurement,\n                        requestor: row === null || row === void 0 ? void 0 : row.requestor_name,\n                        companies: (row === null || row === void 0 ? void 0 : row.prophet_names) ? row === null || row === void 0 ? void 0 : row.prophet_names : \"Not Entered\",\n                        role: (row === null || row === void 0 ? void 0 : row.role_names) ? row === null || row === void 0 ? void 0 : row.role_names : \"Not Entered\",\n                        roleId: (row === null || row === void 0 ? void 0 : row.role_ids) ? JSON.parse(row === null || row === void 0 ? void 0 : row.role_ids) : [],\n                        roleIds: (row === null || row === void 0 ? void 0 : row.role_ids) ? JSON.parse(row === null || row === void 0 ? void 0 : row.role_ids).map((ele)=>{\n                            return ele.role_id;\n                        }) : [],\n                        country_code: row === null || row === void 0 ? void 0 : row.country_code,\n                        payment_type: row === null || row === void 0 ? void 0 : row.payment_type,\n                        payment_type_name: row === null || row === void 0 ? void 0 : row.payment_type_name,\n                        currency_name: row === null || row === void 0 ? void 0 : row.currency_name,\n                        currency_id: row === null || row === void 0 ? void 0 : row.iss_currency_id,\n                        global_gap_number: row === null || row === void 0 ? void 0 : row.global_gap_number,\n                        chile_certificate_number: row === null || row === void 0 ? void 0 : row.chile_certificate_number,\n                        red_tractor: row === null || row === void 0 ? void 0 : row.red_tractor,\n                        organic_certificate_number: row === null || row === void 0 ? void 0 : row.organic_certificate_number,\n                        puc_code: row === null || row === void 0 ? void 0 : row.puc_code,\n                        address_line_1: row === null || row === void 0 ? void 0 : row.address_line_1,\n                        address_line_2: row === null || row === void 0 ? void 0 : row.address_line_2,\n                        address_line_3: row === null || row === void 0 ? void 0 : row.address_line_3,\n                        address_line_4: row === null || row === void 0 ? void 0 : row.address_line_4,\n                        postal_code: row === null || row === void 0 ? void 0 : row.postal_code,\n                        currency_code: (row === null || row === void 0 ? void 0 : row.currency_code) ? row === null || row === void 0 ? void 0 : row.currency_code : \"\",\n                        requestor_email: row === null || row === void 0 ? void 0 : row.requestor_email,\n                        companies: (row === null || row === void 0 ? void 0 : row.prophet_names) ? row === null || row === void 0 ? void 0 : row.prophet_names : \"Not Entered\",\n                        vatable: row === null || row === void 0 ? void 0 : row.vatable,\n                        roleJson: (row === null || row === void 0 ? void 0 : row.role_json) ? JSON.parse(row === null || row === void 0 ? void 0 : row.role_json) : [],\n                        supplier_type: row === null || row === void 0 ? void 0 : row.supplier_type_label,\n                        isProducerSupplier: row === null || row === void 0 ? void 0 : row.product_supplier,\n                        isEmergencyRequest: row === null || row === void 0 ? void 0 : row.emergency_request,\n                        supplier_group: row === null || row === void 0 ? void 0 : row.sendac_groups_json,\n                        supplierLinks: row === null || row === void 0 ? void 0 : row.supplier_links_json,\n                        status: row === null || row === void 0 ? void 0 : row.label,\n                        role_num: row === null || row === void 0 ? void 0 : row.role_nums,\n                        contacts_json: row === null || row === void 0 ? void 0 : row.contacts_json,\n                        distribution_points_json: (row === null || row === void 0 ? void 0 : row.distribution_points_json) ? JSON.parse(row === null || row === void 0 ? void 0 : row.distribution_points_json) : [],\n                        vat_number: row === null || row === void 0 ? void 0 : row.vat_number,\n                        payment_terms: row === null || row === void 0 ? void 0 : row.payment_terms,\n                        sort_bic: row === null || row === void 0 ? void 0 : row.decryptedSort_Bic,\n                        name_branch: row === null || row === void 0 ? void 0 : row.decryptedName_branch,\n                        account_number: row === null || row === void 0 ? void 0 : row.decryptedAccountNumber,\n                        iss_ledger_code: row === null || row === void 0 ? void 0 : row.iss_ledger_code,\n                        internal_ledger_code: row === null || row === void 0 ? void 0 : row.internal_ledger_code,\n                        intermediatery_account_number: row === null || row === void 0 ? void 0 : row.decryptedIntermediatery_account_number,\n                        bacs_currency_code: row === null || row === void 0 ? void 0 : row.bacs_currency_code,\n                        has_iban: row === null || row === void 0 ? void 0 : row.has_iban,\n                        edi: row === null || row === void 0 ? void 0 : row.edi\n                    };\n                    if (((roleIds === null || roleIds === void 0 ? void 0 : roleIds.includes(1)) || (roleIds === null || roleIds === void 0 ? void 0 : roleIds.includes(6))) && (row === null || row === void 0 ? void 0 : row.emergency_request) && (row === null || row === void 0 ? void 0 : row.financial) !== \"Completed\") {\n                        formattedRow.isEmergencyAndFinanceNotComplete = true;\n                    } else {\n                        formattedRow.isEmergencyAndFinanceNotComplete = false;\n                    }\n                    return formattedRow;\n                });\n                setAllRowData(formattedData);\n                const filteredData = formattedData.length > 0 ? formattedData === null || formattedData === void 0 ? void 0 : formattedData.filter((row)=>row.status !== \"Completed\" && row.status !== \"Cancelled\" && row.status !== \"Exported\") : [];\n                setRowData(filteredData);\n                setIsLoading(false);\n            }).catch((error)=>{\n                console.log(error);\n                setIsLoading(false);\n            });\n        }, 3000);\n    };\n    const CustomTooltipComponent = (param)=>/*#__PURE__*/ {\n        let { value } = param;\n        return (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            title: value,\n            children: value\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\suppliers.js\",\n            lineNumber: 515,\n            columnNumber: 5\n        }, undefined);\n    };\n    const columnDefs = [\n        {\n            headerName: \"Supplier Name\",\n            field: \"company_name\",\n            checkboxSelection: (params)=>{\n                return params.data.status === \"Cancelled\" ? {\n                    checked: false,\n                    disabled: true\n                } : true;\n            },\n            cellRenderer: _utils_renderer_nameRenderer__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n            headerCheckboxSelection: true,\n            flex: \"8%\",\n            filter: true,\n            cellRendererParams: {\n                setSuppliers: setRowData,\n                setIsFiltered: setIsFiltered,\n                setIsFilteredName: setIsFilteredName\n            }\n        },\n        {\n            headerName: \"Supplier Code\",\n            cellRenderer: _components_supplierCodeRenderer__WEBPACK_IMPORTED_MODULE_19__[\"default\"],\n            field: \"supplier_code\",\n            flex: \"3%\"\n        },\n        {\n            headerName: \"Roles\",\n            field: \"role\",\n            cellRenderer: CustomCellRenderer,\n            tooltipComponent: CustomTooltipComponent,\n            flex: \"3%\"\n        },\n        {\n            headerName: \"Companies\",\n            field: \"companies\",\n            cellRenderer: CustomCellRenderer,\n            tooltipComponent: CustomTooltipComponent,\n            flex: \"4%\"\n        },\n        {\n            headerName: \"Currency\",\n            field: \"currency\",\n            flex: \"3%\",\n            cellRenderer: CustomCellRenderer,\n            tooltipComponent: CustomTooltipComponent,\n            cellStyle: (params)=>{\n                if (params.value == \"Not Entered\") {\n                    return {\n                        color: \"#B31312\"\n                    };\n                }\n                return null;\n            }\n        },\n        {\n            headerName: \"Requestor\",\n            cellRenderer: CustomCellRenderer,\n            tooltipComponent: CustomTooltipComponent,\n            field: \"requestor\",\n            flex: \"4%\"\n        },\n        {\n            headerName: \"General\",\n            cellRenderer: _utils_renderer_statusRenderer__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n            field: \"General\",\n            flex: \"3%\"\n        },\n        {\n            headerName: \"Financial\",\n            cellRenderer: _utils_renderer_statusRenderer__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n            field: \"Financials\",\n            flex: \"3%\"\n        },\n        {\n            headerName: \"Compliance\",\n            field: \"Compliance\",\n            cellRenderer: _utils_renderer_statusRenderer__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n            flex: \"4%\"\n        },\n        {\n            headerName: \"Procurement\",\n            field: \"Procurement\",\n            cellRenderer: _utils_renderer_statusRenderer__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n            flex: \"4%\"\n        },\n        {\n            headerName: \"Status\",\n            field: \"status\",\n            cellRenderer: _utils_renderer_statusRenderer__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n            cellStyle: ()=>({\n                    justifyContent: \"center\"\n                }),\n            flex: \"4%\",\n            hide: false\n        },\n        {\n            field: \"\",\n            cellRenderer: (params)=>(0,_utils_renderer_actionRenderer__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(params, userData, null, company),\n            flex: \"2%\",\n            cellStyle: ()=>({}),\n            sortable: false,\n            cellRendererParams: {\n                setUpdateStatusChange: setStatusChange\n            }\n        },\n        {\n            field: \"role_num\",\n            hide: true\n        }\n    ];\n    const onFilterTextBoxChanged = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(()=>{\n        gridRef.current.api.setQuickFilter(document.getElementById(\"filter-text-box\").value);\n    }, []);\n    const handleFilterToggle = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)((e)=>{\n        const { value } = e.target;\n        setSelectedStatus(value);\n        let filteredData = [];\n        if (value == \"default\") {\n            filteredData = allRowData.filter((row)=>row.status !== \"Cancelled\" && row.status !== \"Exported\");\n        } else {\n            filteredData = allRowData.filter((row)=>row.status === value);\n        }\n        setRowData(filteredData);\n        setExportDisabled(filteredData.length === 0);\n    }, [\n        allRowData\n    ]);\n    const exportFilteredData = async ()=>{\n        let export_ISSresponse = false;\n        let exportInternal_response = false;\n        const gridApi = gridRef.current.api;\n        let filteredData = [];\n        const isInternal = selectedExportType === \"internalExport\";\n        if (isInternal) {\n            gridApi.forEachNodeAfterFilter((node)=>{\n                var _node_data, _node_data1, _node_data2, _node_data3;\n                if (node.data !== undefined && ((_node_data = node.data) === null || _node_data === void 0 ? void 0 : _node_data.status) === \"Completed\" || ((_node_data1 = node.data) === null || _node_data1 === void 0 ? void 0 : _node_data1.status) === \"Exported\" || ((_node_data2 = node.data) === null || _node_data2 === void 0 ? void 0 : _node_data2.isEmergencyRequest) && ((_node_data3 = node.data) === null || _node_data3 === void 0 ? void 0 : _node_data3.General) === \"Complete\") {\n                    filteredData = [\n                        [\n                            {\n                                \"Supplier Active\": \"test\",\n                                \"Haulage cube local\": \"test\",\n                                \"Haulage cube name\": \"test\",\n                                \"update guesstimates type\": \"test\",\n                                \"Organization ID\": \"\",\n                                \"Enforce department\": \"\",\n                                \"Sendac Group\": \"\",\n                                \"Supplier name\": \"\",\n                                \"Supplier type\": \"\",\n                                \"User Lookup 2\": \"\",\n                                \"Address Line 1\": \"\",\n                                \"Address Line 2\": \"\",\n                                \"Address Line 3\": \"\",\n                                \"Address Line 4\": \"\",\n                                \"Post code\": \"\",\n                                \"Country code\": \"\",\n                                \"Payee supplier code\": \"\",\n                                \"Invoice supplier\": \"\",\n                                \"Head office\": \"\",\n                                \"Settlement days\": \"\",\n                                \"Bank General Ledger Code\": \"\",\n                                \"Currency number\": \"\",\n                                \"Currency number / name\": \"\",\n                                \"Bank general ledger code\": \"\",\n                                \"Payment type\": \"\",\n                                \"Country code\": \"\",\n                                Vatable: \"\",\n                                vatable: \"\",\n                                \"Update guesstimates type\": \"\",\n                                \"Area Number\": \"\",\n                                Buyer: \"\",\n                                \"Multiple lot indicator\": \"\",\n                                \"multiple lot indicator\": \"\",\n                                \"Generate Pallet Loading Plan\": \"\",\n                                \"Distribution point for supplier\": \"\",\n                                \"Payment terms\": \"\",\n                                \"Department Number\": \"\"\n                            }\n                        ]\n                    ];\n                }\n            });\n        } else {\n            gridApi.forEachNodeAfterFilter((node)=>{\n                var _node_data, _node_data1, _node_data2, _node_data3;\n                let rolesArray = node.data.roleId.map((ele)=>{\n                    return ele.role_id;\n                });\n                if (node.data !== undefined && ((_node_data = node.data) === null || _node_data === void 0 ? void 0 : _node_data.status) === \"Completed\" || ((_node_data1 = node.data) === null || _node_data1 === void 0 ? void 0 : _node_data1.status) === \"Exported\" || ((_node_data2 = node.data) === null || _node_data2 === void 0 ? void 0 : _node_data2.isEmergencyRequest) && ((_node_data3 = node.data) === null || _node_data3 === void 0 ? void 0 : _node_data3.General) === \"Complete\") {\n                    var _node_data4, _node_data_prophets__prophet_code, _node_data_prophets_, _node_data5, _node_data_company_name, _node_data6, _node_data7, _node_data8, _node_data9, _node_data10, _node_data11, _node_data12, _node_data13, _node_data_prophets__prophet_code1, _node_data_prophets_1, _node_data14, _node_data15, _node_data16, _node_data17, _node_data18, _node_data19, _node_data20, _node_data21;\n                    const filteredExportData = {\n                        \"Supplier Active\": (node === null || node === void 0 ? void 0 : (_node_data4 = node.data) === null || _node_data4 === void 0 ? void 0 : _node_data4.isActive) ? 1 : 0,\n                        \"Supplier code\": node === null || node === void 0 ? void 0 : (_node_data5 = node.data) === null || _node_data5 === void 0 ? void 0 : (_node_data_prophets_ = _node_data5.prophets[0]) === null || _node_data_prophets_ === void 0 ? void 0 : (_node_data_prophets__prophet_code = _node_data_prophets_.prophet_code) === null || _node_data_prophets__prophet_code === void 0 ? void 0 : _node_data_prophets__prophet_code.trim(),\n                        \"EDI Partner\": \"\",\n                        \"Supplier name\": node === null || node === void 0 ? void 0 : (_node_data6 = node.data) === null || _node_data6 === void 0 ? void 0 : (_node_data_company_name = _node_data6.company_name) === null || _node_data_company_name === void 0 ? void 0 : _node_data_company_name.trim(),\n                        \"Country Code\": node === null || node === void 0 ? void 0 : (_node_data7 = node.data) === null || _node_data7 === void 0 ? void 0 : _node_data7.country_code,\n                        \"Distribution Point for Supplier\": 6,\n                        \"Bank Ledger Code\": node === null || node === void 0 ? void 0 : (_node_data8 = node.data) === null || _node_data8 === void 0 ? void 0 : _node_data8.currency_id,\n                        \"Area Number\": 170,\n                        Vatable: 0,\n                        Buyer: 1,\n                        \"Billing type\": 0,\n                        \"Payment type\": node === null || node === void 0 ? void 0 : (_node_data9 = node.data) === null || _node_data9 === void 0 ? void 0 : _node_data9.payment_type,\n                        \"Currency number\": node === null || node === void 0 ? void 0 : (_node_data10 = node.data) === null || _node_data10 === void 0 ? void 0 : _node_data10.currency_id,\n                        GGN: node === null || node === void 0 ? void 0 : (_node_data11 = node.data) === null || _node_data11 === void 0 ? void 0 : _node_data11.global_gap_number,\n                        \"Organic cert\": node === null || node === void 0 ? void 0 : (_node_data12 = node.data) === null || _node_data12 === void 0 ? void 0 : _node_data12.organic_certificate_number,\n                        \"Regional cert\": node === null || node === void 0 ? void 0 : (_node_data13 = node.data) === null || _node_data13 === void 0 ? void 0 : _node_data13.chile_certificate_number,\n                        \"Head office\": node === null || node === void 0 ? void 0 : (_node_data14 = node.data) === null || _node_data14 === void 0 ? void 0 : (_node_data_prophets_1 = _node_data14.prophets[0]) === null || _node_data_prophets_1 === void 0 ? void 0 : (_node_data_prophets__prophet_code1 = _node_data_prophets_1.prophet_code) === null || _node_data_prophets__prophet_code1 === void 0 ? void 0 : _node_data_prophets__prophet_code1.trim(),\n                        \"Address line 1\": node === null || node === void 0 ? void 0 : (_node_data15 = node.data) === null || _node_data15 === void 0 ? void 0 : _node_data15.address_line_1,\n                        \"Address line 2\": node === null || node === void 0 ? void 0 : (_node_data16 = node.data) === null || _node_data16 === void 0 ? void 0 : _node_data16.address_line_2,\n                        \"Address line 3\": node === null || node === void 0 ? void 0 : (_node_data17 = node.data) === null || _node_data17 === void 0 ? void 0 : _node_data17.address_line_3,\n                        \"Address line 4\": node === null || node === void 0 ? void 0 : (_node_data18 = node.data) === null || _node_data18 === void 0 ? void 0 : _node_data18.address_line_4,\n                        \"Postal code\": node === null || node === void 0 ? void 0 : (_node_data19 = node.data) === null || _node_data19 === void 0 ? void 0 : _node_data19.postal_code,\n                        status: node === null || node === void 0 ? void 0 : (_node_data20 = node.data) === null || _node_data20 === void 0 ? void 0 : _node_data20.status,\n                        id: node === null || node === void 0 ? void 0 : (_node_data21 = node.data) === null || _node_data21 === void 0 ? void 0 : _node_data21.id\n                    };\n                    filteredData.push(filteredExportData);\n                }\n            });\n        }\n        if (filteredData.length === 0) {\n            react_toastify__WEBPACK_IMPORTED_MODULE_14__.toast.error(\"No filtered data to export.\", {\n                position: \"top-right\",\n                autoClose: 5000,\n                hideProgressBar: false,\n                closeOnClick: true,\n                pauseOnHover: false,\n                draggable: true,\n                progress: undefined,\n                theme: \"light\"\n            });\n        } else {\n            if (supplierCheckedValue) {\n                if (true) {\n                    const allStatesData = [\n                        ulpFilData,\n                        supplierActiveData,\n                        roleData,\n                        sendacGroupData,\n                        bankAc,\n                        senBnk,\n                        contactData,\n                        organizationData,\n                        organizationRoleData,\n                        sheetSupplierId\n                    ];\n                    exportInternal_response = await (0,_utils_exportExcel__WEBPACK_IMPORTED_MODULE_18__[\"default\"])(allStatesData, true, null, company, userData, prophetId, userData === null || userData === void 0 ? void 0 : userData.email, \"\");\n                }\n                if (!isUnExportable) {\n                    const allStatesData = [\n                        multipleFilterISSData,\n                        roleData\n                    ];\n                    export_ISSresponse = await (0,_utils_exportExcel__WEBPACK_IMPORTED_MODULE_18__[\"default\"])(allStatesData, false, null, company, userData, prophetId, userData === null || userData === void 0 ? void 0 : userData.email, \"\");\n                }\n                setEmailStatusPopup(true);\n                if (export_ISSresponse && exportInternal_response) {\n                    setPopUpMessage(\"Email sent to Finance Department and ISS admin\");\n                    setISSExportSuccess(true);\n                    setInternalExportSuccess(true);\n                } else if (exportInternal_response && isUnExportable) {\n                    setPopUpMessage(\"Email sent to Finance Department, but suppliers \".concat(unExportableSuppliernames, \" not exported as Hauliers and Expense roles not allowed to be exported to ISS\"));\n                    setInternalExportSuccess(true);\n                } else if (export_ISSresponse) {\n                    setISSExportSuccess(true);\n                    setPopUpMessage(\"Email sent to ISS Admin Team, but not to Finance Department\");\n                } else {\n                    setPopUpMessage(\"Email not sent to either Finance Department or ISS Admin Team\");\n                }\n            }\n        }\n        setSelectedExportType(\"\");\n        gridRef.current.api.deselectAll();\n    };\n    // ... (continuing with the rest of the state variables and functions)\n    const [supplierActiveData, setSupplierActiveData] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([\n        [\n            \"sendac (Supplier file)\"\n        ]\n    ]);\n    const [roleData, setRoleData] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([\n        [\n            \"sendacrole (Supplier role file)\"\n        ]\n    ]);\n    const [contactData, setContactData] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([\n        [\n            \"contactdet (Supplier personnel contact details)\"\n        ]\n    ]);\n    const [organizationData, setOrganizationData] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([\n        [\n            \"organization (Organization)\"\n        ]\n    ]);\n    const [organizationRoleData, setOrganizationRoleData] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([\n        [\n            \"orgroles (Organization Roles)\"\n        ]\n    ]);\n    const [sendacGroupData, setSendacGroupData] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([\n        [\n            \"sendacgroup (Sendac group file)\"\n        ]\n    ]);\n    const [bankAc, setBankAc] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([\n        [\n            \"bankac (Bank account details table)\"\n        ]\n    ]);\n    const [multipleFilterISSData, setMultipleFilterISSData] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([\n        [\n            \"Supplier Data\"\n        ]\n    ]);\n    const [senBnk, setSenBnk] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([\n        [\n            \"senbnk (Supplier bank link table)\"\n        ]\n    ]);\n    const [ulpFilData, setUlpFilData] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([\n        [\n            \"UlpFil\"\n        ]\n    ]);\n    const [sheetSupplierId, setSheetSupplierId] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([\n        [\n            \"Supplier Id\"\n        ]\n    ]);\n    const handleGridReady = (params)=>{\n        params.api.setColumnDefs(columnDefs);\n    };\n    const extractContacts = (supplierCode, contactsJsonStr, supplierName)=>{\n        try {\n            const contacts = contactsJsonStr ? JSON.parse(contactsJsonStr) : [];\n            if (Array.isArray(contacts)) {\n                return contacts.map((contact)=>({\n                        \"Supplier code\": supplierCode ? supplierCode : \"\",\n                        \"Contact ID\": \"\",\n                        Name: supplierName || \"\",\n                        \"Email Address\": contact.email_id || \"\",\n                        \"Telephone number\": contact.telephone || \"\",\n                        \"Cell phone number\": \"\",\n                        \"Fax number\": \"\",\n                        \"Instant Message\": \"\",\n                        \"Physical Address\": \"\",\n                        \"Postal Address\": \"\",\n                        \"Row verision\": \"\",\n                        \"Created timestamp\": \"\"\n                    }));\n            }\n        } catch (error) {\n            console.error(\"Error parsing contacts_json:\", error);\n        }\n        return [\n            {\n                \"Contact ID\": \"\",\n                Name: supplierName || \"\",\n                \"Email Address\": \"\",\n                \"Telephone number\": \"\",\n                \"Cell phone number\": \"\",\n                \"Fax number\": \"\",\n                \"Instant Message\": \"\",\n                \"Physical Address\": \"\",\n                \"Postal Address\": \"\",\n                \"Row verision\": \"\",\n                \"Created timestamp\": \"\"\n            }\n        ];\n    };\n    const extractSendacGroup = (sendacGroupJson)=>{\n        try {\n            const sendacGroups = sendacGroupJson ? JSON.parse(sendacGroupJson) : [];\n            if (Array.isArray(sendacGroups)) {\n                return sendacGroups === null || sendacGroups === void 0 ? void 0 : sendacGroups.map((group)=>({\n                        \"Supplier group\": \"\",\n                        Description: (group === null || group === void 0 ? void 0 : group.created_by) ? group === null || group === void 0 ? void 0 : group.label : \"\"\n                    }));\n            }\n        } catch (error) {\n            console.error(\"Error parsing contacts_json:\", error);\n        }\n    };\n    const multipleSendRoleOnRoleNums = (data, role_num)=>{\n        var _data_role;\n        const roleNums = role_num === null || role_num === void 0 ? void 0 : role_num.split(\",\").map((num)=>num.trim());\n        const roleNames = data === null || data === void 0 ? void 0 : (_data_role = data.role) === null || _data_role === void 0 ? void 0 : _data_role.split(\",\").map((name)=>name.trim());\n        return roleNums.map((num, index)=>{\n            var _data_prophets_, _data_prophets_1;\n            return {\n                Sendacroleid: \"\",\n                \"Supplier code\": (data === null || data === void 0 ? void 0 : (_data_prophets_ = data.prophets[0]) === null || _data_prophets_ === void 0 ? void 0 : _data_prophets_.prophet_code) ? data.prophets[0].prophet_code.trim() : \"\",\n                Description: (data === null || data === void 0 ? void 0 : (_data_prophets_1 = data.prophets[0]) === null || _data_prophets_1 === void 0 ? void 0 : _data_prophets_1.prophet_code) ? data.prophets[0].prophet_code.trim() : \"\",\n                \"Supplier Code Supplier Name\": data.company_name,\n                Type: num,\n                \"Type Description\": roleNames[index],\n                \"Supplier code Global gap number\": data === null || data === void 0 ? void 0 : data.global_gap_number,\n                \"Created timestamp\": \"\",\n                Active: 1\n            };\n        });\n    };\n    function getGLCode(internal_ledger_code) {\n        if (internal_ledger_code) {\n            return internal_ledger_code;\n        } else return \"\";\n    }\n    const [incompleteToast, setIncompleteToast] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        if (incompleteToast) {\n            react_toastify__WEBPACK_IMPORTED_MODULE_14__.toast.error(\"Supplier details are incomplete.\", {\n                position: \"top-right\",\n                autoClose: 1000,\n                hideProgressBar: false,\n                closeOnClick: true,\n                pauseOnHover: false,\n                draggable: true,\n                progress: undefined,\n                theme: \"light\"\n            });\n        }\n    }, [\n        incompleteToast\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        if (containsCancelledSupplier) {\n            react_toastify__WEBPACK_IMPORTED_MODULE_14__.toast.error(\"Cannot export cancelled supplier.\", {\n                position: \"top-right\",\n                autoClose: 1000,\n                hideProgressBar: false,\n                closeOnClick: true,\n                pauseOnHover: false,\n                draggable: true,\n                progress: undefined,\n                theme: \"light\"\n            });\n        }\n    }, [\n        containsCancelledSupplier\n    ]);\n    const handleCheckboxEvent = (event)=>{\n        const getRowData = event.data;\n        const isSelected = event.node.selected;\n        const selectedRows = gridRef.current.api.getSelectedRows();\n        const prophet_id = getRowData.prophets[0].prophet_id;\n        setProphetId(prophet_id);\n        const extractedValues = selectedRows.map((param)=>{\n            let { status, isEmergencyRequest, General } = param;\n            return {\n                status,\n                isEmergencyRequest,\n                General\n            };\n        });\n        const exportDisabled = extractedValues.some((param)=>{\n            let { status, isEmergencyRequest, General } = param;\n            return !(status === \"Completed\" || status === \"Exported\" || isEmergencyRequest && General === \"Complete\");\n        });\n        const canExport = extractedValues.every((param)=>{\n            let { isEmergencyRequest } = param;\n            return !(!isEmergencyRequest && ((userData === null || userData === void 0 ? void 0 : userData.role_id) != 1 || (userData === null || userData === void 0 ? void 0 : userData.role_id) != 2 || (userData === null || userData === void 0 ? void 0 : userData.role_id) != 5 || (userData === null || userData === void 0 ? void 0 : userData.role_id) != 6));\n        });\n        const isExportableBasedOnCodeUnique = selectedRows.every((row)=>{\n            var _row_prophets_, _row_prophets_1;\n            const codeCount = row === null || row === void 0 ? void 0 : (_row_prophets_ = row.prophets[0]) === null || _row_prophets_ === void 0 ? void 0 : _row_prophets_.code_count;\n            const prophetCode = row === null || row === void 0 ? void 0 : (_row_prophets_1 = row.prophets[0]) === null || _row_prophets_1 === void 0 ? void 0 : _row_prophets_1.prophet_code;\n            if (codeCount && codeCount > 1 && prophetCode && prophetCode !== \"\") {\n                return false;\n            } else if (codeCount && codeCount == 1 && prophetCode && prophetCode !== \"\") {\n                return true;\n            }\n            return false;\n        });\n        const doesContainCancelledSupplier = selectedRows.some((row)=>row.status === \"Cancelled\");\n        const isExportValid = selectedRows.every((row)=>{\n            var _row_prophets_, _row_prophets_1, _row_roleIds, _row_roleIds1;\n            const supCode = row === null || row === void 0 ? void 0 : (_row_prophets_ = row.prophets[0]) === null || _row_prophets_ === void 0 ? void 0 : _row_prophets_.prophet_code;\n            const prophet_id = row === null || row === void 0 ? void 0 : (_row_prophets_1 = row.prophets[0]) === null || _row_prophets_1 === void 0 ? void 0 : _row_prophets_1.prophet_id;\n            const isSupplierAccount = (row === null || row === void 0 ? void 0 : (_row_roleIds = row.roleIds) === null || _row_roleIds === void 0 ? void 0 : _row_roleIds.includes(1)) || (row === null || row === void 0 ? void 0 : (_row_roleIds1 = row.roleIds) === null || _row_roleIds1 === void 0 ? void 0 : _row_roleIds1.includes(6));\n            let currency = (row === null || row === void 0 ? void 0 : row.currency) == \"$\" ? \"\\\\\".concat(row === null || row === void 0 ? void 0 : row.currency) : row === null || row === void 0 ? void 0 : row.currency;\n            let actualCurr;\n            if (currency && currency == \"Not Entered\") {\n                actualCurr = \"\";\n            } else {\n                actualCurr = currency;\n            }\n            let isValid = true;\n            if (isSupplierAccount) {\n                if (prophet_id == 1) {\n                    let regexPattern;\n                    regexPattern = new RegExp(\"^[A-Z0-9]{4}[A-Z0145678]\".concat(actualCurr, \"$\"));\n                    isValid = regexPattern.test(supCode);\n                } else if (prophet_id == 2) {\n                    let regexPattern;\n                    regexPattern = new RegExp(\"^([A-Z0]{4})9\".concat(actualCurr, \"$\"));\n                    isValid = regexPattern.test(supCode);\n                } else if (prophet_id == 3) {\n                    let regexPattern = new RegExp(\"^[A-Z0-9]{4}[A-Z01345678][A-Z0-9]*$\");\n                    isValid = regexPattern.test(supCode) && supCode.length == 6;\n                } else if (prophet_id == 4) {\n                    let regexPattern;\n                    regexPattern = new RegExp(\"^([A-Z0]{4})2\".concat(actualCurr, \"$\"));\n                    isValid = regexPattern.test(supCode);\n                }\n            }\n            return isValid;\n        });\n        if (selectedRows.length > 0) {\n            if (!canExport && (userData === null || userData === void 0 ? void 0 : userData.role_id) != 1 && (userData === null || userData === void 0 ? void 0 : userData.role_id) != 2 && (userData === null || userData === void 0 ? void 0 : userData.role_id) != 5 && (userData === null || userData === void 0 ? void 0 : userData.role_id) != 6) {\n                setExportDisabled(true);\n            } else if (doesContainCancelledSupplier) {\n                setContainsCancelledSupplier(true);\n                setExportDisabled(true);\n            } else if (!isExportableBasedOnCodeUnique) {\n                setSupplierUniqueCodeToast(true);\n                setExportDisabled(true);\n            } else if (!isExportValid) {\n                setSupplierCodeValid(false);\n                setExportDisabled(true);\n            } else {\n                setExportDisabled(exportDisabled);\n            }\n        } else {\n            setExportDisabled(true);\n        }\n        let isUnExportableToISS = false;\n        let supplierNames = [];\n        selectedRows.forEach((row)=>{\n            var _row_roleIds, _row_roleIds1, _row_roleIds2, _row_roleIds3;\n            if (!(row === null || row === void 0 ? void 0 : (_row_roleIds = row.roleIds) === null || _row_roleIds === void 0 ? void 0 : _row_roleIds.includes(1)) && !(row === null || row === void 0 ? void 0 : (_row_roleIds1 = row.roleIds) === null || _row_roleIds1 === void 0 ? void 0 : _row_roleIds1.includes(2)) && !(row === null || row === void 0 ? void 0 : (_row_roleIds2 = row.roleIds) === null || _row_roleIds2 === void 0 ? void 0 : _row_roleIds2.includes(3)) && !(row === null || row === void 0 ? void 0 : (_row_roleIds3 = row.roleIds) === null || _row_roleIds3 === void 0 ? void 0 : _row_roleIds3.includes(4))) {\n                isUnExportableToISS = true;\n                supplierNames.push(row.company_name);\n            }\n        });\n        const supplierNamesString = supplierNames.join(\", \");\n        setIsUnExportable(isUnExportableToISS);\n        setUnExportableSupplierNames(supplierNamesString);\n        if ((getRowData === null || getRowData === void 0 ? void 0 : getRowData.status) == \"Completed\" || (getRowData === null || getRowData === void 0 ? void 0 : getRowData.status) == \"Exported\" || (getRowData === null || getRowData === void 0 ? void 0 : getRowData.isEmergencyRequest) && getRowData.status != \"Cancelled\" && (getRowData === null || getRowData === void 0 ? void 0 : getRowData.General) === \"Complete\") {\n            var _getRowData_roleIds, _getRowData_roleIds1, _getRowData_roleIds2, _getRowData_roleIds3, _getRowData_prophets__prophet_code, _getRowData_prophets_, _getRowData_company_name, _getRowData_prophets__prophet_code1, _getRowData_prophets_1, _getRowData_distribution_points_json, _getRowData_prophets_2, _getRowData_prophets_3, _getRowData_prophets__prophet_code2, _getRowData_prophets_4, _getRowData_prophets_5, _getRowData_prophets_6, _getRowData_distribution_points_json1, _getRowData_distribution_points_json2, _getRowData_prophets__prophet_code3, _getRowData_prophets_7, _getRowData_prophets__prophet_code4, _getRowData_prophets_8, _getRowData_prophets_9, _getRowData_prophets__prophet_code5, _getRowData_prophets_10, _getRowData_prophets_11, _getRowData_prophets__prophet_code6, _getRowData_prophets_12;\n            let regional_cert = \"\";\n            if ((getRowData === null || getRowData === void 0 ? void 0 : (_getRowData_roleIds = getRowData.roleIds) === null || _getRowData_roleIds === void 0 ? void 0 : _getRowData_roleIds.includes(2)) || (getRowData === null || getRowData === void 0 ? void 0 : (_getRowData_roleIds1 = getRowData.roleIds) === null || _getRowData_roleIds1 === void 0 ? void 0 : _getRowData_roleIds1.includes(3))) {\n                if (getRowData.country_code == \"UK\") {\n                    regional_cert = getRowData === null || getRowData === void 0 ? void 0 : getRowData.red_tractor;\n                } else if (getRowData.country_code == \"ZA\") {\n                    regional_cert = getRowData === null || getRowData === void 0 ? void 0 : getRowData.puc_code;\n                } else if (getRowData.country_code == \"CL\") {\n                    regional_cert = getRowData === null || getRowData === void 0 ? void 0 : getRowData.chile_certificate_number;\n                }\n            }\n            let currencyId = \"\";\n            let currencyName = \"\";\n            if ((getRowData === null || getRowData === void 0 ? void 0 : (_getRowData_roleIds2 = getRowData.roleIds) === null || _getRowData_roleIds2 === void 0 ? void 0 : _getRowData_roleIds2.includes(1)) || (getRowData === null || getRowData === void 0 ? void 0 : (_getRowData_roleIds3 = getRowData.roleIds) === null || _getRowData_roleIds3 === void 0 ? void 0 : _getRowData_roleIds3.includes(6))) {\n                currencyId = getRowData === null || getRowData === void 0 ? void 0 : getRowData.currency_id;\n                currencyName = getRowData === null || getRowData === void 0 ? void 0 : getRowData.currency_name;\n            } else {\n                currencyId = 1;\n                currencyName = \"Sterling\";\n            }\n            function getCorrespondingUserLookup(curr) {\n                if (curr == \"GBP\") {\n                    return \"GBPBACS\";\n                } else if (curr == \"EUR\") {\n                    return \"EUROSEPA\";\n                } else if (curr == \"USD\") {\n                    return \"USDPRIORITY\";\n                } else {\n                    return \"\";\n                }\n            }\n            var _getRowData_edi;\n            const filteredISSExportData = {\n                \"Supplier Active\": \"N/A\",\n                \"Supplier code\": getRowData === null || getRowData === void 0 ? void 0 : (_getRowData_prophets_ = getRowData.prophets[0]) === null || _getRowData_prophets_ === void 0 ? void 0 : (_getRowData_prophets__prophet_code = _getRowData_prophets_.prophet_code) === null || _getRowData_prophets__prophet_code === void 0 ? void 0 : _getRowData_prophets__prophet_code.trim(),\n                \"EDI Partner\": \"N/A\",\n                \"Supplier name\": getRowData === null || getRowData === void 0 ? void 0 : (_getRowData_company_name = getRowData.company_name) === null || _getRowData_company_name === void 0 ? void 0 : _getRowData_company_name.trim(),\n                \"EDI ANA number\": (_getRowData_edi = getRowData === null || getRowData === void 0 ? void 0 : getRowData.edi) !== null && _getRowData_edi !== void 0 ? _getRowData_edi : \"N/A\",\n                \"Producer (supplier)\": \"N/A\",\n                \"Department number\": \"N/A\",\n                \"Currency number\": (getRowData === null || getRowData === void 0 ? void 0 : getRowData.currency_id) ? getRowData === null || getRowData === void 0 ? void 0 : getRowData.currency_id : 1,\n                \"Global gap number\": getRowData === null || getRowData === void 0 ? void 0 : getRowData.global_gap_number,\n                \"Grower group\": \"N/A\",\n                \"Defra county number\": \"N/A\",\n                \"Date start\": \"N/A\",\n                \"Date end\": \"N/A\",\n                \"Organic Cert\": getRowData === null || getRowData === void 0 ? void 0 : getRowData.organic_certificate_number,\n                \"Regional Cert\": regional_cert,\n                \"Head office\": getRowData === null || getRowData === void 0 ? void 0 : (_getRowData_prophets_1 = getRowData.prophets[0]) === null || _getRowData_prophets_1 === void 0 ? void 0 : (_getRowData_prophets__prophet_code1 = _getRowData_prophets_1.prophet_code) === null || _getRowData_prophets__prophet_code1 === void 0 ? void 0 : _getRowData_prophets__prophet_code1.trim(),\n                \"Country Code\": getRowData === null || getRowData === void 0 ? void 0 : getRowData.country_code,\n                \"Distribution point for supplier\": (getRowData === null || getRowData === void 0 ? void 0 : (_getRowData_distribution_points_json = getRowData.distribution_points_json) === null || _getRowData_distribution_points_json === void 0 ? void 0 : _getRowData_distribution_points_json.length) > 0 ? getRowData === null || getRowData === void 0 ? void 0 : getRowData.distribution_points_json[0].from_dp : \"N/A\",\n                \"Bool 2\": \"N/A\",\n                \"Bool 3\": \"N/A\",\n                \"Address line 1\": getRowData === null || getRowData === void 0 ? void 0 : getRowData.address_line_1,\n                \"Address line 2\": getRowData === null || getRowData === void 0 ? void 0 : getRowData.address_line_2,\n                \"Address line 3\": getRowData === null || getRowData === void 0 ? void 0 : getRowData.address_line_3,\n                \"Address line 4\": getRowData === null || getRowData === void 0 ? void 0 : getRowData.address_line_4,\n                \"Post code\": getRowData === null || getRowData === void 0 ? void 0 : getRowData.postal_code,\n                \"Currency Number\": (getRowData === null || getRowData === void 0 ? void 0 : getRowData.currency_id) ? getRowData === null || getRowData === void 0 ? void 0 : getRowData.currency_id : 1,\n                \"Bank general ledger code\": (getRowData === null || getRowData === void 0 ? void 0 : getRowData.iss_ledger_code) ? getRowData === null || getRowData === void 0 ? void 0 : getRowData.iss_ledger_code : \"12200\",\n                \"Bank general ledger code Currency number if bank\": (getRowData === null || getRowData === void 0 ? void 0 : getRowData.currency_id) ? getRowData === null || getRowData === void 0 ? void 0 : getRowData.currency_id : 1,\n                \"Settlement days\": getRowData === null || getRowData === void 0 ? void 0 : getRowData.payment_terms,\n                \"Department Number\": (getRowData === null || getRowData === void 0 ? void 0 : (_getRowData_prophets_2 = getRowData.prophets[0]) === null || _getRowData_prophets_2 === void 0 ? void 0 : _getRowData_prophets_2.prophet_id) == 1 ? 1 : (getRowData === null || getRowData === void 0 ? void 0 : (_getRowData_prophets_3 = getRowData.prophets[0]) === null || _getRowData_prophets_3 === void 0 ? void 0 : _getRowData_prophets_3.prophet_id) == 2 ? 9 : (getRowData === null || getRowData === void 0 ? void 0 : getRowData.prophets[0].prophet_id) == 3 ? 3 : (getRowData === null || getRowData === void 0 ? void 0 : getRowData.prophets[0].prophet_id) == 4 ? 2 : \"N/A\",\n                \"Area Number\": \"1\",\n                Vatable: (getRowData === null || getRowData === void 0 ? void 0 : getRowData.vatable) != null ? (getRowData === null || getRowData === void 0 ? void 0 : getRowData.vatable) ? \"1\" : \"0\" : \"0\",\n                Buyer: \"1\",\n                \"Billing type\": \"0\",\n                \"Payment type\": (getRowData === null || getRowData === void 0 ? void 0 : getRowData.payment_type) ? getRowData === null || getRowData === void 0 ? void 0 : getRowData.payment_type : 2,\n                \"Expense general ledger code\": \"N/A\",\n                \"Authorise on register\": \"N/A\",\n                \"Use % authorise rule\": 5,\n                \"User text 1\": \"N/A\",\n                \"Mandatory altfil on service jobs\": \"N/A\",\n                \"Organization ID\": \"N/A\",\n                id: getRowData === null || getRowData === void 0 ? void 0 : getRowData.id,\n                isEmergencyAndFinanceNotComplete: getRowData === null || getRowData === void 0 ? void 0 : getRowData.isEmergencyAndFinanceNotComplete\n            };\n            var _getRowData_edi1;\n            const newSupplierActiveData = {\n                \"Supplier Active\": (getRowData === null || getRowData === void 0 ? void 0 : getRowData.isActive) ? 1 : 0,\n                \"Haulage cube local\": \"\",\n                \"Haulage cube name\": \"\",\n                \"update guesstimates type\": 1,\n                \"Organization ID\": \"\",\n                \"Vat number 1\": getRowData === null || getRowData === void 0 ? void 0 : getRowData.vat_number,\n                \"Organic cert\": getRowData === null || getRowData === void 0 ? void 0 : getRowData.organic_certificate_number,\n                \"Regional cert\": regional_cert,\n                \"Global gap number\": getRowData === null || getRowData === void 0 ? void 0 : getRowData.global_gap_number,\n                \"Enforce department\": \"\",\n                \"Sendac Group\": (getRowData === null || getRowData === void 0 ? void 0 : getRowData.supplier_group) ? JSON.parse(getRowData === null || getRowData === void 0 ? void 0 : getRowData.supplier_group)[0].value : \"\",\n                \"Supplier code\": getRowData === null || getRowData === void 0 ? void 0 : (_getRowData_prophets_4 = getRowData.prophets[0]) === null || _getRowData_prophets_4 === void 0 ? void 0 : (_getRowData_prophets__prophet_code2 = _getRowData_prophets_4.prophet_code) === null || _getRowData_prophets__prophet_code2 === void 0 ? void 0 : _getRowData_prophets__prophet_code2.trim(),\n                \"Supplier name\": getRowData.company_name,\n                \"Supplier type\": getRowData === null || getRowData === void 0 ? void 0 : getRowData.supplier_type,\n                \"User Lookup 2\": \"\",\n                \"Address Line 1\": getRowData === null || getRowData === void 0 ? void 0 : getRowData.address_line_1,\n                \"Address Line 2\": getRowData === null || getRowData === void 0 ? void 0 : getRowData.address_line_2,\n                \"Address Line 3\": getRowData === null || getRowData === void 0 ? void 0 : getRowData.address_line_3,\n                \"Address Line 4\": getRowData === null || getRowData === void 0 ? void 0 : getRowData.address_line_4,\n                \"Post code\": getRowData === null || getRowData === void 0 ? void 0 : getRowData.postal_code,\n                \"Country code\": getRowData === null || getRowData === void 0 ? void 0 : getRowData.country_code,\n                \"Payee supplier code\": \"\",\n                \"Invoice supplier\": \"\",\n                \"Head office\": \"\",\n                \"Settlement days\": getRowData === null || getRowData === void 0 ? void 0 : getRowData.payment_terms,\n                \"Bank general ledger code Currency number if bank\": currencyId,\n                \"Currency number\": currencyId,\n                \"Currency number Currency name\": currencyName,\n                \"Bank general ledger code\": getGLCode(getRowData === null || getRowData === void 0 ? void 0 : getRowData.internal_ledger_code),\n                \"payment type\": getRowData === null || getRowData === void 0 ? void 0 : getRowData.payment_type,\n                \"Payment type name\": getRowData === null || getRowData === void 0 ? void 0 : getRowData.payment_type_name,\n                \"country code\": getRowData === null || getRowData === void 0 ? void 0 : getRowData.country_code,\n                Vatable: (getRowData === null || getRowData === void 0 ? void 0 : getRowData.vatable) != null ? (getRowData === null || getRowData === void 0 ? void 0 : getRowData.vatable) ? \"1\" : \"0\" : \"0\",\n                \"vatable desc\": (getRowData === null || getRowData === void 0 ? void 0 : getRowData.vatable) != null ? (getRowData === null || getRowData === void 0 ? void 0 : getRowData.vatable) ? \"Vatable\" : \"None vatable\" : \"None vatable\",\n                \"Area Number\": (getRowData === null || getRowData === void 0 ? void 0 : getRowData.prophets[0].prophet_id) == 3 || (getRowData === null || getRowData === void 0 ? void 0 : getRowData.prophets[0].prophet_id) == 4 ? 1 : 7,\n                Buyer: 1,\n                \"Multiple lot indicator\": \"0\",\n                \"multiple lot indicator desc\": \"By Lot\",\n                \"Generate Pallet Loading Plan\": \"\",\n                \"Distribution point for supplier\": 6,\n                \"Payment terms\": \"\",\n                \"Department Number\": (getRowData === null || getRowData === void 0 ? void 0 : (_getRowData_prophets_5 = getRowData.prophets[0]) === null || _getRowData_prophets_5 === void 0 ? void 0 : _getRowData_prophets_5.prophet_id) == 1 ? 1 : (getRowData === null || getRowData === void 0 ? void 0 : (_getRowData_prophets_6 = getRowData.prophets[0]) === null || _getRowData_prophets_6 === void 0 ? void 0 : _getRowData_prophets_6.prophet_id) == 2 ? 9 : (getRowData === null || getRowData === void 0 ? void 0 : getRowData.prophets[0].prophet_id) == 3 ? 3 : (getRowData === null || getRowData === void 0 ? void 0 : getRowData.prophets[0].prophet_id) == 4 ? 2 : \"\",\n                \"Allow credit rebates\": \"\",\n                \"Alternative DP for supplier\": 1,\n                \"Actual posting stops purchase charges\": \"\",\n                \"Authorise on register\": \"\",\n                \"User text 1\": \"\",\n                \"User lookup 1\": (getRowData === null || getRowData === void 0 ? void 0 : getRowData.prophets[0].prophet_id) == 3 || (getRowData === null || getRowData === void 0 ? void 0 : getRowData.prophets[0].prophet_id) == 4 ? getCorrespondingUserLookup(getRowData === null || getRowData === void 0 ? void 0 : getRowData.currency_code) : \"\",\n                \"Receive orders from edi\": \"\",\n                \"Send invoices from edi\": \"\",\n                \"Send orders from edi\": \"\",\n                \"EDI partner\": (getRowData === null || getRowData === void 0 ? void 0 : getRowData.prophets[0].prophet_id) == 3 || (getRowData === null || getRowData === void 0 ? void 0 : getRowData.prophets[0].prophet_id) == 4 ? 2000 : \"\",\n                \"Generic code\": (getRowData === null || getRowData === void 0 ? void 0 : getRowData.prophets[0].prophet_id) == 3 || (getRowData === null || getRowData === void 0 ? void 0 : getRowData.prophets[0].prophet_id) == 4 ? \"STOCK\" : \"\",\n                \"EDI ANA number\": (_getRowData_edi1 = getRowData === null || getRowData === void 0 ? void 0 : getRowData.edi) !== null && _getRowData_edi1 !== void 0 ? _getRowData_edi1 : \"N/A\",\n                \"User % authorize rule\": 5,\n                FromDP: (getRowData === null || getRowData === void 0 ? void 0 : (_getRowData_distribution_points_json1 = getRowData.distribution_points_json) === null || _getRowData_distribution_points_json1 === void 0 ? void 0 : _getRowData_distribution_points_json1.length) > 0 ? getRowData.distribution_points_json[0].from_dp || \"\" : \"\"\n            };\n            let UlpFil = {};\n            if ((getRowData === null || getRowData === void 0 ? void 0 : (_getRowData_distribution_points_json2 = getRowData.distribution_points_json) === null || _getRowData_distribution_points_json2 === void 0 ? void 0 : _getRowData_distribution_points_json2.length) > 0 && ((getRowData === null || getRowData === void 0 ? void 0 : getRowData.distribution_points_json[0].from_dp) === null || (getRowData === null || getRowData === void 0 ? void 0 : getRowData.distribution_points_json[0].from_dp) === undefined)) {\n                var _getRowData_distribution_points_json3, _getRowData_prophets__prophet_code7, _getRowData_prophets_13, _getRowData_distribution_points_json4, _getRowData_distribution_points_json_;\n                UlpFil = {\n                    \"Distribution point\": \"\",\n                    Description: (getRowData === null || getRowData === void 0 ? void 0 : (_getRowData_distribution_points_json3 = getRowData.distribution_points_json) === null || _getRowData_distribution_points_json3 === void 0 ? void 0 : _getRowData_distribution_points_json3.length) > 0 && ((getRowData === null || getRowData === void 0 ? void 0 : getRowData.distribution_points_json[0].from_dp) === null || (getRowData === null || getRowData === void 0 ? void 0 : getRowData.distribution_points_json[0].from_dp) === undefined) ? getRowData === null || getRowData === void 0 ? void 0 : getRowData.distribution_points_json[0].name : \"\",\n                    \"Service Supplier Code\": ((getRowData === null || getRowData === void 0 ? void 0 : getRowData.distribution_points_json[0].from_dp) === null || (getRowData === null || getRowData === void 0 ? void 0 : getRowData.distribution_points_json[0].from_dp) === undefined) && (getRowData === null || getRowData === void 0 ? void 0 : (_getRowData_prophets_13 = getRowData.prophets[0]) === null || _getRowData_prophets_13 === void 0 ? void 0 : (_getRowData_prophets__prophet_code7 = _getRowData_prophets_13.prophet_code) === null || _getRowData_prophets__prophet_code7 === void 0 ? void 0 : _getRowData_prophets__prophet_code7.trim()),\n                    \"Default expected stock status\": ((getRowData === null || getRowData === void 0 ? void 0 : getRowData.distribution_points_json[0].from_dp) === null || (getRowData === null || getRowData === void 0 ? void 0 : getRowData.distribution_points_json[0].from_dp) === undefined) && \"0\",\n                    \"Default received stock status\": ((getRowData === null || getRowData === void 0 ? void 0 : getRowData.distribution_points_json[0].from_dp) === null || (getRowData === null || getRowData === void 0 ? void 0 : getRowData.distribution_points_json[0].from_dp) === undefined) && \"0\",\n                    \"Pallets in packhouse\": ((getRowData === null || getRowData === void 0 ? void 0 : getRowData.distribution_points_json[0].from_dp) === null || (getRowData === null || getRowData === void 0 ? void 0 : getRowData.distribution_points_json[0].from_dp) === undefined) && \"0\",\n                    \"Default haulier\": ((getRowData === null || getRowData === void 0 ? void 0 : getRowData.distribution_points_json[0].from_dp) === null || (getRowData === null || getRowData === void 0 ? void 0 : getRowData.distribution_points_json[0].from_dp) === undefined) && \"ZZZZZ\",\n                    \"Default expected location id\": ((getRowData === null || getRowData === void 0 ? void 0 : getRowData.distribution_points_json[0].from_dp) === null || (getRowData === null || getRowData === void 0 ? void 0 : getRowData.distribution_points_json[0].from_dp) === undefined) && 21,\n                    \"Default receiving location id\": ((getRowData === null || getRowData === void 0 ? void 0 : getRowData.distribution_points_json[0].from_dp) === null || (getRowData === null || getRowData === void 0 ? void 0 : getRowData.distribution_points_json[0].from_dp) === undefined) && 21,\n                    \"Packhouse location id\": ((getRowData === null || getRowData === void 0 ? void 0 : getRowData.distribution_points_json[0].from_dp) === null || (getRowData === null || getRowData === void 0 ? void 0 : getRowData.distribution_points_json[0].from_dp) === undefined) && 21,\n                    \"Default pick location id\": ((getRowData === null || getRowData === void 0 ? void 0 : getRowData.distribution_points_json[0].from_dp) === null || (getRowData === null || getRowData === void 0 ? void 0 : getRowData.distribution_points_json[0].from_dp) === undefined) && 21,\n                    \"Despatch location id\": ((getRowData === null || getRowData === void 0 ? void 0 : getRowData.distribution_points_json[0].from_dp) === null || (getRowData === null || getRowData === void 0 ? void 0 : getRowData.distribution_points_json[0].from_dp) === undefined) && 21,\n                    \"Default waste location id\": ((getRowData === null || getRowData === void 0 ? void 0 : getRowData.distribution_points_json[0].from_dp) === null || (getRowData === null || getRowData === void 0 ? void 0 : getRowData.distribution_points_json[0].from_dp) === undefined) && 21,\n                    \"Default pre-pick location id\": ((getRowData === null || getRowData === void 0 ? void 0 : getRowData.distribution_points_json[0].from_dp) === null || (getRowData === null || getRowData === void 0 ? void 0 : getRowData.distribution_points_json[0].from_dp) === undefined) && 21,\n                    \"Default returns location id\": ((getRowData === null || getRowData === void 0 ? void 0 : getRowData.distribution_points_json[0].from_dp) === null || (getRowData === null || getRowData === void 0 ? void 0 : getRowData.distribution_points_json[0].from_dp) === undefined) && 21,\n                    Address: \"\",\n                    \"Service supplier code\": \"\",\n                    \"EDI Reference Code\": \"\",\n                    \"EDI ANA Code\": \"\",\n                    \"User Integer 1\": ((getRowData === null || getRowData === void 0 ? void 0 : getRowData.distribution_points_json[0].from_dp) === null || (getRowData === null || getRowData === void 0 ? void 0 : getRowData.distribution_points_json[0].from_dp) === undefined) && 1,\n                    \"Movement resource group\": \"\",\n                    \"Handheld application used\": ((getRowData === null || getRowData === void 0 ? void 0 : getRowData.distribution_points_json[0].from_dp) === null || (getRowData === null || getRowData === void 0 ? void 0 : getRowData.distribution_points_json[0].from_dp) === undefined) && \"0\",\n                    \"Pallets in procure/receiving\": ((getRowData === null || getRowData === void 0 ? void 0 : getRowData.distribution_points_json[0].from_dp) === null || (getRowData === null || getRowData === void 0 ? void 0 : getRowData.distribution_points_json[0].from_dp) === undefined) && \"0\",\n                    \"Operational depo\": \"\",\n                    \"Enabled for masterfile sending\": \"\",\n                    \"Connected registed depot\": \"\",\n                    \"EDI Transmission type of depo\": \"\",\n                    \"Container loading depo\": ((getRowData === null || getRowData === void 0 ? void 0 : getRowData.distribution_points_json[0].from_dp) === null || (getRowData === null || getRowData === void 0 ? void 0 : getRowData.distribution_points_json[0].from_dp) === undefined) && \"0\",\n                    \"Airport depot\": \"\",\n                    \"Sms notification\": \"\",\n                    Port: \"\",\n                    Dormant: \"\",\n                    Active: ((getRowData === null || getRowData === void 0 ? void 0 : getRowData.distribution_points_json[0].from_dp) === null || (getRowData === null || getRowData === void 0 ? void 0 : getRowData.distribution_points_json[0].from_dp) === undefined) && 1,\n                    \"Ingredient distribution point\": \"\",\n                    \"Show in CE\": ((getRowData === null || getRowData === void 0 ? void 0 : getRowData.distribution_points_json[0].from_dp) === null || (getRowData === null || getRowData === void 0 ? void 0 : getRowData.distribution_points_json[0].from_dp) === undefined) && 1,\n                    \"Charge direction\": \"\",\n                    \"Pallet receive time\": \"\",\n                    \"User string 3\": \"\",\n                    \"Direct DP\": (getRowData === null || getRowData === void 0 ? void 0 : (_getRowData_distribution_points_json4 = getRowData.distribution_points_json) === null || _getRowData_distribution_points_json4 === void 0 ? void 0 : _getRowData_distribution_points_json4.length) > 0 && ((getRowData === null || getRowData === void 0 ? void 0 : getRowData.distribution_points_json[0].from_dp) === null || (getRowData === null || getRowData === void 0 ? void 0 : getRowData.distribution_points_json[0].from_dp) === undefined) ? (getRowData === null || getRowData === void 0 ? void 0 : (_getRowData_distribution_points_json_ = getRowData.distribution_points_json[0]) === null || _getRowData_distribution_points_json_ === void 0 ? void 0 : _getRowData_distribution_points_json_.direct_dp) ? 1 : \"0\" : \"\",\n                    \"Include on XML\": ((getRowData === null || getRowData === void 0 ? void 0 : getRowData.distribution_points_json[0].from_dp) === null || (getRowData === null || getRowData === void 0 ? void 0 : getRowData.distribution_points_json[0].from_dp) === undefined) && \"0\"\n                };\n            }\n            const newRoleData = multipleSendRoleOnRoleNums(getRowData, getRowData === null || getRowData === void 0 ? void 0 : getRowData.role_num);\n            const extractedSendacGroup = extractSendacGroup(getRowData === null || getRowData === void 0 ? void 0 : getRowData.supplier_group);\n            let sort_code = \"\";\n            let account_number = \"\";\n            let swiftBicCode = \"\";\n            let iban = \"\";\n            const swiftBicRegex = /^([A-Z0-9]{4}[A-Z0-9]{2}[A-Z0-9]{2}|[A-Z0-9]{4}[A-Z0-9]{2}[A-Z0-9]{2}[A-Z0-9]{3})$/;\n            if (swiftBicRegex.test(getRowData === null || getRowData === void 0 ? void 0 : getRowData.sort_bic) && (getRowData === null || getRowData === void 0 ? void 0 : getRowData.has_iban)) {\n                var _getRowData_account_number;\n                sort_code = \"000000\";\n                account_number = getRowData === null || getRowData === void 0 ? void 0 : (_getRowData_account_number = getRowData.account_number) === null || _getRowData_account_number === void 0 ? void 0 : _getRowData_account_number.slice(-8);\n                swiftBicCode = getRowData === null || getRowData === void 0 ? void 0 : getRowData.sort_bic;\n                iban = getRowData === null || getRowData === void 0 ? void 0 : getRowData.account_number;\n            } else if (!(getRowData === null || getRowData === void 0 ? void 0 : getRowData.has_iban) && swiftBicRegex.test(getRowData === null || getRowData === void 0 ? void 0 : getRowData.sort_bic)) {\n                sort_code = \"000000\";\n                account_number = getRowData === null || getRowData === void 0 ? void 0 : getRowData.account_number;\n                swiftBicCode = getRowData === null || getRowData === void 0 ? void 0 : getRowData.sort_bic;\n            } else {\n                sort_code = getRowData === null || getRowData === void 0 ? void 0 : getRowData.sort_bic;\n                account_number = getRowData === null || getRowData === void 0 ? void 0 : getRowData.account_number;\n            }\n            const bankac = {\n                \"Supplier code\": getRowData === null || getRowData === void 0 ? void 0 : (_getRowData_prophets_7 = getRowData.prophets[0]) === null || _getRowData_prophets_7 === void 0 ? void 0 : (_getRowData_prophets__prophet_code3 = _getRowData_prophets_7.prophet_code) === null || _getRowData_prophets__prophet_code3 === void 0 ? void 0 : _getRowData_prophets__prophet_code3.trim(),\n                \"Record id\": \"\",\n                \"Bank sort code\": sort_code,\n                \"Account number\": account_number,\n                \"Country code\": (getRowData === null || getRowData === void 0 ? void 0 : getRowData.country_code) == \"UK\" ? \"GB\" : getRowData === null || getRowData === void 0 ? void 0 : getRowData.country_code,\n                \"Account holder\": getRowData.company_name,\n                \"Currency number\": currencyId,\n                \"BACS currency\": getRowData === null || getRowData === void 0 ? void 0 : getRowData.bacs_currency_code,\n                \"Address Line 1\": \"\",\n                \"Address Line 2\": \"\",\n                \"BIC/Swift address\": swiftBicCode,\n                \"Internation bank reference code\": iban,\n                \"Account user id\": \"\",\n                \"Post code\": \"\"\n            };\n            const senbnk = {\n                \"Supplier code\": \"\",\n                Bankacid: \"\",\n                \"Header bank record id\": \"\",\n                \"Intermediary bank account id\": \"\",\n                \"Intermediary bank account id Internation bank reference code\": \"\"\n            };\n            // Parse and map contacts\n            const contacts = extractContacts(getRowData === null || getRowData === void 0 ? void 0 : (_getRowData_prophets_8 = getRowData.prophets[0]) === null || _getRowData_prophets_8 === void 0 ? void 0 : (_getRowData_prophets__prophet_code4 = _getRowData_prophets_8.prophet_code) === null || _getRowData_prophets__prophet_code4 === void 0 ? void 0 : _getRowData_prophets__prophet_code4.trim(), getRowData.contacts_json, getRowData === null || getRowData === void 0 ? void 0 : getRowData.company_name);\n            const newOrganizationData = {\n                \"Organization ID\": \"\",\n                \"Organization Name\": (getRowData === null || getRowData === void 0 ? void 0 : (_getRowData_prophets_9 = getRowData.prophets[0]) === null || _getRowData_prophets_9 === void 0 ? void 0 : _getRowData_prophets_9.prophet_code) ? getRowData === null || getRowData === void 0 ? void 0 : (_getRowData_prophets_10 = getRowData.prophets[0]) === null || _getRowData_prophets_10 === void 0 ? void 0 : (_getRowData_prophets__prophet_code5 = _getRowData_prophets_10.prophet_code) === null || _getRowData_prophets__prophet_code5 === void 0 ? void 0 : _getRowData_prophets__prophet_code5.trim() : \"\"\n            };\n            const newOrganizationRoleData = {\n                \"Organization ID\": \"\",\n                \"Organization Code\": \"\",\n                \"Role Type ID\": \"\",\n                Selected: \"\",\n                \"Organisation ID\": \"\",\n                \"Role Type ID\": \"\",\n                \"Contact ID\": \"\",\n                \"Contact ID Email Address\": \"\",\n                \"Contact ID Telephone\": \"\",\n                \"Contact ID Fax\": \"\"\n            };\n            const sheetSuppliersId = {\n                id: getRowData === null || getRowData === void 0 ? void 0 : getRowData.id,\n                supplierName: getRowData === null || getRowData === void 0 ? void 0 : getRowData.company_name,\n                isEmergencyAndFinanceNotComplete: getRowData === null || getRowData === void 0 ? void 0 : getRowData.isEmergencyAndFinanceNotComplete,\n                supplierCode: (getRowData === null || getRowData === void 0 ? void 0 : (_getRowData_prophets_11 = getRowData.prophets[0]) === null || _getRowData_prophets_11 === void 0 ? void 0 : _getRowData_prophets_11.prophet_code) ? getRowData === null || getRowData === void 0 ? void 0 : (_getRowData_prophets_12 = getRowData.prophets[0]) === null || _getRowData_prophets_12 === void 0 ? void 0 : (_getRowData_prophets__prophet_code6 = _getRowData_prophets_12.prophet_code) === null || _getRowData_prophets__prophet_code6 === void 0 ? void 0 : _getRowData_prophets__prophet_code6.trim() : \"\"\n            };\n            if (isSelected) {\n                setSupplierActiveData((prev)=>[\n                        ...prev,\n                        newSupplierActiveData\n                    ]);\n                setRoleData((prev)=>[\n                        ...prev,\n                        ...newRoleData\n                    ]);\n                setContactData((prev)=>[\n                        ...prev,\n                        ...contacts\n                    ]);\n                setOrganizationData((prev)=>[\n                        ...prev,\n                        newOrganizationData\n                    ]);\n                setOrganizationRoleData((prev)=>[\n                        ...prev,\n                        newOrganizationRoleData\n                    ]);\n                setSheetSupplierId((prev)=>[\n                        ...prev,\n                        sheetSuppliersId\n                    ]);\n                setSendacGroupData((prev)=>[\n                        ...prev,\n                        ...extractedSendacGroup\n                    ]);\n                setBankAc((prev)=>[\n                        ...prev,\n                        bankac\n                    ]);\n                setSenBnk((prev)=>[\n                        ...prev,\n                        senbnk\n                    ]);\n                if (Object.keys(UlpFil).length > 0) {\n                    setUlpFilData((prev)=>[\n                            ...prev,\n                            UlpFil\n                        ]);\n                }\n                setMultipleFilterISSData((prev)=>[\n                        ...prev,\n                        filteredISSExportData\n                    ]);\n            } else {\n                setMultipleFilterISSData((prev)=>prev.filter((item)=>item.id !== getRowData.id));\n                setSupplierActiveData((prev)=>prev.filter((item, index)=>{\n                        var _getRowData_prophets__prophet_code, _getRowData_prophets_;\n                        return index === 0 || item[\"Supplier code\"] !== (getRowData === null || getRowData === void 0 ? void 0 : (_getRowData_prophets_ = getRowData.prophets[0]) === null || _getRowData_prophets_ === void 0 ? void 0 : (_getRowData_prophets__prophet_code = _getRowData_prophets_.prophet_code) === null || _getRowData_prophets__prophet_code === void 0 ? void 0 : _getRowData_prophets__prophet_code.trim());\n                    }));\n                setUlpFilData((prev)=>prev.filter((item, index)=>{\n                        var _getRowData_prophets__prophet_code, _getRowData_prophets_;\n                        return index === 0 || item[\"Service Supplier Code\"] !== (getRowData === null || getRowData === void 0 ? void 0 : (_getRowData_prophets_ = getRowData.prophets[0]) === null || _getRowData_prophets_ === void 0 ? void 0 : (_getRowData_prophets__prophet_code = _getRowData_prophets_.prophet_code) === null || _getRowData_prophets__prophet_code === void 0 ? void 0 : _getRowData_prophets__prophet_code.trim());\n                    }));\n                setRoleData((prev)=>prev.filter((item, index)=>{\n                        var _getRowData_prophets__prophet_code, _getRowData_prophets_;\n                        return index === 0 || item[\"Supplier code\"] !== (getRowData === null || getRowData === void 0 ? void 0 : (_getRowData_prophets_ = getRowData.prophets[0]) === null || _getRowData_prophets_ === void 0 ? void 0 : (_getRowData_prophets__prophet_code = _getRowData_prophets_.prophet_code) === null || _getRowData_prophets__prophet_code === void 0 ? void 0 : _getRowData_prophets__prophet_code.trim());\n                    }));\n                setContactData((prev)=>prev.filter((item, index)=>{\n                        if (contacts.length > 0) {\n                            return index === 0 || item[\"Supplier code\"] !== contacts[0][\"Supplier code\"];\n                        } else {\n                            return true;\n                        }\n                    }));\n                setOrganizationData((prev)=>prev.filter((item, index)=>{\n                        var _getRowData_prophets__prophet_code, _getRowData_prophets_;\n                        return index === 0 || item[\"Organization Name\"] !== (getRowData === null || getRowData === void 0 ? void 0 : (_getRowData_prophets_ = getRowData.prophets[0]) === null || _getRowData_prophets_ === void 0 ? void 0 : (_getRowData_prophets__prophet_code = _getRowData_prophets_.prophet_code) === null || _getRowData_prophets__prophet_code === void 0 ? void 0 : _getRowData_prophets__prophet_code.trim());\n                    }));\n                setOrganizationRoleData((prev)=>prev.filter((item, index)=>index === 0 || item[\"Organization ID\"] !== \"\"));\n                setSendacGroupData((prev)=>prev.filter((item, index)=>{\n                        var _extractedSendacGroup_;\n                        return index === 0 || item[\"Description\"] !== ((_extractedSendacGroup_ = extractedSendacGroup[0]) === null || _extractedSendacGroup_ === void 0 ? void 0 : _extractedSendacGroup_.Description);\n                    }));\n                setBankAc((prev)=>prev.filter((item, index)=>{\n                        var _getRowData_prophets__prophet_code, _getRowData_prophets_;\n                        return index === 0 || item[\"Supplier code\"] !== (getRowData === null || getRowData === void 0 ? void 0 : (_getRowData_prophets_ = getRowData.prophets[0]) === null || _getRowData_prophets_ === void 0 ? void 0 : (_getRowData_prophets__prophet_code = _getRowData_prophets_.prophet_code) === null || _getRowData_prophets__prophet_code === void 0 ? void 0 : _getRowData_prophets__prophet_code.trim());\n                    }));\n                setSenBnk((prev)=>prev.filter((item, index)=>index === 0 || item[\"Supplier code\"] !== \"\"));\n                setSheetSupplierId((prev)=>prev.filter((item, index)=>index === 0 || item[\"id\"] !== (getRowData === null || getRowData === void 0 ? void 0 : getRowData.id)));\n            }\n            setSupplierCheckedValue(supplierActiveData.length > 0 || roleData.length > 0 || contactData.length > 0 || organizationData.length > 0 || organizationRoleData.length > 0 || bankAc.length > 0 || senBnk.length > 0 || sendacGroupData.length > 0 || ulpFilData.length > 0 || multipleFilterISSData.length > 0);\n        } else {\n            if (event.node.selected) {\n                if (doesContainCancelledSupplier) {\n                    setContainsCancelledSupplier(true);\n                    return;\n                }\n                setIncompleteToast(true);\n                setTimeout(()=>{\n                    setIncompleteToast(false);\n                }, 3000);\n            }\n        }\n    };\n    const clearFiltersHandler = ()=>{\n        setRowData(allRowData);\n        setIsFiltered(false);\n        setIsFilteredName(\"\");\n        setProphetId(\"\");\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_toastify__WEBPACK_IMPORTED_MODULE_14__.ToastContainer, {\n                limit: 1\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\suppliers.js\",\n                lineNumber: 1668,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Layout__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                userData: userData,\n                blockScreen: blockScreen,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mr-20 md:mr-12 lg:mr-14\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-row md:flex-col lg:flex-row justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center border border-light-gray rounded-md px-3 w-auto md:w-fit lg:w-auto md:py-2 lg:py-1 md:mb-3 lg:mb-0 mr-2\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        id: \"default-checkbox\",\n                                                        type: \"radio\",\n                                                        value: \"default\",\n                                                        checked: selectedStatus === \"default\",\n                                                        className: \"w-5 h-5 text-blue border-theme-blue2 rounded\",\n                                                        onChange: handleFilterToggle\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\suppliers.js\",\n                                                        lineNumber: 1675,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        htmlFor: \"default-checkbox\",\n                                                        className: \"p-0 ml-3 labels\",\n                                                        children: \"Default\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\suppliers.js\",\n                                                        lineNumber: 1683,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\suppliers.js\",\n                                                lineNumber: 1674,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\suppliers.js\",\n                                            lineNumber: 1673,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center border border-light-gray rounded-md px-3 w-auto md:w-fit lg:w-auto md:py-2 lg:py-1 md:mb-3 lg:mb-0 mr-2\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        id: \"export-checkbox\",\n                                                        type: \"radio\",\n                                                        value: \"Exported\",\n                                                        checked: selectedStatus === \"Exported\",\n                                                        className: \"w-5 h-5 text-blue border-theme-blue2 rounded\",\n                                                        onChange: handleFilterToggle\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\suppliers.js\",\n                                                        lineNumber: 1690,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        htmlFor: \"export-checkbox\",\n                                                        className: \"p-0 ml-3 labels\",\n                                                        children: \"Exported\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\suppliers.js\",\n                                                        lineNumber: 1698,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\suppliers.js\",\n                                                lineNumber: 1689,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\suppliers.js\",\n                                            lineNumber: 1688,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center border border-light-gray rounded-md px-3 w-auto md:w-fit lg:w-auto md:py-2 lg:py-1 md:mb-3 lg:mb-0 mr-2\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        id: \"completed-radio\",\n                                                        type: \"radio\",\n                                                        value: \"Completed\",\n                                                        checked: selectedStatus === \"Completed\",\n                                                        className: \"w-5 h-5 text-blue border-theme-blue2 rounded\",\n                                                        onChange: handleFilterToggle\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\suppliers.js\",\n                                                        lineNumber: 1705,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        htmlFor: \"completed-radio\",\n                                                        className: \"p-0 ml-3 labels\",\n                                                        children: \"Completed\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\suppliers.js\",\n                                                        lineNumber: 1713,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\suppliers.js\",\n                                                lineNumber: 1704,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\suppliers.js\",\n                                            lineNumber: 1703,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center border border-light-gray rounded-md px-3 w-auto md:w-fit lg:w-auto md:py-2 lg:py-1 md:mb-3 lg:mb-0 mr-2\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        id: \"cancelled-radio\",\n                                                        type: \"radio\",\n                                                        value: \"Cancelled\",\n                                                        checked: selectedStatus === \"Cancelled\",\n                                                        className: \"w-5 h-5 text-blue border-theme-blue2 rounded\",\n                                                        onChange: handleFilterToggle\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\suppliers.js\",\n                                                        lineNumber: 1720,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        htmlFor: \"cancelled-radio\",\n                                                        className: \"p-0 ml-3 labels\",\n                                                        children: [\n                                                            \" \",\n                                                            \"Cancelled\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\suppliers.js\",\n                                                        lineNumber: 1728,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\suppliers.js\",\n                                                lineNumber: 1719,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\suppliers.js\",\n                                            lineNumber: 1718,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        isFiltered && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"p-3 py-1 flex items-center capitalize ml-5\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"b\", {\n                                                            className: \"mr-3\",\n                                                            children: \"Filtered On: \"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\suppliers.js\",\n                                                            lineNumber: 1738,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        \" \",\n                                                        isFilteredName\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\suppliers.js\",\n                                                    lineNumber: 1737,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    type: \"button\",\n                                                    onClick: clearFiltersHandler,\n                                                    className: \"flex h-[20px] border bg-red-500 text-white border-red-500 button rounded-md items-center !px-1 !py-1 ml-1\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fortawesome_react_fontawesome__WEBPACK_IMPORTED_MODULE_3__.FontAwesomeIcon, {\n                                                        icon: _fortawesome_free_solid_svg_icons__WEBPACK_IMPORTED_MODULE_21__.faClose,\n                                                        className: \"fw-bold\",\n                                                        size: \"lg\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\suppliers.js\",\n                                                        lineNumber: 1745,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\suppliers.js\",\n                                                    lineNumber: 1740,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\suppliers.js\",\n                                    lineNumber: 1672,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"relative block w-[47vh] text-gray-400 mt-0 pt-0\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"absolute px-4 py-1 pt-[0.4rem] 2xl:pt-1.5 text-black\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fortawesome_react_fontawesome__WEBPACK_IMPORTED_MODULE_3__.FontAwesomeIcon, {\n                                                        icon: _fortawesome_free_solid_svg_icons__WEBPACK_IMPORTED_MODULE_21__.faSearch,\n                                                        className: \"fw-bold\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\suppliers.js\",\n                                                        lineNumber: 1757,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\suppliers.js\",\n                                                    lineNumber: 1756,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"text\",\n                                                    id: \"filter-text-box\",\n                                                    placeholder: \"Search\",\n                                                    onInput: onFilterTextBoxChanged,\n                                                    className: \"block w-full px-4 pl-10 text-gray-500 placeholder-gray-400 searchbar border rounded-lg appearance-none form-input focus:outline-none\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\suppliers.js\",\n                                                    lineNumber: 1759,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\suppliers.js\",\n                                            lineNumber: 1755,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>exportFilteredData(),\n                                            className: \" border text-skin-primary border-skin-primary button rounded-md items-center !py-1\",\n                                            disabled: exportDisabled ? true : false,\n                                            children: \"Export\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\suppliers.js\",\n                                            lineNumber: 1767,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        userData.email == \"<EMAIL>\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: deleteAll,\n                                            className: \"border text-skin-primary border-skin-primary button rounded-md items-center !py-1\",\n                                            children: \"Delete\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\suppliers.js\",\n                                            lineNumber: 1775,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_10___default()), {\n                                                href: \"/supplier/add\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    className: \"button border border-skin-primary bg-skin-primary text-white rounded-md whitespace-nowrap\",\n                                                    children: \"Add Supplier\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\suppliers.js\",\n                                                    lineNumber: 1784,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\suppliers.js\",\n                                                lineNumber: 1783,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\suppliers.js\",\n                                            lineNumber: 1782,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\suppliers.js\",\n                                    lineNumber: 1754,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\suppliers.js\",\n                            lineNumber: 1671,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"my-5\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative ag-theme-alpine !rounded-md\",\n                                style: {\n                                    height: \"calc(100vh - 151px)\"\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ag_grid_react__WEBPACK_IMPORTED_MODULE_4__.AgGridReact, {\n                                        rowData: rowData,\n                                        ref: gridRef,\n                                        columnDefs: columnDefs,\n                                        defaultColDef: defaultColDef,\n                                        suppressRowClickSelection: true,\n                                        rowSelection: \"multiple\",\n                                        pagination: true,\n                                        paginationPageSize: pageSize,\n                                        onPageSizeChanged: handlePageSizeChange,\n                                        tooltipShowDelay: 0,\n                                        tooltipHideDelay: 1000,\n                                        onGridReady: handleGridReady,\n                                        onRowSelected: handleCheckboxEvent,\n                                        gridOptions: gridOptions\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\suppliers.js\",\n                                        lineNumber: 1796,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-start mt-2 pagination-style\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            htmlFor: \"page-size-select pagination\",\n                                            className: \"inputs\",\n                                            children: [\n                                                \"Show\",\n                                                \" \",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                    id: \"page-size-select\",\n                                                    onChange: handlePageSizeChange,\n                                                    value: pageSize,\n                                                    className: \"focus:outline-none\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: 10,\n                                                            children: \"10\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\suppliers.js\",\n                                                            lineNumber: 1821,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: 15,\n                                                            children: \"15\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\suppliers.js\",\n                                                            lineNumber: 1822,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: 25,\n                                                            children: \"25\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\suppliers.js\",\n                                                            lineNumber: 1823,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: 50,\n                                                            children: \"50\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\suppliers.js\",\n                                                            lineNumber: 1824,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: 100,\n                                                            children: \"100\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\suppliers.js\",\n                                                            lineNumber: 1825,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\suppliers.js\",\n                                                    lineNumber: 1815,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                \" \",\n                                                \"Entries\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\suppliers.js\",\n                                            lineNumber: 1813,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\suppliers.js\",\n                                        lineNumber: 1812,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\suppliers.js\",\n                                lineNumber: 1792,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\suppliers.js\",\n                            lineNumber: 1791,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\suppliers.js\",\n                    lineNumber: 1670,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\suppliers.js\",\n                lineNumber: 1669,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Dialog_Transition_headlessui_react__WEBPACK_IMPORTED_MODULE_22__.Transition, {\n                appear: true,\n                show: isOpenOption,\n                as: react__WEBPACK_IMPORTED_MODULE_2__.Fragment,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Dialog_Transition_headlessui_react__WEBPACK_IMPORTED_MODULE_22__.Dialog, {\n                    as: \"div\",\n                    className: \"relative z-10\",\n                    onClose: setIsOpenOption,\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Dialog_Transition_headlessui_react__WEBPACK_IMPORTED_MODULE_22__.Transition.Child, {\n                            as: react__WEBPACK_IMPORTED_MODULE_2__.Fragment,\n                            enter: \"ease-out duration-300\",\n                            enterFrom: \"opacity-0\",\n                            enterTo: \"opacity-100\",\n                            leave: \"ease-in duration-200\",\n                            leaveFrom: \"opacity-100\",\n                            leaveTo: \"opacity-0\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"fixed inset-0 bg-white bg-opacity-25 backdrop-blur-sm\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\suppliers.js\",\n                                lineNumber: 1847,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\suppliers.js\",\n                            lineNumber: 1838,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"fixed inset-0 overflow-y-auto\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-center min-h-full p-4 text-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Dialog_Transition_headlessui_react__WEBPACK_IMPORTED_MODULE_22__.Transition.Child, {\n                                    as: react__WEBPACK_IMPORTED_MODULE_2__.Fragment,\n                                    enter: \"ease-out duration-300\",\n                                    enterFrom: \"opacity-0 scale-95\",\n                                    enterTo: \"opacity-100 scale-100\",\n                                    leave: \"ease-in duration-200\",\n                                    leaveFrom: \"opacity-100 scale-100\",\n                                    leaveTo: \"opacity-0 scale-95\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Dialog_Transition_headlessui_react__WEBPACK_IMPORTED_MODULE_22__.Dialog.Panel, {\n                                        className: \"transform overflow-hidden rounded-xl bg-white  text-left align-middle shadow-xl transition-all\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-white w-[500px]\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex w-full bg-skin-primary h-[40px] items-center justify-between\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                                className: \"text-white items-center font-poppinsemibold pl-4 text-[20px]\",\n                                                                children: \"Select the export type\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\suppliers.js\",\n                                                                lineNumber: 1864,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fortawesome_react_fontawesome__WEBPACK_IMPORTED_MODULE_3__.FontAwesomeIcon, {\n                                                                icon: _fortawesome_free_solid_svg_icons__WEBPACK_IMPORTED_MODULE_21__.faClose,\n                                                                className: \"pr-4 text-white cursor-pointer\",\n                                                                onClick: closeOptionModal\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\suppliers.js\",\n                                                                lineNumber: 1867,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\suppliers.js\",\n                                                        lineNumber: 1863,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex justify-around items-center px-5\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center pl-0 pt-5\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                        name: \"exportType\",\n                                                                        id: \"internalExport\",\n                                                                        type: \"radio\",\n                                                                        className: \"mr-4\",\n                                                                        value: \"internalExport\",\n                                                                        checked: selectedExportType === \"internalExport\",\n                                                                        onChange: handleExportType\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\suppliers.js\",\n                                                                        lineNumber: 1875,\n                                                                        columnNumber: 25\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        className: \"font-poppinsregular text-[16px] text-charcoal-gray\",\n                                                                        htmlFor: \"internalExport\",\n                                                                        children: \"Internal\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\suppliers.js\",\n                                                                        lineNumber: 1884,\n                                                                        columnNumber: 25\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\suppliers.js\",\n                                                                lineNumber: 1874,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center pl-4 pt-5\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                        type: \"radio\",\n                                                                        name: \"exportType\",\n                                                                        id: \"ISS\",\n                                                                        className: \"mr-4\",\n                                                                        value: \"ISS\",\n                                                                        checked: selectedExportType === \"ISS\",\n                                                                        onChange: handleExportType\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\suppliers.js\",\n                                                                        lineNumber: 1892,\n                                                                        columnNumber: 25\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        htmlFor: \"ISS\",\n                                                                        className: \"font-poppinsregular text-[16px] text-charcoal-gray\",\n                                                                        children: \"ISS\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\suppliers.js\",\n                                                                        lineNumber: 1901,\n                                                                        columnNumber: 25\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\suppliers.js\",\n                                                                lineNumber: 1891,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\suppliers.js\",\n                                                        lineNumber: 1873,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\suppliers.js\",\n                                                lineNumber: 1862,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-center pb-4 pr-4 mt-2\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: exportFilteredData,\n                                                    disabled: !selectedExportType,\n                                                    className: \"font-circularstdbook rounded-md w-[100px] p-1 leading-5 mt-1 py-2 text-center hover:opacity-80 bg-skin-primary text-white\",\n                                                    children: \"Select\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\suppliers.js\",\n                                                    lineNumber: 1911,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\suppliers.js\",\n                                                lineNumber: 1910,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\suppliers.js\",\n                                        lineNumber: 1861,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\suppliers.js\",\n                                    lineNumber: 1852,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\suppliers.js\",\n                                lineNumber: 1851,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\suppliers.js\",\n                            lineNumber: 1850,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\suppliers.js\",\n                    lineNumber: 1837,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\suppliers.js\",\n                lineNumber: 1836,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Dialog_Transition_headlessui_react__WEBPACK_IMPORTED_MODULE_22__.Transition, {\n                appear: true,\n                show: emailStatusPopup,\n                as: react__WEBPACK_IMPORTED_MODULE_2__.Fragment,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Dialog_Transition_headlessui_react__WEBPACK_IMPORTED_MODULE_22__.Dialog, {\n                    as: \"div\",\n                    className: \"relative z-10\",\n                    onClose: closeEmailPopup,\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Dialog_Transition_headlessui_react__WEBPACK_IMPORTED_MODULE_22__.Transition.Child, {\n                            as: react__WEBPACK_IMPORTED_MODULE_2__.Fragment,\n                            enter: \"ease-out duration-300\",\n                            enterFrom: \"opacity-0\",\n                            enterTo: \"opacity-100\",\n                            leave: \"ease-in duration-200\",\n                            leaveFrom: \"opacity-100\",\n                            leaveTo: \"opacity-0\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"fixed inset-0 bg-black bg-opacity-25 backdrop-blur-sm\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\suppliers.js\",\n                                lineNumber: 1937,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\suppliers.js\",\n                            lineNumber: 1928,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"fixed inset-0 overflow-y-auto\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-center min-h-full p-4 text-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Dialog_Transition_headlessui_react__WEBPACK_IMPORTED_MODULE_22__.Transition.Child, {\n                                    as: react__WEBPACK_IMPORTED_MODULE_2__.Fragment,\n                                    enter: \"ease-out duration-300\",\n                                    enterFrom: \"opacity-0 scale-95\",\n                                    enterTo: \"opacity-100 scale-100\",\n                                    leave: \"ease-in duration-200\",\n                                    leaveFrom: \"opacity-100 scale-100\",\n                                    leaveTo: \"opacity-0 scale-95\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Dialog_Transition_headlessui_react__WEBPACK_IMPORTED_MODULE_22__.Dialog.Panel, {\n                                        className: \" w-[45%] transform overflow-hidden rounded-xl bg-white  text-left align-middle shadow-xl transition-all\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative bg-white rounded-lg shadow\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-start justify-between p-8 rounded-t\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"flex flex-row text-xl font-semibold text-gray-900  items-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"flex justify-center items-center border border-skin-primary text-skin-primary w-[25px] h-[25px] text-center leading-5 rounded-full text-base me-4\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fortawesome_react_fontawesome__WEBPACK_IMPORTED_MODULE_3__.FontAwesomeIcon, {\n                                                                            icon: _fortawesome_free_solid_svg_icons__WEBPACK_IMPORTED_MODULE_21__.faInfo\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\suppliers.js\",\n                                                                            lineNumber: 1956,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        \" \"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\suppliers.js\",\n                                                                    lineNumber: 1955,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                \" \",\n                                                                \"Status Message\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\suppliers.js\",\n                                                            lineNumber: 1954,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: closeEmailPopup,\n                                                            type: \"button\",\n                                                            className: \"text-black bg-transparent hover:bg-gray-200 hover:text-gray-900 rounded-lg text-xl w-8 h-8 ml-auto inline-flex justify-center items-center \",\n                                                            \"data-modal-hide\": \"default-modal\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fortawesome_react_fontawesome__WEBPACK_IMPORTED_MODULE_3__.FontAwesomeIcon, {\n                                                                    icon: _fortawesome_free_solid_svg_icons__WEBPACK_IMPORTED_MODULE_21__.faXmark,\n                                                                    className: \"text-skin-primary\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\suppliers.js\",\n                                                                    lineNumber: 1966,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                \" \"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\suppliers.js\",\n                                                            lineNumber: 1960,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\suppliers.js\",\n                                                    lineNumber: 1953,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"p-8 py-0 space-y-6\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-base xl:text-md 2xl:text-lg leading-relaxed mt-2\",\n                                                        children: popupMessage\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\suppliers.js\",\n                                                        lineNumber: 1973,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\suppliers.js\",\n                                                    lineNumber: 1972,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-end p-6 space-x-2 justify-end\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: closeEmailPopup,\n                                                        \"data-modal-hide\": \"default-modal\",\n                                                        type: \"button\",\n                                                        className: \"border text-dark-gray focus:ring-4 focus:outline-none focus:ring-blue-300 font-medium rounded-md text-md px-6 py-2 text-center \",\n                                                        children: \"Ok\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\suppliers.js\",\n                                                        lineNumber: 1978,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\suppliers.js\",\n                                                    lineNumber: 1977,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\suppliers.js\",\n                                            lineNumber: 1952,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\suppliers.js\",\n                                        lineNumber: 1951,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\suppliers.js\",\n                                    lineNumber: 1942,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\suppliers.js\",\n                                lineNumber: 1941,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\suppliers.js\",\n                            lineNumber: 1940,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\suppliers.js\",\n                    lineNumber: 1927,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\suppliers.js\",\n                lineNumber: 1926,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true);\n};\n_s(suppliers, \"9BQ/M7/84lo+hPFM46zrS8eQD/U=\", false, function() {\n    return [\n        next_router__WEBPACK_IMPORTED_MODULE_11__.useRouter,\n        _utils_rolePermissionsContext__WEBPACK_IMPORTED_MODULE_17__.usePermissions,\n        _utils_loaders_loadingContext__WEBPACK_IMPORTED_MODULE_16__.useLoading\n    ];\n});\nvar __N_SSP = true;\n/* harmony default export */ __webpack_exports__[\"default\"] = (suppliers);\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevExports = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevExports) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports on update so we can compare the boundary\n                // signatures.\n                module.hot.dispose(function (data) {\n                    data.prevExports = currentExports;\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevExports !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevExports, currentExports)) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevExports !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./pages/suppliers.js\n"));

/***/ })

});