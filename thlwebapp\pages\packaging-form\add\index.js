import Layout from "@/components/Layout";
import RawMaterialRequest from "@/components/RawMaterialRequest";
import { apiConfig } from "@/services/apiConfig";
import Cookies from "js-cookie";
import React, { useEffect, useState } from "react";
import { ThreeCircles } from "react-loader-spinner";
import { toast, ToastContainer } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
//
import { useMsal } from "@azure/msal-react";
import PackgingForm from "@/components/PackagingForm";


const index = ({ userData }) => {
  const { instance, accounts } = useMsal();
  const [dropdowns, setDropdowns] = useState(null);

  useEffect(() => {
    const company=Cookies.get("company");
    let prophetId=1;
    if(company=="dpsltd"){
      prophetId=1;
    }else if(company=="efcltd"){
      prophetId=3;
    }else if(company=="fpp-ltd"){
      prophetId=4;
    }else if (company=="iss" || "issproduce"){
      prophetId=5;
    }
    const fetchData = async () => {
      const serverAddress = apiConfig.serverAddress;

      try {
        const allDropDowns = [
          "endCustomer",
          "masterProductCode",
          "packagingMasterproductCodes",
          "PackagingType",
          "RecyclableOPRL",
          "subProductCode",
          "SustainableForestryPaper",
          "TradingBusiness",
          "PackagingMaterialColors",
          "PackagingMaterialTypes",
          "PackagingReason",
        ];

        const res = await fetch(
          `${serverAddress}products/get-products-dropdowns-list?prophetId=${prophetId}`,
          {
            method: "POST",
            headers: {
              Accept: "application/json",
              "Content-Type": "application/json",
              Authorization: `Bearer ${userData.token}`,
            },
            body: JSON.stringify(allDropDowns),
          }
        );        
        // console.log("dropdownsRequest",res);
        if (res.status === 401) {
          console.error("Your session has expired. Please log in again.");
          toast.error("Your session has expired. Please log in again.");
          setTimeout(() => {
            logoutHandler(instance);
          }, 3000);
          return null;
        } 

        const allDropdownsList = await res.json();
        console.log("allDropdownsList",allDropdownsList);

        setDropdowns(allDropdownsList);
      } catch (error) {
        console.error("Error fetching data", error);
      }
    };

    fetchData();
  }, []);

  return (
    <Layout userData={userData}>
      <ToastContainer limit={1} />
      {!dropdowns ? (
        <div
          style={{
            display: "flex",
            alignItems: "center",
            justifyContent: "center",
            height: "calc(100vh - 100px)",
            // width: "calc(100vw - 125px)",
          }}
        >
          <ThreeCircles
            color="#002D73"
            height={50}
            width={50}
            visible={true}
            ariaLabel="oval-loading"
            secondaryColor="#0066FF"
            strokeWidth={2}
            strokeWidthSecondary={2}
          />
        </div>
      ) : (
        
        <PackgingForm
          dropdowns={dropdowns}
          userData={userData}
          pageType={"add"}
        />
       
      )}
      </Layout>
  );
};

export default index;

export const getServerSideProps = async (context) => {
  const serverAddress = apiConfig.serverAddress;
  const userData = context.req.cookies.user; // Access the "user" cookie

  try {
    return {
      props: {
        userData: JSON.parse(userData),
      },
    };
  } catch (error) {
    console.error("Error fetching data", error);
    return {
      props: {
        userData: null,
      },
    };
  }
};
