"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/suppliers",{

/***/ "./pages/suppliers.js":
/*!****************************!*\
  !*** ./pages/suppliers.js ***!
  \****************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __N_SSP: function() { return /* binding */ __N_SSP; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _components_Layout__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/Layout */ \"./components/Layout.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _fortawesome_react_fontawesome__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @fortawesome/react-fontawesome */ \"./node_modules/@fortawesome/react-fontawesome/index.es.js\");\n/* harmony import */ var _fortawesome_free_solid_svg_icons__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! @fortawesome/free-solid-svg-icons */ \"./node_modules/@fortawesome/free-solid-svg-icons/index.mjs\");\n/* harmony import */ var ag_grid_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ag-grid-react */ \"./node_modules/ag-grid-react/lib/main.js\");\n/* harmony import */ var ag_grid_react__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(ag_grid_react__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var ag_grid_community_styles_ag_grid_css__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ag-grid-community/styles//ag-grid.css */ \"./node_modules/ag-grid-community/styles/ag-grid.css\");\n/* harmony import */ var ag_grid_community_styles_ag_grid_css__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(ag_grid_community_styles_ag_grid_css__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var ag_grid_community_styles_ag_theme_alpine_css__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ag-grid-community/styles//ag-theme-alpine.css */ \"./node_modules/ag-grid-community/styles/ag-theme-alpine.css\");\n/* harmony import */ var ag_grid_community_styles_ag_theme_alpine_css__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(ag_grid_community_styles_ag_theme_alpine_css__WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var _utils_renderer_actionRenderer__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../utils/renderer/actionRenderer */ \"./utils/renderer/actionRenderer.js\");\n/* harmony import */ var _utils_renderer_nameRenderer__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ../utils/renderer/nameRenderer */ \"./utils/renderer/nameRenderer.js\");\n/* harmony import */ var _utils_renderer_statusRenderer__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ../utils/renderer/statusRenderer */ \"./utils/renderer/statusRenderer.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! next/link */ \"./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_10___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_10__);\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_11___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_11__);\n/* harmony import */ var _services_apiConfig__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/services/apiConfig */ \"./services/apiConfig.js\");\n/* harmony import */ var js_cookie__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! js-cookie */ \"./node_modules/js-cookie/dist/js.cookie.mjs\");\n/* harmony import */ var _utils_auth_auth__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/utils/auth/auth */ \"./utils/auth/auth.js\");\n/* harmony import */ var react_toastify__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! react-toastify */ \"./node_modules/react-toastify/dist/react-toastify.esm.mjs\");\n/* harmony import */ var react_toastify_dist_ReactToastify_css__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! react-toastify/dist/ReactToastify.css */ \"./node_modules/react-toastify/dist/ReactToastify.css\");\n/* harmony import */ var react_toastify_dist_ReactToastify_css__WEBPACK_IMPORTED_MODULE_16___default = /*#__PURE__*/__webpack_require__.n(react_toastify_dist_ReactToastify_css__WEBPACK_IMPORTED_MODULE_16__);\n/* harmony import */ var _utils_loaders_loadingContext__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! @/utils/loaders/loadingContext */ \"./utils/loaders/loadingContext.js\");\n/* harmony import */ var _utils_getCookieData__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! @/utils/getCookieData */ \"./utils/getCookieData.js\");\n/* harmony import */ var _azure_msal_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! @azure/msal-react */ \"./node_modules/@azure/msal-react/dist/index.js\");\n/* harmony import */ var _utils_userContext__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! @/utils/userContext */ \"./utils/userContext.js\");\n/* harmony import */ var _utils_rolePermissionsContext__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! @/utils/rolePermissionsContext */ \"./utils/rolePermissionsContext.js\");\n/* harmony import */ var _barrel_optimize_names_Dialog_Transition_headlessui_react__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=Dialog,Transition!=!@headlessui/react */ \"__barrel_optimize__?names=Dialog,Transition!=!./node_modules/@headlessui/react/dist/headlessui.esm.js\");\n/* harmony import */ var _utils_exportExcel__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! ../utils/exportExcel */ \"./utils/exportExcel.js\");\n/* harmony import */ var _components_supplierCodeRenderer__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! @/components/supplierCodeRenderer */ \"./components/supplierCodeRenderer.js\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n//import 'ag-grid-enterprise';\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst suppliers = (param)=>{\n    let { userData, token } = param;\n    _s();\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_11__.useRouter)();\n    const { permissions, updatePermissions } = (0,_utils_rolePermissionsContext__WEBPACK_IMPORTED_MODULE_21__.usePermissions)();\n    const [pageSize, setPageSize] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(15);\n    const [allRowData, setAllRowData] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [rowData, setRowData] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [isFiltered, setIsFiltered] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [isFilteredName, setIsFilteredName] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const gridRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)();\n    const { setIsLoading } = (0,_utils_loaders_loadingContext__WEBPACK_IMPORTED_MODULE_17__.useLoading)();\n    const [supplierRoles, setSupplierRoles] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [multipleFilterInternalData, setMultipleFilterInternalData] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [containsCancelledSupplier, setContainsCancelledSupplier] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [exportDisabled, setExportDisabled] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(true);\n    const [supplierCheckedValue, setSupplierCheckedValue] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const { instance, accounts } = (0,_azure_msal_react__WEBPACK_IMPORTED_MODULE_19__.useMsal)();\n    const { userDetails } = (0,_utils_userContext__WEBPACK_IMPORTED_MODULE_20__.useUser)();\n    const [isCommonError, setCommonError] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"\");\n    const [isOpenOption, setIsOpenOption] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [isUnExportable, setIsUnExportable] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [unExportableSuppliernames, setUnExportableSupplierNames] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"\");\n    const [supplierUniqueCodeToast, setSupplierUniqueCodeToast] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [supplierCodeValid, setSupplierCodeValid] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(true);\n    const [selectedExportType, setSelectedExportType] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"\");\n    const [prophetId, setProphetId] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"\");\n    const [emailStatusPopup, setEmailStatusPopup] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [popupMessage, setPopUpMessage] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"\");\n    const [internalExportSuccess, setInternalExportSuccess] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [ISSExportSuccess, setISSExportSuccess] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [blockScreen, setBlockScreen] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const user = (0,_utils_getCookieData__WEBPACK_IMPORTED_MODULE_18__.getCookieData)(\"user\");\n    const [selectedStatus, setSelectedStatus] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"default\"); // State to track selected status\n    const closeOptionModal = ()=>{\n        setIsOpenOption(false);\n    };\n    const handleExportType = (e)=>{\n        setSelectedExportType(e.target.value);\n    };\n    const closeEmailPopup = ()=>{\n        setEmailStatusPopup(false);\n        if (internalExportSuccess && ISSExportSuccess) {\n            setStatusChange();\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        if (containsCancelledSupplier) {\n            react_toastify__WEBPACK_IMPORTED_MODULE_15__.toast.error(//todo: change this message to make it better\n            \"Cannot export cancelled supplier.\");\n            setExportDisabled(true);\n        }\n    }, [\n        containsCancelledSupplier\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        if (supplierUniqueCodeToast && !supplierCodeValid) {\n            react_toastify__WEBPACK_IMPORTED_MODULE_15__.toast.error(//todo: change this message to make it better\n            \"Supplier code is not unique and not valid, kindly make sure the supplier code is unique and valid.\");\n        } else if (supplierUniqueCodeToast) {\n            react_toastify__WEBPACK_IMPORTED_MODULE_15__.toast.error(//todo: change this message to make it better\n            \"Supplier code is not unique, kindly make sure the supplier code is unique.\");\n        } else if (!supplierCodeValid) {\n            react_toastify__WEBPACK_IMPORTED_MODULE_15__.toast.error(\"Supplier code is not valid\");\n        }\n    }, [\n        supplierUniqueCodeToast,\n        supplierCodeValid\n    ]);\n    const [company, setCompany] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"\");\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        if (typeof document !== \"undefined\") {\n            document.title = \"Suppliers\";\n        }\n        if (true) {\n            const company = js_cookie__WEBPACK_IMPORTED_MODULE_13__[\"default\"].get(\"company\");\n            if (company) {\n                setCompany(company);\n            }\n            localStorage.removeItem(\"current\");\n            localStorage.removeItem(\"allowedSections\");\n        }\n        js_cookie__WEBPACK_IMPORTED_MODULE_13__[\"default\"].remove(\"prophet\");\n        js_cookie__WEBPACK_IMPORTED_MODULE_13__[\"default\"].remove(\"prophets\");\n        setIsLoading(false);\n        getData(userData).then((data)=>{\n            if (data === null) {\n                return;\n            }\n            const formattedData = data === null || data === void 0 ? void 0 : data.map((row)=>{\n                const roleIds = (row === null || row === void 0 ? void 0 : row.role_ids) ? JSON.parse(row === null || row === void 0 ? void 0 : row.role_ids).map((ele)=>ele.role_id) : [];\n                const roleJson = JSON.parse(row === null || row === void 0 ? void 0 : row.role_json);\n                const formattedRow = {\n                    isActive: row === null || row === void 0 ? void 0 : row.is_active,\n                    prophets: (row === null || row === void 0 ? void 0 : row.prophet_ids) ? JSON.parse(row === null || row === void 0 ? void 0 : row.prophet_ids) : [],\n                    supplier_code: (row === null || row === void 0 ? void 0 : row.prophet_ids) ? JSON.parse(row === null || row === void 0 ? void 0 : row.prophet_ids)[0].prophet_code : [],\n                    company_name: (row === null || row === void 0 ? void 0 : row.name) ? row === null || row === void 0 ? void 0 : row.name : \"Not Entered\",\n                    country_code: row === null || row === void 0 ? void 0 : row.country_code,\n                    payment_type: row === null || row === void 0 ? void 0 : row.payment_type,\n                    payment_type_name: row === null || row === void 0 ? void 0 : row.payment_type_name,\n                    currency_name: row === null || row === void 0 ? void 0 : row.currency_name,\n                    currency_id: row === null || row === void 0 ? void 0 : row.iss_currency_id,\n                    global_gap_number: row === null || row === void 0 ? void 0 : row.global_gap_number,\n                    chile_certificate_number: row === null || row === void 0 ? void 0 : row.chile_certificate_number,\n                    red_tractor: row === null || row === void 0 ? void 0 : row.red_tractor,\n                    organic_certificate_number: row === null || row === void 0 ? void 0 : row.organic_certificate_number,\n                    puc_code: row === null || row === void 0 ? void 0 : row.puc_code,\n                    address_line_1: row === null || row === void 0 ? void 0 : row.address_line_1,\n                    address_line_2: row === null || row === void 0 ? void 0 : row.address_line_2,\n                    address_line_3: row === null || row === void 0 ? void 0 : row.address_line_3,\n                    address_line_4: row === null || row === void 0 ? void 0 : row.address_line_4,\n                    postal_code: row === null || row === void 0 ? void 0 : row.postal_code,\n                    id: row === null || row === void 0 ? void 0 : row.id,\n                    currency: (row === null || row === void 0 ? void 0 : row.currency) ? row === null || row === void 0 ? void 0 : row.currency : \"Not Entered\",\n                    currency_code: (row === null || row === void 0 ? void 0 : row.currency_code) ? row === null || row === void 0 ? void 0 : row.currency_code : \"\",\n                    Compliance: row === null || row === void 0 ? void 0 : row.compliance,\n                    Financials: row === null || row === void 0 ? void 0 : row.financial,\n                    General: row === null || row === void 0 ? void 0 : row.technical,\n                    Procurement: row === null || row === void 0 ? void 0 : row.procurement,\n                    requestor: row === null || row === void 0 ? void 0 : row.requestor_name,\n                    requestor_email: row === null || row === void 0 ? void 0 : row.requestor_email,\n                    companies: (row === null || row === void 0 ? void 0 : row.prophet_names) ? row === null || row === void 0 ? void 0 : row.prophet_names : \"Not Entered\",\n                    role: (row === null || row === void 0 ? void 0 : row.role_names) ? row === null || row === void 0 ? void 0 : row.role_names : \"Not Entered\",\n                    roleJson: (row === null || row === void 0 ? void 0 : row.role_json) ? JSON.parse(row === null || row === void 0 ? void 0 : row.role_json) : [],\n                    roleId: (row === null || row === void 0 ? void 0 : row.role_ids) ? JSON.parse(row === null || row === void 0 ? void 0 : row.role_ids) : [],\n                    roleIds: (row === null || row === void 0 ? void 0 : row.role_ids) ? JSON.parse(row === null || row === void 0 ? void 0 : row.role_ids).map((ele)=>{\n                        return ele.role_id;\n                    }) : [],\n                    supplier_type: row === null || row === void 0 ? void 0 : row.supplier_type_label,\n                    contacts_json: row === null || row === void 0 ? void 0 : row.contacts_json,\n                    distribution_points_json: (row === null || row === void 0 ? void 0 : row.distribution_points_json) ? JSON.parse(row === null || row === void 0 ? void 0 : row.distribution_points_json) : [],\n                    vat_number: row === null || row === void 0 ? void 0 : row.vat_number,\n                    payment_terms: row === null || row === void 0 ? void 0 : row.payment_terms,\n                    sort_bic: row === null || row === void 0 ? void 0 : row.decryptedSort_Bic,\n                    name_branch: row === null || row === void 0 ? void 0 : row.decryptedName_branch,\n                    account_number: row === null || row === void 0 ? void 0 : row.decryptedAccountNumber,\n                    vatable: row === null || row === void 0 ? void 0 : row.vatable,\n                    iss_ledger_code: row === null || row === void 0 ? void 0 : row.iss_ledger_code,\n                    internal_ledger_code: row === null || row === void 0 ? void 0 : row.internal_ledger_code,\n                    intermediatery_account_number: row === null || row === void 0 ? void 0 : row.decryptedIntermediatery_account_number,\n                    bacs_currency_code: row === null || row === void 0 ? void 0 : row.bacs_currency_code,\n                    has_iban: row === null || row === void 0 ? void 0 : row.has_iban,\n                    isProducerSupplier: row === null || row === void 0 ? void 0 : row.product_supplier,\n                    isEmergencyRequest: row === null || row === void 0 ? void 0 : row.emergency_request,\n                    supplier_group: row === null || row === void 0 ? void 0 : row.sendac_groups_json,\n                    supplierLinks: row === null || row === void 0 ? void 0 : row.supplier_links_json,\n                    status: row === null || row === void 0 ? void 0 : row.label,\n                    role_num: row === null || row === void 0 ? void 0 : row.role_nums,\n                    edi: row === null || row === void 0 ? void 0 : row.edi\n                };\n                if ((roleIds.includes(1) || (roleIds === null || roleIds === void 0 ? void 0 : roleIds.includes(6))) && (row === null || row === void 0 ? void 0 : row.emergency_request) && (row === null || row === void 0 ? void 0 : row.financial) !== \"Completed\") {\n                    formattedRow.isEmergencyAndFinanceNotComplete = true;\n                } else {\n                    formattedRow.isEmergencyAndFinanceNotComplete = false;\n                }\n                return formattedRow;\n            });\n            setAllRowData(formattedData);\n            const filteredData = formattedData.length > 0 ? formattedData === null || formattedData === void 0 ? void 0 : formattedData.filter((row)=>row.status !== \"Cancelled\" && row.status !== \"Exported\") : [];\n            setRowData(filteredData);\n            const fetchRolePermissions = async ()=>{\n                try {\n                    let serverAddress = _services_apiConfig__WEBPACK_IMPORTED_MODULE_12__.apiConfig.serverAddress;\n                    const response = await fetch(\"\".concat(serverAddress, \"suppliers/get-role-permissions\"), {\n                        method: \"GET\",\n                        headers: {\n                            Authorization: \"Bearer \".concat(token)\n                        }\n                    });\n                    if (!response.ok) {\n                        throw new Error(\"Request failed with status \".concat(response.status));\n                    }\n                    const result = await response.json();\n                    const rolePermissions = {};\n                    for (const row of result){\n                        var _row_sections_split, _row_sections;\n                        const sectionsArray = row === null || row === void 0 ? void 0 : (_row_sections = row.sections) === null || _row_sections === void 0 ? void 0 : (_row_sections_split = _row_sections.split(\",\")) === null || _row_sections_split === void 0 ? void 0 : _row_sections_split.filter((section)=>(section === null || section === void 0 ? void 0 : section.trim()) !== \"\"); // Split sections string into an array and remove empty values\n                        rolePermissions[row === null || row === void 0 ? void 0 : row.role_id] = sectionsArray;\n                    }\n                    updatePermissions(rolePermissions);\n                } catch (error) {\n                    console.error(error);\n                // setCommonError(error.message);\n                }\n            };\n            fetchRolePermissions();\n        }).catch((error)=>{\n            console.log(error);\n        });\n    }, []);\n    function getData() {\n        const company = js_cookie__WEBPACK_IMPORTED_MODULE_13__[\"default\"].get(\"company\");\n        let serverAddress = _services_apiConfig__WEBPACK_IMPORTED_MODULE_12__.apiConfig.serverAddress;\n        const ADCompanyName = js_cookie__WEBPACK_IMPORTED_MODULE_13__[\"default\"].get(\"ADCompanyName\");\n        let prophetId = 0;\n        if (ADCompanyName == \"FPP\") {\n            prophetId = 4;\n        } else if (ADCompanyName == \"EFC\") {\n            prophetId = 3;\n        } else if (ADCompanyName == \"DPS\") {\n            prophetId = 1;\n        } else if (ADCompanyName == \"DPS MS\") {\n            prophetId = 2;\n        }\n        console.log(\"prophetId\", prophetId);\n        return fetch(\"\".concat(serverAddress, \"suppliers/get-suppliers/\").concat(company, \"/\").concat(prophetId), {\n            method: \"GET\",\n            headers: {\n                Authorization: \"Bearer \".concat(token)\n            }\n        }).then(async (res)=>{\n            if (res.status == 502) {\n                setBlockScreen(true);\n                return;\n            }\n            setBlockScreen(false);\n            if (res.status === 400) {\n                react_toastify__WEBPACK_IMPORTED_MODULE_15__.toast.error(\"There was an error with your request. Please check your data and try again.\");\n                return null;\n            } else if (res.status === 401) {\n                react_toastify__WEBPACK_IMPORTED_MODULE_15__.toast.error(\"Your session has expired. Please log in again.\");\n                setTimeout(()=>{\n                    localStorage.removeItem(\"superUser\");\n                    js_cookie__WEBPACK_IMPORTED_MODULE_13__[\"default\"].remove(\"company\");\n                    js_cookie__WEBPACK_IMPORTED_MODULE_13__[\"default\"].remove(\"ADCompanyName\");\n                    localStorage.removeItem(\"id\");\n                    localStorage.removeItem(\"name\");\n                    localStorage.removeItem(\"role\");\n                    localStorage.removeItem(\"email\");\n                    js_cookie__WEBPACK_IMPORTED_MODULE_13__[\"default\"].remove(\"user\");\n                    js_cookie__WEBPACK_IMPORTED_MODULE_13__[\"default\"].remove(\"theme\");\n                    js_cookie__WEBPACK_IMPORTED_MODULE_13__[\"default\"].remove(\"token\");\n                    const redirectUrl = \"/login?redirect=\".concat(encodeURIComponent(window.location.pathname));\n                    (0,_utils_auth_auth__WEBPACK_IMPORTED_MODULE_14__.logoutHandler)(instance, redirectUrl);\n                }, 3000);\n                return null;\n            }\n            if (res.status === 200) {\n                return res.json();\n            }\n            throw new Error(\"Failed to fetch data\");\n        }).catch((error)=>{\n            setCommonError(error.message);\n        });\n    }\n    function deleteAll() {\n        setIsLoading(true);\n        let serverAddress = _services_apiConfig__WEBPACK_IMPORTED_MODULE_12__.apiConfig.serverAddress;\n        return fetch(\"\".concat(serverAddress, \"suppliers/delete-all\"), {\n            method: \"DELETE\",\n            headers: {\n                Authorization: \"Bearer \".concat(token)\n            }\n        }).then(async (res)=>{\n            if (res.status === 400) {\n                setIsLoading(false);\n                react_toastify__WEBPACK_IMPORTED_MODULE_15__.toast.error(\"There was an error with your request. Please check your data and try again.\");\n            } else if (res.status === 401) {\n                react_toastify__WEBPACK_IMPORTED_MODULE_15__.toast.error(\"Your session has expired. Please log in again.\");\n                setTimeout(()=>{\n                    setIsLoading(false);\n                    localStorage.removeItem(\"superUser\");\n                    js_cookie__WEBPACK_IMPORTED_MODULE_13__[\"default\"].remove(\"company\");\n                    js_cookie__WEBPACK_IMPORTED_MODULE_13__[\"default\"].remove(\"ADCompanyName\");\n                    localStorage.removeItem(\"id\");\n                    localStorage.removeItem(\"name\");\n                    localStorage.removeItem(\"role\");\n                    localStorage.removeItem(\"email\");\n                    js_cookie__WEBPACK_IMPORTED_MODULE_13__[\"default\"].remove(\"user\");\n                    js_cookie__WEBPACK_IMPORTED_MODULE_13__[\"default\"].remove(\"theme\");\n                    js_cookie__WEBPACK_IMPORTED_MODULE_13__[\"default\"].remove(\"token\");\n                    const redirectUrl = \"/login?redirect=\".concat(encodeURIComponent(window.location.pathname));\n                    (0,_utils_auth_auth__WEBPACK_IMPORTED_MODULE_14__.logoutHandler)(instance, redirectUrl);\n                }, 3000);\n            }\n            if (res.status === 200) {\n                react_toastify__WEBPACK_IMPORTED_MODULE_15__.toast.info(\"Delete successfull\");\n                setIsLoading(false);\n                router.reload();\n            }\n            throw new Error(\"Failed to delete\");\n        }).catch((error)=>{\n            setIsLoading(false);\n            console.error(error);\n        });\n    }\n    const handlePageSizeChange = (event)=>{\n        const newPageSize = parseInt(event.target.value, 15);\n        setPageSize(newPageSize);\n        gridRef.current.api.paginationSetPageSize(newPageSize);\n    };\n    const defaultColDef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useMemo)(()=>({\n            sortable: true,\n            filter: false,\n            resizable: true,\n            flex: 1\n        }));\n    const gridOptions = {\n        responsive: true\n    };\n    const CustomCellRenderer = (params)=>{\n        const truncatedText = params === null || params === void 0 ? void 0 : params.value;\n        // params?.value && params?.value?.length > 12\n        //   ? params?.value?.substring(0, 12) + \"...\"\n        //   : params?.value;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n            title: params === null || params === void 0 ? void 0 : params.value,\n            children: truncatedText\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\suppliers.js\",\n            lineNumber: 403,\n            columnNumber: 12\n        }, undefined);\n    };\n    const setStatusChange = ()=>{\n        setIsLoading(true);\n        setTimeout(function() {\n            getData(userData).then((data)=>{\n                if (data === null) {\n                    return;\n                }\n                const formattedData = data === null || data === void 0 ? void 0 : data.map((row)=>{\n                    const roleIds = (row === null || row === void 0 ? void 0 : row.role_ids) ? JSON.parse(row === null || row === void 0 ? void 0 : row.role_ids).map((ele)=>ele.role_id) : [];\n                    const roleJson = JSON.parse(row === null || row === void 0 ? void 0 : row.role_json);\n                    const formattedRow = {\n                        isActive: row === null || row === void 0 ? void 0 : row.is_active,\n                        prophets: (row === null || row === void 0 ? void 0 : row.prophet_ids) ? JSON.parse(row === null || row === void 0 ? void 0 : row.prophet_ids) : [],\n                        supplier_code: (row === null || row === void 0 ? void 0 : row.prophet_ids) ? JSON.parse(row === null || row === void 0 ? void 0 : row.prophet_ids)[0].prophet_code : [],\n                        company_name: (row === null || row === void 0 ? void 0 : row.name) ? row === null || row === void 0 ? void 0 : row.name : \"Not Entered\",\n                        id: row === null || row === void 0 ? void 0 : row.id,\n                        currency: (row === null || row === void 0 ? void 0 : row.currency) ? row === null || row === void 0 ? void 0 : row.currency : \"Not Entered\",\n                        Compliance: row === null || row === void 0 ? void 0 : row.compliance,\n                        Financials: row === null || row === void 0 ? void 0 : row.financial,\n                        General: row === null || row === void 0 ? void 0 : row.technical,\n                        Procurement: row === null || row === void 0 ? void 0 : row.procurement,\n                        requestor: row === null || row === void 0 ? void 0 : row.requestor_name,\n                        companies: (row === null || row === void 0 ? void 0 : row.prophet_names) ? row === null || row === void 0 ? void 0 : row.prophet_names : \"Not Entered\",\n                        role: (row === null || row === void 0 ? void 0 : row.role_names) ? row === null || row === void 0 ? void 0 : row.role_names : \"Not Entered\",\n                        roleId: (row === null || row === void 0 ? void 0 : row.role_ids) ? JSON.parse(row === null || row === void 0 ? void 0 : row.role_ids) : [],\n                        roleIds: (row === null || row === void 0 ? void 0 : row.role_ids) ? JSON.parse(row === null || row === void 0 ? void 0 : row.role_ids).map((ele)=>{\n                            return ele.role_id;\n                        }) : [],\n                        country_code: row === null || row === void 0 ? void 0 : row.country_code,\n                        payment_type: row === null || row === void 0 ? void 0 : row.payment_type,\n                        payment_type_name: row === null || row === void 0 ? void 0 : row.payment_type_name,\n                        currency_name: row === null || row === void 0 ? void 0 : row.currency_name,\n                        currency_id: row === null || row === void 0 ? void 0 : row.iss_currency_id,\n                        global_gap_number: row === null || row === void 0 ? void 0 : row.global_gap_number,\n                        chile_certificate_number: row === null || row === void 0 ? void 0 : row.chile_certificate_number,\n                        red_tractor: row === null || row === void 0 ? void 0 : row.red_tractor,\n                        organic_certificate_number: row === null || row === void 0 ? void 0 : row.organic_certificate_number,\n                        puc_code: row === null || row === void 0 ? void 0 : row.puc_code,\n                        address_line_1: row === null || row === void 0 ? void 0 : row.address_line_1,\n                        address_line_2: row === null || row === void 0 ? void 0 : row.address_line_2,\n                        address_line_3: row === null || row === void 0 ? void 0 : row.address_line_3,\n                        address_line_4: row === null || row === void 0 ? void 0 : row.address_line_4,\n                        postal_code: row === null || row === void 0 ? void 0 : row.postal_code,\n                        currency_code: (row === null || row === void 0 ? void 0 : row.currency_code) ? row === null || row === void 0 ? void 0 : row.currency_code : \"\",\n                        requestor_email: row === null || row === void 0 ? void 0 : row.requestor_email,\n                        companies: (row === null || row === void 0 ? void 0 : row.prophet_names) ? row === null || row === void 0 ? void 0 : row.prophet_names : \"Not Entered\",\n                        vatable: row === null || row === void 0 ? void 0 : row.vatable,\n                        roleJson: (row === null || row === void 0 ? void 0 : row.role_json) ? JSON.parse(row === null || row === void 0 ? void 0 : row.role_json) : [],\n                        supplier_type: row === null || row === void 0 ? void 0 : row.supplier_type_label,\n                        isProducerSupplier: row === null || row === void 0 ? void 0 : row.product_supplier,\n                        isEmergencyRequest: row === null || row === void 0 ? void 0 : row.emergency_request,\n                        supplier_group: row === null || row === void 0 ? void 0 : row.sendac_groups_json,\n                        supplierLinks: row === null || row === void 0 ? void 0 : row.supplier_links_json,\n                        status: row === null || row === void 0 ? void 0 : row.label,\n                        role_num: row === null || row === void 0 ? void 0 : row.role_nums,\n                        contacts_json: row === null || row === void 0 ? void 0 : row.contacts_json,\n                        distribution_points_json: (row === null || row === void 0 ? void 0 : row.distribution_points_json) ? JSON.parse(row === null || row === void 0 ? void 0 : row.distribution_points_json) : [],\n                        vat_number: row === null || row === void 0 ? void 0 : row.vat_number,\n                        payment_terms: row === null || row === void 0 ? void 0 : row.payment_terms,\n                        sort_bic: row === null || row === void 0 ? void 0 : row.decryptedSort_Bic,\n                        name_branch: row === null || row === void 0 ? void 0 : row.decryptedName_branch,\n                        account_number: row === null || row === void 0 ? void 0 : row.decryptedAccountNumber,\n                        iss_ledger_code: row === null || row === void 0 ? void 0 : row.iss_ledger_code,\n                        internal_ledger_code: row === null || row === void 0 ? void 0 : row.internal_ledger_code,\n                        intermediatery_account_number: row === null || row === void 0 ? void 0 : row.decryptedIntermediatery_account_number,\n                        bacs_currency_code: row === null || row === void 0 ? void 0 : row.bacs_currency_code,\n                        has_iban: row === null || row === void 0 ? void 0 : row.has_iban,\n                        edi: row === null || row === void 0 ? void 0 : row.edi\n                    };\n                    if (((roleIds === null || roleIds === void 0 ? void 0 : roleIds.includes(1)) || (roleIds === null || roleIds === void 0 ? void 0 : roleIds.includes(6))) && (row === null || row === void 0 ? void 0 : row.emergency_request) && (row === null || row === void 0 ? void 0 : row.financial) !== \"Completed\") {\n                        formattedRow.isEmergencyAndFinanceNotComplete = true;\n                    } else {\n                        formattedRow.isEmergencyAndFinanceNotComplete = false;\n                    }\n                    return formattedRow;\n                });\n                setAllRowData(formattedData);\n                const filteredData = formattedData.length > 0 ? formattedData === null || formattedData === void 0 ? void 0 : formattedData.filter((row)=>row.status !== \"Completed\" && row.status !== \"Cancelled\" && row.status !== \"Exported\") : [];\n                setRowData(filteredData);\n                setIsLoading(false);\n            }).catch((error)=>{\n                console.log(error);\n                setIsLoading(false);\n            });\n        }, 3000);\n    // console.log('updated id', id)\n    // console.log('updated status', status)\n    // console.log(rowData)\n    // const find_supplier = rowData.filter((item) => item.id === id)\n    // const filterWithId = rowData.filter((item) => item.id !== id)\n    // console.log(find_supplier)\n    // //const rowNode = gridRef.current.api.getRowNode(id.toString());\n    // //console.log(rowNode);\n    // //rowNode.setRowData(...rowData, find_supplier);\n    // setRowData([...filterWithId, ...find_supplier]);\n    };\n    const CustomTooltipComponent = (param)=>/*#__PURE__*/ {\n        let { value } = param;\n        return (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            title: value,\n            children: value\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\suppliers.js\",\n            lineNumber: 538,\n            columnNumber: 5\n        }, undefined);\n    };\n    const columnDefs = [\n        {\n            headerName: \"Supplier Name\",\n            field: \"company_name\",\n            // checkboxSelection: true,\n            checkboxSelection: (params)=>{\n                return params.data.status === \"Cancelled\" ? {\n                    checked: false,\n                    disabled: true\n                } : true;\n            },\n            cellRenderer: _utils_renderer_nameRenderer__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n            headerCheckboxSelection: true,\n            //suppressMenu: true,\n            //suppressSizeToFit: true,\n            //suppressSizeToFit: false,\n            flex: \"8%\",\n            filter: true,\n            cellRendererParams: {\n                setSuppliers: setRowData,\n                setIsFiltered: setIsFiltered,\n                setIsFilteredName: setIsFilteredName\n            }\n        },\n        {\n            headerName: \"Supplier Code\",\n            cellRenderer: _components_supplierCodeRenderer__WEBPACK_IMPORTED_MODULE_23__[\"default\"],\n            field: \"supplier_code\",\n            flex: \"3%\"\n        },\n        {\n            headerName: \"Roles\",\n            field: \"role\",\n            cellRenderer: CustomCellRenderer,\n            tooltipComponent: CustomTooltipComponent,\n            flex: \"3%\"\n        },\n        {\n            headerName: \"Companies\",\n            field: \"companies\",\n            cellRenderer: CustomCellRenderer,\n            tooltipComponent: CustomTooltipComponent,\n            flex: \"4%\"\n        },\n        {\n            headerName: \"Currency\",\n            field: \"currency\",\n            flex: \"3%\",\n            cellRenderer: CustomCellRenderer,\n            tooltipComponent: CustomTooltipComponent,\n            cellStyle: (params)=>{\n                if (params.value == \"Not Entered\") {\n                    return {\n                        color: \"#B31312\"\n                    };\n                }\n                return null;\n            }\n        },\n        {\n            headerName: \"Requestor\",\n            cellRenderer: CustomCellRenderer,\n            tooltipComponent: CustomTooltipComponent,\n            field: \"requestor\",\n            flex: \"4%\"\n        },\n        {\n            headerName: \"General\",\n            cellRenderer: _utils_renderer_statusRenderer__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n            field: \"General\",\n            flex: \"3%\"\n        },\n        {\n            headerName: \"Financial\",\n            cellRenderer: _utils_renderer_statusRenderer__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n            field: \"Financials\",\n            flex: \"3%\"\n        },\n        {\n            headerName: \"Compliance\",\n            field: \"Compliance\",\n            cellRenderer: _utils_renderer_statusRenderer__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n            flex: \"4%\"\n        },\n        {\n            headerName: \"Procurement\",\n            field: \"Procurement\",\n            cellRenderer: _utils_renderer_statusRenderer__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n            flex: \"4%\"\n        },\n        {\n            headerName: \"Status\",\n            field: \"status\",\n            cellRenderer: _utils_renderer_statusRenderer__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n            cellStyle: ()=>({\n                    justifyContent: \"center\"\n                }),\n            flex: \"4%\",\n            hide: false\n        },\n        {\n            field: \"\",\n            cellRenderer: (params)=>(0,_utils_renderer_actionRenderer__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(params, userData, token, company),\n            flex: \"2%\",\n            cellStyle: ()=>({}),\n            sortable: false,\n            cellRendererParams: {\n                setUpdateStatusChange: setStatusChange\n            }\n        },\n        {\n            field: \"role_num\",\n            hide: true\n        }\n    ];\n    const onFilterTextBoxChanged = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(()=>{\n        gridRef.current.api.setQuickFilter(document.getElementById(\"filter-text-box\").value);\n    }, []);\n    const handleFilterToggle = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)((e)=>{\n        const { value } = e.target;\n        setSelectedStatus(value);\n        let filteredData = [];\n        if (value == \"default\") {\n            filteredData = allRowData.filter((row)=>row.status !== \"Cancelled\" && row.status !== \"Exported\");\n        } else {\n            filteredData = allRowData.filter((row)=>row.status === value);\n        }\n        setRowData(filteredData);\n        setExportDisabled(filteredData.length === 0);\n    }, [\n        allRowData\n    ]);\n    const exportFilteredData = async ()=>{\n        let export_ISSresponse = false;\n        let exportInternal_response = false;\n        // setIsOpenOption(false);\n        const gridApi = gridRef.current.api;\n        let filteredData = [];\n        const isInternal = selectedExportType === \"internalExport\";\n        if (isInternal) {\n            gridApi.forEachNodeAfterFilter((node)=>{\n                var _node_data, _node_data1, _node_data2, _node_data3;\n                if (node.data !== undefined && ((_node_data = node.data) === null || _node_data === void 0 ? void 0 : _node_data.status) === \"Completed\" || ((_node_data1 = node.data) === null || _node_data1 === void 0 ? void 0 : _node_data1.status) === \"Exported\" || ((_node_data2 = node.data) === null || _node_data2 === void 0 ? void 0 : _node_data2.isEmergencyRequest) && ((_node_data3 = node.data) === null || _node_data3 === void 0 ? void 0 : _node_data3.General) === \"Complete\") {\n                    filteredData = [\n                        [\n                            {\n                                \"Supplier Active\": \"test\",\n                                \"Haulage cube local\": \"test\",\n                                \"Haulage cube name\": \"test\",\n                                \"update guesstimates type\": \"test\",\n                                \"Organization ID\": \"\",\n                                \"Enforce department\": \"\",\n                                \"Sendac Group\": \"\",\n                                \"Supplier name\": \"\",\n                                \"Supplier type\": \"\",\n                                \"User Lookup 2\": \"\",\n                                \"Address Line 1\": \"\",\n                                \"Address Line 2\": \"\",\n                                \"Address Line 3\": \"\",\n                                \"Address Line 4\": \"\",\n                                \"Post code\": \"\",\n                                \"Country code\": \"\",\n                                \"Payee supplier code\": \"\",\n                                \"Invoice supplier\": \"\",\n                                \"Head office\": \"\",\n                                \"Settlement days\": \"\",\n                                \"Bank General Ledger Code\": \"\",\n                                \"Currency number\": \"\",\n                                \"Currency number / name\": \"\",\n                                \"Bank general ledger code\": \"\",\n                                \"Payment type\": \"\",\n                                \"Country code\": \"\",\n                                Vatable: \"\",\n                                vatable: \"\",\n                                \"Update guesstimates type\": \"\",\n                                \"Area Number\": \"\",\n                                Buyer: \"\",\n                                \"Multiple lot indicator\": \"\",\n                                \"multiple lot indicator\": \"\",\n                                \"Generate Pallet Loading Plan\": \"\",\n                                \"Distribution point for supplier\": \"\",\n                                \"Payment terms\": \"\",\n                                \"Department Number\": \"\"\n                            }\n                        ],\n                        [\n                            {\n                                \"Supplier code\": \"test\",\n                                Sendacroleid: \"test\",\n                                Description: \"test\",\n                                \"Supplier Code Name\": \"\",\n                                Type: \"\",\n                                \"Type Description\": \"\",\n                                GGN: \"\"\n                            }\n                        ],\n                        [\n                            {\n                                \"Contact ID\": \"test\",\n                                \"Supplier Name\": \"test\",\n                                \"Email Address\": \"\",\n                                Telephone: \"\",\n                                Cell: \"\"\n                            }\n                        ],\n                        [\n                            {\n                                \"Organization ID\": \"test\",\n                                \"Organization Name\": \"\"\n                            }\n                        ],\n                        [\n                            {\n                                \"Organization ID\": \"test\",\n                                \"Organization Code\": \"\",\n                                \"Role Type ID\": \"\",\n                                Selected: \"\",\n                                \"Organisation ID\": \"\",\n                                \"Role Type ID\": \"\",\n                                \"Contact ID\": \"\",\n                                \"Contact ID Email Address\": \"\",\n                                \"Contact ID Telephone\": \"\"\n                            }\n                        ]\n                    ];\n                }\n            });\n        } else {\n            gridApi.forEachNodeAfterFilter((node)=>{\n                var _node_data, _node_data1, _node_data2, _node_data3;\n                let rolesArray = node.data.roleId.map((ele)=>{\n                    return ele.role_id;\n                });\n                if (node.data !== undefined && ((_node_data = node.data) === null || _node_data === void 0 ? void 0 : _node_data.status) === \"Completed\" || ((_node_data1 = node.data) === null || _node_data1 === void 0 ? void 0 : _node_data1.status) === \"Exported\" || ((_node_data2 = node.data) === null || _node_data2 === void 0 ? void 0 : _node_data2.isEmergencyRequest) && ((_node_data3 = node.data) === null || _node_data3 === void 0 ? void 0 : _node_data3.General) === \"Complete\") {\n                    var _node_data4, _node_data_prophets__prophet_code, _node_data_prophets_, _node_data5, _node_data_company_name, _node_data6, _node_data7, _node_data8, _node_data9, _node_data10, _node_data11, _node_data12, _node_data13, _node_data_prophets__prophet_code1, _node_data_prophets_1, _node_data14, _node_data15, _node_data16, _node_data17, _node_data18, _node_data19, _node_data20, _node_data21;\n                    const filteredExportData = {\n                        \"Supplier Active\": (node === null || node === void 0 ? void 0 : (_node_data4 = node.data) === null || _node_data4 === void 0 ? void 0 : _node_data4.isActive) ? 1 : 0,\n                        \"Supplier code\": node === null || node === void 0 ? void 0 : (_node_data5 = node.data) === null || _node_data5 === void 0 ? void 0 : (_node_data_prophets_ = _node_data5.prophets[0]) === null || _node_data_prophets_ === void 0 ? void 0 : (_node_data_prophets__prophet_code = _node_data_prophets_.prophet_code) === null || _node_data_prophets__prophet_code === void 0 ? void 0 : _node_data_prophets__prophet_code.trim(),\n                        \"EDI Partner\": \"\",\n                        \"Supplier name\": node === null || node === void 0 ? void 0 : (_node_data6 = node.data) === null || _node_data6 === void 0 ? void 0 : (_node_data_company_name = _node_data6.company_name) === null || _node_data_company_name === void 0 ? void 0 : _node_data_company_name.trim(),\n                        \"Country Code\": node === null || node === void 0 ? void 0 : (_node_data7 = node.data) === null || _node_data7 === void 0 ? void 0 : _node_data7.country_code,\n                        \"Distribution Point for Supplier\": 6,\n                        \"Bank Ledger Code\": node === null || node === void 0 ? void 0 : (_node_data8 = node.data) === null || _node_data8 === void 0 ? void 0 : _node_data8.currency_id,\n                        \"Area Number\": 170,\n                        Vatable: 0,\n                        Buyer: 1,\n                        \"Billing type\": 0,\n                        \"Payment type\": node === null || node === void 0 ? void 0 : (_node_data9 = node.data) === null || _node_data9 === void 0 ? void 0 : _node_data9.payment_type,\n                        \"Currency number\": node === null || node === void 0 ? void 0 : (_node_data10 = node.data) === null || _node_data10 === void 0 ? void 0 : _node_data10.currency_id,\n                        GGN: node === null || node === void 0 ? void 0 : (_node_data11 = node.data) === null || _node_data11 === void 0 ? void 0 : _node_data11.global_gap_number,\n                        \"Organic cert\": node === null || node === void 0 ? void 0 : (_node_data12 = node.data) === null || _node_data12 === void 0 ? void 0 : _node_data12.organic_certificate_number,\n                        \"Regional cert\": node === null || node === void 0 ? void 0 : (_node_data13 = node.data) === null || _node_data13 === void 0 ? void 0 : _node_data13.chile_certificate_number,\n                        \"Head office\": node === null || node === void 0 ? void 0 : (_node_data14 = node.data) === null || _node_data14 === void 0 ? void 0 : (_node_data_prophets_1 = _node_data14.prophets[0]) === null || _node_data_prophets_1 === void 0 ? void 0 : (_node_data_prophets__prophet_code1 = _node_data_prophets_1.prophet_code) === null || _node_data_prophets__prophet_code1 === void 0 ? void 0 : _node_data_prophets__prophet_code1.trim(),\n                        \"Address line 1\": node === null || node === void 0 ? void 0 : (_node_data15 = node.data) === null || _node_data15 === void 0 ? void 0 : _node_data15.address_line_1,\n                        \"Address line 2\": node === null || node === void 0 ? void 0 : (_node_data16 = node.data) === null || _node_data16 === void 0 ? void 0 : _node_data16.address_line_2,\n                        \"Address line 3\": node === null || node === void 0 ? void 0 : (_node_data17 = node.data) === null || _node_data17 === void 0 ? void 0 : _node_data17.address_line_3,\n                        \"Address line 4\": node === null || node === void 0 ? void 0 : (_node_data18 = node.data) === null || _node_data18 === void 0 ? void 0 : _node_data18.address_line_4,\n                        \"Postal code\": node === null || node === void 0 ? void 0 : (_node_data19 = node.data) === null || _node_data19 === void 0 ? void 0 : _node_data19.postal_code,\n                        status: node === null || node === void 0 ? void 0 : (_node_data20 = node.data) === null || _node_data20 === void 0 ? void 0 : _node_data20.status,\n                        id: node === null || node === void 0 ? void 0 : (_node_data21 = node.data) === null || _node_data21 === void 0 ? void 0 : _node_data21.id\n                    };\n                    filteredData.push(filteredExportData);\n                }\n            });\n        }\n        if (filteredData.length === 0) {\n            react_toastify__WEBPACK_IMPORTED_MODULE_15__.toast.error(\"No filtered data to export.\", {\n                position: \"top-right\",\n                autoClose: 5000,\n                hideProgressBar: false,\n                closeOnClick: true,\n                pauseOnHover: false,\n                draggable: true,\n                progress: undefined,\n                theme: \"light\"\n            });\n        } else {\n            if (supplierCheckedValue) {\n                if (true) {\n                    const allStatesData = [\n                        ulpFilData,\n                        supplierActiveData,\n                        roleData,\n                        sendacGroupData,\n                        bankAc,\n                        senBnk,\n                        contactData,\n                        organizationData,\n                        organizationRoleData,\n                        sheetSupplierId\n                    ];\n                    exportInternal_response = await (0,_utils_exportExcel__WEBPACK_IMPORTED_MODULE_22__[\"default\"])(allStatesData, true, token, company, userData, prophetId, userData === null || userData === void 0 ? void 0 : userData.email, \"\");\n                }\n                if (!isUnExportable) {\n                    const allStatesData = [\n                        multipleFilterISSData,\n                        roleData\n                    ];\n                    export_ISSresponse = await (0,_utils_exportExcel__WEBPACK_IMPORTED_MODULE_22__[\"default\"])(allStatesData, false, token, company, userData, prophetId, userData === null || userData === void 0 ? void 0 : userData.email, \"\");\n                }\n                setEmailStatusPopup(true);\n                if (export_ISSresponse && exportInternal_response) {\n                    setPopUpMessage(\"Email sent to Finance Department and ISS admin\");\n                    setISSExportSuccess(true);\n                    setInternalExportSuccess(true);\n                } else if (exportInternal_response && isUnExportable) {\n                    setPopUpMessage(\"Email sent to Finance Department, but suppliers \".concat(unExportableSuppliernames, \" not exported as Hauliers and Expense roles not allowed to be exported to ISS\"));\n                    setInternalExportSuccess(true);\n                } else if (export_ISSresponse) {\n                    setISSExportSuccess(true);\n                    setPopUpMessage(\"Email sent to ISS Admin Team, but not to Finance Department\");\n                } else {\n                    setPopUpMessage(\"Email not sent to either Finance Department or ISS Admin Team\");\n                }\n            }\n        }\n        setSelectedExportType(\"\");\n        gridRef.current.api.deselectAll();\n    };\n    const [supplierActiveData, setSupplierActiveData] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([\n        [\n            \"sendac (Supplier file)\"\n        ]\n    ]);\n    const [roleData, setRoleData] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([\n        [\n            \"sendacrole (Supplier role file)\"\n        ]\n    ]);\n    const [contactData, setContactData] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([\n        [\n            \"contactdet (Supplier personnel contact details)\"\n        ]\n    ]);\n    const [organizationData, setOrganizationData] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([\n        [\n            \"organization (Organization)\"\n        ]\n    ]);\n    const [organizationRoleData, setOrganizationRoleData] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([\n        [\n            \"orgroles (Organization Roles)\"\n        ]\n    ]);\n    const [sendacGroupData, setSendacGroupData] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([\n        [\n            \"sendacgroup (Sendac group file)\"\n        ]\n    ]);\n    const [bankAc, setBankAc] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([\n        [\n            \"bankac (Bank account details table)\"\n        ]\n    ]);\n    const [multipleFilterISSData, setMultipleFilterISSData] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([\n        [\n            \"Supplier Data\"\n        ]\n    ]);\n    const [senBnk, setSenBnk] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([\n        [\n            \"senbnk (Supplier bank link table)\"\n        ]\n    ]);\n    const [ulpFilData, setUlpFilData] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([\n        [\n            \"UlpFil\"\n        ]\n    ]);\n    const [sheetSupplierId, setSheetSupplierId] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([\n        [\n            \"Supplier Id\"\n        ]\n    ]);\n    const handleGridReady = (params)=>{\n        params.api.setColumnDefs(columnDefs);\n    };\n    const extractContacts = (supplierCode, contactsJsonStr, supplierName)=>{\n        try {\n            const contacts = contactsJsonStr ? JSON.parse(contactsJsonStr) : [];\n            if (Array.isArray(contacts)) {\n                return contacts.map((contact)=>({\n                        \"Supplier code\": supplierCode ? supplierCode : \"\",\n                        \"Contact ID\": \"\",\n                        Name: supplierName || \"\",\n                        \"Email Address\": contact.email_id || \"\",\n                        \"Telephone number\": contact.telephone || \"\",\n                        \"Cell phone number\": \"\",\n                        \"Fax number\": \"\",\n                        \"Instant Message\": \"\",\n                        \"Physical Address\": \"\",\n                        \"Postal Address\": \"\",\n                        \"Row verision\": \"\",\n                        \"Created timestamp\": \"\"\n                    }));\n            }\n        } catch (error) {\n            console.error(\"Error parsing contacts_json:\", error);\n        }\n        return [\n            {\n                \"Contact ID\": \"\",\n                Name: supplierName || \"\",\n                \"Email Address\": \"\",\n                \"Telephone number\": \"\",\n                \"Cell phone number\": \"\",\n                \"Fax number\": \"\",\n                \"Instant Message\": \"\",\n                \"Physical Address\": \"\",\n                \"Postal Address\": \"\",\n                \"Row verision\": \"\",\n                \"Created timestamp\": \"\"\n            }\n        ];\n    };\n    const extractSendacGroup = (sendacGroupJson)=>{\n        try {\n            const sendacGroups = sendacGroupJson ? JSON.parse(sendacGroupJson) : [];\n            if (Array.isArray(sendacGroups)) {\n                return sendacGroups === null || sendacGroups === void 0 ? void 0 : sendacGroups.map((group)=>({\n                        \"Supplier group\": \"\",\n                        Description: (group === null || group === void 0 ? void 0 : group.created_by) ? group === null || group === void 0 ? void 0 : group.label : \"\"\n                    }));\n            }\n        } catch (error) {\n            console.error(\"Error parsing contacts_json:\", error);\n        }\n    };\n    const multipleSendRoleOnRoleNums = (data, role_num)=>{\n        var _data_role;\n        const roleNums = role_num === null || role_num === void 0 ? void 0 : role_num.split(\",\").map((num)=>num.trim());\n        const roleNames = data === null || data === void 0 ? void 0 : (_data_role = data.role) === null || _data_role === void 0 ? void 0 : _data_role.split(\",\").map((name)=>name.trim());\n        return roleNums.map((num, index)=>{\n            var _data_prophets_, _data_prophets_1;\n            return {\n                Sendacroleid: \"\",\n                \"Supplier code\": (data === null || data === void 0 ? void 0 : (_data_prophets_ = data.prophets[0]) === null || _data_prophets_ === void 0 ? void 0 : _data_prophets_.prophet_code) ? data.prophets[0].prophet_code.trim() : \"\",\n                Description: (data === null || data === void 0 ? void 0 : (_data_prophets_1 = data.prophets[0]) === null || _data_prophets_1 === void 0 ? void 0 : _data_prophets_1.prophet_code) ? data.prophets[0].prophet_code.trim() : \"\",\n                \"Supplier Code Supplier Name\": data.company_name,\n                Type: num,\n                \"Type Description\": roleNames[index],\n                \"Supplier code Global gap number\": data === null || data === void 0 ? void 0 : data.global_gap_number,\n                \"Created timestamp\": \"\",\n                Active: 1\n            };\n        });\n    };\n    function getGLCode(internal_ledger_code) {\n        if (internal_ledger_code) {\n            return internal_ledger_code;\n        } else return \"\";\n    }\n    const [incompleteToast, setIncompleteToast] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        if (incompleteToast) {\n            react_toastify__WEBPACK_IMPORTED_MODULE_15__.toast.error(\"Supplier details are incomplete.\", {\n                position: \"top-right\",\n                autoClose: 1000,\n                hideProgressBar: false,\n                closeOnClick: true,\n                pauseOnHover: false,\n                draggable: true,\n                progress: undefined,\n                theme: \"light\"\n            });\n        }\n    }, [\n        incompleteToast\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        if (containsCancelledSupplier) {\n            react_toastify__WEBPACK_IMPORTED_MODULE_15__.toast.error(\"Cannot export cancelled supplier.\", {\n                position: \"top-right\",\n                autoClose: 1000,\n                hideProgressBar: false,\n                closeOnClick: true,\n                pauseOnHover: false,\n                draggable: true,\n                progress: undefined,\n                theme: \"light\"\n            });\n        }\n    }, [\n        containsCancelledSupplier\n    ]);\n    const handleCheckboxEvent = (event)=>{\n        const getRowData = event.data;\n        const isSelected = event.node.selected;\n        const selectedRows = gridRef.current.api.getSelectedRows();\n        const prophet_id = getRowData.prophets[0].prophet_id;\n        setProphetId(prophet_id);\n        const extractedValues = selectedRows.map((param)=>{\n            let { status, isEmergencyRequest, General } = param;\n            return {\n                status,\n                isEmergencyRequest,\n                General\n            };\n        });\n        const exportDisabled = extractedValues.some((param)=>{\n            let { status, isEmergencyRequest, General } = param;\n            return !(status === \"Completed\" || status === \"Exported\" || isEmergencyRequest && General === \"Complete\");\n        });\n        const canExport = extractedValues.every((param)=>{\n            let { isEmergencyRequest } = param;\n            return !(!isEmergencyRequest && ((userData === null || userData === void 0 ? void 0 : userData.role_id) != 1 || (userData === null || userData === void 0 ? void 0 : userData.role_id) != 2 || (userData === null || userData === void 0 ? void 0 : userData.role_id) != 5 || (userData === null || userData === void 0 ? void 0 : userData.role_id) != 6));\n        });\n        const isExportableBasedOnCodeUnique = selectedRows.every((row)=>{\n            var _row_prophets_, _row_prophets_1;\n            const codeCount = row === null || row === void 0 ? void 0 : (_row_prophets_ = row.prophets[0]) === null || _row_prophets_ === void 0 ? void 0 : _row_prophets_.code_count;\n            const prophetCode = row === null || row === void 0 ? void 0 : (_row_prophets_1 = row.prophets[0]) === null || _row_prophets_1 === void 0 ? void 0 : _row_prophets_1.prophet_code;\n            if (codeCount && codeCount > 1 && prophetCode && prophetCode !== \"\") {\n                return false;\n            } else if (codeCount && codeCount == 1 && prophetCode && prophetCode !== \"\") {\n                return true;\n            }\n            return false;\n        });\n        const doesContainCancelledSupplier = selectedRows.some((row)=>row.status === \"Cancelled\");\n        const isExportValid = selectedRows.every((row)=>{\n            var _row_prophets_, _row_prophets_1, _row_roleIds, _row_roleIds1;\n            const supCode = row === null || row === void 0 ? void 0 : (_row_prophets_ = row.prophets[0]) === null || _row_prophets_ === void 0 ? void 0 : _row_prophets_.prophet_code;\n            const prophet_id = row === null || row === void 0 ? void 0 : (_row_prophets_1 = row.prophets[0]) === null || _row_prophets_1 === void 0 ? void 0 : _row_prophets_1.prophet_id;\n            const isSupplierAccount = (row === null || row === void 0 ? void 0 : (_row_roleIds = row.roleIds) === null || _row_roleIds === void 0 ? void 0 : _row_roleIds.includes(1)) || (row === null || row === void 0 ? void 0 : (_row_roleIds1 = row.roleIds) === null || _row_roleIds1 === void 0 ? void 0 : _row_roleIds1.includes(6));\n            let currency = (row === null || row === void 0 ? void 0 : row.currency) == \"$\" ? \"\\\\\".concat(row === null || row === void 0 ? void 0 : row.currency) : row === null || row === void 0 ? void 0 : row.currency;\n            let actualCurr;\n            if (currency && currency == \"Not Entered\") {\n                actualCurr = \"\";\n            } else {\n                actualCurr = currency;\n            }\n            let isValid = true;\n            if (isSupplierAccount) {\n                if (prophet_id == 1) {\n                    let regexPattern;\n                    regexPattern = new RegExp(\"^[A-Z0-9]{4}[A-Z0145678]\".concat(actualCurr, \"$\"));\n                    // let regexPattern = new RegExp(`^([A-Z0]{5})${actualCurr}$`);\n                    isValid = regexPattern.test(supCode);\n                } else if (prophet_id == 2) {\n                    let regexPattern;\n                    regexPattern = new RegExp(\"^([A-Z0]{4})9\".concat(actualCurr, \"$\"));\n                    // let regexPattern = new RegExp(`^([A-Z0]{4})9${actualCurr}$`);\n                    isValid = regexPattern.test(supCode);\n                } else if (prophet_id == 3) {\n                    let regexPattern = new RegExp(\"^[A-Z0-9]{4}[A-Z01345678][A-Z0-9]*$\");\n                    isValid = regexPattern.test(supCode) && supCode.length == 6;\n                } else if (prophet_id == 4) {\n                    let regexPattern;\n                    regexPattern = new RegExp(\"^([A-Z0]{4})2\".concat(actualCurr, \"$\"));\n                    isValid = regexPattern.test(supCode);\n                }\n            }\n            return isValid;\n        });\n        if (selectedRows.length > 0) {\n            if (!canExport && (userData === null || userData === void 0 ? void 0 : userData.role_id) != 1 && (userData === null || userData === void 0 ? void 0 : userData.role_id) != 2 && (userData === null || userData === void 0 ? void 0 : userData.role_id) != 5 && (userData === null || userData === void 0 ? void 0 : userData.role_id) != 6) {\n                setExportDisabled(true);\n            } else if (doesContainCancelledSupplier) {\n                setContainsCancelledSupplier(true);\n                setExportDisabled(true);\n            } else if (!isExportableBasedOnCodeUnique) {\n                setSupplierUniqueCodeToast(true);\n                setExportDisabled(true);\n            } else if (!isExportValid) {\n                setSupplierCodeValid(false);\n                setExportDisabled(true);\n            } else {\n                setExportDisabled(exportDisabled);\n            }\n        } else {\n            setExportDisabled(true);\n        }\n        let isUnExportableToISS = false;\n        let supplierNames = [];\n        selectedRows.forEach((row)=>{\n            var _row_roleIds, _row_roleIds1, _row_roleIds2, _row_roleIds3;\n            if (!(row === null || row === void 0 ? void 0 : (_row_roleIds = row.roleIds) === null || _row_roleIds === void 0 ? void 0 : _row_roleIds.includes(1)) && !(row === null || row === void 0 ? void 0 : (_row_roleIds1 = row.roleIds) === null || _row_roleIds1 === void 0 ? void 0 : _row_roleIds1.includes(2)) && !(row === null || row === void 0 ? void 0 : (_row_roleIds2 = row.roleIds) === null || _row_roleIds2 === void 0 ? void 0 : _row_roleIds2.includes(3)) && !(row === null || row === void 0 ? void 0 : (_row_roleIds3 = row.roleIds) === null || _row_roleIds3 === void 0 ? void 0 : _row_roleIds3.includes(4))) {\n                isUnExportableToISS = true;\n                supplierNames.push(row.company_name);\n            }\n        });\n        const supplierNamesString = supplierNames.join(\", \");\n        setIsUnExportable(isUnExportableToISS);\n        setUnExportableSupplierNames(supplierNamesString);\n        if ((getRowData === null || getRowData === void 0 ? void 0 : getRowData.status) == \"Completed\" || (getRowData === null || getRowData === void 0 ? void 0 : getRowData.status) == \"Exported\" || (getRowData === null || getRowData === void 0 ? void 0 : getRowData.isEmergencyRequest) && getRowData.status != \"Cancelled\" && (getRowData === null || getRowData === void 0 ? void 0 : getRowData.General) === \"Complete\") {\n            var _getRowData_roleIds, _getRowData_roleIds1, _getRowData_roleIds2, _getRowData_roleIds3, _getRowData_prophets__prophet_code, _getRowData_prophets_, _getRowData_company_name, _getRowData_prophets__prophet_code1, _getRowData_prophets_1, _getRowData_distribution_points_json, _getRowData_prophets_2, _getRowData_prophets_3, _getRowData_prophets__prophet_code2, _getRowData_prophets_4, _getRowData_prophets_5, _getRowData_prophets_6, _getRowData_distribution_points_json1, _getRowData_distribution_points_json2, _getRowData_prophets__prophet_code3, _getRowData_prophets_7, _getRowData_prophets__prophet_code4, _getRowData_prophets_8, _getRowData_prophets_9, _getRowData_prophets__prophet_code5, _getRowData_prophets_10, _getRowData_prophets_11, _getRowData_prophets__prophet_code6, _getRowData_prophets_12;\n            let regional_cert = \"\";\n            if ((getRowData === null || getRowData === void 0 ? void 0 : (_getRowData_roleIds = getRowData.roleIds) === null || _getRowData_roleIds === void 0 ? void 0 : _getRowData_roleIds.includes(2)) || (getRowData === null || getRowData === void 0 ? void 0 : (_getRowData_roleIds1 = getRowData.roleIds) === null || _getRowData_roleIds1 === void 0 ? void 0 : _getRowData_roleIds1.includes(3))) {\n                if (getRowData.country_code == \"UK\") {\n                    regional_cert = getRowData === null || getRowData === void 0 ? void 0 : getRowData.red_tractor;\n                } else if (getRowData.country_code == \"ZA\") {\n                    regional_cert = getRowData === null || getRowData === void 0 ? void 0 : getRowData.puc_code;\n                } else if (getRowData.country_code == \"CL\") {\n                    regional_cert = getRowData === null || getRowData === void 0 ? void 0 : getRowData.chile_certificate_number;\n                }\n            }\n            let currencyId = \"\";\n            let currencyName = \"\";\n            if ((getRowData === null || getRowData === void 0 ? void 0 : (_getRowData_roleIds2 = getRowData.roleIds) === null || _getRowData_roleIds2 === void 0 ? void 0 : _getRowData_roleIds2.includes(1)) || (getRowData === null || getRowData === void 0 ? void 0 : (_getRowData_roleIds3 = getRowData.roleIds) === null || _getRowData_roleIds3 === void 0 ? void 0 : _getRowData_roleIds3.includes(6))) {\n                currencyId = getRowData === null || getRowData === void 0 ? void 0 : getRowData.currency_id;\n                currencyName = getRowData === null || getRowData === void 0 ? void 0 : getRowData.currency_name;\n            } else {\n                currencyId = 1;\n                currencyName = \"Sterling\";\n            }\n            function getCorrespondingUserLookup(curr) {\n                if (curr == \"GBP\") {\n                    return \"GBPBACS\";\n                } else if (curr == \"EUR\") {\n                    return \"EUROSEPA\";\n                } else if (curr == \"USD\") {\n                    return \"USDPRIORITY\";\n                } else {\n                    return \"\";\n                }\n            }\n            console.log(\"get row data\", getRowData);\n            var _getRowData_edi;\n            const filteredISSExportData = {\n                \"Supplier Active\": \"N/A\",\n                \"Supplier code\": getRowData === null || getRowData === void 0 ? void 0 : (_getRowData_prophets_ = getRowData.prophets[0]) === null || _getRowData_prophets_ === void 0 ? void 0 : (_getRowData_prophets__prophet_code = _getRowData_prophets_.prophet_code) === null || _getRowData_prophets__prophet_code === void 0 ? void 0 : _getRowData_prophets__prophet_code.trim(),\n                \"EDI Partner\": \"N/A\",\n                \"Supplier name\": getRowData === null || getRowData === void 0 ? void 0 : (_getRowData_company_name = getRowData.company_name) === null || _getRowData_company_name === void 0 ? void 0 : _getRowData_company_name.trim(),\n                \"EDI ANA number\": (_getRowData_edi = getRowData === null || getRowData === void 0 ? void 0 : getRowData.edi) !== null && _getRowData_edi !== void 0 ? _getRowData_edi : \"N/A\",\n                \"Producer (supplier)\": \"N/A\",\n                \"Department number\": \"N/A\",\n                \"Currency number\": (getRowData === null || getRowData === void 0 ? void 0 : getRowData.currency_id) ? getRowData === null || getRowData === void 0 ? void 0 : getRowData.currency_id : 1,\n                \"Global gap number\": getRowData === null || getRowData === void 0 ? void 0 : getRowData.global_gap_number,\n                \"Grower group\": \"N/A\",\n                \"Defra county number\": \"N/A\",\n                \"Date start\": \"N/A\",\n                \"Date end\": \"N/A\",\n                \"Organic Cert\": getRowData === null || getRowData === void 0 ? void 0 : getRowData.organic_certificate_number,\n                \"Regional Cert\": regional_cert,\n                \"Head office\": getRowData === null || getRowData === void 0 ? void 0 : (_getRowData_prophets_1 = getRowData.prophets[0]) === null || _getRowData_prophets_1 === void 0 ? void 0 : (_getRowData_prophets__prophet_code1 = _getRowData_prophets_1.prophet_code) === null || _getRowData_prophets__prophet_code1 === void 0 ? void 0 : _getRowData_prophets__prophet_code1.trim(),\n                \"Country Code\": getRowData === null || getRowData === void 0 ? void 0 : getRowData.country_code,\n                \"Distribution point for supplier\": (getRowData === null || getRowData === void 0 ? void 0 : (_getRowData_distribution_points_json = getRowData.distribution_points_json) === null || _getRowData_distribution_points_json === void 0 ? void 0 : _getRowData_distribution_points_json.length) > 0 ? getRowData === null || getRowData === void 0 ? void 0 : getRowData.distribution_points_json[0].from_dp : \"N/A\",\n                \"Bool 2\": \"N/A\",\n                \"Bool 3\": \"N/A\",\n                \"Address line 1\": getRowData === null || getRowData === void 0 ? void 0 : getRowData.address_line_1,\n                \"Address line 2\": getRowData === null || getRowData === void 0 ? void 0 : getRowData.address_line_2,\n                \"Address line 3\": getRowData === null || getRowData === void 0 ? void 0 : getRowData.address_line_3,\n                \"Address line 4\": getRowData === null || getRowData === void 0 ? void 0 : getRowData.address_line_4,\n                \"Post code\": getRowData === null || getRowData === void 0 ? void 0 : getRowData.postal_code,\n                \"Currency Number\": (getRowData === null || getRowData === void 0 ? void 0 : getRowData.currency_id) ? getRowData === null || getRowData === void 0 ? void 0 : getRowData.currency_id : 1,\n                \"Bank general ledger code\": (getRowData === null || getRowData === void 0 ? void 0 : getRowData.iss_ledger_code) ? getRowData === null || getRowData === void 0 ? void 0 : getRowData.iss_ledger_code : \"12200\",\n                \"Bank general ledger code Currency number if bank\": (getRowData === null || getRowData === void 0 ? void 0 : getRowData.currency_id) ? getRowData === null || getRowData === void 0 ? void 0 : getRowData.currency_id : 1,\n                \"Settlement days\": getRowData === null || getRowData === void 0 ? void 0 : getRowData.payment_terms,\n                \"Department Number\": (getRowData === null || getRowData === void 0 ? void 0 : (_getRowData_prophets_2 = getRowData.prophets[0]) === null || _getRowData_prophets_2 === void 0 ? void 0 : _getRowData_prophets_2.prophet_id) == 1 ? 1 : (getRowData === null || getRowData === void 0 ? void 0 : (_getRowData_prophets_3 = getRowData.prophets[0]) === null || _getRowData_prophets_3 === void 0 ? void 0 : _getRowData_prophets_3.prophet_id) == 2 ? 9 : (getRowData === null || getRowData === void 0 ? void 0 : getRowData.prophets[0].prophet_id) == 3 ? 3 : (getRowData === null || getRowData === void 0 ? void 0 : getRowData.prophets[0].prophet_id) == 4 ? 2 : \"N/A\",\n                \"Area Number\": \"1\",\n                Vatable: (getRowData === null || getRowData === void 0 ? void 0 : getRowData.vatable) != null ? (getRowData === null || getRowData === void 0 ? void 0 : getRowData.vatable) ? \"1\" : \"0\" : \"0\",\n                Buyer: \"1\",\n                \"Billing type\": \"0\",\n                \"Payment type\": (getRowData === null || getRowData === void 0 ? void 0 : getRowData.payment_type) ? getRowData === null || getRowData === void 0 ? void 0 : getRowData.payment_type : 2,\n                \"Expense general ledger code\": \"N/A\",\n                \"Authorise on register\": \"N/A\",\n                \"Use % authorise rule\": 5,\n                \"User text 1\": \"N/A\",\n                \"Mandatory altfil on service jobs\": \"N/A\",\n                \"Organization ID\": \"N/A\",\n                id: getRowData === null || getRowData === void 0 ? void 0 : getRowData.id,\n                isEmergencyAndFinanceNotComplete: getRowData === null || getRowData === void 0 ? void 0 : getRowData.isEmergencyAndFinanceNotComplete\n            };\n            var _getRowData_edi1;\n            const newSupplierActiveData = {\n                \"Supplier Active\": (getRowData === null || getRowData === void 0 ? void 0 : getRowData.isActive) ? 1 : 0,\n                \"Haulage cube local\": \"\",\n                \"Haulage cube name\": \"\",\n                \"update guesstimates type\": 1,\n                \"Organization ID\": \"\",\n                \"Vat number 1\": getRowData === null || getRowData === void 0 ? void 0 : getRowData.vat_number,\n                \"Organic cert\": getRowData === null || getRowData === void 0 ? void 0 : getRowData.organic_certificate_number,\n                \"Regional cert\": regional_cert,\n                \"Global gap number\": getRowData === null || getRowData === void 0 ? void 0 : getRowData.global_gap_number,\n                \"Enforce department\": \"\",\n                \"Sendac Group\": (getRowData === null || getRowData === void 0 ? void 0 : getRowData.supplier_group) ? JSON.parse(getRowData === null || getRowData === void 0 ? void 0 : getRowData.supplier_group)[0].value : \"\",\n                \"Supplier code\": getRowData === null || getRowData === void 0 ? void 0 : (_getRowData_prophets_4 = getRowData.prophets[0]) === null || _getRowData_prophets_4 === void 0 ? void 0 : (_getRowData_prophets__prophet_code2 = _getRowData_prophets_4.prophet_code) === null || _getRowData_prophets__prophet_code2 === void 0 ? void 0 : _getRowData_prophets__prophet_code2.trim(),\n                \"Supplier name\": getRowData.company_name,\n                \"Supplier type\": getRowData === null || getRowData === void 0 ? void 0 : getRowData.supplier_type,\n                \"User Lookup 2\": \"\",\n                \"Address Line 1\": getRowData === null || getRowData === void 0 ? void 0 : getRowData.address_line_1,\n                \"Address Line 2\": getRowData === null || getRowData === void 0 ? void 0 : getRowData.address_line_2,\n                \"Address Line 3\": getRowData === null || getRowData === void 0 ? void 0 : getRowData.address_line_3,\n                \"Address Line 4\": getRowData === null || getRowData === void 0 ? void 0 : getRowData.address_line_4,\n                \"Post code\": getRowData === null || getRowData === void 0 ? void 0 : getRowData.postal_code,\n                \"Country code\": getRowData === null || getRowData === void 0 ? void 0 : getRowData.country_code,\n                \"Payee supplier code\": \"\",\n                \"Invoice supplier\": \"\",\n                \"Head office\": \"\",\n                \"Settlement days\": getRowData === null || getRowData === void 0 ? void 0 : getRowData.payment_terms,\n                \"Bank general ledger code Currency number if bank\": currencyId,\n                \"Currency number\": currencyId,\n                \"Currency number Currency name\": currencyName,\n                \"Bank general ledger code\": getGLCode(getRowData === null || getRowData === void 0 ? void 0 : getRowData.internal_ledger_code),\n                \"payment type\": getRowData === null || getRowData === void 0 ? void 0 : getRowData.payment_type,\n                \"Payment type name\": getRowData === null || getRowData === void 0 ? void 0 : getRowData.payment_type_name,\n                \"country code\": getRowData === null || getRowData === void 0 ? void 0 : getRowData.country_code,\n                Vatable: (getRowData === null || getRowData === void 0 ? void 0 : getRowData.vatable) != null ? (getRowData === null || getRowData === void 0 ? void 0 : getRowData.vatable) ? \"1\" : \"0\" : \"0\",\n                \"vatable desc\": (getRowData === null || getRowData === void 0 ? void 0 : getRowData.vatable) != null ? (getRowData === null || getRowData === void 0 ? void 0 : getRowData.vatable) ? \"Vatable\" : \"None vatable\" : \"None vatable\",\n                \"Area Number\": (getRowData === null || getRowData === void 0 ? void 0 : getRowData.prophets[0].prophet_id) == 3 || (getRowData === null || getRowData === void 0 ? void 0 : getRowData.prophets[0].prophet_id) == 4 ? 1 : 7,\n                Buyer: 1,\n                \"Multiple lot indicator\": \"0\",\n                \"multiple lot indicator desc\": \"By Lot\",\n                \"Generate Pallet Loading Plan\": \"\",\n                \"Distribution point for supplier\": 6,\n                \"Payment terms\": \"\",\n                \"Department Number\": (getRowData === null || getRowData === void 0 ? void 0 : (_getRowData_prophets_5 = getRowData.prophets[0]) === null || _getRowData_prophets_5 === void 0 ? void 0 : _getRowData_prophets_5.prophet_id) == 1 ? 1 : (getRowData === null || getRowData === void 0 ? void 0 : (_getRowData_prophets_6 = getRowData.prophets[0]) === null || _getRowData_prophets_6 === void 0 ? void 0 : _getRowData_prophets_6.prophet_id) == 2 ? 9 : (getRowData === null || getRowData === void 0 ? void 0 : getRowData.prophets[0].prophet_id) == 3 ? 3 : (getRowData === null || getRowData === void 0 ? void 0 : getRowData.prophets[0].prophet_id) == 4 ? 2 : \"\",\n                \"Allow credit rebates\": \"\",\n                \"Alternative DP for supplier\": 1,\n                \"Actual posting stops purchase charges\": \"\",\n                \"Authorise on register\": \"\",\n                \"User text 1\": \"\",\n                \"User lookup 1\": (getRowData === null || getRowData === void 0 ? void 0 : getRowData.prophets[0].prophet_id) == 3 || (getRowData === null || getRowData === void 0 ? void 0 : getRowData.prophets[0].prophet_id) == 4 ? getCorrespondingUserLookup(getRowData === null || getRowData === void 0 ? void 0 : getRowData.currency_code) : \"\",\n                \"Receive orders from edi\": \"\",\n                \"Send invoices from edi\": \"\",\n                \"Send orders from edi\": \"\",\n                \"EDI partner\": (getRowData === null || getRowData === void 0 ? void 0 : getRowData.prophets[0].prophet_id) == 3 || (getRowData === null || getRowData === void 0 ? void 0 : getRowData.prophets[0].prophet_id) == 4 ? 2000 : \"\",\n                \"Generic code\": (getRowData === null || getRowData === void 0 ? void 0 : getRowData.prophets[0].prophet_id) == 3 || (getRowData === null || getRowData === void 0 ? void 0 : getRowData.prophets[0].prophet_id) == 4 ? \"STOCK\" : \"\",\n                \"EDI ANA number\": (_getRowData_edi1 = getRowData === null || getRowData === void 0 ? void 0 : getRowData.edi) !== null && _getRowData_edi1 !== void 0 ? _getRowData_edi1 : \"N/A\",\n                \"User % authorize rule\": 5,\n                FromDP: (getRowData === null || getRowData === void 0 ? void 0 : (_getRowData_distribution_points_json1 = getRowData.distribution_points_json) === null || _getRowData_distribution_points_json1 === void 0 ? void 0 : _getRowData_distribution_points_json1.length) > 0 ? getRowData.distribution_points_json[0].from_dp || \"\" : \"\"\n            };\n            let UlpFil = {};\n            if ((getRowData === null || getRowData === void 0 ? void 0 : (_getRowData_distribution_points_json2 = getRowData.distribution_points_json) === null || _getRowData_distribution_points_json2 === void 0 ? void 0 : _getRowData_distribution_points_json2.length) > 0 && ((getRowData === null || getRowData === void 0 ? void 0 : getRowData.distribution_points_json[0].from_dp) === null || (getRowData === null || getRowData === void 0 ? void 0 : getRowData.distribution_points_json[0].from_dp) === undefined)) {\n                var _getRowData_distribution_points_json3, _getRowData_prophets__prophet_code7, _getRowData_prophets_13, _getRowData_distribution_points_json4, _getRowData_distribution_points_json_;\n                UlpFil = {\n                    \"Distribution point\": \"\",\n                    Description: (getRowData === null || getRowData === void 0 ? void 0 : (_getRowData_distribution_points_json3 = getRowData.distribution_points_json) === null || _getRowData_distribution_points_json3 === void 0 ? void 0 : _getRowData_distribution_points_json3.length) > 0 && ((getRowData === null || getRowData === void 0 ? void 0 : getRowData.distribution_points_json[0].from_dp) === null || (getRowData === null || getRowData === void 0 ? void 0 : getRowData.distribution_points_json[0].from_dp) === undefined) ? getRowData === null || getRowData === void 0 ? void 0 : getRowData.distribution_points_json[0].name : \"\",\n                    \"Service Supplier Code\": ((getRowData === null || getRowData === void 0 ? void 0 : getRowData.distribution_points_json[0].from_dp) === null || (getRowData === null || getRowData === void 0 ? void 0 : getRowData.distribution_points_json[0].from_dp) === undefined) && (getRowData === null || getRowData === void 0 ? void 0 : (_getRowData_prophets_13 = getRowData.prophets[0]) === null || _getRowData_prophets_13 === void 0 ? void 0 : (_getRowData_prophets__prophet_code7 = _getRowData_prophets_13.prophet_code) === null || _getRowData_prophets__prophet_code7 === void 0 ? void 0 : _getRowData_prophets__prophet_code7.trim()),\n                    \"Default expected stock status\": ((getRowData === null || getRowData === void 0 ? void 0 : getRowData.distribution_points_json[0].from_dp) === null || (getRowData === null || getRowData === void 0 ? void 0 : getRowData.distribution_points_json[0].from_dp) === undefined) && \"0\",\n                    \"Default received stock status\": ((getRowData === null || getRowData === void 0 ? void 0 : getRowData.distribution_points_json[0].from_dp) === null || (getRowData === null || getRowData === void 0 ? void 0 : getRowData.distribution_points_json[0].from_dp) === undefined) && \"0\",\n                    \"Pallets in packhouse\": ((getRowData === null || getRowData === void 0 ? void 0 : getRowData.distribution_points_json[0].from_dp) === null || (getRowData === null || getRowData === void 0 ? void 0 : getRowData.distribution_points_json[0].from_dp) === undefined) && \"0\",\n                    \"Default haulier\": ((getRowData === null || getRowData === void 0 ? void 0 : getRowData.distribution_points_json[0].from_dp) === null || (getRowData === null || getRowData === void 0 ? void 0 : getRowData.distribution_points_json[0].from_dp) === undefined) && \"ZZZZZ\",\n                    \"Default expected location id\": ((getRowData === null || getRowData === void 0 ? void 0 : getRowData.distribution_points_json[0].from_dp) === null || (getRowData === null || getRowData === void 0 ? void 0 : getRowData.distribution_points_json[0].from_dp) === undefined) && 21,\n                    \"Default receiving location id\": ((getRowData === null || getRowData === void 0 ? void 0 : getRowData.distribution_points_json[0].from_dp) === null || (getRowData === null || getRowData === void 0 ? void 0 : getRowData.distribution_points_json[0].from_dp) === undefined) && 21,\n                    \"Packhouse location id\": ((getRowData === null || getRowData === void 0 ? void 0 : getRowData.distribution_points_json[0].from_dp) === null || (getRowData === null || getRowData === void 0 ? void 0 : getRowData.distribution_points_json[0].from_dp) === undefined) && 21,\n                    \"Default pick location id\": ((getRowData === null || getRowData === void 0 ? void 0 : getRowData.distribution_points_json[0].from_dp) === null || (getRowData === null || getRowData === void 0 ? void 0 : getRowData.distribution_points_json[0].from_dp) === undefined) && 21,\n                    \"Despatch location id\": ((getRowData === null || getRowData === void 0 ? void 0 : getRowData.distribution_points_json[0].from_dp) === null || (getRowData === null || getRowData === void 0 ? void 0 : getRowData.distribution_points_json[0].from_dp) === undefined) && 21,\n                    \"Default waste location id\": ((getRowData === null || getRowData === void 0 ? void 0 : getRowData.distribution_points_json[0].from_dp) === null || (getRowData === null || getRowData === void 0 ? void 0 : getRowData.distribution_points_json[0].from_dp) === undefined) && 21,\n                    \"Default pre-pick location id\": ((getRowData === null || getRowData === void 0 ? void 0 : getRowData.distribution_points_json[0].from_dp) === null || (getRowData === null || getRowData === void 0 ? void 0 : getRowData.distribution_points_json[0].from_dp) === undefined) && 21,\n                    \"Default returns location id\": ((getRowData === null || getRowData === void 0 ? void 0 : getRowData.distribution_points_json[0].from_dp) === null || (getRowData === null || getRowData === void 0 ? void 0 : getRowData.distribution_points_json[0].from_dp) === undefined) && 21,\n                    Address: \"\",\n                    \"Service supplier code\": \"\",\n                    \"EDI Reference Code\": \"\",\n                    \"EDI ANA Code\": \"\",\n                    \"User Integer 1\": ((getRowData === null || getRowData === void 0 ? void 0 : getRowData.distribution_points_json[0].from_dp) === null || (getRowData === null || getRowData === void 0 ? void 0 : getRowData.distribution_points_json[0].from_dp) === undefined) && 1,\n                    \"Movement resource group\": \"\",\n                    \"Handheld application used\": ((getRowData === null || getRowData === void 0 ? void 0 : getRowData.distribution_points_json[0].from_dp) === null || (getRowData === null || getRowData === void 0 ? void 0 : getRowData.distribution_points_json[0].from_dp) === undefined) && \"0\",\n                    \"Pallets in procure/receiving\": ((getRowData === null || getRowData === void 0 ? void 0 : getRowData.distribution_points_json[0].from_dp) === null || (getRowData === null || getRowData === void 0 ? void 0 : getRowData.distribution_points_json[0].from_dp) === undefined) && \"0\",\n                    \"Operational depo\": \"\",\n                    \"Enabled for masterfile sending\": \"\",\n                    \"Connected registed depot\": \"\",\n                    \"EDI Transmission type of depo\": \"\",\n                    \"Container loading depo\": ((getRowData === null || getRowData === void 0 ? void 0 : getRowData.distribution_points_json[0].from_dp) === null || (getRowData === null || getRowData === void 0 ? void 0 : getRowData.distribution_points_json[0].from_dp) === undefined) && \"0\",\n                    \"Airport depot\": \"\",\n                    \"Sms notification\": \"\",\n                    Port: \"\",\n                    Dormant: \"\",\n                    Active: ((getRowData === null || getRowData === void 0 ? void 0 : getRowData.distribution_points_json[0].from_dp) === null || (getRowData === null || getRowData === void 0 ? void 0 : getRowData.distribution_points_json[0].from_dp) === undefined) && 1,\n                    \"Ingredient distribution point\": \"\",\n                    \"Show in CE\": ((getRowData === null || getRowData === void 0 ? void 0 : getRowData.distribution_points_json[0].from_dp) === null || (getRowData === null || getRowData === void 0 ? void 0 : getRowData.distribution_points_json[0].from_dp) === undefined) && 1,\n                    \"Charge direction\": \"\",\n                    \"Pallet receive time\": \"\",\n                    \"User string 3\": \"\",\n                    \"Direct DP\": (getRowData === null || getRowData === void 0 ? void 0 : (_getRowData_distribution_points_json4 = getRowData.distribution_points_json) === null || _getRowData_distribution_points_json4 === void 0 ? void 0 : _getRowData_distribution_points_json4.length) > 0 && ((getRowData === null || getRowData === void 0 ? void 0 : getRowData.distribution_points_json[0].from_dp) === null || (getRowData === null || getRowData === void 0 ? void 0 : getRowData.distribution_points_json[0].from_dp) === undefined) ? (getRowData === null || getRowData === void 0 ? void 0 : (_getRowData_distribution_points_json_ = getRowData.distribution_points_json[0]) === null || _getRowData_distribution_points_json_ === void 0 ? void 0 : _getRowData_distribution_points_json_.direct_dp) ? 1 : \"0\" : \"\",\n                    \"Include on XML\": ((getRowData === null || getRowData === void 0 ? void 0 : getRowData.distribution_points_json[0].from_dp) === null || (getRowData === null || getRowData === void 0 ? void 0 : getRowData.distribution_points_json[0].from_dp) === undefined) && \"0\"\n                };\n            }\n            // const newRoleData = {\n            //   Sendacroleid: \"\",\n            //   \"Supplier code\": getRowData?.prophets[0]?.prophet_code?.trim(),\n            //   Description: \"\",\n            //   \"Supplier Code Supplier Name\": getRowData.company_name,\n            //   Type: \"\",\n            //   \"Type Description\": getRowData?.[\"role names\"],\n            //   \"Supplier code Global gap number\": getRowData?.global_gap_number,\n            //   \"Created timestamp\": \"\",\n            //   Active: \"\",\n            // };\n            const newRoleData = multipleSendRoleOnRoleNums(getRowData, getRowData === null || getRowData === void 0 ? void 0 : getRowData.role_num);\n            const extractedSendacGroup = extractSendacGroup(getRowData === null || getRowData === void 0 ? void 0 : getRowData.supplier_group);\n            let sort_code = \"\";\n            let account_number = \"\";\n            let swiftBicCode = \"\";\n            let iban = \"\";\n            const swiftBicRegex = /^([A-Z0-9]{4}[A-Z0-9]{2}[A-Z0-9]{2}|[A-Z0-9]{4}[A-Z0-9]{2}[A-Z0-9]{2}[A-Z0-9]{3})$/;\n            if (swiftBicRegex.test(getRowData === null || getRowData === void 0 ? void 0 : getRowData.sort_bic) && (getRowData === null || getRowData === void 0 ? void 0 : getRowData.has_iban)) {\n                var _getRowData_account_number;\n                sort_code = \"000000\";\n                account_number = getRowData === null || getRowData === void 0 ? void 0 : (_getRowData_account_number = getRowData.account_number) === null || _getRowData_account_number === void 0 ? void 0 : _getRowData_account_number.slice(-8);\n                swiftBicCode = getRowData === null || getRowData === void 0 ? void 0 : getRowData.sort_bic;\n                iban = getRowData === null || getRowData === void 0 ? void 0 : getRowData.account_number;\n            } else if (!(getRowData === null || getRowData === void 0 ? void 0 : getRowData.has_iban) && swiftBicRegex.test(getRowData === null || getRowData === void 0 ? void 0 : getRowData.sort_bic)) {\n                sort_code = \"000000\";\n                account_number = getRowData === null || getRowData === void 0 ? void 0 : getRowData.account_number;\n                swiftBicCode = getRowData === null || getRowData === void 0 ? void 0 : getRowData.sort_bic;\n            } else {\n                sort_code = getRowData === null || getRowData === void 0 ? void 0 : getRowData.sort_bic;\n                account_number = getRowData === null || getRowData === void 0 ? void 0 : getRowData.account_number;\n            }\n            const bankac = {\n                \"Supplier code\": getRowData === null || getRowData === void 0 ? void 0 : (_getRowData_prophets_7 = getRowData.prophets[0]) === null || _getRowData_prophets_7 === void 0 ? void 0 : (_getRowData_prophets__prophet_code3 = _getRowData_prophets_7.prophet_code) === null || _getRowData_prophets__prophet_code3 === void 0 ? void 0 : _getRowData_prophets__prophet_code3.trim(),\n                \"Record id\": \"\",\n                \"Bank sort code\": sort_code,\n                \"Account number\": account_number,\n                \"Country code\": (getRowData === null || getRowData === void 0 ? void 0 : getRowData.country_code) == \"UK\" ? \"GB\" : getRowData === null || getRowData === void 0 ? void 0 : getRowData.country_code,\n                \"Account holder\": getRowData.company_name,\n                \"Currency number\": currencyId,\n                \"BACS currency\": getRowData === null || getRowData === void 0 ? void 0 : getRowData.bacs_currency_code,\n                \"Address Line 1\": \"\",\n                \"Address Line 2\": \"\",\n                \"BIC/Swift address\": swiftBicCode,\n                \"Internation bank reference code\": iban,\n                \"Account user id\": \"\",\n                \"Post code\": \"\"\n            };\n            const senbnk = {\n                \"Supplier code\": \"\",\n                Bankacid: \"\",\n                \"Header bank record id\": \"\",\n                \"Intermediary bank account id\": \"\",\n                \"Intermediary bank account id Internation bank reference code\": \"\"\n            };\n            // Parse and map contacts\n            const contacts = extractContacts(getRowData === null || getRowData === void 0 ? void 0 : (_getRowData_prophets_8 = getRowData.prophets[0]) === null || _getRowData_prophets_8 === void 0 ? void 0 : (_getRowData_prophets__prophet_code4 = _getRowData_prophets_8.prophet_code) === null || _getRowData_prophets__prophet_code4 === void 0 ? void 0 : _getRowData_prophets__prophet_code4.trim(), getRowData.contacts_json, getRowData === null || getRowData === void 0 ? void 0 : getRowData.company_name);\n            const newOrganizationData = {\n                \"Organization ID\": \"\",\n                \"Organization Name\": (getRowData === null || getRowData === void 0 ? void 0 : (_getRowData_prophets_9 = getRowData.prophets[0]) === null || _getRowData_prophets_9 === void 0 ? void 0 : _getRowData_prophets_9.prophet_code) ? getRowData === null || getRowData === void 0 ? void 0 : (_getRowData_prophets_10 = getRowData.prophets[0]) === null || _getRowData_prophets_10 === void 0 ? void 0 : (_getRowData_prophets__prophet_code5 = _getRowData_prophets_10.prophet_code) === null || _getRowData_prophets__prophet_code5 === void 0 ? void 0 : _getRowData_prophets__prophet_code5.trim() : \"\"\n            };\n            const newOrganizationRoleData = {\n                \"Organization ID\": \"\",\n                \"Organization Code\": \"\",\n                \"Role Type ID\": \"\",\n                Selected: \"\",\n                \"Organisation ID\": \"\",\n                \"Role Type ID\": \"\",\n                \"Contact ID\": \"\",\n                \"Contact ID Email Address\": \"\",\n                \"Contact ID Telephone\": \"\",\n                \"Contact ID Fax\": \"\"\n            };\n            const sheetSuppliersId = {\n                id: getRowData === null || getRowData === void 0 ? void 0 : getRowData.id,\n                supplierName: getRowData === null || getRowData === void 0 ? void 0 : getRowData.company_name,\n                isEmergencyAndFinanceNotComplete: getRowData === null || getRowData === void 0 ? void 0 : getRowData.isEmergencyAndFinanceNotComplete,\n                supplierCode: (getRowData === null || getRowData === void 0 ? void 0 : (_getRowData_prophets_11 = getRowData.prophets[0]) === null || _getRowData_prophets_11 === void 0 ? void 0 : _getRowData_prophets_11.prophet_code) ? getRowData === null || getRowData === void 0 ? void 0 : (_getRowData_prophets_12 = getRowData.prophets[0]) === null || _getRowData_prophets_12 === void 0 ? void 0 : (_getRowData_prophets__prophet_code6 = _getRowData_prophets_12.prophet_code) === null || _getRowData_prophets__prophet_code6 === void 0 ? void 0 : _getRowData_prophets__prophet_code6.trim() : \"\"\n            };\n            if (isSelected) {\n                setSupplierActiveData((prev)=>[\n                        ...prev,\n                        newSupplierActiveData\n                    ]);\n                setRoleData((prev)=>[\n                        ...prev,\n                        ...newRoleData\n                    ]);\n                setContactData((prev)=>[\n                        ...prev,\n                        ...contacts\n                    ]);\n                setOrganizationData((prev)=>[\n                        ...prev,\n                        newOrganizationData\n                    ]);\n                setOrganizationRoleData((prev)=>[\n                        ...prev,\n                        newOrganizationRoleData\n                    ]);\n                setSheetSupplierId((prev)=>[\n                        ...prev,\n                        sheetSuppliersId\n                    ]);\n                setSendacGroupData((prev)=>[\n                        ...prev,\n                        ...extractedSendacGroup\n                    ]);\n                setBankAc((prev)=>[\n                        ...prev,\n                        bankac\n                    ]);\n                setSenBnk((prev)=>[\n                        ...prev,\n                        senbnk\n                    ]);\n                if (Object.keys(UlpFil).length > 0) {\n                    setUlpFilData((prev)=>[\n                            ...prev,\n                            UlpFil\n                        ]);\n                }\n                setMultipleFilterISSData((prev)=>[\n                        ...prev,\n                        filteredISSExportData\n                    ]);\n            } else {\n                setMultipleFilterISSData((prev)=>prev.filter((item)=>item.id !== getRowData.id));\n                setSupplierActiveData((prev)=>prev.filter((item, index)=>{\n                        var _getRowData_prophets__prophet_code, _getRowData_prophets_;\n                        return index === 0 || item[\"Supplier code\"] !== (getRowData === null || getRowData === void 0 ? void 0 : (_getRowData_prophets_ = getRowData.prophets[0]) === null || _getRowData_prophets_ === void 0 ? void 0 : (_getRowData_prophets__prophet_code = _getRowData_prophets_.prophet_code) === null || _getRowData_prophets__prophet_code === void 0 ? void 0 : _getRowData_prophets__prophet_code.trim());\n                    }));\n                setUlpFilData((prev)=>prev.filter((item, index)=>{\n                        var _getRowData_prophets__prophet_code, _getRowData_prophets_;\n                        return index === 0 || item[\"Service Supplier Code\"] !== (getRowData === null || getRowData === void 0 ? void 0 : (_getRowData_prophets_ = getRowData.prophets[0]) === null || _getRowData_prophets_ === void 0 ? void 0 : (_getRowData_prophets__prophet_code = _getRowData_prophets_.prophet_code) === null || _getRowData_prophets__prophet_code === void 0 ? void 0 : _getRowData_prophets__prophet_code.trim());\n                    }));\n                setRoleData((prev)=>prev.filter((item, index)=>{\n                        var _getRowData_prophets__prophet_code, _getRowData_prophets_;\n                        return index === 0 || item[\"Supplier code\"] !== (getRowData === null || getRowData === void 0 ? void 0 : (_getRowData_prophets_ = getRowData.prophets[0]) === null || _getRowData_prophets_ === void 0 ? void 0 : (_getRowData_prophets__prophet_code = _getRowData_prophets_.prophet_code) === null || _getRowData_prophets__prophet_code === void 0 ? void 0 : _getRowData_prophets__prophet_code.trim());\n                    }));\n                // setContactData((prev) =>\n                // prev.filter(\n                //   (item, index) => index === 0 || item[\"Supplier code\"] !== contacts[0][\"Supplier code\"]\n                // )\n                // );\n                setContactData((prev)=>prev.filter((item, index)=>{\n                        if (contacts.length > 0) {\n                            return index === 0 || item[\"Supplier code\"] !== contacts[0][\"Supplier code\"];\n                        } else {\n                            // Handle the case when contacts array is empty\n                            return true; // or any other logic based on your requirements\n                        }\n                    }));\n                setOrganizationData((prev)=>prev.filter((item, index)=>{\n                        var _getRowData_prophets__prophet_code, _getRowData_prophets_;\n                        return index === 0 || item[\"Organization Name\"] !== (getRowData === null || getRowData === void 0 ? void 0 : (_getRowData_prophets_ = getRowData.prophets[0]) === null || _getRowData_prophets_ === void 0 ? void 0 : (_getRowData_prophets__prophet_code = _getRowData_prophets_.prophet_code) === null || _getRowData_prophets__prophet_code === void 0 ? void 0 : _getRowData_prophets__prophet_code.trim());\n                    }));\n                setOrganizationRoleData((prev)=>prev.filter((item, index)=>index === 0 || item[\"Organization ID\"] !== \"\"));\n                setSendacGroupData((prev)=>prev.filter((item, index)=>{\n                        var _extractedSendacGroup_;\n                        return index === 0 || item[\"Description\"] !== ((_extractedSendacGroup_ = extractedSendacGroup[0]) === null || _extractedSendacGroup_ === void 0 ? void 0 : _extractedSendacGroup_.Description);\n                    }));\n                setBankAc((prev)=>prev.filter((item, index)=>{\n                        var _getRowData_prophets__prophet_code, _getRowData_prophets_;\n                        return index === 0 || item[\"Supplier code\"] !== (getRowData === null || getRowData === void 0 ? void 0 : (_getRowData_prophets_ = getRowData.prophets[0]) === null || _getRowData_prophets_ === void 0 ? void 0 : (_getRowData_prophets__prophet_code = _getRowData_prophets_.prophet_code) === null || _getRowData_prophets__prophet_code === void 0 ? void 0 : _getRowData_prophets__prophet_code.trim());\n                    }));\n                setSenBnk((prev)=>prev.filter((item, index)=>index === 0 || item[\"Supplier code\"] !== \"\"));\n                setSheetSupplierId((prev)=>prev.filter((item, index)=>index === 0 || item[\"id\"] !== (getRowData === null || getRowData === void 0 ? void 0 : getRowData.id)));\n            }\n            setSupplierCheckedValue(supplierActiveData.length > 0 || roleData.length > 0 || contactData.length > 0 || organizationData.length > 0 || organizationRoleData.length > 0 || bankAc.length > 0 || senBnk.length > 0 || sendacGroupData.length > 0 || ulpFilData.length > 0 || multipleFilterISSData.length > 0);\n        } else {\n            if (event.node.selected) {\n                if (doesContainCancelledSupplier) {\n                    setContainsCancelledSupplier(true);\n                    return;\n                }\n                setIncompleteToast(true);\n                setTimeout(()=>{\n                    setIncompleteToast(false);\n                }, 3000);\n            }\n        }\n    };\n    const clearFiltersHandler = ()=>{\n        setRowData(allRowData);\n        setIsFiltered(false);\n        setIsFilteredName(\"\");\n        setProphetId(\"\");\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_toastify__WEBPACK_IMPORTED_MODULE_15__.ToastContainer, {\n                limit: 1\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\suppliers.js\",\n                lineNumber: 1770,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Layout__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                userData: userData,\n                blockScreen: blockScreen,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mr-20 md:mr-12 lg:mr-14\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-row md:flex-col lg:flex-row justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center border border-light-gray rounded-md px-3 w-auto md:w-fit lg:w-auto md:py-2 lg:py-1 md:mb-3 lg:mb-0 mr-2\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        id: \"default-checkbox\",\n                                                        type: \"radio\",\n                                                        value: \"default\",\n                                                        checked: selectedStatus === \"default\",\n                                                        className: \"w-5 h-5 text-blue border-theme-blue2 rounded\",\n                                                        onChange: handleFilterToggle\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\suppliers.js\",\n                                                        lineNumber: 1777,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        htmlFor: \"default-checkbox\",\n                                                        className: \"p-0 ml-3 labels\",\n                                                        children: \"Default\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\suppliers.js\",\n                                                        lineNumber: 1785,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\suppliers.js\",\n                                                lineNumber: 1776,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\suppliers.js\",\n                                            lineNumber: 1775,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center border border-light-gray rounded-md px-3 w-auto md:w-fit lg:w-auto md:py-2 lg:py-1 md:mb-3 lg:mb-0 mr-2\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        id: \"export-checkbox\",\n                                                        type: \"radio\",\n                                                        value: \"Exported\",\n                                                        checked: selectedStatus === \"Exported\",\n                                                        className: \"w-5 h-5 text-blue border-theme-blue2 rounded\",\n                                                        onChange: handleFilterToggle\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\suppliers.js\",\n                                                        lineNumber: 1792,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        htmlFor: \"export-checkbox\",\n                                                        className: \"p-0 ml-3 labels\",\n                                                        children: \"Exported\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\suppliers.js\",\n                                                        lineNumber: 1800,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\suppliers.js\",\n                                                lineNumber: 1791,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\suppliers.js\",\n                                            lineNumber: 1790,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center border border-light-gray rounded-md px-3 w-auto md:w-fit lg:w-auto md:py-2 lg:py-1 md:mb-3 lg:mb-0 mr-2\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        id: \"completed-radio\",\n                                                        type: \"radio\",\n                                                        value: \"Completed\",\n                                                        checked: selectedStatus === \"Completed\",\n                                                        className: \"w-5 h-5 text-blue border-theme-blue2 rounded\",\n                                                        onChange: handleFilterToggle\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\suppliers.js\",\n                                                        lineNumber: 1807,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        htmlFor: \"completed-radio\",\n                                                        className: \"p-0 ml-3 labels\",\n                                                        children: \"Completed\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\suppliers.js\",\n                                                        lineNumber: 1815,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\suppliers.js\",\n                                                lineNumber: 1806,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\suppliers.js\",\n                                            lineNumber: 1805,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center border border-light-gray rounded-md px-3 w-auto md:w-fit lg:w-auto md:py-2 lg:py-1 md:mb-3 lg:mb-0 mr-2\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        id: \"cancelled-radio\",\n                                                        type: \"radio\",\n                                                        value: \"Cancelled\",\n                                                        checked: selectedStatus === \"Cancelled\",\n                                                        className: \"w-5 h-5 text-blue border-theme-blue2 rounded\",\n                                                        onChange: handleFilterToggle\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\suppliers.js\",\n                                                        lineNumber: 1822,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        htmlFor: \"cancelled-radio\",\n                                                        className: \"p-0 ml-3 labels\",\n                                                        children: [\n                                                            \" \",\n                                                            \"Cancelled\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\suppliers.js\",\n                                                        lineNumber: 1830,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\suppliers.js\",\n                                                lineNumber: 1821,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\suppliers.js\",\n                                            lineNumber: 1820,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        isFiltered && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"p-3 py-1 flex items-center capitalize ml-5\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"b\", {\n                                                            className: \"mr-3\",\n                                                            children: \"Filtered On: \"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\suppliers.js\",\n                                                            lineNumber: 1840,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        \" \",\n                                                        isFilteredName\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\suppliers.js\",\n                                                    lineNumber: 1839,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    type: \"button\",\n                                                    onClick: clearFiltersHandler,\n                                                    className: \"flex h-[20px] border bg-red-500 text-white border-red-500 button rounded-md items-center !px-1 !py-1 ml-1\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fortawesome_react_fontawesome__WEBPACK_IMPORTED_MODULE_3__.FontAwesomeIcon, {\n                                                        icon: _fortawesome_free_solid_svg_icons__WEBPACK_IMPORTED_MODULE_24__.faClose,\n                                                        className: \"fw-bold\",\n                                                        size: \"lg\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\suppliers.js\",\n                                                        lineNumber: 1847,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\suppliers.js\",\n                                                    lineNumber: 1842,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\suppliers.js\",\n                                    lineNumber: 1774,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"relative block w-[47vh] text-gray-400 mt-0 pt-0\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"absolute px-4 py-1 pt-[0.4rem] 2xl:pt-1.5 text-black\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fortawesome_react_fontawesome__WEBPACK_IMPORTED_MODULE_3__.FontAwesomeIcon, {\n                                                        icon: _fortawesome_free_solid_svg_icons__WEBPACK_IMPORTED_MODULE_24__.faSearch,\n                                                        className: \"fw-bold\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\suppliers.js\",\n                                                        lineNumber: 1859,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\suppliers.js\",\n                                                    lineNumber: 1858,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"text\",\n                                                    id: \"filter-text-box\",\n                                                    placeholder: \"Search\",\n                                                    onInput: onFilterTextBoxChanged,\n                                                    className: \"block w-full px-4 pl-10 text-gray-500 placeholder-gray-400 searchbar border rounded-lg appearance-none form-input focus:outline-none\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\suppliers.js\",\n                                                    lineNumber: 1861,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\suppliers.js\",\n                                            lineNumber: 1857,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>exportFilteredData(),\n                                            className: \" border text-skin-primary border-skin-primary button rounded-md items-center !py-1\",\n                                            disabled: exportDisabled ? true : false,\n                                            children: \"Export\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\suppliers.js\",\n                                            lineNumber: 1869,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        userData.email == \"<EMAIL>\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: deleteAll,\n                                            className: \"border text-skin-primary border-skin-primary button rounded-md items-center !py-1\",\n                                            children: \"Delete\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\suppliers.js\",\n                                            lineNumber: 1877,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_10___default()), {\n                                                href: \"/supplier/add\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    className: \"button border border-skin-primary bg-skin-primary text-white rounded-md whitespace-nowrap\",\n                                                    children: \"Add Supplier\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\suppliers.js\",\n                                                    lineNumber: 1886,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\suppliers.js\",\n                                                lineNumber: 1885,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\suppliers.js\",\n                                            lineNumber: 1884,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\suppliers.js\",\n                                    lineNumber: 1856,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\suppliers.js\",\n                            lineNumber: 1773,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"my-5\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative ag-theme-alpine !rounded-md\",\n                                style: {\n                                    height: \"calc(100vh - 151px)\"\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ag_grid_react__WEBPACK_IMPORTED_MODULE_4__.AgGridReact, {\n                                        rowData: rowData,\n                                        ref: gridRef,\n                                        columnDefs: columnDefs,\n                                        defaultColDef: defaultColDef,\n                                        suppressRowClickSelection: true,\n                                        rowSelection: \"multiple\",\n                                        pagination: true,\n                                        paginationPageSize: pageSize,\n                                        onPageSizeChanged: handlePageSizeChange,\n                                        tooltipShowDelay: 0,\n                                        tooltipHideDelay: 1000,\n                                        onGridReady: handleGridReady,\n                                        onRowSelected: handleCheckboxEvent,\n                                        gridOptions: gridOptions\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\suppliers.js\",\n                                        lineNumber: 1898,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-start mt-2 pagination-style\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            htmlFor: \"page-size-select pagination\",\n                                            className: \"inputs\",\n                                            children: [\n                                                \"Show\",\n                                                \" \",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                    id: \"page-size-select\",\n                                                    onChange: handlePageSizeChange,\n                                                    value: pageSize,\n                                                    className: \"focus:outline-none\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: 10,\n                                                            children: \"10\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\suppliers.js\",\n                                                            lineNumber: 1923,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: 15,\n                                                            children: \"15\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\suppliers.js\",\n                                                            lineNumber: 1924,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: 25,\n                                                            children: \"25\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\suppliers.js\",\n                                                            lineNumber: 1925,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: 50,\n                                                            children: \"50\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\suppliers.js\",\n                                                            lineNumber: 1926,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: 100,\n                                                            children: \"100\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\suppliers.js\",\n                                                            lineNumber: 1927,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\suppliers.js\",\n                                                    lineNumber: 1917,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                \" \",\n                                                \"Entries\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\suppliers.js\",\n                                            lineNumber: 1915,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\suppliers.js\",\n                                        lineNumber: 1914,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\suppliers.js\",\n                                lineNumber: 1894,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\suppliers.js\",\n                            lineNumber: 1893,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\suppliers.js\",\n                    lineNumber: 1772,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\suppliers.js\",\n                lineNumber: 1771,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Dialog_Transition_headlessui_react__WEBPACK_IMPORTED_MODULE_25__.Transition, {\n                appear: true,\n                show: isOpenOption,\n                as: react__WEBPACK_IMPORTED_MODULE_2__.Fragment,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Dialog_Transition_headlessui_react__WEBPACK_IMPORTED_MODULE_25__.Dialog, {\n                    as: \"div\",\n                    className: \"relative z-10\",\n                    onClose: setIsOpenOption,\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Dialog_Transition_headlessui_react__WEBPACK_IMPORTED_MODULE_25__.Transition.Child, {\n                            as: react__WEBPACK_IMPORTED_MODULE_2__.Fragment,\n                            enter: \"ease-out duration-300\",\n                            enterFrom: \"opacity-0\",\n                            enterTo: \"opacity-100\",\n                            leave: \"ease-in duration-200\",\n                            leaveFrom: \"opacity-100\",\n                            leaveTo: \"opacity-0\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"fixed inset-0 bg-white bg-opacity-25 backdrop-blur-sm\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\suppliers.js\",\n                                lineNumber: 1947,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\suppliers.js\",\n                            lineNumber: 1938,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"fixed inset-0 overflow-y-auto\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-center min-h-full p-4 text-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Dialog_Transition_headlessui_react__WEBPACK_IMPORTED_MODULE_25__.Transition.Child, {\n                                    as: react__WEBPACK_IMPORTED_MODULE_2__.Fragment,\n                                    enter: \"ease-out duration-300\",\n                                    enterFrom: \"opacity-0 scale-95\",\n                                    enterTo: \"opacity-100 scale-100\",\n                                    leave: \"ease-in duration-200\",\n                                    leaveFrom: \"opacity-100 scale-100\",\n                                    leaveTo: \"opacity-0 scale-95\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Dialog_Transition_headlessui_react__WEBPACK_IMPORTED_MODULE_25__.Dialog.Panel, {\n                                        className: \"transform overflow-hidden rounded-xl bg-white  text-left align-middle shadow-xl transition-all\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-white w-[500px]\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex w-full bg-skin-primary h-[40px] items-center justify-between\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                                className: \"text-white items-center font-poppinsemibold pl-4 text-[20px]\",\n                                                                children: \"Select the export type\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\suppliers.js\",\n                                                                lineNumber: 1964,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fortawesome_react_fontawesome__WEBPACK_IMPORTED_MODULE_3__.FontAwesomeIcon, {\n                                                                icon: _fortawesome_free_solid_svg_icons__WEBPACK_IMPORTED_MODULE_24__.faClose,\n                                                                className: \"pr-4 text-white cursor-pointer\",\n                                                                onClick: closeOptionModal\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\suppliers.js\",\n                                                                lineNumber: 1967,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\suppliers.js\",\n                                                        lineNumber: 1963,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex justify-around items-center px-5\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center pl-0 pt-5\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                        name: \"exportType\",\n                                                                        id: \"internalExport\",\n                                                                        type: \"radio\",\n                                                                        className: \"mr-4\",\n                                                                        value: \"internalExport\",\n                                                                        checked: selectedExportType === \"internalExport\",\n                                                                        onChange: handleExportType\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\suppliers.js\",\n                                                                        lineNumber: 1975,\n                                                                        columnNumber: 25\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        className: \"font-poppinsregular text-[16px] text-charcoal-gray\",\n                                                                        htmlFor: \"internalExport\",\n                                                                        children: \"Internal\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\suppliers.js\",\n                                                                        lineNumber: 1984,\n                                                                        columnNumber: 25\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\suppliers.js\",\n                                                                lineNumber: 1974,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center pl-4 pt-5\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                        type: \"radio\",\n                                                                        name: \"exportType\",\n                                                                        id: \"ISS\",\n                                                                        className: \"mr-4\",\n                                                                        value: \"ISS\",\n                                                                        checked: selectedExportType === \"ISS\",\n                                                                        onChange: handleExportType\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\suppliers.js\",\n                                                                        lineNumber: 1992,\n                                                                        columnNumber: 25\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        htmlFor: \"ISS\",\n                                                                        className: \"font-poppinsregular text-[16px] text-charcoal-gray\",\n                                                                        children: \"ISS\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\suppliers.js\",\n                                                                        lineNumber: 2001,\n                                                                        columnNumber: 25\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\suppliers.js\",\n                                                                lineNumber: 1991,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\suppliers.js\",\n                                                        lineNumber: 1973,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\suppliers.js\",\n                                                lineNumber: 1962,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-center pb-4 pr-4 mt-2\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: exportFilteredData,\n                                                    disabled: !selectedExportType,\n                                                    className: \"font-circularstdbook rounded-md w-[100px] p-1 leading-5 mt-1 py-2 text-center hover:opacity-80 bg-skin-primary text-white\",\n                                                    children: \"Select\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\suppliers.js\",\n                                                    lineNumber: 2011,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\suppliers.js\",\n                                                lineNumber: 2010,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\suppliers.js\",\n                                        lineNumber: 1961,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\suppliers.js\",\n                                    lineNumber: 1952,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\suppliers.js\",\n                                lineNumber: 1951,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\suppliers.js\",\n                            lineNumber: 1950,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\suppliers.js\",\n                    lineNumber: 1937,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\suppliers.js\",\n                lineNumber: 1936,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Dialog_Transition_headlessui_react__WEBPACK_IMPORTED_MODULE_25__.Transition, {\n                appear: true,\n                show: emailStatusPopup,\n                as: react__WEBPACK_IMPORTED_MODULE_2__.Fragment,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Dialog_Transition_headlessui_react__WEBPACK_IMPORTED_MODULE_25__.Dialog, {\n                    as: \"div\",\n                    className: \"relative z-10\",\n                    onClose: closeEmailPopup,\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Dialog_Transition_headlessui_react__WEBPACK_IMPORTED_MODULE_25__.Transition.Child, {\n                            as: react__WEBPACK_IMPORTED_MODULE_2__.Fragment,\n                            enter: \"ease-out duration-300\",\n                            enterFrom: \"opacity-0\",\n                            enterTo: \"opacity-100\",\n                            leave: \"ease-in duration-200\",\n                            leaveFrom: \"opacity-100\",\n                            leaveTo: \"opacity-0\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"fixed inset-0 bg-black bg-opacity-25 backdrop-blur-sm\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\suppliers.js\",\n                                lineNumber: 2036,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\suppliers.js\",\n                            lineNumber: 2027,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"fixed inset-0 overflow-y-auto\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-center min-h-full p-4 text-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Dialog_Transition_headlessui_react__WEBPACK_IMPORTED_MODULE_25__.Transition.Child, {\n                                    as: react__WEBPACK_IMPORTED_MODULE_2__.Fragment,\n                                    enter: \"ease-out duration-300\",\n                                    enterFrom: \"opacity-0 scale-95\",\n                                    enterTo: \"opacity-100 scale-100\",\n                                    leave: \"ease-in duration-200\",\n                                    leaveFrom: \"opacity-100 scale-100\",\n                                    leaveTo: \"opacity-0 scale-95\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Dialog_Transition_headlessui_react__WEBPACK_IMPORTED_MODULE_25__.Dialog.Panel, {\n                                        className: \" w-[45%] transform overflow-hidden rounded-xl bg-white  text-left align-middle shadow-xl transition-all\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative bg-white rounded-lg shadow\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-start justify-between p-8 rounded-t\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"flex flex-row text-xl font-semibold text-gray-900  items-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"flex justify-center items-center border border-skin-primary text-skin-primary w-[25px] h-[25px] text-center leading-5 rounded-full text-base me-4\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fortawesome_react_fontawesome__WEBPACK_IMPORTED_MODULE_3__.FontAwesomeIcon, {\n                                                                            icon: _fortawesome_free_solid_svg_icons__WEBPACK_IMPORTED_MODULE_24__.faInfo\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\suppliers.js\",\n                                                                            lineNumber: 2057,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        \" \"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\suppliers.js\",\n                                                                    lineNumber: 2056,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                \" \",\n                                                                \"Status Message\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\suppliers.js\",\n                                                            lineNumber: 2055,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: closeEmailPopup,\n                                                            type: \"button\",\n                                                            className: \"text-black bg-transparent hover:bg-gray-200 hover:text-gray-900 rounded-lg text-xl w-8 h-8 ml-auto inline-flex justify-center items-center \",\n                                                            \"data-modal-hide\": \"default-modal\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fortawesome_react_fontawesome__WEBPACK_IMPORTED_MODULE_3__.FontAwesomeIcon, {\n                                                                    icon: _fortawesome_free_solid_svg_icons__WEBPACK_IMPORTED_MODULE_24__.faXmark,\n                                                                    className: \"text-skin-primary\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\suppliers.js\",\n                                                                    lineNumber: 2067,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                \" \"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\suppliers.js\",\n                                                            lineNumber: 2061,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\suppliers.js\",\n                                                    lineNumber: 2054,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"p-8 py-0 space-y-6\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-base xl:text-md 2xl:text-lg leading-relaxed mt-2\",\n                                                        children: popupMessage\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\suppliers.js\",\n                                                        lineNumber: 2075,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\suppliers.js\",\n                                                    lineNumber: 2074,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-end p-6 space-x-2 justify-end\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: closeEmailPopup,\n                                                        \"data-modal-hide\": \"default-modal\",\n                                                        type: \"button\",\n                                                        className: \"border text-dark-gray focus:ring-4 focus:outline-none focus:ring-blue-300 font-medium rounded-md text-md px-6 py-2 text-center \",\n                                                        children: \"Ok\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\suppliers.js\",\n                                                        lineNumber: 2081,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\suppliers.js\",\n                                                    lineNumber: 2080,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\suppliers.js\",\n                                            lineNumber: 2052,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\suppliers.js\",\n                                        lineNumber: 2050,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\suppliers.js\",\n                                    lineNumber: 2041,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\suppliers.js\",\n                                lineNumber: 2040,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\suppliers.js\",\n                            lineNumber: 2039,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\suppliers.js\",\n                    lineNumber: 2026,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\suppliers.js\",\n                lineNumber: 2025,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true);\n};\n_s(suppliers, \"H9gtB91Zay9/ScaLTy1kYxLBRMI=\", false, function() {\n    return [\n        next_router__WEBPACK_IMPORTED_MODULE_11__.useRouter,\n        _utils_rolePermissionsContext__WEBPACK_IMPORTED_MODULE_21__.usePermissions,\n        _utils_loaders_loadingContext__WEBPACK_IMPORTED_MODULE_17__.useLoading,\n        _azure_msal_react__WEBPACK_IMPORTED_MODULE_19__.useMsal,\n        _utils_userContext__WEBPACK_IMPORTED_MODULE_20__.useUser\n    ];\n});\nvar __N_SSP = true;\n/* harmony default export */ __webpack_exports__[\"default\"] = (suppliers);\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevExports = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevExports) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports on update so we can compare the boundary\n                // signatures.\n                module.hot.dispose(function (data) {\n                    data.prevExports = currentExports;\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevExports !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevExports, currentExports)) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevExports !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************\n//# sourceURL=webpack-internal:///./pages/suppliers.js\n"));

/***/ })

});