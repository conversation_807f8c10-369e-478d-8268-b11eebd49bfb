"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/products",{

/***/ "./utils/renderer/productActionRenderer.js":
/*!*************************************************!*\
  !*** ./utils/renderer/productActionRenderer.js ***!
  \*************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _fortawesome_react_fontawesome__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @fortawesome/react-fontawesome */ \"./node_modules/@fortawesome/react-fontawesome/index.es.js\");\n/* harmony import */ var _fortawesome_free_solid_svg_icons__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @fortawesome/free-solid-svg-icons */ \"./node_modules/@fortawesome/free-solid-svg-icons/index.mjs\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _services_apiConfig__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/services/apiConfig */ \"./services/apiConfig.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _exportExcel__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../exportExcel */ \"./utils/exportExcel.js\");\n/* harmony import */ var react_toastify__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! react-toastify */ \"./node_modules/react-toastify/dist/react-toastify.esm.mjs\");\n/* harmony import */ var react_toastify_dist_ReactToastify_css__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! react-toastify/dist/ReactToastify.css */ \"./node_modules/react-toastify/dist/ReactToastify.css\");\n/* harmony import */ var react_toastify_dist_ReactToastify_css__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(react_toastify_dist_ReactToastify_css__WEBPACK_IMPORTED_MODULE_7__);\n/* harmony import */ var _barrel_optimize_names_Dialog_Transition_headlessui_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Dialog,Transition!=!@headlessui/react */ \"__barrel_optimize__?names=Dialog,Transition!=!./node_modules/@headlessui/react/dist/headlessui.esm.js\");\n/* harmony import */ var _azure_msal_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @azure/msal-react */ \"./node_modules/@azure/msal-react/dist/index.js\");\n/* harmony import */ var js_cookie__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! js-cookie */ \"./node_modules/js-cookie/dist/js.cookie.mjs\");\n/* harmony import */ var _secureStorage__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ../secureStorage */ \"./utils/secureStorage.js\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n//\n\n\n\nconst productActionRenderer = (params, userData, token, company, typeId, setIsLoading, isIssUser)=>{\n    _s();\n    const { instance, accounts } = (0,_azure_msal_react__WEBPACK_IMPORTED_MODULE_8__.useMsal)();\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const product_id = params.data.id;\n    const data = params.data;\n    const serverAddress = _services_apiConfig__WEBPACK_IMPORTED_MODULE_3__.apiConfig.serverAddress;\n    const [isCancelOpen, setIsCancelOpen] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(false);\n    const [cancelledReasonapi, setCancelledReasonapi] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(\"\");\n    const [isValidCancelReason, setIsValidCancelReason] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(true);\n    const [isEditing, setIsEditing] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(false);\n    const editProduct = ()=>{\n        setIsEditing(true);\n        setIsLoading(true);\n        if (true) {\n            var _params_data;\n            if (params && (params === null || params === void 0 ? void 0 : (_params_data = params.data) === null || _params_data === void 0 ? void 0 : _params_data.type) == \"FG\") {\n                router.push({\n                    pathname: \"/finished-product-request/\".concat(product_id, \"/edit\")\n                });\n            } else if (params && params.data.type == \"RM\") {\n                setIsLoading(true);\n                router.push({\n                    pathname: \"/raw-material-request/\".concat(product_id, \"/edit\")\n                });\n            } else if (params && params.data.type == \"NV\") {\n                setIsLoading(true);\n                router.push({\n                    pathname: \"/variety/\".concat(product_id, \"/edit\")\n                });\n            } else if (params && params.data.type == \"PK\") {\n                setIsLoading(true);\n                router.push({\n                    pathname: \"/packaging-form/\".concat(product_id, \"/edit\")\n                });\n            }\n        }\n    };\n    const handleCancelReason = (data)=>{\n        if (data) {\n            setIsValidCancelReason(true);\n        } else {\n            setIsValidCancelReason(false);\n        }\n    };\n    const trimInputText = (input)=>{\n        return input.trim();\n    };\n    const exportToExcel = async ()=>{\n        if (data.status === \"Submitted\" || data.status === \"Exported\") {\n            let userText3 = \"\";\n            let markVariety = \"\";\n            if (data.company == \"dpsltd\") {\n                userText3 = \"DPS\";\n                markVariety = data === null || data === void 0 ? void 0 : data.mark_variety_name;\n            } else if (data.company == \"efcltd\") {\n                userText3 = \"OFF\";\n                markVariety = \"RM\" + \" \" + (data === null || data === void 0 ? void 0 : data.mark_variety_name);\n            } else if (data.company == \"fpp-ltd\") {\n                userText3 = \"FPP\";\n                markVariety = \"RM\" + \" \" + (data === null || data === void 0 ? void 0 : data.mark_variety_name);\n            } else {\n                userText3 = \"FLRS\"; //TODO: remove this later\n                markVariety = data === null || data === void 0 ? void 0 : data.mark_variety_name;\n            }\n            const filteredExportData = [\n                [\n                    \"Product Extract\",\n                    {\n                        \"User Boolean 1\": \"True\",\n                        \"Master Product Code\": data === null || data === void 0 ? void 0 : data.master_product_code,\n                        \"Commodity Code\": data === null || data === void 0 ? void 0 : data.intrastat_commodity_code_id,\n                        \"User Text 4\": data === null || data === void 0 ? void 0 : data.userText4,\n                        \"User Text 5\": data === null || data === void 0 ? void 0 : data.userText5,\n                        \"User Text 6\": data === null || data === void 0 ? void 0 : data.userText6,\n                        \"Intrastat weight mass\": data === null || data === void 0 ? void 0 : data.outer_gross_weight,\n                        \"Sub Product Code\": data === null || data === void 0 ? void 0 : data.sub_product_code,\n                        \"Mark/variety\": markVariety,\n                        \"Count or size\": data === null || data === void 0 ? void 0 : data.count_or_size,\n                        \"Sort Group Number\": data === null || data === void 0 ? void 0 : data.group_id,\n                        \"User Text 3\": userText3,\n                        \"Product Number\": \"\",\n                        \"Units in Outer\": data === null || data === void 0 ? void 0 : data.units_in_outer,\n                        \"Packs per pallet\": data === null || data === void 0 ? void 0 : data.cases_per_pallet,\n                        \"Sell packs per pallet\": data === null || data === void 0 ? void 0 : data.cases_per_pallet,\n                        \"Weight of outer\": data === null || data === void 0 ? void 0 : data.outer_gross_weight,\n                        \"Product distribution Point\": \"\",\n                        Buyer: 1,\n                        \"Temperature grade\": data === null || data === void 0 ? void 0 : data.temperature_grade_id,\n                        \"Temperature Grade\": data === null || data === void 0 ? void 0 : data.temperature_grade_name,\n                        \"Product Type\": data === null || data === void 0 ? void 0 : data.product_type_id,\n                        \"Product type\": data === null || data === void 0 ? void 0 : data.product_type_name,\n                        Active: \"True\"\n                    }\n                ]\n            ];\n            if (data.company == \"efcltd\" || data.company == \"fpp-ltd\") {\n                filteredExportData.push([\n                    \"ALTFIL Extract\",\n                    {\n                        Active: \"True\",\n                        \"Altfil record id\": \"\",\n                        \"Generic Code\": userText3,\n                        \"Alternate product number\": \"\",\n                        \"Alternate number\": \"\",\n                        \"Alternate bar code number\": \"\",\n                        \"Alternate description\": data === null || data === void 0 ? void 0 : data.sub_product_code,\n                        \"Alternate product Master product code\": \"\",\n                        \"Alternate product number Count or size\": \"\",\n                        \"Alternate product number Gross weight outer\": \"\",\n                        \"Alternate product number Mark/variety\": \"\",\n                        \"Alternate group\": data === null || data === void 0 ? void 0 : data.group_id,\n                        \"Alternate count or size\": data === null || data === void 0 ? void 0 : data.units_in_outer,\n                        \"Alternate prefix\": \"\",\n                        \"Inner product barcode\": \"\",\n                        \"Outer product barcode\": \"\",\n                        \"Alternate product number extension\": \"\",\n                        \"End Customer\": \"\",\n                        Brand: \"\",\n                        \"Display until days\": \"\",\n                        \"GTIN 14\": \"\",\n                        \"Calibre / Size\": \"\",\n                        \"Alternate product number Packs per pallet\": \"\",\n                        \"Inner stock keeping unit\": \"\",\n                        \"Stock keeping unit\": \"\",\n                        \"Customer product code\": \"\",\n                        \"Alternate use standard prefix (1=yes)\": \"1\",\n                        \"User text 1\": \"\"\n                    }\n                ]);\n            // console.log(\n            //   \"filtered export data after creating new array\",\n            //   filteredExportData\n            // );\n            }\n            const productEmailParagraph = \"<p>User \".concat(userData.name, \" submitted a Raw material request with request number \").concat(params.data.request_no, \" \\n          to ISS.\\n       \\n      </p>\");\n            let productEmailCommentPlaceholder = '<p style=\\'\\n      color: #32353e; margin: 0 0 10px 0; padding: 0;font-family: \"HelveticaNeueLight\", \"HelveticaNeue-Light\", \"Helvetica Neue Light\", \"HelveticaNeue\", \"Helvetica Neue\", \"TeXGyreHerosRegular\", \"Helvetica\", \"Tahoma\", \"Geneva\", \"Arial\", sans-serif; font-weight: 300;\\n        font-stretch: normal; font-size: 14px; line-height: 1.7; text-align: left;\\'>Comments: <i>'.concat(params.data.emailComment ? params.data.emailComment : \"-\", \"</i></p>\\n      \");\n            const export_response = await (0,_exportExcel__WEBPACK_IMPORTED_MODULE_5__[\"default\"])(filteredExportData, false, token, data.company, userData, \"\", params.data.originator_email, false, true, true, productEmailParagraph, productEmailCommentPlaceholder, params.data.request_no);\n            // console.log(\"export_response\", export_response);\n            if (export_response === 401) {\n                react_toastify__WEBPACK_IMPORTED_MODULE_6__.toast.error(\"Your session has expired. Please log in again.\");\n                setTimeout(async ()=>{\n                    await (0,_secureStorage__WEBPACK_IMPORTED_MODULE_10__.logout)();\n                    const redirectUrl = \"/login?redirect=\".concat(encodeURIComponent(window.location.pathname));\n                }, 3000);\n                return null;\n            }\n        } else {\n            react_toastify__WEBPACK_IMPORTED_MODULE_6__.toast.error(\"Kindly Submit the Request to Export it.\", {\n                position: \"top-right\",\n                autoClose: 3000,\n                hideProgressBar: false,\n                closeOnClick: true,\n                pauseOnHover: false,\n                draggable: true,\n                progress: undefined,\n                theme: \"light\"\n            });\n            return;\n        }\n    };\n    const cancelProduct = ()=>{\n        setIsCancelOpen(true);\n    };\n    const closeCancelModal = ()=>{\n        setIsCancelOpen(false);\n    };\n    const getProphetId = ()=>{\n        switch(params.data.company){\n            case \"dpsltd\":\n                return 1;\n            case \"efcltd\":\n                return 3;\n            case \"fpp-ltd\":\n                return 4;\n            default:\n                return 1;\n        }\n    };\n    const saveModalData = ()=>{\n        const prophetId = getProphetId();\n        // return;\n        if (!cancelledReasonapi) {\n            setIsValidCancelReason(false);\n            return;\n        }\n        try {\n            var _params_data;\n            fetch(\"\".concat(serverAddress, \"products/product-update-status\"), {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\",\n                    Authorization: \"Bearer \".concat(userData.token)\n                },\n                body: JSON.stringify({\n                    status: 6,\n                    productId: product_id,\n                    updated_date: new Date().toISOString(),\n                    reason: cancelledReasonapi,\n                    request_no: params.data.request_no,\n                    type: params.data.type,\n                    cancelled_by: userData.email,\n                    cancelled_by_name: userData.name,\n                    cancelled_date: new Date().toISOString(),\n                    originator_email: (_params_data = params.data) === null || _params_data === void 0 ? void 0 : _params_data.originator_email,\n                    current_action_id: params.data.action_id,\n                    prophetId: prophetId,\n                    code: params.data.code\n                })\n            }).then((res)=>{\n                if (res.status === 200) {\n                    return res.json();\n                } else if (res.status === 401) {\n                    react_toastify__WEBPACK_IMPORTED_MODULE_6__.toast.error(\"Your session has expired. Please log in again.\");\n                    setTimeout(async ()=>{\n                        await (0,_secureStorage__WEBPACK_IMPORTED_MODULE_10__.logout)();\n                        const redirectUrl = \"/login?redirect=\".concat(encodeURIComponent(window.location.pathname));\n                    }, 3000);\n                }\n                return null;\n            }).then((json)=>{\n                if (json) {\n                    setIsCancelOpen(false);\n                    if (params.data.type == \"NV\") {\n                        js_cookie__WEBPACK_IMPORTED_MODULE_9__[\"default\"].set(\"PreviousPage\", true);\n                    }\n                    window.location.reload();\n                }\n            });\n        } catch (error) {\n            console.error(\"Failed to cancel product by :\", error);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex flex-row gap-4 justify-start text-blue-500 pl-3\",\n                children: params.data.status == \"Prophet Setup Completed\" || params.data.status == \"Prophet to Setup\" || params.data.status == \"Cancelled\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    title: \"View Request\",\n                    onClick: editProduct,\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fortawesome_react_fontawesome__WEBPACK_IMPORTED_MODULE_1__.FontAwesomeIcon, {\n                        icon: _fortawesome_free_solid_svg_icons__WEBPACK_IMPORTED_MODULE_11__.faEye,\n                        size: \"lg\",\n                        className: \"text-skin-primary\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\utils\\\\renderer\\\\productActionRenderer.js\",\n                        lineNumber: 308,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\utils\\\\renderer\\\\productActionRenderer.js\",\n                    lineNumber: 307,\n                    columnNumber: 11\n                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            disabled: isEditing,\n                            title: \"Edit Request\",\n                            onClick: editProduct,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fortawesome_react_fontawesome__WEBPACK_IMPORTED_MODULE_1__.FontAwesomeIcon, {\n                                icon: _fortawesome_free_solid_svg_icons__WEBPACK_IMPORTED_MODULE_11__.faPenToSquare,\n                                size: \"lg\",\n                                className: \"text-skin-primary\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\utils\\\\renderer\\\\productActionRenderer.js\",\n                                lineNumber: 322,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\utils\\\\renderer\\\\productActionRenderer.js\",\n                            lineNumber: 316,\n                            columnNumber: 13\n                        }, undefined),\n                        params.data.status != \"Setup Completed\" && params.data.status != \"Submitted\" && params.data.status != \"Cancelled\" && !isIssUser && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: cancelProduct,\n                            title: \"Cancel Request\",\n                            className: \"flex items-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fortawesome_react_fontawesome__WEBPACK_IMPORTED_MODULE_1__.FontAwesomeIcon, {\n                                icon: _fortawesome_free_solid_svg_icons__WEBPACK_IMPORTED_MODULE_11__.faXmark,\n                                size: \"sm\",\n                                className: \"border rounded-sm border-skin-primary text-skin-primary m-0 w-[15px] h-[15px]\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\utils\\\\renderer\\\\productActionRenderer.js\",\n                                lineNumber: 337,\n                                columnNumber: 19\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\utils\\\\renderer\\\\productActionRenderer.js\",\n                            lineNumber: 332,\n                            columnNumber: 17\n                        }, undefined),\n                        params.data.status == \"Submitted\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>exportToExcel(params),\n                            title: \"Export Request\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fortawesome_react_fontawesome__WEBPACK_IMPORTED_MODULE_1__.FontAwesomeIcon, {\n                                icon: _fortawesome_free_solid_svg_icons__WEBPACK_IMPORTED_MODULE_11__.faFileExport,\n                                size: \"lg\",\n                                className: \"cursor-pointer text-skin-primary\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\utils\\\\renderer\\\\productActionRenderer.js\",\n                                lineNumber: 349,\n                                columnNumber: 17\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\utils\\\\renderer\\\\productActionRenderer.js\",\n                            lineNumber: 345,\n                            columnNumber: 15\n                        }, undefined)\n                    ]\n                }, void 0, true)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\utils\\\\renderer\\\\productActionRenderer.js\",\n                lineNumber: 304,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Dialog_Transition_headlessui_react__WEBPACK_IMPORTED_MODULE_12__.Transition, {\n                appear: true,\n                show: isCancelOpen,\n                as: react__WEBPACK_IMPORTED_MODULE_4__.Fragment,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Dialog_Transition_headlessui_react__WEBPACK_IMPORTED_MODULE_12__.Dialog, {\n                    as: \"div\",\n                    className: \"relative z-10\",\n                    onClose: closeCancelModal,\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Dialog_Transition_headlessui_react__WEBPACK_IMPORTED_MODULE_12__.Transition.Child, {\n                            as: react__WEBPACK_IMPORTED_MODULE_4__.Fragment,\n                            enter: \"ease-out duration-300\",\n                            enterFrom: \"opacity-0\",\n                            enterTo: \"opacity-100\",\n                            leave: \"ease-in duration-200\",\n                            leaveFrom: \"opacity-100\",\n                            leaveTo: \"opacity-0\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"fixed inset-0 bg-black bg-opacity-25 backdrop-blur-sm\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\utils\\\\renderer\\\\productActionRenderer.js\",\n                                lineNumber: 371,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\utils\\\\renderer\\\\productActionRenderer.js\",\n                            lineNumber: 362,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"fixed inset-0 overflow-y-auto\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-center min-h-full p-4 text-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Dialog_Transition_headlessui_react__WEBPACK_IMPORTED_MODULE_12__.Transition.Child, {\n                                    as: react__WEBPACK_IMPORTED_MODULE_4__.Fragment,\n                                    enter: \"ease-out duration-300\",\n                                    enterFrom: \"opacity-0 scale-95\",\n                                    enterTo: \"opacity-100 scale-100\",\n                                    leave: \"ease-in duration-200\",\n                                    leaveFrom: \"opacity-100 scale-100\",\n                                    leaveTo: \"opacity-0 scale-95\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Dialog_Transition_headlessui_react__WEBPACK_IMPORTED_MODULE_12__.Dialog.Panel, {\n                                        className: \" w-[45%] transform overflow-hidden rounded-xl bg-white  text-left align-middle shadow-xl transition-all\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative bg-white rounded-lg shadow\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-start justify-between p-8 rounded-t\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"flex flex-row text-xl font-semibold text-gray-900  items-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"flex justify-center items-center border border-skin-primary text-skin-primary w-[25px] h-[25px] text-center leading-5 rounded-full text-base me-4\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fortawesome_react_fontawesome__WEBPACK_IMPORTED_MODULE_1__.FontAwesomeIcon, {\n                                                                            icon: _fortawesome_free_solid_svg_icons__WEBPACK_IMPORTED_MODULE_11__.faInfo\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\utils\\\\renderer\\\\productActionRenderer.js\",\n                                                                            lineNumber: 392,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        \" \"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\utils\\\\renderer\\\\productActionRenderer.js\",\n                                                                    lineNumber: 391,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                \" \",\n                                                                \"Cancellation Reason\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\utils\\\\renderer\\\\productActionRenderer.js\",\n                                                            lineNumber: 390,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: closeCancelModal,\n                                                            type: \"button\",\n                                                            className: \"text-black bg-transparent hover:bg-gray-200 hover:text-gray-900 rounded-lg text-xl w-8 h-8 ml-auto inline-flex justify-center items-center \",\n                                                            \"data-modal-hide\": \"default-modal\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fortawesome_react_fontawesome__WEBPACK_IMPORTED_MODULE_1__.FontAwesomeIcon, {\n                                                                    icon: _fortawesome_free_solid_svg_icons__WEBPACK_IMPORTED_MODULE_11__.faXmark,\n                                                                    className: \"text-skin-primary\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\utils\\\\renderer\\\\productActionRenderer.js\",\n                                                                    lineNumber: 402,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                \" \"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\utils\\\\renderer\\\\productActionRenderer.js\",\n                                                            lineNumber: 396,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\utils\\\\renderer\\\\productActionRenderer.js\",\n                                                    lineNumber: 389,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"p-8 py-0 space-y-6\",\n                                                    children: [\n                                                        params.data.type == \"NV\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-base xl:text-md 2xl:text-lg leading-relaxed mt-2\",\n                                                            children: \"Enter Variety Cancellation Reason.\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\utils\\\\renderer\\\\productActionRenderer.js\",\n                                                            lineNumber: 411,\n                                                            columnNumber: 25\n                                                        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-base xl:text-md 2xl:text-lg leading-relaxed mt-2\",\n                                                            children: \"Enter Product Cancellation Reason.\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\utils\\\\renderer\\\\productActionRenderer.js\",\n                                                            lineNumber: 415,\n                                                            columnNumber: 25\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                            className: \"flex flex-col w-full rounded-md p-2 px-3  border border-light-gray2\",\n                                                            rows: \"8\",\n                                                            value: cancelledReasonapi,\n                                                            onChange: (e)=>{\n                                                                setCancelledReasonapi(e.target.value), handleCancelReason(e.target.value);\n                                                            },\n                                                            onBlur: (e)=>{\n                                                                const trimmedValue = trimInputText(e.target.value);\n                                                                setCancelledReasonapi(trimmedValue);\n                                                                handleCancelReason(trimmedValue);\n                                                            },\n                                                            // disabled={(e) => {e.target.value == \"\"}}\n                                                            placeholder: \"Provide reason for cancellation...\",\n                                                            maxlength: \"500\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\utils\\\\renderer\\\\productActionRenderer.js\",\n                                                            lineNumber: 419,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        !isValidCancelReason && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-red-500\",\n                                                            children: \"Please Provide reason for cancellation\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\utils\\\\renderer\\\\productActionRenderer.js\",\n                                                            lineNumber: 437,\n                                                            columnNumber: 25\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\utils\\\\renderer\\\\productActionRenderer.js\",\n                                                    lineNumber: 409,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-end p-6 space-x-2 justify-end\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: saveModalData,\n                                                        \"data-modal-hide\": \"default-modal\",\n                                                        type: \"button\",\n                                                        className: \" bg-skin-primary hover:bg-skin-primary focus:ring-4 focus:outline-none focus:ring-blue-300 font-medium rounded-md text-md text-white mr-1 px-6 py-2 text-center \",\n                                                        children: \"Cancel Request\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\utils\\\\renderer\\\\productActionRenderer.js\",\n                                                        lineNumber: 444,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\utils\\\\renderer\\\\productActionRenderer.js\",\n                                                    lineNumber: 443,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\utils\\\\renderer\\\\productActionRenderer.js\",\n                                            lineNumber: 387,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\utils\\\\renderer\\\\productActionRenderer.js\",\n                                        lineNumber: 385,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\utils\\\\renderer\\\\productActionRenderer.js\",\n                                    lineNumber: 376,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\utils\\\\renderer\\\\productActionRenderer.js\",\n                                lineNumber: 375,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\utils\\\\renderer\\\\productActionRenderer.js\",\n                            lineNumber: 374,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\utils\\\\renderer\\\\productActionRenderer.js\",\n                    lineNumber: 361,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\utils\\\\renderer\\\\productActionRenderer.js\",\n                lineNumber: 360,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true);\n};\n_s(productActionRenderer, \"WRLNpZR8GjNJEEDzJrv+TBRq/eY=\", false, function() {\n    return [\n        _azure_msal_react__WEBPACK_IMPORTED_MODULE_8__.useMsal,\n        next_router__WEBPACK_IMPORTED_MODULE_2__.useRouter\n    ];\n});\n/* harmony default export */ __webpack_exports__[\"default\"] = (productActionRenderer);\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevExports = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevExports) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports on update so we can compare the boundary\n                // signatures.\n                module.hot.dispose(function (data) {\n                    data.prevExports = currentExports;\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevExports !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevExports, currentExports)) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevExports !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./utils/renderer/productActionRenderer.js\n"));

/***/ })

});