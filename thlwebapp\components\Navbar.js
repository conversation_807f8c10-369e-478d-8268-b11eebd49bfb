import { useState, useEffect } from "react";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faChevronLeft } from "@fortawesome/free-solid-svg-icons";
import Link from "next/link";
import { useRouter } from "next/router";
import { useMsal } from "@azure/msal-react";
import Cookies from "js-cookie";

export const themes = [
  { bg: "#022d71", text: "#022d71", name: "DPS" },
  { bg: "#2e9b28", text: "#2e9b28", name: "EFC" },
  { bg: "#a91e23", text: "#a91e23", name: "<PERSON>&<PERSON>" },
  { bg: "#3d6546", text: "#3d6546", name: "FPP" },
];

const Navbar = ({ userData }) => {
  const router = useRouter();
  const [pageName, setPageName] = useState("");
  const [currentRoute, setCurrentRoute] = useState("");
  const [isUpdating, setIsUpdating] = useState(false);

  useEffect(() => {
    const currentPath = router.pathname;
    setCurrentRoute(currentPath);
    if (currentPath === "/finished-product-request/add") {
      Cookies.set("PreviousPage", "FG");
      setPageName("Finished Goods Request");
    } else if (currentPath === "/raw-material-request/add") {
      setPageName("Raw Material Request");
      Cookies.set("PreviousPage", "RM");
    } else if (currentPath === "/packaging-form/add") {
      setPageName("Packaging Request");
    } else if (currentPath === "/variety/add") {
      Cookies.set("PreviousPage", "NV");
      setPageName("New Variety Request");
    } else if (currentPath === "/variety/[productId]/edit") {
      Cookies.set("PreviousPage", "NV");
      setPageName("Edit New Variety Request");
    } else if (currentPath === "/packaging-form/[productId]/edit") {
      Cookies.set("PreviousPage", "PK");
      setPageName("Edit Packaging Request");
    } else if (currentPath === "/raw-material-request/[productId]/edit") {
      Cookies.set("PreviousPage", "RM");
      setPageName("Edit Raw Material Request");
    } else if (currentPath === "/finished-product-request/[productId]/edit") {
      Cookies.set("PreviousPage", "FG");
      setPageName("Edit Finished Goods Request");
    } else if (currentPath?.endsWith("/edit")) {
      setPageName(`Edit Supplier`);
    } else if (currentPath?.endsWith("/add")) {
      setPageName("Add Supplier");
    } else if (currentPath?.endsWith("/edit/forms")) {
      setPageName(`Supplier Form`);
    } else if (currentPath?.endsWith("/confirm")) {
      setPageName(`Confirm Details for Supplier`);
    } else if (currentPath === "/suppliers") {
      setPageName("Suppliers");
    } else if (currentPath === "/users") {
      setPageName("User Management");
    } else if (currentPath === "/viewlogs") {
      setPageName("View Logs");
    } else if (currentPath === "/products") {
      Cookies.set("PreviousPage", "RM");
      setPageName("Products");
    } else if (currentPath === "/variety") {
      setPageName("Variety");
      Cookies.set("PreviousPage", "NV");
    } else if (currentPath === "/finishedProductRequest") {
      Cookies.set("PreviousPage", "FG");
      setPageName("Finished Product Request");
    } else if (currentPath === "/rawMaterialRequest") {
      Cookies.set("PreviousPage", "RM");
      setPageName("Raw Material Request");
    } else if (currentPath === "/whatif") {
      setPageName("Whatif");
    } else if (
      currentPath === "/service_level" ||
      currentPath === "/service_level/reports/masterForcast"
    ) {
      setPageName("Service Level");
    }
  }, [router.pathname]);

  const baseCompanyOptions = [
    { value: "dpsltd", label: "DPS" },
    { value: "dpsltdms", label: "DPS M&S" },
    { value: "efcltd", label: "EFC" },
    { value: "fpp-ltd", label: "FPP" },
  ];

  const companyOptions = [
    ...baseCompanyOptions,
    ...(userData?.role_id === 6
      ? [
          { value: "issproduce", label: "ISS" },
          { value: "flrs", label: "FLRS", disabled: true },
          { value: "thl", label: "THL", disabled: true },
        ]
      : []),
  ];

  const [selectedCompany, setSelectedCompany] = useState(
    userData?.ADCompanyName === "DPS MS"
      ? "dpsltdms"
      : userData?.company || ""
  );

  const handleCompanyChange = async (event) => {
    const company = event.target.value;
    
    if (isUpdating) return; // Prevent multiple rapid clicks
    
    setIsUpdating(true);
    
    try {
      console.log("Updating company to:", company);
      const apiBase = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:8081';
      // Call the API to update company in session
      const response = await fetch(`${apiBase}/api/auth/update-company`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include', // Important: Include cookies in request
        body: JSON.stringify({ company }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to update company');
      }

      const result = await response.json();
      console.log("Company updated successfully:", result);
      
      // Update local state
      setSelectedCompany(company);
      
      // Reload the page to reflect changes throughout the application
      router.reload();
      
    } catch (error) {
      console.error("Error updating company:", error);
      alert("Failed to update company. Please try again.");
      
      // Reset the select to previous value on error
      event.target.value = selectedCompany;
    } finally {
      setIsUpdating(false);
    }
  };

  return (
    <header>
      <div className="titlebar">
        <div className="flex flex-row justify-between w-full bg-skin-primary h-14">
          <div className="flex flex-row w-full items-center">
            <div className="page-heading cursor-pointer">
              <FontAwesomeIcon
                icon={faChevronLeft}
                className="pageName text-white"
                onClick={() => router.back()}
              />
              <Link
                href={currentRoute}
                className="ml-4 2xl:text-lg font-poppinssemibold pageName text-white tracking-wide"
              >
                {pageName}
              </Link>
            </div>
          </div>
          {(userData?.role_id == 5 || userData?.role_id == 6) && (
            <div className="flex flex-row justify-end w-1/2 items-center mr-4">
              <select
                value={selectedCompany}
                onChange={handleCompanyChange}
                disabled={isUpdating}
                className="bg-white text-black rounded disabled:opacity-50"
              >
                {companyOptions.map((opt) => (
                  <option
                    key={opt.value}
                    value={opt.value}
                    disabled={opt.disabled}
                  >
                    {opt.label}
                  </option>
                ))}
              </select>
              {isUpdating && (
                <span className="ml-2 text-white text-sm">Updating...</span>
              )}
            </div>
          )}
        </div>
      </div>
    </header>
  );
};

export default Navbar;
