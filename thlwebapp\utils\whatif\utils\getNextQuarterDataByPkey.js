import { apiConfig } from "@/services/apiConfig";
import { toast } from "react-toastify";

export async function getNextQuarterDataByPkey(token,pkey,weekNo, currentYear,whatif_id) {
  const encodedPkey = encodeURIComponent(pkey);

    return await fetch(`${apiConfig.serverAddress}whatif/get-next-quarter-product-data-by-week?year=${currentYear}&pkey=${encodedPkey}&weekNo=${weekNo}&whatif_id=${whatif_id}`, {
      method: "GET",
      headers: {
        Authorization: `Bearer ${token}`,
      },
    })
      .then(async (res) => {
        if (res.status === 400) {
          toast.error(
            "There was an error with your request. Please check your data and try again."
          );
        } else if (res.status === 401) {
          toast.error("Your session has expired. Please log in again.");
          setTimeout(() => {
            Cookies.remove("user");
            Cookies.remove("theme");
            Cookies.remove("token");
            const redirectUrl = `/login?redirect=${encodeURIComponent(window.location.pathname)}`;
logoutHandler(instance,redirectUrl);;
          }, 3000);
        }
        if (res.status === 200) {
          return res.json();
        }
        throw new Error("Failed to fetch data");
      })
      .catch((error) => {
        console.log(error);
      });
  }