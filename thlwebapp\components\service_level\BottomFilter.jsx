import React, { useState, useEffect } from "react";
import { Tooltip } from "@fluentui/react-components";
import Link from "next/link";
import moment from "moment";
import Cookies from "js-cookie";
import { getSLData } from "@/utils/service-level/utils/getSLData";

const BottomFilter = ({
  setInitialLoading,
  startDateRange,
  endDateRange,
  selectedCustomer,
  setCustomerSLData,
  seeAll,
  setSeeAll,
  orderTypeId,
  recordsCount,
  searchBoxContent,
  setSearchBoxContent,
  selectedRows,
  handleBulkUpdate,
  setNoDataExists,
  setShowLoadingMessage,
  setAllReasonsSubreasons,
  allReasonsSubreasons,
  setSelectedSubReasons,
  setSelectedReasons,
}) => {
  let token = Cookies.get("token");

  const seeAllHandler = async (seeAllParam) => {
    setSeeAll(seeAllParam);
    const company = Cookies.get("company");
    const ADCompanyName = Cookies.get("ADCompanyName");
    setInitialLoading(true);
    setSelectedReasons([]);
    setSelectedSubReasons([]);
    let start_date = moment(startDateRange, "DD-MM-YYYY").format("MM-DD-YYYY");
    let end_date = moment(endDateRange, "DD-MM-YYYY").format("MM-DD-YYYY");
    try {
      if (token && selectedCustomer.value) {
        const data = await getSLData(
          `get-initial-sl-data/${selectedCustomer.value}/${start_date}/${end_date}/${seeAllParam}/${orderTypeId}/${company}/${ADCompanyName}`,
          token
        );
        setShowLoadingMessage(true);
        if (data) {
          const dataArray = Object.values(data.formattedSLData);
          if (dataArray.length == 0) {
            setNoDataExists(true);
            setInitialLoading(false);
          }
          setAllReasonsSubreasons(data.allReasonSubReasons);
          setCustomerSLData(dataArray || []);
        } else {
          console.error("Error fetching 'All' data.");
          setNoDataExists(true);
          setInitialLoading(false);
        }
      }
      setInitialLoading(false);
    } catch (error) {
      setNoDataExists(true);
      setInitialLoading(false);
      console.error("Error fetching 'All' data:", error);
    }
    //  setLoading(false);
  };

  return (
    <div className="flex flex-row gap-4 justify-between items-center h-10 px-3 py-1 border-b border-gray-200">
      <div className="flex w-full">
        <div className="flex gap-2 px-3 items-center border-r border-gray-200">
          <Tooltip
            content="Need to Update Reasons"
            relationship="label"
            className="!bg-white"
          >
            <div className="rounded-full w-5 h-5 !bg-needsupdate-status border border-gray-400"></div>
          </Tooltip>
          <Tooltip
            content="Locked Orders"
            relationship="label"
            className="!bg-white"
          >
            <div className="rounded-full w-5 h-5 !bg-locked-products border border-gray-400"></div>
          </Tooltip>
          <Tooltip
            content="Additional orders"
            relationship="label"
            className="!bg-white"
          >
            <div className="rounded-full w-5 h-5 !bg-volumechange-status border border-gray-400"></div>
          </Tooltip>
        </div>
        <div className="flex gap-2 px-3 items-center">
          <label>
            Rows: <span className="font-bold">{recordsCount ?? 0}</span>
          </label>
        </div>
        <div className="ml-auto flex gap-2">
          <div
            className={`px-3 ${
              selectedRows.length == 0 ? "cursor-not-allowed" : "pointer-cursor"
            }  border-r border-gray-200`}
          >
            <button
              className={` px-4 2xl:px-4 border rounded-full h-9 ${
                selectedRows.length == 0
                  ? "cursor-not-allowed"
                  : "pointer-cursor"
              } border-gray-300 flex items-center justify-center font-medium bg-skin-primary text-white`}
              onClick={handleBulkUpdate}
              disabled={selectedRows.length == 0}
            >
              Bulk Update
            </button>
          </div>
          <div className="px-3  border-r border-gray-200">
            <input
              type="number"
              placeholder="Search Order Number..."
              className="pl-4 px-2 2xl:px-4 pt-2 border rounded-full h-9"
              defaultValue={searchBoxContent}
              max={100000000000000000n}
              min={0}
              onChange={(e) => setSearchBoxContent(e.target.value)}
            />
          </div>

          <div className="flex gap-2 px-3  border-r border-gray-200">
            <input
              className="hidden"
              type="radio"
              id="all"
              name="filter"
              checked={seeAll}
              onChange={() => seeAllHandler(true)}
            />
            <label
              htmlFor="all"
              className={`pointer-cursor labelcheck border border-gray-300 rounded-full px-4 2xl:px-6 py-1 flex items-center font-medium hover:cursor-pointer ${
                seeAll ? "bg-theme-blue2 text-white" : ""
              }`}
            >
              All
            </label>
            <input
              className="hidden"
              type="radio"
              id="needsupdate"
              name="filter"
              checked={!seeAll}
              onChange={() => seeAllHandler(false)}
            />
            <label
              htmlFor="needsupdate"
              className={`pointer-cursor labelcheck border border-gray-300 rounded-full px-4 2xl:px-6 py-1 flex items-center font-medium hover:cursor-pointer ${
                !seeAll ? "bg-theme-blue2 text-white" : ""
              }`}
            >
              Needs Update
            </label>
          </div>

          <Link href="/service_level/reports/masterForcast">
            <div className="bg-white border border-skin-primary text-skin-primary rounded-full px-4 py-1 h-9 flex items-center justify-center">
              Reports
            </div>
          </Link>
        </div>
      </div>
    </div>
  );
};

export default BottomFilter;
