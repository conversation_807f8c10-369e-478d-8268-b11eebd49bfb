"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/raw-material-request/add",{

/***/ "./components/DrawerComponent.js":
/*!***************************************!*\
  !*** ./components/DrawerComponent.js ***!
  \***************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DrawerComponent: function() { return /* binding */ DrawerComponent; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _fortawesome_react_fontawesome__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @fortawesome/react-fontawesome */ \"./node_modules/@fortawesome/react-fontawesome/index.es.js\");\n/* harmony import */ var _fortawesome_free_solid_svg_icons__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @fortawesome/free-solid-svg-icons */ \"./node_modules/@fortawesome/free-solid-svg-icons/index.mjs\");\n/* harmony import */ var _fluentui_react_components__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @fluentui/react-components */ \"./node_modules/@fluentui/react-components/lib/index.js\");\n/* harmony import */ var _fluentui_react_icons__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @fluentui/react-icons */ \"./node_modules/@fluentui/react-icons/lib/index.js\");\n/* harmony import */ var ag_grid_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ag-grid-react */ \"./node_modules/ag-grid-react/lib/main.js\");\n/* harmony import */ var ag_grid_react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(ag_grid_react__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _services_apiConfig__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/services/apiConfig */ \"./services/apiConfig.js\");\n/* harmony import */ var react_toastify__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! react-toastify */ \"./node_modules/react-toastify/dist/react-toastify.esm.mjs\");\n/* harmony import */ var react_toastify_dist_ReactToastify_css__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! react-toastify/dist/ReactToastify.css */ \"./node_modules/react-toastify/dist/ReactToastify.css\");\n/* harmony import */ var react_toastify_dist_ReactToastify_css__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(react_toastify_dist_ReactToastify_css__WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var _utils_renderer_productActionRenderer__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/utils/renderer/productActionRenderer */ \"./utils/renderer/productActionRenderer.js\");\n/* harmony import */ var _utils_renderer_productReferenceRenderer__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/utils/renderer/productReferenceRenderer */ \"./utils/renderer/productReferenceRenderer.js\");\n/* harmony import */ var _utils_secureStorage__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/utils/secureStorage */ \"./utils/secureStorage.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_10___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_10__);\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n // Import useRef and useCallback\n\n\n\n\n\n\n\nconst useStyles = (0,_fluentui_react_components__WEBPACK_IMPORTED_MODULE_11__.makeStyles)({\n    root: {\n        ..._fluentui_react_components__WEBPACK_IMPORTED_MODULE_11__.shorthands.border(\"2px\", \"solid\", \"#ccc\"),\n        ..._fluentui_react_components__WEBPACK_IMPORTED_MODULE_11__.shorthands.overflow(\"hidden\"),\n        display: \"flex\",\n        height: \"480px\",\n        backgroundColor: \"#fff\"\n    },\n    content: {\n        ..._fluentui_react_components__WEBPACK_IMPORTED_MODULE_11__.shorthands.flex(1),\n        ..._fluentui_react_components__WEBPACK_IMPORTED_MODULE_11__.shorthands.padding(\"16px\"),\n        display: \"grid\",\n        justifyContent: \"flex-start\",\n        alignItems: \"flex-start\",\n        gridRowGap: _fluentui_react_components__WEBPACK_IMPORTED_MODULE_11__.tokens.spacingVerticalXXL,\n        gridAutoRows: \"max-content\"\n    },\n    field: {\n        display: \"grid\",\n        gridRowGap: _fluentui_react_components__WEBPACK_IMPORTED_MODULE_11__.tokens.spacingVerticalS\n    }\n});\nconst DrawerComponent = (param)=>{\n    let { isDrawerOpen, setIsDrawerOpen, title, dropdownData, placeholderText, legend, max_length, min_length, dataKey, onNewDropdownData, username, useremail, userData, prophetId } = param;\n    _s();\n    const [type, setType] = react__WEBPACK_IMPORTED_MODULE_1__.useState(\"overlay\");\n    const [description, setDescription] = react__WEBPACK_IMPORTED_MODULE_1__.useState(\"\");\n    const [removedRows, setRemovedRows] = react__WEBPACK_IMPORTED_MODULE_1__.useState(\"\");\n    const [value, setValue] = react__WEBPACK_IMPORTED_MODULE_1__.useState(\"\");\n    const [isButtonDisabled, setIsButtonDisabled] = react__WEBPACK_IMPORTED_MODULE_1__.useState(true);\n    const [isUpdateMode, setIsUpdateMode] = react__WEBPACK_IMPORTED_MODULE_1__.useState(false);\n    const [code, setCode] = react__WEBPACK_IMPORTED_MODULE_1__.useState(\"\");\n    const [rowData, setRowData] = react__WEBPACK_IMPORTED_MODULE_1__.useState([]);\n    const [addNewValue, setAddNewValue] = react__WEBPACK_IMPORTED_MODULE_1__.useState(false);\n    //const [keyy, setKey] =  React.useState(dataKey ? dataKey : \"\")\n    const serverAddress = _services_apiConfig__WEBPACK_IMPORTED_MODULE_4__.apiConfig.serverAddress;\n    const [isValidDescription, setisValidDescription] = react__WEBPACK_IMPORTED_MODULE_1__.useState(true);\n    const [isValidCode, setisValidCode] = react__WEBPACK_IMPORTED_MODULE_1__.useState(true);\n    // Define gridRef using useRef\n    const gridRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Define onFilterTextBoxChanged function using useCallback\n    const onFilterTextBoxChanged = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        gridRef.current.api.setQuickFilter(document.getElementById(\"filter-text-box\").value);\n    // setGridApi(params.api);\n    }, []);\n    const formatName = (params)=>{\n        // Check condition and return formatted value\n        // console.log(typeof params.data.is_new);\n        if (params.data.is_new === true) {\n            // console.log(\"inside here\");\n            // console.log(params.data.name);\n            return \"*\".concat(params.data.label);\n        }\n    //return null;\n    };\n    const formatCode = (params)=>{\n        // Check condition and return formatted value\n        if (params.data.is_new === true) {\n            // console.log(params.data.code);\n            return \"*\".concat(params.data.code);\n        }\n    //return null;\n    };\n    // Define defaultColDef and columnDefs using useMemo\n    const defaultColDef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>({\n            sortable: true,\n            filter: false,\n            resizable: true,\n            flex: 1,\n            suppressMenu: false\n        }), []);\n    const trimInputText = (input)=>{\n        return input.trim();\n    };\n    react__WEBPACK_IMPORTED_MODULE_1__.useEffect(()=>{\n        if (!addNewValue) {\n            setRowData(dropdownData);\n        }\n    }, []);\n    const columnDefs = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>[\n            {\n                headerName: \"Name\",\n                field: \"label\",\n                flex: \"3%\",\n                headerClass: \"header-with-border\",\n                cellStyle: {\n                    display: \"flex\"\n                },\n                valueFormatter: formatName,\n                cellStyle: function(params) {\n                    if (params.data.is_new === true || params.data.is_new === 1) {\n                        //Here you can check the value and based on that you can change the color\n                        return {\n                            color: \"red\"\n                        };\n                    } else {\n                        return null;\n                    }\n                }\n            },\n            {\n                headerName: \"Code\",\n                field: \"code\",\n                flex: \"2%\",\n                headerClass: \"header-with-border\",\n                cellStyle: {\n                    display: \"flex\"\n                },\n                valueFormatter: formatCode,\n                cellStyle: function(params) {\n                    if (params.data.is_new === true || params.data.is_new === 1) {\n                        //Here you can check the value and based on that you can change the color\n                        return {\n                            color: \"red\"\n                        };\n                    } else {\n                        return null;\n                    }\n                }\n            },\n            {\n                headerName: \"Is New\",\n                field: \"is_new\",\n                hide: true\n            },\n            {\n                headerName: \"Action\",\n                field: \"\",\n                headerClass: \"header-with-border\",\n                cellRenderer: (params)=>(0,_utils_renderer_productReferenceRenderer__WEBPACK_IMPORTED_MODULE_8__[\"default\"])(params, userData, isUpdateMode),\n                // cellRenderer: () => addRow(),\n                flex: \"2%\",\n                cellStyle: ()=>({\n                        display: \"flex\",\n                        justifyContent: \"center\",\n                        paddingRight: \"20px\"\n                    }),\n                sortable: false,\n                cellRendererParams: {\n                    setCode: setCode,\n                    setDescription: setDescription,\n                    setValue: setValue,\n                    setIsUpdateMode: setIsUpdateMode,\n                    setIsButtonDisabled: setIsButtonDisabled,\n                    isUpdateMode: isUpdateMode\n                },\n                onCellClicked: ()=>{\n                    setisValidDescription(true);\n                    setisValidCode(true);\n                // addRow();\n                }\n            }\n        ], []);\n    // console.log(\"isUpdateMode\",isUpdateMode);\n    // console.log('description: ',description,'\\ncode: ', code,'\\nvalue: ', value)\n    const handelupdate = ()=>{\n        const checkDescription = rowData.find((desc)=>desc.label == description && desc.value != value);\n        // console.log(code);\n        const checkProduct = rowData.find((product)=>product.code == code && product.value != value);\n        if (checkDescription) {\n            setisValidDescription(false);\n        // console.log(\"yes\");\n        } else {\n            setisValidDescription(true);\n        // console.log(\"no\");\n        }\n        if (checkProduct) {\n            setisValidCode(false);\n        // console.log(\"yes yes\");\n        } else {\n            setisValidCode(true);\n        // console.log(\"no no\");\n        }\n        if (!checkDescription && !checkProduct) {\n            const saveData = {\n                value: value,\n                description: description,\n                code: code,\n                is_new: true,\n                tableName: dataKey,\n                username: username,\n                useremail: useremail,\n                prophetId: prophetId\n            };\n            try {\n                fetch(\"\".concat(serverAddress, \"products/update-all-dropdown-value\"), {\n                    method: \"PUT\",\n                    headers: {\n                        \"Content-Type\": \"application/json\"\n                    },\n                    credentials: \"include\",\n                    body: JSON.stringify(saveData)\n                }).then((res)=>{\n                    if (res.status === 401) {\n                        react_toastify__WEBPACK_IMPORTED_MODULE_5__.toast.error(\"Your session has expired. Please log in again.\");\n                        setTimeout(async ()=>{\n                            await (0,_utils_secureStorage__WEBPACK_IMPORTED_MODULE_9__.logout)();\n                            router.push(\"/login\");\n                        }, 3000);\n                    }\n                    if (res.status === 400) {\n                        react_toastify__WEBPACK_IMPORTED_MODULE_5__.toast.error(\"There was an error with your request. Please check your data and try again.\");\n                    }\n                    if (res.status === 200) {\n                        return res.json();\n                    } else {\n                        react_toastify__WEBPACK_IMPORTED_MODULE_5__.toast.error(\"Failed to save raw material.\");\n                    //setLoading(false);\n                    }\n                }).then((json)=>{\n                    // console.log(json);\n                    if (dataKey == \"masterProductCode\") {\n                        onNewDropdownData(dataKey, json.masterProductCode);\n                        setRowData(json.masterProductCode);\n                        react_toastify__WEBPACK_IMPORTED_MODULE_5__.toast.success(\"Updated New Master Code.\", {\n                            position: \"top-left\"\n                        });\n                    } else if (dataKey == \"markVariety\") {\n                        setRowData(json.markVariety);\n                        onNewDropdownData(dataKey, json.markVariety);\n                        react_toastify__WEBPACK_IMPORTED_MODULE_5__.toast.success(\"Updated New Mark Variety.\", {\n                            position: \"top-left\"\n                        });\n                    } else if (dataKey == \"brand\") {\n                        setRowData(json.brand);\n                        onNewDropdownData(dataKey, json.brand);\n                        react_toastify__WEBPACK_IMPORTED_MODULE_5__.toast.success(\"Updated New Brand.\", {\n                            position: \"top-left\"\n                        });\n                    } else if (dataKey == \"end_customer\") {\n                        setRowData(json.endCustomer);\n                        onNewDropdownData(dataKey, json.endCustomer);\n                        react_toastify__WEBPACK_IMPORTED_MODULE_5__.toast.success(\"Updated New End Customer.\", {\n                            position: \"top-left\"\n                        });\n                    } else if (dataKey == \"countryOfOrigin\") {\n                        setRowData(json.countryOfOrigin);\n                        onNewDropdownData(dataKey, json.countryOfOrigin);\n                        react_toastify__WEBPACK_IMPORTED_MODULE_5__.toast.success(\"Updated New Country of Origin.\", {\n                            position: \"top-left\"\n                        });\n                    } else if (dataKey == \"caliberSize\") {\n                        setRowData(json.caliberSize);\n                        onNewDropdownData(dataKey, json.caliberSize);\n                        react_toastify__WEBPACK_IMPORTED_MODULE_5__.toast.success(\"Updated New Caliber Size.\", {\n                            position: \"top-left\"\n                        });\n                    } else if (dataKey == \"variety\") {\n                        setRowData(json.variety);\n                        onNewDropdownData(dataKey, json.variety);\n                        react_toastify__WEBPACK_IMPORTED_MODULE_5__.toast.success(\"Updated New Variety.\", {\n                            position: \"top-left\"\n                        });\n                    } else if (dataKey == \"newOuterBoxType\") {\n                        setRowData(json.newOuterBoxType);\n                        onNewDropdownData(dataKey, json.newOuterBoxType);\n                        react_toastify__WEBPACK_IMPORTED_MODULE_5__.toast.success(\"Updated New Box Type.\", {\n                            position: \"top-left\"\n                        });\n                    }\n                    setDescription(\"\");\n                    setCode(\"\");\n                    setIsButtonDisabled(true);\n                    setIsUpdateMode(false);\n                    setAddNewValue(true);\n                });\n            } catch (error) {\n                console.error(\"Failed to save reference code.\", error);\n            }\n        } else {\n            // toast.error(\"Product code or description already exist.\", {\n            //   position: \"top-left\",\n            // });\n            console.error(\"Failed to save reference code.\");\n        }\n    };\n    const handleAdd = ()=>{\n        const checkDescription = rowData.find((desc)=>desc.label == description);\n        const checkProduct = rowData.find((product)=>product.code == code);\n        if (checkDescription) {\n            setisValidDescription(false);\n        } else {\n            setisValidDescription(true);\n        }\n        if (checkProduct) {\n            setisValidCode(false);\n        } else {\n            setisValidCode(true);\n        }\n        // console.log('checkDescription: ',checkDescription,'\\n checkProduct: ', checkProduct)\n        if (!checkDescription && !checkProduct) {\n            const saveData = {\n                description: description,\n                code: code,\n                is_new: true,\n                tableName: dataKey,\n                username: username,\n                useremail: useremail,\n                prophetId: prophetId\n            };\n            try {\n                fetch(\"\".concat(serverAddress, \"products/add-all-dropdown-value\"), {\n                    method: \"POST\",\n                    headers: {\n                        \"Content-Type\": \"application/json\"\n                    },\n                    credentials: \"include\",\n                    body: JSON.stringify(saveData)\n                }).then((res)=>{\n                    if (res.status === 401) {\n                        react_toastify__WEBPACK_IMPORTED_MODULE_5__.toast.error(\"Your session has expired. Please log in again.\");\n                        setTimeout(async ()=>{\n                            await (0,_utils_secureStorage__WEBPACK_IMPORTED_MODULE_9__.logout)();\n                            router.push(\"/login\");\n                        }, 3000);\n                    }\n                    if (res.status === 400) {\n                        react_toastify__WEBPACK_IMPORTED_MODULE_5__.toast.error(\"There was an error with your request. Please check your data and try again.\");\n                    }\n                    if (res.status === 200) {\n                        return res.json();\n                    } else {\n                        react_toastify__WEBPACK_IMPORTED_MODULE_5__.toast.error(\"Failed to save raw material.\");\n                    //setLoading(false);\n                    }\n                }).then((json)=>{\n                    // console.log(json);\n                    if (dataKey == \"masterProductCode\") {\n                        onNewDropdownData(dataKey, json.masterProductCode);\n                        setRowData(json.masterProductCode);\n                        react_toastify__WEBPACK_IMPORTED_MODULE_5__.toast.success(\"Added New Master Code.\", {\n                            position: \"top-left\"\n                        });\n                        setDescription(\"\");\n                        setCode(\"\");\n                        setIsButtonDisabled(true);\n                    } else if (dataKey == \"markVariety\") {\n                        setRowData(json.markVariety);\n                        onNewDropdownData(dataKey, json.markVariety);\n                        react_toastify__WEBPACK_IMPORTED_MODULE_5__.toast.success(\"Added New Mark Variety.\", {\n                            position: \"top-left\"\n                        });\n                        setDescription(\"\");\n                        setCode(\"\");\n                        setIsButtonDisabled(true);\n                    } else if (dataKey == \"brand\") {\n                        setRowData(json.brand);\n                        onNewDropdownData(dataKey, json.brand);\n                        react_toastify__WEBPACK_IMPORTED_MODULE_5__.toast.success(\"Added New Brand.\", {\n                            position: \"top-left\"\n                        });\n                        setDescription(\"\");\n                        setCode(\"\");\n                        setIsButtonDisabled(true);\n                    } else if (dataKey == \"end_customer\") {\n                        setRowData(json.endCustomer);\n                        onNewDropdownData(dataKey, json.endCustomer);\n                        react_toastify__WEBPACK_IMPORTED_MODULE_5__.toast.success(\"Added New End Customer.\", {\n                            position: \"top-left\"\n                        });\n                        setDescription(\"\");\n                        setCode(\"\");\n                        setIsButtonDisabled(true);\n                    } else if (dataKey == \"countryOfOrigin\") {\n                        setRowData(json.countryOfOrigin);\n                        onNewDropdownData(dataKey, json.countryOfOrigin);\n                        react_toastify__WEBPACK_IMPORTED_MODULE_5__.toast.success(\"Added New Country of Origin.\", {\n                            position: \"top-left\"\n                        });\n                        setDescription(\"\");\n                        setCode(\"\");\n                        setIsButtonDisabled(true);\n                    } else if (dataKey == \"caliberSize\") {\n                        setRowData(json.caliberSize);\n                        onNewDropdownData(dataKey, json.caliberSize);\n                        react_toastify__WEBPACK_IMPORTED_MODULE_5__.toast.success(\"Added New Caliber Size.\", {\n                            position: \"top-left\"\n                        });\n                        setDescription(\"\");\n                        setCode(\"\");\n                        setIsButtonDisabled(true);\n                    } else if (dataKey == \"variety\") {\n                        setRowData(json.variety);\n                        onNewDropdownData(dataKey, json.variety);\n                        react_toastify__WEBPACK_IMPORTED_MODULE_5__.toast.success(\"Added New Variety.\", {\n                            position: \"top-left\"\n                        });\n                        setDescription(\"\");\n                        setCode(\"\");\n                        setIsButtonDisabled(true);\n                    } else if (dataKey == \"newOuterBoxType\") {\n                        setRowData(json.newOuterBoxType);\n                        onNewDropdownData(dataKey, json.newOuterBoxType);\n                        react_toastify__WEBPACK_IMPORTED_MODULE_5__.toast.success(\"Added New Box Type.\", {\n                            position: \"top-left\"\n                        });\n                        setDescription(\"\");\n                        setCode(\"\");\n                        setIsButtonDisabled(true);\n                    }\n                    setAddNewValue(true);\n                });\n            } catch (error) {\n                console.error(\"Failed to save reference code.\", error);\n            }\n        } else {\n            console.error(\"Failed to save reference code.\");\n        }\n    };\n    const getRowStyle = (params)=>{\n        if (params.data.is_new === true) {\n            // Apply custom styling for rows where is_new is true\n            return {\n                color: \"red !important\"\n            }; // Example background color\n        }\n        return null;\n    };\n    return(// <div className={styles.root}>\n    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_toastify__WEBPACK_IMPORTED_MODULE_5__.ToastContainer, {\n                limit: 1\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\DrawerComponent.js\",\n                lineNumber: 480,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fluentui_react_components__WEBPACK_IMPORTED_MODULE_11__.Drawer, {\n                    type: type,\n                    separator: true,\n                    open: isDrawerOpen,\n                    position: \"end\",\n                    className: \"!bg-white !w-[450px] p-3 px-5 !shadow-lg !border-0\",\n                    onOpenChange: (_, param)=>{\n                        let { open } = param;\n                        return setIsDrawerOpen(open);\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fluentui_react_components__WEBPACK_IMPORTED_MODULE_11__.DrawerHeader, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fluentui_react_components__WEBPACK_IMPORTED_MODULE_11__.DrawerHeaderTitle, {\n                                action: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fluentui_react_components__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                    appearance: \"subtle\",\n                                    \"aria-label\": \"Close\",\n                                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fluentui_react_icons__WEBPACK_IMPORTED_MODULE_12__.Dismiss24Regular, {}, void 0, false, void 0, void 0),\n                                    onClick: ()=>setIsDrawerOpen(false)\n                                }, void 0, false, void 0, void 0),\n                                className: \"font-bold\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"font-bold\",\n                                    children: title\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\DrawerComponent.js\",\n                                    lineNumber: 502,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\DrawerComponent.js\",\n                                lineNumber: 491,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\DrawerComponent.js\",\n                            lineNumber: 490,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fluentui_react_components__WEBPACK_IMPORTED_MODULE_11__.DrawerBody, {\n                            className: \"!max-h-full !overflow-hidden\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"border rounded-md relative mt-3 mb-3 \",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"absolute -top-3 left-5 bg-white z-50 w-auto inline px-3\",\n                                                style: {\n                                                    opacity: !isUpdateMode ? 1 : 0,\n                                                    transform: !isUpdateMode ? \"scale(1)\" : \"scale(0.9)\",\n                                                    transition: \"opacity 0.1s ease, transform 0.1s ease\",\n                                                    pointerEvents: !isUpdateMode ? \"auto\" : \"none\"\n                                                },\n                                                children: legend\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\DrawerComponent.js\",\n                                                lineNumber: 509,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                onClick: ()=>{\n                                                    setIsUpdateMode(false);\n                                                    setIsButtonDisabled(true);\n                                                    setDescription(\"\");\n                                                    setCode(\"\");\n                                                    // setRemovedRows(\"\");\n                                                    setValue(\"\");\n                                                    setisValidCode(true);\n                                                    setisValidDescription(true);\n                                                // addRow();\n                                                // console.log(\"removedRows\",removedRows);\n                                                },\n                                                style: {\n                                                    opacity: isUpdateMode ? 1 : 0,\n                                                    transform: isUpdateMode ? \"scale(1)\" : \"scale(0.9)\",\n                                                    transition: \"opacity 0.1s ease, transform 0.1s ease\",\n                                                    pointerEvents: isUpdateMode ? \"auto\" : \"none\"\n                                                },\n                                                className: \"absolute flex items-center justify-center border rounded-md border-skin-primary -top-3 right-5 text-skin-primary bg-white z-50 cursor-pointer shadow-sm hover:bg-gray-50 transition-all duration-300 ease-in-out\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fortawesome_react_fontawesome__WEBPACK_IMPORTED_MODULE_2__.FontAwesomeIcon, {\n                                                        icon: _fortawesome_free_solid_svg_icons__WEBPACK_IMPORTED_MODULE_13__.faPlus,\n                                                        className: \"px-1 py-1 text-skin-primary cursor-pointer rotate-45 transition-all duration-300 ease-in-out transform\",\n                                                        title: \"Go back to add new \".concat(title)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\DrawerComponent.js\",\n                                                        lineNumber: 542,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"mr-[5px]\",\n                                                        children: \"Cancel\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\DrawerComponent.js\",\n                                                        lineNumber: 547,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\DrawerComponent.js\",\n                                                lineNumber: 521,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"hidden\",\n                                                name: \"type\",\n                                                value: \"product\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\DrawerComponent.js\",\n                                                lineNumber: 550,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex flex-col w-full p-4 pt-5 overflow-hidden\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        id: \"description\",\n                                                        name: \"description\",\n                                                        maxLength: 50,\n                                                        className: \"w-full py-1 px-2 2xl:px-3 2xl:py-3 border rounded-md\",\n                                                        placeholder: \"Description\",\n                                                        onChange: (e)=>{\n                                                            setDescription(e.target.value);\n                                                            setisValidDescription(true);\n                                                            setIsButtonDisabled(e.target.value === \"\" || code === \"\");\n                                                        },\n                                                        onBlur: (e)=>{\n                                                            const trimmedValue = trimInputText(e.target.value);\n                                                            setDescription(trimmedValue);\n                                                            setisValidDescription(true);\n                                                            setIsButtonDisabled(trimmedValue === \"\" || code === \"\");\n                                                        },\n                                                        // style={{ textTransform: \"capitalize\" }}\n                                                        value: description\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\DrawerComponent.js\",\n                                                        lineNumber: 552,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    !isValidDescription && // ?\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-red-500 text-xs mt-1 ml-1\",\n                                                        children: \"Description Exists Please Enter Different Description\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\DrawerComponent.js\",\n                                                        lineNumber: 575,\n                                                        columnNumber: 23\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex flex-row mt-2 transition-all duration-300 ease-in-out\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex flex-col w-full \",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                        id: \"addNew\",\n                                                                        name: \"add new\",\n                                                                        className: \"w-full py-1 px-2 2xl:px-3 2xl:py-3 border rounded-md \",\n                                                                        onChange: (e)=>{\n                                                                            // console.log(\"max_length\",min_length - 1, e.target.value.length);\n                                                                            // console.log(\"hi:\");\n                                                                            if (e.target.value.length <= max_length) {\n                                                                                setCode(e.target.value.toUpperCase());\n                                                                                setisValidCode(true);\n                                                                                setIsButtonDisabled(e.target.value === \"\" || description === \"\" || (typeof e.target.value === \"number\" ? e.target.value.toString().length < min_length : e.target.value.length < min_length));\n                                                                            }\n                                                                        },\n                                                                        onBlur: (e)=>{\n                                                                            const trimmedValue = trimInputText(e.target.value);\n                                                                            if (trimmedValue.length <= max_length) {\n                                                                                setCode(trimmedValue.toUpperCase());\n                                                                                setisValidCode(true);\n                                                                                setIsButtonDisabled(e.target.value === \"\" || description === \"\" || (typeof e.target.value === \"number\" ? e.target.value.toString().length < min_length : e.target.value.length < min_length));\n                                                                            } else {\n                                                                                setisValidCode(false);\n                                                                            }\n                                                                        },\n                                                                        placeholder: placeholderText,\n                                                                        // style={{ textTransform: \"uppercase\" }}\n                                                                        value: code\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\DrawerComponent.js\",\n                                                                        lineNumber: 587,\n                                                                        columnNumber: 23\n                                                                    }, undefined),\n                                                                    !isValidCode && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-red-500 text-xs mt-1 ml-1\",\n                                                                        children: \"Code Already Exists\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\DrawerComponent.js\",\n                                                                        lineNumber: 628,\n                                                                        columnNumber: 25\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\DrawerComponent.js\",\n                                                                lineNumber: 586,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                className: \"border border-skin-primary text-white bg-skin-primary py-1 px-5 ml-4 \".concat(!isValidCode ? \"mb-[18px]\" : \"\", ' w-[130px] font-medium rounded-md scale-x-100\"\\n                      }'),\n                                                                onClick: isUpdateMode ? handelupdate : handleAdd,\n                                                                disabled: isButtonDisabled,\n                                                                children: isUpdateMode ? \"Update\" : \"Add New\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\DrawerComponent.js\",\n                                                                lineNumber: 633,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\DrawerComponent.js\",\n                                                        lineNumber: 585,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\DrawerComponent.js\",\n                                                lineNumber: 551,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\DrawerComponent.js\",\n                                        lineNumber: 508,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-col\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"relative block w-full text-gray-400 mt-0 pt-0 mb-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"absolute px-4 py-1 pt-[0.4rem] 2xl:pt-1.5 text-black\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fortawesome_react_fontawesome__WEBPACK_IMPORTED_MODULE_2__.FontAwesomeIcon, {\n                                                            icon: _fortawesome_free_solid_svg_icons__WEBPACK_IMPORTED_MODULE_13__.faSearch,\n                                                            className: \"fw-bold\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\DrawerComponent.js\",\n                                                            lineNumber: 649,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\DrawerComponent.js\",\n                                                        lineNumber: 648,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"text\",\n                                                        id: \"filter-text-box\",\n                                                        placeholder: \"Search\",\n                                                        onInput: onFilterTextBoxChanged,\n                                                        className: \"block w-full px-4 pl-10 text-gray-500 placeholder-gray-400 searchbar border rounded-lg appearance-none form-input focus:outline-none\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\DrawerComponent.js\",\n                                                        lineNumber: 651,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\DrawerComponent.js\",\n                                                lineNumber: 647,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"!rounded-md px-5 border border-gray-300\",\n                                                style: {\n                                                    height: \"calc(100vh - 210px)\"\n                                                },\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ag_grid_react__WEBPACK_IMPORTED_MODULE_3__.AgGridReact, {\n                                                    rowData: rowData,\n                                                    ref: gridRef,\n                                                    columnDefs: columnDefs,\n                                                    defaultColDef: defaultColDef,\n                                                    getRowStyle: getRowStyle\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\DrawerComponent.js\",\n                                                    lineNumber: 663,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\DrawerComponent.js\",\n                                                lineNumber: 659,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\DrawerComponent.js\",\n                                        lineNumber: 646,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\DrawerComponent.js\",\n                                lineNumber: 507,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\DrawerComponent.js\",\n                            lineNumber: 506,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\DrawerComponent.js\",\n                    lineNumber: 482,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\DrawerComponent.js\",\n                lineNumber: 481,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true));\n};\n_s(DrawerComponent, \"zpbapB7wDtu66vJweoDKOcOYtUI=\");\n_c = DrawerComponent;\n/* harmony default export */ __webpack_exports__[\"default\"] = (DrawerComponent);\nvar _c;\n$RefreshReg$(_c, \"DrawerComponent\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevExports = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevExports) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports on update so we can compare the boundary\n                // signatures.\n                module.hot.dispose(function (data) {\n                    data.prevExports = currentExports;\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevExports !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevExports, currentExports)) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevExports !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/DrawerComponent.js\n"));

/***/ })

});