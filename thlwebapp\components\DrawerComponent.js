import * as React from "react";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faPlus, faSearch } from "@fortawesome/free-solid-svg-icons";
import {
  Drawer<PERSON><PERSON>,
  DrawerHeader,
  DrawerHeaderTitle,
  Drawer,
  Button,
  Label,
  Radio,
  RadioGroup,
  makeStyles,
  shorthands,
  tokens,
  useId,
} from "@fluentui/react-components";
import { Dismiss24Regular } from "@fluentui/react-icons";
import { AgGridReact } from "ag-grid-react";
import { useRef, useCallback, useMemo } from "react"; // Import useRef and useCallback
import { apiConfig } from "@/services/apiConfig";
import { add } from "lodash";
import { ToastContainer, toast } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import productActionRenderer from "@/utils/renderer/productActionRenderer";
import productReferenceRenderer from "@/utils/renderer/productReferenceRenderer";
// import { GridApi } from "ag-grid-community";

const useStyles = makeStyles({
  root: {
    ...shorthands.border("2px", "solid", "#ccc"),
    ...shorthands.overflow("hidden"),
    display: "flex",
    height: "480px",
    backgroundColor: "#fff",
  },
  content: {
    ...shorthands.flex(1),
    ...shorthands.padding("16px"),
    display: "grid",
    justifyContent: "flex-start",
    alignItems: "flex-start",
    gridRowGap: tokens.spacingVerticalXXL,
    gridAutoRows: "max-content",
  },
  field: {
    display: "grid",
    gridRowGap: tokens.spacingVerticalS,
  },
});

export const DrawerComponent = ({
  isDrawerOpen,
  setIsDrawerOpen,
  title,
  dropdownData,
  placeholderText,
  legend,
  max_length,
  min_length,
  dataKey,
  onNewDropdownData,username,useremail,userData,prophetId
}) => {
  console.log("dropdownData",onNewDropdownData);
  const styles = useStyles();
  const labelId = useId("type-label");
  //const [isOpen, setIsOpen] = React.useState(true);
  const [type, setType] = React.useState("overlay");
  const [description, setDescription] = React.useState("");
  const [removedRows, setRemovedRows] = React.useState("");
  const [value, setValue] = React.useState("");
  const [isButtonDisabled, setIsButtonDisabled] = React.useState(true);
  const [isUpdateMode, setIsUpdateMode] = React.useState(false);
  const [code, setCode] = React.useState("");
  const [rowData, setRowData] = React.useState([]);
  const [addNewValue, setAddNewValue] = React.useState(false);
  //const [keyy, setKey] =  React.useState(dataKey ? dataKey : "")
  const serverAddress = apiConfig.serverAddress;
  const [isValidDescription, setisValidDescription] = React.useState(true)
  const [isValidCode, setisValidCode] = React.useState(true)


  // Define gridRef using useRef
  const gridRef = useRef(null);
  // Define onFilterTextBoxChanged function using useCallback
  const onFilterTextBoxChanged = useCallback(() => {
    gridRef.current.api.setQuickFilter(
      document.getElementById("filter-text-box").value
    );
    // setGridApi(params.api);
  }, []);

  const formatName = (params) => {
    // Check condition and return formatted value
    // console.log(typeof params.data.is_new);
    if (params.data.is_new === true) {
      // console.log("inside here");
      // console.log(params.data.name);
      return `*${params.data.label}`;
    }
    //return null;
  };

  const formatCode = (params) => {
    // Check condition and return formatted value
    if (params.data.is_new === true) {
      // console.log(params.data.code);
      return `*${params.data.code}`;
    }
    //return null;
  };

  // Define defaultColDef and columnDefs using useMemo
  const defaultColDef = useMemo(
    () => ({
      sortable: true,
      filter: false,
      resizable: true,
      flex: 1,
      suppressMenu: false,
    }),
    []
  );

  const trimInputText = (input) => {
    return input.trim();
  };

  React.useEffect(() => {
    if (!addNewValue) {
      setRowData(dropdownData);
    }
  }, []);
  const columnDefs = useMemo(
    () => [
      {
        headerName: "Name",
        field: "label",
        flex: "3%",
        headerClass: "header-with-border",
        cellStyle: { display: "flex" },
        valueFormatter: formatName,
        cellStyle: function (params) {
          if (params.data.is_new === true || params.data.is_new === 1) {
            //Here you can check the value and based on that you can change the color
            return { color: "red" };
          } else {
            return null;
          }
        },
      },
      {
        headerName: "Code",
        field: "code",
        flex: "2%",
        headerClass: "header-with-border",
        cellStyle: { display: "flex" },
        valueFormatter: formatCode,
        cellStyle: function (params) {
          if (params.data.is_new === true || params.data.is_new === 1) {
            //Here you can check the value and based on that you can change the color
            return { color: "red" };
          } else {
            return null;
          }
        },
      },
      {
        headerName: "Is New",
        field: "is_new",
        hide: true, // Hide the column from being displayed
      },
      {
        headerName: "Action",
        field: "",
        headerClass: "header-with-border",
        cellRenderer: (params) => productReferenceRenderer(params, userData, isUpdateMode),
        // cellRenderer: () => addRow(),
        flex: "2%",
        cellStyle: () => ({
          display: "flex",
          justifyContent: "center",
          paddingRight: "20px",
        }),
        sortable: false,
        cellRendererParams: {
          setCode: setCode,
          setDescription: setDescription,
          setValue: setValue,
          setIsUpdateMode:setIsUpdateMode,
          setIsButtonDisabled:setIsButtonDisabled,
          isUpdateMode:isUpdateMode,
          // addRow:addRow,
          // removedRows:removedRows,
        },
        onCellClicked: () => {
          setisValidDescription(true);
          setisValidCode(true);
          // addRow();
        },
      },
    ],
    []
  );
  // console.log("isUpdateMode",isUpdateMode);

  // console.log('description: ',description,'\ncode: ', code,'\nvalue: ', value)

  const handelupdate = () => {
    const checkDescription = rowData.find((desc) => desc.label == description && desc.value != value);
    // console.log(code);
    const checkProduct = rowData.find((product) => product.code == code && product.value != value);
    if(checkDescription){
      setisValidDescription(false);
      // console.log("yes");
    } else {
      setisValidDescription(true);
      // console.log("no");

    }
    if(checkProduct){
      setisValidCode(false);
      // console.log("yes yes");

    } else{
      setisValidCode(true);
      // console.log("no no");
    }
    if (!checkDescription && !checkProduct) {
      const saveData = {
        value: value,
        description: description,
        code: code,
        is_new: true,
        tableName: dataKey,
        username:username,
        useremail:useremail,
        prophetId:prophetId
      };
      try {
        fetch(`${serverAddress}products/update-all-dropdown-value`, {
          method: "PUT",
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${userData.token}`,
          },
          body: JSON.stringify(saveData),
        })
        .then((res) => {
          if (res.status === 200) {
            return res.json();
          } else {
            toast.error("Failed to save raw material.");
            //setLoading(false);
          }
        })
        .then((json) => {
          // console.log(json);
          if (dataKey == "masterProductCode") {
            onNewDropdownData(dataKey, json.masterProductCode);
            setRowData(json.masterProductCode);
            toast.success("Updated New Master Code.", {
              position: "top-left",
            });
          } else if (dataKey == "markVariety") {
            setRowData(json.markVariety);
            onNewDropdownData(dataKey, json.markVariety);
            toast.success("Updated New Mark Variety.", {
              position: "top-left",
            });
          } else if (dataKey == "brand") {
            setRowData(json.brand);
            onNewDropdownData(dataKey, json.brand);
            toast.success("Updated New Brand.", {
              position: "top-left",
            });
          } else if (dataKey == "end_customer") {
            setRowData(json.endCustomer);
            onNewDropdownData(dataKey, json.endCustomer);
            toast.success("Updated New End Customer.", {
              position: "top-left",
            });
          } else if (dataKey == "countryOfOrigin") {
            setRowData(json.countryOfOrigin);
            onNewDropdownData(dataKey, json.countryOfOrigin);
            toast.success("Updated New Country of Origin.", {
              position: "top-left",
            });
          } else if (dataKey == "caliberSize") {
            setRowData(json.caliberSize);
            onNewDropdownData(dataKey, json.caliberSize);
            toast.success("Updated New Caliber Size.", {
              position: "top-left",
            });
          } else if (dataKey == "variety") {
            setRowData(json.variety);
            onNewDropdownData(dataKey, json.variety);
            toast.success("Updated New Variety.", {
              position: "top-left",
            });
          } else if (dataKey == "newOuterBoxType") {
            setRowData(json.newOuterBoxType);
            onNewDropdownData(dataKey, json.newOuterBoxType);
            toast.success("Updated New Box Type.", {
              position: "top-left",
            });
          }
          setDescription("");
          setCode("");
          setIsButtonDisabled(true);
          setIsUpdateMode(false);
          setAddNewValue(true);
          //setIsDrawerOpen(false);
          //alert("after new add")
        });
      } catch (error) {
        // toast.error("Failed to save reference code.", {
        //   position: "top-left",
        // });
        console.error("Failed to save reference code.", error);
        //setLoading(false);
      }
    } else {
      // toast.error("Product code or description already exist.", {
      //   position: "top-left",
      // });
      console.error("Failed to save reference code.",);
    }
  }
  const handleAdd = () => {
    const checkDescription = rowData.find((desc) => desc.label == description);
    const checkProduct = rowData.find((product) => product.code == code);
    if(checkDescription){
      setisValidDescription(false);
    } else {
      setisValidDescription(true);
    }
    if(checkProduct){
      setisValidCode(false);
    } else{
      setisValidCode(true);
    }
    // console.log('checkDescription: ',checkDescription,'\n checkProduct: ', checkProduct)

    if (!checkDescription && !checkProduct) {
      
      const saveData = {
        description: description,
        code: code,
        is_new: true,
        tableName: dataKey,
        username:username,
        useremail:useremail,
        prophetId:prophetId
      };
      try {
        fetch(`${serverAddress}products/add-all-dropdown-value`, {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${userData.token}`,
          },
          body: JSON.stringify(saveData),
        })
        .then((res) => {
          if (res.status === 200) {
            return res.json();
          } else {
            toast.error("Failed to save raw material.");
            //setLoading(false);
          }
        })
        .then((json) => {
          // console.log(json);
          if (dataKey == "masterProductCode") {
            onNewDropdownData(dataKey, json.masterProductCode);
            setRowData(json.masterProductCode);
            toast.success("Added New Master Code.", {
              position: "top-left",
            });
            setDescription("");
            setCode("");
            setIsButtonDisabled(true);
          } else if (dataKey == "markVariety") {
            setRowData(json.markVariety);
            onNewDropdownData(dataKey, json.markVariety);
            toast.success("Added New Mark Variety.", {
              position: "top-left",
            });
            setDescription("");
            setCode("");
            setIsButtonDisabled(true);
          } else if (dataKey == "brand") {
            setRowData(json.brand);
            onNewDropdownData(dataKey, json.brand);
            toast.success("Added New Brand.", {
              position: "top-left",
            });
            setDescription("");
            setCode("");
            setIsButtonDisabled(true);
          } else if (dataKey == "end_customer") {
            setRowData(json.endCustomer);
            onNewDropdownData(dataKey, json.endCustomer);
            toast.success("Added New End Customer.", {
              position: "top-left",
            });
            setDescription("");
            setCode("");
            setIsButtonDisabled(true);
          } else if (dataKey == "countryOfOrigin") {
            setRowData(json.countryOfOrigin);
            onNewDropdownData(dataKey, json.countryOfOrigin);
            toast.success("Added New Country of Origin.", {
              position: "top-left",
            });
            setDescription("");
            setCode("");
            setIsButtonDisabled(true);
          } else if (dataKey == "caliberSize") {
            setRowData(json.caliberSize);
            onNewDropdownData(dataKey, json.caliberSize);
            toast.success("Added New Caliber Size.", {
              position: "top-left",
            });
            setDescription("");
            setCode("");
            setIsButtonDisabled(true);
          } else if (dataKey == "variety") {
            setRowData(json.variety);
            onNewDropdownData(dataKey, json.variety);
            toast.success("Added New Variety.", {
              position: "top-left",
            });
            setDescription("");
            setCode("");
            setIsButtonDisabled(true);
          } else if (dataKey == "newOuterBoxType") {
            setRowData(json.newOuterBoxType);
            onNewDropdownData(dataKey, json.newOuterBoxType);
            toast.success("Added New Box Type.", {
              position: "top-left",
            });
            setDescription("");
            setCode("");
            setIsButtonDisabled(true);
          }
          setAddNewValue(true);
        });
      } catch (error) {
        console.error("Failed to save reference code.", error);
      }
    } else {
      console.error("Failed to save reference code.",);
    }
  };

  const getRowStyle = (params) => {
    if (params.data.is_new === true) {
      // Apply custom styling for rows where is_new is true
      return { color: "red !important" }; // Example background color
    }
    return null;
  };

  return (
    // <div className={styles.root}>
    <>
      <ToastContainer limit={1} />
      <div className="">
        <Drawer
          type={type}
          separator
          open={isDrawerOpen}
          position="end"
          className="!bg-white !w-[450px] p-3 px-5 !shadow-lg !border-0"
          onOpenChange={(_, { open }) => setIsDrawerOpen(open)}
        >
          <DrawerHeader>
            <DrawerHeaderTitle
              action={
                <Button
                  appearance="subtle"
                  aria-label="Close"
                  icon={<Dismiss24Regular />}
                  onClick={() => setIsDrawerOpen(false)}
                />
              }
              className="font-bold"
            >
              <div className="font-bold">{title}</div>
            </DrawerHeaderTitle>
          </DrawerHeader>

          <DrawerBody className="!max-h-full !overflow-hidden">
            <div className="flex flex-col">
              <div className="border rounded-md relative mt-3 mb-3 ">
                <h3
                  className="absolute -top-3 left-5 bg-white z-50 w-auto inline px-3"
                  style={{
                    opacity: !isUpdateMode ? 1 : 0,
                    transform: !isUpdateMode ? "scale(1)" : "scale(0.9)",
                    transition: "opacity 0.1s ease, transform 0.1s ease",
                    pointerEvents: !isUpdateMode ? "auto" : "none",
                  }}
                >
                  {/* {isUpdateMode ? "" : legend} */}
                  {legend}
                </h3>
                <h3
                  onClick={() => {
                    setIsUpdateMode(false);
                    setIsButtonDisabled(true);
                    setDescription("");
                    setCode("");
                    // setRemovedRows("");
                    setValue("");
                    setisValidCode(true);
                    setisValidDescription(true);
                    // addRow();
                    // console.log("removedRows",removedRows);
                  }}
                  style={{
                    opacity: isUpdateMode ? 1 : 0,
                    transform: isUpdateMode ? "scale(1)" : "scale(0.9)",
                    transition: "opacity 0.1s ease, transform 0.1s ease",
                    pointerEvents: isUpdateMode ? "auto" : "none",
                  }}
                  className="absolute flex items-center justify-center border rounded-md border-skin-primary -top-3 right-5 text-skin-primary bg-white z-50 cursor-pointer shadow-sm hover:bg-gray-50 transition-all duration-300 ease-in-out"
                >
                  <FontAwesomeIcon
                    icon={faPlus}
                    className="px-1 py-1 text-skin-primary cursor-pointer rotate-45 transition-all duration-300 ease-in-out transform"
                    title={`Go back to add new ${title}`}
                  />
                  <span className="mr-[5px]">Cancel</span>
                </h3>

                <input type="hidden" name="type" value="product" />
                <div className="flex flex-col w-full p-4 pt-5 overflow-hidden">
                  <input
                    id="description"
                    name="description"
                    maxLength={50}
                    className={`w-full py-1 px-2 2xl:px-3 2xl:py-3 border rounded-md`}
                    placeholder="Description"
                    onChange={(e) => {
                      setDescription(e.target.value);
                      setisValidDescription(true);
                      setIsButtonDisabled(e.target.value === "" || code === "");
                    }}
                    onBlur={(e) => {
                      const trimmedValue = trimInputText(e.target.value);
                      setDescription(trimmedValue);
                      setisValidDescription(true);
                      setIsButtonDisabled(trimmedValue === "" || code === "");
                    }}
                    // style={{ textTransform: "capitalize" }}
                    value={description}
                  />
                  {
                    !isValidDescription && (
                      // ?
                      <span className="text-red-500 text-xs mt-1 ml-1">
                        Description Exists Please Enter Different Description
                      </span>
                    )
                    // :
                    // (
                    //   <span className="mt-[17px]"></span>
                    // )
                  }
                  {/* //todo-handle for other ones which have max 2 digits and all instead of setting 5 digits in general */}
                  <div className="flex flex-row mt-2 transition-all duration-300 ease-in-out">
                    <div className={`flex flex-col w-full `}>
                      <input
                        id="addNew"
                        name="add new"
                        className="w-full py-1 px-2 2xl:px-3 2xl:py-3 border rounded-md "
                        onChange={(e) => {
                          // console.log("max_length",min_length - 1, e.target.value.length);
                          // console.log("hi:");
                          if (e.target.value.length <= max_length) {
                            setCode(e.target.value.toUpperCase());
                            setisValidCode(true);
                            setIsButtonDisabled(
                              e.target.value === "" || 
                              description === "" || 
                              (typeof e.target.value === 'number' ? 
                                e.target.value.toString().length < min_length : 
                                e.target.value.length < min_length)
                            );
                          }
                        }}
                        onBlur= {(e) => {
                          const trimmedValue = trimInputText(e.target.value);
                          if (trimmedValue.length <= max_length) {
                            setCode(trimmedValue.toUpperCase());
                            setisValidCode(true);
                            setIsButtonDisabled(
                              e.target.value === "" || 
                              description === "" || 
                              (typeof e.target.value === 'number' ? 
                                e.target.value.toString().length < min_length : 
                                e.target.value.length < min_length)
                            );
                          } else {
                            setisValidCode(false);
                          }
                        }}
                
                        placeholder={placeholderText}
                        // style={{ textTransform: "uppercase" }}
                        value={code}
                      />
                      {!isValidCode && (
                        <span className="text-red-500 text-xs mt-1 ml-1">
                          Code Already Exists
                        </span>
                      )}
                    </div>
                    <button
                      className={`border border-skin-primary text-white bg-skin-primary py-1 px-5 ml-4 ${
                        !isValidCode ? "mb-[18px]" : ""
                      } w-[130px] font-medium rounded-md scale-x-100"
                      }`}
                      onClick={isUpdateMode ? handelupdate : handleAdd}
                      disabled={isButtonDisabled}
                    >
                      {isUpdateMode ? "Update" : "Add New"}
                    </button>
                  </div>
                </div>
              </div>
              <div className="flex flex-col">
                <label className="relative block w-full text-gray-400 mt-0 pt-0 mb-3">
                  <span className="absolute px-4 py-1 pt-[0.4rem] 2xl:pt-1.5 text-black">
                    <FontAwesomeIcon icon={faSearch} className="fw-bold" />
                  </span>
                  <input
                    type="text"
                    id="filter-text-box"
                    placeholder="Search"
                    onInput={onFilterTextBoxChanged}
                    className="block w-full px-4 pl-10 text-gray-500 placeholder-gray-400 searchbar border rounded-lg appearance-none form-input focus:outline-none"
                  />
                </label>
                <div
                  className="!rounded-md px-5 border border-gray-300"
                  style={{ height: "calc(100vh - 210px)" }}
                >
                  <AgGridReact
                    rowData={rowData}
                    ref={gridRef}
                    columnDefs={columnDefs}
                    defaultColDef={defaultColDef}
                    getRowStyle={getRowStyle} // Apply custom row styles
                  />
                </div>
              </div>
            </div>
          </DrawerBody>
        </Drawer>
      </div>
    </>
    //</div>
  );
};

export default DrawerComponent;
