const { isDateInPastWeek } = require("./isDateInPastWeek");
const { getCurrentWeeksFriday } = require("./utils/getCurrentWeeksFriday");
import { toast } from "react-toastify";
import {
  GRID_BE,
  GRID_PRICE,
  GRID_VOLUME,
  WHATIF_TYPE_BREAKEVEN_ID,
  WHATIF_TYPE_PRICE_ID,
  WHATIF_TYPE_PROMO_ID,
  WHATIF_TYPE_RR_VOLUME_ID,
  WHATIF_TYPE_SUPPLIR_ISSUE_ID,
} from "./utils/constants";
import { stripTime } from "./utils/stripTime";

const generateWeeks = (
  startDate,
  startWeek,
  endWeek,
  promotions,
  previousValues,
  ctxCalenderData,
  fiscalYear
) => {
  const nextWeekWhatifs = promotions.filter(
    (promo) => promo.promo_start_week_no > endWeek
  );

  let nextWeekHavingWhatif = nextWeekWhatifs.reduce((lowest, whatif) => {
    return whatif.promo_start_week_no < lowest.promo_start_week_no
      ? whatif
      : lowest;
  }, nextWeekWhatifs[0]);
  let isNextWeekHavingWhatifInNextFY = false;

  // Handle case where no promotions meet the filter criteria
  if (!nextWeekWhatifs.length) {
    nextWeekHavingWhatif = ctxCalenderData.slice(-1)[0].fiscalweek + 1;

    isNextWeekHavingWhatifInNextFY =
      ctxCalenderData.slice(-1)[0].fiscalyear > fiscalYear;
  } else {
    nextWeekHavingWhatif = nextWeekHavingWhatif.promo_start_week_no;
  }

  const result = [];
  let currentDate = new Date(startDate);
  let startWeekDate = new Date(startDate);

  // Check if the date is valid
  if (!isNaN(startWeekDate.getTime())) {
    startWeekDate = startWeekDate.toISOString().split("T")[0]; // Format to YYYY-MM-DD
  }

  const matchingWeek = ctxCalenderData.filter(
    (item) => item.startweek == startWeekDate
  );
  const currentPromotion = promotions.filter(
    (promo) => promo.promo_start_week_no === startWeek
  );
  if (startWeek > endWeek) {
    let currentFiscalYear = parseInt(fiscalYear);
    let week = startWeek;
    while (true) {
      if (week > 52) {
        currentFiscalYear += 1;
        week = week - 52;
      }

      result.push({
        weekNumber: week,
        isNew: currentPromotion[0]?.weekData?.[week] ? false : true,
        weekStartDate: currentDate.toISOString(),
        weekEndDate:matchingWeek.length>0 && matchingWeek[0].endweek,
        fiscalYear: currentFiscalYear,
        nextWeekHavingWhatif,
        isNextWeekHavingWhatifInNextFY,
        current: currentPromotion[0]?.weekData?.[week]?.current ?? {
          volume: previousValues[week]?.volume ?? 0,
          price: previousValues[week]?.price ?? 0,
          sales: previousValues[week]?.sales ?? 0,
          be: previousValues[week]?.be ?? 0,
          gp: previousValues[week]?.gp ?? 0,
          gp_percent: previousValues[week]?.gp_percent ?? 0,
        },
        previous: {
          volume: previousValues[week]?.volume ?? 0,
          price: previousValues[week]?.price ?? 0,
          sales: previousValues[week]?.sales ?? 0,
          be: previousValues[week]?.be ?? 0,
          gp: previousValues[week]?.gp ?? 0,
          gp_percent: previousValues[week]?.gp_percent ?? 0,
        },
      });

      // Increment the current date by 7 days
      currentDate.setDate(currentDate.getDate() + 7);

      // Check termination condition
      if (week === endWeek && currentFiscalYear === fiscalYear + 1) {
        break;
      }

      // Increment week
      week += 1;
    }
  } else {
    for (let week = startWeek; week <= endWeek; week++) {
      let weekKey = `${week}-${fiscalYear}`;
      result.push({
        weekNumber: week,
        isNew: currentPromotion[0]?.weekData?.[week] ? false : true,
        weekStartDate: currentDate.toISOString(),
        weekEndDate:matchingWeek.length>0 && matchingWeek[0].endweek,
        fiscalYear,
        nextWeekHavingWhatif,
        isNextWeekHavingWhatifInNextFY,
        current: currentPromotion[0]?.weekData?.[week]?.current ?? {
          volume: previousValues[weekKey]?.volume ?? 0,
          price: previousValues[weekKey]?.price ?? 0,
          sales: previousValues[weekKey]?.sales ?? 0,
          be: previousValues[weekKey]?.be ?? 0,
          gp: previousValues[weekKey]?.gp ?? 0,
          gp_percent: previousValues[weekKey]?.gp_percent ?? 0,
        },
        previous: {
          volume: previousValues[weekKey]?.volume ?? 0,
          price: previousValues[weekKey]?.price ?? 0,
          sales: previousValues[weekKey]?.sales ?? 0,
          be: previousValues[weekKey]?.be ?? 0,
          gp: previousValues[weekKey]?.gp ?? 0,
          gp_percent: previousValues[weekKey]?.gp_percent ?? 0,
        },
      });
      currentDate.setDate(currentDate.getDate() + 7);
    }
  }

  return result;
};

const modalLogic = async (
  currentProduct,
  grid,
  productData,
  previousValues,
  ctxCalenderData
) => {
  if (!productData) return null; //return if no product to edit

  //check if productData.startWeek is in the past week OR currentDate is >= friday in this week
  const isDatePastWeek = isDateInPastWeek(
    new Date(productData.startWeek),
    productData.startWeek
  );

    if(isDatePastWeek || stripTime(new Date()) >= stripTime(getCurrentWeeksFriday(productData.startWeek))) {
    toast.info(`You cannot add a promotion to a past week or on or after Friday of the current week.`,{
      theme:'colored',
      autoClose: 5000,
    })
    return null;
  }

  const promotions = currentProduct.promotions;

  let whatIfs = [];

  if (grid === GRID_VOLUME && !!promotions) {
    const currentDate = new Date(productData.startWeek);
    const currentMonth = currentDate.getMonth() + 1;
    const startWeekYear = currentDate.getFullYear();

    const financialYear =
      currentMonth >= 10 ? startWeekYear + 1 : startWeekYear;
    const promotionInSelectedWeek = promotions.filter((promo) => {
      if (promo?.promo_end_week_no >= promo?.promo_start_week_no) {
        return (
          promo.type_id === WHATIF_TYPE_PROMO_ID &&
          productData?.week >= promo?.promo_start_week_no && //start week 51 and end week 1 current week is 52, 1
          productData?.week <= promo?.promo_end_week_no &&
          financialYear === promo?.promo_fiscalYear
        );
      } else {
        return (
          (promo.type_id === WHATIF_TYPE_PROMO_ID &&
            productData?.week >= promo?.promo_start_week_no &&
            financialYear === promo?.promo_fiscalYear) ||
          (productData?.week <= promo?.promo_end_week_no &&
            financialYear === promo?.promo_fiscalYear + 1)
        );
      }
    });

    const issueInSelectedWeek = promotions.filter((promo) => {
      if (promo?.promo_end_week_no >= promo?.promo_start_week_no) {
        return (
          promo.type_id === WHATIF_TYPE_SUPPLIR_ISSUE_ID &&
          productData?.week >= promo?.promo_start_week_no && //start week 51 and end week 1 current week is 52, 1
          productData?.week <= promo?.promo_end_week_no &&
          financialYear === promo?.promo_fiscalYear
        );
      } else {
        return (
          (promo.type_id === WHATIF_TYPE_SUPPLIR_ISSUE_ID &&
            productData?.week >= promo?.promo_start_week_no &&
            financialYear === promo?.promo_fiscalYear) ||
          (promo.type_id === WHATIF_TYPE_SUPPLIR_ISSUE_ID &&
            productData?.week <= promo?.promo_end_week_no &&
            financialYear === promo?.promo_fiscalYear + 1)
        );
      }
    });

    const rrVolumeInSelectedWeek = promotions.filter((promo) => {
      if (promo?.promo_end_week_no >= promo?.promo_start_week_no) {
        return (
          promo.type_id === WHATIF_TYPE_RR_VOLUME_ID &&
          productData?.week >= promo?.promo_start_week_no && //start week 51 and end week 1 current week is 52, 1
          productData?.week <= promo?.promo_end_week_no &&
          financialYear === promo?.promo_fiscalYear
        );
      } else {
        return (
          (promo.type_id === WHATIF_TYPE_RR_VOLUME_ID &&
            productData?.week >= promo?.promo_start_week_no &&
            financialYear === promo?.promo_fiscalYear) ||
          (promo.type_id === WHATIF_TYPE_RR_VOLUME_ID &&
            productData?.week <= promo?.promo_end_week_no &&
            financialYear === promo?.promo_fiscalYear + 1)
        );
      }
    });

    if (promotionInSelectedWeek && promotionInSelectedWeek.length > 0) {
      const data = promotionInSelectedWeek[0];
    
      const weeks = generateWeeks(
        data.promo_start_week,
        data.promo_start_week_no,
        data.promo_end_week_no,
        promotions,
        previousValues,
        ctxCalenderData,
        data.promo_fiscalYear
      );

      whatIfs.push({
        type: WHATIF_TYPE_PROMO_ID,
        currentWeek: productData.week,
        weeksData: weeks,
        description: data.description,
        is_confirmed: data.is_confirmed,
        whatif_id: data.what_if_id,
      });
    }

    if (issueInSelectedWeek && issueInSelectedWeek.length > 0) {
      const data = issueInSelectedWeek[0];

      const weeks = generateWeeks(
        data.promo_start_week,
        data.promo_start_week_no,
        data.promo_end_week_no,
        promotions,
        previousValues,
        ctxCalenderData,
        data.promo_fiscalYear
      );

      whatIfs.push({
        type: WHATIF_TYPE_SUPPLIR_ISSUE_ID,
        currentWeek: productData.week,
        weeksData: weeks,
        description: data.description,
        is_confirmed: data.is_confirmed,
        whatif_id: data.what_if_id,
      });
    }

    if (rrVolumeInSelectedWeek && rrVolumeInSelectedWeek.length > 0) {
      const data = rrVolumeInSelectedWeek[0];
      const weeks = generateWeeks(
        data.promo_start_week,
        data.promo_start_week_no,
        data.promo_end_week_no,
        promotions,
        previousValues,
        ctxCalenderData,
        data.promo_fiscalYear
      );
      whatIfs.push({
        type: WHATIF_TYPE_RR_VOLUME_ID,
        currentWeek: productData.week,
        weeksData: weeks,
        description: data.description,
        is_confirmed: data.is_confirmed,
        whatif_id: data.what_if_id,
      });
    }
  } else if (grid === GRID_BE && !!promotions) {
    const currentDate = new Date(productData.startWeek);
    const currentMonth = currentDate.getMonth() + 1;
    const startWeekYear = currentDate.getFullYear();

    const financialYear =
      currentMonth >= 10 ? startWeekYear + 1 : startWeekYear;
    const breakevenInSelectedWeek = promotions.filter((promo) => {
      if (promo?.promo_end_week_no > promo?.promo_start_week_no) {
        return (
          promo.type_id === WHATIF_TYPE_BREAKEVEN_ID &&
          productData?.week >= promo?.promo_start_week_no && //start week 51 and end week 1 current week is 52, 1
          productData?.week <= promo?.promo_end_week_no &&
          financialYear === promo?.promo_fiscalYear
        );
      } else {
        return (
          (promo.type_id === WHATIF_TYPE_BREAKEVEN_ID &&
            productData?.week >= promo?.promo_start_week_no &&
            financialYear === promo?.promo_fiscalYear) ||
          (productData?.week <= promo?.promo_end_week_no &&
            financialYear === promo?.promo_fiscalYear + 1)
        );
      }
    });

    if (breakevenInSelectedWeek && breakevenInSelectedWeek.length > 0) {
      const data = breakevenInSelectedWeek[0];
      const weeks = generateWeeks(
        data.promo_start_week,
        data.promo_start_week_no,
        data.promo_end_week_no,
        promotions,
        previousValues,
        ctxCalenderData,
        data.promo_fiscalYear
      );
      whatIfs.push({
        type: WHATIF_TYPE_BREAKEVEN_ID,
        currentWeek: productData.week,
        weeksData: weeks,
        description: data.description,
        is_confirmed: data.is_confirmed,
        whatif_id: data.what_if_id,
        beStatusId: data.breakeven_status_id,
      });
    }
  } else if (grid === GRID_PRICE && !!promotions) {
    const currentDate = new Date(productData.startWeek);
    const currentMonth = currentDate.getMonth() + 1;
    const startWeekYear = currentDate.getFullYear();

    const financialYear =
      currentMonth >= 10 ? startWeekYear + 1 : startWeekYear;
    const priceInSelectedWeek = promotions.filter((promo) => {
      promo.type_id === WHATIF_TYPE_PRICE_ID &&
        promo.promo_start_week_no <= productData.week &&
        promo.promo_end_week_no >= productData.week;
      if (promo?.promo_end_week_no >= promo?.promo_start_week_no) {
        return (
          promo.type_id === WHATIF_TYPE_PRICE_ID &&
          productData?.week >= promo?.promo_start_week_no &&
          productData?.week <= promo?.promo_end_week_no &&
          financialYear === promo?.promo_fiscalYear
        );
      } else {
        return (
          (promo.type_id === WHATIF_TYPE_PRICE_ID &&
            productData?.week >= promo?.promo_start_week_no &&
            financialYear === promo?.promo_fiscalYear) ||
          (productData?.week <= promo?.promo_end_week_no &&
            financialYear === promo?.promo_fiscalYear + 1)
        );
      }
    });

    if (priceInSelectedWeek && priceInSelectedWeek.length > 0) {
      const data = priceInSelectedWeek[0];
      const weeks = generateWeeks(
        data.promo_start_week,
        data.promo_start_week_no,
        data.promo_end_week_no,
        promotions,
        previousValues,
        ctxCalenderData,
        data.promo_fiscalYear
      );
      whatIfs.push({
        type: WHATIF_TYPE_PRICE_ID,
        currentWeek: productData.week,
        weeksData: weeks,
        description: data.description,
        is_confirmed: data.is_confirmed,
        whatif_id: data.what_if_id,
      });
    }
  }

  if (!whatIfs || whatIfs.length === 0) {
    const weeks = generateWeeks(
      productData.startWeek,
      productData.week,
      productData.week,
      promotions,
      previousValues,
      ctxCalenderData,
      productData.fiscalYear
    );

    whatIfs.push({
      type:
        grid === GRID_VOLUME
          ? WHATIF_TYPE_PROMO_ID
          : grid === GRID_BE
          ? WHATIF_TYPE_BREAKEVEN_ID
          : WHATIF_TYPE_PRICE_ID,
      currentWeek: productData.week,
      weeksData: weeks,
      isNew: true,
      whatif_id: null,
      beStatusId: 1,
    });
  }

  return whatIfs;
};

export default modalLogic;
