"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/raw-material-request/[productId]/edit",{

/***/ "./pages/raw-material-request/[productId]/edit/index.js":
/*!**************************************************************!*\
  !*** ./pages/raw-material-request/[productId]/edit/index.js ***!
  \**************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __N_SSP: function() { return /* binding */ __N_SSP; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _components_Layout__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/Layout */ \"./components/Layout.js\");\n/* harmony import */ var _components_RawMaterialRequest__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/RawMaterialRequest */ \"./components/RawMaterialRequest.js\");\n/* harmony import */ var _services_apiConfig__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/services/apiConfig */ \"./services/apiConfig.js\");\n/* harmony import */ var js_cookie__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! js-cookie */ \"./node_modules/js-cookie/dist/js.cookie.mjs\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var react_loader_spinner__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! react-loader-spinner */ \"./node_modules/react-loader-spinner/dist/module.js\");\n/* harmony import */ var react_toastify__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! react-toastify */ \"./node_modules/react-toastify/dist/react-toastify.esm.mjs\");\n/* harmony import */ var react_toastify_dist_ReactToastify_css__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! react-toastify/dist/ReactToastify.css */ \"./node_modules/react-toastify/dist/ReactToastify.css\");\n/* harmony import */ var react_toastify_dist_ReactToastify_css__WEBPACK_IMPORTED_MODULE_9___default = /*#__PURE__*/__webpack_require__.n(react_toastify_dist_ReactToastify_css__WEBPACK_IMPORTED_MODULE_9__);\n/* harmony import */ var _azure_msal_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @azure/msal-react */ \"./node_modules/@azure/msal-react/dist/index.js\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n//\n\nconst index = (param)=>{\n    let { userData } = param;\n    _s();\n    const { instance, accounts } = (0,_azure_msal_react__WEBPACK_IMPORTED_MODULE_10__.useMsal)();\n    const [dropdowns, setDropdowns] = (0,react__WEBPACK_IMPORTED_MODULE_6__.useState)(null);\n    const [rawMaterialData, setRawMaterialData] = (0,react__WEBPACK_IMPORTED_MODULE_6__.useState)(null);\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_5__.useRouter)();\n    const { productId } = router.query;\n    (0,react__WEBPACK_IMPORTED_MODULE_6__.useEffect)(()=>{\n        const company = js_cookie__WEBPACK_IMPORTED_MODULE_4__[\"default\"].get(\"company\");\n        let prophetId = 1;\n        if (company == \"dpsltd\") {\n            prophetId = 1;\n        } else if (company == \"efcltd\") {\n            prophetId = 3;\n        } else if (company == \"fpp-ltd\") {\n            prophetId = 4;\n        }\n        const fetchData = async ()=>{\n            const serverAddress = _services_apiConfig__WEBPACK_IMPORTED_MODULE_3__.apiConfig.serverAddress;\n            try {\n                const allDropDowns = [\n                    \"reasonForRequest\",\n                    \"masterProductCode\",\n                    \"markVariety\",\n                    \"productType\",\n                    \"organicCertification\",\n                    \"temperatureGrade\",\n                    \"intrastatCommodityCode\",\n                    \"classifiedAllergicTypes\",\n                    \"countryOfOrigin\",\n                    \"brand\",\n                    \"caliberSize\",\n                    \"endCustomer\",\n                    \"variety\",\n                    \"subProductCode\"\n                ];\n                const productType = 0;\n                const dropdownsRequest = await fetch(\"\".concat(serverAddress, \"products/get-products-dropdowns-list?prophetId=\").concat(prophetId, \"&productType=\").concat(productType), {\n                    method: \"POST\",\n                    headers: {\n                        Accept: \"application/json\",\n                        \"Content-Type\": \"application/json\",\n                        Authorization: \"Bearer \".concat(userData.token)\n                    },\n                    body: JSON.stringify(allDropDowns)\n                });\n                // console.log(\"dropdownsRequest\",dropdownsRequest);\n                if (dropdownsRequest.status === 401) {\n                    console.error(\"Your session has expired. Please log in again.\");\n                    react_toastify__WEBPACK_IMPORTED_MODULE_8__.toast.error(\"Your session has expired. Please log in again.\");\n                    setTimeout(()=>{\n                        const redirectUrl = \"/login?redirect=\".concat(encodeURIComponent(window.location.pathname));\n                        logoutHandler(instance, redirectUrl);\n                    }, 3000);\n                    return null;\n                }\n                const rawMaterialRequest = fetch(\"\".concat(serverAddress, \"products/get-raw-materials-by-id/\").concat(productId), {\n                    method: \"GET\",\n                    headers: {\n                        Accept: \"application/json\",\n                        \"Content-Type\": \"application/json\",\n                        Authorization: \"Bearer \".concat(userData.token)\n                    }\n                });\n                const [dropdownsResponse, rawMaterialResponse] = await Promise.all([\n                    dropdownsRequest,\n                    rawMaterialRequest\n                ]);\n                const allDropdownsList = await dropdownsResponse.json();\n                const rawMaterialData = await rawMaterialResponse.json();\n                // console.log(\"dropdowns\", allDropdownsList);\n                // console.log(\"raw material data\", rawMaterialData);\n                setDropdowns(allDropdownsList);\n                setRawMaterialData(rawMaterialData);\n            } catch (error) {\n                console.error(\"Error fetching data\", error);\n            }\n        };\n        if (productId) {\n            fetchData();\n        }\n    }, [\n        productId\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Layout__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n        userData: userData,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_toastify__WEBPACK_IMPORTED_MODULE_8__.ToastContainer, {\n                limit: 1\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\raw-material-request\\\\[productId]\\\\edit\\\\index.js\",\n                lineNumber: 116,\n                columnNumber: 7\n            }, undefined),\n            !dropdowns && !rawMaterialData ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    display: \"flex\",\n                    alignItems: \"center\",\n                    justifyContent: \"center\",\n                    height: \"100vh\"\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_loader_spinner__WEBPACK_IMPORTED_MODULE_7__.ThreeCircles, {\n                    color: \"#002D73\",\n                    height: 50,\n                    width: 50,\n                    visible: true,\n                    ariaLabel: \"oval-loading\",\n                    secondaryColor: \"#0066FF\",\n                    strokeWidth: 2,\n                    strokeWidthSecondary: 2\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\raw-material-request\\\\[productId]\\\\edit\\\\index.js\",\n                    lineNumber: 126,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\raw-material-request\\\\[productId]\\\\edit\\\\index.js\",\n                lineNumber: 118,\n                columnNumber: 9\n            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_RawMaterialRequest__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                dropdowns: dropdowns,\n                userData: userData,\n                rawMaterialData: rawMaterialData,\n                pageType: \"update\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\raw-material-request\\\\[productId]\\\\edit\\\\index.js\",\n                lineNumber: 138,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\raw-material-request\\\\[productId]\\\\edit\\\\index.js\",\n        lineNumber: 115,\n        columnNumber: 5\n    }, undefined);\n};\n_s(index, \"0z5kQTS9UygltPlkfhEkvUUtu4E=\", false, function() {\n    return [\n        _azure_msal_react__WEBPACK_IMPORTED_MODULE_10__.useMsal,\n        next_router__WEBPACK_IMPORTED_MODULE_5__.useRouter\n    ];\n});\nvar __N_SSP = true;\n/* harmony default export */ __webpack_exports__[\"default\"] = (index);\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevExports = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevExports) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports on update so we can compare the boundary\n                // signatures.\n                module.hot.dispose(function (data) {\n                    data.prevExports = currentExports;\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevExports !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevExports, currentExports)) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevExports !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./pages/raw-material-request/[productId]/edit/index.js\n"));

/***/ })

});