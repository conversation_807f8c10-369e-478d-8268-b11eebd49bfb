"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/products",{

/***/ "./utils/renderer/productStatusRenderer.js":
/*!*************************************************!*\
  !*** ./utils/renderer/productStatusRenderer.js ***!
  \*************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n\nvar _s = $RefreshSig$();\n\nconst colorMap = {\n    reason: {\n        New: \"#3255F4\",\n        Change: \"#49BB7F\",\n        Replacement: \"#ff7f00\",\n        Contingency: \"#49B47F\",\n        default: \"#9A9A9A\"\n    },\n    type: {\n        RM: \"#0066FF\",\n        FG: \"#25AE65\",\n        PK: \"#49BB7F\",\n        default: \"#9A9A9A\"\n    },\n    status: {\n        Complete: \"#fff\",\n        New: \"#fff\",\n        Cancelled: \"#fff\",\n        Contingency: \"#fff\",\n        default: \"#fff\"\n    }\n};\nconst statusColumnStyles = {\n    Completed: {\n        width: \"140px\",\n        backgroundColor: \"#25AE65\",\n        borderRadius: \"5px\",\n        padding: \"5px\",\n        textAlign: \"center\",\n        color: \"white\"\n    },\n    Replacement: {\n        width: \"190px\",\n        color: \"white\",\n        backgroundColor: \"#ff7f00\",\n        borderRadius: \"5px\",\n        padding: \"5px\",\n        textAlign: \"center\"\n    },\n    Submitted: {\n        width: \"140px\",\n        backgroundColor: \"#25AE65\",\n        borderRadius: \"5px\",\n        padding: \"5px\",\n        textAlign: \"center\",\n        color: \"white\"\n    },\n    Updated: {\n        width: \"140px\",\n        color: \"white\",\n        backgroundColor: \"#0066FF\",\n        borderRadius: \"5px\",\n        padding: \"5px\",\n        textAlign: \"center\"\n    },\n    New: {\n        width: \"140px\",\n        backgroundColor: \"#54C5ED\",\n        borderRadius: \"5px\",\n        padding: \"5px\",\n        textAlign: \"center\"\n    },\n    Cancelled: {\n        width: \"140px\",\n        backgroundColor: \"#FF6D29\",\n        borderRadius: \"5px\",\n        padding: \"5px\",\n        textAlign: \"center\"\n    },\n    Draft: {\n        width: \"140px\",\n        backgroundColor: \"#54C5ED\",\n        borderRadius: \"5px\",\n        padding: \"5px\",\n        textAlign: \"center\",\n        color: \"white\"\n    },\n    \"Pending Review\": {\n        backgroundColor: \"#0066FF\",\n        width: \"140px\",\n        borderRadius: \"5px\",\n        padding: \"5px\",\n        textAlign: \"center\",\n        color: \"white\"\n    },\n    \"ISS to Setup\": {\n        width: \"140px\",\n        color: \"white\",\n        backgroundColor: \"#6E3EAB\",\n        borderRadius: \"5px\",\n        padding: \"5px\",\n        textAlign: \"center\"\n    },\n    \"Prophet to Setup\": {\n        width: \"140px\",\n        color: \"white\",\n        backgroundColor: \"#AB6E3E\",\n        borderRadius: \"5px\",\n        padding: \"5px\",\n        textAlign: \"center\"\n    },\n    Rejected: {\n        width: \"140px\",\n        color: \"white\",\n        backgroundColor: \"#F93647\",\n        borderRadius: \"5px\",\n        padding: \"5px\",\n        textAlign: \"center\"\n    },\n    \"Prophet Setup Completed\": {\n        width: \"140px\",\n        color: \"white\",\n        backgroundColor: \"#25AE65\",\n        borderRadius: \"5px\",\n        padding: \"5px\",\n        textAlign: \"center\"\n    },\n    Contingency: {\n        width: \"140px\",\n        color: \"white\",\n        backgroundColor: \"#F93647\",\n        borderRadius: \"5px\",\n        padding: \"10px\",\n        textAlign: \"center\"\n    },\n    \"To be Setup\": {\n        width: \"140px\",\n        color: \"white\",\n        backgroundColor: \"#6E3EAB\",\n        borderRadius: \"5px\",\n        padding: \"5px\",\n        textAlign: \"center\"\n    },\n    \"Setup Complete\": {\n        width: \"140px\",\n        color: \"white\",\n        backgroundColor: \"#F93647\",\n        borderRadius: \"5px\",\n        padding: \"5px\",\n        textAlign: \"center\"\n    }\n};\nconst productStatusRenderer = (params)=>{\n    _s();\n    const isStatusField = params.colDef.field === \"status\";\n    const isReasonField = params.colDef.field === \"reason\";\n    const color = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        var _params_value_trim, _params_value;\n        const fieldMap = colorMap[params.colDef.field];\n        const normalizedValue = (_params_value = params.value) === null || _params_value === void 0 ? void 0 : (_params_value_trim = _params_value.trim) === null || _params_value_trim === void 0 ? void 0 : _params_value_trim.call(_params_value);\n        return fieldMap ? fieldMap[normalizedValue] || fieldMap.default : undefined;\n    }, [\n        params.colDef.field,\n        params.value\n    ]);\n    let valueToDisplay;\n    if (params.value === \"Prophet Setup Completed\") {\n        if (params.data.company === \"dpsltd\" || params.data.company === \"iss\") {\n            valueToDisplay = \"ISS Setup Completed\";\n        } else if (params.data.company === \"efcltd\") {\n            valueToDisplay = \"EFC Setup Completed\";\n        } else if (params.data.company === \"fpp-ltd\") {\n            valueToDisplay = \"FPP Setup Completed\";\n        } else {\n            valueToDisplay = \"Setup Completed\";\n        }\n    } else if (params.value === \"Prophet to Setup\") {\n        if (params.data.company === \"efcltd\") {\n            valueToDisplay = \"EFC to Setup\";\n        } else if (params.data.company === \"fpp-ltd\") {\n            valueToDisplay = \"FPP to Setup\";\n        } else {\n            valueToDisplay = \"ISS to Setup\";\n        }\n    } else {\n        valueToDisplay = params.value;\n    }\n    const spanStyle = {\n        width: \"90px\",\n        textAlign: \"left\",\n        display: \"inline-block\",\n        verticalAlign: \"middle\",\n        lineHeight: \"24px\",\n        height: \"32px\",\n        ...isStatusField ? statusColumnStyles[params.value] || {} : {\n            color\n        },\n        ...isReasonField && {\n            border: \"1px solid \".concat(color),\n            borderRadius: \"0.375rem\",\n            textAlign: \"center\"\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n        style: spanStyle,\n        children: valueToDisplay\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\utils\\\\renderer\\\\productStatusRenderer.js\",\n        lineNumber: 197,\n        columnNumber: 10\n    }, undefined);\n};\n_s(productStatusRenderer, \"/WjuwVs/0HBZwnTqmkaDvvE9h4w=\");\n/* harmony default export */ __webpack_exports__[\"default\"] = (productStatusRenderer);\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevExports = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevExports) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports on update so we can compare the boundary\n                // signatures.\n                module.hot.dispose(function (data) {\n                    data.prevExports = currentExports;\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevExports !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevExports, currentExports)) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevExports !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./utils/renderer/productStatusRenderer.js\n"));

/***/ })

});