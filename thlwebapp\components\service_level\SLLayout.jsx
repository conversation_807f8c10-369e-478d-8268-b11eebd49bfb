import React, { useState, useRef, useEffect, Fragment } from "react";
import SLTable from "./SLTable";
import Filter from "./SLFilter";
import { ThreeCircles } from "react-loader-spinner";
import NodataImg from "../../public/images/nodatafound.png";
import Image from "next/image";
import io from "socket.io-client";
import { apiConfig } from "@/services/apiConfig";
import { getCookieData } from "@/utils/getCookieData";
import { Dialog, Transition } from "@headlessui/react";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faInfo, faXmark } from "@fortawesome/free-solid-svg-icons";
import { toast, ToastContainer } from "react-toastify";
//
import Cookies from "js-cookie";
import { useMsal } from "@azure/msal-react";

const SLLayout = ({
  customerList,
  customerSLDataOg,
  initialDataExists,
  userData,
  currentCustomer,
  productList,
  initalDate,
  endDate,
  setCustomerList,
  setAllReasonsSubreasons,
  allReasonsSubreasons,
}) => {
  const { instance } = useMsal();
  const [selectedCustomer, setSelectedCustomer] = useState();
  const [selectedServiceCustomer, setSelectedServiceCustomer] = useState({value:"All Service Customers",label:"All Service Customers"});
  const [customerSLData, setCustomerSLData] = useState([]);
  const [customerSLDataMaster, setCustomerSLDataMaster] = useState([]);
  const [selectedProducts, setSelectedProducts] = useState([]);
  const [slFilters, setSlFilters] = useState(null);
  const [seeAll, setSeeAll] = useState(true);
  const [recordsCount, setRecordsCount] = useState(0);
  const [searchBoxContent, setSearchBoxContent] = useState("");
  const [masterProducts, setMasterProducts] = useState([]);
  const [selectedMasterProductCode, setSelectedMasterProductCode] = useState({
    value: "all",
    label: "All Master Product Codes",
  });
  const [selectedRows, setSelectedRows] = useState([]);
  const [bulkDeleteOrdIds, setBulkDeleteOrdIds] = useState([]);
  const [triggerBulkUpdatePopup, setTriggerBulkUpdatePopup] = useState(false);
  const [initialLoad, setInitialLoad] = useState(true);
  const [noDataExists, setNoDataExists] = useState(false);
  const [showLoadingMessage, setShowLoadingMessage] = useState(true);
  const [selectedReasons, setSelectedReasons] = useState([]);
  const [selectedSubReasons, setSelectedSubReasons] = useState([]);
  const loadingMessageRef = useRef(null);
  const initialTopOffset = useRef(null);
  useEffect(() => {
    
    if (customerSLDataOg.length == 0 && initialLoad && !initialDataExists) {
    
      setNoDataExists(true);
      setShowLoadingMessage(false);
    } else {
      setNoDataExists(false);
      setShowLoadingMessage(true);
    }
    setInitialLoading(false);
    setCustomerSLData(customerSLDataOg);
    setCustomerSLDataMaster(customerSLDataOg);
    setInitialLoad(false);
  }, [customerSLDataOg]);
  
  useEffect(() => {
    let slFilters = Cookies.get("slFilters");
    if (slFilters) {
      setSlFilters(JSON.parse(slFilters));
    }
  }, []);

  useEffect(() => {
    if (triggerBulkUpdatePopup) {
      handleBulkUpdate();
      setTriggerBulkUpdatePopup(false);
    }
  }, [triggerBulkUpdatePopup]);

  useEffect(() => {
    setSelectedCustomer(currentCustomer);
  }, [currentCustomer]);

  const [checkedStates, setCheckedStates] = useState(
    slFilters
      ? slFilters.columns
      : {
          columns: {
            depotdate: true,
            serviceCustomers: true,
            weekNo: false,
            altfill: false,
            customer: true,
            salesorder: true,
            salesOrderId: false,
            product: true,
            casesize: false,
          },
        }
  );

  const [isTableRendered, setIsTableRendered] = useState(false);
  const [initialLoading, setInitialLoading] = useState(true);
  const [loading, setLoading] = useState(true);
  const [toggle, setToggle] = useState(true);
  const [isReasonsDeletePopupOpen, setIsReasonsDeletePopupOpen] =
    useState(false);

  const [gridcolumnRefs] = useState({
    checkboxRef: useRef(null),
    depotdate: useRef(null),
    serviceCustomers: useRef(null),
    weekNo: useRef(null),
    altfill: useRef(null),
    customer: useRef(null),
    salesorder: useRef(null),
    salesOrderId: useRef(null),
    product: useRef(null),
    casesize: useRef(null),
  });

  const [gridcolumnWidths, setGridColumnWidths] = useState({
    checkboxWidth: 0,
    depotdate: 0,
    serviceCustomers: 0,
    weekNo: 0,
    altfill: 0,
    customer: 0,
    salesorder: 0,
    salesOrderId: 0,
    product: 0,
    casesize: 0,
  });
  const closeModal = (e) => {
    if (e) {
      e.preventDefault();
    }
    setIsReasonsDeletePopupOpen(false);
  };
  useEffect(() => {
    const checkboxWidth = gridcolumnRefs.checkboxRef.current
      ? gridcolumnRefs.checkboxRef.current.offsetWidth
      : 0;
    const depotdatewidth = gridcolumnRefs.depotdate.current
      ? gridcolumnRefs.depotdate.current.offsetWidth + checkboxWidth
      : 0;
    const serviceCustomerswidth = gridcolumnRefs.serviceCustomers.current
      ? gridcolumnRefs.serviceCustomers.current.offsetWidth + depotdatewidth
      : 0;
    const efcweekwidth = gridcolumnRefs.weekNo.current
      ? gridcolumnRefs.weekNo.current.offsetWidth + serviceCustomerswidth
      : serviceCustomerswidth;
    const altfillwidth = gridcolumnRefs.altfill.current
      ? gridcolumnRefs.altfill.current.offsetWidth + efcweekwidth
      : efcweekwidth;
    const customerwidth = gridcolumnRefs.customer.current
      ? gridcolumnRefs.customer.current.offsetWidth + altfillwidth
      : altfillwidth;
    const salesorderwidth = gridcolumnRefs.salesorder.current
      ? gridcolumnRefs.salesorder.current.offsetWidth + customerwidth
      : customerwidth;
    const salesorderIdwidth = gridcolumnRefs.salesOrderId.current
      ? gridcolumnRefs.salesOrderId.current.offsetWidth + salesorderwidth
      : salesorderwidth;
    const productwidth = gridcolumnRefs.product.current
      ? gridcolumnRefs.product.current.offsetWidth + salesorderIdwidth
      : salesorderIdwidth;
    const casesizewidth = gridcolumnRefs.casesize.current
      ? gridcolumnRefs.casesize.current.offsetWidth + productwidth
      : productwidth;

    setGridColumnWidths({
      checkboxWidth: checkboxWidth,
      depotdate: depotdatewidth,
      serviceCustomers: serviceCustomerswidth,
      weekNo: efcweekwidth,
      altfill: altfillwidth,
      customer: customerwidth,
      salesorder: salesorderwidth,
      salesOrderId: salesorderwidth,
      product: productwidth,
      casesize: casesizewidth,
    });
  }, [checkedStates, isTableRendered, gridcolumnRefs]);

  const [rowRefs] = useState({
    filterRef: useRef(null),
  });

  const [rowHeights, setRowHeights] = useState({
    filterHeight: 0,
  });

  useEffect(() => {
    const filterHeight = rowRefs.filterRef.current
      ? rowRefs.filterRef.current.offsetHeight
      : 0;

    setRowHeights({
      filterHeight,
    });
  }, []);
  useEffect(() => {
    // if (Object.keys(customerSLData)) {
    //   const masterProducts = [
    //     { value: "all", label: "All Master Product Codes" },
    //     ...Array.from(
    //       new Set(
    //         Object.keys(customerSLData).map(
    //           (key) => customerSLData[key].MASTER_PRODUCT_CODE
    //         )
    //       )
    //     ).map((code) => ({ value: code, label: code })),
    //   ];
    //   console.log("master products",masterProducts)
    //   setMasterProducts(masterProducts);
    // }
    if (Object.keys(customerSLData)) {
      const masterProducts = [
        { value: "all", label: "All Master Product Codes" },
        ...Array.from(
          new Set(
            Object.keys(customerSLData).map(
              (key) => customerSLData[key].MASTER_PRODUCT_CODE
            )
          )
        )
          .map((code) => ({ value: code, label: code }))
          .sort((a, b) => a.label.localeCompare(b.label)), // Sorting alphabetically
      ];
    
      setMasterProducts(masterProducts);
    }
    
    if (Object.keys(customerSLData)?.length == 0) {
      setRecordsCount(0);
    }
    // if (customerSLData.length > 0) {
    //   const timer = setTimeout(() => {
    //     setShowLoadingMessage(false);
    //   }, 3000);

    //   // Cleanup timer on unmount or re-run
    //   return () => clearTimeout(timer);
    // }

    if (!loadingMessageRef.current) return;

    // Get initial position
    initialTopOffset.current =
      loadingMessageRef.current.getBoundingClientRect().top;

    // Observe changes in position
    const observer = new ResizeObserver(() => {
      const currentTop = loadingMessageRef.current?.getBoundingClientRect().top;
      if (
        currentTop &&
        initialTopOffset.current &&
        currentTop !== initialTopOffset.current
      ) {
        setShowLoadingMessage(false);
      }
    });

    observer.observe(loadingMessageRef.current);

    const timer = setTimeout(() => {
      setShowLoadingMessage(false);
    }, 100);
  
    return () => {
      observer.disconnect();
      clearTimeout(timer);
    };
  }, [customerSLData]);

  useEffect(() => {
    const socket = io(`${apiConfig.socketAddress}`);

    socket.on("connect", () => {
      console.log("SL socket connected to server");
    });

    socket.on("SLlockAdded", (payload) => {
      setCustomerSLData((prev) => {
        const foundOrder = prev.filter((o) => o.ORD_ID == payload.orderId);

        if (foundOrder && foundOrder.length > 0) {
          if (
            foundOrder[0].ORD_ID == payload.orderId &&
            foundOrder[0].CUSTOMER === payload.custCode
          ) {
            foundOrder[0].LOCKED_BY = payload.email;
          }
        }
        return prev;
      });

      setToggle((prev) => !prev);
    });

    socket.on("SLlockRemoved", (payload) => {
      setCustomerSLData((prev) => {
        const foundOrder = prev.filter((o) => o.ORD_ID == payload.orderId);

        if (foundOrder && foundOrder.length > 0) {
          if (
            foundOrder[0].ORD_ID == payload.orderId &&
            foundOrder[0].CUSTOMER == payload.custCode
          ) {
            foundOrder[0].LOCKED_BY = "";
          }
        }
        return prev;
      });
      setToggle((prev) => !prev);
    });

    socket.on("slAdded", (msg) => {
      setCustomerSLData((prev) => {
        msg.forEach((m) => {
          if (m.orderId) {
            const foundOrder = prev.filter((o) => o.ORD_ID == m.orderId);
            if (foundOrder && foundOrder.length > 0) {
              foundOrder[0].CASES_ADDED_REASONS =
                foundOrder[0].CASES_ADDED_REASONS + Number(m.quantity);

              const reason = {
                REASON_ID: m.id,
                MAIN_REASON_ID: parseInt(m.reasons),
                MAIN_REASON: m.reasonsLabel,
                SUB_REASON_ID: m.subReason,
                SUB_REASON: m.subReasonLabel,
                COMMENT: m.comment,
                REASON_QTY: Number(m.quantity),
                REASON_ADDED_BY: m.addedBy,
                REASON_ADDED_TIMESTAMP: Date(),
              };
              foundOrder[0].reasons.push(reason);


              setAllReasonsSubreasons((prevState) => {
                const updatedState = { ...prevState };
                if (!updatedState.reasons[reason.MAIN_REASON]) { 
                  updatedState.reasons = {
                    ...updatedState.reasons,
                    [reason.MAIN_REASON]: reason.MAIN_REASON_ID,
                  };
                }
          
                if (!updatedState.subReasons[reason.SUB_REASON]) {
                  updatedState.subReasons = {
                    ...updatedState.subReasons,
                    [reason.SUB_REASON]: reason.SUB_REASON_ID,
                  };
                }
          
                return updatedState;
              });
              
            }
          }
        });

        return prev;
      });
    });

    socket.on("slUpdated", (msg) => {
      setCustomerSLData((prev) => {
        msg.forEach((m) => {
          let newAddedQty = 0;
          if (m.orderId) {
            const foundOrder = prev.filter((o) => o.ORD_ID == m.orderId);
            const reasons = foundOrder[0]?.reasons;

            if (reasons && Array.isArray(reasons)) {
              reasons.forEach((reason) => {
                newAddedQty +=
                  reason.REASON_ID === m.id ? +m.quantity : reason.REASON_QTY;
                if (reason.REASON_ID === m.id) {
                  reason.MAIN_REASON_ID = m.reasons;
                  reason.MAIN_REASON = m.reasonsLabel;
                  reason.SUB_REASON_ID = m.subReason;
                  reason.SUB_REASON = m.subReasonLabel;
                  reason.COMMENT = m.comment;
                  reason.REASON_QTY = Number(m.quantity);
                  reason.REASON_UPDATED_BY = m.updatedBy;
                  reason.REASON_UPDATED_TIMESTAMP = Date();
                }
              });
            }

            if (foundOrder && foundOrder.length > 0) {
              foundOrder[0].CASES_ADDED_REASONS = newAddedQty;
            }
          }
        });

        return prev;
      });
    });

    socket.on("slDeleted", (msg) => {
      setCustomerSLData((prev) => {
        if (msg.orderId) {
          msg.orderId.forEach((oid, i) => {
            const foundOrder = prev.filter((o) => o.ORD_ID == oid);
            if (foundOrder && foundOrder.length > 0) {
              let newAddedQty = 0;
              const reasons = foundOrder[0].reasons;
              if (reasons && Array.isArray(reasons)) {
                const correspondingIds = msg.id.filter(
                  (id, index) => msg.orderId[index] === oid
                );
                foundOrder[0].reasons = reasons.filter(
                  (reason) => !correspondingIds.includes(reason.REASON_ID)
                );
                // console.log("test", test);
                // foundOrder[0].reasons = reasons.filter(
                //   (reason) => reason.REASON_ID !== msg.id[i]
                // );
              }
              foundOrder[0].reasons.map((reason) => {
                newAddedQty += reason.REASON_QTY;
              });
              foundOrder[0].CASES_ADDED_REASONS = newAddedQty;
            }
          });
        }
        return prev;
      });
    });

    const handleBeforeUnload = (event) => {
      const serverAddress = apiConfig.serverAddress;
      const user = getCookieData("user");
      fetch(`${serverAddress}serviceLevel/remove-locks`, {
        method: "POST",
        headers: {
          Authorization: `Bearer ${user.token}`,
          Accept: "application/json",
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          email: userData.email,
          isPayloadRequired: false,
        }),
      });
    };

    window.addEventListener("beforeunload", handleBeforeUnload);

    return () => {
      socket.disconnect();
      window.removeEventListener("beforeunload", handleBeforeUnload);
    };
  }, []);

  const handleDeleteBulkReasons = async () => {
    try {
      const serverAddress = apiConfig.serverAddress;
      const user = getCookieData("user");
      const response = await fetch(
        `${serverAddress}serviceLevel/delete-bulk-reasons`,
        {
          method: "PUT",
          headers: {
            Authorization: `Bearer ${user.token}`,
            Accept: "application/json",
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            deletedBy: userData.email,
            deletedByName: userData.name,
            orderIds: bulkDeleteOrdIds,
          }),
        }
      );
      if (response.status == 401) {
        toast.error("Your session has expired. Please log in again.");
        setTimeout(() => {
          localStorage.removeItem("superUser");
          Cookies.remove("company");
          Cookies.remove("ADCompanyName");
          localStorage.removeItem("id");
          localStorage.removeItem("name");
          localStorage.removeItem("role");
          localStorage.removeItem("email");
          Cookies.remove("user");
          Cookies.remove("theme");
          Cookies.remove("token");
          const redirectUrl = `/login?redirect=${encodeURIComponent(
            window.location.pathname
          )}`;
          logoutHandler(instance, redirectUrl);
        }, 3000);
        return null;
      } else if (response.status == 400) {
        toast.error(
          "There was an error with your request. Please check your data and try again."
        );
        return null;
      } else if (response.status === 201) {
        toast.success("Reasons deleted successfully", {
          position: "top-right",
        });
        setIsReasonsDeletePopupOpen(false);
        setBulkDeleteOrdIds([]);
        setTriggerBulkUpdatePopup(true);
        return;
      } else {
        throw new Error(response.statusText);
      }
    } catch (error) {
      console.log(error);
      toast.error(`Error deleting reasons:${error.message}`, {
        position: "top-right",
      });
    }
  };
  const [bulkUpdateData, setBulkUpdateData] = useState({
    totalCasesDelivered: 0,
    totalCasesDifferent: 0,
    totalCasesOrdered: 0,
    totalCasesAddedReasons: 0,
  });
  const [isOpen, setIsOpen] = useState(false);

  const [isBulkUpdate, setIsBulkUpdate] = useState(false);
  const handleBulkUpdate = () => {
    if (bulkDeleteOrdIds.length > 0) {
      setIsReasonsDeletePopupOpen(true);
      return;
    }
    setIsBulkUpdate(true);
    let totalCasesDelivered = 0;
    let totalCasesDifferent = 0;
    let totalCasesOrdered = 0;
    let totalCasesAddedReasons = 0;
    selectedRows.forEach((row) => {
      totalCasesDelivered += row.CASES_DELIVERED;
      totalCasesDifferent += row.CASES_DIFFERENCE;
      totalCasesOrdered += row.CASES_ORIGINAL;
      totalCasesAddedReasons += row.CASES_ADDED_REASONS;
    });
    setBulkUpdateData({
      totalCasesDelivered,
      totalCasesDifferent,
      totalCasesOrdered,
      totalCasesAddedReasons,
    });
    setIsOpen(true);
  };

  return (
    <div>
      <ToastContainer limit={1} />
      <div ref={rowRefs.filterRef} className="z-20 relative">
        <Filter
          initialLoading={initialLoading}
          setInitialLoading={setInitialLoading}
          productList={productList}
          customerList={customerList}
          customerSLData={customerSLData}
          setCustomerSLData={setCustomerSLData}
          checkedStates={checkedStates}
          setCheckedStates={setCheckedStates}
          selectedCustomer={selectedCustomer}
          setSelectedCustomer={setSelectedCustomer}
          initalDate={initalDate}
          endDate={endDate}
          setCustomerList={setCustomerList}
          selectedProducts={selectedProducts}
          setSelectedProducts={setSelectedProducts}
          customerSLDataMaster={customerSLDataMaster}
          setCustomerSLDataMaster={setCustomerSLDataMaster}
          seeAll={seeAll}
          setSeeAll={setSeeAll}
          recordsCount={recordsCount}
          searchBoxContent={searchBoxContent}
          setSearchBoxContent={setSearchBoxContent}
          slFilters={slFilters}
          masterProducts={masterProducts}
          selectedMasterProductCode={selectedMasterProductCode}
          setSelectedMasterProductCode={setSelectedMasterProductCode}
          setRecordsCount={setRecordsCount}
          selectedRows={selectedRows}
          handleBulkUpdate={handleBulkUpdate}
          setNoDataExists={setNoDataExists}
          setShowLoadingMessage={setShowLoadingMessage}
          allReasonsSubreasons={allReasonsSubreasons}
          setAllReasonsSubreasons={setAllReasonsSubreasons}
          selectedReasons={selectedReasons}
          setSelectedReasons={setSelectedReasons}
          selectedSubReasons={selectedSubReasons}
          setSelectedSubReasons={setSelectedSubReasons}
          selectedServiceCustomer={selectedServiceCustomer}
          setSelectedServiceCustomer={setSelectedServiceCustomer}
        />
      </div>
      <div
        className="relative overflow-auto flex flex-row]"
        style={{
          maxHeight: `calc(100vh - ${rowHeights.filterHeight}px - 63px)`,
        }}
      >
        <>
          {initialLoading ? (
            <div
              className="w-full"
              style={{
                display: "flex",
                alignItems: "center",
                justifyContent: "center",
                height: "100vh",
              }}
            >
              <ThreeCircles
                color="#002D73"
                height={50}
                width={50}
                visible={true}
                ariaLabel="oval-loading"
                secondaryColor="#0066FF"
                strokeWidth={2}
                strokeWidthSecondary={2}
              />
            </div>
          ) : (
            <div className="overflow-x-auto w-full mx-4">
              {Object.keys(customerSLData)?.length > 0 ? (
                <SLTable
                  customerList={customerList}
                  customerSLData={customerSLData}
                  userData={userData}
                  checkedStates={checkedStates}
                  gridcolumnWidths={gridcolumnWidths}
                  gridcolumnRefs={gridcolumnRefs}
                  setIsTableRendered={setIsTableRendered}
                  initalDate={initalDate}
                  endDate={endDate}
                  selectedProducts={selectedProducts}
                  seeAll={seeAll}
                  recordCount={recordsCount}
                  setRecordsCount={setRecordsCount}
                  searchBoxContent={searchBoxContent}
                  slFilters={slFilters}
                  selectedMasterProductCode={selectedMasterProductCode.value}
                  selectedCustomer={selectedCustomer.value}
                  toggle={toggle}
                  selectedRows={selectedRows}
                  setSelectedRows={setSelectedRows}
                  isBulkUpdate={isBulkUpdate}
                  setIsBulkUpdate={setIsBulkUpdate}
                  bulkUpdateData={bulkUpdateData}
                  isOpen={isOpen}
                  setIsOpen={setIsOpen}
                  setBulkUpdateData={setBulkUpdateData}
                  masterProducts={masterProducts}
                  bulkDeleteOrdIds={bulkDeleteOrdIds}
                  setBulkDeleteOrdIds={setBulkDeleteOrdIds}
                  setNoDataExists={setNoDataExists}
                  setShowLoadingMessage={setShowLoadingMessage}
                  allReasonsSubreasons={allReasonsSubreasons}
                  setAllReasonsSubreasons={setAllReasonsSubreasons}
                  selectedReasons={selectedReasons}
                  setSelectedReasons={setSelectedReasons}
                  selectedSubReasons={selectedSubReasons}
                  setSelectedSubReasons={setSelectedSubReasons}
                  showLoadingMessage={showLoadingMessage}
                />
              ) : null}
              {customerSLData.length === 0 && noDataExists ? (
                <div className="w-[40%] mx-auto nodata rounded-lg my-32 flex flex-row justify-between p-8 gap-8">
                  <div>
                    <Image
                      src={NodataImg}
                      className=""
                      width={400}
                      alt="No data found"
                    />
                  </div>
                  <div className="flex flex-col items-center text-2xl font-bold justify-center">
                    We couldn't find any matching results for your search.
                    Please try refining your search.
                  </div>
                </div>
              ) : showLoadingMessage ? (
                <div
                  ref={loadingMessageRef}
                  className="w-[40%] mx-auto nodata rounded-lg my-32 flex flex-row justify-between p-8 gap-8"
                >
                  <div className="w-full flex flex-col items-center text-2xl font-bold justify-center">
                    Almost there—loading your data now.
                  </div>
                </div>
              ) : null}
            </div>
          )}
        </>
      </div>
      <Transition appear show={isReasonsDeletePopupOpen} as={Fragment}>
        <Dialog as="div" className="relative z-10" onClose={closeModal}>
          <Transition.Child
            as={Fragment}
            enter="ease-out duration-300"
            enterFrom="opacity-0"
            enterTo="opacity-100"
            leave="ease-in duration-200"
            leaveFrom="opacity-100"
            leaveTo="opacity-0"
          >
            <div className="fixed backdrop-blur-none" />
          </Transition.Child>

          <div className="fixed inset-0 overflow-y-auto">
            <div className="flex items-center justify-center min-h-full p-4 text-center">
              <Transition.Child
                as={Fragment}
                enter="ease-out duration-300"
                enterFrom="opacity-0 scale-95"
                enterTo="opacity-100 scale-100"
                leave="ease-in duration-200"
                leaveFrom="opacity-100 scale-100"
                leaveTo="opacity-0 scale-95"
              >
                <Dialog.Panel className=" w-[30%] transform overflow-hidden rounded-xl bg-white  text-left align-middle shadow-xl transition-all">
                  {/* <!-- Modal content --> */}
                  <div className="relative bg-white rounded-lg shadow">
                    {/* <!-- Modal header --> */}
                    <div className="flex items-start justify-between p-8 rounded-t">
                      <h3 className="flex flex-row text-xl font-semibold text-gray-900  items-center">
                        <span className="flex justify-center items-center border border-skin-primary text-skin-primary w-[25px] h-[25px] text-center leading-5 rounded-full text-base me-4">
                          <FontAwesomeIcon icon={faInfo} />{" "}
                        </span>{" "}
                        Warning
                      </h3>
                      <button
                        onClick={closeModal}
                        type="button"
                        className="text-black bg-transparent hover:bg-gray-200 hover:text-gray-900 rounded-lg text-xl w-8 h-8 ml-auto inline-flex justify-center items-center "
                        data-modal-hide="default-modal"
                      >
                        <FontAwesomeIcon
                          icon={faXmark}
                          className="text-skin-primary"
                        />{" "}
                      </button>
                    </div>
                    {/* <!-- Modal body --> */}
                    <div className="p-8 py-0 space-y-6">
                      <p className="text-base xl:text-md 2xl:text-lg leading-relaxed mt-2">
                        The selection contains 1 or more Reasons.<br></br>
                        <br></br> Do you want to remove the Reasons for the
                        Selected Orders?
                      </p>
                      <p
                        style={{
                          color: "red",
                          fontWeight: "bold",
                          backgroundColor: "#ffe6e6",
                          padding: "5px",
                        }}
                      >
                        ⚠️<strong>NB.</strong> Action cannot be undone once
                        approved.
                      </p>
                    </div>
                    {/* <!-- Modal footer --> */}
                    <div className="flex items-end p-6 space-x-2 justify-end">
                      <button
                        onClick={closeModal}
                        data-modal-hide="default-modal"
                        type="button"
                        className="border border-skin-primary text-skin-primary focus:ring-4 focus:outline-none focus:ring-blue-300 font-medium rounded-md text-md px-6 py-2 text-center "
                      >
                        Cancel
                      </button>
                      <button
                        onClick={(e) => handleDeleteBulkReasons()}
                        data-modal-hide="default-modal"
                        type="button"
                        className="text-white bg-skin-primary hover:bg-skin-primary focus:ring-4 focus:outline-none focus:ring-blue-300 font-medium rounded-md text-md px-6 py-2 text-center "
                      >
                        Yes
                      </button>
                    </div>
                  </div>
                </Dialog.Panel>
              </Transition.Child>
            </div>
          </div>
        </Dialog>
      </Transition>
    </div>
  );
};

export default SLLayout;
