"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/_app",{

/***/ "./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[8].oneOf[13].use[1]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[8].oneOf[13].use[2]!./styles/globals.css":
/*!**********************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[8].oneOf[13].use[1]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[8].oneOf[13].use[2]!./styles/globals.css ***!
  \**********************************************************************************************************************************************************************************************************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var _node_modules_next_dist_build_webpack_loaders_css_loader_src_runtime_api_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../node_modules/next/dist/build/webpack/loaders/css-loader/src/runtime/api.js */ \"./node_modules/next/dist/build/webpack/loaders/css-loader/src/runtime/api.js\");\n/* harmony import */ var _node_modules_next_dist_build_webpack_loaders_css_loader_src_runtime_api_js__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_node_modules_next_dist_build_webpack_loaders_css_loader_src_runtime_api_js__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _node_modules_next_dist_build_webpack_loaders_css_loader_src_runtime_getUrl_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../node_modules/next/dist/build/webpack/loaders/css-loader/src/runtime/getUrl.js */ \"./node_modules/next/dist/build/webpack/loaders/css-loader/src/runtime/getUrl.js\");\n/* harmony import */ var _node_modules_next_dist_build_webpack_loaders_css_loader_src_runtime_getUrl_js__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_node_modules_next_dist_build_webpack_loaders_css_loader_src_runtime_getUrl_js__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _assets_fonts_Poppins_Black_ttf__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../assets/fonts/Poppins-Black.ttf */ \"./assets/fonts/Poppins-Black.ttf\");\n/* harmony import */ var _assets_fonts_Poppins_BlackItalic_ttf__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../assets/fonts/Poppins-BlackItalic.ttf */ \"./assets/fonts/Poppins-BlackItalic.ttf\");\n/* harmony import */ var _assets_fonts_Poppins_Bold_ttf__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../assets/fonts/Poppins-Bold.ttf */ \"./assets/fonts/Poppins-Bold.ttf\");\n/* harmony import */ var _assets_fonts_Poppins_BoldItalic_ttf__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../assets/fonts/Poppins-BoldItalic.ttf */ \"./assets/fonts/Poppins-BoldItalic.ttf\");\n/* harmony import */ var _assets_fonts_Poppins_ExtraBold_ttf__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../assets/fonts/Poppins-ExtraBold.ttf */ \"./assets/fonts/Poppins-ExtraBold.ttf\");\n/* harmony import */ var _assets_fonts_Poppins_ExtraBoldItalic_ttf__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../assets/fonts/Poppins-ExtraBoldItalic.ttf */ \"./assets/fonts/Poppins-ExtraBoldItalic.ttf\");\n/* harmony import */ var _assets_fonts_Poppins_ExtraLight_ttf__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ../assets/fonts/Poppins-ExtraLight.ttf */ \"./assets/fonts/Poppins-ExtraLight.ttf\");\n/* harmony import */ var _assets_fonts_Poppins_ExtraLightItalic_ttf__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ../assets/fonts/Poppins-ExtraLightItalic.ttf */ \"./assets/fonts/Poppins-ExtraLightItalic.ttf\");\n/* harmony import */ var _assets_fonts_Poppins_Italic_ttf__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ../assets/fonts/Poppins-Italic.ttf */ \"./assets/fonts/Poppins-Italic.ttf\");\n/* harmony import */ var _assets_fonts_Poppins_Light_ttf__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ../assets/fonts/Poppins-Light.ttf */ \"./assets/fonts/Poppins-Light.ttf\");\n/* harmony import */ var _assets_fonts_Poppins_LightItalic_ttf__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ../assets/fonts/Poppins-LightItalic.ttf */ \"./assets/fonts/Poppins-LightItalic.ttf\");\n/* harmony import */ var _assets_fonts_Poppins_Medium_ttf__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! ../assets/fonts/Poppins-Medium.ttf */ \"./assets/fonts/Poppins-Medium.ttf\");\n/* harmony import */ var _assets_fonts_Poppins_MediumItalic_ttf__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! ../assets/fonts/Poppins-MediumItalic.ttf */ \"./assets/fonts/Poppins-MediumItalic.ttf\");\n/* harmony import */ var _assets_fonts_Poppins_Regular_ttf__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! ../assets/fonts/Poppins-Regular.ttf */ \"./assets/fonts/Poppins-Regular.ttf\");\n/* harmony import */ var _assets_fonts_Poppins_SemiBold_ttf__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! ../assets/fonts/Poppins-SemiBold.ttf */ \"./assets/fonts/Poppins-SemiBold.ttf\");\n/* harmony import */ var _assets_fonts_Poppins_SemiBoldItalic_ttf__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! ../assets/fonts/Poppins-SemiBoldItalic.ttf */ \"./assets/fonts/Poppins-SemiBoldItalic.ttf\");\n// Imports\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nvar ___CSS_LOADER_EXPORT___ = _node_modules_next_dist_build_webpack_loaders_css_loader_src_runtime_api_js__WEBPACK_IMPORTED_MODULE_0___default()(true);\nvar ___CSS_LOADER_URL_REPLACEMENT_0___ = _node_modules_next_dist_build_webpack_loaders_css_loader_src_runtime_getUrl_js__WEBPACK_IMPORTED_MODULE_1___default()(_assets_fonts_Poppins_Black_ttf__WEBPACK_IMPORTED_MODULE_2__);\nvar ___CSS_LOADER_URL_REPLACEMENT_1___ = _node_modules_next_dist_build_webpack_loaders_css_loader_src_runtime_getUrl_js__WEBPACK_IMPORTED_MODULE_1___default()(_assets_fonts_Poppins_BlackItalic_ttf__WEBPACK_IMPORTED_MODULE_3__);\nvar ___CSS_LOADER_URL_REPLACEMENT_2___ = _node_modules_next_dist_build_webpack_loaders_css_loader_src_runtime_getUrl_js__WEBPACK_IMPORTED_MODULE_1___default()(_assets_fonts_Poppins_Bold_ttf__WEBPACK_IMPORTED_MODULE_4__);\nvar ___CSS_LOADER_URL_REPLACEMENT_3___ = _node_modules_next_dist_build_webpack_loaders_css_loader_src_runtime_getUrl_js__WEBPACK_IMPORTED_MODULE_1___default()(_assets_fonts_Poppins_BoldItalic_ttf__WEBPACK_IMPORTED_MODULE_5__);\nvar ___CSS_LOADER_URL_REPLACEMENT_4___ = _node_modules_next_dist_build_webpack_loaders_css_loader_src_runtime_getUrl_js__WEBPACK_IMPORTED_MODULE_1___default()(_assets_fonts_Poppins_ExtraBold_ttf__WEBPACK_IMPORTED_MODULE_6__);\nvar ___CSS_LOADER_URL_REPLACEMENT_5___ = _node_modules_next_dist_build_webpack_loaders_css_loader_src_runtime_getUrl_js__WEBPACK_IMPORTED_MODULE_1___default()(_assets_fonts_Poppins_ExtraBoldItalic_ttf__WEBPACK_IMPORTED_MODULE_7__);\nvar ___CSS_LOADER_URL_REPLACEMENT_6___ = _node_modules_next_dist_build_webpack_loaders_css_loader_src_runtime_getUrl_js__WEBPACK_IMPORTED_MODULE_1___default()(_assets_fonts_Poppins_ExtraLight_ttf__WEBPACK_IMPORTED_MODULE_8__);\nvar ___CSS_LOADER_URL_REPLACEMENT_7___ = _node_modules_next_dist_build_webpack_loaders_css_loader_src_runtime_getUrl_js__WEBPACK_IMPORTED_MODULE_1___default()(_assets_fonts_Poppins_ExtraLightItalic_ttf__WEBPACK_IMPORTED_MODULE_9__);\nvar ___CSS_LOADER_URL_REPLACEMENT_8___ = _node_modules_next_dist_build_webpack_loaders_css_loader_src_runtime_getUrl_js__WEBPACK_IMPORTED_MODULE_1___default()(_assets_fonts_Poppins_Italic_ttf__WEBPACK_IMPORTED_MODULE_10__);\nvar ___CSS_LOADER_URL_REPLACEMENT_9___ = _node_modules_next_dist_build_webpack_loaders_css_loader_src_runtime_getUrl_js__WEBPACK_IMPORTED_MODULE_1___default()(_assets_fonts_Poppins_Light_ttf__WEBPACK_IMPORTED_MODULE_11__);\nvar ___CSS_LOADER_URL_REPLACEMENT_10___ = _node_modules_next_dist_build_webpack_loaders_css_loader_src_runtime_getUrl_js__WEBPACK_IMPORTED_MODULE_1___default()(_assets_fonts_Poppins_LightItalic_ttf__WEBPACK_IMPORTED_MODULE_12__);\nvar ___CSS_LOADER_URL_REPLACEMENT_11___ = _node_modules_next_dist_build_webpack_loaders_css_loader_src_runtime_getUrl_js__WEBPACK_IMPORTED_MODULE_1___default()(_assets_fonts_Poppins_Medium_ttf__WEBPACK_IMPORTED_MODULE_13__);\nvar ___CSS_LOADER_URL_REPLACEMENT_12___ = _node_modules_next_dist_build_webpack_loaders_css_loader_src_runtime_getUrl_js__WEBPACK_IMPORTED_MODULE_1___default()(_assets_fonts_Poppins_MediumItalic_ttf__WEBPACK_IMPORTED_MODULE_14__);\nvar ___CSS_LOADER_URL_REPLACEMENT_13___ = _node_modules_next_dist_build_webpack_loaders_css_loader_src_runtime_getUrl_js__WEBPACK_IMPORTED_MODULE_1___default()(_assets_fonts_Poppins_Regular_ttf__WEBPACK_IMPORTED_MODULE_15__);\nvar ___CSS_LOADER_URL_REPLACEMENT_14___ = _node_modules_next_dist_build_webpack_loaders_css_loader_src_runtime_getUrl_js__WEBPACK_IMPORTED_MODULE_1___default()(_assets_fonts_Poppins_SemiBold_ttf__WEBPACK_IMPORTED_MODULE_16__);\nvar ___CSS_LOADER_URL_REPLACEMENT_15___ = _node_modules_next_dist_build_webpack_loaders_css_loader_src_runtime_getUrl_js__WEBPACK_IMPORTED_MODULE_1___default()(_assets_fonts_Poppins_SemiBoldItalic_ttf__WEBPACK_IMPORTED_MODULE_17__);\n// Module\n___CSS_LOADER_EXPORT___.push([module.id, \"/*\\n! tailwindcss v3.3.3 | MIT License | https://tailwindcss.com\\n*//*\\n1. Prevent padding and border from affecting element width. (https://github.com/mozdevs/cssremedy/issues/4)\\n2. Allow adding a border to an element by just adding a border-width. (https://github.com/tailwindcss/tailwindcss/pull/116)\\n*/\\n\\n*,\\n::before,\\n::after {\\n  box-sizing: border-box; /* 1 */\\n  border-width: 0; /* 2 */\\n  border-style: solid; /* 2 */\\n  border-color: #e5e7eb; /* 2 */\\n}\\n\\n::before,\\n::after {\\n  --tw-content: '';\\n}\\n\\n/*\\n1. Use a consistent sensible line-height in all browsers.\\n2. Prevent adjustments of font size after orientation changes in iOS.\\n3. Use a more readable tab size.\\n4. Use the user's configured `sans` font-family by default.\\n5. Use the user's configured `sans` font-feature-settings by default.\\n6. Use the user's configured `sans` font-variation-settings by default.\\n*/\\n\\nhtml {\\n  line-height: 1.5; /* 1 */\\n  -webkit-text-size-adjust: 100%; /* 2 */\\n  -moz-tab-size: 4; /* 3 */\\n  -o-tab-size: 4;\\n     tab-size: 4; /* 3 */\\n  font-family: ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont, \\\"Segoe UI\\\", Roboto, \\\"Helvetica Neue\\\", Arial, \\\"Noto Sans\\\", sans-serif, \\\"Apple Color Emoji\\\", \\\"Segoe UI Emoji\\\", \\\"Segoe UI Symbol\\\", \\\"Noto Color Emoji\\\"; /* 4 */\\n  font-feature-settings: normal; /* 5 */\\n  font-variation-settings: normal; /* 6 */\\n}\\n\\n/*\\n1. Remove the margin in all browsers.\\n2. Inherit line-height from `html` so users can set them as a class directly on the `html` element.\\n*/\\n\\nbody {\\n  margin: 0; /* 1 */\\n  line-height: inherit; /* 2 */\\n}\\n\\n/*\\n1. Add the correct height in Firefox.\\n2. Correct the inheritance of border color in Firefox. (https://bugzilla.mozilla.org/show_bug.cgi?id=190655)\\n3. Ensure horizontal rules are visible by default.\\n*/\\n\\nhr {\\n  height: 0; /* 1 */\\n  color: inherit; /* 2 */\\n  border-top-width: 1px; /* 3 */\\n}\\n\\n/*\\nAdd the correct text decoration in Chrome, Edge, and Safari.\\n*/\\n\\nabbr:where([title]) {\\n  -webkit-text-decoration: underline dotted;\\n          text-decoration: underline dotted;\\n}\\n\\n/*\\nRemove the default font size and weight for headings.\\n*/\\n\\nh1,\\nh2,\\nh3,\\nh4,\\nh5,\\nh6 {\\n  font-size: inherit;\\n  font-weight: inherit;\\n}\\n\\n/*\\nReset links to optimize for opt-in styling instead of opt-out.\\n*/\\n\\na {\\n  color: inherit;\\n  text-decoration: inherit;\\n}\\n\\n/*\\nAdd the correct font weight in Edge and Safari.\\n*/\\n\\nb,\\nstrong {\\n  font-weight: bolder;\\n}\\n\\n/*\\n1. Use the user's configured `mono` font family by default.\\n2. Correct the odd `em` font sizing in all browsers.\\n*/\\n\\ncode,\\nkbd,\\nsamp,\\npre {\\n  font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, \\\"Liberation Mono\\\", \\\"Courier New\\\", monospace; /* 1 */\\n  font-size: 1em; /* 2 */\\n}\\n\\n/*\\nAdd the correct font size in all browsers.\\n*/\\n\\nsmall {\\n  font-size: 80%;\\n}\\n\\n/*\\nPrevent `sub` and `sup` elements from affecting the line height in all browsers.\\n*/\\n\\nsub,\\nsup {\\n  font-size: 75%;\\n  line-height: 0;\\n  position: relative;\\n  vertical-align: baseline;\\n}\\n\\nsub {\\n  bottom: -0.25em;\\n}\\n\\nsup {\\n  top: -0.5em;\\n}\\n\\n/*\\n1. Remove text indentation from table contents in Chrome and Safari. (https://bugs.chromium.org/p/chromium/issues/detail?id=999088, https://bugs.webkit.org/show_bug.cgi?id=201297)\\n2. Correct table border color inheritance in all Chrome and Safari. (https://bugs.chromium.org/p/chromium/issues/detail?id=935729, https://bugs.webkit.org/show_bug.cgi?id=195016)\\n3. Remove gaps between table borders by default.\\n*/\\n\\ntable {\\n  text-indent: 0; /* 1 */\\n  border-color: inherit; /* 2 */\\n  border-collapse: collapse; /* 3 */\\n}\\n\\n/*\\n1. Change the font styles in all browsers.\\n2. Remove the margin in Firefox and Safari.\\n3. Remove default padding in all browsers.\\n*/\\n\\nbutton,\\ninput,\\noptgroup,\\nselect,\\ntextarea {\\n  font-family: inherit; /* 1 */\\n  font-feature-settings: inherit; /* 1 */\\n  font-variation-settings: inherit; /* 1 */\\n  font-size: 100%; /* 1 */\\n  font-weight: inherit; /* 1 */\\n  line-height: inherit; /* 1 */\\n  color: inherit; /* 1 */\\n  margin: 0; /* 2 */\\n  padding: 0; /* 3 */\\n}\\n\\n/*\\nRemove the inheritance of text transform in Edge and Firefox.\\n*/\\n\\nbutton,\\nselect {\\n  text-transform: none;\\n}\\n\\n/*\\n1. Correct the inability to style clickable types in iOS and Safari.\\n2. Remove default button styles.\\n*/\\n\\nbutton,\\n[type='button'],\\n[type='reset'],\\n[type='submit'] {\\n  -webkit-appearance: button; /* 1 */\\n  background-color: transparent; /* 2 */\\n  background-image: none; /* 2 */\\n}\\n\\n/*\\nUse the modern Firefox focus style for all focusable elements.\\n*/\\n\\n:-moz-focusring {\\n  outline: auto;\\n}\\n\\n/*\\nRemove the additional `:invalid` styles in Firefox. (https://github.com/mozilla/gecko-dev/blob/2f9eacd9d3d995c937b4251a5557d95d494c9be1/layout/style/res/forms.css#L728-L737)\\n*/\\n\\n:-moz-ui-invalid {\\n  box-shadow: none;\\n}\\n\\n/*\\nAdd the correct vertical alignment in Chrome and Firefox.\\n*/\\n\\nprogress {\\n  vertical-align: baseline;\\n}\\n\\n/*\\nCorrect the cursor style of increment and decrement buttons in Safari.\\n*/\\n\\n::-webkit-inner-spin-button,\\n::-webkit-outer-spin-button {\\n  height: auto;\\n}\\n\\n/*\\n1. Correct the odd appearance in Chrome and Safari.\\n2. Correct the outline style in Safari.\\n*/\\n\\n[type='search'] {\\n  -webkit-appearance: textfield; /* 1 */\\n  outline-offset: -2px; /* 2 */\\n}\\n\\n/*\\nRemove the inner padding in Chrome and Safari on macOS.\\n*/\\n\\n::-webkit-search-decoration {\\n  -webkit-appearance: none;\\n}\\n\\n/*\\n1. Correct the inability to style clickable types in iOS and Safari.\\n2. Change font properties to `inherit` in Safari.\\n*/\\n\\n::-webkit-file-upload-button {\\n  -webkit-appearance: button; /* 1 */\\n  font: inherit; /* 2 */\\n}\\n\\n/*\\nAdd the correct display in Chrome and Safari.\\n*/\\n\\nsummary {\\n  display: list-item;\\n}\\n\\n/*\\nRemoves the default spacing and border for appropriate elements.\\n*/\\n\\nblockquote,\\ndl,\\ndd,\\nh1,\\nh2,\\nh3,\\nh4,\\nh5,\\nh6,\\nhr,\\nfigure,\\np,\\npre {\\n  margin: 0;\\n}\\n\\nfieldset {\\n  margin: 0;\\n  padding: 0;\\n}\\n\\nlegend {\\n  padding: 0;\\n}\\n\\nol,\\nul,\\nmenu {\\n  list-style: none;\\n  margin: 0;\\n  padding: 0;\\n}\\n\\n/*\\nReset default styling for dialogs.\\n*/\\ndialog {\\n  padding: 0;\\n}\\n\\n/*\\nPrevent resizing textareas horizontally by default.\\n*/\\n\\ntextarea {\\n  resize: vertical;\\n}\\n\\n/*\\n1. Reset the default placeholder opacity in Firefox. (https://github.com/tailwindlabs/tailwindcss/issues/3300)\\n2. Set the default placeholder color to the user's configured gray 400 color.\\n*/\\n\\ninput::-moz-placeholder, textarea::-moz-placeholder {\\n  opacity: 1; /* 1 */\\n  color: #9ca3af; /* 2 */\\n}\\n\\ninput::placeholder,\\ntextarea::placeholder {\\n  opacity: 1; /* 1 */\\n  color: #9ca3af; /* 2 */\\n}\\n\\n/*\\nSet the default cursor for buttons.\\n*/\\n\\nbutton,\\n[role=\\\"button\\\"] {\\n  cursor: pointer;\\n}\\n\\n/*\\nMake sure disabled buttons don't get the pointer cursor.\\n*/\\n:disabled {\\n  cursor: default;\\n}\\n\\n/*\\n1. Make replaced elements `display: block` by default. (https://github.com/mozdevs/cssremedy/issues/14)\\n2. Add `vertical-align: middle` to align replaced elements more sensibly by default. (https://github.com/jensimmons/cssremedy/issues/14#issuecomment-634934210)\\n   This can trigger a poorly considered lint error in some tools but is included by design.\\n*/\\n\\nimg,\\nsvg,\\nvideo,\\ncanvas,\\naudio,\\niframe,\\nembed,\\nobject {\\n  display: block; /* 1 */\\n  vertical-align: middle; /* 2 */\\n}\\n\\n/*\\nConstrain images and videos to the parent width and preserve their intrinsic aspect ratio. (https://github.com/mozdevs/cssremedy/issues/14)\\n*/\\n\\nimg,\\nvideo {\\n  max-width: 100%;\\n  height: auto;\\n}\\n\\n/* Make elements with the HTML hidden attribute stay hidden by default */\\n[hidden] {\\n  display: none;\\n}\\n\\n*, ::before, ::after {\\n  --tw-border-spacing-x: 0;\\n  --tw-border-spacing-y: 0;\\n  --tw-translate-x: 0;\\n  --tw-translate-y: 0;\\n  --tw-rotate: 0;\\n  --tw-skew-x: 0;\\n  --tw-skew-y: 0;\\n  --tw-scale-x: 1;\\n  --tw-scale-y: 1;\\n  --tw-pan-x:  ;\\n  --tw-pan-y:  ;\\n  --tw-pinch-zoom:  ;\\n  --tw-scroll-snap-strictness: proximity;\\n  --tw-gradient-from-position:  ;\\n  --tw-gradient-via-position:  ;\\n  --tw-gradient-to-position:  ;\\n  --tw-ordinal:  ;\\n  --tw-slashed-zero:  ;\\n  --tw-numeric-figure:  ;\\n  --tw-numeric-spacing:  ;\\n  --tw-numeric-fraction:  ;\\n  --tw-ring-inset:  ;\\n  --tw-ring-offset-width: 0px;\\n  --tw-ring-offset-color: #fff;\\n  --tw-ring-color: rgb(59 130 246 / 0.5);\\n  --tw-ring-offset-shadow: 0 0 #0000;\\n  --tw-ring-shadow: 0 0 #0000;\\n  --tw-shadow: 0 0 #0000;\\n  --tw-shadow-colored: 0 0 #0000;\\n  --tw-blur:  ;\\n  --tw-brightness:  ;\\n  --tw-contrast:  ;\\n  --tw-grayscale:  ;\\n  --tw-hue-rotate:  ;\\n  --tw-invert:  ;\\n  --tw-saturate:  ;\\n  --tw-sepia:  ;\\n  --tw-drop-shadow:  ;\\n  --tw-backdrop-blur:  ;\\n  --tw-backdrop-brightness:  ;\\n  --tw-backdrop-contrast:  ;\\n  --tw-backdrop-grayscale:  ;\\n  --tw-backdrop-hue-rotate:  ;\\n  --tw-backdrop-invert:  ;\\n  --tw-backdrop-opacity:  ;\\n  --tw-backdrop-saturate:  ;\\n  --tw-backdrop-sepia:  ;\\n}\\n\\n::backdrop {\\n  --tw-border-spacing-x: 0;\\n  --tw-border-spacing-y: 0;\\n  --tw-translate-x: 0;\\n  --tw-translate-y: 0;\\n  --tw-rotate: 0;\\n  --tw-skew-x: 0;\\n  --tw-skew-y: 0;\\n  --tw-scale-x: 1;\\n  --tw-scale-y: 1;\\n  --tw-pan-x:  ;\\n  --tw-pan-y:  ;\\n  --tw-pinch-zoom:  ;\\n  --tw-scroll-snap-strictness: proximity;\\n  --tw-gradient-from-position:  ;\\n  --tw-gradient-via-position:  ;\\n  --tw-gradient-to-position:  ;\\n  --tw-ordinal:  ;\\n  --tw-slashed-zero:  ;\\n  --tw-numeric-figure:  ;\\n  --tw-numeric-spacing:  ;\\n  --tw-numeric-fraction:  ;\\n  --tw-ring-inset:  ;\\n  --tw-ring-offset-width: 0px;\\n  --tw-ring-offset-color: #fff;\\n  --tw-ring-color: rgb(59 130 246 / 0.5);\\n  --tw-ring-offset-shadow: 0 0 #0000;\\n  --tw-ring-shadow: 0 0 #0000;\\n  --tw-shadow: 0 0 #0000;\\n  --tw-shadow-colored: 0 0 #0000;\\n  --tw-blur:  ;\\n  --tw-brightness:  ;\\n  --tw-contrast:  ;\\n  --tw-grayscale:  ;\\n  --tw-hue-rotate:  ;\\n  --tw-invert:  ;\\n  --tw-saturate:  ;\\n  --tw-sepia:  ;\\n  --tw-drop-shadow:  ;\\n  --tw-backdrop-blur:  ;\\n  --tw-backdrop-brightness:  ;\\n  --tw-backdrop-contrast:  ;\\n  --tw-backdrop-grayscale:  ;\\n  --tw-backdrop-hue-rotate:  ;\\n  --tw-backdrop-invert:  ;\\n  --tw-backdrop-opacity:  ;\\n  --tw-backdrop-saturate:  ;\\n  --tw-backdrop-sepia:  ;\\n}\\r\\n.container {\\n  width: 100%;\\n}\\r\\n@media (min-width: 640px) {\\n\\n  .container {\\n    max-width: 640px;\\n  }\\n}\\r\\n@media (min-width: 765px) {\\n\\n  .container {\\n    max-width: 765px;\\n  }\\n}\\r\\n@media (min-width: 1024px) {\\n\\n  .container {\\n    max-width: 1024px;\\n  }\\n}\\r\\n@media (min-width: 1280px) {\\n\\n  .container {\\n    max-width: 1280px;\\n  }\\n}\\r\\n@media (min-width: 1536px) {\\n\\n  .container {\\n    max-width: 1536px;\\n  }\\n}\\r\\n@media (min-width: 1920px) {\\n\\n  .container {\\n    max-width: 1920px;\\n  }\\n}\\r\\n.sr-only {\\n  position: absolute;\\n  width: 1px;\\n  height: 1px;\\n  padding: 0;\\n  margin: -1px;\\n  overflow: hidden;\\n  clip: rect(0, 0, 0, 0);\\n  white-space: nowrap;\\n  border-width: 0;\\n}\\r\\n.pointer-events-none {\\n  pointer-events: none;\\n}\\r\\n.visible {\\n  visibility: visible;\\n}\\r\\n.fixed {\\n  position: fixed;\\n}\\r\\n.\\\\!absolute {\\n  position: absolute !important;\\n}\\r\\n.absolute {\\n  position: absolute;\\n}\\r\\n.relative {\\n  position: relative;\\n}\\r\\n.sticky {\\n  position: sticky;\\n}\\r\\n.inset-0 {\\n  inset: 0px;\\n}\\r\\n.\\\\!right-1 {\\n  right: 0.25rem !important;\\n}\\r\\n.\\\\!right-2 {\\n  right: 0.5rem !important;\\n}\\r\\n.\\\\!top-1 {\\n  top: 0.25rem !important;\\n}\\r\\n.-top-0 {\\n  top: -0px;\\n}\\r\\n.-top-2 {\\n  top: -0.5rem;\\n}\\r\\n.-top-3 {\\n  top: -0.75rem;\\n}\\r\\n.-top-4 {\\n  top: -1rem;\\n}\\r\\n.-top-\\\\[0\\\\.9\\\\] {\\n  top: -0.9;\\n}\\r\\n.-top-\\\\[2px\\\\] {\\n  top: -2px;\\n}\\r\\n.bottom-0 {\\n  bottom: 0px;\\n}\\r\\n.bottom-\\\\[-20px\\\\] {\\n  bottom: -20px;\\n}\\r\\n.left-0 {\\n  left: 0px;\\n}\\r\\n.left-1\\\\/2 {\\n  left: 50%;\\n}\\r\\n.left-2 {\\n  left: 0.5rem;\\n}\\r\\n.left-5 {\\n  left: 1.25rem;\\n}\\r\\n.left-\\\\[42\\\\%\\\\] {\\n  left: 42%;\\n}\\r\\n.right-0 {\\n  right: 0px;\\n}\\r\\n.right-1 {\\n  right: 0.25rem;\\n}\\r\\n.right-10 {\\n  right: 2.5rem;\\n}\\r\\n.right-2 {\\n  right: 0.5rem;\\n}\\r\\n.right-5 {\\n  right: 1.25rem;\\n}\\r\\n.top-0 {\\n  top: 0px;\\n}\\r\\n.top-1 {\\n  top: 0.25rem;\\n}\\r\\n.top-1\\\\/2 {\\n  top: 50%;\\n}\\r\\n.top-2 {\\n  top: 0.5rem;\\n}\\r\\n.top-\\\\[52px\\\\] {\\n  top: 52px;\\n}\\r\\n.top-full {\\n  top: 100%;\\n}\\r\\n.\\\\!z-\\\\[10\\\\] {\\n  z-index: 10 !important;\\n}\\r\\n.\\\\!z-\\\\[9999999\\\\] {\\n  z-index: 9999999 !important;\\n}\\r\\n.\\\\!z-\\\\[9\\\\] {\\n  z-index: 9 !important;\\n}\\r\\n.z-10 {\\n  z-index: 10;\\n}\\r\\n.z-20 {\\n  z-index: 20;\\n}\\r\\n.z-50 {\\n  z-index: 50;\\n}\\r\\n.z-\\\\[5\\\\] {\\n  z-index: 5;\\n}\\r\\n.z-\\\\[999\\\\] {\\n  z-index: 999;\\n}\\r\\n.col-span-2 {\\n  grid-column: span 2 / span 2;\\n}\\r\\n.col-start-6 {\\n  grid-column-start: 6;\\n}\\r\\n.m-1 {\\n  margin: 0.25rem;\\n}\\r\\n.m-3 {\\n  margin: 0.75rem;\\n}\\r\\n.m-4 {\\n  margin: 1rem;\\n}\\r\\n.m-5 {\\n  margin: 1.25rem;\\n}\\r\\n.\\\\!my-3 {\\n  margin-top: 0.75rem !important;\\n  margin-bottom: 0.75rem !important;\\n}\\r\\n.mx-2 {\\n  margin-left: 0.5rem;\\n  margin-right: 0.5rem;\\n}\\r\\n.mx-4 {\\n  margin-left: 1rem;\\n  margin-right: 1rem;\\n}\\r\\n.mx-auto {\\n  margin-left: auto;\\n  margin-right: auto;\\n}\\r\\n.my-1 {\\n  margin-top: 0.25rem;\\n  margin-bottom: 0.25rem;\\n}\\r\\n.my-2 {\\n  margin-top: 0.5rem;\\n  margin-bottom: 0.5rem;\\n}\\r\\n.my-32 {\\n  margin-top: 8rem;\\n  margin-bottom: 8rem;\\n}\\r\\n.my-4 {\\n  margin-top: 1rem;\\n  margin-bottom: 1rem;\\n}\\r\\n.my-5 {\\n  margin-top: 1.25rem;\\n  margin-bottom: 1.25rem;\\n}\\r\\n.my-6 {\\n  margin-top: 1.5rem;\\n  margin-bottom: 1.5rem;\\n}\\r\\n.my-8 {\\n  margin-top: 2rem;\\n  margin-bottom: 2rem;\\n}\\r\\n.\\\\!mt-2 {\\n  margin-top: 0.5rem !important;\\n}\\r\\n.\\\\!mt-3 {\\n  margin-top: 0.75rem !important;\\n}\\r\\n.-mt-1 {\\n  margin-top: -0.25rem;\\n}\\r\\n.-mt-\\\\[1px\\\\] {\\n  margin-top: -1px;\\n}\\r\\n.-mt-\\\\[2px\\\\] {\\n  margin-top: -2px;\\n}\\r\\n.mb-0 {\\n  margin-bottom: 0px;\\n}\\r\\n.mb-1 {\\n  margin-bottom: 0.25rem;\\n}\\r\\n.mb-10 {\\n  margin-bottom: 2.5rem;\\n}\\r\\n.mb-2 {\\n  margin-bottom: 0.5rem;\\n}\\r\\n.mb-20 {\\n  margin-bottom: 5rem;\\n}\\r\\n.mb-3 {\\n  margin-bottom: 0.75rem;\\n}\\r\\n.mb-4 {\\n  margin-bottom: 1rem;\\n}\\r\\n.mb-5 {\\n  margin-bottom: 1.25rem;\\n}\\r\\n.mb-6 {\\n  margin-bottom: 1.5rem;\\n}\\r\\n.mb-8 {\\n  margin-bottom: 2rem;\\n}\\r\\n.mb-\\\\[18px\\\\] {\\n  margin-bottom: 18px;\\n}\\r\\n.mb-\\\\[2px\\\\] {\\n  margin-bottom: 2px;\\n}\\r\\n.me-10 {\\n  margin-inline-end: 2.5rem;\\n}\\r\\n.me-3 {\\n  margin-inline-end: 0.75rem;\\n}\\r\\n.me-4 {\\n  margin-inline-end: 1rem;\\n}\\r\\n.me-5 {\\n  margin-inline-end: 1.25rem;\\n}\\r\\n.ml-0 {\\n  margin-left: 0px;\\n}\\r\\n.ml-1 {\\n  margin-left: 0.25rem;\\n}\\r\\n.ml-2 {\\n  margin-left: 0.5rem;\\n}\\r\\n.ml-3 {\\n  margin-left: 0.75rem;\\n}\\r\\n.ml-4 {\\n  margin-left: 1rem;\\n}\\r\\n.ml-5 {\\n  margin-left: 1.25rem;\\n}\\r\\n.ml-8 {\\n  margin-left: 2rem;\\n}\\r\\n.ml-\\\\[18\\\\%\\\\] {\\n  margin-left: 18%;\\n}\\r\\n.ml-\\\\[28px\\\\] {\\n  margin-left: 28px;\\n}\\r\\n.ml-\\\\[46px\\\\] {\\n  margin-left: 46px;\\n}\\r\\n.ml-auto {\\n  margin-left: auto;\\n}\\r\\n.mr-1 {\\n  margin-right: 0.25rem;\\n}\\r\\n.mr-12 {\\n  margin-right: 3rem;\\n}\\r\\n.mr-2 {\\n  margin-right: 0.5rem;\\n}\\r\\n.mr-20 {\\n  margin-right: 5rem;\\n}\\r\\n.mr-3 {\\n  margin-right: 0.75rem;\\n}\\r\\n.mr-4 {\\n  margin-right: 1rem;\\n}\\r\\n.mr-\\\\[23px\\\\] {\\n  margin-right: 23px;\\n}\\r\\n.mr-\\\\[25px\\\\] {\\n  margin-right: 25px;\\n}\\r\\n.mr-\\\\[55px\\\\] {\\n  margin-right: 55px;\\n}\\r\\n.mr-\\\\[5px\\\\] {\\n  margin-right: 5px;\\n}\\r\\n.mt-0 {\\n  margin-top: 0px;\\n}\\r\\n.mt-1 {\\n  margin-top: 0.25rem;\\n}\\r\\n.mt-14 {\\n  margin-top: 3.5rem;\\n}\\r\\n.mt-2 {\\n  margin-top: 0.5rem;\\n}\\r\\n.mt-3 {\\n  margin-top: 0.75rem;\\n}\\r\\n.mt-4 {\\n  margin-top: 1rem;\\n}\\r\\n.mt-5 {\\n  margin-top: 1.25rem;\\n}\\r\\n.mt-6 {\\n  margin-top: 1.5rem;\\n}\\r\\n.mt-7 {\\n  margin-top: 1.75rem;\\n}\\r\\n.mt-8 {\\n  margin-top: 2rem;\\n}\\r\\n.mt-9 {\\n  margin-top: 2.25rem;\\n}\\r\\n.mt-\\\\[17px\\\\] {\\n  margin-top: 17px;\\n}\\r\\n.mt-\\\\[2px\\\\] {\\n  margin-top: 2px;\\n}\\r\\n.mt-\\\\[45px\\\\] {\\n  margin-top: 45px;\\n}\\r\\n.mt-\\\\[60px\\\\] {\\n  margin-top: 60px;\\n}\\r\\n.block {\\n  display: block;\\n}\\r\\n.inline-block {\\n  display: inline-block;\\n}\\r\\n.inline {\\n  display: inline;\\n}\\r\\n.\\\\!flex {\\n  display: flex !important;\\n}\\r\\n.flex {\\n  display: flex;\\n}\\r\\n.inline-flex {\\n  display: inline-flex;\\n}\\r\\n.table {\\n  display: table;\\n}\\r\\n.grid {\\n  display: grid;\\n}\\r\\n.hidden {\\n  display: none;\\n}\\r\\n.\\\\!h-0 {\\n  height: 0px !important;\\n}\\r\\n.h-10 {\\n  height: 2.5rem;\\n}\\r\\n.h-12 {\\n  height: 3rem;\\n}\\r\\n.h-14 {\\n  height: 3.5rem;\\n}\\r\\n.h-4 {\\n  height: 1rem;\\n}\\r\\n.h-5 {\\n  height: 1.25rem;\\n}\\r\\n.h-52 {\\n  height: 13rem;\\n}\\r\\n.h-6 {\\n  height: 1.5rem;\\n}\\r\\n.h-8 {\\n  height: 2rem;\\n}\\r\\n.h-9 {\\n  height: 2.25rem;\\n}\\r\\n.h-\\\\[100vh\\\\] {\\n  height: 100vh;\\n}\\r\\n.h-\\\\[15px\\\\] {\\n  height: 15px;\\n}\\r\\n.h-\\\\[20px\\\\] {\\n  height: 20px;\\n}\\r\\n.h-\\\\[22px\\\\] {\\n  height: 22px;\\n}\\r\\n.h-\\\\[23px\\\\] {\\n  height: 23px;\\n}\\r\\n.h-\\\\[25px\\\\] {\\n  height: 25px;\\n}\\r\\n.h-\\\\[28px\\\\] {\\n  height: 28px;\\n}\\r\\n.h-\\\\[30px\\\\] {\\n  height: 30px;\\n}\\r\\n.h-\\\\[31px\\\\] {\\n  height: 31px;\\n}\\r\\n.h-\\\\[36px\\\\] {\\n  height: 36px;\\n}\\r\\n.h-\\\\[38px\\\\] {\\n  height: 38px;\\n}\\r\\n.h-\\\\[40px\\\\] {\\n  height: 40px;\\n}\\r\\n.h-\\\\[calc\\\\(100vh-100px\\\\)\\\\] {\\n  height: calc(100vh - 100px);\\n}\\r\\n.h-full {\\n  height: 100%;\\n}\\r\\n.\\\\!h-16 {\\n  height: 4rem !important;\\n}\\r\\n.h-24 {\\n  height: 6rem;\\n}\\r\\n.h-\\\\[85vh\\\\] {\\n  height: 85vh;\\n}\\r\\n.\\\\!max-h-\\\\[28rem\\\\] {\\n  max-height: 28rem !important;\\n}\\r\\n.\\\\!max-h-\\\\[70vh\\\\] {\\n  max-height: 70vh !important;\\n}\\r\\n.\\\\!max-h-full {\\n  max-height: 100% !important;\\n}\\r\\n.min-h-full {\\n  min-height: 100%;\\n}\\r\\n.min-h-screen {\\n  min-height: 100vh;\\n}\\r\\n.\\\\!w-10 {\\n  width: 2.5rem !important;\\n}\\r\\n.\\\\!w-2\\\\/5 {\\n  width: 40% !important;\\n}\\r\\n.\\\\!w-28 {\\n  width: 7rem !important;\\n}\\r\\n.\\\\!w-32 {\\n  width: 8rem !important;\\n}\\r\\n.\\\\!w-44 {\\n  width: 11rem !important;\\n}\\r\\n.\\\\!w-48 {\\n  width: 12rem !important;\\n}\\r\\n.\\\\!w-52 {\\n  width: 13rem !important;\\n}\\r\\n.\\\\!w-60 {\\n  width: 15rem !important;\\n}\\r\\n.\\\\!w-72 {\\n  width: 18rem !important;\\n}\\r\\n.\\\\!w-80 {\\n  width: 20rem !important;\\n}\\r\\n.\\\\!w-\\\\[20\\\\%\\\\] {\\n  width: 20% !important;\\n}\\r\\n.\\\\!w-\\\\[450px\\\\] {\\n  width: 450px !important;\\n}\\r\\n.\\\\!w-full {\\n  width: 100% !important;\\n}\\r\\n.w-1\\\\/2 {\\n  width: 50%;\\n}\\r\\n.w-1\\\\/3 {\\n  width: 33.333333%;\\n}\\r\\n.w-1\\\\/4 {\\n  width: 25%;\\n}\\r\\n.w-1\\\\/5 {\\n  width: 20%;\\n}\\r\\n.w-11 {\\n  width: 2.75rem;\\n}\\r\\n.w-16 {\\n  width: 4rem;\\n}\\r\\n.w-2\\\\/3 {\\n  width: 66.666667%;\\n}\\r\\n.w-2\\\\/5 {\\n  width: 40%;\\n}\\r\\n.w-2\\\\/6 {\\n  width: 33.333333%;\\n}\\r\\n.w-28 {\\n  width: 7rem;\\n}\\r\\n.w-3\\\\/4 {\\n  width: 75%;\\n}\\r\\n.w-4 {\\n  width: 1rem;\\n}\\r\\n.w-5 {\\n  width: 1.25rem;\\n}\\r\\n.w-6 {\\n  width: 1.5rem;\\n}\\r\\n.w-8 {\\n  width: 2rem;\\n}\\r\\n.w-\\\\[10\\\\%\\\\] {\\n  width: 10%;\\n}\\r\\n.w-\\\\[100\\\\%-70px\\\\] {\\n  width: 100%-70px;\\n}\\r\\n.w-\\\\[100px\\\\] {\\n  width: 100px;\\n}\\r\\n.w-\\\\[115px\\\\] {\\n  width: 115px;\\n}\\r\\n.w-\\\\[130px\\\\] {\\n  width: 130px;\\n}\\r\\n.w-\\\\[150px\\\\] {\\n  width: 150px;\\n}\\r\\n.w-\\\\[15px\\\\] {\\n  width: 15px;\\n}\\r\\n.w-\\\\[160px\\\\] {\\n  width: 160px;\\n}\\r\\n.w-\\\\[20\\\\%\\\\] {\\n  width: 20%;\\n}\\r\\n.w-\\\\[22px\\\\] {\\n  width: 22px;\\n}\\r\\n.w-\\\\[25\\\\%\\\\] {\\n  width: 25%;\\n}\\r\\n.w-\\\\[25px\\\\] {\\n  width: 25px;\\n}\\r\\n.w-\\\\[30\\\\%\\\\] {\\n  width: 30%;\\n}\\r\\n.w-\\\\[30px\\\\] {\\n  width: 30px;\\n}\\r\\n.w-\\\\[40\\\\%\\\\] {\\n  width: 40%;\\n}\\r\\n.w-\\\\[44\\\\%\\\\] {\\n  width: 44%;\\n}\\r\\n.w-\\\\[45\\\\%\\\\] {\\n  width: 45%;\\n}\\r\\n.w-\\\\[47vh\\\\] {\\n  width: 47vh;\\n}\\r\\n.w-\\\\[48\\\\%\\\\] {\\n  width: 48%;\\n}\\r\\n.w-\\\\[5\\\\%\\\\] {\\n  width: 5%;\\n}\\r\\n.w-\\\\[50\\\\%\\\\] {\\n  width: 50%;\\n}\\r\\n.w-\\\\[500px\\\\] {\\n  width: 500px;\\n}\\r\\n.w-\\\\[60\\\\%\\\\] {\\n  width: 60%;\\n}\\r\\n.w-\\\\[75\\\\%\\\\] {\\n  width: 75%;\\n}\\r\\n.w-\\\\[8\\\\%\\\\] {\\n  width: 8%;\\n}\\r\\n.w-\\\\[93\\\\%\\\\] {\\n  width: 93%;\\n}\\r\\n.w-\\\\[94\\\\%\\\\] {\\n  width: 94%;\\n}\\r\\n.w-\\\\[95\\\\%\\\\] {\\n  width: 95%;\\n}\\r\\n.w-\\\\[96\\\\%\\\\] {\\n  width: 96%;\\n}\\r\\n.w-\\\\[calc\\\\(100\\\\%-20px\\\\)\\\\] {\\n  width: calc(100% - 20px);\\n}\\r\\n.w-auto {\\n  width: auto;\\n}\\r\\n.w-full {\\n  width: 100%;\\n}\\r\\n.w-screen {\\n  width: 100vw;\\n}\\r\\n.\\\\!w-auto {\\n  width: auto !important;\\n}\\r\\n.w-80 {\\n  width: 20rem;\\n}\\r\\n.w-\\\\[15\\\\%\\\\] {\\n  width: 15%;\\n}\\r\\n.w-96 {\\n  width: 24rem;\\n}\\r\\n.\\\\!min-w-0 {\\n  min-width: 0px !important;\\n}\\r\\n.\\\\!min-w-fit {\\n  min-width: -moz-fit-content !important;\\n  min-width: fit-content !important;\\n}\\r\\n.\\\\!max-w-\\\\[60\\\\%\\\\] {\\n  max-width: 60% !important;\\n}\\r\\n.\\\\!max-w-\\\\[600px\\\\] {\\n  max-width: 600px !important;\\n}\\r\\n.\\\\!max-w-\\\\[90\\\\%\\\\] {\\n  max-width: 90% !important;\\n}\\r\\n.max-w-\\\\[288px\\\\] {\\n  max-width: 288px;\\n}\\r\\n.max-w-md {\\n  max-width: 28rem;\\n}\\r\\n.flex-1 {\\n  flex: 1 1 0%;\\n}\\r\\n.table-fixed {\\n  table-layout: fixed;\\n}\\r\\n.border-collapse {\\n  border-collapse: collapse;\\n}\\r\\n.-translate-x-1\\\\/2 {\\n  --tw-translate-x: -50%;\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\r\\n.-translate-x-\\\\[58\\\\.5\\\\%\\\\] {\\n  --tw-translate-x: -58.5%;\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\r\\n.-translate-y-1\\\\/2 {\\n  --tw-translate-y: -50%;\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\r\\n.translate-y-0 {\\n  --tw-translate-y: 0px;\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\r\\n.translate-y-1 {\\n  --tw-translate-y: 0.25rem;\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\r\\n.-rotate-90 {\\n  --tw-rotate: -90deg;\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\r\\n.rotate-180 {\\n  --tw-rotate: 180deg;\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\r\\n.rotate-45 {\\n  --tw-rotate: 45deg;\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\r\\n.scale-100 {\\n  --tw-scale-x: 1;\\n  --tw-scale-y: 1;\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\r\\n.scale-95 {\\n  --tw-scale-x: .95;\\n  --tw-scale-y: .95;\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\r\\n.scale-x-100 {\\n  --tw-scale-x: 1;\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\r\\n.transform {\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\r\\n.cursor-default {\\n  cursor: default;\\n}\\r\\n.cursor-not-allowed {\\n  cursor: not-allowed;\\n}\\r\\n.cursor-pointer {\\n  cursor: pointer;\\n}\\r\\n.resize-none {\\n  resize: none;\\n}\\r\\n.resize {\\n  resize: both;\\n}\\r\\n.list-inside {\\n  list-style-position: inside;\\n}\\r\\n.list-disc {\\n  list-style-type: disc;\\n}\\r\\n.appearance-none {\\n  -webkit-appearance: none;\\n     -moz-appearance: none;\\n          appearance: none;\\n}\\r\\n.grid-cols-1 {\\n  grid-template-columns: repeat(1, minmax(0, 1fr));\\n}\\r\\n.grid-cols-2 {\\n  grid-template-columns: repeat(2, minmax(0, 1fr));\\n}\\r\\n.grid-cols-3 {\\n  grid-template-columns: repeat(3, minmax(0, 1fr));\\n}\\r\\n.grid-cols-4 {\\n  grid-template-columns: repeat(4, minmax(0, 1fr));\\n}\\r\\n.grid-cols-8 {\\n  grid-template-columns: repeat(8, minmax(0, 1fr));\\n}\\r\\n.flex-row {\\n  flex-direction: row;\\n}\\r\\n.flex-col {\\n  flex-direction: column;\\n}\\r\\n.flex-wrap {\\n  flex-wrap: wrap;\\n}\\r\\n.items-start {\\n  align-items: flex-start;\\n}\\r\\n.items-end {\\n  align-items: flex-end;\\n}\\r\\n.items-center {\\n  align-items: center;\\n}\\r\\n.items-baseline {\\n  align-items: baseline;\\n}\\r\\n.items-stretch {\\n  align-items: stretch;\\n}\\r\\n.justify-start {\\n  justify-content: flex-start;\\n}\\r\\n.justify-end {\\n  justify-content: flex-end;\\n}\\r\\n.justify-center {\\n  justify-content: center;\\n}\\r\\n.\\\\!justify-between {\\n  justify-content: space-between !important;\\n}\\r\\n.justify-between {\\n  justify-content: space-between;\\n}\\r\\n.justify-around {\\n  justify-content: space-around;\\n}\\r\\n.\\\\!gap-0 {\\n  gap: 0px !important;\\n}\\r\\n.gap-1 {\\n  gap: 0.25rem;\\n}\\r\\n.gap-10 {\\n  gap: 2.5rem;\\n}\\r\\n.gap-2 {\\n  gap: 0.5rem;\\n}\\r\\n.gap-3 {\\n  gap: 0.75rem;\\n}\\r\\n.gap-4 {\\n  gap: 1rem;\\n}\\r\\n.gap-5 {\\n  gap: 1.25rem;\\n}\\r\\n.gap-6 {\\n  gap: 1.5rem;\\n}\\r\\n.gap-8 {\\n  gap: 2rem;\\n}\\r\\n.gap-12 {\\n  gap: 3rem;\\n}\\r\\n.space-x-1 > :not([hidden]) ~ :not([hidden]) {\\n  --tw-space-x-reverse: 0;\\n  margin-right: calc(0.25rem * var(--tw-space-x-reverse));\\n  margin-left: calc(0.25rem * calc(1 - var(--tw-space-x-reverse)));\\n}\\r\\n.space-x-2 > :not([hidden]) ~ :not([hidden]) {\\n  --tw-space-x-reverse: 0;\\n  margin-right: calc(0.5rem * var(--tw-space-x-reverse));\\n  margin-left: calc(0.5rem * calc(1 - var(--tw-space-x-reverse)));\\n}\\r\\n.space-y-6 > :not([hidden]) ~ :not([hidden]) {\\n  --tw-space-y-reverse: 0;\\n  margin-top: calc(1.5rem * calc(1 - var(--tw-space-y-reverse)));\\n  margin-bottom: calc(1.5rem * var(--tw-space-y-reverse));\\n}\\r\\n.space-x-3 > :not([hidden]) ~ :not([hidden]) {\\n  --tw-space-x-reverse: 0;\\n  margin-right: calc(0.75rem * var(--tw-space-x-reverse));\\n  margin-left: calc(0.75rem * calc(1 - var(--tw-space-x-reverse)));\\n}\\r\\n.space-y-2 > :not([hidden]) ~ :not([hidden]) {\\n  --tw-space-y-reverse: 0;\\n  margin-top: calc(0.5rem * calc(1 - var(--tw-space-y-reverse)));\\n  margin-bottom: calc(0.5rem * var(--tw-space-y-reverse));\\n}\\r\\n.space-y-4 > :not([hidden]) ~ :not([hidden]) {\\n  --tw-space-y-reverse: 0;\\n  margin-top: calc(1rem * calc(1 - var(--tw-space-y-reverse)));\\n  margin-bottom: calc(1rem * var(--tw-space-y-reverse));\\n}\\r\\n.space-x-4 > :not([hidden]) ~ :not([hidden]) {\\n  --tw-space-x-reverse: 0;\\n  margin-right: calc(1rem * var(--tw-space-x-reverse));\\n  margin-left: calc(1rem * calc(1 - var(--tw-space-x-reverse)));\\n}\\r\\n.space-y-1 > :not([hidden]) ~ :not([hidden]) {\\n  --tw-space-y-reverse: 0;\\n  margin-top: calc(0.25rem * calc(1 - var(--tw-space-y-reverse)));\\n  margin-bottom: calc(0.25rem * var(--tw-space-y-reverse));\\n}\\r\\n.space-y-8 > :not([hidden]) ~ :not([hidden]) {\\n  --tw-space-y-reverse: 0;\\n  margin-top: calc(2rem * calc(1 - var(--tw-space-y-reverse)));\\n  margin-bottom: calc(2rem * var(--tw-space-y-reverse));\\n}\\r\\n.overflow-auto {\\n  overflow: auto;\\n}\\r\\n.\\\\!overflow-hidden {\\n  overflow: hidden !important;\\n}\\r\\n.overflow-hidden {\\n  overflow: hidden;\\n}\\r\\n.overflow-x-auto {\\n  overflow-x: auto;\\n}\\r\\n.overflow-y-auto {\\n  overflow-y: auto;\\n}\\r\\n.whitespace-nowrap {\\n  white-space: nowrap;\\n}\\r\\n.\\\\!rounded-md {\\n  border-radius: 0.375rem !important;\\n}\\r\\n.\\\\!rounded-xl {\\n  border-radius: 0.75rem !important;\\n}\\r\\n.rounded {\\n  border-radius: 0.25rem;\\n}\\r\\n.rounded-\\\\[4px\\\\] {\\n  border-radius: 4px;\\n}\\r\\n.rounded-full {\\n  border-radius: 9999px;\\n}\\r\\n.rounded-lg {\\n  border-radius: 0.5rem;\\n}\\r\\n.rounded-md {\\n  border-radius: 0.375rem;\\n}\\r\\n.rounded-sm {\\n  border-radius: 0.125rem;\\n}\\r\\n.rounded-xl {\\n  border-radius: 0.75rem;\\n}\\r\\n.rounded-t {\\n  border-top-left-radius: 0.25rem;\\n  border-top-right-radius: 0.25rem;\\n}\\r\\n.rounded-b-lg {\\n  border-bottom-right-radius: 0.5rem;\\n  border-bottom-left-radius: 0.5rem;\\n}\\r\\n.rounded-t-lg {\\n  border-top-left-radius: 0.5rem;\\n  border-top-right-radius: 0.5rem;\\n}\\r\\n.rounded-bl-lg {\\n  border-bottom-left-radius: 0.5rem;\\n}\\r\\n.rounded-bl-md {\\n  border-bottom-left-radius: 0.375rem;\\n}\\r\\n.rounded-br-lg {\\n  border-bottom-right-radius: 0.5rem;\\n}\\r\\n.rounded-br-md {\\n  border-bottom-right-radius: 0.375rem;\\n}\\r\\n.rounded-tl-lg {\\n  border-top-left-radius: 0.5rem;\\n}\\r\\n.rounded-tl-md {\\n  border-top-left-radius: 0.375rem;\\n}\\r\\n.rounded-tl-xl {\\n  border-top-left-radius: 0.75rem;\\n}\\r\\n.rounded-tr-lg {\\n  border-top-right-radius: 0.5rem;\\n}\\r\\n.rounded-tr-md {\\n  border-top-right-radius: 0.375rem;\\n}\\r\\n.rounded-tr-xl {\\n  border-top-right-radius: 0.75rem;\\n}\\r\\n.\\\\!border {\\n  border-width: 1px !important;\\n}\\r\\n.\\\\!border-0 {\\n  border-width: 0px !important;\\n}\\r\\n.border {\\n  border-width: 1px;\\n}\\r\\n.border-0 {\\n  border-width: 0px;\\n}\\r\\n.border-b {\\n  border-bottom-width: 1px;\\n}\\r\\n.border-b-2 {\\n  border-bottom-width: 2px;\\n}\\r\\n.border-e-\\\\[1px\\\\] {\\n  border-inline-end-width: 1px;\\n}\\r\\n.border-l-4 {\\n  border-left-width: 4px;\\n}\\r\\n.border-r {\\n  border-right-width: 1px;\\n}\\r\\n.border-t {\\n  border-top-width: 1px;\\n}\\r\\n.\\\\!border-bright-red {\\n  --tw-border-opacity: 1 !important;\\n  border-color: rgb(249 54 71 / var(--tw-border-opacity)) !important;\\n}\\r\\n.\\\\!border-red-500 {\\n  --tw-border-opacity: 1 !important;\\n  border-color: rgb(239 68 68 / var(--tw-border-opacity)) !important;\\n}\\r\\n.\\\\!border-seablue {\\n  --tw-border-opacity: 1 !important;\\n  border-color: rgb(0 224 213 / var(--tw-border-opacity)) !important;\\n}\\r\\n.\\\\!border-skin-primary {\\n  --tw-border-opacity: 1 !important;\\n  border-color: rgba(var(--color-primary), var(--tw-border-opacity)) !important;\\n}\\r\\n.border-\\\\[\\\\#0066ff\\\\] {\\n  --tw-border-opacity: 1;\\n  border-color: rgb(0 102 255 / var(--tw-border-opacity));\\n}\\r\\n.border-\\\\[\\\\#FBB522\\\\] {\\n  --tw-border-opacity: 1;\\n  border-color: rgb(251 181 34 / var(--tw-border-opacity));\\n}\\r\\n.border-\\\\[\\\\#cccccc\\\\] {\\n  --tw-border-opacity: 1;\\n  border-color: rgb(204 204 204 / var(--tw-border-opacity));\\n}\\r\\n.border-\\\\[\\\\#ddd\\\\] {\\n  --tw-border-opacity: 1;\\n  border-color: rgb(221 221 221 / var(--tw-border-opacity));\\n}\\r\\n.border-blue-300 {\\n  --tw-border-opacity: 1;\\n  border-color: rgb(147 197 253 / var(--tw-border-opacity));\\n}\\r\\n.border-blue-500 {\\n  --tw-border-opacity: 1;\\n  border-color: rgb(59 130 246 / var(--tw-border-opacity));\\n}\\r\\n.border-bright-green {\\n  --tw-border-opacity: 1;\\n  border-color: rgb(62 171 88 / var(--tw-border-opacity));\\n}\\r\\n.border-bright-red {\\n  --tw-border-opacity: 1;\\n  border-color: rgb(249 54 71 / var(--tw-border-opacity));\\n}\\r\\n.border-gray-100 {\\n  --tw-border-opacity: 1;\\n  border-color: rgb(243 244 246 / var(--tw-border-opacity));\\n}\\r\\n.border-gray-200 {\\n  --tw-border-opacity: 1;\\n  border-color: rgb(229 231 235 / var(--tw-border-opacity));\\n}\\r\\n.border-gray-300 {\\n  --tw-border-opacity: 1;\\n  border-color: rgb(209 213 219 / var(--tw-border-opacity));\\n}\\r\\n.border-gray-400 {\\n  --tw-border-opacity: 1;\\n  border-color: rgb(156 163 175 / var(--tw-border-opacity));\\n}\\r\\n.border-light-gray {\\n  --tw-border-opacity: 1;\\n  border-color: rgb(214 214 214 / var(--tw-border-opacity));\\n}\\r\\n.border-light-gray2 {\\n  --tw-border-opacity: 1;\\n  border-color: rgb(176 176 176 / var(--tw-border-opacity));\\n}\\r\\n.border-light-gray3 {\\n  --tw-border-opacity: 1;\\n  border-color: rgb(211 211 211 / var(--tw-border-opacity));\\n}\\r\\n.border-lighter-gray {\\n  --tw-border-opacity: 1;\\n  border-color: rgb(175 175 175 / var(--tw-border-opacity));\\n}\\r\\n.border-red-500 {\\n  --tw-border-opacity: 1;\\n  border-color: rgb(239 68 68 / var(--tw-border-opacity));\\n}\\r\\n.border-save-green {\\n  --tw-border-opacity: 1;\\n  border-color: rgb(37 174 101 / var(--tw-border-opacity));\\n}\\r\\n.border-skin-primary {\\n  --tw-border-opacity: 1;\\n  border-color: rgba(var(--color-primary), var(--tw-border-opacity));\\n}\\r\\n.border-theme-blue2 {\\n  --tw-border-opacity: 1;\\n  border-color: rgb(0 102 255 / var(--tw-border-opacity));\\n}\\r\\n.border-white {\\n  --tw-border-opacity: 1;\\n  border-color: rgb(255 255 255 / var(--tw-border-opacity));\\n}\\r\\n.border-\\\\[\\\\#d3d3d3\\\\] {\\n  --tw-border-opacity: 1;\\n  border-color: rgb(211 211 211 / var(--tw-border-opacity));\\n}\\r\\n.border-blue-200 {\\n  --tw-border-opacity: 1;\\n  border-color: rgb(191 219 254 / var(--tw-border-opacity));\\n}\\r\\n.border-green-200 {\\n  --tw-border-opacity: 1;\\n  border-color: rgb(187 247 208 / var(--tw-border-opacity));\\n}\\r\\n.border-transparent {\\n  border-color: transparent;\\n}\\r\\n.\\\\!bg-\\\\[\\\\#f3f8ff\\\\] {\\n  --tw-bg-opacity: 1 !important;\\n  background-color: rgb(243 248 255 / var(--tw-bg-opacity)) !important;\\n}\\r\\n.\\\\!bg-be-status {\\n  --tw-bg-opacity: 1 !important;\\n  background-color: rgb(255 154 3 / var(--tw-bg-opacity)) !important;\\n}\\r\\n.\\\\!bg-confirm-status {\\n  --tw-bg-opacity: 1 !important;\\n  background-color: rgb(51 202 127 / var(--tw-bg-opacity)) !important;\\n}\\r\\n.\\\\!bg-gray-100 {\\n  --tw-bg-opacity: 1 !important;\\n  background-color: rgb(243 244 246 / var(--tw-bg-opacity)) !important;\\n}\\r\\n.\\\\!bg-issue-status {\\n  --tw-bg-opacity: 1 !important;\\n  background-color: rgb(251 70 70 / var(--tw-bg-opacity)) !important;\\n}\\r\\n.\\\\!bg-locked-products {\\n  --tw-bg-opacity: 1 !important;\\n  background-color: rgb(211 234 255 / var(--tw-bg-opacity)) !important;\\n}\\r\\n.\\\\!bg-needsupdate-status {\\n  --tw-bg-opacity: 1 !important;\\n  background-color: rgb(255 245 208 / var(--tw-bg-opacity)) !important;\\n}\\r\\n.\\\\!bg-pricechange-status {\\n  --tw-bg-opacity: 1 !important;\\n  background-color: rgb(44 176 250 / var(--tw-bg-opacity)) !important;\\n}\\r\\n.\\\\!bg-seablue {\\n  --tw-bg-opacity: 1 !important;\\n  background-color: rgb(0 224 213 / var(--tw-bg-opacity)) !important;\\n}\\r\\n.\\\\!bg-transparent {\\n  background-color: transparent !important;\\n}\\r\\n.\\\\!bg-volumechange-status {\\n  --tw-bg-opacity: 1 !important;\\n  background-color: rgb(179 187 221 / var(--tw-bg-opacity)) !important;\\n}\\r\\n.\\\\!bg-white {\\n  --tw-bg-opacity: 1 !important;\\n  background-color: rgb(255 255 255 / var(--tw-bg-opacity)) !important;\\n}\\r\\n.\\\\!bg-wip-status {\\n  --tw-bg-opacity: 1 !important;\\n  background-color: rgb(255 223 55 / var(--tw-bg-opacity)) !important;\\n}\\r\\n.bg-\\\\[\\\\#00E0D5\\\\] {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(0 224 213 / var(--tw-bg-opacity));\\n}\\r\\n.bg-\\\\[\\\\#3EAB58\\\\] {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(62 171 88 / var(--tw-bg-opacity));\\n}\\r\\n.bg-\\\\[\\\\#54C5ED\\\\] {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(84 197 237 / var(--tw-bg-opacity));\\n}\\r\\n.bg-\\\\[\\\\#F3F8FF\\\\] {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(243 248 255 / var(--tw-bg-opacity));\\n}\\r\\n.bg-\\\\[\\\\#FF6C09\\\\] {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(255 108 9 / var(--tw-bg-opacity));\\n}\\r\\n.bg-\\\\[\\\\#FFAE00\\\\] {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(255 174 0 / var(--tw-bg-opacity));\\n}\\r\\n.bg-\\\\[\\\\#f3f8ff\\\\] {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(243 248 255 / var(--tw-bg-opacity));\\n}\\r\\n.bg-\\\\[\\\\#ff2929\\\\] {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(255 41 41 / var(--tw-bg-opacity));\\n}\\r\\n.bg-be-status {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(255 154 3 / var(--tw-bg-opacity));\\n}\\r\\n.bg-black {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(0 0 0 / var(--tw-bg-opacity));\\n}\\r\\n.bg-black\\\\/25 {\\n  background-color: rgb(0 0 0 / 0.25);\\n}\\r\\n.bg-blue-50 {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(239 246 255 / var(--tw-bg-opacity));\\n}\\r\\n.bg-blue-600 {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(37 99 235 / var(--tw-bg-opacity));\\n}\\r\\n.bg-cancelled-status {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(255 108 9 / var(--tw-bg-opacity));\\n}\\r\\n.bg-complete-status {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(62 171 88 / var(--tw-bg-opacity));\\n}\\r\\n.bg-confirm-status {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(51 202 127 / var(--tw-bg-opacity));\\n}\\r\\n.bg-gray-100 {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(243 244 246 / var(--tw-bg-opacity));\\n}\\r\\n.bg-gray-200 {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(229 231 235 / var(--tw-bg-opacity));\\n}\\r\\n.bg-green-300 {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(134 239 172 / var(--tw-bg-opacity));\\n}\\r\\n.bg-green-500 {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(34 197 94 / var(--tw-bg-opacity));\\n}\\r\\n.bg-issue-status {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(251 70 70 / var(--tw-bg-opacity));\\n}\\r\\n.bg-locked-products {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(211 234 255 / var(--tw-bg-opacity));\\n}\\r\\n.bg-needsupdate-status {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(255 245 208 / var(--tw-bg-opacity));\\n}\\r\\n.bg-pricechange-status {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(44 176 250 / var(--tw-bg-opacity));\\n}\\r\\n.bg-qtydiff-status {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(255 203 71 / var(--tw-bg-opacity));\\n}\\r\\n.bg-red-500 {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(239 68 68 / var(--tw-bg-opacity));\\n}\\r\\n.bg-save-green {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(37 174 101 / var(--tw-bg-opacity));\\n}\\r\\n.bg-skin-primary {\\n  --tw-bg-opacity: 1;\\n  background-color: rgba(var(--color-primary), var(--tw-bg-opacity));\\n}\\r\\n.bg-theme-blue {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(0 45 115 / var(--tw-bg-opacity));\\n}\\r\\n.bg-theme-blue2 {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(0 102 255 / var(--tw-bg-opacity));\\n}\\r\\n.bg-transparent {\\n  background-color: transparent;\\n}\\r\\n.bg-volumechange-status {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(179 187 221 / var(--tw-bg-opacity));\\n}\\r\\n.bg-white {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(255 255 255 / var(--tw-bg-opacity));\\n}\\r\\n.bg-wip-status {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(255 223 55 / var(--tw-bg-opacity));\\n}\\r\\n.bg-gray-50 {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(249 250 251 / var(--tw-bg-opacity));\\n}\\r\\n.bg-gray-600 {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(75 85 99 / var(--tw-bg-opacity));\\n}\\r\\n.bg-green-50 {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(240 253 244 / var(--tw-bg-opacity));\\n}\\r\\n.bg-indigo-600 {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(79 70 229 / var(--tw-bg-opacity));\\n}\\r\\n.bg-opacity-25 {\\n  --tw-bg-opacity: 0.25;\\n}\\r\\n.bg-cover {\\n  background-size: cover;\\n}\\r\\n.fill-current {\\n  fill: currentColor;\\n}\\r\\n.fill-gray-300 {\\n  fill: #d1d5db;\\n}\\r\\n.fill-skin-primary {\\n  fill: rgb(var(--color-primary));\\n}\\r\\n.fill-white {\\n  fill: #FFFFFF;\\n}\\r\\n.\\\\!p-0 {\\n  padding: 0px !important;\\n}\\r\\n.\\\\!p-1 {\\n  padding: 0.25rem !important;\\n}\\r\\n.\\\\!p-3 {\\n  padding: 0.75rem !important;\\n}\\r\\n.\\\\!p-5 {\\n  padding: 1.25rem !important;\\n}\\r\\n.p-0 {\\n  padding: 0px;\\n}\\r\\n.p-1 {\\n  padding: 0.25rem;\\n}\\r\\n.p-2 {\\n  padding: 0.5rem;\\n}\\r\\n.p-3 {\\n  padding: 0.75rem;\\n}\\r\\n.p-4 {\\n  padding: 1rem;\\n}\\r\\n.p-6 {\\n  padding: 1.5rem;\\n}\\r\\n.p-8 {\\n  padding: 2rem;\\n}\\r\\n.p-\\\\[6px\\\\] {\\n  padding: 6px;\\n}\\r\\n.\\\\!px-1 {\\n  padding-left: 0.25rem !important;\\n  padding-right: 0.25rem !important;\\n}\\r\\n.\\\\!px-3 {\\n  padding-left: 0.75rem !important;\\n  padding-right: 0.75rem !important;\\n}\\r\\n.\\\\!px-4 {\\n  padding-left: 1rem !important;\\n  padding-right: 1rem !important;\\n}\\r\\n.\\\\!py-1 {\\n  padding-top: 0.25rem !important;\\n  padding-bottom: 0.25rem !important;\\n}\\r\\n.\\\\!py-3 {\\n  padding-top: 0.75rem !important;\\n  padding-bottom: 0.75rem !important;\\n}\\r\\n.px-0 {\\n  padding-left: 0px;\\n  padding-right: 0px;\\n}\\r\\n.px-1 {\\n  padding-left: 0.25rem;\\n  padding-right: 0.25rem;\\n}\\r\\n.px-14 {\\n  padding-left: 3.5rem;\\n  padding-right: 3.5rem;\\n}\\r\\n.px-2 {\\n  padding-left: 0.5rem;\\n  padding-right: 0.5rem;\\n}\\r\\n.px-2\\\\.5 {\\n  padding-left: 0.625rem;\\n  padding-right: 0.625rem;\\n}\\r\\n.px-3 {\\n  padding-left: 0.75rem;\\n  padding-right: 0.75rem;\\n}\\r\\n.px-4 {\\n  padding-left: 1rem;\\n  padding-right: 1rem;\\n}\\r\\n.px-5 {\\n  padding-left: 1.25rem;\\n  padding-right: 1.25rem;\\n}\\r\\n.px-6 {\\n  padding-left: 1.5rem;\\n  padding-right: 1.5rem;\\n}\\r\\n.px-8 {\\n  padding-left: 2rem;\\n  padding-right: 2rem;\\n}\\r\\n.px-9 {\\n  padding-left: 2.25rem;\\n  padding-right: 2.25rem;\\n}\\r\\n.py-0 {\\n  padding-top: 0px;\\n  padding-bottom: 0px;\\n}\\r\\n.py-1 {\\n  padding-top: 0.25rem;\\n  padding-bottom: 0.25rem;\\n}\\r\\n.py-2 {\\n  padding-top: 0.5rem;\\n  padding-bottom: 0.5rem;\\n}\\r\\n.py-3 {\\n  padding-top: 0.75rem;\\n  padding-bottom: 0.75rem;\\n}\\r\\n.py-4 {\\n  padding-top: 1rem;\\n  padding-bottom: 1rem;\\n}\\r\\n.py-5 {\\n  padding-top: 1.25rem;\\n  padding-bottom: 1.25rem;\\n}\\r\\n.py-\\\\[5px\\\\] {\\n  padding-top: 5px;\\n  padding-bottom: 5px;\\n}\\r\\n.px-\\\\[9px\\\\] {\\n  padding-left: 9px;\\n  padding-right: 9px;\\n}\\r\\n.\\\\!pb-3 {\\n  padding-bottom: 0.75rem !important;\\n}\\r\\n.pb-0 {\\n  padding-bottom: 0px;\\n}\\r\\n.pb-1 {\\n  padding-bottom: 0.25rem;\\n}\\r\\n.pb-2 {\\n  padding-bottom: 0.5rem;\\n}\\r\\n.pb-20 {\\n  padding-bottom: 5rem;\\n}\\r\\n.pb-3 {\\n  padding-bottom: 0.75rem;\\n}\\r\\n.pb-4 {\\n  padding-bottom: 1rem;\\n}\\r\\n.pb-8 {\\n  padding-bottom: 2rem;\\n}\\r\\n.pe-1 {\\n  padding-inline-end: 0.25rem;\\n}\\r\\n.pe-8 {\\n  padding-inline-end: 2rem;\\n}\\r\\n.pl-0 {\\n  padding-left: 0px;\\n}\\r\\n.pl-1 {\\n  padding-left: 0.25rem;\\n}\\r\\n.pl-10 {\\n  padding-left: 2.5rem;\\n}\\r\\n.pl-2 {\\n  padding-left: 0.5rem;\\n}\\r\\n.pl-3 {\\n  padding-left: 0.75rem;\\n}\\r\\n.pl-4 {\\n  padding-left: 1rem;\\n}\\r\\n.pl-\\\\[2px\\\\] {\\n  padding-left: 2px;\\n}\\r\\n.pl-\\\\[88px\\\\] {\\n  padding-left: 88px;\\n}\\r\\n.pr-2 {\\n  padding-right: 0.5rem;\\n}\\r\\n.pr-3 {\\n  padding-right: 0.75rem;\\n}\\r\\n.pr-4 {\\n  padding-right: 1rem;\\n}\\r\\n.pr-6 {\\n  padding-right: 1.5rem;\\n}\\r\\n.pr-\\\\[10px\\\\] {\\n  padding-right: 10px;\\n}\\r\\n.pr-\\\\[12px\\\\] {\\n  padding-right: 12px;\\n}\\r\\n.pr-\\\\[18px\\\\] {\\n  padding-right: 18px;\\n}\\r\\n.pr-\\\\[45px\\\\] {\\n  padding-right: 45px;\\n}\\r\\n.pr-\\\\[5px\\\\] {\\n  padding-right: 5px;\\n}\\r\\n.pr-\\\\[70px\\\\] {\\n  padding-right: 70px;\\n}\\r\\n.ps-8 {\\n  padding-inline-start: 2rem;\\n}\\r\\n.pt-0 {\\n  padding-top: 0px;\\n}\\r\\n.pt-1 {\\n  padding-top: 0.25rem;\\n}\\r\\n.pt-10 {\\n  padding-top: 2.5rem;\\n}\\r\\n.pt-2 {\\n  padding-top: 0.5rem;\\n}\\r\\n.pt-3 {\\n  padding-top: 0.75rem;\\n}\\r\\n.pt-4 {\\n  padding-top: 1rem;\\n}\\r\\n.pt-5 {\\n  padding-top: 1.25rem;\\n}\\r\\n.pt-6 {\\n  padding-top: 1.5rem;\\n}\\r\\n.pt-\\\\[0\\\\.4rem\\\\] {\\n  padding-top: 0.4rem;\\n}\\r\\n.pt-\\\\[80px\\\\] {\\n  padding-top: 80px;\\n}\\r\\n.pl-12 {\\n  padding-left: 3rem;\\n}\\r\\n.pr-12 {\\n  padding-right: 3rem;\\n}\\r\\n.\\\\!text-left {\\n  text-align: left !important;\\n}\\r\\n.text-left {\\n  text-align: left;\\n}\\r\\n.\\\\!text-center {\\n  text-align: center !important;\\n}\\r\\n.text-center {\\n  text-align: center;\\n}\\r\\n.text-right {\\n  text-align: right;\\n}\\r\\n.align-top {\\n  vertical-align: top;\\n}\\r\\n.align-middle {\\n  vertical-align: middle;\\n}\\r\\n.font-poppinsregular {\\n  font-family: poppinsregular;\\n}\\r\\n.font-poppinssemibold {\\n  font-family: poppinssemibold;\\n}\\r\\n.\\\\!text-base {\\n  font-size: 1rem !important;\\n  line-height: 1.5rem !important;\\n}\\r\\n.text-2xl {\\n  font-size: 1.5rem;\\n  line-height: 2rem;\\n}\\r\\n.text-4xl {\\n  font-size: 2.25rem;\\n  line-height: 2.5rem;\\n}\\r\\n.text-\\\\[10px\\\\] {\\n  font-size: 10px;\\n}\\r\\n.text-\\\\[16px\\\\] {\\n  font-size: 16px;\\n}\\r\\n.text-\\\\[20px\\\\] {\\n  font-size: 20px;\\n}\\r\\n.text-base {\\n  font-size: 1rem;\\n  line-height: 1.5rem;\\n}\\r\\n.text-lg {\\n  font-size: 1.125rem;\\n  line-height: 1.75rem;\\n}\\r\\n.text-sm {\\n  font-size: 0.875rem;\\n  line-height: 1.25rem;\\n}\\r\\n.text-xl {\\n  font-size: 1.25rem;\\n  line-height: 1.75rem;\\n}\\r\\n.text-xs {\\n  font-size: 0.75rem;\\n  line-height: 1rem;\\n}\\r\\n.text-3xl {\\n  font-size: 1.875rem;\\n  line-height: 2.25rem;\\n}\\r\\n.\\\\!font-medium {\\n  font-weight: 500 !important;\\n}\\r\\n.\\\\!font-normal {\\n  font-weight: 400 !important;\\n}\\r\\n.font-bold {\\n  font-weight: 700;\\n}\\r\\n.font-medium {\\n  font-weight: 500;\\n}\\r\\n.font-normal {\\n  font-weight: 400;\\n}\\r\\n.font-semibold {\\n  font-weight: 600;\\n}\\r\\n.font-extrabold {\\n  font-weight: 800;\\n}\\r\\n.uppercase {\\n  text-transform: uppercase;\\n}\\r\\n.capitalize {\\n  text-transform: capitalize;\\n}\\r\\n.leading-5 {\\n  line-height: 1.25rem;\\n}\\r\\n.leading-8 {\\n  line-height: 2rem;\\n}\\r\\n.leading-\\\\[30px\\\\] {\\n  line-height: 30px;\\n}\\r\\n.leading-relaxed {\\n  line-height: 1.625;\\n}\\r\\n.tracking-tight {\\n  letter-spacing: -0.025em;\\n}\\r\\n.tracking-wider {\\n  letter-spacing: 0.05em;\\n}\\r\\n.tracking-wide {\\n  letter-spacing: 0.025em;\\n}\\r\\n.\\\\!text-\\\\[\\\\#333333\\\\] {\\n  --tw-text-opacity: 1 !important;\\n  color: rgb(51 51 51 / var(--tw-text-opacity)) !important;\\n}\\r\\n.\\\\!text-gray-300 {\\n  --tw-text-opacity: 1 !important;\\n  color: rgb(209 213 219 / var(--tw-text-opacity)) !important;\\n}\\r\\n.\\\\!text-gray-500 {\\n  --tw-text-opacity: 1 !important;\\n  color: rgb(107 114 128 / var(--tw-text-opacity)) !important;\\n}\\r\\n.\\\\!text-gray-700 {\\n  --tw-text-opacity: 1 !important;\\n  color: rgb(55 65 81 / var(--tw-text-opacity)) !important;\\n}\\r\\n.\\\\!text-red-500 {\\n  --tw-text-opacity: 1 !important;\\n  color: rgb(239 68 68 / var(--tw-text-opacity)) !important;\\n}\\r\\n.\\\\!text-skin-primary {\\n  --tw-text-opacity: 1 !important;\\n  color: rgba(var(--color-primary), var(--tw-text-opacity)) !important;\\n}\\r\\n.text-\\\\[\\\\#B31312\\\\] {\\n  --tw-text-opacity: 1;\\n  color: rgb(179 19 18 / var(--tw-text-opacity));\\n}\\r\\n.text-\\\\[\\\\#FBB522\\\\] {\\n  --tw-text-opacity: 1;\\n  color: rgb(251 181 34 / var(--tw-text-opacity));\\n}\\r\\n.text-\\\\[\\\\#ffffff\\\\] {\\n  --tw-text-opacity: 1;\\n  color: rgb(255 255 255 / var(--tw-text-opacity));\\n}\\r\\n.text-black {\\n  --tw-text-opacity: 1;\\n  color: rgb(0 0 0 / var(--tw-text-opacity));\\n}\\r\\n.text-blue-500 {\\n  --tw-text-opacity: 1;\\n  color: rgb(59 130 246 / var(--tw-text-opacity));\\n}\\r\\n.text-blue-600 {\\n  --tw-text-opacity: 1;\\n  color: rgb(37 99 235 / var(--tw-text-opacity));\\n}\\r\\n.text-bright-green {\\n  --tw-text-opacity: 1;\\n  color: rgb(62 171 88 / var(--tw-text-opacity));\\n}\\r\\n.text-bright-red {\\n  --tw-text-opacity: 1;\\n  color: rgb(249 54 71 / var(--tw-text-opacity));\\n}\\r\\n.text-bright-yellow {\\n  --tw-text-opacity: 1;\\n  color: rgb(251 181 34 / var(--tw-text-opacity));\\n}\\r\\n.text-dark-gray {\\n  --tw-text-opacity: 1;\\n  color: rgb(114 114 114 / var(--tw-text-opacity));\\n}\\r\\n.text-gray-300 {\\n  --tw-text-opacity: 1;\\n  color: rgb(209 213 219 / var(--tw-text-opacity));\\n}\\r\\n.text-gray-400 {\\n  --tw-text-opacity: 1;\\n  color: rgb(156 163 175 / var(--tw-text-opacity));\\n}\\r\\n.text-gray-500 {\\n  --tw-text-opacity: 1;\\n  color: rgb(107 114 128 / var(--tw-text-opacity));\\n}\\r\\n.text-gray-600 {\\n  --tw-text-opacity: 1;\\n  color: rgb(75 85 99 / var(--tw-text-opacity));\\n}\\r\\n.text-gray-800 {\\n  --tw-text-opacity: 1;\\n  color: rgb(31 41 55 / var(--tw-text-opacity));\\n}\\r\\n.text-gray-900 {\\n  --tw-text-opacity: 1;\\n  color: rgb(17 24 39 / var(--tw-text-opacity));\\n}\\r\\n.text-issue-status {\\n  --tw-text-opacity: 1;\\n  color: rgb(251 70 70 / var(--tw-text-opacity));\\n}\\r\\n.text-light-gray2 {\\n  --tw-text-opacity: 1;\\n  color: rgb(176 176 176 / var(--tw-text-opacity));\\n}\\r\\n.text-lighter-gray {\\n  --tw-text-opacity: 1;\\n  color: rgb(175 175 175 / var(--tw-text-opacity));\\n}\\r\\n.text-red-500 {\\n  --tw-text-opacity: 1;\\n  color: rgb(239 68 68 / var(--tw-text-opacity));\\n}\\r\\n.text-red-600 {\\n  --tw-text-opacity: 1;\\n  color: rgb(220 38 38 / var(--tw-text-opacity));\\n}\\r\\n.text-skin-a11y {\\n  --tw-text-opacity: 1;\\n  color: rgba(var(--color-a11y), var(--tw-text-opacity));\\n}\\r\\n.text-skin-primary {\\n  --tw-text-opacity: 1;\\n  color: rgba(var(--color-primary), var(--tw-text-opacity));\\n}\\r\\n.text-theme-blue2 {\\n  --tw-text-opacity: 1;\\n  color: rgb(0 102 255 / var(--tw-text-opacity));\\n}\\r\\n.text-white {\\n  --tw-text-opacity: 1;\\n  color: rgb(255 255 255 / var(--tw-text-opacity));\\n}\\r\\n.text-gray-700 {\\n  --tw-text-opacity: 1;\\n  color: rgb(55 65 81 / var(--tw-text-opacity));\\n}\\r\\n.text-blue-700 {\\n  --tw-text-opacity: 1;\\n  color: rgb(29 78 216 / var(--tw-text-opacity));\\n}\\r\\n.text-blue-800 {\\n  --tw-text-opacity: 1;\\n  color: rgb(30 64 175 / var(--tw-text-opacity));\\n}\\r\\n.text-green-600 {\\n  --tw-text-opacity: 1;\\n  color: rgb(22 163 74 / var(--tw-text-opacity));\\n}\\r\\n.text-green-800 {\\n  --tw-text-opacity: 1;\\n  color: rgb(22 101 52 / var(--tw-text-opacity));\\n}\\r\\n.underline {\\n  text-decoration-line: underline;\\n}\\r\\n.placeholder-gray-400::-moz-placeholder {\\n  --tw-placeholder-opacity: 1;\\n  color: rgb(156 163 175 / var(--tw-placeholder-opacity));\\n}\\r\\n.placeholder-gray-400::placeholder {\\n  --tw-placeholder-opacity: 1;\\n  color: rgb(156 163 175 / var(--tw-placeholder-opacity));\\n}\\r\\n.accent-skin-primary {\\n  accent-color: rgb(var(--color-primary));\\n}\\r\\n.opacity-0 {\\n  opacity: 0;\\n}\\r\\n.opacity-100 {\\n  opacity: 1;\\n}\\r\\n.opacity-50 {\\n  opacity: 0.5;\\n}\\r\\n.opacity-70 {\\n  opacity: 0.7;\\n}\\r\\n.\\\\!shadow-lg {\\n  --tw-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1) !important;\\n  --tw-shadow-colored: 0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -4px var(--tw-shadow-color) !important;\\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow) !important;\\n}\\r\\n.shadow {\\n  --tw-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);\\n  --tw-shadow-colored: 0 1px 3px 0 var(--tw-shadow-color), 0 1px 2px -1px var(--tw-shadow-color);\\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\\n}\\r\\n.shadow-lg {\\n  --tw-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);\\n  --tw-shadow-colored: 0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -4px var(--tw-shadow-color);\\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\\n}\\r\\n.shadow-sm {\\n  --tw-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05);\\n  --tw-shadow-colored: 0 1px 2px 0 var(--tw-shadow-color);\\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\\n}\\r\\n.shadow-xl {\\n  --tw-shadow: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);\\n  --tw-shadow-colored: 0 20px 25px -5px var(--tw-shadow-color), 0 8px 10px -6px var(--tw-shadow-color);\\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\\n}\\r\\n.shadow-md {\\n  --tw-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);\\n  --tw-shadow-colored: 0 4px 6px -1px var(--tw-shadow-color), 0 2px 4px -2px var(--tw-shadow-color);\\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\\n}\\r\\n.outline {\\n  outline-style: solid;\\n}\\r\\n.blur {\\n  --tw-blur: blur(8px);\\n  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);\\n}\\r\\n.filter {\\n  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);\\n}\\r\\n.backdrop-blur-none {\\n  --tw-backdrop-blur: blur(0);\\n  -webkit-backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);\\n          backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);\\n}\\r\\n.backdrop-blur-sm {\\n  --tw-backdrop-blur: blur(4px);\\n  -webkit-backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);\\n          backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);\\n}\\r\\n.transition {\\n  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, -webkit-backdrop-filter;\\n  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter;\\n  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter, -webkit-backdrop-filter;\\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\\n  transition-duration: 150ms;\\n}\\r\\n.transition-all {\\n  transition-property: all;\\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\\n  transition-duration: 150ms;\\n}\\r\\n.duration-150 {\\n  transition-duration: 150ms;\\n}\\r\\n.duration-200 {\\n  transition-duration: 200ms;\\n}\\r\\n.duration-300 {\\n  transition-duration: 300ms;\\n}\\r\\n.ease-in {\\n  transition-timing-function: cubic-bezier(0.4, 0, 1, 1);\\n}\\r\\n.ease-in-out {\\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\\n}\\r\\n.ease-out {\\n  transition-timing-function: cubic-bezier(0, 0, 0.2, 1);\\n}\\r\\n/*@import \\\"bootstrap/bootstrap\\\";*/\\r\\n\\r\\n@font-face {\\r\\n  font-family: poppinsblack;\\r\\n  src: url(\" + ___CSS_LOADER_URL_REPLACEMENT_0___ + \");\\r\\n}\\r\\n@font-face {\\r\\n  font-family: poppinsblackitalic;\\r\\n  src: url(\" + ___CSS_LOADER_URL_REPLACEMENT_1___ + \");\\r\\n}\\r\\n@font-face {\\r\\n  font-family: poppinsbold;\\r\\n  src: url(\" + ___CSS_LOADER_URL_REPLACEMENT_2___ + \");\\r\\n}\\r\\n@font-face {\\r\\n  font-family: poppinsbolditalic;\\r\\n  src: url(\" + ___CSS_LOADER_URL_REPLACEMENT_3___ + \");\\r\\n}\\r\\n@font-face {\\r\\n  font-family: poppinsextrabold;\\r\\n  src: url(\" + ___CSS_LOADER_URL_REPLACEMENT_4___ + \");\\r\\n}\\r\\n@font-face {\\r\\n  font-family: poppinsextrabolditalic;\\r\\n  src: url(\" + ___CSS_LOADER_URL_REPLACEMENT_5___ + \");\\r\\n}\\r\\n@font-face {\\r\\n  font-family: poppinsextralight;\\r\\n  src: url(\" + ___CSS_LOADER_URL_REPLACEMENT_6___ + \");\\r\\n}\\r\\n@font-face {\\r\\n  font-family: poppinsextralightitalic;\\r\\n  src: url(\" + ___CSS_LOADER_URL_REPLACEMENT_7___ + \");\\r\\n}\\r\\n@font-face {\\r\\n  font-family: poppinsitalic;\\r\\n  src: url(\" + ___CSS_LOADER_URL_REPLACEMENT_8___ + \");\\r\\n}\\r\\n@font-face {\\r\\n  font-family: poppinslight;\\r\\n  src: url(\" + ___CSS_LOADER_URL_REPLACEMENT_9___ + \");\\r\\n}\\r\\n@font-face {\\r\\n  font-family: poppinslightitalic;\\r\\n  src: url(\" + ___CSS_LOADER_URL_REPLACEMENT_10___ + \");\\r\\n}\\r\\n@font-face {\\r\\n  font-family: poppinsmedium;\\r\\n  src: url(\" + ___CSS_LOADER_URL_REPLACEMENT_11___ + \");\\r\\n}\\r\\n@font-face {\\r\\n  font-family: poppinsmediumitalic;\\r\\n  src: url(\" + ___CSS_LOADER_URL_REPLACEMENT_12___ + \");\\r\\n}\\r\\n@font-face {\\r\\n  font-family: poppinsregular;\\r\\n  src: url(\" + ___CSS_LOADER_URL_REPLACEMENT_13___ + \");\\r\\n}\\r\\n@font-face {\\r\\n  font-family: poppinssemibold;\\r\\n  src: url(\" + ___CSS_LOADER_URL_REPLACEMENT_14___ + \");\\r\\n}\\r\\n@font-face {\\r\\n  font-family: poppinssemibolditalic;\\r\\n  src: url(\" + ___CSS_LOADER_URL_REPLACEMENT_15___ + \");\\r\\n}\\r\\n@font-face {\\r\\n  font-family: poppinsitalic;\\r\\n  src: url(\" + ___CSS_LOADER_URL_REPLACEMENT_8___ + \");\\r\\n}\\r\\n@font-face {\\r\\n  font-family: poppinsitalic;\\r\\n  src: url(\" + ___CSS_LOADER_URL_REPLACEMENT_8___ + \");\\r\\n}\\r\\n\\r\\nbody,\\r\\nhtml {\\r\\n  height: 100%;\\r\\n  background-repeat: repeat;\\r\\n  font-family: \\\"poppinsregular\\\";\\r\\n  overflow-x: hidden;\\r\\n  background-color: #f3f8ff;\\r\\n  font-size: 12px;\\r\\n}\\r\\n\\r\\n.dark body,\\r\\nhtml {\\r\\n  height: 100%;\\r\\n  background-repeat: repeat;\\r\\n  font-family: \\\"poppinsregular\\\" !important;\\r\\n  overflow-x: hidden;\\r\\n  background-color: #0e0e10;\\r\\n  font-size: 14px;\\r\\n}\\r\\n\\r\\n.ag-header .ag-header-cell.header-with-border  {\\r\\n  border-bottom: 1px solid #ccc;\\r\\n}\\r\\n\\r\\n.ag-header-cell-text {\\r\\n  font-size: 12px;\\r\\n  color: #333333;\\r\\n  font-family: \\\"poppinsmedium\\\";\\r\\n}\\r\\n\\r\\nbutton[disabled] {\\r\\n  opacity: 0.5;\\r\\n  cursor: not-allowed;\\r\\n}\\r\\n\\r\\n.dark .ag-header-cell-text {\\r\\n  font-size: 12px;\\r\\n  color: #fff;\\r\\n  font-family: \\\"poppinsmedium\\\";\\r\\n}\\r\\n\\r\\n.ag-ltr .ag-cell {\\r\\n  color: #333333;\\r\\n  font-family: \\\"poppinsregular\\\";\\r\\n  font-size: 12px;\\r\\n}\\r\\n\\r\\n.dark .ag-ltr .ag-cell {\\r\\n  color: white;\\r\\n  font-family: \\\"poppinsregular\\\";\\r\\n  font-size: 12px;\\r\\n  background-color: #1d212d;\\r\\n}\\r\\n\\r\\n.ag-paging-row-summary-panel-number {\\r\\n  font-family: \\\"poppinsregular\\\";\\r\\n  font-size: 12px;\\r\\n}\\r\\n.dark .ag-paging-row-summary-panel-number {\\r\\n  font-family: \\\"poppinsregular\\\";\\r\\n  font-size: 12px;\\r\\n  color: white;\\r\\n}\\r\\n\\r\\n.ag-paging-page-summary-panel {\\r\\n  font-family: \\\"poppinsregular\\\";\\r\\n  font-size: 12px;\\r\\n}\\r\\n.dark .ag-paging-page-summary-panel {\\r\\n  font-family: \\\"poppinsregular\\\";\\r\\n  font-size: 12px;\\r\\n  color: white;\\r\\n}\\r\\n\\r\\n.pagination {\\r\\n  font-family: \\\"poppinsregular\\\";\\r\\n  font-size: 12px;\\r\\n}\\r\\n.dark .pagination {\\r\\n  font-family: \\\"poppinsregular\\\";\\r\\n  font-size: 12px;\\r\\n  color: white;\\r\\n}\\r\\n\\r\\n.ag-paging-panel {\\r\\n  justify-content: between;\\r\\n}\\r\\n.dark .ag-paging-panel {\\r\\n  justify-content: between;\\r\\n  color: white;\\r\\n  background-color: #1d212d;\\r\\n}\\r\\n\\r\\n.dark .ag-center-cols-viewport {\\r\\n  background-color: #1d212d;\\r\\n}\\r\\n\\r\\n.contentsectionbg {\\r\\n  background-color: white;\\r\\n}\\r\\n\\r\\n.dark .contentsectionbg {\\r\\n  background-color: #1d212d;\\r\\n  font-family: \\\"poppinsregular\\\";\\r\\n}\\r\\n\\r\\n.dark\\r\\n  .ag-header.ag-header-allow-overflow\\r\\n  .ag-header-row\\r\\n  .ag-root-wrapper.ag-layout-normal {\\r\\n  background-color: #1d212d;\\r\\n  font-family: \\\"poppinsregular\\\";\\r\\n}\\r\\n\\r\\n.dark .ag-header-container {\\r\\n  background-color: #1d212d;\\r\\n  font-family: \\\"poppinsregular\\\";\\r\\n}\\r\\n\\r\\n.dark .pagination-style {\\r\\n  color: white;\\r\\n  font-family: \\\"poppinsregular\\\";\\r\\n}\\r\\n.dark .ag-paging-button,\\r\\n.ag-paging-description {\\r\\n  font-family: \\\"poppinsregular\\\";\\r\\n  color: white;\\r\\n}\\r\\n.ag-paging-button,\\r\\n.ag-paging-description {\\r\\n  font-family: \\\"poppinsregular\\\";\\r\\n  color: #000;\\r\\n}\\r\\n.dark .ag-paging-button.ag-disabled {\\r\\n  font-family: \\\"poppinsregular\\\";\\r\\n  color: white;\\r\\n}\\r\\n\\r\\n.ag-body-horizontal-scroll-viewport {\\r\\n  display: none;\\r\\n} \\r\\n\\r\\n\\r\\n.ag-overlay-no-rows-center {\\r\\n  font-family: \\\"poppinsregular\\\";\\r\\n  font-size: 12px;\\r\\n}\\r\\n\\r\\nnav.sidebar .navbar-nav .open .dropdown-menu > li > a:hover,\\r\\nnav.sidebar .navbar-nav .open .dropdown-menu > li > a:focus {\\r\\n  color: #ccc;\\r\\n  background-color: transparent;\\r\\n}\\r\\n\\r\\nnav:hover .forAnimate {\\r\\n  opacity: 1;\\r\\n}\\r\\n\\r\\n.mainmenu {\\r\\n  background: #002d73;\\r\\n  border: #002d73;\\r\\n  border-radius: 0px;\\r\\n}\\r\\n\\r\\n.mainmenu .navbar-nav > .active > a,\\r\\n.mainmenu .navbar-nav > .active > a:hover,\\r\\n.mainmenu .navbar-nav > .active > a:focus {\\r\\n  color: #fff;\\r\\n  background-color: #2a3344;\\r\\n  border-left: 2px solid #1ca9c0;\\r\\n  padding-top: 15px;\\r\\n  padding-bottom: 15px;\\r\\n  display: flex;\\r\\n  flex-direction: column;\\r\\n  justify-content: center;\\r\\n}\\r\\n\\r\\n.mainmenu .navbar-nav > li > a:hover,\\r\\n.mainmenu .navbar-nav > li > a:focus {\\r\\n  color: #fff;\\r\\n  background-color: lightgray;\\r\\n  padding-top: 15px;\\r\\n  padding-bottom: 15px;\\r\\n}\\r\\n\\r\\n.navbar-default .navbar-nav > li > a {\\r\\n  color: #00b1a3;\\r\\n  padding-top: 15px;\\r\\n  padding-bottom: 15px;\\r\\n  display: flex;\\r\\n  flex-direction: column;\\r\\n  justify-content: center;\\r\\n  text-decoration: none;\\r\\n}\\r\\n\\r\\n.navbar {\\r\\n  min-height: 45px;\\r\\n}\\r\\n\\r\\n.page-heading {\\r\\n  color: #333333;\\r\\n  font-size: 14px;\\r\\n  margin-top: 0px !important;\\r\\n  margin-left: 25px;\\r\\n  /* position: absolute; */\\r\\n  top: 10px;\\r\\n  left: 90px;\\r\\n  font-weight: 600;\\r\\n}\\r\\n\\r\\n.page-heading h2 {\\r\\n  font-size: 16px;\\r\\n  margin-top: 15px !important;\\r\\n}\\r\\n\\r\\n/* ----------- Notification dropdown end ----------------------- */\\r\\n\\r\\nnav.sidebar .brand a {\\r\\n  padding: 0;\\r\\n}\\r\\n\\r\\n.brand {\\r\\n  font-size: 24px;\\r\\n  /* padding: 0px 5px; */\\r\\n  color: #fff;\\r\\n  width: 69px;\\r\\n  /* background-color: #002d73; */\\r\\n  text-align: center;\\r\\n  display: flex;\\r\\n  justify-content: center;\\r\\n  align-items: center;\\r\\n}\\r\\n\\r\\n.titlebar {\\r\\n  width: 100%;\\r\\n  position: fixed;\\r\\n  /* height: 55px; */\\r\\n  z-index: 98;\\r\\n  top: 0;\\r\\n  padding-left: 69px;\\r\\n}\\r\\n\\r\\n.accordion {\\r\\n  cursor: pointer;\\r\\n  width: 100%;\\r\\n  text-align: left;\\r\\n  outline: none;\\r\\n  transition: 0.4s;\\r\\n}\\r\\n\\r\\n.active,\\r\\n.accordion:hover {\\r\\n  background-color: transparent;\\r\\n}\\r\\n\\r\\n.panel {\\r\\n  background-color: white;\\r\\n  max-height: 0;\\r\\n  overflow: hidden;\\r\\n  transition: max-height 0.2s ease-out;\\r\\n}\\r\\n\\r\\n/* .reactSelectCustom .css-1fdsijx-ValueContainer , .reactSelectCustom .css-b62m3t-ValueContainer {\\r\\n  position: relative;\\r\\n  top: -5px;\\r\\n}\\r\\n\\r\\n.reactSelectCustom .css-1hb7zxy-IndicatorsContainer, .reactSelectCustom .css-1xc3v61-IndicatorsContainer{\\r\\n  position: relative;\\r\\n  top: -5px;\\r\\n} */\\r\\n/* .reactSelectCustom .css-1jgx7bw-control{\\r\\n  flex-wrap: nowrap !important;\\r\\n} */\\r\\ninput[type=date]:invalid::-ms-datetime-edit {\\r\\n  color: #808080;\\r\\n}\\r\\n/* label {\\r\\n  font-size: 14px;\\r\\n  color: #505050;\\r\\n} */\\r\\n.top-navbar {\\r\\n  background-color: white;\\r\\n}\\r\\n.dark .top-navbar {\\r\\n  background-color: #000;\\r\\n  color: white;\\r\\n}\\r\\n\\r\\n.dark .pageName {\\r\\n  color: white;\\r\\n}\\r\\n\\r\\ninput[type=\\\"number\\\"] {\\r\\n  -webkit-appearance: textfield;\\r\\n     -moz-appearance: textfield;\\r\\n          appearance: textfield;\\r\\n}\\r\\ninput[type=number]::-webkit-inner-spin-button, \\r\\ninput[type=number]::-webkit-outer-spin-button { \\r\\n  -webkit-appearance: none;\\r\\n}\\r\\n\\r\\ninput[type=\\\"text\\\"],\\r\\ninput[type=\\\"tel\\\"],\\r\\ninput[type=\\\"date\\\"],\\r\\ninput[type=\\\"email\\\"],\\r\\ninput[type=\\\"number\\\"],\\r\\nselect {\\r\\n  padding-top: 4px;\\r\\n  padding-bottom: 4px;\\r\\n  border-color: #d6d6d6;\\r\\n  outline: 2px solid #ffffff;\\r\\n  color: #333333;\\r\\n  font-family: \\\"poppinsregular\\\";\\r\\n}\\r\\n\\r\\ninput:focus, select:focus , textarea:focus{\\r\\n  outline-color: #0066ff;\\r\\n}\\r\\n\\r\\n.button {\\r\\n  padding: 4px 12px 4px 12px;\\r\\n  font-family: \\\"poppinsmedium\\\";\\r\\n}\\r\\n\\r\\n.labels {\\r\\n  font-family: \\\"poppinsregular\\\";\\r\\n  font-size: 12px;\\r\\n  color: #505050;\\r\\n}\\r\\n.dark .labels {\\r\\n  font-family: \\\"poppinsregular\\\";\\r\\n  font-size: 12px;\\r\\n  color: white;\\r\\n}\\r\\n\\r\\n.searchbar {\\r\\n  font-family: \\\"poppinsregular\\\";\\r\\n  font-size: 12px;\\r\\n  color: #AFAFAF;\\r\\n  background-color: #FFFFFF;\\r\\n}\\r\\n\\r\\n.dark .searchbar {\\r\\n  font-family: \\\"poppinsregular\\\";\\r\\n  font-size: 12px;\\r\\n  color: #c0c0c0;\\r\\n  background-color: #1d212d;\\r\\n  outline-color: #1d212d;\\r\\n}\\r\\n\\r\\n.checkboxbg {\\r\\n  background-color: #D9D9D9;\\r\\n}\\r\\n.dark .checkboxbg {\\r\\n  background-color: #4d4d4d;\\r\\n}\\r\\n\\r\\n.dark select {\\r\\n  background-color: #1d1d1d;\\r\\n  color: white;\\r\\n}\\r\\n\\r\\n.borderc-theme-blue2 {\\r\\n  border-color: #0066ff;\\r\\n}\\r\\n.dark .borderc-theme-blue2 {\\r\\n  border-color: #6699ff;\\r\\n}\\r\\n.textc-theme-blue2 {\\r\\n  color: #0066ff;\\r\\n}\\r\\n.dark .textc-theme-blue2 {\\r\\n  color: #6699ff;\\r\\n}\\r\\n\\r\\n.bgc-theme-blue2 {\\r\\n  background-color: #0066ff;\\r\\n}\\r\\n.dark .bgc-theme-blue2 {\\r\\n  background-color: #6699ff;\\r\\n}\\r\\n.textc-dark-gray {\\r\\n  color: #727272;\\r\\n}\\r\\n.dark .textc-dark-gray {\\r\\n  color: #a3a3a3;\\r\\n}\\r\\n.borderc-dark-gray {\\r\\n  color: #727272;\\r\\n}\\r\\n.dark .borderc-dark-gray {\\r\\n  color: #a3a3a3;\\r\\n}\\r\\n\\r\\n.input {\\r\\n  font-size: 12px;\\r\\n  font-family: \\\"poppinsregular\\\";\\r\\n}\\r\\n\\r\\n.dark .inputs {\\r\\n  font-family: \\\"poppinsregular\\\";\\r\\n  font-size: 12px;\\r\\n  color: white;\\r\\n}\\r\\n\\r\\n.confirmInputs {\\r\\n    font-size: 12px;\\r\\n    color: #333333;\\r\\n    font-family: \\\"poppinsmedium\\\";\\r\\n}\\r\\n\\r\\n.formtitle {\\r\\n  font-family: \\\"poppinssemibold\\\";\\r\\n  color: #333333;\\r\\n  font-size: 12px;\\r\\n}\\r\\n\\r\\n.subtitles {\\r\\n  font-family: \\\"poppinssemibold\\\";\\r\\n  color: #333333;\\r\\n  font-size: 14px;\\r\\n}\\r\\n\\r\\n.text-blackcolor {\\r\\n  color:#505050;\\r\\n  font-size: 12px;\\r\\n  font-family: \\\"poppinsregular\\\";\\r\\n}\\r\\n\\r\\n.ag-ltr .ag-cell-focus:not(.ag-cell-range-selected):focus-within {\\r\\n  border: none !important;\\r\\n}\\r\\n\\r\\n.ag-row-odd{\\r\\n  background-color: #f3f3f3 !important;\\r\\n}\\r\\n\\r\\n.ag-cell-value span {\\r\\n  height: auto !important;\\r\\n  line-height: normal !important;\\r\\n}\\r\\n\\r\\n.ag-cell{\\r\\n  display:flex;\\r\\n  align-items:center;\\r\\n  border: none !important;\\r\\n}\\r\\n\\r\\n.ag-cell-value, .ag-header-cell-text{\\r\\n  text-overflow: unset !important;\\r\\n}\\r\\n\\r\\n.pagination-style {\\r\\n  position: absolute;\\r\\n  bottom: 10px;\\r\\n  left: 20px;\\r\\n}\\r\\n\\r\\n.pagination-style select {\\r\\n  background-color: #f2f2f2;\\r\\n  border-radius: 3px;\\r\\n}\\r\\n\\r\\n.ag-ltr .ag-header-select-all{\\r\\n  margin-right: 12px !important; \\r\\n}\\r\\n\\r\\n.ag-root-wrapper.ag-layout-normal{\\r\\n  border-radius: 5px !important;\\r\\n}\\r\\n\\r\\n.distribution-point .ag-header-container {\\r\\n    /* width: 100% !important; */\\r\\n}\\r\\n\\r\\n.distribution-point .ag-header-row.ag-header-row-column {\\r\\n    /* width: 100% !important; */\\r\\n}\\r\\n\\r\\n.distribution-point .ag-center-cols-container {\\r\\n    /* width: 100% !important; */\\r\\n}\\r\\n\\r\\n.general_section .ag-header-container, .general_section .ag-center-cols-container, .general_section .ag-header-row  {\\r\\n  width: 100% !important;\\r\\n}\\r\\n\\r\\n.general_section .ag-cell {\\r\\n  /* display: flex; */\\r\\n  margin-right: 10px;\\r\\n}\\r\\n\\r\\n.product_link_def .ag-row .ag-cell {\\r\\n  display: flex;\\r\\n  align-items: center;\\r\\n}\\r\\n\\r\\n.product_data_def .ag-cell {\\r\\n  display: flex;\\r\\n  align-items: center;\\r\\n}\\r\\n\\r\\n/* @media (min-width: 640px) {\\r\\n  .ag-header-cell-text,.dark .ag-header-cell-text,.ag-ltr .ag-cell,.dark .ag-ltr .ag-cell,.ag-paging-row-summary-panel-number,.dark .ag-paging-row-summary-panel-number,.ag-paging-page-summary-panel,.dark .ag-paging-page-summary-panel,.pagination,.dark .pagination,.labels,.dark .labels,.searchbar,.dark .searchbar,.inputs,.dark .inputs,.formtitle{\\r\\n    font-size: 10px;\\r\\n  }\\r\\n} */\\r\\n\\r\\n.viewlog .ag-cell-value {\\r\\n    align-items: center;\\r\\n    display: flex;\\r\\n}\\r\\n\\r\\n@media (min-width: 765px) {\\r\\n  .main {\\r\\n    position: absolute;\\r\\n    width: calc(100% - 40px);\\r\\n    margin-left: 40px;\\r\\n    float: right;\\r\\n  }\\r\\n  nav.sidebar:hover + .main {\\r\\n    margin-left: 200px;\\r\\n  }\\r\\n  nav.sidebar.navbar.sidebar > .container .navbar-brand,\\r\\n  .navbar > .container-fluid .navbar-brand {\\r\\n    margin-left: 0px;\\r\\n  }\\r\\n  nav.sidebar .navbar-brand,\\r\\n  nav.sidebar .navbar-header {\\r\\n    text-align: center;\\r\\n    width: 100%;\\r\\n    margin-left: 0px;\\r\\n  }\\r\\n  /* nav.sidebar a {\\r\\n    padding-bottom: 34px;\\r\\n  } */\\r\\n\\r\\n  nav.sidebar .navbar-nav > li {\\r\\n    font-size: 13px;\\r\\n  }\\r\\n  nav.sidebar .navbar-nav .open .dropdown-menu {\\r\\n    position: static;\\r\\n    float: none;\\r\\n    width: auto;\\r\\n    margin-top: 0;\\r\\n    background-color: transparent;\\r\\n    border: 0;\\r\\n    box-shadow: none;\\r\\n  }\\r\\n  nav.sidebar .navbar-collapse,\\r\\n  nav.sidebar .container-fluid {\\r\\n    padding: 0 0px 0 0px;\\r\\n  }\\r\\n  .navbar-inverse .navbar-nav .open .dropdown-menu > li > a {\\r\\n    color: #777;\\r\\n  }\\r\\n  nav.sidebar {\\r\\n    width: 69px;\\r\\n    height: 100%;\\r\\n    margin-bottom: 0px;\\r\\n    position: fixed;\\r\\n    top: 0px;\\r\\n    display: flex;\\r\\n    align-items: flex-start;\\r\\n  }\\r\\n  nav.sidebar li {\\r\\n    width: 100%;\\r\\n  }\\r\\n\\r\\n  .forAnimate {\\r\\n    opacity: 0;\\r\\n  }\\r\\n}\\r\\n\\r\\n\\r\\n@media (min-width: 1024px) {\\r\\n  \\r\\n}\\r\\n\\r\\n/* @media (min-width: 1280px) {\\r\\n  .ag-header-cell-text,.dark .ag-header-cell-text,.ag-ltr .ag-cell,.dark .ag-ltr .ag-cell,.ag-paging-row-summary-panel-number,.dark .ag-paging-row-summary-panel-number,.ag-paging-page-summary-panel,.dark .ag-paging-page-summary-panel,.pagination,.dark .pagination,.labels,.dark .labels,.searchbar,.dark .searchbar,.inputs,.dark .inputs,.formtitle{\\r\\n    font-size: 14px;\\r\\n  }}\\r\\n\\r\\n@media (min-width: 1536px) {\\r\\n  \\r\\n}\\r\\n@media (min-width: 1940px) {\\r\\n  \\r\\n} */\\r\\n\\r\\n.desktop-view-message {\\r\\n  position: fixed;\\r\\n  top: 0;\\r\\n  left: 0;\\r\\n  width: 100%;\\r\\n  height: 100%;\\r\\n  background-color: rgba(0, 0, 0, 0.5); /* Semi-transparent black */\\r\\n  display: flex;\\r\\n  justify-content: center;\\r\\n  align-items: center;\\r\\n  z-index: 9999; /* Ensure it's on top of other content */\\r\\n}\\r\\n.block-view-message {\\r\\n  position: fixed;\\r\\n  top: 0;\\r\\n  left: 0;\\r\\n  width: 100%;\\r\\n  height: 100%;\\r\\n  background-color: rgba(214, 21, 21, 0.5); /* Semi-transparent black */\\r\\n  display: flex;\\r\\n  justify-content: center;\\r\\n  align-items: center;\\r\\n  z-index: 9999; /* Ensure it's on top of other content */\\r\\n}\\r\\n\\r\\n.message-content {\\r\\n  text-align: center;\\r\\n  color: white;\\r\\n}\\r\\n\\r\\n.message-content h2 {\\r\\n  font-size: 24px;\\r\\n  margin-bottom: 10px;\\r\\n}\\r\\n\\r\\n.message-content p {\\r\\n  font-size: 16px;\\r\\n}\\r\\n/* quarter filter css */\\r\\n\\r\\n #q1:checked  + .labelcheck, #q2:checked  + .labelcheck, #q3:checked  + .labelcheck, #q4:checked  + .labelcheck, #all:checked  + .labelcheck, #needsupdate:checked  + .labelcheck{\\r\\n  background-color: #00E0D5;\\r\\n  border:1px solid #00BBB2;\\r\\n  color:#fff;\\r\\n  font-weight: 500;\\r\\n  font-family: \\\"poppinsregular\\\";\\r\\n}\\r\\n/* section filter css */\\r\\n #volume:checked  + .labelcheck, #breakeven:checked  + .labelcheck, #unitprice:checked  + .labelcheck, #grossprofit:checked  + .labelcheck, #gppercent:checked  + .labelcheck, #value:checked  + .labelcheck{\\r\\n  background-color: #00E0D5;\\r\\n  border:1px solid #00BBB2;\\r\\n  color:#fff;\\r\\n  font-weight: 500;\\r\\n  font-family: \\\"poppinsregular\\\";\\r\\n}\\r\\n #bestatus-1:checked  + .labelcheck {\\r\\n  background-color: #FF9A03;\\r\\n  color:#fff;\\r\\n  font-weight: 500;\\r\\n  font-family: \\\"poppinsregular\\\";\\r\\n}\\r\\n#bestatus-2:checked  + .labelcheck{\\r\\n  background-color: #33CA7F;\\r\\n  /* border:1px solid #00BBB2; */\\r\\n  color:#444;\\r\\n  font-weight: 500;\\r\\n  font-family: \\\"poppinsregular\\\";\\r\\n}\\r\\n#bestatus-3:checked  + .labelcheck{\\r\\n  background-color: #FFDF37;\\r\\n  /* border:1px solid #00BBB2; */\\r\\n  color:#fff;\\r\\n  font-weight: 500;\\r\\n  font-family: \\\"poppinsregular\\\";\\r\\n}\\r\\n#bestatus-4:checked  + .labelcheck{\\r\\n  background-color: #FB4646;\\r\\n  /* border:1px solid #00BBB2; */\\r\\n  color:#fff;\\r\\n  font-weight: 500;\\r\\n  font-family: \\\"poppinsregular\\\";\\r\\n}\\r\\n\\r\\n.planningtoolgrid\\r\\n{\\r\\n   border-collapse: separate;\\r\\n   border-spacing: 0;\\r\\n   /* border: 1px solid #ddd; */\\r\\n}\\r\\n.input-number::-webkit-inner-spin-button,\\r\\n.input-number::-webkit-outer-spin-button {\\r\\n  -webkit-appearance: none;\\r\\n  margin: 0;\\r\\n}\\r\\n\\r\\n\\r\\n.planningtoolgrid thead th {\\r\\n  padding: 5px;\\r\\n  text-align: center;\\r\\n  position: -webkit-sticky; /* for Safari */\\r\\n  height: 35px;\\r\\n  width:90px;\\r\\n  overflow:hidden;\\r\\n  border: 1px solid #ddd;\\r\\n  font-family: \\\"poppinsregular\\\";\\r\\n}\\r\\n/* .planningtoolgrid thead tr, .planningtoolgrid tbody tr{\\r\\n  border: 1px solid #ddd;\\r\\n} */\\r\\n.planningtoolgrid thead tr:first-child th{\\r\\n  border: none !important;\\r\\n}\\r\\n/* .planningtoolgrid tbody tr{\\r\\n height:70px !important;\\r\\n} */\\r\\n.planningtoolgrid tbody th{\\r\\n  width:120px; \\r\\n  height: 40px;\\r\\n}\\r\\n.planningtoolgrid td{\\r\\n  padding: 5px;\\r\\n  border: 1px solid #ddd;\\r\\n  text-align: left;\\r\\n  z-index:0;\\r\\n  width:90px; \\r\\n  height: 35px !important;\\r\\n  overflow:hidden;\\r\\n  font-family: \\\"poppinsregular\\\";\\r\\n}\\r\\n.planningtoolgrid thead{\\r\\n  position:sticky;\\r\\n  top:0;\\r\\n  left:0;\\r\\n  background:#f3f8ff;\\r\\n  z-index:10;\\r\\n}\\r\\n\\r\\n.quartertotals{\\r\\n  /* border:0; */\\r\\n  table-layout: fixed;\\r\\n  width:50%;\\r\\n  text-align:center;\\r\\n}\\r\\n.quartertotals thead th, .quartertotals tbody td{\\r\\n  text-align: center;\\r\\n  border:0;\\r\\n}\\r\\n.quartertotals thead tr, .quartertotals tbody tr {\\r\\n    border: none;\\r\\n}\\r\\n.quartertotals thead th{\\r\\n  border-top-left-radius: 20px;\\r\\n  border-top-right-radius: 20px;\\r\\n  background-color: #fff !important;\\r\\n}\\r\\n.quartertotals thead{\\r\\n  z-index: 5 !important;\\r\\n}\\r\\n\\r\\n.quartertotals thead th:first-child, .quartertotals tbody td:first-child{\\r\\n  background-color: #f3f8ff !important;\\r\\n  width:50px !important;\\r\\n}\\r\\n.quartertotals tbody td:nth-child(2){\\r\\n  background-color: #201E50 !important;\\r\\n  color:#fff;\\r\\n  border-bottom: 1px solid #504F66;\\r\\n  border-right: 5px solid #f3f8ff;\\r\\n}\\r\\n.quartertotals tbody td:nth-child(3){\\r\\n  background-color: #D499B9 !important;\\r\\n  color:#fff;\\r\\n  border-bottom: 1px solid #C0749E;\\r\\n  border-right: 5px solid #f3f8ff;\\r\\n  border-left: 5px solid #f3f8ff;\\r\\n}\\r\\n.quartertotals tbody td:nth-child(4){\\r\\n  background-color: #EE6C4D !important;\\r\\n  color:#fff;\\r\\n  border-bottom: 1px solid #CE4B2C;\\r\\n  border-left: 5px solid #f3f8ff;\\r\\n}\\r\\n.quartertotals tbody tr:last-child td{\\r\\n    border-bottom-left-radius: 15px;\\r\\n    border-bottom-right-radius: 15px;\\r\\n    border-bottom: none;\\r\\n}\\r\\n\\r\\n.titlerow{\\r\\n  background-color: #00E0D5;\\r\\n  color:#fff;\\r\\n  z-index: 9;\\r\\n  font-size: 15px;\\r\\n  height: 28px !important;\\r\\n}\\r\\ntd.sectionrow,th.sectionrow{\\r\\n  background-color: #DDD;\\r\\n  color:#444;\\r\\n  font-weight: 600;\\r\\n  z-index: 9;\\r\\n  height: 28px !important;\\r\\n  font-size: 13px !important;\\r\\n}\\r\\ninput[type=\\\"text\\\"]:disabled, input[type=\\\"number\\\"]:disabled, select:disabled{\\r\\n  background-color: #F6F3F3;\\r\\n}\\r\\n\\r\\n#wrapper.closed .list {\\r\\n  display: none;\\r\\n   transition: height 200ms;\\r\\n}\\r\\n\\r\\n#wrapper.open .list {\\r\\n  display: block;\\r\\n   transition: height 200ms;\\r\\n}\\r\\n\\r\\n.bg-currentWeek{\\r\\n  background-color: #fcf6b1;\\r\\n}\\r\\n.nodata{\\r\\n  background-color:#ffecd4;\\r\\n  color:#b31818\\r\\n}\\r\\n.selected{\\r\\n  color: #0066FF;\\r\\n  border-bottom: 3px solid #0066FF;\\r\\n}\\r\\n.buttonText{\\r\\n  font-family: 'poppinsmedium';\\r\\n  \\r\\n}\\r\\n\\r\\n/*--- SLP CSS ---*/\\r\\n\\r\\n.service-level-grid{\\r\\n   border-collapse: separate;\\r\\n   border-spacing: 0;\\r\\n}\\r\\n\\r\\n.service-level-grid thead th {\\r\\n  padding: 5px;\\r\\n  text-align: left;\\r\\n  position: -webkit-sticky; /* for Safari */\\r\\n  height: 35px;\\r\\n  width:90px;\\r\\n  overflow:hidden;\\r\\n  border: 1px solid #ddd;\\r\\n  white-space: normal;\\r\\n  background:#f3f8ff;\\r\\n  font-family: \\\"poppinsregular\\\";\\r\\n}\\r\\n\\r\\n/* .service-level-grid thead tr:first-child th{\\r\\n  border: none !important;\\r\\n} */\\r\\n.service-level-grid thead tr th:last-child{\\r\\n  background:#f3f8ff;\\r\\n  position:sticky;\\r\\n  right:0;\\r\\n}\\r\\n.service-level-grid tbody tr td:last-child{\\r\\n  background:#fff;\\r\\n  position:sticky;\\r\\n  right:0;\\r\\n}\\r\\n\\r\\n.service-level-grid tbody th{\\r\\n  width:120px; \\r\\n  height: 40px;\\r\\n  border: 1px solid #ddd;\\r\\n  padding:5px;\\r\\n  z-index: 5;\\r\\n  background:#f3f8ff;\\r\\n  font-family: \\\"poppinsregular\\\";\\r\\n}\\r\\n.service-level-grid td{\\r\\n  padding: 5px;\\r\\n  border: 1px solid #ddd;\\r\\n  background-color:white;\\r\\n  text-align: left;\\r\\n  z-index:0;\\r\\n  width:90px; \\r\\n  height: 35px !important;\\r\\n  overflow:hidden;\\r\\n  font-family: \\\"poppinsregular\\\";\\r\\n}\\r\\n.service-level-grid thead{\\r\\n  position:sticky;\\r\\n  top:0;\\r\\n  left:0;\\r\\n  /* background:#f3f8ff; */\\r\\n  z-index:10;\\r\\n}\\r\\n\\r\\n.ag-grid-checkbox-cell .ag-checkbox-input {\\r\\n  cursor: pointer;\\r\\n}\\r\\n\\r\\n.depotdaterange .rdrDateDisplayWrapper{\\r\\n  display:none;\\r\\n}\\r\\n\\r\\nlabel{\\r\\n  font-family: \\\"poppinsregular\\\";\\r\\n}\\r\\nselect::-moz-placeholder {\\r\\n  color: #333333 !important;\\r\\n  opacity: 0.5;\\r\\n  font-weight: 500;\\r\\n  font-family:\\\"poppinsregular\\\";\\r\\n}\\r\\nselect::placeholder, .css-1jqq78o-placeholder, .placeholdertext {\\r\\n  color: #333333 !important;\\r\\n  opacity: 0.5;\\r\\n  font-weight: 500;\\r\\n  font-family:\\\"poppinsregular\\\";\\r\\n}\\r\\n.text-truncate2L {\\r\\n  display: -webkit-box;\\r\\n  -webkit-box-orient: vertical;\\r\\n  -webkit-line-clamp: 2;\\r\\n  overflow: hidden;\\r\\n  text-overflow: ellipsis;\\r\\n  }\\r\\n\\r\\n  .variety-disabled-block{\\r\\n    background-color: #f6f6f6;\\r\\n    color: #888888;\\r\\n    border:1px solid #E2E2E2;\\r\\n    box-shadow: none;\\r\\n  }\\r\\n  .variety-disabled-block h4, .variety-disabled-block label{\\r\\n    color:#797979;\\r\\n  }\\r\\n  .after\\\\:absolute::after {\\n  content: var(--tw-content);\\n  position: absolute;\\n}\\r\\n  .after\\\\:left-\\\\[1px\\\\]::after {\\n  content: var(--tw-content);\\n  left: 1px;\\n}\\r\\n  .after\\\\:top-2::after {\\n  content: var(--tw-content);\\n  top: 0.5rem;\\n}\\r\\n  .after\\\\:h-5::after {\\n  content: var(--tw-content);\\n  height: 1.25rem;\\n}\\r\\n  .after\\\\:w-5::after {\\n  content: var(--tw-content);\\n  width: 1.25rem;\\n}\\r\\n  .after\\\\:translate-x-0::after {\\n  content: var(--tw-content);\\n  --tw-translate-x: 0px;\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\r\\n  .after\\\\:rounded-full::after {\\n  content: var(--tw-content);\\n  border-radius: 9999px;\\n}\\r\\n  .after\\\\:border::after {\\n  content: var(--tw-content);\\n  border-width: 1px;\\n}\\r\\n  .after\\\\:border-light-gray::after {\\n  content: var(--tw-content);\\n  --tw-border-opacity: 1;\\n  border-color: rgb(214 214 214 / var(--tw-border-opacity));\\n}\\r\\n  .after\\\\:bg-white::after {\\n  content: var(--tw-content);\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(255 255 255 / var(--tw-bg-opacity));\\n}\\r\\n  .after\\\\:transition-all::after {\\n  content: var(--tw-content);\\n  transition-property: all;\\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\\n  transition-duration: 150ms;\\n}\\r\\n  .after\\\\:content-\\\\[\\\\'\\\\'\\\\]::after {\\n  --tw-content: '';\\n  content: var(--tw-content);\\n}\\r\\n  .focus-within\\\\:text-gray-600:focus-within {\\n  --tw-text-opacity: 1;\\n  color: rgb(75 85 99 / var(--tw-text-opacity));\\n}\\r\\n  .focus-within\\\\:outline-none:focus-within {\\n  outline: 2px solid transparent;\\n  outline-offset: 2px;\\n}\\r\\n  .hover\\\\:cursor-pointer:hover {\\n  cursor: pointer;\\n}\\r\\n  .hover\\\\:border-l-4:hover {\\n  border-left-width: 4px;\\n}\\r\\n  .hover\\\\:border-white:hover {\\n  --tw-border-opacity: 1;\\n  border-color: rgb(255 255 255 / var(--tw-border-opacity));\\n}\\r\\n  .hover\\\\:bg-blue-800:hover {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(30 64 175 / var(--tw-bg-opacity));\\n}\\r\\n  .hover\\\\:bg-confirm-status\\\\/80:hover {\\n  background-color: rgb(51 202 127 / 0.8);\\n}\\r\\n  .hover\\\\:bg-gray-100:hover {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(243 244 246 / var(--tw-bg-opacity));\\n}\\r\\n  .hover\\\\:bg-gray-200:hover {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(229 231 235 / var(--tw-bg-opacity));\\n}\\r\\n  .hover\\\\:bg-gray-50:hover {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(249 250 251 / var(--tw-bg-opacity));\\n}\\r\\n  .hover\\\\:bg-lighter-gray:hover {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(175 175 175 / var(--tw-bg-opacity));\\n}\\r\\n  .hover\\\\:bg-red-500:hover {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(239 68 68 / var(--tw-bg-opacity));\\n}\\r\\n  .hover\\\\:bg-skin-primary:hover {\\n  --tw-bg-opacity: 1;\\n  background-color: rgba(var(--color-primary), var(--tw-bg-opacity));\\n}\\r\\n  .hover\\\\:bg-blue-700:hover {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(29 78 216 / var(--tw-bg-opacity));\\n}\\r\\n  .hover\\\\:bg-gray-700:hover {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(55 65 81 / var(--tw-bg-opacity));\\n}\\r\\n  .hover\\\\:bg-indigo-700:hover {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(67 56 202 / var(--tw-bg-opacity));\\n}\\r\\n  .hover\\\\:bg-red-600:hover {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(220 38 38 / var(--tw-bg-opacity));\\n}\\r\\n  .hover\\\\:text-gray-900:hover {\\n  --tw-text-opacity: 1;\\n  color: rgb(17 24 39 / var(--tw-text-opacity));\\n}\\r\\n  .hover\\\\:text-black:hover {\\n  --tw-text-opacity: 1;\\n  color: rgb(0 0 0 / var(--tw-text-opacity));\\n}\\r\\n  .hover\\\\:opacity-80:hover {\\n  opacity: 0.8;\\n}\\r\\n  .focus\\\\:outline-none:focus {\\n  outline: 2px solid transparent;\\n  outline-offset: 2px;\\n}\\r\\n  .focus\\\\:outline-2:focus {\\n  outline-width: 2px;\\n}\\r\\n  .focus\\\\:\\\\!outline-skin-primary:focus {\\n  outline-color: rgb(var(--color-primary)) !important;\\n}\\r\\n  .focus\\\\:ring-2:focus {\\n  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);\\n  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);\\n  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);\\n}\\r\\n  .focus\\\\:ring-4:focus {\\n  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);\\n  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(4px + var(--tw-ring-offset-width)) var(--tw-ring-color);\\n  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);\\n}\\r\\n  .focus\\\\:ring-blue-300:focus {\\n  --tw-ring-opacity: 1;\\n  --tw-ring-color: rgb(147 197 253 / var(--tw-ring-opacity));\\n}\\r\\n  .focus\\\\:ring-blue-500:focus {\\n  --tw-ring-opacity: 1;\\n  --tw-ring-color: rgb(59 130 246 / var(--tw-ring-opacity));\\n}\\r\\n  .focus\\\\:ring-indigo-500:focus {\\n  --tw-ring-opacity: 1;\\n  --tw-ring-color: rgb(99 102 241 / var(--tw-ring-opacity));\\n}\\r\\n  .focus\\\\:ring-offset-2:focus {\\n  --tw-ring-offset-width: 2px;\\n}\\r\\n  .disabled\\\\:bg-slate-600:disabled {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(71 85 105 / var(--tw-bg-opacity));\\n}\\r\\n  .disabled\\\\:bg-blue-400:disabled {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(96 165 250 / var(--tw-bg-opacity));\\n}\\r\\n  .disabled\\\\:bg-gray-100:disabled {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(243 244 246 / var(--tw-bg-opacity));\\n}\\r\\n  .disabled\\\\:bg-gray-400:disabled {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(156 163 175 / var(--tw-bg-opacity));\\n}\\r\\n  .disabled\\\\:opacity-50:disabled {\\n  opacity: 0.5;\\n}\\r\\n  .group:hover .group-hover\\\\:block {\\n  display: block;\\n}\\r\\n  .group:hover .group-hover\\\\:text-white {\\n  --tw-text-opacity: 1;\\n  color: rgb(255 255 255 / var(--tw-text-opacity));\\n}\\r\\n  .peer:checked ~ .peer-checked\\\\:bg-blue-600 {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(37 99 235 / var(--tw-bg-opacity));\\n}\\r\\n  .peer:checked ~ .peer-checked\\\\:bg-skin-primary {\\n  --tw-bg-opacity: 1;\\n  background-color: rgba(var(--color-primary), var(--tw-bg-opacity));\\n}\\r\\n  .peer:checked ~ .peer-checked\\\\:after\\\\:left-\\\\[3px\\\\]::after {\\n  content: var(--tw-content);\\n  left: 3px;\\n}\\r\\n  .peer:checked ~ .peer-checked\\\\:after\\\\:translate-x-full::after {\\n  content: var(--tw-content);\\n  --tw-translate-x: 100%;\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\r\\n  .peer:checked ~ .peer-checked\\\\:after\\\\:border-white::after {\\n  content: var(--tw-content);\\n  --tw-border-opacity: 1;\\n  border-color: rgb(255 255 255 / var(--tw-border-opacity));\\n}\\r\\n  @media (prefers-color-scheme: dark) {\\n\\n  .dark\\\\:border-gray-600 {\\n    --tw-border-opacity: 1;\\n    border-color: rgb(75 85 99 / var(--tw-border-opacity));\\n  }\\n\\n  .dark\\\\:bg-blue-600 {\\n    --tw-bg-opacity: 1;\\n    background-color: rgb(37 99 235 / var(--tw-bg-opacity));\\n  }\\n\\n  .dark\\\\:bg-gray-700 {\\n    --tw-bg-opacity: 1;\\n    background-color: rgb(55 65 81 / var(--tw-bg-opacity));\\n  }\\n\\n  .dark\\\\:text-gray-300 {\\n    --tw-text-opacity: 1;\\n    color: rgb(209 213 219 / var(--tw-text-opacity));\\n  }\\n\\n  .dark\\\\:text-white {\\n    --tw-text-opacity: 1;\\n    color: rgb(255 255 255 / var(--tw-text-opacity));\\n  }\\n\\n  .dark\\\\:hover\\\\:bg-blue-700:hover {\\n    --tw-bg-opacity: 1;\\n    background-color: rgb(29 78 216 / var(--tw-bg-opacity));\\n  }\\n\\n  .dark\\\\:hover\\\\:bg-gray-600:hover {\\n    --tw-bg-opacity: 1;\\n    background-color: rgb(75 85 99 / var(--tw-bg-opacity));\\n  }\\n\\n  .dark\\\\:hover\\\\:text-white:hover {\\n    --tw-text-opacity: 1;\\n    color: rgb(255 255 255 / var(--tw-text-opacity));\\n  }\\n\\n  .dark\\\\:focus\\\\:ring-blue-800:focus {\\n    --tw-ring-opacity: 1;\\n    --tw-ring-color: rgb(30 64 175 / var(--tw-ring-opacity));\\n  }\\n}\\r\\n  @media (max-width: 1600px) {\\n\\n  .max-\\\\[1600px\\\\]\\\\:hidden {\\n    display: none;\\n  }\\n}\\r\\n  @media not all and (min-width: 1024px) {\\n\\n  .max-lg\\\\:gap-10 {\\n    gap: 2.5rem;\\n  }\\n}\\r\\n  @media (min-width: 765px) {\\n\\n  .md\\\\:mb-3 {\\n    margin-bottom: 0.75rem;\\n  }\\n\\n  .md\\\\:mb-6 {\\n    margin-bottom: 1.5rem;\\n  }\\n\\n  .md\\\\:ml-\\\\[15\\\\%\\\\] {\\n    margin-left: 15%;\\n  }\\n\\n  .md\\\\:ml-\\\\[55px\\\\] {\\n    margin-left: 55px;\\n  }\\n\\n  .md\\\\:mr-12 {\\n    margin-right: 3rem;\\n  }\\n\\n  .md\\\\:w-1\\\\/2 {\\n    width: 50%;\\n  }\\n\\n  .md\\\\:w-auto {\\n    width: auto;\\n  }\\n\\n  .md\\\\:w-fit {\\n    width: -moz-fit-content;\\n    width: fit-content;\\n  }\\n\\n  .md\\\\:w-full {\\n    width: 100%;\\n  }\\n\\n  .md\\\\:grid-cols-2 {\\n    grid-template-columns: repeat(2, minmax(0, 1fr));\\n  }\\n\\n  .md\\\\:flex-row {\\n    flex-direction: row;\\n  }\\n\\n  .md\\\\:flex-col {\\n    flex-direction: column;\\n  }\\n\\n  .md\\\\:items-center {\\n    align-items: center;\\n  }\\n\\n  .md\\\\:py-2 {\\n    padding-top: 0.5rem;\\n    padding-bottom: 0.5rem;\\n  }\\n\\n  .md\\\\:text-base {\\n    font-size: 1rem;\\n    line-height: 1.5rem;\\n  }\\n}\\r\\n  @media (min-width: 1024px) {\\n\\n  .lg\\\\:mb-0 {\\n    margin-bottom: 0px;\\n  }\\n\\n  .lg\\\\:mb-6 {\\n    margin-bottom: 1.5rem;\\n  }\\n\\n  .lg\\\\:ml-\\\\[60px\\\\] {\\n    margin-left: 60px;\\n  }\\n\\n  .lg\\\\:mr-14 {\\n    margin-right: 3.5rem;\\n  }\\n\\n  .lg\\\\:mt-0 {\\n    margin-top: 0px;\\n  }\\n\\n  .lg\\\\:flex {\\n    display: flex;\\n  }\\n\\n  .lg\\\\:w-\\\\[95\\\\%\\\\] {\\n    width: 95%;\\n  }\\n\\n  .lg\\\\:w-auto {\\n    width: auto;\\n  }\\n\\n  .lg\\\\:w-full {\\n    width: 100%;\\n  }\\n\\n  .lg\\\\:-translate-x-\\\\[70\\\\%\\\\] {\\n    --tw-translate-x: -70%;\\n    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n  }\\n\\n  .lg\\\\:grid-cols-2 {\\n    grid-template-columns: repeat(2, minmax(0, 1fr));\\n  }\\n\\n  .lg\\\\:grid-cols-3 {\\n    grid-template-columns: repeat(3, minmax(0, 1fr));\\n  }\\n\\n  .lg\\\\:grid-cols-4 {\\n    grid-template-columns: repeat(4, minmax(0, 1fr));\\n  }\\n\\n  .lg\\\\:flex-row {\\n    flex-direction: row;\\n  }\\n\\n  .lg\\\\:items-center {\\n    align-items: center;\\n  }\\n\\n  .lg\\\\:gap-9 {\\n    gap: 2.25rem;\\n  }\\n\\n  .lg\\\\:py-1 {\\n    padding-top: 0.25rem;\\n    padding-bottom: 0.25rem;\\n  }\\n\\n  .lg\\\\:pl-0 {\\n    padding-left: 0px;\\n  }\\n\\n  .lg\\\\:text-left {\\n    text-align: left;\\n  }\\n\\n  .lg\\\\:text-sm {\\n    font-size: 0.875rem;\\n    line-height: 1.25rem;\\n  }\\n}\\r\\n  @media (min-width: 1280px) {\\n\\n  .xl\\\\:mb-6 {\\n    margin-bottom: 1.5rem;\\n  }\\n\\n  .xl\\\\:mt-20 {\\n    margin-top: 5rem;\\n  }\\n\\n  .xl\\\\:mt-4 {\\n    margin-top: 1rem;\\n  }\\n\\n  .xl\\\\:mt-6 {\\n    margin-top: 1.5rem;\\n  }\\n\\n  .xl\\\\:block {\\n    display: block;\\n  }\\n\\n  .xl\\\\:inline {\\n    display: inline;\\n  }\\n\\n  .xl\\\\:hidden {\\n    display: none;\\n  }\\n\\n  .xl\\\\:w-1\\\\/2 {\\n    width: 50%;\\n  }\\n\\n  .xl\\\\:w-3\\\\/5 {\\n    width: 60%;\\n  }\\n\\n  .xl\\\\:w-\\\\[100\\\\%\\\\] {\\n    width: 100%;\\n  }\\n\\n  .xl\\\\:w-\\\\[20\\\\%\\\\] {\\n    width: 20%;\\n  }\\n\\n  .xl\\\\:w-\\\\[40\\\\%\\\\] {\\n    width: 40%;\\n  }\\n\\n  .xl\\\\:w-\\\\[48\\\\%\\\\] {\\n    width: 48%;\\n  }\\n\\n  .xl\\\\:w-\\\\[65\\\\%\\\\] {\\n    width: 65%;\\n  }\\n\\n  .xl\\\\:w-\\\\[70\\\\%\\\\] {\\n    width: 70%;\\n  }\\n\\n  .xl\\\\:flex-row {\\n    flex-direction: row;\\n  }\\n\\n  .xl\\\\:gap-6 {\\n    gap: 1.5rem;\\n  }\\n\\n  .xl\\\\:border-e {\\n    border-inline-end-width: 1px;\\n  }\\n\\n  .xl\\\\:border-e-\\\\[1px\\\\] {\\n    border-inline-end-width: 1px;\\n  }\\n\\n  .xl\\\\:pe-8 {\\n    padding-inline-end: 2rem;\\n  }\\n\\n  .xl\\\\:pl-5 {\\n    padding-left: 1.25rem;\\n  }\\n\\n  .xl\\\\:ps-8 {\\n    padding-inline-start: 2rem;\\n  }\\n\\n  .xl\\\\:text-4xl {\\n    font-size: 2.25rem;\\n    line-height: 2.5rem;\\n  }\\n\\n  .xl\\\\:text-lg {\\n    font-size: 1.125rem;\\n    line-height: 1.75rem;\\n  }\\n\\n  .xl\\\\:tracking-normal {\\n    letter-spacing: 0em;\\n  }\\n}\\r\\n  @media (min-width: 1536px) {\\n\\n  .\\\\32xl\\\\:mb-0 {\\n    margin-bottom: 0px;\\n  }\\n\\n  .\\\\32xl\\\\:mb-0\\\\.5 {\\n    margin-bottom: 0.125rem;\\n  }\\n\\n  .\\\\32xl\\\\:block {\\n    display: block;\\n  }\\n\\n  .\\\\32xl\\\\:h-\\\\[calc\\\\(100\\\\%-60px\\\\)\\\\] {\\n    height: calc(100% - 60px);\\n  }\\n\\n  .\\\\32xl\\\\:h-full {\\n    height: 100%;\\n  }\\n\\n  .\\\\32xl\\\\:w-1\\\\/5 {\\n    width: 20%;\\n  }\\n\\n  .\\\\32xl\\\\:w-\\\\[50\\\\%\\\\] {\\n    width: 50%;\\n  }\\n\\n  .\\\\32xl\\\\:w-\\\\[55\\\\%\\\\] {\\n    width: 55%;\\n  }\\n\\n  .\\\\32xl\\\\:w-\\\\[60\\\\%\\\\] {\\n    width: 60%;\\n  }\\n\\n  .\\\\32xl\\\\:w-\\\\[calc\\\\(100\\\\%-70px\\\\)\\\\] {\\n    width: calc(100% - 70px);\\n  }\\n\\n  .\\\\32xl\\\\:\\\\!max-w-\\\\[70\\\\%\\\\] {\\n    max-width: 70% !important;\\n  }\\n\\n  .\\\\32xl\\\\:flex-row {\\n    flex-direction: row;\\n  }\\n\\n  .\\\\32xl\\\\:gap-3 {\\n    gap: 0.75rem;\\n  }\\n\\n  .\\\\32xl\\\\:p-3 {\\n    padding: 0.75rem;\\n  }\\n\\n  .\\\\32xl\\\\:px-3 {\\n    padding-left: 0.75rem;\\n    padding-right: 0.75rem;\\n  }\\n\\n  .\\\\32xl\\\\:px-3\\\\.5 {\\n    padding-left: 0.875rem;\\n    padding-right: 0.875rem;\\n  }\\n\\n  .\\\\32xl\\\\:px-4 {\\n    padding-left: 1rem;\\n    padding-right: 1rem;\\n  }\\n\\n  .\\\\32xl\\\\:px-6 {\\n    padding-left: 1.5rem;\\n    padding-right: 1.5rem;\\n  }\\n\\n  .\\\\32xl\\\\:py-1 {\\n    padding-top: 0.25rem;\\n    padding-bottom: 0.25rem;\\n  }\\n\\n  .\\\\32xl\\\\:py-2 {\\n    padding-top: 0.5rem;\\n    padding-bottom: 0.5rem;\\n  }\\n\\n  .\\\\32xl\\\\:py-3 {\\n    padding-top: 0.75rem;\\n    padding-bottom: 0.75rem;\\n  }\\n\\n  .\\\\32xl\\\\:pt-1 {\\n    padding-top: 0.25rem;\\n  }\\n\\n  .\\\\32xl\\\\:pt-1\\\\.5 {\\n    padding-top: 0.375rem;\\n  }\\n\\n  .\\\\32xl\\\\:text-2xl {\\n    font-size: 1.5rem;\\n    line-height: 2rem;\\n  }\\n\\n  .\\\\32xl\\\\:text-5xl {\\n    font-size: 3rem;\\n    line-height: 1;\\n  }\\n\\n  .\\\\32xl\\\\:text-lg {\\n    font-size: 1.125rem;\\n    line-height: 1.75rem;\\n  }\\n\\n  .\\\\32xl\\\\:text-xl {\\n    font-size: 1.25rem;\\n    line-height: 1.75rem;\\n  }\\n}\\r\\n  @media (min-width: 1600px) {\\n\\n  .min-\\\\[1600px\\\\]\\\\:w-\\\\[23\\\\%\\\\] {\\n    width: 23%;\\n  }\\n\\n  .min-\\\\[1600px\\\\]\\\\:w-\\\\[42\\\\%\\\\] {\\n    width: 42%;\\n  }\\n}\\r\\n  @media (min-width: 1610px) {\\n\\n  .min-\\\\[1610px\\\\]\\\\:inline {\\n    display: inline;\\n  }\\n\\n  .min-\\\\[1610px\\\\]\\\\:w-\\\\[35\\\\%\\\\] {\\n    width: 35%;\\n  }\\n}\", \"\",{\"version\":3,\"sources\":[\"webpack://styles/globals.css\",\"<no source>\"],\"names\":[],\"mappings\":\"AAAA;;CAAc,CAAd;;;CAAc;;AAAd;;;EAAA,sBAAc,EAAd,MAAc;EAAd,eAAc,EAAd,MAAc;EAAd,mBAAc,EAAd,MAAc;EAAd,qBAAc,EAAd,MAAc;AAAA;;AAAd;;EAAA,gBAAc;AAAA;;AAAd;;;;;;;CAAc;;AAAd;EAAA,gBAAc,EAAd,MAAc;EAAd,8BAAc,EAAd,MAAc;EAAd,gBAAc,EAAd,MAAc;EAAd,cAAc;KAAd,WAAc,EAAd,MAAc;EAAd,4NAAc,EAAd,MAAc;EAAd,6BAAc,EAAd,MAAc;EAAd,+BAAc,EAAd,MAAc;AAAA;;AAAd;;;CAAc;;AAAd;EAAA,SAAc,EAAd,MAAc;EAAd,oBAAc,EAAd,MAAc;AAAA;;AAAd;;;;CAAc;;AAAd;EAAA,SAAc,EAAd,MAAc;EAAd,cAAc,EAAd,MAAc;EAAd,qBAAc,EAAd,MAAc;AAAA;;AAAd;;CAAc;;AAAd;EAAA,yCAAc;UAAd,iCAAc;AAAA;;AAAd;;CAAc;;AAAd;;;;;;EAAA,kBAAc;EAAd,oBAAc;AAAA;;AAAd;;CAAc;;AAAd;EAAA,cAAc;EAAd,wBAAc;AAAA;;AAAd;;CAAc;;AAAd;;EAAA,mBAAc;AAAA;;AAAd;;;CAAc;;AAAd;;;;EAAA,+GAAc,EAAd,MAAc;EAAd,cAAc,EAAd,MAAc;AAAA;;AAAd;;CAAc;;AAAd;EAAA,cAAc;AAAA;;AAAd;;CAAc;;AAAd;;EAAA,cAAc;EAAd,cAAc;EAAd,kBAAc;EAAd,wBAAc;AAAA;;AAAd;EAAA,eAAc;AAAA;;AAAd;EAAA,WAAc;AAAA;;AAAd;;;;CAAc;;AAAd;EAAA,cAAc,EAAd,MAAc;EAAd,qBAAc,EAAd,MAAc;EAAd,yBAAc,EAAd,MAAc;AAAA;;AAAd;;;;CAAc;;AAAd;;;;;EAAA,oBAAc,EAAd,MAAc;EAAd,8BAAc,EAAd,MAAc;EAAd,gCAAc,EAAd,MAAc;EAAd,eAAc,EAAd,MAAc;EAAd,oBAAc,EAAd,MAAc;EAAd,oBAAc,EAAd,MAAc;EAAd,cAAc,EAAd,MAAc;EAAd,SAAc,EAAd,MAAc;EAAd,UAAc,EAAd,MAAc;AAAA;;AAAd;;CAAc;;AAAd;;EAAA,oBAAc;AAAA;;AAAd;;;CAAc;;AAAd;;;;EAAA,0BAAc,EAAd,MAAc;EAAd,6BAAc,EAAd,MAAc;EAAd,sBAAc,EAAd,MAAc;AAAA;;AAAd;;CAAc;;AAAd;EAAA,aAAc;AAAA;;AAAd;;CAAc;;AAAd;EAAA,gBAAc;AAAA;;AAAd;;CAAc;;AAAd;EAAA,wBAAc;AAAA;;AAAd;;CAAc;;AAAd;;EAAA,YAAc;AAAA;;AAAd;;;CAAc;;AAAd;EAAA,6BAAc,EAAd,MAAc;EAAd,oBAAc,EAAd,MAAc;AAAA;;AAAd;;CAAc;;AAAd;EAAA,wBAAc;AAAA;;AAAd;;;CAAc;;AAAd;EAAA,0BAAc,EAAd,MAAc;EAAd,aAAc,EAAd,MAAc;AAAA;;AAAd;;CAAc;;AAAd;EAAA,kBAAc;AAAA;;AAAd;;CAAc;;AAAd;;;;;;;;;;;;;EAAA,SAAc;AAAA;;AAAd;EAAA,SAAc;EAAd,UAAc;AAAA;;AAAd;EAAA,UAAc;AAAA;;AAAd;;;EAAA,gBAAc;EAAd,SAAc;EAAd,UAAc;AAAA;;AAAd;;CAAc;AAAd;EAAA,UAAc;AAAA;;AAAd;;CAAc;;AAAd;EAAA,gBAAc;AAAA;;AAAd;;;CAAc;;AAAd;EAAA,UAAc,EAAd,MAAc;EAAd,cAAc,EAAd,MAAc;AAAA;;AAAd;;EAAA,UAAc,EAAd,MAAc;EAAd,cAAc,EAAd,MAAc;AAAA;;AAAd;;CAAc;;AAAd;;EAAA,eAAc;AAAA;;AAAd;;CAAc;AAAd;EAAA,eAAc;AAAA;;AAAd;;;;CAAc;;AAAd;;;;;;;;EAAA,cAAc,EAAd,MAAc;EAAd,sBAAc,EAAd,MAAc;AAAA;;AAAd;;CAAc;;AAAd;;EAAA,eAAc;EAAd,YAAc;AAAA;;AAAd,wEAAc;AAAd;EAAA,aAAc;AAAA;;AAAd;EAAA,wBAAc;EAAd,wBAAc;EAAd,mBAAc;EAAd,mBAAc;EAAd,cAAc;EAAd,cAAc;EAAd,cAAc;EAAd,eAAc;EAAd,eAAc;EAAd,aAAc;EAAd,aAAc;EAAd,kBAAc;EAAd,sCAAc;EAAd,8BAAc;EAAd,6BAAc;EAAd,4BAAc;EAAd,eAAc;EAAd,oBAAc;EAAd,sBAAc;EAAd,uBAAc;EAAd,wBAAc;EAAd,kBAAc;EAAd,2BAAc;EAAd,4BAAc;EAAd,sCAAc;EAAd,kCAAc;EAAd,2BAAc;EAAd,sBAAc;EAAd,8BAAc;EAAd,YAAc;EAAd,kBAAc;EAAd,gBAAc;EAAd,iBAAc;EAAd,kBAAc;EAAd,cAAc;EAAd,gBAAc;EAAd,aAAc;EAAd,mBAAc;EAAd,qBAAc;EAAd,2BAAc;EAAd,yBAAc;EAAd,0BAAc;EAAd,2BAAc;EAAd,uBAAc;EAAd,wBAAc;EAAd,yBAAc;EAAd;AAAc;;AAAd;EAAA,wBAAc;EAAd,wBAAc;EAAd,mBAAc;EAAd,mBAAc;EAAd,cAAc;EAAd,cAAc;EAAd,cAAc;EAAd,eAAc;EAAd,eAAc;EAAd,aAAc;EAAd,aAAc;EAAd,kBAAc;EAAd,sCAAc;EAAd,8BAAc;EAAd,6BAAc;EAAd,4BAAc;EAAd,eAAc;EAAd,oBAAc;EAAd,sBAAc;EAAd,uBAAc;EAAd,wBAAc;EAAd,kBAAc;EAAd,2BAAc;EAAd,4BAAc;EAAd,sCAAc;EAAd,kCAAc;EAAd,2BAAc;EAAd,sBAAc;EAAd,8BAAc;EAAd,YAAc;EAAd,kBAAc;EAAd,gBAAc;EAAd,iBAAc;EAAd,kBAAc;EAAd,cAAc;EAAd,gBAAc;EAAd,aAAc;EAAd,mBAAc;EAAd,qBAAc;EAAd,2BAAc;EAAd,yBAAc;EAAd,0BAAc;EAAd,2BAAc;EAAd,uBAAc;EAAd,wBAAc;EAAd,yBAAc;EAAd;AAAc;AACd;EAAA;AAAoB;AAApB;;EAAA;IAAA;EAAoB;AAAA;AAApB;;EAAA;IAAA;EAAoB;AAAA;AAApB;;EAAA;IAAA;EAAoB;AAAA;AAApB;;EAAA;IAAA;EAAoB;AAAA;AAApB;;EAAA;IAAA;EAAoB;AAAA;AAApB;;EAAA;IAAA;EAAoB;AAAA;AACpB;EAAA,kBAAmB;EAAnB,UAAmB;EAAnB,WAAmB;EAAnB,UAAmB;EAAnB,YAAmB;EAAnB,gBAAmB;EAAnB,sBAAmB;EAAnB,mBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,8BAAmB;EAAnB;AAAmB;AAAnB;EAAA,mBAAmB;EAAnB;AAAmB;AAAnB;EAAA,iBAAmB;EAAnB;AAAmB;AAAnB;EAAA,iBAAmB;EAAnB;AAAmB;AAAnB;EAAA,mBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,gBAAmB;EAAnB;AAAmB;AAAnB;EAAA,gBAAmB;EAAnB;AAAmB;AAAnB;EAAA,mBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,gBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,sCAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA,wBAAmB;EAAnB;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA,qBAAmB;EAAnB;AAAmB;AAAnB;EAAA,yBAAmB;EAAnB;AAAmB;AAAnB;EAAA,mBAAmB;EAAnB;AAAmB;AAAnB;EAAA,mBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,eAAmB;EAAnB,eAAmB;EAAnB;AAAmB;AAAnB;EAAA,iBAAmB;EAAnB,iBAAmB;EAAnB;AAAmB;AAAnB;EAAA,eAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,wBAAmB;KAAnB,qBAAmB;UAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,uBAAmB;EAAnB,uDAAmB;EAAnB;AAAmB;AAAnB;EAAA,uBAAmB;EAAnB,sDAAmB;EAAnB;AAAmB;AAAnB;EAAA,uBAAmB;EAAnB,8DAAmB;EAAnB;AAAmB;AAAnB;EAAA,uBAAmB;EAAnB,uDAAmB;EAAnB;AAAmB;AAAnB;EAAA,uBAAmB;EAAnB,8DAAmB;EAAnB;AAAmB;AAAnB;EAAA,uBAAmB;EAAnB,4DAAmB;EAAnB;AAAmB;AAAnB;EAAA,uBAAmB;EAAnB,oDAAmB;EAAnB;AAAmB;AAAnB;EAAA,uBAAmB;EAAnB,+DAAmB;EAAnB;AAAmB;AAAnB;EAAA,uBAAmB;EAAnB,4DAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,+BAAmB;EAAnB;AAAmB;AAAnB;EAAA,kCAAmB;EAAnB;AAAmB;AAAnB;EAAA,8BAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,iCAAmB;EAAnB;AAAmB;AAAnB;EAAA,iCAAmB;EAAnB;AAAmB;AAAnB;EAAA,iCAAmB;EAAnB;AAAmB;AAAnB;EAAA,iCAAmB;EAAnB;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,6BAAmB;EAAnB;AAAmB;AAAnB;EAAA,6BAAmB;EAAnB;AAAmB;AAAnB;EAAA,6BAAmB;EAAnB;AAAmB;AAAnB;EAAA,6BAAmB;EAAnB;AAAmB;AAAnB;EAAA,6BAAmB;EAAnB;AAAmB;AAAnB;EAAA,6BAAmB;EAAnB;AAAmB;AAAnB;EAAA,6BAAmB;EAAnB;AAAmB;AAAnB;EAAA,6BAAmB;EAAnB;AAAmB;AAAnB;EAAA,6BAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,6BAAmB;EAAnB;AAAmB;AAAnB;EAAA,6BAAmB;EAAnB;AAAmB;AAAnB;EAAA,6BAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,gCAAmB;EAAnB;AAAmB;AAAnB;EAAA,gCAAmB;EAAnB;AAAmB;AAAnB;EAAA,6BAAmB;EAAnB;AAAmB;AAAnB;EAAA,+BAAmB;EAAnB;AAAmB;AAAnB;EAAA,+BAAmB;EAAnB;AAAmB;AAAnB;EAAA,iBAAmB;EAAnB;AAAmB;AAAnB;EAAA,qBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA,qBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,qBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,qBAAmB;EAAnB;AAAmB;AAAnB;EAAA,gBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,mBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,iBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,gBAAmB;EAAnB;AAAmB;AAAnB;EAAA,iBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,0BAAmB;EAAnB;AAAmB;AAAnB;EAAA,iBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,eAAmB;EAAnB;AAAmB;AAAnB;EAAA,mBAAmB;EAAnB;AAAmB;AAAnB;EAAA,mBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,mBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,+BAAmB;EAAnB;AAAmB;AAAnB;EAAA,+BAAmB;EAAnB;AAAmB;AAAnB;EAAA,+BAAmB;EAAnB;AAAmB;AAAnB;EAAA,+BAAmB;EAAnB;AAAmB;AAAnB;EAAA,+BAAmB;EAAnB;AAAmB;AAAnB;EAAA,+BAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,2BAAmB;EAAnB;AAAmB;AAAnB;EAAA,2BAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,0FAAmB;EAAnB,8GAAmB;EAAnB;AAAmB;AAAnB;EAAA,0EAAmB;EAAnB,8FAAmB;EAAnB;AAAmB;AAAnB;EAAA,+EAAmB;EAAnB,mGAAmB;EAAnB;AAAmB;AAAnB;EAAA,0CAAmB;EAAnB,uDAAmB;EAAnB;AAAmB;AAAnB;EAAA,gFAAmB;EAAnB,oGAAmB;EAAnB;AAAmB;AAAnB;EAAA,6EAAmB;EAAnB,iGAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,2BAAmB;EAAnB,+QAAmB;UAAnB;AAAmB;AAAnB;EAAA,6BAAmB;EAAnB,+QAAmB;UAAnB;AAAmB;AAAnB;EAAA,gKAAmB;EAAnB,wJAAmB;EAAnB,iLAAmB;EAAnB,wDAAmB;EAAnB;AAAmB;AAAnB;EAAA,wBAAmB;EAAnB,wDAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AACnB,iCAAiC;;AAEjC;EACE,yBAAyB;EACzB,4CAA6C;AAC/C;AACA;EACE,+BAA+B;EAC/B,4CAAmD;AACrD;AACA;EACE,wBAAwB;EACxB,4CAA4C;AAC9C;AACA;EACE,8BAA8B;EAC9B,4CAAkD;AACpD;AACA;EACE,6BAA6B;EAC7B,4CAAiD;AACnD;AACA;EACE,mCAAmC;EACnC,4CAAuD;AACzD;AACA;EACE,8BAA8B;EAC9B,4CAAkD;AACpD;AACA;EACE,oCAAoC;EACpC,4CAAwD;AAC1D;AACA;EACE,0BAA0B;EAC1B,4CAA8C;AAChD;AACA;EACE,yBAAyB;EACzB,4CAA6C;AAC/C;AACA;EACE,+BAA+B;EAC/B,6CAAmD;AACrD;AACA;EACE,0BAA0B;EAC1B,6CAA8C;AAChD;AACA;EACE,gCAAgC;EAChC,6CAAoD;AACtD;AACA;EACE,2BAA2B;EAC3B,6CAA+C;AACjD;AACA;EACE,4BAA4B;EAC5B,6CAAgD;AAClD;AACA;EACE,kCAAkC;EAClC,6CAAsD;AACxD;AACA;EACE,0BAA0B;EAC1B,4CAA8C;AAChD;AACA;EACE,0BAA0B;EAC1B,4CAA8C;AAChD;;AAEA;;EAEE,YAAY;EACZ,yBAAyB;EACzB,6BAA6B;EAC7B,kBAAkB;EAClB,yBAAyB;EACzB,eAAe;AACjB;;AAEA;;EAEE,YAAY;EACZ,yBAAyB;EACzB,wCAAwC;EACxC,kBAAkB;EAClB,yBAAyB;EACzB,eAAe;AACjB;;AAEA;EACE,6BAA6B;AAC/B;;AAEA;EACE,eAAe;EACf,cAAc;EACd,4BAA4B;AAC9B;;AAEA;EACE,YAAY;EACZ,mBAAmB;AACrB;;AAEA;EACE,eAAe;EACf,WAAW;EACX,4BAA4B;AAC9B;;AAEA;EACE,cAAc;EACd,6BAA6B;EAC7B,eAAe;AACjB;;AAEA;EACE,YAAY;EACZ,6BAA6B;EAC7B,eAAe;EACf,yBAAyB;AAC3B;;AAEA;EACE,6BAA6B;EAC7B,eAAe;AACjB;AACA;EACE,6BAA6B;EAC7B,eAAe;EACf,YAAY;AACd;;AAEA;EACE,6BAA6B;EAC7B,eAAe;AACjB;AACA;EACE,6BAA6B;EAC7B,eAAe;EACf,YAAY;AACd;;AAEA;EACE,6BAA6B;EAC7B,eAAe;AACjB;AACA;EACE,6BAA6B;EAC7B,eAAe;EACf,YAAY;AACd;;AAEA;EACE,wBAAwB;AAC1B;AACA;EACE,wBAAwB;EACxB,YAAY;EACZ,yBAAyB;AAC3B;;AAEA;EACE,yBAAyB;AAC3B;;AAEA;EACE,uBAAuB;AACzB;;AAEA;EACE,yBAAyB;EACzB,6BAA6B;AAC/B;;AAEA;;;;EAIE,yBAAyB;EACzB,6BAA6B;AAC/B;;AAEA;EACE,yBAAyB;EACzB,6BAA6B;AAC/B;;AAEA;EACE,YAAY;EACZ,6BAA6B;AAC/B;AACA;;EAEE,6BAA6B;EAC7B,YAAY;AACd;AACA;;EAEE,6BAA6B;EAC7B,WAAW;AACb;AACA;EACE,6BAA6B;EAC7B,YAAY;AACd;;AAEA;EACE,aAAa;AACf;;;AAGA;EACE,6BAA6B;EAC7B,eAAe;AACjB;;AAEA;;EAEE,WAAW;EACX,6BAA6B;AAC/B;;AAEA;EACE,UAAU;AACZ;;AAEA;EACE,mBAAmB;EACnB,eAAe;EACf,kBAAkB;AACpB;;AAEA;;;EAGE,WAAW;EACX,yBAAyB;EACzB,8BAA8B;EAC9B,iBAAiB;EACjB,oBAAoB;EACpB,aAAa;EACb,sBAAsB;EACtB,uBAAuB;AACzB;;AAEA;;EAEE,WAAW;EACX,2BAA2B;EAC3B,iBAAiB;EACjB,oBAAoB;AACtB;;AAEA;EACE,cAAc;EACd,iBAAiB;EACjB,oBAAoB;EACpB,aAAa;EACb,sBAAsB;EACtB,uBAAuB;EACvB,qBAAqB;AACvB;;AAEA;EACE,gBAAgB;AAClB;;AAEA;EACE,cAAc;EACd,eAAe;EACf,0BAA0B;EAC1B,iBAAiB;EACjB,wBAAwB;EACxB,SAAS;EACT,UAAU;EACV,gBAAgB;AAClB;;AAEA;EACE,eAAe;EACf,2BAA2B;AAC7B;;AAEA,kEAAkE;;AAElE;EACE,UAAU;AACZ;;AAEA;EACE,eAAe;EACf,sBAAsB;EACtB,WAAW;EACX,WAAW;EACX,+BAA+B;EAC/B,kBAAkB;EAClB,aAAa;EACb,uBAAuB;EACvB,mBAAmB;AACrB;;AAEA;EACE,WAAW;EACX,eAAe;EACf,kBAAkB;EAClB,WAAW;EACX,MAAM;EACN,kBAAkB;AACpB;;AAEA;EACE,eAAe;EACf,WAAW;EACX,gBAAgB;EAChB,aAAa;EACb,gBAAgB;AAClB;;AAEA;;EAEE,6BAA6B;AAC/B;;AAEA;EACE,uBAAuB;EACvB,aAAa;EACb,gBAAgB;EAChB,oCAAoC;AACtC;;AAEA;;;;;;;;GAQG;AACH;;GAEG;AACH;EACE,cAAc;AAChB;AACA;;;GAGG;AACH;EACE,uBAAuB;AACzB;AACA;EACE,sBAAsB;EACtB,YAAY;AACd;;AAEA;EACE,YAAY;AACd;;AAEA;EACE,6BAA6B;KAC1B,0BAA0B;UACrB,qBAAqB;AAC/B;AACA;;EAEE,wBAAwB;AAC1B;;AAEA;;;;;;EAME,gBAAgB;EAChB,mBAAmB;EACnB,qBAAqB;EACrB,0BAA0B;EAC1B,cAAc;EACd,6BAA6B;AAC/B;;AAEA;EACE,sBAAsB;AACxB;;AAEA;EACE,0BAA0B;EAC1B,4BAA4B;AAC9B;;AAEA;EACE,6BAA6B;EAC7B,eAAe;EACf,cAAc;AAChB;AACA;EACE,6BAA6B;EAC7B,eAAe;EACf,YAAY;AACd;;AAEA;EACE,6BAA6B;EAC7B,eAAe;EACf,cAAc;EACd,yBAAyB;AAC3B;;AAEA;EACE,6BAA6B;EAC7B,eAAe;EACf,cAAc;EACd,yBAAyB;EACzB,sBAAsB;AACxB;;AAEA;EACE,yBAAyB;AAC3B;AACA;EACE,yBAAyB;AAC3B;;AAEA;EACE,yBAAyB;EACzB,YAAY;AACd;;AAEA;EACE,qBAAqB;AACvB;AACA;EACE,qBAAqB;AACvB;AACA;EACE,cAAc;AAChB;AACA;EACE,cAAc;AAChB;;AAEA;EACE,yBAAyB;AAC3B;AACA;EACE,yBAAyB;AAC3B;AACA;EACE,cAAc;AAChB;AACA;EACE,cAAc;AAChB;AACA;EACE,cAAc;AAChB;AACA;EACE,cAAc;AAChB;;AAEA;EACE,eAAe;EACf,6BAA6B;AAC/B;;AAEA;EACE,6BAA6B;EAC7B,eAAe;EACf,YAAY;AACd;;AAEA;IACI,eAAe;IACf,cAAc;IACd,4BAA4B;AAChC;;AAEA;EACE,8BAA8B;EAC9B,cAAc;EACd,eAAe;AACjB;;AAEA;EACE,8BAA8B;EAC9B,cAAc;EACd,eAAe;AACjB;;AAEA;EACE,aAAa;EACb,eAAe;EACf,6BAA6B;AAC/B;;AAEA;EACE,uBAAuB;AACzB;;AAEA;EACE,oCAAoC;AACtC;;AAEA;EACE,uBAAuB;EACvB,8BAA8B;AAChC;;AAEA;EACE,YAAY;EACZ,kBAAkB;EAClB,uBAAuB;AACzB;;AAEA;EACE,+BAA+B;AACjC;;AAEA;EACE,kBAAkB;EAClB,YAAY;EACZ,UAAU;AACZ;;AAEA;EACE,yBAAyB;EACzB,kBAAkB;AACpB;;AAEA;EACE,6BAA6B;AAC/B;;AAEA;EACE,6BAA6B;AAC/B;;AAEA;IACI,4BAA4B;AAChC;;AAEA;IACI,4BAA4B;AAChC;;AAEA;IACI,4BAA4B;AAChC;;AAEA;EACE,sBAAsB;AACxB;;AAEA;EACE,mBAAmB;EACnB,kBAAkB;AACpB;;AAEA;EACE,aAAa;EACb,mBAAmB;AACrB;;AAEA;EACE,aAAa;EACb,mBAAmB;AACrB;;AAEA;;;;GAIG;;AAEH;IACI,mBAAmB;IACnB,aAAa;AACjB;;AAEA;EACE;IACE,kBAAkB;IAClB,wBAAwB;IACxB,iBAAiB;IACjB,YAAY;EACd;EACA;IACE,kBAAkB;EACpB;EACA;;IAEE,gBAAgB;EAClB;EACA;;IAEE,kBAAkB;IAClB,WAAW;IACX,gBAAgB;EAClB;EACA;;KAEG;;EAEH;IACE,eAAe;EACjB;EACA;IACE,gBAAgB;IAChB,WAAW;IACX,WAAW;IACX,aAAa;IACb,6BAA6B;IAC7B,SAAS;IAET,gBAAgB;EAClB;EACA;;IAEE,oBAAoB;EACtB;EACA;IACE,WAAW;EACb;EACA;IACE,WAAW;IACX,YAAY;IACZ,kBAAkB;IAClB,eAAe;IACf,QAAQ;IACR,aAAa;IACb,uBAAuB;EACzB;EACA;IACE,WAAW;EACb;;EAEA;IACE,UAAU;EACZ;AACF;;;AAGA;;AAEA;;AAEA;;;;;;;;;;GAUG;;AAEH;EACE,eAAe;EACf,MAAM;EACN,OAAO;EACP,WAAW;EACX,YAAY;EACZ,oCAAoC,EAAE,2BAA2B;EACjE,aAAa;EACb,uBAAuB;EACvB,mBAAmB;EACnB,aAAa,EAAE,wCAAwC;AACzD;AACA;EACE,eAAe;EACf,MAAM;EACN,OAAO;EACP,WAAW;EACX,YAAY;EACZ,wCAAwC,EAAE,2BAA2B;EACrE,aAAa;EACb,uBAAuB;EACvB,mBAAmB;EACnB,aAAa,EAAE,wCAAwC;AACzD;;AAEA;EACE,kBAAkB;EAClB,YAAY;AACd;;AAEA;EACE,eAAe;EACf,mBAAmB;AACrB;;AAEA;EACE,eAAe;AACjB;AACA,uBAAuB;;CAEtB;EACC,yBAAyB;EACzB,wBAAwB;EACxB,UAAU;EACV,gBAAgB;EAChB,6BAA6B;AAC/B;AACA,uBAAuB;CACtB;EACC,yBAAyB;EACzB,wBAAwB;EACxB,UAAU;EACV,gBAAgB;EAChB,6BAA6B;AAC/B;CACC;EACC,yBAAyB;EACzB,UAAU;EACV,gBAAgB;EAChB,6BAA6B;AAC/B;AACA;EACE,yBAAyB;EACzB,8BAA8B;EAC9B,UAAU;EACV,gBAAgB;EAChB,6BAA6B;AAC/B;AACA;EACE,yBAAyB;EACzB,8BAA8B;EAC9B,UAAU;EACV,gBAAgB;EAChB,6BAA6B;AAC/B;AACA;EACE,yBAAyB;EACzB,8BAA8B;EAC9B,UAAU;EACV,gBAAgB;EAChB,6BAA6B;AAC/B;;AAEA;;GAEG,yBAAyB;GACzB,iBAAiB;GACjB,4BAA4B;AAC/B;AACA;;EAEE,wBAAwB;EACxB,SAAS;AACX;;;AAGA;EACE,YAAY;EACZ,kBAAkB;EAClB,wBAAwB,EAAE,eAAe;EACzC,YAAY;EACZ,UAAU;EACV,eAAe;EACf,sBAAsB;EAEtB,6BAA6B;AAC/B;AACA;;GAEG;AACH;EACE,uBAAuB;AACzB;AACA;;GAEG;AACH;EACE,WAAW;EACX,YAAY;AACd;AACA;EACE,YAAY;EACZ,sBAAsB;EACtB,gBAAgB;EAChB,SAAS;EACT,UAAU;EACV,uBAAuB;EACvB,eAAe;EACf,6BAA6B;AAC/B;AACA;EACE,eAAe;EACf,KAAK;EACL,MAAM;EACN,kBAAkB;EAClB,UAAU;AACZ;;AAEA;EACE,cAAc;EACd,mBAAmB;EACnB,SAAS;EACT,iBAAiB;AACnB;AACA;EACE,kBAAkB;EAClB,QAAQ;AACV;AACA;IACI,YAAY;AAChB;AACA;EACE,4BAA4B;EAC5B,6BAA6B;EAC7B,iCAAiC;AACnC;AACA;EACE,qBAAqB;AACvB;;AAEA;EACE,oCAAoC;EACpC,qBAAqB;AACvB;AACA;EACE,oCAAoC;EACpC,UAAU;EACV,gCAAgC;EAChC,+BAA+B;AACjC;AACA;EACE,oCAAoC;EACpC,UAAU;EACV,gCAAgC;EAChC,+BAA+B;EAC/B,8BAA8B;AAChC;AACA;EACE,oCAAoC;EACpC,UAAU;EACV,gCAAgC;EAChC,8BAA8B;AAChC;AACA;IACI,+BAA+B;IAC/B,gCAAgC;IAChC,mBAAmB;AACvB;;AAEA;EACE,yBAAyB;EACzB,UAAU;EACV,UAAU;EACV,eAAe;EACf,uBAAuB;AACzB;AACA;EACE,sBAAsB;EACtB,UAAU;EACV,gBAAgB;EAChB,UAAU;EACV,uBAAuB;EACvB,0BAA0B;AAC5B;AACA;EACE,yBAAyB;AAC3B;;AAEA;EACE,aAAa;GACZ,wBAAwB;AAC3B;;AAEA;EACE,cAAc;GACb,wBAAwB;AAC3B;;AAEA;EACE,yBAAyB;AAC3B;AACA;EACE,wBAAwB;EACxB;AACF;AACA;EACE,cAAc;EACd,gCAAgC;AAClC;AACA;EACE,4BAA4B;;AAE9B;;AAEA,kBAAkB;;AAElB;GACG,yBAAyB;GACzB,iBAAiB;AACpB;;AAEA;EACE,YAAY;EACZ,gBAAgB;EAChB,wBAAwB,EAAE,eAAe;EACzC,YAAY;EACZ,UAAU;EACV,eAAe;EACf,sBAAsB;EACtB,mBAAmB;EACnB,kBAAkB;EAClB,6BAA6B;AAC/B;;AAEA;;GAEG;AACH;EACE,kBAAkB;EAClB,eAAe;EACf,OAAO;AACT;AACA;EACE,eAAe;EACf,eAAe;EACf,OAAO;AACT;;AAEA;EACE,WAAW;EACX,YAAY;EACZ,sBAAsB;EACtB,WAAW;EACX,UAAU;EACV,kBAAkB;EAClB,6BAA6B;AAC/B;AACA;EACE,YAAY;EACZ,sBAAsB;EACtB,sBAAsB;EACtB,gBAAgB;EAChB,SAAS;EACT,UAAU;EACV,uBAAuB;EACvB,eAAe;EACf,6BAA6B;AAC/B;AACA;EACE,eAAe;EACf,KAAK;EACL,MAAM;EACN,wBAAwB;EACxB,UAAU;AACZ;;AAEA;EACE,eAAe;AACjB;;AAEA;EACE,YAAY;AACd;;AAEA;EACE,6BAA6B;AAC/B;AACA;EACE,yBAAyB;EACzB,YAAY;EACZ,gBAAgB;EAChB,4BAA4B;AAC9B;AALA;EACE,yBAAyB;EACzB,YAAY;EACZ,gBAAgB;EAChB,4BAA4B;AAC9B;AACA;EACE,oBAAoB;EACpB,4BAA4B;EAC5B,qBAAqB;EACrB,gBAAgB;EAChB,uBAAuB;EACvB;;EAEA;IACE,yBAAyB;IACzB,cAAc;IACd,wBAAwB;IACxB,gBAAgB;EAClB;EACA;IACE,aAAa;EACf;EAz+BF;EAAA,2BCAA;EDAA;CCAA;EDAA;EAAA,2BCAA;EDAA;CCAA;EDAA;EAAA,2BCAA;EDAA;CCAA;EDAA;EAAA,2BCAA;EDAA;CCAA;EDAA;EAAA,2BCAA;EDAA;CCAA;EDAA;EAAA,2BCAA;EDAA,sBCAA;EDAA;CCAA;EDAA;EAAA,2BCAA;EDAA;CCAA;EDAA;EAAA,2BCAA;EDAA;CCAA;EDAA;EAAA,2BCAA;EDAA,uBCAA;EDAA;CCAA;EDAA;EAAA,2BCAA;EDAA,mBCAA;EDAA;CCAA;EDAA;EAAA,2BCAA;EDAA,yBCAA;EDAA,yDCAA;EDAA;CCAA;EDAA;EAAA,iBCAA;EDAA;CCAA;EDAA;EAAA,qBCAA;EDAA;CCAA;EDAA;EAAA,+BCAA;EDAA;CCAA;EDAA;EAAA;CCAA;EDAA;EAAA;CCAA;EDAA;EAAA,uBCAA;EDAA;CCAA;EDAA;EAAA,mBCAA;EDAA;CCAA;EDAA;EAAA;CCAA;EDAA;EAAA,mBCAA;EDAA;CCAA;EDAA;EAAA,mBCAA;EDAA;CCAA;EDAA;EAAA,mBCAA;EDAA;CCAA;EDAA;EAAA,mBCAA;EDAA;CCAA;EDAA;EAAA,mBCAA;EDAA;CCAA;EDAA;EAAA,mBCAA;EDAA;CCAA;EDAA;EAAA,mBCAA;EDAA;CCAA;EDAA;EAAA,mBCAA;EDAA;CCAA;EDAA;EAAA,mBCAA;EDAA;CCAA;EDAA;EAAA,mBCAA;EDAA;CCAA;EDAA;EAAA,qBCAA;EDAA;CCAA;EDAA;EAAA,qBCAA;EDAA;CCAA;EDAA;EAAA;CCAA;EDAA;EAAA,+BCAA;EDAA;CCAA;EDAA;EAAA;CCAA;EDAA;EAAA;CCAA;EDAA;EAAA,4GCAA;EDAA,0GCAA;EDAA;CCAA;EDAA;EAAA,4GCAA;EDAA,0GCAA;EDAA;CCAA;EDAA;EAAA,qBCAA;EDAA;CCAA;EDAA;EAAA,qBCAA;EDAA;CCAA;EDAA;EAAA,qBCAA;EDAA;CCAA;EDAA;EAAA;CCAA;EDAA;EAAA,mBCAA;EDAA;CCAA;EDAA;EAAA,mBCAA;EDAA;CCAA;EDAA;EAAA,mBCAA;EDAA;CCAA;EDAA;EAAA,mBCAA;EDAA;CCAA;EDAA;EAAA;CCAA;EDAA;EAAA;CCAA;EDAA;EAAA,qBCAA;EDAA;CCAA;EDAA;EAAA,mBCAA;EDAA;CCAA;EDAA;EAAA,mBCAA;EDAA;CCAA;EDAA;EAAA,2BCAA;EDAA;CCAA;EDAA;EAAA,2BCAA;EDAA,uBCAA;EDAA;CCAA;EDAA;EAAA,2BCAA;EDAA,uBCAA;EDAA;CCAA;EDAA;;EAAA;IAAA,uBCAA;IDAA;GCAA;;EDAA;IAAA,mBCAA;IDAA;GCAA;;EDAA;IAAA,mBCAA;IDAA;GCAA;;EDAA;IAAA,qBCAA;IDAA;GCAA;;EDAA;IAAA,qBCAA;IDAA;GCAA;;EDAA;IAAA,mBCAA;IDAA;GCAA;;EDAA;IAAA,mBCAA;IDAA;GCAA;;EDAA;IAAA,qBCAA;IDAA;GCAA;;EDAA;IAAA,qBCAA;IDAA;GCAA;CAAA;EDAA;;EAAA;IAAA;GCAA;CAAA;EDAA;;EAAA;IAAA;GCAA;CAAA;EDAA;;EAAA;IAAA;GCAA;;EDAA;IAAA;GCAA;;EDAA;IAAA;GCAA;;EDAA;IAAA;GCAA;;EDAA;IAAA;GCAA;;EDAA;IAAA;GCAA;;EDAA;IAAA;GCAA;;EDAA;IAAA,wBCAA;IDAA;GCAA;;EDAA;IAAA;GCAA;;EDAA;IAAA;GCAA;;EDAA;IAAA;GCAA;;EDAA;IAAA;GCAA;;EDAA;IAAA;GCAA;;EDAA;IAAA,oBCAA;IDAA;GCAA;;EDAA;IAAA,gBCAA;IDAA;GCAA;CAAA;EDAA;;EAAA;IAAA;GCAA;;EDAA;IAAA;GCAA;;EDAA;IAAA;GCAA;;EDAA;IAAA;GCAA;;EDAA;IAAA;GCAA;;EDAA;IAAA;GCAA;;EDAA;IAAA;GCAA;;EDAA;IAAA;GCAA;;EDAA;IAAA;GCAA;;EDAA;IAAA,uBCAA;IDAA;GCAA;;EDAA;IAAA;GCAA;;EDAA;IAAA;GCAA;;EDAA;IAAA;GCAA;;EDAA;IAAA;GCAA;;EDAA;IAAA;GCAA;;EDAA;IAAA;GCAA;;EDAA;IAAA,qBCAA;IDAA;GCAA;;EDAA;IAAA;GCAA;;EDAA;IAAA;GCAA;;EDAA;IAAA,oBCAA;IDAA;GCAA;CAAA;EDAA;;EAAA;IAAA;GCAA;;EDAA;IAAA;GCAA;;EDAA;IAAA;GCAA;;EDAA;IAAA;GCAA;;EDAA;IAAA;GCAA;;EDAA;IAAA;GCAA;;EDAA;IAAA;GCAA;;EDAA;IAAA;GCAA;;EDAA;IAAA;GCAA;;EDAA;IAAA;GCAA;;EDAA;IAAA;GCAA;;EDAA;IAAA;GCAA;;EDAA;IAAA;GCAA;;EDAA;IAAA;GCAA;;EDAA;IAAA;GCAA;;EDAA;IAAA;GCAA;;EDAA;IAAA;GCAA;;EDAA;IAAA;GCAA;;EDAA;IAAA;GCAA;;EDAA;IAAA;GCAA;;EDAA;IAAA;GCAA;;EDAA;IAAA;GCAA;;EDAA;IAAA,mBCAA;IDAA;GCAA;;EDAA;IAAA,oBCAA;IDAA;GCAA;;EDAA;IAAA;GCAA;CAAA;EDAA;;EAAA;IAAA;GCAA;;EDAA;IAAA;GCAA;;EDAA;IAAA;GCAA;;EDAA;IAAA;GCAA;;EDAA;IAAA;GCAA;;EDAA;IAAA;GCAA;;EDAA;IAAA;GCAA;;EDAA;IAAA;GCAA;;EDAA;IAAA;GCAA;;EDAA;IAAA;GCAA;;EDAA;IAAA;GCAA;;EDAA;IAAA;GCAA;;EDAA;IAAA;GCAA;;EDAA;IAAA;GCAA;;EDAA;IAAA,sBCAA;IDAA;GCAA;;EDAA;IAAA,uBCAA;IDAA;GCAA;;EDAA;IAAA,mBCAA;IDAA;GCAA;;EDAA;IAAA,qBCAA;IDAA;GCAA;;EDAA;IAAA,qBCAA;IDAA;GCAA;;EDAA;IAAA,oBCAA;IDAA;GCAA;;EDAA;IAAA,qBCAA;IDAA;GCAA;;EDAA;IAAA;GCAA;;EDAA;IAAA;GCAA;;EDAA;IAAA,kBCAA;IDAA;GCAA;;EDAA;IAAA,gBCAA;IDAA;GCAA;;EDAA;IAAA,oBCAA;IDAA;GCAA;;EDAA;IAAA,mBCAA;IDAA;GCAA;CAAA;EDAA;;EAAA;IAAA;GCAA;;EDAA;IAAA;GCAA;CAAA;EDAA;;EAAA;IAAA;GCAA;;EDAA;IAAA;GCAA;CAAA\",\"sourcesContent\":[\"@tailwind base;\\r\\n@tailwind components;\\r\\n@tailwind utilities;\\r\\n/*@import \\\"bootstrap/bootstrap\\\";*/\\r\\n\\r\\n@font-face {\\r\\n  font-family: poppinsblack;\\r\\n  src: url(\\\"../assets/fonts/Poppins-Black.ttf\\\");\\r\\n}\\r\\n@font-face {\\r\\n  font-family: poppinsblackitalic;\\r\\n  src: url(\\\"../assets/fonts/Poppins-BlackItalic.ttf\\\");\\r\\n}\\r\\n@font-face {\\r\\n  font-family: poppinsbold;\\r\\n  src: url(\\\"../assets/fonts/Poppins-Bold.ttf\\\");\\r\\n}\\r\\n@font-face {\\r\\n  font-family: poppinsbolditalic;\\r\\n  src: url(\\\"../assets/fonts/Poppins-BoldItalic.ttf\\\");\\r\\n}\\r\\n@font-face {\\r\\n  font-family: poppinsextrabold;\\r\\n  src: url(\\\"../assets/fonts/Poppins-ExtraBold.ttf\\\");\\r\\n}\\r\\n@font-face {\\r\\n  font-family: poppinsextrabolditalic;\\r\\n  src: url(\\\"../assets/fonts/Poppins-ExtraBoldItalic.ttf\\\");\\r\\n}\\r\\n@font-face {\\r\\n  font-family: poppinsextralight;\\r\\n  src: url(\\\"../assets/fonts/Poppins-ExtraLight.ttf\\\");\\r\\n}\\r\\n@font-face {\\r\\n  font-family: poppinsextralightitalic;\\r\\n  src: url(\\\"../assets/fonts/Poppins-ExtraLightItalic.ttf\\\");\\r\\n}\\r\\n@font-face {\\r\\n  font-family: poppinsitalic;\\r\\n  src: url(\\\"../assets/fonts/Poppins-Italic.ttf\\\");\\r\\n}\\r\\n@font-face {\\r\\n  font-family: poppinslight;\\r\\n  src: url(\\\"../assets/fonts/Poppins-Light.ttf\\\");\\r\\n}\\r\\n@font-face {\\r\\n  font-family: poppinslightitalic;\\r\\n  src: url(\\\"../assets/fonts/Poppins-LightItalic.ttf\\\");\\r\\n}\\r\\n@font-face {\\r\\n  font-family: poppinsmedium;\\r\\n  src: url(\\\"../assets/fonts/Poppins-Medium.ttf\\\");\\r\\n}\\r\\n@font-face {\\r\\n  font-family: poppinsmediumitalic;\\r\\n  src: url(\\\"../assets/fonts/Poppins-MediumItalic.ttf\\\");\\r\\n}\\r\\n@font-face {\\r\\n  font-family: poppinsregular;\\r\\n  src: url(\\\"../assets/fonts/Poppins-Regular.ttf\\\");\\r\\n}\\r\\n@font-face {\\r\\n  font-family: poppinssemibold;\\r\\n  src: url(\\\"../assets/fonts/Poppins-SemiBold.ttf\\\");\\r\\n}\\r\\n@font-face {\\r\\n  font-family: poppinssemibolditalic;\\r\\n  src: url(\\\"../assets/fonts/Poppins-SemiBoldItalic.ttf\\\");\\r\\n}\\r\\n@font-face {\\r\\n  font-family: poppinsitalic;\\r\\n  src: url(\\\"../assets/fonts/Poppins-Italic.ttf\\\");\\r\\n}\\r\\n@font-face {\\r\\n  font-family: poppinsitalic;\\r\\n  src: url(\\\"../assets/fonts/Poppins-Italic.ttf\\\");\\r\\n}\\r\\n\\r\\nbody,\\r\\nhtml {\\r\\n  height: 100%;\\r\\n  background-repeat: repeat;\\r\\n  font-family: \\\"poppinsregular\\\";\\r\\n  overflow-x: hidden;\\r\\n  background-color: #f3f8ff;\\r\\n  font-size: 12px;\\r\\n}\\r\\n\\r\\n.dark body,\\r\\nhtml {\\r\\n  height: 100%;\\r\\n  background-repeat: repeat;\\r\\n  font-family: \\\"poppinsregular\\\" !important;\\r\\n  overflow-x: hidden;\\r\\n  background-color: #0e0e10;\\r\\n  font-size: 14px;\\r\\n}\\r\\n\\r\\n.ag-header .ag-header-cell.header-with-border  {\\r\\n  border-bottom: 1px solid #ccc;\\r\\n}\\r\\n\\r\\n.ag-header-cell-text {\\r\\n  font-size: 12px;\\r\\n  color: #333333;\\r\\n  font-family: \\\"poppinsmedium\\\";\\r\\n}\\r\\n\\r\\nbutton[disabled] {\\r\\n  opacity: 0.5;\\r\\n  cursor: not-allowed;\\r\\n}\\r\\n\\r\\n.dark .ag-header-cell-text {\\r\\n  font-size: 12px;\\r\\n  color: #fff;\\r\\n  font-family: \\\"poppinsmedium\\\";\\r\\n}\\r\\n\\r\\n.ag-ltr .ag-cell {\\r\\n  color: #333333;\\r\\n  font-family: \\\"poppinsregular\\\";\\r\\n  font-size: 12px;\\r\\n}\\r\\n\\r\\n.dark .ag-ltr .ag-cell {\\r\\n  color: white;\\r\\n  font-family: \\\"poppinsregular\\\";\\r\\n  font-size: 12px;\\r\\n  background-color: #1d212d;\\r\\n}\\r\\n\\r\\n.ag-paging-row-summary-panel-number {\\r\\n  font-family: \\\"poppinsregular\\\";\\r\\n  font-size: 12px;\\r\\n}\\r\\n.dark .ag-paging-row-summary-panel-number {\\r\\n  font-family: \\\"poppinsregular\\\";\\r\\n  font-size: 12px;\\r\\n  color: white;\\r\\n}\\r\\n\\r\\n.ag-paging-page-summary-panel {\\r\\n  font-family: \\\"poppinsregular\\\";\\r\\n  font-size: 12px;\\r\\n}\\r\\n.dark .ag-paging-page-summary-panel {\\r\\n  font-family: \\\"poppinsregular\\\";\\r\\n  font-size: 12px;\\r\\n  color: white;\\r\\n}\\r\\n\\r\\n.pagination {\\r\\n  font-family: \\\"poppinsregular\\\";\\r\\n  font-size: 12px;\\r\\n}\\r\\n.dark .pagination {\\r\\n  font-family: \\\"poppinsregular\\\";\\r\\n  font-size: 12px;\\r\\n  color: white;\\r\\n}\\r\\n\\r\\n.ag-paging-panel {\\r\\n  justify-content: between;\\r\\n}\\r\\n.dark .ag-paging-panel {\\r\\n  justify-content: between;\\r\\n  color: white;\\r\\n  background-color: #1d212d;\\r\\n}\\r\\n\\r\\n.dark .ag-center-cols-viewport {\\r\\n  background-color: #1d212d;\\r\\n}\\r\\n\\r\\n.contentsectionbg {\\r\\n  background-color: white;\\r\\n}\\r\\n\\r\\n.dark .contentsectionbg {\\r\\n  background-color: #1d212d;\\r\\n  font-family: \\\"poppinsregular\\\";\\r\\n}\\r\\n\\r\\n.dark\\r\\n  .ag-header.ag-header-allow-overflow\\r\\n  .ag-header-row\\r\\n  .ag-root-wrapper.ag-layout-normal {\\r\\n  background-color: #1d212d;\\r\\n  font-family: \\\"poppinsregular\\\";\\r\\n}\\r\\n\\r\\n.dark .ag-header-container {\\r\\n  background-color: #1d212d;\\r\\n  font-family: \\\"poppinsregular\\\";\\r\\n}\\r\\n\\r\\n.dark .pagination-style {\\r\\n  color: white;\\r\\n  font-family: \\\"poppinsregular\\\";\\r\\n}\\r\\n.dark .ag-paging-button,\\r\\n.ag-paging-description {\\r\\n  font-family: \\\"poppinsregular\\\";\\r\\n  color: white;\\r\\n}\\r\\n.ag-paging-button,\\r\\n.ag-paging-description {\\r\\n  font-family: \\\"poppinsregular\\\";\\r\\n  color: #000;\\r\\n}\\r\\n.dark .ag-paging-button.ag-disabled {\\r\\n  font-family: \\\"poppinsregular\\\";\\r\\n  color: white;\\r\\n}\\r\\n\\r\\n.ag-body-horizontal-scroll-viewport {\\r\\n  display: none;\\r\\n} \\r\\n\\r\\n\\r\\n.ag-overlay-no-rows-center {\\r\\n  font-family: \\\"poppinsregular\\\";\\r\\n  font-size: 12px;\\r\\n}\\r\\n\\r\\nnav.sidebar .navbar-nav .open .dropdown-menu > li > a:hover,\\r\\nnav.sidebar .navbar-nav .open .dropdown-menu > li > a:focus {\\r\\n  color: #ccc;\\r\\n  background-color: transparent;\\r\\n}\\r\\n\\r\\nnav:hover .forAnimate {\\r\\n  opacity: 1;\\r\\n}\\r\\n\\r\\n.mainmenu {\\r\\n  background: #002d73;\\r\\n  border: #002d73;\\r\\n  border-radius: 0px;\\r\\n}\\r\\n\\r\\n.mainmenu .navbar-nav > .active > a,\\r\\n.mainmenu .navbar-nav > .active > a:hover,\\r\\n.mainmenu .navbar-nav > .active > a:focus {\\r\\n  color: #fff;\\r\\n  background-color: #2a3344;\\r\\n  border-left: 2px solid #1ca9c0;\\r\\n  padding-top: 15px;\\r\\n  padding-bottom: 15px;\\r\\n  display: flex;\\r\\n  flex-direction: column;\\r\\n  justify-content: center;\\r\\n}\\r\\n\\r\\n.mainmenu .navbar-nav > li > a:hover,\\r\\n.mainmenu .navbar-nav > li > a:focus {\\r\\n  color: #fff;\\r\\n  background-color: lightgray;\\r\\n  padding-top: 15px;\\r\\n  padding-bottom: 15px;\\r\\n}\\r\\n\\r\\n.navbar-default .navbar-nav > li > a {\\r\\n  color: #00b1a3;\\r\\n  padding-top: 15px;\\r\\n  padding-bottom: 15px;\\r\\n  display: flex;\\r\\n  flex-direction: column;\\r\\n  justify-content: center;\\r\\n  text-decoration: none;\\r\\n}\\r\\n\\r\\n.navbar {\\r\\n  min-height: 45px;\\r\\n}\\r\\n\\r\\n.page-heading {\\r\\n  color: #333333;\\r\\n  font-size: 14px;\\r\\n  margin-top: 0px !important;\\r\\n  margin-left: 25px;\\r\\n  /* position: absolute; */\\r\\n  top: 10px;\\r\\n  left: 90px;\\r\\n  font-weight: 600;\\r\\n}\\r\\n\\r\\n.page-heading h2 {\\r\\n  font-size: 16px;\\r\\n  margin-top: 15px !important;\\r\\n}\\r\\n\\r\\n/* ----------- Notification dropdown end ----------------------- */\\r\\n\\r\\nnav.sidebar .brand a {\\r\\n  padding: 0;\\r\\n}\\r\\n\\r\\n.brand {\\r\\n  font-size: 24px;\\r\\n  /* padding: 0px 5px; */\\r\\n  color: #fff;\\r\\n  width: 69px;\\r\\n  /* background-color: #002d73; */\\r\\n  text-align: center;\\r\\n  display: flex;\\r\\n  justify-content: center;\\r\\n  align-items: center;\\r\\n}\\r\\n\\r\\n.titlebar {\\r\\n  width: 100%;\\r\\n  position: fixed;\\r\\n  /* height: 55px; */\\r\\n  z-index: 98;\\r\\n  top: 0;\\r\\n  padding-left: 69px;\\r\\n}\\r\\n\\r\\n.accordion {\\r\\n  cursor: pointer;\\r\\n  width: 100%;\\r\\n  text-align: left;\\r\\n  outline: none;\\r\\n  transition: 0.4s;\\r\\n}\\r\\n\\r\\n.active,\\r\\n.accordion:hover {\\r\\n  background-color: transparent;\\r\\n}\\r\\n\\r\\n.panel {\\r\\n  background-color: white;\\r\\n  max-height: 0;\\r\\n  overflow: hidden;\\r\\n  transition: max-height 0.2s ease-out;\\r\\n}\\r\\n\\r\\n/* .reactSelectCustom .css-1fdsijx-ValueContainer , .reactSelectCustom .css-b62m3t-ValueContainer {\\r\\n  position: relative;\\r\\n  top: -5px;\\r\\n}\\r\\n\\r\\n.reactSelectCustom .css-1hb7zxy-IndicatorsContainer, .reactSelectCustom .css-1xc3v61-IndicatorsContainer{\\r\\n  position: relative;\\r\\n  top: -5px;\\r\\n} */\\r\\n/* .reactSelectCustom .css-1jgx7bw-control{\\r\\n  flex-wrap: nowrap !important;\\r\\n} */\\r\\ninput[type=date]:invalid::-ms-datetime-edit {\\r\\n  color: #808080;\\r\\n}\\r\\n/* label {\\r\\n  font-size: 14px;\\r\\n  color: #505050;\\r\\n} */\\r\\n.top-navbar {\\r\\n  background-color: white;\\r\\n}\\r\\n.dark .top-navbar {\\r\\n  background-color: #000;\\r\\n  color: white;\\r\\n}\\r\\n\\r\\n.dark .pageName {\\r\\n  color: white;\\r\\n}\\r\\n\\r\\ninput[type=\\\"number\\\"] {\\r\\n  -webkit-appearance: textfield;\\r\\n     -moz-appearance: textfield;\\r\\n          appearance: textfield;\\r\\n}\\r\\ninput[type=number]::-webkit-inner-spin-button, \\r\\ninput[type=number]::-webkit-outer-spin-button { \\r\\n  -webkit-appearance: none;\\r\\n}\\r\\n\\r\\ninput[type=\\\"text\\\"],\\r\\ninput[type=\\\"tel\\\"],\\r\\ninput[type=\\\"date\\\"],\\r\\ninput[type=\\\"email\\\"],\\r\\ninput[type=\\\"number\\\"],\\r\\nselect {\\r\\n  padding-top: 4px;\\r\\n  padding-bottom: 4px;\\r\\n  border-color: #d6d6d6;\\r\\n  outline: 2px solid #ffffff;\\r\\n  color: #333333;\\r\\n  font-family: \\\"poppinsregular\\\";\\r\\n}\\r\\n\\r\\ninput:focus, select:focus , textarea:focus{\\r\\n  outline-color: #0066ff;\\r\\n}\\r\\n\\r\\n.button {\\r\\n  padding: 4px 12px 4px 12px;\\r\\n  font-family: \\\"poppinsmedium\\\";\\r\\n}\\r\\n\\r\\n.labels {\\r\\n  font-family: \\\"poppinsregular\\\";\\r\\n  font-size: 12px;\\r\\n  color: #505050;\\r\\n}\\r\\n.dark .labels {\\r\\n  font-family: \\\"poppinsregular\\\";\\r\\n  font-size: 12px;\\r\\n  color: white;\\r\\n}\\r\\n\\r\\n.searchbar {\\r\\n  font-family: \\\"poppinsregular\\\";\\r\\n  font-size: 12px;\\r\\n  color: #AFAFAF;\\r\\n  background-color: #FFFFFF;\\r\\n}\\r\\n\\r\\n.dark .searchbar {\\r\\n  font-family: \\\"poppinsregular\\\";\\r\\n  font-size: 12px;\\r\\n  color: #c0c0c0;\\r\\n  background-color: #1d212d;\\r\\n  outline-color: #1d212d;\\r\\n}\\r\\n\\r\\n.checkboxbg {\\r\\n  background-color: #D9D9D9;\\r\\n}\\r\\n.dark .checkboxbg {\\r\\n  background-color: #4d4d4d;\\r\\n}\\r\\n\\r\\n.dark select {\\r\\n  background-color: #1d1d1d;\\r\\n  color: white;\\r\\n}\\r\\n\\r\\n.borderc-theme-blue2 {\\r\\n  border-color: #0066ff;\\r\\n}\\r\\n.dark .borderc-theme-blue2 {\\r\\n  border-color: #6699ff;\\r\\n}\\r\\n.textc-theme-blue2 {\\r\\n  color: #0066ff;\\r\\n}\\r\\n.dark .textc-theme-blue2 {\\r\\n  color: #6699ff;\\r\\n}\\r\\n\\r\\n.bgc-theme-blue2 {\\r\\n  background-color: #0066ff;\\r\\n}\\r\\n.dark .bgc-theme-blue2 {\\r\\n  background-color: #6699ff;\\r\\n}\\r\\n.textc-dark-gray {\\r\\n  color: #727272;\\r\\n}\\r\\n.dark .textc-dark-gray {\\r\\n  color: #a3a3a3;\\r\\n}\\r\\n.borderc-dark-gray {\\r\\n  color: #727272;\\r\\n}\\r\\n.dark .borderc-dark-gray {\\r\\n  color: #a3a3a3;\\r\\n}\\r\\n\\r\\n.input {\\r\\n  font-size: 12px;\\r\\n  font-family: \\\"poppinsregular\\\";\\r\\n}\\r\\n\\r\\n.dark .inputs {\\r\\n  font-family: \\\"poppinsregular\\\";\\r\\n  font-size: 12px;\\r\\n  color: white;\\r\\n}\\r\\n\\r\\n.confirmInputs {\\r\\n    font-size: 12px;\\r\\n    color: #333333;\\r\\n    font-family: \\\"poppinsmedium\\\";\\r\\n}\\r\\n\\r\\n.formtitle {\\r\\n  font-family: \\\"poppinssemibold\\\";\\r\\n  color: #333333;\\r\\n  font-size: 12px;\\r\\n}\\r\\n\\r\\n.subtitles {\\r\\n  font-family: \\\"poppinssemibold\\\";\\r\\n  color: #333333;\\r\\n  font-size: 14px;\\r\\n}\\r\\n\\r\\n.text-blackcolor {\\r\\n  color:#505050;\\r\\n  font-size: 12px;\\r\\n  font-family: \\\"poppinsregular\\\";\\r\\n}\\r\\n\\r\\n.ag-ltr .ag-cell-focus:not(.ag-cell-range-selected):focus-within {\\r\\n  border: none !important;\\r\\n}\\r\\n\\r\\n.ag-row-odd{\\r\\n  background-color: #f3f3f3 !important;\\r\\n}\\r\\n\\r\\n.ag-cell-value span {\\r\\n  height: auto !important;\\r\\n  line-height: normal !important;\\r\\n}\\r\\n\\r\\n.ag-cell{\\r\\n  display:flex;\\r\\n  align-items:center;\\r\\n  border: none !important;\\r\\n}\\r\\n\\r\\n.ag-cell-value, .ag-header-cell-text{\\r\\n  text-overflow: unset !important;\\r\\n}\\r\\n\\r\\n.pagination-style {\\r\\n  position: absolute;\\r\\n  bottom: 10px;\\r\\n  left: 20px;\\r\\n}\\r\\n\\r\\n.pagination-style select {\\r\\n  background-color: #f2f2f2;\\r\\n  border-radius: 3px;\\r\\n}\\r\\n\\r\\n.ag-ltr .ag-header-select-all{\\r\\n  margin-right: 12px !important; \\r\\n}\\r\\n\\r\\n.ag-root-wrapper.ag-layout-normal{\\r\\n  border-radius: 5px !important;\\r\\n}\\r\\n\\r\\n.distribution-point .ag-header-container {\\r\\n    /* width: 100% !important; */\\r\\n}\\r\\n\\r\\n.distribution-point .ag-header-row.ag-header-row-column {\\r\\n    /* width: 100% !important; */\\r\\n}\\r\\n\\r\\n.distribution-point .ag-center-cols-container {\\r\\n    /* width: 100% !important; */\\r\\n}\\r\\n\\r\\n.general_section .ag-header-container, .general_section .ag-center-cols-container, .general_section .ag-header-row  {\\r\\n  width: 100% !important;\\r\\n}\\r\\n\\r\\n.general_section .ag-cell {\\r\\n  /* display: flex; */\\r\\n  margin-right: 10px;\\r\\n}\\r\\n\\r\\n.product_link_def .ag-row .ag-cell {\\r\\n  display: flex;\\r\\n  align-items: center;\\r\\n}\\r\\n\\r\\n.product_data_def .ag-cell {\\r\\n  display: flex;\\r\\n  align-items: center;\\r\\n}\\r\\n\\r\\n/* @media (min-width: 640px) {\\r\\n  .ag-header-cell-text,.dark .ag-header-cell-text,.ag-ltr .ag-cell,.dark .ag-ltr .ag-cell,.ag-paging-row-summary-panel-number,.dark .ag-paging-row-summary-panel-number,.ag-paging-page-summary-panel,.dark .ag-paging-page-summary-panel,.pagination,.dark .pagination,.labels,.dark .labels,.searchbar,.dark .searchbar,.inputs,.dark .inputs,.formtitle{\\r\\n    font-size: 10px;\\r\\n  }\\r\\n} */\\r\\n\\r\\n.viewlog .ag-cell-value {\\r\\n    align-items: center;\\r\\n    display: flex;\\r\\n}\\r\\n\\r\\n@media (min-width: 765px) {\\r\\n  .main {\\r\\n    position: absolute;\\r\\n    width: calc(100% - 40px);\\r\\n    margin-left: 40px;\\r\\n    float: right;\\r\\n  }\\r\\n  nav.sidebar:hover + .main {\\r\\n    margin-left: 200px;\\r\\n  }\\r\\n  nav.sidebar.navbar.sidebar > .container .navbar-brand,\\r\\n  .navbar > .container-fluid .navbar-brand {\\r\\n    margin-left: 0px;\\r\\n  }\\r\\n  nav.sidebar .navbar-brand,\\r\\n  nav.sidebar .navbar-header {\\r\\n    text-align: center;\\r\\n    width: 100%;\\r\\n    margin-left: 0px;\\r\\n  }\\r\\n  /* nav.sidebar a {\\r\\n    padding-bottom: 34px;\\r\\n  } */\\r\\n\\r\\n  nav.sidebar .navbar-nav > li {\\r\\n    font-size: 13px;\\r\\n  }\\r\\n  nav.sidebar .navbar-nav .open .dropdown-menu {\\r\\n    position: static;\\r\\n    float: none;\\r\\n    width: auto;\\r\\n    margin-top: 0;\\r\\n    background-color: transparent;\\r\\n    border: 0;\\r\\n    -webkit-box-shadow: none;\\r\\n    box-shadow: none;\\r\\n  }\\r\\n  nav.sidebar .navbar-collapse,\\r\\n  nav.sidebar .container-fluid {\\r\\n    padding: 0 0px 0 0px;\\r\\n  }\\r\\n  .navbar-inverse .navbar-nav .open .dropdown-menu > li > a {\\r\\n    color: #777;\\r\\n  }\\r\\n  nav.sidebar {\\r\\n    width: 69px;\\r\\n    height: 100%;\\r\\n    margin-bottom: 0px;\\r\\n    position: fixed;\\r\\n    top: 0px;\\r\\n    display: flex;\\r\\n    align-items: flex-start;\\r\\n  }\\r\\n  nav.sidebar li {\\r\\n    width: 100%;\\r\\n  }\\r\\n\\r\\n  .forAnimate {\\r\\n    opacity: 0;\\r\\n  }\\r\\n}\\r\\n\\r\\n\\r\\n@media (min-width: 1024px) {\\r\\n  \\r\\n}\\r\\n\\r\\n/* @media (min-width: 1280px) {\\r\\n  .ag-header-cell-text,.dark .ag-header-cell-text,.ag-ltr .ag-cell,.dark .ag-ltr .ag-cell,.ag-paging-row-summary-panel-number,.dark .ag-paging-row-summary-panel-number,.ag-paging-page-summary-panel,.dark .ag-paging-page-summary-panel,.pagination,.dark .pagination,.labels,.dark .labels,.searchbar,.dark .searchbar,.inputs,.dark .inputs,.formtitle{\\r\\n    font-size: 14px;\\r\\n  }}\\r\\n\\r\\n@media (min-width: 1536px) {\\r\\n  \\r\\n}\\r\\n@media (min-width: 1940px) {\\r\\n  \\r\\n} */\\r\\n\\r\\n.desktop-view-message {\\r\\n  position: fixed;\\r\\n  top: 0;\\r\\n  left: 0;\\r\\n  width: 100%;\\r\\n  height: 100%;\\r\\n  background-color: rgba(0, 0, 0, 0.5); /* Semi-transparent black */\\r\\n  display: flex;\\r\\n  justify-content: center;\\r\\n  align-items: center;\\r\\n  z-index: 9999; /* Ensure it's on top of other content */\\r\\n}\\r\\n.block-view-message {\\r\\n  position: fixed;\\r\\n  top: 0;\\r\\n  left: 0;\\r\\n  width: 100%;\\r\\n  height: 100%;\\r\\n  background-color: rgba(214, 21, 21, 0.5); /* Semi-transparent black */\\r\\n  display: flex;\\r\\n  justify-content: center;\\r\\n  align-items: center;\\r\\n  z-index: 9999; /* Ensure it's on top of other content */\\r\\n}\\r\\n\\r\\n.message-content {\\r\\n  text-align: center;\\r\\n  color: white;\\r\\n}\\r\\n\\r\\n.message-content h2 {\\r\\n  font-size: 24px;\\r\\n  margin-bottom: 10px;\\r\\n}\\r\\n\\r\\n.message-content p {\\r\\n  font-size: 16px;\\r\\n}\\r\\n/* quarter filter css */\\r\\n\\r\\n #q1:checked  + .labelcheck, #q2:checked  + .labelcheck, #q3:checked  + .labelcheck, #q4:checked  + .labelcheck, #all:checked  + .labelcheck, #needsupdate:checked  + .labelcheck{\\r\\n  background-color: #00E0D5;\\r\\n  border:1px solid #00BBB2;\\r\\n  color:#fff;\\r\\n  font-weight: 500;\\r\\n  font-family: \\\"poppinsregular\\\";\\r\\n}\\r\\n/* section filter css */\\r\\n #volume:checked  + .labelcheck, #breakeven:checked  + .labelcheck, #unitprice:checked  + .labelcheck, #grossprofit:checked  + .labelcheck, #gppercent:checked  + .labelcheck, #value:checked  + .labelcheck{\\r\\n  background-color: #00E0D5;\\r\\n  border:1px solid #00BBB2;\\r\\n  color:#fff;\\r\\n  font-weight: 500;\\r\\n  font-family: \\\"poppinsregular\\\";\\r\\n}\\r\\n #bestatus-1:checked  + .labelcheck {\\r\\n  background-color: #FF9A03;\\r\\n  color:#fff;\\r\\n  font-weight: 500;\\r\\n  font-family: \\\"poppinsregular\\\";\\r\\n}\\r\\n#bestatus-2:checked  + .labelcheck{\\r\\n  background-color: #33CA7F;\\r\\n  /* border:1px solid #00BBB2; */\\r\\n  color:#444;\\r\\n  font-weight: 500;\\r\\n  font-family: \\\"poppinsregular\\\";\\r\\n}\\r\\n#bestatus-3:checked  + .labelcheck{\\r\\n  background-color: #FFDF37;\\r\\n  /* border:1px solid #00BBB2; */\\r\\n  color:#fff;\\r\\n  font-weight: 500;\\r\\n  font-family: \\\"poppinsregular\\\";\\r\\n}\\r\\n#bestatus-4:checked  + .labelcheck{\\r\\n  background-color: #FB4646;\\r\\n  /* border:1px solid #00BBB2; */\\r\\n  color:#fff;\\r\\n  font-weight: 500;\\r\\n  font-family: \\\"poppinsregular\\\";\\r\\n}\\r\\n\\r\\n.planningtoolgrid\\r\\n{\\r\\n   border-collapse: separate;\\r\\n   border-spacing: 0;\\r\\n   /* border: 1px solid #ddd; */\\r\\n}\\r\\n.input-number::-webkit-inner-spin-button,\\r\\n.input-number::-webkit-outer-spin-button {\\r\\n  -webkit-appearance: none;\\r\\n  margin: 0;\\r\\n}\\r\\n\\r\\n\\r\\n.planningtoolgrid thead th {\\r\\n  padding: 5px;\\r\\n  text-align: center;\\r\\n  position: -webkit-sticky; /* for Safari */\\r\\n  height: 35px;\\r\\n  width:90px;\\r\\n  overflow:hidden;\\r\\n  border: 1px solid #ddd;\\r\\n  font-family: \\\"poppinsregular\\\";\\r\\n  font-family: \\\"poppinsregular\\\";\\r\\n}\\r\\n/* .planningtoolgrid thead tr, .planningtoolgrid tbody tr{\\r\\n  border: 1px solid #ddd;\\r\\n} */\\r\\n.planningtoolgrid thead tr:first-child th{\\r\\n  border: none !important;\\r\\n}\\r\\n/* .planningtoolgrid tbody tr{\\r\\n height:70px !important;\\r\\n} */\\r\\n.planningtoolgrid tbody th{\\r\\n  width:120px; \\r\\n  height: 40px;\\r\\n}\\r\\n.planningtoolgrid td{\\r\\n  padding: 5px;\\r\\n  border: 1px solid #ddd;\\r\\n  text-align: left;\\r\\n  z-index:0;\\r\\n  width:90px; \\r\\n  height: 35px !important;\\r\\n  overflow:hidden;\\r\\n  font-family: \\\"poppinsregular\\\";\\r\\n}\\r\\n.planningtoolgrid thead{\\r\\n  position:sticky;\\r\\n  top:0;\\r\\n  left:0;\\r\\n  background:#f3f8ff;\\r\\n  z-index:10;\\r\\n}\\r\\n\\r\\n.quartertotals{\\r\\n  /* border:0; */\\r\\n  table-layout: fixed;\\r\\n  width:50%;\\r\\n  text-align:center;\\r\\n}\\r\\n.quartertotals thead th, .quartertotals tbody td{\\r\\n  text-align: center;\\r\\n  border:0;\\r\\n}\\r\\n.quartertotals thead tr, .quartertotals tbody tr {\\r\\n    border: none;\\r\\n}\\r\\n.quartertotals thead th{\\r\\n  border-top-left-radius: 20px;\\r\\n  border-top-right-radius: 20px;\\r\\n  background-color: #fff !important;\\r\\n}\\r\\n.quartertotals thead{\\r\\n  z-index: 5 !important;\\r\\n}\\r\\n\\r\\n.quartertotals thead th:first-child, .quartertotals tbody td:first-child{\\r\\n  background-color: #f3f8ff !important;\\r\\n  width:50px !important;\\r\\n}\\r\\n.quartertotals tbody td:nth-child(2){\\r\\n  background-color: #201E50 !important;\\r\\n  color:#fff;\\r\\n  border-bottom: 1px solid #504F66;\\r\\n  border-right: 5px solid #f3f8ff;\\r\\n}\\r\\n.quartertotals tbody td:nth-child(3){\\r\\n  background-color: #D499B9 !important;\\r\\n  color:#fff;\\r\\n  border-bottom: 1px solid #C0749E;\\r\\n  border-right: 5px solid #f3f8ff;\\r\\n  border-left: 5px solid #f3f8ff;\\r\\n}\\r\\n.quartertotals tbody td:nth-child(4){\\r\\n  background-color: #EE6C4D !important;\\r\\n  color:#fff;\\r\\n  border-bottom: 1px solid #CE4B2C;\\r\\n  border-left: 5px solid #f3f8ff;\\r\\n}\\r\\n.quartertotals tbody tr:last-child td{\\r\\n    border-bottom-left-radius: 15px;\\r\\n    border-bottom-right-radius: 15px;\\r\\n    border-bottom: none;\\r\\n}\\r\\n\\r\\n.titlerow{\\r\\n  background-color: #00E0D5;\\r\\n  color:#fff;\\r\\n  z-index: 9;\\r\\n  font-size: 15px;\\r\\n  height: 28px !important;\\r\\n}\\r\\ntd.sectionrow,th.sectionrow{\\r\\n  background-color: #DDD;\\r\\n  color:#444;\\r\\n  font-weight: 600;\\r\\n  z-index: 9;\\r\\n  height: 28px !important;\\r\\n  font-size: 13px !important;\\r\\n}\\r\\ninput[type=\\\"text\\\"]:disabled, input[type=\\\"number\\\"]:disabled, select:disabled{\\r\\n  background-color: #F6F3F3;\\r\\n}\\r\\n\\r\\n#wrapper.closed .list {\\r\\n  display: none;\\r\\n   transition: height 200ms;\\r\\n}\\r\\n\\r\\n#wrapper.open .list {\\r\\n  display: block;\\r\\n   transition: height 200ms;\\r\\n}\\r\\n\\r\\n.bg-currentWeek{\\r\\n  background-color: #fcf6b1;\\r\\n}\\r\\n.nodata{\\r\\n  background-color:#ffecd4;\\r\\n  color:#b31818\\r\\n}\\r\\n.selected{\\r\\n  color: #0066FF;\\r\\n  border-bottom: 3px solid #0066FF;\\r\\n}\\r\\n.buttonText{\\r\\n  font-family: 'poppinsmedium';\\r\\n  \\r\\n}\\r\\n\\r\\n/*--- SLP CSS ---*/\\r\\n\\r\\n.service-level-grid{\\r\\n   border-collapse: separate;\\r\\n   border-spacing: 0;\\r\\n}\\r\\n\\r\\n.service-level-grid thead th {\\r\\n  padding: 5px;\\r\\n  text-align: left;\\r\\n  position: -webkit-sticky; /* for Safari */\\r\\n  height: 35px;\\r\\n  width:90px;\\r\\n  overflow:hidden;\\r\\n  border: 1px solid #ddd;\\r\\n  white-space: normal;\\r\\n  background:#f3f8ff;\\r\\n  font-family: \\\"poppinsregular\\\";\\r\\n}\\r\\n\\r\\n/* .service-level-grid thead tr:first-child th{\\r\\n  border: none !important;\\r\\n} */\\r\\n.service-level-grid thead tr th:last-child{\\r\\n  background:#f3f8ff;\\r\\n  position:sticky;\\r\\n  right:0;\\r\\n}\\r\\n.service-level-grid tbody tr td:last-child{\\r\\n  background:#fff;\\r\\n  position:sticky;\\r\\n  right:0;\\r\\n}\\r\\n\\r\\n.service-level-grid tbody th{\\r\\n  width:120px; \\r\\n  height: 40px;\\r\\n  border: 1px solid #ddd;\\r\\n  padding:5px;\\r\\n  z-index: 5;\\r\\n  background:#f3f8ff;\\r\\n  font-family: \\\"poppinsregular\\\";\\r\\n}\\r\\n.service-level-grid td{\\r\\n  padding: 5px;\\r\\n  border: 1px solid #ddd;\\r\\n  background-color:white;\\r\\n  text-align: left;\\r\\n  z-index:0;\\r\\n  width:90px; \\r\\n  height: 35px !important;\\r\\n  overflow:hidden;\\r\\n  font-family: \\\"poppinsregular\\\";\\r\\n}\\r\\n.service-level-grid thead{\\r\\n  position:sticky;\\r\\n  top:0;\\r\\n  left:0;\\r\\n  /* background:#f3f8ff; */\\r\\n  z-index:10;\\r\\n}\\r\\n\\r\\n.ag-grid-checkbox-cell .ag-checkbox-input {\\r\\n  cursor: pointer;\\r\\n}\\r\\n\\r\\n.depotdaterange .rdrDateDisplayWrapper{\\r\\n  display:none;\\r\\n}\\r\\n\\r\\nlabel{\\r\\n  font-family: \\\"poppinsregular\\\";\\r\\n}\\r\\nselect::placeholder, .css-1jqq78o-placeholder, .placeholdertext {\\r\\n  color: #333333 !important;\\r\\n  opacity: 0.5;\\r\\n  font-weight: 500;\\r\\n  font-family:\\\"poppinsregular\\\";\\r\\n}\\r\\n.text-truncate2L {\\r\\n  display: -webkit-box;\\r\\n  -webkit-box-orient: vertical;\\r\\n  -webkit-line-clamp: 2;\\r\\n  overflow: hidden;\\r\\n  text-overflow: ellipsis;\\r\\n  }\\r\\n\\r\\n  .variety-disabled-block{\\r\\n    background-color: #f6f6f6;\\r\\n    color: #888888;\\r\\n    border:1px solid #E2E2E2;\\r\\n    box-shadow: none;\\r\\n  }\\r\\n  .variety-disabled-block h4, .variety-disabled-block label{\\r\\n    color:#797979;\\r\\n  }\",null],\"sourceRoot\":\"\"}]);\n// Exports\n/* harmony default export */ __webpack_exports__[\"default\"] = (___CSS_LOADER_EXPORT___);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[8].oneOf[13].use[1]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[8].oneOf[13].use[2]!./styles/globals.css\n"));

/***/ }),

/***/ "./pages/_app.js":
/*!***********************!*\
  !*** ./pages/_app.js ***!
  \***********************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ App; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _styles_globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/styles/globals.css */ \"./styles/globals.css\");\n/* harmony import */ var _styles_globals_css__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_styles_globals_css__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _utils_auth_msalProvider__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/utils/auth/msalProvider */ \"./utils/auth/msalProvider.jsx\");\n/* harmony import */ var _utils_loaders_loadingContext__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/utils/loaders/loadingContext */ \"./utils/loaders/loadingContext.js\");\n/* harmony import */ var _utils_loaders_overlaySpinner__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/utils/loaders/overlaySpinner */ \"./utils/loaders/overlaySpinner.js\");\n/* harmony import */ var _utils_secureThemeContext__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/utils/secureThemeContext */ \"./utils/secureThemeContext.js\");\n/* harmony import */ var _utils_rolePermissionsContext__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/utils/rolePermissionsContext */ \"./utils/rolePermissionsContext.js\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nfunction App(param) {\n    let { Component, pageProps } = param;\n    var _pageProps_userData;\n    _s();\n    const [isOpen, setIsOpen] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(true);\n    const isOnline = useNetwork();\n    function useNetwork() {\n        if (true) {\n            // Client-side-only code\n            const [isOnline, setNetwork] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(window.navigator.onLine);\n            (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n                window.addEventListener(\"offline\", ()=>setNetwork(window.navigator.onLine));\n                window.addEventListener(\"online\", ()=>setNetwork(window.navigator.onLine));\n            });\n            return isOnline;\n        }\n    }\n    ;\n    const closeModal = ()=>{\n        setIsOpen(false);\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        if (isOnline) {\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                classNam: \"no-connection\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        children: \"No Internet Connection\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\_app.js\",\n                        lineNumber: 36,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"noConnectionAlertBox\", {\n                        isOpen: isOpen,\n                        closeModal: closeModal\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\_app.js\",\n                        lineNumber: 37,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\_app.js\",\n                lineNumber: 35,\n                columnNumber: 7\n            }, this);\n        }\n    }, []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_utils_auth_msalProvider__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_utils_secureThemeContext__WEBPACK_IMPORTED_MODULE_6__.SecureThemeProvider, {\n            initialTheme: (_pageProps_userData = pageProps.userData) === null || _pageProps_userData === void 0 ? void 0 : _pageProps_userData.theme,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_utils_rolePermissionsContext__WEBPACK_IMPORTED_MODULE_7__.PermissionsProvider, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_utils_loaders_loadingContext__WEBPACK_IMPORTED_MODULE_4__.LoadingProvider, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_utils_loaders_overlaySpinner__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {}, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\_app.js\",\n                            lineNumber: 47,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Component, {\n                            ...pageProps\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\_app.js\",\n                            lineNumber: 48,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\_app.js\",\n                    lineNumber: 46,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\_app.js\",\n                lineNumber: 45,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\_app.js\",\n            lineNumber: 44,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\_app.js\",\n        lineNumber: 43,\n        columnNumber: 5\n    }, this);\n}\n_s(App, \"ZN1wroM70TG0wUXJ8uHLTOJ6UUs=\", true);\n_c = App;\nvar _c;\n$RefreshReg$(_c, \"App\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevExports = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevExports) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports on update so we can compare the boundary\n                // signatures.\n                module.hot.dispose(function (data) {\n                    data.prevExports = currentExports;\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevExports !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevExports, currentExports)) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevExports !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./pages/_app.js\n"));

/***/ }),

/***/ "./utils/auth/authConfig.js":
/*!**********************************!*\
  !*** ./utils/auth/authConfig.js ***!
  \**********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   BASE_URL: function() { return /* binding */ BASE_URL; },\n/* harmony export */   loginRequest: function() { return /* binding */ loginRequest; },\n/* harmony export */   msalConfig: function() { return /* binding */ msalConfig; }\n/* harmony export */ });\nconst BASE_URL = \"\".concat(\"http://localhost:3000\", \"/login\");\nconst msalConfig = {\n    auth: {\n        clientId: \"bafc3c7b-820b-4d2d-9a96-9162c4b2c78a\",\n        authority: \"https://login.microsoftonline.com/\".concat(\"6d90d24f-9602-49e8-8903-eb86dce9656a\"),\n        redirectUri: \"/\"\n    },\n    cache: {\n        cacheLocation: \"sessionStorage\",\n        storeAuthStateInCookie: false\n    }\n};\nconst loginRequest = {\n    scopes: [\n        \"user.read\"\n    ]\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevExports = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevExports) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports on update so we can compare the boundary\n                // signatures.\n                module.hot.dispose(function (data) {\n                    data.prevExports = currentExports;\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevExports !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevExports, currentExports)) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevExports !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi91dGlscy9hdXRoL2F1dGhDb25maWcuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQU8sTUFBTUEsV0FBVyxHQUFvQyxPQUFqQ0MsdUJBQWdDLEVBQUMsVUFBUTtBQUU3RCxNQUFNRyxhQUFhO0lBQ3hCQyxNQUFNO1FBQ0pDLFVBQVVMLHNDQUFpQztRQUMzQ08sV0FBVyxxQ0FBdUUsT0FBbENQLHNDQUFpQztRQUNqRlMsYUFBYTtJQUNmO0lBQ0FDLE9BQU87UUFDTEMsZUFBZTtRQUNmQyx3QkFBd0I7SUFDMUI7QUFDRixFQUFFO0FBRUssTUFBTUMsZUFBZTtJQUMxQkMsUUFBUTtRQUFDO0tBQVk7QUFDdkIsRUFBRSIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi91dGlscy9hdXRoL2F1dGhDb25maWcuanM/YWM5ZCJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgY29uc3QgQkFTRV9VUkwgPSBgJHtwcm9jZXNzLmVudi5ORVhUX1BVQkxJQ19CQVNFX1VSTH0vbG9naW5gO1xyXG5cclxuZXhwb3J0IGNvbnN0IG1zYWxDb25maWcgPSB7XHJcbiAgYXV0aDoge1xyXG4gICAgY2xpZW50SWQ6IHByb2Nlc3MuZW52Lk5FWFRfUFVCTElDX0NMSUVOVF9JRCxcclxuICAgIGF1dGhvcml0eTogYGh0dHBzOi8vbG9naW4ubWljcm9zb2Z0b25saW5lLmNvbS8ke3Byb2Nlc3MuZW52Lk5FWFRfUFVCTElDX1RFTkFOVF9JRH1gLFxyXG4gICAgcmVkaXJlY3RVcmk6IFwiL1wiLFxyXG4gIH0sXHJcbiAgY2FjaGU6IHtcclxuICAgIGNhY2hlTG9jYXRpb246IFwic2Vzc2lvblN0b3JhZ2VcIiwgLy8gQ2hhbmdlZCBmcm9tIGxvY2FsU3RvcmFnZVxyXG4gICAgc3RvcmVBdXRoU3RhdGVJbkNvb2tpZTogZmFsc2UsXHJcbiAgfSxcclxufTtcclxuXHJcbmV4cG9ydCBjb25zdCBsb2dpblJlcXVlc3QgPSB7XHJcbiAgc2NvcGVzOiBbXCJ1c2VyLnJlYWRcIl0sXHJcbn07XHJcbiJdLCJuYW1lcyI6WyJCQVNFX1VSTCIsInByb2Nlc3MiLCJlbnYiLCJORVhUX1BVQkxJQ19CQVNFX1VSTCIsIm1zYWxDb25maWciLCJhdXRoIiwiY2xpZW50SWQiLCJORVhUX1BVQkxJQ19DTElFTlRfSUQiLCJhdXRob3JpdHkiLCJORVhUX1BVQkxJQ19URU5BTlRfSUQiLCJyZWRpcmVjdFVyaSIsImNhY2hlIiwiY2FjaGVMb2NhdGlvbiIsInN0b3JlQXV0aFN0YXRlSW5Db29raWUiLCJsb2dpblJlcXVlc3QiLCJzY29wZXMiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///./utils/auth/authConfig.js\n"));

/***/ }),

/***/ "./utils/secureThemeContext.js":
/*!*************************************!*\
  !*** ./utils/secureThemeContext.js ***!
  \*************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SecureThemeProvider: function() { return /* binding */ SecureThemeProvider; },\n/* harmony export */   useSecureTheme: function() { return /* binding */ useSecureTheme; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n\nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\nconst SecureThemeContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)();\nconst SecureThemeProvider = (param)=>{\n    let { children, initialTheme = \"#022D71\" } = param;\n    _s();\n    const [themeColor, setThemeColor] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(initialTheme);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    // Initialize theme from session data\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const initializeTheme = async ()=>{\n            try {\n                const apiBase = \"http://localhost:8081\" || 0;\n                // Get theme from session via API call\n                const response = await fetch(\"\".concat(apiBase, \"/api/auth/me\"), {\n                    method: \"GET\",\n                    credentials: \"include\"\n                });\n                if (response.ok) {\n                    const { user } = await response.json();\n                    if (user === null || user === void 0 ? void 0 : user.theme) {\n                        setThemeColor(user.theme);\n                    }\n                }\n            } catch (error) {\n                console.error(\"Error loading theme:\", error);\n            } finally{\n                setIsLoading(false);\n            }\n        };\n        initializeTheme();\n    }, []);\n    // Apply theme to CSS variables\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if ( true && themeColor) {\n            const existingStyleElement = document.getElementById(\"theme-style\");\n            if (existingStyleElement) {\n                existingStyleElement.remove();\n            }\n            const $style = document.createElement(\"style\");\n            $style.id = \"theme-style\";\n            document.head.appendChild($style);\n            // Convert hex to RGB\n            const getRGBColor = (hex, type)=>{\n                let color = hex.replace(/#/g, \"\");\n                var r = parseInt(color.substr(0, 2), 16);\n                var g = parseInt(color.substr(2, 2), 16);\n                var b = parseInt(color.substr(4, 2), 16);\n                return \"--color-\".concat(type, \": \").concat(r, \", \").concat(g, \", \").concat(b, \";\");\n            };\n            const getAccessibleColor = (hex)=>{\n                let color = hex.replace(/#/g, \"\");\n                var r = parseInt(color.substr(0, 2), 16);\n                var g = parseInt(color.substr(2, 2), 16);\n                var b = parseInt(color.substr(4, 2), 16);\n                var yiq = (r * 299 + g * 587 + b * 114) / 1000;\n                return yiq >= 128 ? \"#000000\" : \"#FFFFFF\";\n            };\n            const primaryColor = getRGBColor(themeColor, \"primary\");\n            const textColor = getRGBColor(getAccessibleColor(themeColor), \"a11y\");\n            $style.innerHTML = \":root {\".concat(primaryColor, \" \").concat(textColor, \"}\");\n        }\n    }, [\n        themeColor\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SecureThemeContext.Provider, {\n        value: {\n            themeColor,\n            setThemeColor,\n            isLoading\n        },\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\utils\\\\secureThemeContext.js\",\n        lineNumber: 75,\n        columnNumber: 5\n    }, undefined);\n};\n_s(SecureThemeProvider, \"zU1TgpWkr8xem8az6440BL4hsGI=\");\n_c = SecureThemeProvider;\nconst useSecureTheme = ()=>{\n    _s1();\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(SecureThemeContext);\n    if (!context) {\n        throw new Error(\"useSecureTheme must be used within a SecureThemeProvider\");\n    }\n    return context;\n};\n_s1(useSecureTheme, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nvar _c;\n$RefreshReg$(_c, \"SecureThemeProvider\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevExports = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevExports) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports on update so we can compare the boundary\n                // signatures.\n                module.hot.dispose(function (data) {\n                    data.prevExports = currentExports;\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevExports !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevExports, currentExports)) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevExports !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./utils/secureThemeContext.js\n"));

/***/ })

});