"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["node_modules_azure_msal-browser_dist_controllers_NestedAppAuthController_mjs"],{

/***/ "./node_modules/@azure/msal-browser/dist/controllers/NestedAppAuthController.mjs":
/*!***************************************************************************************!*\
  !*** ./node_modules/@azure/msal-browser/dist/controllers/NestedAppAuthController.mjs ***!
  \***************************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   NestedAppAuthController: function() { return /* binding */ NestedAppAuthController; }\n/* harmony export */ });\n/* harmony import */ var _azure_msal_common__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @azure/msal-common */ \"./node_modules/@azure/msal-common/dist/index.mjs\");\n/* harmony import */ var _utils_BrowserConstants_mjs__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../utils/BrowserConstants.mjs */ \"./node_modules/@azure/msal-browser/dist/utils/BrowserConstants.mjs\");\n/* harmony import */ var _crypto_CryptoOps_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../crypto/CryptoOps.mjs */ \"./node_modules/@azure/msal-browser/dist/crypto/CryptoOps.mjs\");\n/* harmony import */ var _naa_mapping_NestedAppAuthAdapter_mjs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../naa/mapping/NestedAppAuthAdapter.mjs */ \"./node_modules/@azure/msal-browser/dist/naa/mapping/NestedAppAuthAdapter.mjs\");\n/* harmony import */ var _error_NestedAppAuthError_mjs__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../error/NestedAppAuthError.mjs */ \"./node_modules/@azure/msal-browser/dist/error/NestedAppAuthError.mjs\");\n/* harmony import */ var _event_EventHandler_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../event/EventHandler.mjs */ \"./node_modules/@azure/msal-browser/dist/event/EventHandler.mjs\");\n/* harmony import */ var _event_EventType_mjs__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../event/EventType.mjs */ \"./node_modules/@azure/msal-browser/dist/event/EventType.mjs\");\n/*! @azure/msal-browser v3.6.0 2023-12-01 */\n\n\n\n\n\n\n\n\n\n/*\n * Copyright (c) Microsoft Corporation. All rights reserved.\n * Licensed under the MIT License.\n */\nclass NestedAppAuthController {\n    constructor(operatingContext) {\n        this.operatingContext = operatingContext;\n        const proxy = this.operatingContext.getBridgeProxy();\n        if (proxy !== undefined) {\n            this.bridgeProxy = proxy;\n        }\n        else {\n            throw new Error(\"unexpected: bridgeProxy is undefined\");\n        }\n        // Set the configuration.\n        this.config = operatingContext.getConfig();\n        // Initialize logger\n        this.logger = this.operatingContext.getLogger();\n        // Initialize performance client\n        this.performanceClient = this.config.telemetry.client;\n        // Initialize the crypto class.\n        this.browserCrypto = operatingContext.isBrowserEnvironment()\n            ? new _crypto_CryptoOps_mjs__WEBPACK_IMPORTED_MODULE_0__.CryptoOps(this.logger, this.performanceClient)\n            : _azure_msal_common__WEBPACK_IMPORTED_MODULE_1__.DEFAULT_CRYPTO_IMPLEMENTATION;\n        this.eventHandler = new _event_EventHandler_mjs__WEBPACK_IMPORTED_MODULE_2__.EventHandler(this.logger, this.browserCrypto);\n        this.nestedAppAuthAdapter = new _naa_mapping_NestedAppAuthAdapter_mjs__WEBPACK_IMPORTED_MODULE_3__.NestedAppAuthAdapter(this.config.auth.clientId, this.config.auth.clientCapabilities, this.browserCrypto, this.logger);\n    }\n    getBrowserStorage() {\n        throw _error_NestedAppAuthError_mjs__WEBPACK_IMPORTED_MODULE_4__.NestedAppAuthError.createUnsupportedError();\n    }\n    getEventHandler() {\n        return this.eventHandler;\n    }\n    static async createController(operatingContext) {\n        const controller = new NestedAppAuthController(operatingContext);\n        return Promise.resolve(controller);\n    }\n    initialize() {\n        // do nothing not required by this controller\n        return Promise.resolve();\n    }\n    async acquireTokenInteractive(request) {\n        this.eventHandler.emitEvent(_event_EventType_mjs__WEBPACK_IMPORTED_MODULE_5__.EventType.ACQUIRE_TOKEN_START, _utils_BrowserConstants_mjs__WEBPACK_IMPORTED_MODULE_6__.InteractionType.Popup, request);\n        const atPopupMeasurement = this.performanceClient.startMeasurement(_azure_msal_common__WEBPACK_IMPORTED_MODULE_1__.PerformanceEvents.AcquireTokenPopup, request.correlationId);\n        atPopupMeasurement?.add({ nestedAppAuthRequest: true });\n        try {\n            const naaRequest = this.nestedAppAuthAdapter.toNaaTokenRequest(request);\n            const reqTimestamp = _azure_msal_common__WEBPACK_IMPORTED_MODULE_1__.TimeUtils.nowSeconds();\n            const response = await this.bridgeProxy.getTokenInteractive(naaRequest);\n            const result = this.nestedAppAuthAdapter.fromNaaTokenResponse(naaRequest, response, reqTimestamp);\n            this.operatingContext.setActiveAccount(result.account);\n            this.eventHandler.emitEvent(_event_EventType_mjs__WEBPACK_IMPORTED_MODULE_5__.EventType.ACQUIRE_TOKEN_SUCCESS, _utils_BrowserConstants_mjs__WEBPACK_IMPORTED_MODULE_6__.InteractionType.Popup, result);\n            atPopupMeasurement.add({\n                accessTokenSize: result.accessToken.length,\n                idTokenSize: result.idToken.length,\n            });\n            atPopupMeasurement.end({\n                success: true,\n                requestId: result.requestId,\n            });\n            return result;\n        }\n        catch (e) {\n            const error = this.nestedAppAuthAdapter.fromBridgeError(e);\n            this.eventHandler.emitEvent(_event_EventType_mjs__WEBPACK_IMPORTED_MODULE_5__.EventType.ACQUIRE_TOKEN_FAILURE, _utils_BrowserConstants_mjs__WEBPACK_IMPORTED_MODULE_6__.InteractionType.Popup, null, e);\n            atPopupMeasurement.end({\n                errorCode: error.errorCode,\n                subErrorCode: error.subError,\n                success: false,\n            });\n            throw error;\n        }\n    }\n    async acquireTokenSilentInternal(request) {\n        this.eventHandler.emitEvent(_event_EventType_mjs__WEBPACK_IMPORTED_MODULE_5__.EventType.ACQUIRE_TOKEN_START, _utils_BrowserConstants_mjs__WEBPACK_IMPORTED_MODULE_6__.InteractionType.Silent, request);\n        const ssoSilentMeasurement = this.performanceClient.startMeasurement(_azure_msal_common__WEBPACK_IMPORTED_MODULE_1__.PerformanceEvents.SsoSilent, request.correlationId);\n        ssoSilentMeasurement?.increment({\n            visibilityChangeCount: 0,\n        });\n        ssoSilentMeasurement?.add({\n            nestedAppAuthRequest: true,\n        });\n        try {\n            const naaRequest = this.nestedAppAuthAdapter.toNaaTokenRequest(request);\n            const reqTimestamp = _azure_msal_common__WEBPACK_IMPORTED_MODULE_1__.TimeUtils.nowSeconds();\n            const response = await this.bridgeProxy.getTokenSilent(naaRequest);\n            const result = this.nestedAppAuthAdapter.fromNaaTokenResponse(naaRequest, response, reqTimestamp);\n            this.operatingContext.setActiveAccount(result.account);\n            this.eventHandler.emitEvent(_event_EventType_mjs__WEBPACK_IMPORTED_MODULE_5__.EventType.ACQUIRE_TOKEN_SUCCESS, _utils_BrowserConstants_mjs__WEBPACK_IMPORTED_MODULE_6__.InteractionType.Silent, result);\n            ssoSilentMeasurement?.add({\n                accessTokenSize: result.accessToken.length,\n                idTokenSize: result.idToken.length,\n            });\n            ssoSilentMeasurement?.end({\n                success: true,\n                requestId: result.requestId,\n            });\n            return result;\n        }\n        catch (e) {\n            const error = this.nestedAppAuthAdapter.fromBridgeError(e);\n            this.eventHandler.emitEvent(_event_EventType_mjs__WEBPACK_IMPORTED_MODULE_5__.EventType.ACQUIRE_TOKEN_FAILURE, _utils_BrowserConstants_mjs__WEBPACK_IMPORTED_MODULE_6__.InteractionType.Silent, null, e);\n            ssoSilentMeasurement?.end({\n                errorCode: error.errorCode,\n                subErrorCode: error.subError,\n                success: false,\n            });\n            throw error;\n        }\n    }\n    async acquireTokenPopup(request) {\n        return this.acquireTokenInteractive(request);\n    }\n    // eslint-disable-next-line @typescript-eslint/no-unused-vars\n    acquireTokenRedirect(request) {\n        throw _error_NestedAppAuthError_mjs__WEBPACK_IMPORTED_MODULE_4__.NestedAppAuthError.createUnsupportedError();\n    }\n    async acquireTokenSilent(silentRequest) {\n        return this.acquireTokenSilentInternal(silentRequest);\n    }\n    // eslint-disable-next-line @typescript-eslint/no-unused-vars\n    acquireTokenByCode(request // eslint-disable-line @typescript-eslint/no-unused-vars\n    ) {\n        throw _error_NestedAppAuthError_mjs__WEBPACK_IMPORTED_MODULE_4__.NestedAppAuthError.createUnsupportedError();\n    }\n    acquireTokenNative(request, apiId, // eslint-disable-line @typescript-eslint/no-unused-vars\n    accountId // eslint-disable-line @typescript-eslint/no-unused-vars\n    ) {\n        throw _error_NestedAppAuthError_mjs__WEBPACK_IMPORTED_MODULE_4__.NestedAppAuthError.createUnsupportedError();\n    }\n    acquireTokenByRefreshToken(commonRequest, // eslint-disable-line @typescript-eslint/no-unused-vars\n    silentRequest // eslint-disable-line @typescript-eslint/no-unused-vars\n    ) {\n        throw _error_NestedAppAuthError_mjs__WEBPACK_IMPORTED_MODULE_4__.NestedAppAuthError.createUnsupportedError();\n    }\n    /**\n     * Adds event callbacks to array\n     * @param callback\n     */\n    addEventCallback(callback) {\n        return this.eventHandler.addEventCallback(callback);\n    }\n    /**\n     * Removes callback with provided id from callback array\n     * @param callbackId\n     */\n    removeEventCallback(callbackId) {\n        this.eventHandler.removeEventCallback(callbackId);\n    }\n    // eslint-disable-next-line @typescript-eslint/no-unused-vars\n    addPerformanceCallback(callback) {\n        throw _error_NestedAppAuthError_mjs__WEBPACK_IMPORTED_MODULE_4__.NestedAppAuthError.createUnsupportedError();\n    }\n    // eslint-disable-next-line @typescript-eslint/no-unused-vars\n    removePerformanceCallback(callbackId) {\n        throw _error_NestedAppAuthError_mjs__WEBPACK_IMPORTED_MODULE_4__.NestedAppAuthError.createUnsupportedError();\n    }\n    enableAccountStorageEvents() {\n        throw _error_NestedAppAuthError_mjs__WEBPACK_IMPORTED_MODULE_4__.NestedAppAuthError.createUnsupportedError();\n    }\n    disableAccountStorageEvents() {\n        throw _error_NestedAppAuthError_mjs__WEBPACK_IMPORTED_MODULE_4__.NestedAppAuthError.createUnsupportedError();\n    }\n    // eslint-disable-next-line @typescript-eslint/no-unused-vars\n    getAccount(accountFilter) {\n        throw _error_NestedAppAuthError_mjs__WEBPACK_IMPORTED_MODULE_4__.NestedAppAuthError.createUnsupportedError();\n        // TODO: Look at standard implementation\n    }\n    getAccountByHomeId(homeAccountId) {\n        const currentAccount = this.operatingContext.getActiveAccount();\n        if (currentAccount !== undefined) {\n            if (currentAccount.homeAccountId === homeAccountId) {\n                return this.nestedAppAuthAdapter.fromNaaAccountInfo(currentAccount);\n            }\n            else {\n                return null;\n            }\n        }\n        else {\n            return null;\n        }\n    }\n    getAccountByLocalId(localId) {\n        const currentAccount = this.operatingContext.getActiveAccount();\n        if (currentAccount !== undefined) {\n            if (currentAccount.localAccountId === localId) {\n                return this.nestedAppAuthAdapter.fromNaaAccountInfo(currentAccount);\n            }\n            else {\n                return null;\n            }\n        }\n        else {\n            return null;\n        }\n    }\n    getAccountByUsername(userName) {\n        const currentAccount = this.operatingContext.getActiveAccount();\n        if (currentAccount !== undefined) {\n            if (currentAccount.username === userName) {\n                return this.nestedAppAuthAdapter.fromNaaAccountInfo(currentAccount);\n            }\n            else {\n                return null;\n            }\n        }\n        else {\n            return null;\n        }\n    }\n    getAllAccounts() {\n        const currentAccount = this.operatingContext.getActiveAccount();\n        if (currentAccount !== undefined) {\n            return [\n                this.nestedAppAuthAdapter.fromNaaAccountInfo(currentAccount),\n            ];\n        }\n        else {\n            return [];\n        }\n    }\n    handleRedirectPromise(hash // eslint-disable-line @typescript-eslint/no-unused-vars\n    ) {\n        throw _error_NestedAppAuthError_mjs__WEBPACK_IMPORTED_MODULE_4__.NestedAppAuthError.createUnsupportedError();\n    }\n    loginPopup(request // eslint-disable-line @typescript-eslint/no-unused-vars\n    ) {\n        if (request !== undefined) {\n            return this.acquireTokenInteractive(request);\n        }\n        else {\n            throw _error_NestedAppAuthError_mjs__WEBPACK_IMPORTED_MODULE_4__.NestedAppAuthError.createUnsupportedError();\n        }\n    }\n    // eslint-disable-next-line @typescript-eslint/no-unused-vars\n    loginRedirect(request) {\n        throw _error_NestedAppAuthError_mjs__WEBPACK_IMPORTED_MODULE_4__.NestedAppAuthError.createUnsupportedError();\n    }\n    // eslint-disable-next-line @typescript-eslint/no-unused-vars\n    logout(logoutRequest) {\n        throw _error_NestedAppAuthError_mjs__WEBPACK_IMPORTED_MODULE_4__.NestedAppAuthError.createUnsupportedError();\n    }\n    logoutRedirect(logoutRequest // eslint-disable-line @typescript-eslint/no-unused-vars\n    ) {\n        throw _error_NestedAppAuthError_mjs__WEBPACK_IMPORTED_MODULE_4__.NestedAppAuthError.createUnsupportedError();\n    }\n    logoutPopup(logoutRequest // eslint-disable-line @typescript-eslint/no-unused-vars\n    ) {\n        throw _error_NestedAppAuthError_mjs__WEBPACK_IMPORTED_MODULE_4__.NestedAppAuthError.createUnsupportedError();\n    }\n    ssoSilent(\n    // eslint-disable-next-line @typescript-eslint/no-unused-vars\n    request) {\n        return this.acquireTokenSilentInternal(request);\n    }\n    getTokenCache() {\n        throw _error_NestedAppAuthError_mjs__WEBPACK_IMPORTED_MODULE_4__.NestedAppAuthError.createUnsupportedError();\n    }\n    /**\n     * Returns the logger instance\n     */\n    getLogger() {\n        return this.logger;\n    }\n    /**\n     * Replaces the default logger set in configurations with new Logger with new configurations\n     * @param logger Logger instance\n     */\n    setLogger(logger) {\n        this.logger = logger;\n    }\n    // eslint-disable-next-line @typescript-eslint/no-unused-vars\n    setActiveAccount(account) {\n        /*\n         * StandardController uses this to allow the developer to set the active account\n         * in the nested app auth scenario the active account is controlled by the app hosting the nested app\n         */\n        this.logger.warning(\"nestedAppAuth does not support setActiveAccount\");\n        return;\n    }\n    getActiveAccount() {\n        const currentAccount = this.operatingContext.getActiveAccount();\n        if (currentAccount !== undefined) {\n            return this.nestedAppAuthAdapter.fromNaaAccountInfo(currentAccount);\n        }\n        else {\n            return null;\n        }\n    }\n    // eslint-disable-next-line @typescript-eslint/no-unused-vars\n    initializeWrapperLibrary(sku, version) {\n        /*\n         * Standard controller uses this to set the sku and version of the wrapper library in the storage\n         * we do nothing here\n         */\n        return;\n    }\n    // eslint-disable-next-line @typescript-eslint/no-unused-vars\n    setNavigationClient(navigationClient) {\n        this.logger.warning(\"setNavigationClient is not supported in nested app auth\");\n    }\n    getConfiguration() {\n        return this.config;\n    }\n    isBrowserEnv() {\n        return this.operatingContext.isBrowserEnvironment();\n    }\n    getBrowserCrypto() {\n        return this.browserCrypto;\n    }\n    getPerformanceClient() {\n        throw _error_NestedAppAuthError_mjs__WEBPACK_IMPORTED_MODULE_4__.NestedAppAuthError.createUnsupportedError();\n    }\n    getRedirectResponse() {\n        throw _error_NestedAppAuthError_mjs__WEBPACK_IMPORTED_MODULE_4__.NestedAppAuthError.createUnsupportedError();\n    }\n    preflightBrowserEnvironmentCheck(interactionType, // eslint-disable-line @typescript-eslint/no-unused-vars\n    setInteractionInProgress // eslint-disable-line @typescript-eslint/no-unused-vars\n    ) {\n        throw _error_NestedAppAuthError_mjs__WEBPACK_IMPORTED_MODULE_4__.NestedAppAuthError.createUnsupportedError();\n    }\n    // eslint-disable-next-line @typescript-eslint/no-unused-vars\n    async clearCache(logoutRequest) {\n        throw _error_NestedAppAuthError_mjs__WEBPACK_IMPORTED_MODULE_4__.NestedAppAuthError.createUnsupportedError();\n    }\n    // eslint-disable-next-line @typescript-eslint/no-unused-vars\n    async hydrateCache(\n    // eslint-disable-next-line @typescript-eslint/no-unused-vars\n    result, \n    // eslint-disable-next-line @typescript-eslint/no-unused-vars\n    request) {\n        throw _error_NestedAppAuthError_mjs__WEBPACK_IMPORTED_MODULE_4__.NestedAppAuthError.createUnsupportedError();\n    }\n}\n\n\n//# sourceMappingURL=NestedAppAuthController.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/@azure/msal-browser/dist/controllers/NestedAppAuthController.mjs\n"));

/***/ }),

/***/ "./node_modules/@azure/msal-browser/dist/error/NestedAppAuthError.mjs":
/*!****************************************************************************!*\
  !*** ./node_modules/@azure/msal-browser/dist/error/NestedAppAuthError.mjs ***!
  \****************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   NestedAppAuthError: function() { return /* binding */ NestedAppAuthError; },\n/* harmony export */   NestedAppAuthErrorMessage: function() { return /* binding */ NestedAppAuthErrorMessage; }\n/* harmony export */ });\n/* harmony import */ var _azure_msal_common__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @azure/msal-common */ \"./node_modules/@azure/msal-common/dist/index.mjs\");\n/*! @azure/msal-browser v3.6.0 2023-12-01 */\n\n\n\n/*\n * Copyright (c) Microsoft Corporation. All rights reserved.\n * Licensed under the MIT License.\n */\n/**\n * NestedAppAuthErrorMessage class containing string constants used by error codes and messages.\n */\nconst NestedAppAuthErrorMessage = {\n    unsupportedMethod: {\n        code: \"unsupported_method\",\n        desc: \"The PKCE code challenge and verifier could not be generated.\",\n    },\n};\nclass NestedAppAuthError extends _azure_msal_common__WEBPACK_IMPORTED_MODULE_0__.AuthError {\n    constructor(errorCode, errorMessage) {\n        super(errorCode, errorMessage);\n        Object.setPrototypeOf(this, NestedAppAuthError.prototype);\n        this.name = \"NestedAppAuthError\";\n    }\n    static createUnsupportedError() {\n        return new NestedAppAuthError(NestedAppAuthErrorMessage.unsupportedMethod.code, NestedAppAuthErrorMessage.unsupportedMethod.desc);\n    }\n}\n\n\n//# sourceMappingURL=NestedAppAuthError.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/@azure/msal-browser/dist/error/NestedAppAuthError.mjs\n"));

/***/ }),

/***/ "./node_modules/@azure/msal-browser/dist/naa/BridgeError.mjs":
/*!*******************************************************************!*\
  !*** ./node_modules/@azure/msal-browser/dist/naa/BridgeError.mjs ***!
  \*******************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   isBridgeError: function() { return /* binding */ isBridgeError; }\n/* harmony export */ });\n/*! @azure/msal-browser v3.6.0 2023-12-01 */\n\n/*\n * Copyright (c) Microsoft Corporation. All rights reserved.\n * Licensed under the MIT License.\n */\nfunction isBridgeError(error) {\n    return error.status !== undefined;\n}\n\n\n//# sourceMappingURL=BridgeError.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9ub2RlX21vZHVsZXMvQGF6dXJlL21zYWwtYnJvd3Nlci9kaXN0L25hYS9CcmlkZ2VFcnJvci5tanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBO0FBQ2E7QUFDYjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFeUI7QUFDekIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vbm9kZV9tb2R1bGVzL0BhenVyZS9tc2FsLWJyb3dzZXIvZGlzdC9uYWEvQnJpZGdlRXJyb3IubWpzPzExZDIiXSwic291cmNlc0NvbnRlbnQiOlsiLyohIEBhenVyZS9tc2FsLWJyb3dzZXIgdjMuNi4wIDIwMjMtMTItMDEgKi9cbid1c2Ugc3RyaWN0Jztcbi8qXG4gKiBDb3B5cmlnaHQgKGMpIE1pY3Jvc29mdCBDb3Jwb3JhdGlvbi4gQWxsIHJpZ2h0cyByZXNlcnZlZC5cbiAqIExpY2Vuc2VkIHVuZGVyIHRoZSBNSVQgTGljZW5zZS5cbiAqL1xuZnVuY3Rpb24gaXNCcmlkZ2VFcnJvcihlcnJvcikge1xuICAgIHJldHVybiBlcnJvci5zdGF0dXMgIT09IHVuZGVmaW5lZDtcbn1cblxuZXhwb3J0IHsgaXNCcmlkZ2VFcnJvciB9O1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9QnJpZGdlRXJyb3IubWpzLm1hcFxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///./node_modules/@azure/msal-browser/dist/naa/BridgeError.mjs\n"));

/***/ }),

/***/ "./node_modules/@azure/msal-browser/dist/naa/BridgeStatusCode.mjs":
/*!************************************************************************!*\
  !*** ./node_modules/@azure/msal-browser/dist/naa/BridgeStatusCode.mjs ***!
  \************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   BridgeStatusCode: function() { return /* binding */ BridgeStatusCode; }\n/* harmony export */ });\n/*! @azure/msal-browser v3.6.0 2023-12-01 */\n\n/*\n * Copyright (c) Microsoft Corporation. All rights reserved.\n * Licensed under the MIT License.\n */\nvar BridgeStatusCode;\n(function (BridgeStatusCode) {\n    BridgeStatusCode[\"USER_INTERACTION_REQUIRED\"] = \"USER_INTERACTION_REQUIRED\";\n    BridgeStatusCode[\"USER_CANCEL\"] = \"USER_CANCEL\";\n    BridgeStatusCode[\"NO_NETWORK\"] = \"NO_NETWORK\";\n    BridgeStatusCode[\"TRANSIENT_ERROR\"] = \"TRANSIENT_ERROR\";\n    BridgeStatusCode[\"PERSISTENT_ERROR\"] = \"PERSISTENT_ERROR\";\n    BridgeStatusCode[\"DISABLED\"] = \"DISABLED\";\n    BridgeStatusCode[\"ACCOUNT_UNAVAILABLE\"] = \"ACCOUNT_UNAVAILABLE\";\n    BridgeStatusCode[\"NESTED_APP_AUTH_UNAVAILABLE\"] = \"NESTED_APP_AUTH_UNAVAILABLE\";\n})(BridgeStatusCode || (BridgeStatusCode = {}));\n\n\n//# sourceMappingURL=BridgeStatusCode.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9ub2RlX21vZHVsZXMvQGF6dXJlL21zYWwtYnJvd3Nlci9kaXN0L25hYS9CcmlkZ2VTdGF0dXNDb2RlLm1qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUE7QUFDYTtBQUNiO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxDQUFDLDRDQUE0Qzs7QUFFakI7QUFDNUIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vbm9kZV9tb2R1bGVzL0BhenVyZS9tc2FsLWJyb3dzZXIvZGlzdC9uYWEvQnJpZGdlU3RhdHVzQ29kZS5tanM/ZGEwYyJdLCJzb3VyY2VzQ29udGVudCI6WyIvKiEgQGF6dXJlL21zYWwtYnJvd3NlciB2My42LjAgMjAyMy0xMi0wMSAqL1xuJ3VzZSBzdHJpY3QnO1xuLypcbiAqIENvcHlyaWdodCAoYykgTWljcm9zb2Z0IENvcnBvcmF0aW9uLiBBbGwgcmlnaHRzIHJlc2VydmVkLlxuICogTGljZW5zZWQgdW5kZXIgdGhlIE1JVCBMaWNlbnNlLlxuICovXG52YXIgQnJpZGdlU3RhdHVzQ29kZTtcbihmdW5jdGlvbiAoQnJpZGdlU3RhdHVzQ29kZSkge1xuICAgIEJyaWRnZVN0YXR1c0NvZGVbXCJVU0VSX0lOVEVSQUNUSU9OX1JFUVVJUkVEXCJdID0gXCJVU0VSX0lOVEVSQUNUSU9OX1JFUVVJUkVEXCI7XG4gICAgQnJpZGdlU3RhdHVzQ29kZVtcIlVTRVJfQ0FOQ0VMXCJdID0gXCJVU0VSX0NBTkNFTFwiO1xuICAgIEJyaWRnZVN0YXR1c0NvZGVbXCJOT19ORVRXT1JLXCJdID0gXCJOT19ORVRXT1JLXCI7XG4gICAgQnJpZGdlU3RhdHVzQ29kZVtcIlRSQU5TSUVOVF9FUlJPUlwiXSA9IFwiVFJBTlNJRU5UX0VSUk9SXCI7XG4gICAgQnJpZGdlU3RhdHVzQ29kZVtcIlBFUlNJU1RFTlRfRVJST1JcIl0gPSBcIlBFUlNJU1RFTlRfRVJST1JcIjtcbiAgICBCcmlkZ2VTdGF0dXNDb2RlW1wiRElTQUJMRURcIl0gPSBcIkRJU0FCTEVEXCI7XG4gICAgQnJpZGdlU3RhdHVzQ29kZVtcIkFDQ09VTlRfVU5BVkFJTEFCTEVcIl0gPSBcIkFDQ09VTlRfVU5BVkFJTEFCTEVcIjtcbiAgICBCcmlkZ2VTdGF0dXNDb2RlW1wiTkVTVEVEX0FQUF9BVVRIX1VOQVZBSUxBQkxFXCJdID0gXCJORVNURURfQVBQX0FVVEhfVU5BVkFJTEFCTEVcIjtcbn0pKEJyaWRnZVN0YXR1c0NvZGUgfHwgKEJyaWRnZVN0YXR1c0NvZGUgPSB7fSkpO1xuXG5leHBvcnQgeyBCcmlkZ2VTdGF0dXNDb2RlIH07XG4vLyMgc291cmNlTWFwcGluZ1VSTD1CcmlkZ2VTdGF0dXNDb2RlLm1qcy5tYXBcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///./node_modules/@azure/msal-browser/dist/naa/BridgeStatusCode.mjs\n"));

/***/ }),

/***/ "./node_modules/@azure/msal-browser/dist/naa/mapping/NestedAppAuthAdapter.mjs":
/*!************************************************************************************!*\
  !*** ./node_modules/@azure/msal-browser/dist/naa/mapping/NestedAppAuthAdapter.mjs ***!
  \************************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   NestedAppAuthAdapter: function() { return /* binding */ NestedAppAuthAdapter; }\n/* harmony export */ });\n/* harmony import */ var _azure_msal_common__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @azure/msal-common */ \"./node_modules/@azure/msal-common/dist/index.mjs\");\n/* harmony import */ var _BridgeError_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../BridgeError.mjs */ \"./node_modules/@azure/msal-browser/dist/naa/BridgeError.mjs\");\n/* harmony import */ var _BridgeStatusCode_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../BridgeStatusCode.mjs */ \"./node_modules/@azure/msal-browser/dist/naa/BridgeStatusCode.mjs\");\n/*! @azure/msal-browser v3.6.0 2023-12-01 */\n\n\n\n\n\n/*\n * Copyright (c) Microsoft Corporation. All rights reserved.\n * Licensed under the MIT License.\n */\nclass NestedAppAuthAdapter {\n    constructor(clientId, clientCapabilities, crypto, logger) {\n        this.clientId = clientId;\n        this.clientCapabilities = clientCapabilities;\n        this.crypto = crypto;\n        this.logger = logger;\n    }\n    toNaaTokenRequest(request) {\n        let extraParams;\n        if (request.extraQueryParameters === undefined) {\n            extraParams = new Map();\n        }\n        else {\n            extraParams = new Map(Object.entries(request.extraQueryParameters));\n        }\n        const requestBuilder = new _azure_msal_common__WEBPACK_IMPORTED_MODULE_0__.RequestParameterBuilder();\n        const claims = requestBuilder.addClientCapabilitiesToClaims(request.claims, this.clientCapabilities);\n        const tokenRequest = {\n            userObjectId: request.account?.homeAccountId,\n            clientId: this.clientId,\n            authority: request.authority,\n            scope: request.scopes.join(\" \"),\n            correlationId: request.correlationId !== undefined\n                ? request.correlationId\n                : this.crypto.createNewGuid(),\n            nonce: request.nonce,\n            claims: !_azure_msal_common__WEBPACK_IMPORTED_MODULE_0__.StringUtils.isEmptyObj(claims) ? claims : undefined,\n            state: request.state,\n            authenticationScheme: request.authenticationScheme || _azure_msal_common__WEBPACK_IMPORTED_MODULE_0__.AuthenticationScheme.BEARER,\n            extraParameters: extraParams,\n        };\n        return tokenRequest;\n    }\n    fromNaaTokenResponse(request, response, reqTimestamp) {\n        if (!response.id_token || !response.access_token) {\n            throw (0,_azure_msal_common__WEBPACK_IMPORTED_MODULE_0__.createClientAuthError)(_azure_msal_common__WEBPACK_IMPORTED_MODULE_0__.ClientAuthErrorCodes.nullOrEmptyToken);\n        }\n        const expiresOn = new Date((reqTimestamp + (response.expires_in || 0)) * 1000);\n        const idTokenClaims = _azure_msal_common__WEBPACK_IMPORTED_MODULE_0__.AuthToken.extractTokenClaims(response.id_token, this.crypto.base64Decode);\n        const account = this.fromNaaAccountInfo(response.account, idTokenClaims);\n        const authenticationResult = {\n            authority: response.authority || account.environment,\n            uniqueId: account.localAccountId,\n            tenantId: account.tenantId,\n            scopes: response.scope.split(\" \"),\n            account,\n            idToken: response.id_token !== undefined ? response.id_token : \"\",\n            idTokenClaims,\n            accessToken: response.access_token,\n            fromCache: true,\n            expiresOn: expiresOn,\n            tokenType: request.authenticationScheme || _azure_msal_common__WEBPACK_IMPORTED_MODULE_0__.AuthenticationScheme.BEARER,\n            correlationId: request.correlationId,\n            extExpiresOn: expiresOn,\n            state: request.state,\n        };\n        return authenticationResult;\n    }\n    /*\n     *  export type AccountInfo = {\n     *     homeAccountId: string;\n     *     environment: string;\n     *     tenantId: string;\n     *     username: string;\n     *     localAccountId: string;\n     *     name?: string;\n     *     idToken?: string;\n     *     idTokenClaims?: TokenClaims & {\n     *         [key: string]:\n     *             | string\n     *             | number\n     *             | string[]\n     *             | object\n     *             | undefined\n     *             | unknown;\n     *     };\n     *     nativeAccountId?: string;\n     *     authorityType?: string;\n     * };\n     */\n    fromNaaAccountInfo(fromAccount, idTokenClaims) {\n        const effectiveIdTokenClaims = idTokenClaims || fromAccount.idTokenClaims;\n        const localAccountId = fromAccount.localAccountId ||\n            effectiveIdTokenClaims?.oid ||\n            effectiveIdTokenClaims?.sub ||\n            \"\";\n        const tenantId = fromAccount.tenantId || effectiveIdTokenClaims?.tid || \"\";\n        const homeAccountId = fromAccount.homeAccountId || `${localAccountId}.${tenantId}`;\n        const username = fromAccount.username ||\n            effectiveIdTokenClaims?.preferred_username ||\n            \"\";\n        const name = fromAccount.name || effectiveIdTokenClaims?.name;\n        const account = {\n            homeAccountId,\n            environment: fromAccount.environment,\n            tenantId,\n            username,\n            localAccountId,\n            name,\n            idToken: fromAccount.idToken,\n            idTokenClaims: effectiveIdTokenClaims,\n        };\n        return account;\n    }\n    /**\n     *\n     * @param error BridgeError\n     * @returns AuthError, ClientAuthError, ClientConfigurationError, ServerError, InteractionRequiredError\n     */\n    fromBridgeError(error) {\n        if ((0,_BridgeError_mjs__WEBPACK_IMPORTED_MODULE_1__.isBridgeError)(error)) {\n            switch (error.status) {\n                case _BridgeStatusCode_mjs__WEBPACK_IMPORTED_MODULE_2__.BridgeStatusCode.USER_CANCEL:\n                    return new _azure_msal_common__WEBPACK_IMPORTED_MODULE_0__.ClientAuthError(_azure_msal_common__WEBPACK_IMPORTED_MODULE_0__.ClientAuthErrorCodes.userCanceled);\n                case _BridgeStatusCode_mjs__WEBPACK_IMPORTED_MODULE_2__.BridgeStatusCode.NO_NETWORK:\n                    return new _azure_msal_common__WEBPACK_IMPORTED_MODULE_0__.ClientAuthError(_azure_msal_common__WEBPACK_IMPORTED_MODULE_0__.ClientAuthErrorCodes.noNetworkConnectivity);\n                case _BridgeStatusCode_mjs__WEBPACK_IMPORTED_MODULE_2__.BridgeStatusCode.ACCOUNT_UNAVAILABLE:\n                    return new _azure_msal_common__WEBPACK_IMPORTED_MODULE_0__.ClientAuthError(_azure_msal_common__WEBPACK_IMPORTED_MODULE_0__.ClientAuthErrorCodes.noAccountFound);\n                case _BridgeStatusCode_mjs__WEBPACK_IMPORTED_MODULE_2__.BridgeStatusCode.DISABLED:\n                    return new _azure_msal_common__WEBPACK_IMPORTED_MODULE_0__.ClientAuthError(_azure_msal_common__WEBPACK_IMPORTED_MODULE_0__.ClientAuthErrorCodes.nestedAppAuthBridgeDisabled);\n                case _BridgeStatusCode_mjs__WEBPACK_IMPORTED_MODULE_2__.BridgeStatusCode.NESTED_APP_AUTH_UNAVAILABLE:\n                    return new _azure_msal_common__WEBPACK_IMPORTED_MODULE_0__.ClientAuthError(error.code ||\n                        _azure_msal_common__WEBPACK_IMPORTED_MODULE_0__.ClientAuthErrorCodes.nestedAppAuthBridgeDisabled, error.description);\n                case _BridgeStatusCode_mjs__WEBPACK_IMPORTED_MODULE_2__.BridgeStatusCode.TRANSIENT_ERROR:\n                case _BridgeStatusCode_mjs__WEBPACK_IMPORTED_MODULE_2__.BridgeStatusCode.PERSISTENT_ERROR:\n                    return new _azure_msal_common__WEBPACK_IMPORTED_MODULE_0__.ServerError(error.code, error.description);\n                case _BridgeStatusCode_mjs__WEBPACK_IMPORTED_MODULE_2__.BridgeStatusCode.USER_INTERACTION_REQUIRED:\n                    return new _azure_msal_common__WEBPACK_IMPORTED_MODULE_0__.InteractionRequiredAuthError(error.code, error.description);\n                default:\n                    return new _azure_msal_common__WEBPACK_IMPORTED_MODULE_0__.AuthError(error.code, error.description);\n            }\n        }\n        else {\n            return new _azure_msal_common__WEBPACK_IMPORTED_MODULE_0__.AuthError(\"unknown_error\", \"An unknown error occurred\");\n        }\n    }\n}\n\n\n//# sourceMappingURL=NestedAppAuthAdapter.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/@azure/msal-browser/dist/naa/mapping/NestedAppAuthAdapter.mjs\n"));

/***/ })

}]);