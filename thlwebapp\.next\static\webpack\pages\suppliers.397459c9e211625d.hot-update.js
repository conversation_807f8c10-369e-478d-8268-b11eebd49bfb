"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/suppliers",{

/***/ "./utils/exportExcel.js":
/*!******************************!*\
  !*** ./utils/exportExcel.js ***!
  \******************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var exceljs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! exceljs */ \"./node_modules/exceljs/dist/exceljs.min.js\");\n/* harmony import */ var exceljs__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(exceljs__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react_toastify__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react-toastify */ \"./node_modules/react-toastify/dist/react-toastify.esm.mjs\");\n/* harmony import */ var react_toastify_dist_ReactToastify_css__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-toastify/dist/ReactToastify.css */ \"./node_modules/react-toastify/dist/ReactToastify.css\");\n/* harmony import */ var react_toastify_dist_ReactToastify_css__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react_toastify_dist_ReactToastify_css__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _services_apiConfig__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/services/apiConfig */ \"./services/apiConfig.js\");\n\n\n\n\n// todo:move the supplierCode column from the excel file to first column\nconst exportExcel = async function(data, isInternal, token, company, userData, prophet_id, requestor_email, isMultiple) {\n    let isProductExtract = arguments.length > 8 && arguments[8] !== void 0 ? arguments[8] : false, onProductSubmit = arguments.length > 9 && arguments[9] !== void 0 ? arguments[9] : false, productEmailParagraph = arguments.length > 10 && arguments[10] !== void 0 ? arguments[10] : \"\", productEmailCommentPlaceholder = arguments.length > 11 && arguments[11] !== void 0 ? arguments[11] : \"\", request_no = arguments.length > 12 && arguments[12] !== void 0 ? arguments[12] : \"\", varietyRequest = arguments.length > 13 && arguments[13] !== void 0 ? arguments[13] : false;\n    let serverAddress = _services_apiConfig__WEBPACK_IMPORTED_MODULE_3__.apiConfig.serverAddress;\n    let isEmergencyAndFinanceNotCompleteObj = [];\n    let supplierNames = [];\n    const workbook = new (exceljs__WEBPACK_IMPORTED_MODULE_0___default().Workbook)();\n    if (isInternal) {\n        data.forEach((sheetData, index1)=>{\n            if (sheetData.length === 0) {\n                console.error(\"sheetData is empty for index:\", index1);\n                return;\n            }\n            const sheetName = sheetData[0] || \"Sheet\".concat(index1 + 1);\n            let worksheet;\n            if (index1 < 9) {\n                if (Array.isArray(sheetName)) {\n                    const actualSheetName = sheetName[0];\n                    worksheet = workbook.addWorksheet(actualSheetName);\n                } else {\n                    worksheet = workbook.addWorksheet(sheetName);\n                }\n            }\n            if (sheetData.length > 1) {\n                let headers;\n                if (index1 < 9) {\n                    headers = Object.keys(sheetData[1]);\n                    worksheet.addRow(headers);\n                }\n                sheetData.slice(1).forEach((row, internalIndex)=>{\n                    if (index1 < 9) {\n                        const rowData = headers.map((header)=>row[header] || \"\");\n                        if (index1 != 4 && index1 != 3) {\n                            worksheet.addRow(rowData);\n                        } else if (index1 == 4) {\n                            worksheet.addRow(rowData);\n                        } else if (index1 == 3 && rowData[1] != \"\") {\n                            worksheet.addRow(rowData);\n                        }\n                    }\n                    if (index1 === 9) {\n                        supplierNames.push({\n                            supplierName: row === null || row === void 0 ? void 0 : row.supplierName,\n                            supplierCode: row === null || row === void 0 ? void 0 : row.supplierCode\n                        });\n                        if (row.isEmergencyAndFinanceNotComplete) {\n                            let isEmergencyAndFinanceNotCompleteSupplier = {\n                                isEmergencyAndFinanceNotComplete: row.isEmergencyAndFinanceNotComplete,\n                                supplierName: row.supplierName\n                            };\n                            isEmergencyAndFinanceNotCompleteObj.push(isEmergencyAndFinanceNotCompleteSupplier);\n                        }\n                        fetch(\"\".concat(serverAddress, \"suppliers/update-supplier/\").concat(row.id), {\n                            method: \"PUT\",\n                            headers: {\n                                Accept: \"application/json\",\n                                \"Content-Type\": \"application/json\"\n                            },\n                            body: JSON.stringify({\n                                sectionName: \"updateStatus\",\n                                type: \"exportExcel\",\n                                status: 5,\n                                exported: true,\n                                updated_date: new Date().toISOString(),\n                                company_name: row.supplierName,\n                                to: \"Internal\"\n                            })\n                        }).then((res)=>{\n                            if (res.status === 200) {\n                                return res.json();\n                            }\n                            // if (res.status === 401){\n                            //   toast.error(\"Your session has expired. Please log in again.\");\n                            //   setTimeout(() => {\n                            //     const redirectUrl = `/login?redirect=${encodeURIComponent(\n                            //       window.location.pathname\n                            //     )}`;\n                            //     logoutHandler(instance, redirectUrl);\n                            //   }, 3000);\n                            //   return null;\n                            // }\n                            return Promise.reject(res);\n                        }).then((json)=>{\n                            if (json.status == 200) {\n                                return true;\n                            }\n                        }).catch((error)=>{\n                            console.log(error);\n                        });\n                    }\n                });\n            }\n        });\n    } else if (isProductExtract) {\n        if (data.length === 0) {\n            console.error(\"sheetData is empty for index:\");\n            return;\n        }\n        data.forEach((sheetData)=>{\n            var _Object;\n            const sheetName = sheetData[0] || \"Sheet\".concat(index + 1);\n            let worksheet;\n            if (Array.isArray(sheetName)) {\n                const actualSheetName = sheetName[0];\n                worksheet = workbook.addWorksheet(actualSheetName);\n            } else {\n                worksheet = workbook.addWorksheet(sheetName);\n            }\n            // console.log(\"Object.keys(sheetData[1])\",Object?.keys(sheetData[1])? Object?.keys(sheetData[1]) : 'not there');\n            let headers = ((_Object = Object) === null || _Object === void 0 ? void 0 : _Object.keys(sheetData[1])) ? Object.keys(sheetData[1]) : \"null\";\n            if (isMultiple) {\n                headers = headers.slice(0, -1);\n            }\n            worksheet.addRow(headers);\n            sheetData.slice(1).forEach((row, internalIndex)=>{\n                let rowData;\n                rowData = headers.map((header)=>row[header] || \"\");\n                worksheet.addRow(rowData);\n            });\n        });\n    } else {\n        if (data.length === 0) {\n            console.error(\"sheetData is empty for index:\");\n            return;\n        }\n        data.forEach((row, index1)=>{\n            const sheetName = row[0] || \"Sheet\".concat(index1 + 1);\n            let worksheet;\n            if (Array.isArray(sheetName)) {\n                const actualSheetName = sheetName[0];\n                worksheet = workbook.addWorksheet(actualSheetName);\n            } else {\n                worksheet = workbook.addWorksheet(sheetName);\n            }\n            let headers;\n            if (index1 == 1) {\n                headers = Object.keys(row[1]).slice(0, -2);\n            } else {\n                headers = Object.keys(row[1]);\n            }\n            worksheet.addRow(headers);\n            for(let i = 1; i < row.length; i++){\n                if (index1 == 0 && i > 0) {\n                    supplierNames.push({\n                        supplierName: row[i][\"Supplier name\"],\n                        supplierCode: row[i][\"Supplier code\"]\n                    });\n                }\n                let rowData;\n                if (index1 == 1) {\n                    rowData = headers.map((header)=>row[i][header] || \"\").slice(0, -2);\n                } else {\n                    rowData = headers.map((header)=>row[i][header] || \"\");\n                }\n                worksheet.addRow(rowData);\n                if (row[i].isEmergencyAndFinanceNotComplete) {\n                    let isEmergencyAndFinanceNotCompleteSupplier = {\n                        isEmergencyAndFinanceNotComplete: row[i].isEmergencyAndFinanceNotComplete,\n                        supplierName: row[i][\"Supplier name\"]\n                    };\n                    isEmergencyAndFinanceNotCompleteObj.push(isEmergencyAndFinanceNotCompleteSupplier);\n                }\n                if (index1 == 1) {\n                    fetch(\"\".concat(serverAddress, \"suppliers/update-supplier/\").concat(row[i].id), {\n                        method: \"PUT\",\n                        headers: {\n                            Accept: \"application/json\",\n                            \"Content-Type\": \"application/json\"\n                        },\n                        credentials: \"include\",\n                        body: JSON.stringify({\n                            sectionName: \"updateStatus\",\n                            type: \"exportExcel\",\n                            status: 5,\n                            exported: true,\n                            updated_date: new Date().toISOString(),\n                            company_name: row[\"Supplier name\"],\n                            to: \"ISS\"\n                        })\n                    }).then((res)=>{\n                        if (res.status === 200) {\n                            return res.json();\n                        }\n                        // if (res.status === 401){\n                        //   toast.error(\"Your session has expired. Please log in again.\");\n                        //   setTimeout(() => {\n                        //     const redirectUrl = `/login?redirect=${encodeURIComponent(\n                        //       window.location.pathname\n                        //     )}`;\n                        //     logoutHandler(instance, redirectUrl);\n                        //   }, 3000);\n                        //   return null;\n                        // }\n                        return Promise.reject(res);\n                    }).then((json)=>{\n                        if (json.status == 200) {\n                            return true;\n                        }\n                    }).catch((error)=>{\n                        console.log(error);\n                    });\n                }\n            }\n        });\n    }\n    const buffer = await workbook.xlsx.writeBuffer();\n    const blob = new Blob([\n        buffer\n    ], {\n        type: \"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet\"\n    });\n    const blobUrl = URL.createObjectURL(blob);\n    const a = document.createElement(\"a\");\n    a.style.display = \"none\";\n    a.href = blobUrl;\n    const now = new Date();\n    const timestamp = \"\".concat(now.getFullYear(), \"-\").concat((now.getMonth() + 1).toString().padStart(2, \"0\"), \"-\").concat(now.getDate().toString().padStart(2, \"0\"), \"_\").concat(now.getHours().toString().padStart(2, \"0\"), \"-\").concat(now.getMinutes().toString().padStart(2, \"0\"), \"-\").concat(now.getSeconds().toString().padStart(2, \"0\"));\n    if (isInternal) {\n        a.download = \"internal_export_\".concat(timestamp, \"_\").concat(data[0].length - 1 === 1 ? \"S\" : \"G\", \".xlsx\");\n    } else if (isProductExtract) {\n        if (request_no && !varietyRequest) {\n            a.download = \"\".concat(request_no, \"_product_export.xlsx\");\n        } else if (request_no && varietyRequest) {\n            a.download = \"\".concat(request_no, \"_export.xlsx\");\n        } else {\n            a.download = \"product_export.xlsx\";\n        }\n    } else {\n        a.download = \"iss_export_\".concat(timestamp, \"_\").concat(data.length === 1 ? \"S\" : \"G\", \".xlsx\");\n    }\n    let fileName;\n    console.log(\"fle name\", fileName);\n    if (isInternal) {\n        fileName = \"internal_export_\".concat(timestamp, \"_\").concat(data[0].length - 1 === 1 ? \"S\" : \"G\", \".xlsx\");\n    } else if (isProductExtract && !varietyRequest) {\n        fileName = request_no ? \"\".concat(request_no, \"_product_export.xlsx\") : \"product_export.xlsx\";\n    } else if (varietyRequest) {\n        fileName = request_no ? \"\".concat(request_no, \"_NV_export.xlsx\") : \"NV_export.xlsx\";\n    } else {\n        fileName = \"iss_export_\".concat(timestamp, \"_\").concat(data.length === 1 ? \"S\" : \"G\", \".xlsx\");\n    }\n    console.log(\"fle name --- \\n\", fileName);\n    document.body.appendChild(a);\n    a.click();\n    // Clean up\n    URL.revokeObjectURL(blobUrl);\n    document.body.removeChild(a);\n    react_toastify__WEBPACK_IMPORTED_MODULE_1__.toast.success(\"Request details extracted successfully.\", {\n        toastId: 22,\n        position: \"top-right\",\n        autoClose: 3000,\n        pauseOnHover: false\n    });\n    if (!varietyRequest) {\n        const formData = new FormData();\n        formData.append(\"file\", blob, fileName);\n        formData.append(\"company\", company);\n        formData.append(\"prophet_id\", prophet_id);\n        formData.append(\"name\", userData === null || userData === void 0 ? void 0 : userData.name);\n        formData.append(\"isInternal\", isInternal);\n        formData.append(\"exporterEmail\", userData === null || userData === void 0 ? void 0 : userData.email);\n        formData.append(\"requestorEmail\", requestor_email);\n        formData.append(\"isProductRequest\", isProductExtract);\n        formData.append(\"supplierNames\", JSON.stringify(supplierNames));\n        formData.append(\"onProductSubmit\", onProductSubmit);\n        formData.append(\"request_no\", request_no);\n        formData.append(\"productEmailCommentPlaceholder\", productEmailCommentPlaceholder);\n        formData.append(\"productEmailParagraph\", productEmailParagraph);\n        const serializedData = JSON.stringify(isEmergencyAndFinanceNotCompleteObj);\n        formData.append(\"isEmergencyAndFinanceNotCompleteObj\", serializedData);\n        const response = await fetch(\"\".concat(serverAddress, \"email/send-email\"), {\n            method: \"POST\",\n            body: formData,\n            credentials: \"include\"\n        });\n        if (response.ok) {\n            react_toastify__WEBPACK_IMPORTED_MODULE_1__.toast.success(\"Email sent\", {\n                toastId: 22,\n                position: \"top-right\",\n                autoClose: 3000,\n                pauseOnHover: false\n            });\n        } else if (response.status === 401) {\n            // console.log(\"error YES 401\");\n            react_toastify__WEBPACK_IMPORTED_MODULE_1__.toast.error(\"Failed to send email\", {\n                toastId: 22,\n                position: \"top-right\",\n                autoClose: 3000,\n                pauseOnHover: false\n            });\n            return response.status;\n        } else {\n            react_toastify__WEBPACK_IMPORTED_MODULE_1__.toast.error(\"Failed to send email\", {\n                toastId: 22,\n                position: \"top-right\",\n                autoClose: 3000,\n                pauseOnHover: false\n            });\n        }\n    }\n    return true;\n};\n/* harmony default export */ __webpack_exports__[\"default\"] = (exportExcel);\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevExports = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevExports) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports on update so we can compare the boundary\n                // signatures.\n                module.hot.dispose(function (data) {\n                    data.prevExports = currentExports;\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevExports !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevExports, currentExports)) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevExports !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./utils/exportExcel.js\n"));

/***/ })

});