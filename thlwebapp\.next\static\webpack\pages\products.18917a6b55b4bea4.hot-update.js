"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/products",{

/***/ "./pages/products.js":
/*!***************************!*\
  !*** ./pages/products.js ***!
  \***************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __N_SSP: function() { return /* binding */ __N_SSP; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _components_Layout__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/Layout */ \"./components/Layout.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _fortawesome_react_fontawesome__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @fortawesome/react-fontawesome */ \"./node_modules/@fortawesome/react-fontawesome/index.es.js\");\n/* harmony import */ var _fortawesome_free_solid_svg_icons__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! @fortawesome/free-solid-svg-icons */ \"./node_modules/@fortawesome/free-solid-svg-icons/index.mjs\");\n/* harmony import */ var ag_grid_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ag-grid-react */ \"./node_modules/ag-grid-react/lib/main.js\");\n/* harmony import */ var ag_grid_react__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(ag_grid_react__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var ag_grid_community_styles_ag_grid_css__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ag-grid-community/styles//ag-grid.css */ \"./node_modules/ag-grid-community/styles/ag-grid.css\");\n/* harmony import */ var ag_grid_community_styles_ag_grid_css__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(ag_grid_community_styles_ag_grid_css__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var ag_grid_community_styles_ag_theme_alpine_css__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ag-grid-community/styles//ag-theme-alpine.css */ \"./node_modules/ag-grid-community/styles/ag-theme-alpine.css\");\n/* harmony import */ var ag_grid_community_styles_ag_theme_alpine_css__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(ag_grid_community_styles_ag_theme_alpine_css__WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_7__);\n/* harmony import */ var react_toastify__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! react-toastify */ \"./node_modules/react-toastify/dist/react-toastify.esm.mjs\");\n/* harmony import */ var react_toastify_dist_ReactToastify_css__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! react-toastify/dist/ReactToastify.css */ \"./node_modules/react-toastify/dist/ReactToastify.css\");\n/* harmony import */ var react_toastify_dist_ReactToastify_css__WEBPACK_IMPORTED_MODULE_9___default = /*#__PURE__*/__webpack_require__.n(react_toastify_dist_ReactToastify_css__WEBPACK_IMPORTED_MODULE_9__);\n/* harmony import */ var _utils_loaders_loadingContext__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/utils/loaders/loadingContext */ \"./utils/loaders/loadingContext.js\");\n/* harmony import */ var react_select__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! react-select */ \"./node_modules/react-select/dist/react-select.esm.js\");\n/* harmony import */ var _utils_rolePermissionsContext__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/utils/rolePermissionsContext */ \"./utils/rolePermissionsContext.js\");\n/* harmony import */ var _utils_renderer_productStatusRenderer__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/utils/renderer/productStatusRenderer */ \"./utils/renderer/productStatusRenderer.js\");\n/* harmony import */ var _utils_renderer_productActionRenderer__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/utils/renderer/productActionRenderer */ \"./utils/renderer/productActionRenderer.js\");\n/* harmony import */ var _services_apiConfig__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/services/apiConfig */ \"./services/apiConfig.js\");\n/* harmony import */ var js_cookie__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! js-cookie */ \"./node_modules/js-cookie/dist/js.cookie.mjs\");\n/* harmony import */ var _azure_msal_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @azure/msal-react */ \"./node_modules/@azure/msal-react/dist/index.js\");\n/* harmony import */ var _components_ProductDialog__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! @/components/ProductDialog */ \"./components/ProductDialog.js\");\n/* harmony import */ var _utils_secureStorage__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! @/utils/secureStorage */ \"./utils/secureStorage.js\");\n/* harmony import */ var _utils_extractCompanyFromEmail__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! @/utils/extractCompanyFromEmail */ \"./utils/extractCompanyFromEmail.js\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst customSelectStyles = {\n    // Default style\n    control: (base)=>({\n            ...base,\n            height: \"28px\",\n            minHeight: \"28px\"\n        }),\n    // Style when the condition is true\n    option: (base, param)=>{\n        let { data } = param;\n        return {\n            ...base,\n            color: data.is_new == true ? \"red\" : \"\"\n        };\n    },\n    valueContainer: (provided, state)=>({\n            ...provided,\n            height: \"26px\",\n            width: \"300px\",\n            padding: \"0 6px\"\n        }),\n    input: (provided, state)=>({\n            ...provided,\n            margin: \"0px\"\n        }),\n    indicatorSeparator: (state)=>({\n            display: \"none\"\n        }),\n    indicatorsContainer: (provided, state)=>({\n            ...provided,\n            height: \"28px\"\n        })\n};\nconst Products = (param)=>{\n    let { userData, PreviousPage, pageTypeId } = param;\n    _s();\n    console.log(\"userData, PreviousPage, pageTypeId\", userData, PreviousPage, pageTypeId);\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_7__.useRouter)();\n    const [pageSize, setPageSize] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(15);\n    const gridRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)();\n    const [isRMChecked, setIsRMChecked] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [isFGChecked, setIsFGChecked] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [isNVChecked, setIsNVChecked] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const { setIsLoading } = (0,_utils_loaders_loadingContext__WEBPACK_IMPORTED_MODULE_10__.useLoading)();\n    const [rowData, setRowData] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [company, setCompany] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"\");\n    const [blockScreen, setBlockScreen] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [typeId, setTypeId] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(pageTypeId);\n    const [isDialogOpen, setIsDialogOpen] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [isIssUser, setIsIssUser] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [isIssProcurementTeamUser, setIsIssProcurementTeamUser] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [isIssAdmin, setisIssAdmin] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [nvStatus, setNVStatus] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [statusOptions] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([\n        {\n            value: \"Draft\",\n            label: \"Draft\"\n        },\n        {\n            value: \"Pending Review\",\n            label: \"Pending Review\"\n        },\n        {\n            value: \"Rejected\",\n            label: \"Rejected\"\n        },\n        {\n            value: \"ISS to Setup\",\n            label: \"ISS to Setup\"\n        },\n        {\n            value: \"Setup Completed\",\n            label: \"Setup Completed\"\n        },\n        {\n            value: \"Cancelled\",\n            label: \"Cancelled\"\n        }\n    ]);\n    const [filteredRowData, setFilteredRowData] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [checkedValue, setCheckedValue] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [incompleteToast, setIncompleteToast] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [searchInput, setSearchInput] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"\");\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        // const userCompany = Cookies.get(\"company\");\n        const userCompany = (userData === null || userData === void 0 ? void 0 : userData.company) || (0,_utils_extractCompanyFromEmail__WEBPACK_IMPORTED_MODULE_19__.extractCompanyFromEmail)(userData === null || userData === void 0 ? void 0 : userData.email);\n        if (userCompany === \"issproduce\") {\n            setIsIssUser(true);\n        }\n        console.log(userData);\n        if (userData.department_id === 2) {\n            setIsIssProcurementTeamUser(true);\n            console.log(\"checking if it is an admin\", userData.role);\n            if (userData.role_id === 1) {\n                console.log(\"is admin\");\n                setisIssAdmin(true);\n            }\n        }\n        console.log(\"isIssProcurementTeamUser\", isIssProcurementTeamUser);\n    }, []);\n    const openDialog = ()=>{\n        setIsDialogOpen(true);\n    };\n    const closeDialog = ()=>{\n        setIsDialogOpen(false);\n    };\n    // const [isOpenOption, setIsOpenOption] = useState(false);\n    const [selectedRequestType, setSelectedRequestType] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(PreviousPage);\n    const handleTypeIdChange = (e)=>{\n        setSearchInput(\"\");\n        const filterTextBox = document.getElementById(\"filter-text-box\");\n        filterTextBox.value = \"\";\n        gridRef.current.api.setQuickFilter(\"\");\n        setNVStatus([]);\n        setTypeId(parseInt(e.target.value));\n        if (e.target.value === \"1\") {\n            setSelectedRequestType(\"rawMaterialRequest\");\n        } else if (e.target.value === \"3\") {\n            setSelectedRequestType(\"newVarietyRequest\");\n        } else if (e.target.value === \"4\") {\n            setSelectedRequestType(\"packagingform\");\n        }\n    };\n    // const closeOptionModal = () => {\n    //   setIsOpenOption(false);\n    // };\n    const handleRequestType = (type)=>{\n        setSelectedRequestType(type);\n    };\n    const handleFormType = ()=>{\n        if (selectedRequestType) {\n            localStorage.setItem(\"formType\", selectedRequestType);\n            console.log(selectedRequestType);\n            if (selectedRequestType === \"rawMaterialRequest\") {\n                router.push({\n                    pathname: \"/raw-material-request/add\"\n                });\n            // } else if (selectedRequestType == \"finishedProductRequest\") {\n            //   router.push({\n            //     pathname: `/finished-product-request/add` });\n            } else if (selectedRequestType == \"newVarietyRequest\") {\n                router.push({\n                    pathname: \"/variety/add\"\n                });\n            } else if (selectedRequestType == \"packagingform\") {\n                // router.push({ pathname: `/packaging/add` });\n                router.push({\n                    pathname: \"/packaging-form/add\"\n                }); //TODO\n            }\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        if (incompleteToast) {\n            react_toastify__WEBPACK_IMPORTED_MODULE_8__.toast.error(\"Kindly Submit the Request to Export it.\", {\n                position: \"top-right\",\n                autoClose: 1000,\n                hideProgressBar: false,\n                closeOnClick: true,\n                pauseOnHover: false,\n                draggable: true,\n                progress: undefined,\n                theme: \"light\"\n            });\n        }\n    }, [\n        incompleteToast\n    ]);\n    const [productsExtractData, setProductsExtractData] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([\n        [\n            \"Product Extract Data\"\n        ]\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        if (productsExtractData.length > 0) {\n            setCheckedValue(true);\n        } else {\n            setCheckedValue(false);\n        }\n    }, [\n        productsExtractData\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        if (typeof document !== \"undefined\") {\n            document.title = \"Products\";\n        }\n        setIsLoading(false);\n        getData().then((data)=>{\n            console.log(data);\n            const formattedData = data === null || data === void 0 ? void 0 : data.map((row)=>({\n                    id: row.id,\n                    code: row.code,\n                    action_id: row.action_id,\n                    request_no: row.request_no,\n                    type: row.product_type,\n                    product_type_name: row.product_type_label,\n                    product_type_id: row.product_type_id,\n                    reason: (row === null || row === void 0 ? void 0 : row.reason_description) ? row === null || row === void 0 ? void 0 : row.reason_description : \"Not Entered\",\n                    delivery_date: row.delivery_date ? new Date(row.delivery_date).toISOString().split(\"T\")[0] : \"Not Entered\",\n                    // delivery_date: new Date(row.delivery_date),\n                    product_code: row.master_product_code ? row.master_product_code : \"Not Entered\",\n                    product_description: row.product_description ? row.product_description : \"Not Entered\",\n                    originator: row.originator ? row.originator : row.originator_name,\n                    originator_email: row.originator_email,\n                    coo: row.coo ? row.coo : \"Not Entered\",\n                    status: row.status_label,\n                    master_product_code: row === null || row === void 0 ? void 0 : row.master_product_code,\n                    count_or_size: row === null || row === void 0 ? void 0 : row.count_or_size,\n                    units_in_outer: row === null || row === void 0 ? void 0 : row.units_in_outer,\n                    cases_per_pallet: row === null || row === void 0 ? void 0 : row.cases_per_pallet,\n                    outer_net_weight: row === null || row === void 0 ? void 0 : row.outer_net_weight,\n                    outer_gross_weight: row === null || row === void 0 ? void 0 : row.outer_gross_weight,\n                    sub_product_code: row === null || row === void 0 ? void 0 : row.sub_product_code,\n                    temperature_grade: row === null || row === void 0 ? void 0 : row.temperature_grade,\n                    intrastat_commodity_code: row === null || row === void 0 ? void 0 : row.intrastat_commodity_code,\n                    temperature_grade_name: row === null || row === void 0 ? void 0 : row.temperature_grade_name,\n                    intrastat_commodity_code_name: row === null || row === void 0 ? void 0 : row.intrastat_commodity_code_name,\n                    mark_variety_name: row === null || row === void 0 ? void 0 : row.mark_variety_name,\n                    intrastat_commodity_code_id: row === null || row === void 0 ? void 0 : row.intrastat_commodity_code_id,\n                    sort_group_id: row === null || row === void 0 ? void 0 : row.group_id,\n                    company: row === null || row === void 0 ? void 0 : row.company_name,\n                    temperature_grade_id: row === null || row === void 0 ? void 0 : row.temperature_grade_id,\n                    userText4: row === null || row === void 0 ? void 0 : row.userText4,\n                    userText5: row === null || row === void 0 ? void 0 : row.userText5,\n                    userText6: row === null || row === void 0 ? void 0 : row.userText6\n                }));\n            setCompany(formattedData[0].company);\n            setRowData(formattedData);\n            setFilteredRowData(formattedData);\n        }).catch((error)=>{\n            return error;\n        });\n    }, [\n        typeId\n    ]);\n    function getData() {\n        setRowData([]);\n        setFilteredRowData([]);\n        let serverAddress = _services_apiConfig__WEBPACK_IMPORTED_MODULE_14__.apiConfig.serverAddress;\n        const company_name = (userData === null || userData === void 0 ? void 0 : userData.company) || (0,_utils_extractCompanyFromEmail__WEBPACK_IMPORTED_MODULE_19__.extractCompanyFromEmail)(userData === null || userData === void 0 ? void 0 : userData.email);\n        const AdCompany = (userData === null || userData === void 0 ? void 0 : userData.companyName) || (userData === null || userData === void 0 ? void 0 : userData.ADCompanyName);\n        return fetch(\"\".concat(serverAddress, \"products/get-products/\").concat(company_name == \"dpsltd\" && AdCompany == \"DPS MS\" ? \"DPS MS\" : company_name, \"\\n      /\").concat(typeId), {\n            method: \"GET\",\n            headers: {\n                \"Content-Type\": \"application/json\"\n            },\n            credentials: \"include\"\n        }).then(async (res)=>{\n            if (res.status == 502) {\n                setBlockScreen(true);\n                return;\n            }\n            setBlockScreen(false);\n            if (res.status === 200) {\n                return res.json();\n            }\n            if (res.status === 400) {\n                react_toastify__WEBPACK_IMPORTED_MODULE_8__.toast.error(\"There was an error with your request. Please check your data and try again.\");\n            } else if (res.status === 401) {\n                react_toastify__WEBPACK_IMPORTED_MODULE_8__.toast.error(\"Your session has expired. Please log in again.\");\n                setTimeout(async ()=>{\n                    await (0,_utils_secureStorage__WEBPACK_IMPORTED_MODULE_18__.logout)();\n                    router.push(\"/login\");\n                }, 3000);\n            }\n            throw new Error(\"Failed to fetch data\");\n        }).catch((error)=>{\n            console.error(error);\n        });\n    }\n    const handlePageSizeChange = (event)=>{\n        const newPageSize = parseInt(event.target.value, 15);\n        setPageSize(newPageSize);\n        gridRef.current.api.paginationSetPageSize(newPageSize);\n    };\n    const handleStatusChange = (selectedOptions)=>{\n        setNVStatus(selectedOptions);\n        filterData(selectedOptions);\n    };\n    const filterData = (statuses)=>{\n        if (statuses.length == 0) {\n            setFilteredRowData(rowData);\n            return;\n        }\n        const filteredData = rowData.filter((row)=>statuses.some((status)=>status.value === row.status));\n        setFilteredRowData(filteredData.length > 0 ? filteredData : []);\n    };\n    // const clearFilters = () => {\n    //   setSelectedStatuses([]);\n    //   setFilteredRowData(rowData);\n    // };\n    const defaultColDef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useMemo)(()=>({\n            sortable: true,\n            filter: false,\n            resizable: true,\n            flex: 1,\n            suppressMenu: false\n        }));\n    const gridOptions = {\n        responsive: true\n    };\n    const columnDefs = [\n        {\n            headerName: \"Request No.\",\n            field: \"request_no\",\n            // cellRenderer: nameRenderer,\n            suppressMenu: true,\n            suppressSizeToFit: true,\n            suppressSizeToFit: false,\n            cellClass: \"ag-grid-checkbox-cell\",\n            flex: \"2%\",\n            filter: true\n        },\n        {\n            headerName: \"Reason\",\n            field: \"reason\",\n            flex: \"2%\",\n            cellRenderer: _utils_renderer_productStatusRenderer__WEBPACK_IMPORTED_MODULE_12__[\"default\"],\n            hide: typeId == 1 || 4 ? false : 0,\n            cellStyle: (params)=>{\n                if (params.value == \"Not Entered\") {\n                    return {\n                        color: \"#B31312\"\n                    };\n                }\n                return null;\n            }\n        },\n        {\n            headerName: \"Delivery Date\",\n            field: \"delivery_date\",\n            flex: \"2%\",\n            hide: typeId == 1 || 4 ? false : 0,\n            cellRenderer: (params)=>{\n                if (params.value === \"Not Entered\") {\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-[#B31312]\",\n                        children: \"Not Entered\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\products.js\",\n                        lineNumber: 381,\n                        columnNumber: 18\n                    }, undefined);\n                } else {\n                    // params.value\n                    // Format the date from yyyy-mm-dd to dd/mm/yyyy\n                    const dateParts = params.value.split(\"-\");\n                    const formattedDate = dateParts.reverse().join(\"/\");\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: formattedDate\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\products.js\",\n                        lineNumber: 388,\n                        columnNumber: 18\n                    }, undefined);\n                }\n            }\n        },\n        {\n            headerName: \"Product Code\",\n            field: \"product_code\",\n            flex: \"2%\",\n            // cellClass: (params) => {\n            //   return params.value === \"Not Entered\" ? 'not-entered' : '';\n            // }\n            cellStyle: (params)=>{\n                if (params.value == \"Not Entered\") {\n                    return {\n                        color: \"#B31312\"\n                    };\n                }\n                return null;\n            }\n        },\n        {\n            // headerName: \"Product description\",\n            // field: \"product_description\",\n            headerName: \"Product Description\",\n            field: \"product_description\",\n            // cellRenderer: statusRenderer,\n            flex: \"3%\",\n            cellStyle: (params)=>{\n                if (params.value == \"Not Entered\") {\n                    return {\n                        color: \"#B31312\"\n                    };\n                }\n                return null;\n            }\n        },\n        {\n            headerName: \"Originator\",\n            field: \"originator\",\n            headerName: \"Originator\",\n            field: \"originator\",\n            // cellRenderer: statusRenderer,\n            flex: \"2%\",\n            flex: \"2%\"\n        },\n        // {\n        //   headerName: \"COO\",\n        //   field: \"coo\",\n        //   headerName: \"COO\",\n        //   field: \"coo\",\n        //   // cellRenderer: statusRenderer,\n        //   flex: \"2%\",\n        //   cellStyle: (params) => {\n        //     if (params.value == \"Not Entered\") {\n        //       return { color: \"#B31312\" };\n        //     }\n        //     return null;\n        //   },\n        // },\n        {\n            headerName: \"Status\",\n            field: \"status\",\n            cellRenderer: _utils_renderer_productStatusRenderer__WEBPACK_IMPORTED_MODULE_12__[\"default\"],\n            cellStyle: ()=>({\n                    justifyContent: \"center\"\n                }),\n            flex: \"2%\",\n            hide: false\n        },\n        {\n            // field: \"Action(s)\",\n            field: typeId == 3 ? \"Action(s)\" : \"Action(s)\",\n            cellRenderer: (params)=>(0,_utils_renderer_productActionRenderer__WEBPACK_IMPORTED_MODULE_13__[\"default\"])(params, userData, company, typeId, setIsLoading, isIssUser),\n            flex: \"2%\",\n            cellStyle: ()=>({}),\n            cellStyle: {\n                justifyContent: \"end\",\n                paddingRight: \"10px\"\n            },\n            sortable: false\n        },\n        {\n            field: \"role_num\",\n            hide: true\n        }\n    ];\n    const handleGridReady = (params)=>{\n        params.api.setColumnDefs(columnDefs);\n    };\n    const onFilterTextBoxChanged = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(()=>{\n        gridRef.current.api.setQuickFilter(document.getElementById(\"filter-text-box\").value);\n        setSearchInput(document.getElementById(\"filter-text-box\").value);\n    }, []);\n    const handleFilterProductType = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)((e)=>{\n        if (e.target.value == \"RM\") {\n            if (e.target.checked) {\n                setIsRMChecked(true);\n                setIsFGChecked(false);\n                setIsNVChecked(false);\n                gridRef.current.api.setQuickFilter(\"RM\");\n            } else {\n                setIsRMChecked(false);\n                gridRef.current.api.setQuickFilter(\"\");\n            }\n        } else if (e.target.value == \"FG\") {\n            if (e.target.checked) {\n                setIsFGChecked(true);\n                setIsRMChecked(false);\n                setIsNVChecked(false);\n                gridRef.current.api.setQuickFilter(\"FG\");\n            } else {\n                setIsFGChecked(false);\n                gridRef.current.api.setQuickFilter(\"\");\n            }\n        } else if (e.target.value == \"NV\") {\n            if (e.target.checked) {\n                setIsNVChecked(true);\n                setIsFGChecked(false);\n                setIsRMChecked(false);\n                gridRef.current.api.setQuickFilter(\"NV\");\n            } else {\n                setIsNVChecked(true);\n                gridRef.current.api.setQuickFilter(\"\");\n            }\n        } else {\n            setIsRMChecked(false);\n            setIsFGChecked(false);\n            setIsNVChecked(false);\n            gridRef.current.api.setQuickFilter(\"\");\n        }\n    }, []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_toastify__WEBPACK_IMPORTED_MODULE_8__.ToastContainer, {\n                limit: 1\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\products.js\",\n                lineNumber: 527,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Layout__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                userData: userData,\n                blockScreen: blockScreen,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mr-20 md:mr-12 lg:mr-14\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-row md:flex-col lg:flex-row justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"border border-light-gray rounded-md px-3 py-1\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"flex items-center gap-2 cursor-pointer\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                type: \"radio\",\n                                                                name: \"requestType\",\n                                                                value: \"1\",\n                                                                className: \"form-radio w-5 h-5 cursor-pointer\",\n                                                                checked: typeId === 1,\n                                                                onChange: handleTypeIdChange\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\products.js\",\n                                                                lineNumber: 535,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"labels\",\n                                                                children: \"Raw Materials\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\products.js\",\n                                                                lineNumber: 543,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\products.js\",\n                                                        lineNumber: 534,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\products.js\",\n                                                    lineNumber: 533,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"border border-light-gray rounded-md px-3 py-1\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"flex items-center gap-2 cursor-pointer\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                type: \"radio\",\n                                                                name: \"requestType\",\n                                                                value: \"3\",\n                                                                className: \"form-radio w-5 h-5 cursor-pointer\",\n                                                                checked: typeId === 3,\n                                                                onChange: handleTypeIdChange\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\products.js\",\n                                                                lineNumber: 559,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"labels\",\n                                                                children: \"New Variety\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\products.js\",\n                                                                lineNumber: 567,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\products.js\",\n                                                        lineNumber: 558,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\products.js\",\n                                                    lineNumber: 557,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                isIssUser && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"border border-light-gray rounded-md px-3 py-1\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"flex items-center gap-2 cursor-pointer\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                type: \"radio\",\n                                                                name: \"requestType\",\n                                                                value: \"4\",\n                                                                className: \"form-radio w-5 h-5 cursor-pointer\",\n                                                                checked: typeId === 4,\n                                                                onChange: handleTypeIdChange\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\products.js\",\n                                                                lineNumber: 573,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"labels\",\n                                                                children: \"Packaging\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\products.js\",\n                                                                lineNumber: 581,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\products.js\",\n                                                        lineNumber: 572,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\products.js\",\n                                                    lineNumber: 571,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\products.js\",\n                                            lineNumber: 532,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"labels\",\n                                            style: {\n                                                marginLeft: \"10px\"\n                                            },\n                                            children: typeId === 3 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_select__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                isMulti: true,\n                                                options: statusOptions,\n                                                onChange: handleStatusChange,\n                                                placeholder: \"Select Status...\",\n                                                className: \"basic-multi-select\",\n                                                classNamePrefix: \"select\",\n                                                value: nvStatus,\n                                                isSearchable: false,\n                                                styles: customSelectStyles\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\products.js\",\n                                                lineNumber: 588,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\products.js\",\n                                            lineNumber: 586,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center px-3 w-auto md:w-fit lg:w-auto md:py-2 lg:py-1 md:mb-3 lg:mb-0\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\products.js\",\n                                            lineNumber: 604,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\products.js\",\n                                    lineNumber: 531,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"relative block w-[47vh] text-gray-400 mt-0 pt-0\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"absolute px-4 py-1 pt-[0.4rem] 2xl:pt-1.5 text-black\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fortawesome_react_fontawesome__WEBPACK_IMPORTED_MODULE_3__.FontAwesomeIcon, {\n                                                        icon: _fortawesome_free_solid_svg_icons__WEBPACK_IMPORTED_MODULE_21__.faSearch,\n                                                        className: \"fw-bold\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\products.js\",\n                                                        lineNumber: 635,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\products.js\",\n                                                    lineNumber: 634,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"text\",\n                                                    id: \"filter-text-box\",\n                                                    placeholder: \"Search...\",\n                                                    onInput: onFilterTextBoxChanged,\n                                                    value: searchInput,\n                                                    className: \"block w-full px-4 pl-10 text-gray-500 placeholder-gray-400 searchbar border rounded-lg appearance-none form-input focus:outline-none\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\products.js\",\n                                                    lineNumber: 637,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\products.js\",\n                                            lineNumber: 633,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ProductDialog__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                            isOpen: openDialog,\n                                            onClose: closeDialog,\n                                            handleFormType: handleFormType,\n                                            selectedRequestType: selectedRequestType,\n                                            isIssUser: isIssUser,\n                                            isIssProcurmentUser: isIssProcurementTeamUser,\n                                            handleRequestType: handleRequestType,\n                                            admin: isIssAdmin\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\products.js\",\n                                            lineNumber: 656,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\products.js\",\n                                    lineNumber: 632,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\products.js\",\n                            lineNumber: 530,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"my-5\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative ag-theme-alpine !rounded-md\",\n                                style: {\n                                    height: \"calc(100vh - 151px)\"\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ag_grid_react__WEBPACK_IMPORTED_MODULE_4__.AgGridReact, {\n                                        rowData: filteredRowData,\n                                        ref: gridRef,\n                                        columnDefs: columnDefs,\n                                        defaultColDef: defaultColDef,\n                                        suppressRowClickSelection: true,\n                                        pagination: true,\n                                        paginationPageSize: pageSize,\n                                        onPageSizeChanged: handlePageSizeChange,\n                                        tooltipShowDelay: 0,\n                                        tooltipHideDelay: 1000,\n                                        onGridReady: handleGridReady,\n                                        gridOptions: gridOptions\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\products.js\",\n                                        lineNumber: 673,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-start mt-2 pagination-style\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            htmlFor: \"page-size-select pagination\",\n                                            className: \"inputs\",\n                                            children: [\n                                                \"Show\",\n                                                \" \",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                    id: \"page-size-select\",\n                                                    onChange: handlePageSizeChange,\n                                                    value: pageSize,\n                                                    className: \"focus:outline-none\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: 10,\n                                                            children: \"10\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\products.js\",\n                                                            lineNumber: 696,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: 15,\n                                                            children: \"15\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\products.js\",\n                                                            lineNumber: 697,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: 25,\n                                                            children: \"25\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\products.js\",\n                                                            lineNumber: 698,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: 50,\n                                                            children: \"50\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\products.js\",\n                                                            lineNumber: 699,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: 100,\n                                                            children: \"100\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\products.js\",\n                                                            lineNumber: 700,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\products.js\",\n                                                    lineNumber: 690,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                \" \",\n                                                \"Entries\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\products.js\",\n                                            lineNumber: 688,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\products.js\",\n                                        lineNumber: 687,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\products.js\",\n                                lineNumber: 669,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\products.js\",\n                            lineNumber: 668,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\products.js\",\n                    lineNumber: 529,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\products.js\",\n                lineNumber: 528,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true);\n};\n_s(Products, \"K/vbnwHMd05vB3GAmUTG2EVlocA=\", false, function() {\n    return [\n        next_router__WEBPACK_IMPORTED_MODULE_7__.useRouter,\n        _utils_loaders_loadingContext__WEBPACK_IMPORTED_MODULE_10__.useLoading\n    ];\n});\n_c = Products;\nvar __N_SSP = true;\n/* harmony default export */ __webpack_exports__[\"default\"] = (Products);\nvar _c;\n$RefreshReg$(_c, \"Products\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevExports = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevExports) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports on update so we can compare the boundary\n                // signatures.\n                module.hot.dispose(function (data) {\n                    data.prevExports = currentExports;\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevExports !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevExports, currentExports)) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevExports !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./pages/products.js\n"));

/***/ })

});