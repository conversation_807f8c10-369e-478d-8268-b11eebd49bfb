"use strict";

const userData = require("../data/users");
const verifyToken = require("../auth");

const getUsers = async (req, res, next) => {
  try {
    //if (await verifyToken(req, res)) {
    const users = await userData.getUsers();
    res.send(users);
    // } else {
    //   res.status(401).send("Invalid Token");
    // }
  } catch (error) {
    logger.error({
      username: req?.user?.name,
      type: "error",
      description: `Fetch users failed with message: ${error.message}`,
    });
    res.status(400).send(error.message);
  }
};

const getUserById = async (req, res, next) => {
  try {
    //if (await verifyToken(req, res)) {
    const userId = req?.params?.id;
    const name = req?.user?.name;
    const user = await userData.getUserById(userId, name);
    res.send(user);
    // } else {
    //   res.status(401).send("Invalid Token");
    // }
  } catch (error) {
    logger.error({
      username: req?.user?.name,
      type: "error",
      description: `Fetch user failed with message: ${error.message}`,
    });
    res.status(400).send(error.message);
  }
};

const getUserByEmail = async (req, res, next) => {
  try {
    //if (await verifyToken(req, res)) {
    const { email, name } = req.body;
    const user = await userData.getUserByEmail(email, name);
    res.send(user);
    // } else {
    //   res.status(401).send("Invalid Token");
    // }
  } catch (error) {
    console.error("Error fetching user by email", error);
    res.status(400).send(error.message);
  }
};

const getTheme = async (req, res, next) => {
  try {
    // const { email}  = req.body;
    const company = req.body?.company;
    const ADCompany = req.body?.ADCompany;
    // if (await verifyToken(req, res)) {
    const user = await userData.getTheme(company, ADCompany);
    res.status(200).json(user[0]);
    // }
  } catch (error) {
    console.log("error:", error);
    res.status(400).send(error.message);
  }
};

const addUser = async (req, res, next) => {
  try {
    const data = req.body;
    //if (await verifyToken(req, res)) {
    const email = req.body?.user?.useremail;
    const name = req.body?.user?.username;
    const department_id = req.body?.department_id;
    const created = await userData.createUser(data, email, name, department_id);
    res.send(created);
    // } else {
    //   res.status(401).send("Invalid Token");
    // }
  } catch (error) {
    console.error("Error", error);
    res.status(400).send(error.message);
  }
};

const updateUser = async (req, res, next) => {
  try {
    //if (await verifyToken(req, res)) {
    const email = req.body?.user?.useremail;
    const name = req.body?.user?.username;
    const department_id = req.body?.department_id;
    const userId = req.params?.id;
    const data = req.body;
    const updated = await userData.updateUser(
      userId,
      data,
      name,
      email,
      department_id
    );
    res.send(updated);
    // } else {
    //   res.status(401).send("Invalid Token");
    // }
  } catch (error) {
    console.error("Error", error);
    res.status(400).send(error.message);
  }
};

const deleteUser = async (req, res, next) => {
  try {
    //if (await verifyToken(req, res)) {
    const email = req.body?.useremail;
    const name = req.body?.username;
    const userId = req.params?.id;
    const deletedUser = await userData.deleteUser(userId, email, name);
    res.send({ status: 200, message: "User successfully deleted" });
    // } else {
    //   res.status(401).send("Invalid Token");
    // }
  } catch (error) {
    console.error(error);
    res.status(400).send(error.message);
  }
};

const updateUsers = async (req, res, next) => {
  try {
    const { body } = req;
    const date = new Date();
    console.log(
      `Request received at ${date.toISOString()} from NaturalHR on thl`,
      req.body
    );
    const result = await fetch(`http://localhost:3006/webhook`, {
      method: "POST",
      headers: {
        Accept: "application/json",
        "Content-Type": "application/json",
      },
      body: JSON.stringify(body),
    });
    console.log("Result from NaturalHR", result.status);
    return res.status(200).send("Natural hr kronos endpoint hit");
  } catch (error) {
    console.error(error);
    res.status(400).send(error.message);
  }
};

module.exports = {
  getUsers,
  getUserById,
  getUserByEmail,
  getTheme,
  addUser,
  updateUser,
  deleteUser,
  updateUsers,
};
