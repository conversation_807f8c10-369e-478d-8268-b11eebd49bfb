import { createContext, useCallback, useState } from "react";
import { apiConfig } from "@/services/apiConfig";
import { getCookieData } from "@/utils/getCookieData";
import { toast } from "react-toastify";
import modalLogic from "@/utils/whatif/modalLogic";
import {
  removePromotionByPkeyAndWhatIfId,
  replaceCustomerTotalsWithTheNew,
  updatePromotionByPkey,
} from "@/utils/whatif/utils/indexedDB";
//
import { useMsal } from "@azure/msal-react";
import Cookies from "js-cookie";
import findQuarter from "@/utils/whatif/utils/findQuarter";
import whatif from "@/pages/whatif";

const ModalContext = createContext();

const ModalProvider = ({ children, userData }) => {
  const { instance, accounts } = useMsal();
  const [currentData, setCurrentData] = useState([]);
  const [currentTotal, setCurrentTotal] = useState([]);
  const [ctxCalenderData, setCtxCalenderData] = useState([]);
  const [ctxStdCalenderData, setCtxStdCalenderData] = useState([]);
  const [currentCustomer, setCurrentCustomer] = useState("");
  const [currentYear, setCurrentYear] = useState(null);

  const serverAddress = apiConfig.serverAddress;

  const [modalConfig, setModalConfig] = useState({
    isModalOpen: false,
    grid: "volume",
    whatIfs: [],
    selectedWeekNo: 0,
    selectedProductPkey: "",
    productName: "",
    previousValues: {},
  });

  const setModal = useCallback(
    async (grid, visibility, pKey, productData, whatifId) => {

      if (visibility) {
        const currentProduct = currentData.filter((data) => data.pkey === pKey);

        if (!currentProduct || currentProduct.length <= 0) return; //return if no product to edit
        const allQuarters = [
          ...currentProduct[0].quarters.Q2,
          ...currentProduct[0].quarters.Q1,
          ...currentProduct[0].quarters.Q3,
          ...currentProduct[0].quarters.Q4,
          ...currentProduct[0].quarters.Q5,
        ];


        const previousValues = {};
        allQuarters.forEach((data) => {
          const uniqueKey = `${data.week}-${data.fiscalYear}`; // Create a unique key
          previousValues[uniqueKey] = {
            volume: data.data[0].value ?? 0,
            be: data.data[2].value ?? 0,
            price: data.data[3].value ?? 0,
            sales: data.data[7].value ?? 0,
            gp: data.data[5].value ?? 0,
            gp_percent: data.data[6].value ?? 0,
            hasData: data.hasData,
          };
        });

        const whatIfs = await modalLogic(
          currentProduct[0],
          grid,
          productData,
          previousValues,
          ctxCalenderData
        );

        if (!whatIfs) return;

        setModalConfig((prevConfig) => ({
          ...prevConfig,
          isModalOpen: visibility,
          grid,
          selectedProductPkey: pKey,
          fiscalYear: currentProduct[0].fiscalyear,
          caseSize: currentProduct[0].case_size,
          productName: currentProduct[0].product_desc,
          selectedWeekNo: productData.week,
          whatIfs: whatIfs,
          previousValues,
          existingWhatifId: whatifId,
          NoDataNextFY_Q1: currentProduct[0].NoDataNextFY_Q1,
          hasData: productData.nextWeekHasData,
        }));
      } else {
        setModalConfig((prevConfig) => ({
          ...prevConfig,
          isModalOpen: visibility,
          grid: "volume",
          whatIfs: [],
          selectedProductPkey: "",
          selectedWeekNo: 0,
          productName: "",
          previousValues: {},
          existingWhatifId: null,
        }));
      }
    },
    [currentData]
  );

  const setCurrentTotalAndUpdateIDB = (
    weeksData,
    previousPromoValues,
    subtractExtra = null
  ) => {
    setCurrentTotal((prev) => {
      const quaterwiseSums = weeksData.reduce(
        (acc, thisWeek) => {
          const prevValues = previousPromoValues[thisWeek.weekNumber] || {
            current: {
              volume: 0,
              sales: 0,
              be: 0,
              gp: 0,
              gp_percent: 0,
              price: 0,
            },
          };

          prev.weeklyAllProductsTotals[currentYear.value][
            thisWeek.weekNumber
          ].total_all_products_volume =
            prev.weeklyAllProductsTotals[currentYear.value][thisWeek.weekNumber]
              .total_all_products_volume +
            (thisWeek.newVolume -
              prevValues.current.volume -
              (subtractExtra?.weekly[thisWeek.weekNumber]?.volumeDiff ?? 0));

          prev.weeklyAllProductsTotals[currentYear.value][
            thisWeek.weekNumber
          ].total_all_products_value =
            prev.weeklyAllProductsTotals[currentYear.value][thisWeek.weekNumber]
              .total_all_products_value +
            (thisWeek.newSales -
              prevValues.current.sales -
              (subtractExtra?.weekly[thisWeek.weekNumber]?.saleDiff ?? 0));

          prev.weeklyAllProductsTotals[currentYear.value][
            thisWeek.weekNumber
          ].total_all_products_gross_profit =
            prev.weeklyAllProductsTotals[currentYear.value][thisWeek.weekNumber]
              .total_all_products_gross_profit +
            (thisWeek.newGp -
              prevValues.current.gp -
              (subtractExtra?.weekly[thisWeek.weekNumber]?.gpDiff ?? 0));

          const quarter = thisWeek.quarter;

          if (quarter) {
            const newCalculatedVolume =
              thisWeek.newVolume - prevValues.current.volume;
            const newCalculatedGp = thisWeek.newGp - prevValues.current.gp;
            const newCalculatedValue =
              thisWeek.newSales - prevValues.current.sales;
            acc[quarter].volume =
              (acc[quarter].volume || 0) + newCalculatedVolume;
            acc[quarter].gp = (acc[quarter].gp || 0) + newCalculatedGp;
            acc[quarter].value = (acc[quarter].value || 0) + newCalculatedValue;
          }
          return acc;
        },
        { Q1: {}, Q2: {}, Q3: {}, Q4: {}, Q5: {} }
      );

      Object.keys(quaterwiseSums).forEach((qt) => {
        if (!!quaterwiseSums[qt] && !!prev.quarterlyAllProductsTotals[qt]) {
          prev.quarterlyAllProductsTotals[qt].total_all_products_volume +=
            quaterwiseSums[qt].volume - (subtractExtra?.[qt]?.volumeDiff ?? 0);

          const valueTotal =
            prev.quarterlyAllProductsTotals[qt].total_all_products_value +
            quaterwiseSums[qt].value -
            (subtractExtra?.[qt]?.saleDiff ?? 0);

          prev.quarterlyAllProductsTotals[qt].total_all_products_value =
            valueTotal;

          const gpTotal =
            prev.quarterlyAllProductsTotals[qt]
              .total_all_products_gross_profit +
            quaterwiseSums[qt].gp -
            (subtractExtra?.[qt]?.gpDiff ?? 0);

          prev.quarterlyAllProductsTotals[qt].total_all_products_gross_profit =
            gpTotal;

          prev.quarterlyAllProductsTotals[qt].total_all_gp_percent =
            valueTotal !== 0 ? (gpTotal / valueTotal) * 100 : 0;
        }
      });

      replaceCustomerTotalsWithTheNew(prev);
      return prev;
    });
  };

  const saveWhatifHandler = async (
    whatIfType,
    selectedWeeks,
    description = "",
    beStatusId = null,
    isWip = false,
    existingWhatifId,
    previousPromoValues,
    quarters,
    removeExtra,
    storedWeeks,
    overlappingWeekArr,
    promoFiscalYear
  ) => {
    const weeksData = [];
    let apiWeeksData = [];
    selectedWeeks.map((week) => {
      weeksData.push({
        weekNumber: week.weekNumber,
        weekStartDate:week.weekStartDate,
        weekEndDate:week.weekEndDate,
        isNew: week.isNew,
        quarter: findQuarter(week.weekNumber, quarters),
        newVolume: week?.current?.volume ?? week?.previous?.volume,
        newBe: week?.current?.be ?? week?.previous?.be,
        newPrice: week?.current?.price ?? week?.previous?.price,
        newGp: week?.current?.gp ?? week?.previous?.gp,
        newSales: week?.current?.sales ?? week?.previous?.sales,
        newGpPer: week?.current?.gp_percent ?? week?.previous?.gp_percent,
      });
    });
    apiWeeksData = [...weeksData];
    if (storedWeeks.length > 0 && overlappingWeekArr.length > 0) {
      storedWeeks.map((week) => {
        apiWeeksData.push({
          weekNumber: week.weekNumber,
          weekStartDate:week.weekStartDate,
          isNew: week.isNew,
          quarter:
            week.weekNumber > 0 && week.weekNumber < 14
              ? "Q1"
              : week.weekNumber > 13 && week.weekNumber < 27
              ? "Q2"
              : week.weekNumber > 26 && week.weekNumber < 40
              ? "Q3"
              : week.weekNumber > 39 && week.weekNumber < 53
              ? "Q4"
              : "Invalid week number",
          newVolume: week?.current?.volume ?? week?.previous?.volume,
          newBe: week?.current?.be ?? week?.previous?.be,
          newPrice: week?.current?.price ?? week?.previous?.price,
          newGp: week?.current?.gp ?? week?.previous?.gp,
          newSales: week?.current?.sales ?? week?.previous?.sales,
          newGpPer: week?.current?.gp_percent ?? week?.previous?.gp_percent,
        });
      });
    }

    const newWhatIf = {
      pkey: modalConfig.selectedProductPkey,
      productName: modalConfig.productName,
      whatIfId: !!existingWhatifId
        ? existingWhatifId
        : modalConfig.whatIfs[0].whatif_id,
      taskTypeId: whatIfType,
      startWeekNo: selectedWeeks.slice(0, 1)[0].weekNumber,
      startWeekDate: selectedWeeks.slice(0, 1)[0].weekStartDate,
      weekEndDate: selectedWeeks.slice(0, 1)[0].weekEndDate,
      endWeekNo: selectedWeeks.slice(-1)[0].weekNumber,
      breakEvenStatusId: beStatusId,
      fiscalYear: promoFiscalYear,
      description,
      isConfirmed: !isWip,
      apiWeeksData,
      created_by: userData.user_id,
    };

    return await fetch(`${serverAddress}whatif/add-whatif`, {
      method: "POST",
      headers: {
        Authorization: `Bearer ${userData.token}`,
        Accept: "application/json",
        "Content-Type": "application/json",
      },
      body: JSON.stringify(newWhatIf),
    })
      .then((res) => {
        if (res.status === 400) {
          toast.error(
            "There was an error with your request. Please check your data and try again."
          );
        } else if (res.status === 401) {
          toast.error("Your session has expired. Please log in again.");
          setTimeout(() => {
            localStorage.removeItem("superUser");
            localStorage.removeItem("company");
            localStorage.removeItem("id");
            localStorage.removeItem("name");
            localStorage.removeItem("role");
            localStorage.removeItem("email");
            Cookies.remove("user");
            Cookies.remove("theme");
            Cookies.remove("token");
            const redirectUrl = `/login?redirect=${encodeURIComponent(
              window.location.pathname
            )}`;
            logoutHandler(instance, redirectUrl);
          }, 3000);
          return;
        }
        if (res.status === 200) {
          return res.json();
        }
        return Promise.reject(res);
      })
      .then(async (json) => {
        if (json.response) {
          const weekData = {};

          selectedWeeks.map((week) => {
            if (!weekData[week.weekNumber]) {
              weekData[week.weekNumber] = {};
            }

            weekData[week.weekNumber]["current"] = {
              volume: week?.current?.volume ?? week?.previous?.volume,
              price: week?.current?.price ?? week?.previous?.price,
              sales: week?.current?.sales ?? week?.previous?.sales,
              be: week?.current?.be ?? week?.previous?.be,
              gp: week?.current?.gp ?? week?.previous?.gp,
              gp_percent:
                week?.current?.gp_percent ?? week?.previous?.gp_percent,
            };
          });

          storedWeeks.map((week) => {
            if (!weekData[week.weekNumber]) {
              weekData[week.weekNumber] = {};
            }

            weekData[week.weekNumber]["current"] = {
              volume: week?.current?.volume ?? week?.previous?.volume,
              price: week?.current?.price ?? week?.previous?.price,
              sales: week?.current?.sales ?? week?.previous?.sales,
              be: week?.current?.be ?? week?.previous?.be,
              gp: week?.current?.gp ?? week?.previous?.gp,
              gp_percent:
                week?.current?.gp_percent ?? week?.previous?.gp_percent,
            };
          });

          const newWhatif = {
            what_if_id: json.response,
            type_id: whatIfType,
            breakeven_status_id: beStatusId,
            promo_fiscalYear: promoFiscalYear,
            promo_start_week_no: selectedWeeks.slice(0, 1)[0].weekNumber,
            promo_end_week_no:
              selectedWeeks.length === 1
                ? selectedWeeks[0].weekNumber + storedWeeks.length
                : selectedWeeks.slice(-1)[0].weekNumber + storedWeeks.length,
            promo_start_week: selectedWeeks.slice(0, 1)[0].weekStartDate,
            description: description,
            is_confirmed: !isWip,
            weekData,
            created_by: userData.name,
          };
          setCurrentData((prevState) => {
            // Find the productIndex based on pkey
            const productIndex = prevState.findIndex(
              (product) => product.pkey === modalConfig.selectedProductPkey
            );

            // If productIndex is found, proceed with the update
            if (productIndex !== -1) {
              const newState = [...prevState];
              const promotions = newState[productIndex].promotions;
              // Find the index of the existing promotion with the same promo_start_week_no
              const existingPromoIndex = promotions.findIndex(
                (promo) =>
                  promo.promo_start_week_no === newWhatif.promo_start_week_no
              );

              const oldPromotionsData =
                existingPromoIndex !== -1
                  ? promotions[existingPromoIndex]
                  : null;

              let modifiedVolumeTotalOverModifiedWeeks = 0;
              let modifiedPriceTotalOverModifiedWeeks = 0;
              let modifiedBeTotalOverModifiedWeeks = 0;
              let modifiedSalesTotalOverModifiedWeeks = 0;
              let modifiedGpTotalOverModifiedWeeks = 0;
              let modifiedGppTotalOverModifiedWeeks = 0;
              let oldPromotionVolumeTotal = 0;
              let oldPromotionPriceTotal = 0;
              let oldPromotionBeTotal = 0;
              let oldPromotionSalesTotal = 0;
              let oldPromotionGpTotal = 0;
              let oldPromotionGppTotal = 0;
              if (newWhatif.weekData) {
                Object.keys(newWhatif.weekData).forEach((current, week) => {
                  const currentValues = newWhatif.weekData[current].current;

                  modifiedVolumeTotalOverModifiedWeeks += currentValues.volume;

                  modifiedPriceTotalOverModifiedWeeks += currentValues.price;

                  modifiedBeTotalOverModifiedWeeks += currentValues.be;
                  modifiedSalesTotalOverModifiedWeeks += currentValues.sales;
                  modifiedGpTotalOverModifiedWeeks += currentValues.gp;
                  modifiedGppTotalOverModifiedWeeks += currentValues.gp_percent;

                  if (
                    oldPromotionsData &&
                    oldPromotionsData.weekData[current]
                  ) {
                    const oldPromotionData =
                      oldPromotionsData.weekData[current]?.current;

                    oldPromotionVolumeTotal += oldPromotionData.volume;
                    oldPromotionPriceTotal += oldPromotionData.price;
                    oldPromotionBeTotal += oldPromotionData.be;
                    oldPromotionSalesTotal += oldPromotionData.sales;
                    oldPromotionGpTotal += oldPromotionData.gp;
                    oldPromotionGppTotal += oldPromotionData.gp_percent;
                  }
                });

                const adjustableTotalVolume =
                  modifiedVolumeTotalOverModifiedWeeks -
                  oldPromotionVolumeTotal;
                const adjustableTotalPrice =
                  modifiedPriceTotalOverModifiedWeeks - oldPromotionPriceTotal;
                const adjustableTotalBe =
                  modifiedBeTotalOverModifiedWeeks - oldPromotionBeTotal;
                const adjustableTotalSales =
                  modifiedSalesTotalOverModifiedWeeks - oldPromotionSalesTotal;
                const adjustableTotalGp =
                  modifiedGpTotalOverModifiedWeeks - oldPromotionGpTotal;
                const adjustableTotalGpp =
                  modifiedGppTotalOverModifiedWeeks - oldPromotionGppTotal;

                const currentProduct = prevState[productIndex];

                currentProduct.total_product_volume =
                  currentProduct.total_product_volume + adjustableTotalVolume;
                currentProduct.total_product_avg_unit_price =
                  currentProduct.total_product_avg_unit_price +
                  adjustableTotalPrice;
                currentProduct.total_product_be =
                  currentProduct.total_product_be + adjustableTotalBe;
                currentProduct.total_product_value =
                  currentProduct.total_product_value + adjustableTotalSales;
                currentProduct.total_product_gross_profit =
                  currentProduct.total_product_gross_profit + adjustableTotalGp;
                currentProduct.total_product_gpp =
                  currentProduct.total_product_gpp + adjustableTotalGpp;
              }
              let currentWeek=Cookies.get("currentWeek");
              const currentDate = new Date();
              const currentDay = (currentDate.getDay() - 1 + 7) % 7;
              const daysToSum = 7 - currentDay;
         
              for (const [weekKey, data] of Object.entries(newWhatif.weekData)) {
                if (weekKey.includes(currentWeek)) {
                    const { volume, price, sales, be, gp, gp_percent } = data.current;
                
                    // const dailyVolume = volume / 7;
                    // const dailySales = sales / 7;
                    // const dailyGp = gp / 7;
        
                    // const updatedVolume = dailyVolume * daysToSum;
                    // const updatedSales = dailySales * daysToSum;
                    // const updatedGp = dailyGp * daysToSum;
             
                    weekData[weekKey].current = {
                        volume: volume,
                        price: price,
                        sales: sales,
                        be: be,
                        gp: gp,
                        gp_percent: gp_percent,
                    };
                }
            }


              if (existingPromoIndex !== -1) {
                // Update the existing promotion
                promotions[existingPromoIndex] = newWhatif;
              } else {
                // Push the new promotion
                promotions.push(newWhatif);
              }

              return newState;
            }

            // If productIndex is not found, return the previous state
            return prevState;
          });

          const removeExtraWeekNumbers = Object.keys(removeExtra).map(Number);

          const subtractExtra = removeExtraWeekNumbers.reduce(
            (acc, weekNumber) => {
              const weekKey = String(weekNumber);
              const diff = removeExtra[weekKey];

              // Weekly diffs
              acc.weekly[weekKey] = {
                volumeDiff: diff.volumeDiff || 0,
                saleDiff: diff.saleDiff || 0,
                gpDiff: diff.gpDiff || 0,
              };

              // Quarterly diffs
              const quarter = findQuarter(weekNumber, quarters);
              acc[quarter].volumeDiff += diff.volumeDiff || 0;
              acc[quarter].saleDiff += diff.saleDiff || 0;
              acc[quarter].gpDiff += diff.gpDiff || 0;

              return acc;
            },
            {
              weekly: {},
              Q1: { volumeDiff: 0, saleDiff: 0, gpDiff: 0 },
              Q2: { volumeDiff: 0, saleDiff: 0, gpDiff: 0 },
              Q3: { volumeDiff: 0, saleDiff: 0, gpDiff: 0 },
              Q4: { volumeDiff: 0, saleDiff: 0, gpDiff: 0 },
              Q5: { volumeDiff: 0, saleDiff: 0, gpDiff: 0 },
            }
          );

          setCurrentTotalAndUpdateIDB(
            weeksData,
            previousPromoValues,
            subtractExtra
          );

          //update promotions in IDB
          await updatePromotionByPkey(
            modalConfig.selectedProductPkey,
            newWhatif
          );

          setModalConfig((prev) => {
            return {
              isModalOpen: false,
              grid: "volume",
              whatIfs: [],
              selectedWeekNo: 0,
              selectedProductPkey: "",
              productName: "",
              previousValues: {},
            };
          });

          toast.success(`Saved successfully`, {
            theme: "colored",
            autoClose: 5000,
          });

          return true;
        }
        toast.error("There was an error while saving", {
          theme: "colored",
          autoClose: 5000,
        });

        return false;
      })
      .catch((error) => {
        console.log("error", error);
        toast.error("There was an error while saving", {
          theme: "colored",
          autoClose: 5000,
        });
        return false;
      });
  };

  const removeWhatifHandler = async (
    selectedWeeks,
    previousPromoValues,
    quarters
  ) => {
    return await fetch(`${serverAddress}whatif/remove-whatif`, {
      method: "POST",
      headers: {
        Authorization: `Bearer ${userData.token}`,
        Accept: "application/json",
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        whatif_id: modalConfig.whatIfs[0].whatif_id,
        productName: modalConfig.productName,
        fiscalYear: modalConfig.fiscalYear,
      }),
    })
      .then(async (res) => {
        return res.text().then((text) => {
          if (res.status === 200) {
            try {
              const json = JSON.parse(text);

              const weeksData = [];
              selectedWeeks.map((week) => {
                weeksData.push({
                  weekNumber: week.weekNumber,
                  isNew: previousPromoValues.hasOwnProperty(week.weekNumber)
                    ? false
                    : week.isNew,
                  quarter: findQuarter(week.weekNumber, quarters),
                  newVolume: week?.previous?.volume,
                  newBe: week?.previous?.be,
                  newPrice: week?.previous?.price,
                  newGp: week?.previous?.gp,
                  newSales: week?.previous?.sales,
                  newGpPer: week?.previous?.gp_percent,
                });
              });

              setCurrentTotalAndUpdateIDB(weeksData, previousPromoValues);

              return json;
            } catch (e) {
              console.error("Failed to parse JSON:", e);
              return Promise.reject(new Error("Invalid JSON"));
            }
          }
          return Promise.reject(res);
        });
      })
      .then(async (json) => {
        if (json?.response) {
          setCurrentData((prevState) => {
            // Remove from promotions

            // Find the productIndex based on pkey
            const productIndex = prevState.findIndex(
              (product) => product.pkey === modalConfig.selectedProductPkey
            );

            // If productIndex is found, proceed with the removal
            if (productIndex !== -1) {
              const newState = [...prevState];
              const productPromotions = newState[productIndex].promotions;

              // Find the promotion index based on whatIfId
              const promoIndex = productPromotions.findIndex(
                (promotion) =>
                  promotion.what_if_id === modalConfig.whatIfs[0].whatif_id
              );

              // If promoIndex is found, remove the promotion
              if (promoIndex !== -1) {
                Object.keys(productPromotions[promoIndex].weekData).forEach(
                  (key) => {
                    const promotionData =
                      productPromotions[promoIndex].weekData[key].current;

                    const product = newState[productIndex];

                    product.total_product_volume =
                      product.total_product_volume - promotionData.volume;
                    product.total_product_avg_unit_price =
                      product.total_product_avg_unit_price -
                      promotionData.price;
                    product.total_product_be =
                      product.total_product_be - promotionData.be;
                    product.total_product_value =
                      product.total_product_value - promotionData.sales;
                    product.total_product_gross_profit =
                      product.total_product_gross_profit - promotionData.gp;
                    // product.total_product_gpp = total_product_gpp - promotionData.gp_percent; //TOFIX: total_product_gpp not found
                  }
                );
                productPromotions.splice(promoIndex, 1);
              }

              return newState;
            }

            // If productIndex is not found, return the previous state
            return prevState;
          });

          await removePromotionByPkeyAndWhatIfId(
            modalConfig.selectedProductPkey,
            modalConfig.whatIfs[0].whatif_id
          );

          setModalConfig((prev) => {
            return {
              isModalOpen: false,
              grid: "volume",
              whatIfs: [],
              selectedWeekNo: 0,
              selectedProductPkey: "",
              productName: "",
              previousValues: {},
            };
          });

          setTimeout(() => {
            toast.success("Removed successfully", {
              theme: "colored",
              autoClose: 5000,
            });
          }, 500);

          return true;
        }

        toast.error("Failed to remove", {
          theme: "colored",
          autoClose: 5000,
        });
        return false;
      })
      .catch((error) => {
        console.error("Error:", error);

        toast.error("Failed to remove", {
          theme: "colored",
          autoClose: 5000,
        });
        return false;
      });
  };

  const addLock = async () => {
    const lockPayload = {
      pkey: modalConfig.selectedProductPkey,
      customer_id: currentCustomer,
    };

    await fetch(`${serverAddress}whatif/add-lock`, {
      method: "POST",
      headers: {
        Authorization: `Bearer ${userData.token}`,
        Accept: "application/json",
        "Content-Type": "application/json",
      },
      body: JSON.stringify(lockPayload),
    })
      .then(async (res) => {
        if (res.status === 401) {
          toast.error("Your session has expired. Please log in again.");
          setTimeout(() => {
            const redirectUrl = `/login?redirect=${encodeURIComponent(
              window.location.pathname
            )}`;
            logoutHandler(instance, redirectUrl);
          }, 3000);
          return;
        }
      })
      .catch((error) => {
        console.log("error", error);
        toast.error("There was an error while saving", {
          theme: "colored",
          autoClose: 5000,
        });
        return false;
      });
  };

  const removeLocks = async () => {
    await fetch(`${serverAddress}whatif/remove-locks`, {
      method: "POST",
      headers: {
        Authorization: `Bearer ${userData.token}`,
        Accept: "application/json",
        "Content-Type": "application/json",
      },
    }).catch((error) => {
      console.log("error", error);
      toast.error("There was an error while saving", {
        theme: "colored",
        autoClose: 5000,
      });
      return false;
    });
  };

  return (
    <ModalContext.Provider
      value={{
        modalConfig,
        setModalConfig,
        setModal,
        currentData,
        currentTotal,
        ctxCalenderData,
        ctxStdCalenderData,
        setCurrentData,
        setCurrentTotal,
        setCtxCalenderData,
        setCtxStdCalenderData,
        saveWhatifHandler,
        removeWhatifHandler,
        addLock,
        removeLocks,
        setCurrentCustomer,
        currentCustomer,
        currentYear,
        setCurrentYear,
      }}
    >
      {children}
    </ModalContext.Provider>
  );
};

export { ModalContext, ModalProvider };
