"use strict";
const dotenv = require("dotenv");
const assert = require("assert");

dotenv.config();

const {
  PORT,
  HOST,
  HOST_URL,
  SQL_USER,
  SQL_PASSWORD,
  SQL_DATABASE,
  SQL_SERVER,
  SECRET_KEY,
  SECRET_IV,
  <PERSON><PERSON><PERSON><PERSON><PERSON>ON_METHOD,
  CLIENT_ID,
  CLIENT_SECRET,
  TENANT_ID,EMAIL_SENDER
} = process.env;

const sqlEncrypt = process.env.ENCRYPT === "true";
assert(PORT, "PORT is required");
assert(HOST, "HOST is required");

module.exports = {
  port: PORT,
  host: HOST,
  url: HOST_URL,
  sql: {
    server: SQL_SERVER,
    database: SQL_DATABASE,
    user: SQL_USER,
    password: SQL_PASSWORD,
    options: {
      encrypt: sqlEncrypt,
      enableArithAbort: true,
      requestTimeout: 30000,
    },
    pool: {
      max: 10,
      min: 0,
      idleTimeoutMillis: 30000,
    },
    debug:true,
  },
  secret_key: SECRET_KEY,
  secret_iv: SECRET_IV,
  encryption_method: ENCRYPTION_METHOD,
  client_id: CLIENT_ID,
  client_secret: CLIENT_SECRET,
  tenant_id: TENANT_ID,
  email_sender:EMAIL_SENDER
};
