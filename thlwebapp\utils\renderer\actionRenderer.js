import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import {
  faCopy,
  faPenToSquare,
  faEye,
  faFileExport,
} from "@fortawesome/free-solid-svg-icons";
import { Router, useRouter } from "next/router";
import Link from "next/link";
import { apiConfig } from "@/services/apiConfig";
import { useState, useEffect, Fragment } from "react";
import Cookies from "js-cookie";
import * as XLSX from "xlsx";
import exportExcelData from "../exportExcel";
import { ToastContainer, toast } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import { getCookieData } from "@/utils/getCookieData";
import { Dialog, Transition } from "@headlessui/react";
import { faInfo, faClose, faXmark } from "@fortawesome/free-solid-svg-icons";

import { usePermissions } from "@/utils/rolePermissionsContext";
import exportExcel from "../exportExcel";
import { logout } from "../secureStorage";
const actionRenderer = (params, userData, token, company) => {
  const router = useRouter();
  const canExport =
    userData?.role_id === 1 ||
    userData?.role_id === 2 ||
    userData?.role_id === 5 ||
    userData?.role_id === 6 ||
    params.data.isEmergencyRequest;
  const { permissions, updatePermissions } = usePermissions();

  const serverAddress = apiConfig.serverAddress;
  const supplierData = params.data;
  let prophet_id =
    supplierData?.prophets.length > 0 && supplierData?.prophets[0]?.prophet_id;
  const role_ids = params.data.roleId.map((ele) => ele.role_id);

  const supplier_id = params.data.id;
  const supplier_status = params.data.status;
  const [status, setStatus] = useState("");
  const [data, setData] = useState([]);
  const user = getCookieData("user");
  const [isOpen, setIsOpen] = useState(false);
  const [isOpenOption, setIsOpenOption] = useState(false);
  const [selectedExportType, setSelectedExportType] = useState("");
  const [emailStatusPopup, setEmailStatusPopup] = useState(false);
  const [popupMessage, setPopUpMessage] = useState("");
  const [internalExportSuccess, setInternalExportSuccess] = useState(false);
  const [ISSExportSuccess, setISSExportSuccess] = useState(false);

  const closeOptionModal = () => {
    setIsOpenOption(false);
  };

  const [isCancelOpen, setIsCancelOpen] = useState(false);
  const cancelProduct = () => {
    setIsCancelOpen(true);
  };

  const closeCancelModal = () => {
    setIsCancelOpen(false);
  };
  const closeEmailPopup = () => {
    setEmailStatusPopup(false);
    if (internalExportSuccess && ISSExportSuccess) {
      params.setUpdateStatusChange(data?.id, data?.status);
    }
  };

  function saveModalData() {
    let serverAddress = apiConfig.serverAddress;

    // setLoading(true);
    fetch(`${serverAddress}suppliers/update-supplier/${supplier_id}`, {
      method: "PUT",
      headers: {
        Accept: "application/json",
        "Content-Type": "application/json",
      },
      credentials: "include",
      body: JSON.stringify({
        sectionName: "updateStatus",
        type: "cancelProduct",
        status: 6,
        updated_date: new Date().toISOString(),
        company_name: params?.data?.company_name,
        requestor_name: params.data.requestor,
        requestor_email: params.data?.requestor_email,
      }),
    })
      .then((res) => {
        if (res.status === 400) {
          toast.error(
            "There was an error with your request. Please check your data and try again."
          );
        } else if (res.status === 401) {
          toast.error("Your session has expired. Please log in again.");
          setTimeout(async() => {
            await logout();
            router.push("/login");
          }, 3000);
        }
        if (res.status === 200) {
          return res.json();
        }
        return Promise.reject(res);
      })
      .then((json) => {
        toast.success("Supplier cancelled successfully", {
          position: "top-right",
        });
        params.setUpdateStatusChange(data?.id, data?.status);
        closeCancelModal();
      })
      .catch((err) => {
        // setLoading(false);
        toast.error("Error cancelling product:", err.statusText, {
          position: "top-right",
        });
        return err;
      });
  }

  const openOptionModal = (supplier_id) => {
    if (
      supplierData.status == "Completed" ||
      supplierData.status == "Exported" ||
      (params.data.isEmergencyRequest && params.data.General === "Complete")
    ) {
      let isExportableBasedOnCodeUnique = false;

      const codeCount = params.data?.prophets[0]?.code_count;
      const prophetCode = params.data?.prophets[0]?.prophet_code;

      const prophet_id =
        params.data?.prophets.length > 0 && params.data?.prophets[0].prophet_id;

      const isSupplierAccount =
        params.data?.roleIds?.includes(1) || params.data?.roleIds?.includes(6);

      let currency =
        params.data?.currency == "$"
          ? `\\${params.data?.currency}`
          : params.data?.currency;
      let actualCurr;
      if (currency && currency == "Not Entered") {
        actualCurr = "";
      } else {
        actualCurr = currency;
      }

      let isValid = true;
      if (isSupplierAccount) {
        if (prophet_id == 1) {
          let regexPattern;

          regexPattern = new RegExp(`^[A-Z0-9]{4}[A-Z0145678]${actualCurr}$`);

          // let regexPattern = new RegExp(`^([A-Z0]{5})${actualCurr}$`);
          isValid = regexPattern.test(prophetCode);
        } else if (prophet_id == 2) {
          let regexPattern;

          regexPattern = new RegExp(`^([A-Z0]{4})9${actualCurr}$`);

          // let regexPattern = new RegExp(`^([A-Z0]{4})9${actualCurr}$`);
          isValid = regexPattern.test(prophetCode);
        } else if (prophet_id == 3) {
          let regexPattern = new RegExp(`^[A-Z0-9]{4}[A-Z01345678][A-Z0-9]*$`);
          isValid = regexPattern.test(prophetCode) && prophetCode.length == 6;
        } else if (prophet_id == 4) {
          let regexPattern;

          regexPattern = new RegExp(`^([A-Z0]{4})2${actualCurr}$`);

          isValid = regexPattern.test(prophetCode);
        }
      }

      if (codeCount && codeCount > 1 && prophetCode && prophetCode !== "") {
        isExportableBasedOnCodeUnique = false;
      } else if (
        codeCount &&
        codeCount == 1 &&
        prophetCode &&
        prophetCode !== ""
      ) {
        isExportableBasedOnCodeUnique = true;
      }

      if (!isExportableBasedOnCodeUnique && !isValid) {
        toast.error(
          "Supplier code is not unique and valid, kindly make sure the supplier code is unique and is valid."
        );
        return;
      } else if (!isExportableBasedOnCodeUnique) {
        toast.error(
          "Supplier code is not unique, kindly make sure the supplier code is unique."
        );
        return;
      } else if (!isValid) {
        toast.error(
          "Supplier code is not vaild, kindly make sure the supplier code is valid."
        );
        return;
      }
      handleSingleExportSupplier(supplier_id);
    } else {
      handleSingleExportSupplier(supplier_id);
    }
  };

  useEffect(() => {
    if (supplier_id) {
      setData(supplierData);
      setStatus(supplier_status);
    }
  }, [supplier_id]);

  const editSupplier = () => {
    if (typeof window !== "undefined") {
      router.push({
        pathname: `/supplier/${supplier_id}/edit`,
        // query: { item_id: supplier_id },
      });
    }
  };
  const confirmPage = () => {
    const mappedPermissions = role_ids.map((roleId) => ({
      roleId: roleId,
      permissions: permissions[roleId],
    }));
    const uniqueSections = [
      ...new Set(mappedPermissions.flatMap((item) => item.permissions)),
    ];

    localStorage.setItem("allowedSections", uniqueSections);
    if (typeof window !== "undefined") {
      router.push({
        pathname: `/supplier/${supplier_id}/confirm`,
      });
    }
  };

  function getGLCode(internal_ledger_code, department, currency, roleIds) {
    if (internal_ledger_code) {
      return internal_ledger_code;
    } else {
      return "";
    }
  }

  const extractContacts = (contactsJsonStr) => {
    try {
      const contacts = contactsJsonStr ? JSON.parse(contactsJsonStr) : [];
      if (Array.isArray(contacts)) {
        return contacts.map((contact) => ({
          "Supplier code": data?.prophets[0]?.prophet_code
            ? data?.prophets[0]?.prophet_code?.trim()
            : "", //!have added supplier code here which is to know unqniue contacts this was not in their extract process
          "Contact ID": "",
          Name: data.company_name || "", //!this looks like supplier name but shouldnt it be contact name
          "Email Address": contact.email_id || "",
          "Telephone number": contact.telephone || "",
          "Cell phone number": "",
          "Fax number": "",
          "Instant Message": "",
          "Physical Address": "",
          "Postal Address": "",
          "Row verision": "",
          "Created timestamp": "",
        }));
      }
    } catch (error) {
      console.error("Error parsing contacts_json:", error);
    }

    return [
      {
        "Contact ID": "",
        Name: data.company_name || "", //!this looks like supplier name but shouldnt it be contact name
        "Email Address": "",
        "Telephone number": "",
        "Cell phone number": "",
        "Fax number": "",
        "Instant Message": "",
        "Physical Address": "",
        "Postal Address": "",
        "Row verision": "",
        "Created timestamp": "",
      },
    ];
  };

  const extractSendacGroup = (sendacGroupJson, id) => {
    try {
      const sendacGroups = sendacGroupJson ? JSON.parse(sendacGroupJson) : [];
      if (Array.isArray(sendacGroups)) {
        const filteredGroups = sendacGroups.filter(
          (group) => group?.created_by === id
        );
        if (filteredGroups.length > 0) {
          return filteredGroups.map((group) => ({
            "Supplier group": "",
            Description: group?.created_by ? group?.label : "",
          }));
        } else {
          // Handle the case when no matching group is found
          return []; // or any other default value or action
        }
      }
    } catch (error) {
      console.error("Error parsing contacts_json:", error);
    }
  };
  const multipleSendRoleOnRoleNums = () => {
    const roleNums = data?.role_num?.split(",").map((num) => num.trim());
    return roleNums.map((num) => ({
      Sendacroleid: "",
      "Supplier code": data?.prophets[0]?.prophet_code
        ? data.prophets[0].prophet_code.trim()
        : "",
      Description: data?.prophets[0]?.prophet_code
        ? data.prophets[0].prophet_code.trim()
        : "", // Is this role name?
      "Supplier Code Supplier Name": data.company_name,
      Type: num,
      "Type Description": data?.["role names"],
      "Supplier code Global gap number": data?.global_gap_number,
      "Created timestamp": "",
      Active: 1,
    }));
  };

  const handleSingleExportSupplier = async (id) => {
    const company = Cookies.get("company");

    // setIsOpenOption(false);
    let rolesArray = params.data.roleId.map((ele) => {
      return ele.role_id;
    });

    const formattedDistributionData = data?.distribution_points_json?.map(
      (row) => ({
        distributionPoint: row?.name,
        directDPvalue: row.direct_dp ? "True" : "False",
        directDP: row.direct_dp,
        from_dp: row.from_dp,
      })
    );
    let filteredInternalExportData = [];
    let filteredISSExportData = [];
    // const isInternal = selectedExportType === "internalExport";

    if (
      supplier_status === "Completed" ||
      supplier_status === "Exported" ||
      (params.data.isEmergencyRequest &&
        params.data.status != "Cancelled" &&
        params.data.General === "Complete" &&
        (data?.roleIds.includes(1)|| data?.roleIds?.includes(6)) &&
        params.data.currency_id)
    ) {
      if (true) {
        let sort_code = "";
        let account_number = "";
        let swiftBicCode = "";
        let iban = "";

        const swiftBicRegex =
          /^([A-Z0-9]{4}[A-Z0-9]{2}[A-Z0-9]{2}|[A-Z0-9]{4}[A-Z0-9]{2}[A-Z0-9]{2}[A-Z0-9]{3})$/;

        if (swiftBicRegex.test(data?.decryptedSort_Bic) && data?.has_iban) {
          sort_code = "000000";
          account_number = data?.decryptedAccountNumber?.slice(-8);
          swiftBicCode = data?.decryptedSort_Bic;
          iban = data?.decryptedAccountNumber;
        } else if (
          !data?.has_iban &&
          swiftBicRegex.test(data?.decryptedSort_Bic)
        ) {
          sort_code = "000000";
          account_number = data?.decryptedAccountNumber;
          swiftBicCode = data?.decryptedSort_Bic;
        } else {
          sort_code = data?.decryptedSort_Bic;
          account_number = data?.decryptedAccountNumber;
        }

        let regional_cert = "";
        if (data?.roleIds?.includes(2) || data?.roleIds?.includes(3)) {
          if (data?.country_code == "UK") {
            regional_cert = data?.red_tractor;
          } else if (data?.country_code == "ZA") {
            regional_cert = data?.puc_code;
          } else if (data?.country_code == "CL") {
            regional_cert = data?.chile_certificate_number;
          }
        }
        let currencyId = "";
        let currencyName = "";
        if (
          data?.roleIds.includes(1) ||
          data?.roleIds.includes(5) ||
          data?.roleIds?.includes(6)
        ) {
          currencyId = data?.currency_id || 1;
          currencyName = data?.currency_name || "Sterling";
        } else {
          currencyId = 1;
          currencyName = "Sterling";
        }
        function getCorrespondingUserLookup(curr) {
          if (curr == "GBP") {
            return "GBPBACS";
          } else if (curr == "EUR") {
            return "EUROSEPA";
          } else if (curr == "USD") {
            return "USDPRIORITY";
          } else {
            return "";
          }
        }
        console.log("supplier type", data?.supplier_type);

        filteredInternalExportData = [
          [
            "UlpFil",
            {
              "Distribution point": "",
              Description:
                formattedDistributionData?.length > 0 &&
                (formattedDistributionData[0].from_dp === null ||
                  formattedDistributionData[0].from_dp === undefined)
                  ? formattedDistributionData[0].distributionPoint
                  : "",
              "Service Supplier Code":
                formattedDistributionData?.length > 0 &&
                (formattedDistributionData[0].from_dp === null ||
                  formattedDistributionData[0].from_dp === undefined) &&
                data?.prophets[0]?.prophet_code?.trim(),
              "Default expected stock status":
                formattedDistributionData?.length > 0 &&
                (formattedDistributionData[0].from_dp === null ||
                  formattedDistributionData[0].from_dp === undefined) &&
                "0",
              "Default received stock status":
                formattedDistributionData?.length > 0 &&
                (formattedDistributionData[0].from_dp === null ||
                  formattedDistributionData[0].from_dp === undefined) &&
                "0",
              "Pallets in packhouse":
                formattedDistributionData?.length > 0 &&
                (formattedDistributionData[0].from_dp === null ||
                  formattedDistributionData[0].from_dp === undefined) &&
                "0",
              "Default haulier":
                formattedDistributionData?.length > 0 &&
                (formattedDistributionData[0].from_dp === null ||
                  formattedDistributionData[0].from_dp === undefined) &&
                "ZZZZZ",
              "Default expected location id":
                formattedDistributionData?.length > 0 &&
                (formattedDistributionData[0].from_dp === null ||
                  formattedDistributionData[0].from_dp === undefined) &&
                21,
              "Default receiving location id":
                formattedDistributionData?.length > 0 &&
                (formattedDistributionData[0].from_dp === null ||
                  formattedDistributionData[0].from_dp === undefined) &&
                21,
              "Packhouse location id":
                formattedDistributionData?.length > 0 &&
                (formattedDistributionData[0].from_dp === null ||
                  formattedDistributionData[0].from_dp === undefined) &&
                21,
              "Default pick location id":
                formattedDistributionData?.length > 0 &&
                (formattedDistributionData[0].from_dp === null ||
                  formattedDistributionData[0].from_dp === undefined) &&
                21,
              "Despatch location id":
                formattedDistributionData?.length > 0 &&
                (formattedDistributionData[0].from_dp === null ||
                  formattedDistributionData[0].from_dp === undefined) &&
                21,
              "Default waste location id":
                formattedDistributionData?.length > 0 &&
                (formattedDistributionData[0].from_dp === null ||
                  formattedDistributionData[0].from_dp === undefined) &&
                21,
              "Default pre-pick location id":
                formattedDistributionData?.length > 0 &&
                (formattedDistributionData[0].from_dp === null ||
                  formattedDistributionData[0].from_dp === undefined) &&
                21,
              "Default returns location id":
                formattedDistributionData?.length > 0 &&
                (formattedDistributionData[0].from_dp === null ||
                  formattedDistributionData[0].from_dp === undefined) &&
                21,
              Address: "",
              "Service supplier code": "",
              "EDI Reference Code": "",
              "EDI ANA Code": "",
              "User Integer 1":
                formattedDistributionData?.length > 0 &&
                (formattedDistributionData[0].from_dp === null ||
                  formattedDistributionData[0].from_dp === undefined) &&
                1,
              "Movement resource group": "",
              "Handheld application used":
                formattedDistributionData?.length > 0 &&
                (formattedDistributionData[0].from_dp === null ||
                  formattedDistributionData[0].from_dp === undefined) &&
                "0",
              "Pallets in procure/receiving":
                formattedDistributionData?.length > 0 &&
                (formattedDistributionData[0].from_dp === null ||
                  formattedDistributionData[0].from_dp === undefined) &&
                "0",
              "Operational depo": "",
              "Enabled for masterfile sending": "",
              "Connected registed depot": "",
              "EDI Transmission type of depo": "",
              "Container loading depo":
                formattedDistributionData?.length > 0 &&
                (formattedDistributionData[0].from_dp === null ||
                  formattedDistributionData[0].from_dp === undefined) &&
                "0",
              "Airport depot": "",
              "Sms notification": "",
              Port: "",
              Dormant: "",
              Active:
                formattedDistributionData?.length > 0 &&
                (formattedDistributionData[0].from_dp === null ||
                  formattedDistributionData[0].from_dp === undefined) &&
                1,
              "Ingredient distribution point": "",
              "Show in CE":
                formattedDistributionData?.length > 0 &&
                (formattedDistributionData[0].from_dp === null ||
                  formattedDistributionData[0].from_dp === undefined) &&
                1,
              "Charge direction": "",
              "Pallet receive time": "",
              "User string 3": "",
              "Direct DP":
                formattedDistributionData?.length > 0 &&
                (formattedDistributionData[0].from_dp === null ||
                  formattedDistributionData[0].from_dp === undefined)
                  ? formattedDistributionData[0]?.directDP
                    ? 1
                    : "0"
                  : "",
              "Include on XML":
                formattedDistributionData?.length > 0 &&
                (formattedDistributionData[0].from_dp === null ||
                  formattedDistributionData[0].from_dp === undefined) &&
                "0",
            },
          ],
          [
            "sendac (Supplier file)",
            {
              "Supplier Active": data?.isActive ? 1 : 0,
              "Haulage cube local": "",
              "Haulage cube name": "",
              "update guesstimates type": 1,
              "Organization ID": "",
              "Vat number 1": data?.vat_number,
              "Organic cert": data?.organic_certificate_number,
              "Regional cert": regional_cert,
              "Global gap number": data?.global_gap_number,
              "Enforce department": "",
              "Sendac Group": data?.supplier_group
                ? JSON.parse(data.supplier_group)[0].value
                : "",
              "Supplier code": data?.prophets[0]?.prophet_code?.trim(),
              "Supplier name": data.company_name,
              "Supplier type": data?.supplier_type,
              "User Lookup 2": "",
              "Address Line 1": data?.address_line_1,
              "Address Line 2": data?.address_line_2,
              "Address Line 3": data?.address_line_3,
              "Address Line 4": data?.address_line_4,
              "Post code": data?.postal_code,
              "Country Code": data?.country_code,
              "Payee supplier code": "",
              "Invoice supplier": "",
              "Head office": "",
              "Settlement days": data?.payment_terms,
              "Bank general ledger code Currency number if bank": currencyId,
              "Currency number": currencyId,
              "Currency number Currency name": currencyName,
              "Bank general ledger code": getGLCode(
                data?.internal_ledger_code,
                data?.prophets[0]?.prophet_id,
                data?.currency_code,
                data?.roleIds
              ),
              "payment Type": data?.payment_type,
              "payment type name": data?.payment_type_name,
              "Country code": data?.country_code,
              Vatable:
                data?.vatable != null ? (data?.vatable ? "1" : "0") : "0",
              "vatable desc":
                data?.vatable != null
                  ? data?.vatable
                    ? "Vatable"
                    : "None vatable"
                  : "None vatable",
              "Area Number":
                data?.prophets[0].prophet_id == 3 ||
                data?.prophets[0].prophet_id == 4
                  ? 1
                  : 7,
              Buyer: 1,
              "Multiple Lot Indicator": "0",
              "multiple lot indicator desc": "By Lot",
              "Generate Pallet Loading Plan": "",
              "Distribution point for supplier": 6,
              "Payment terms": "",
              "Department Number":
                data?.prophets[0]?.prophet_id == 1
                  ? 1
                  : data?.prophets[0]?.prophet_id == 2
                  ? 9
                  : data?.prophets[0].prophet_id == 3
                  ? 3
                  : data?.prophets[0].prophet_id == 4
                  ? 2
                  : "",
              "Allow credit rebates": "",
              "Alternative DP for supplier": 1,
              "Actual posting stops purchase charges": "",
              "Authorise on register": "",
              "User text 1": "",
              "User lookup 1":
                data?.prophets[0].prophet_id == 3 ||
                data?.prophets[0].prophet_id == 4
                  ? getCorrespondingUserLookup(data?.currency_code)
                  : "",
              "Receive orders from edi": "",
              "Send invoices from edi": "",
              "send orders from edi": "",
              "EDI partner":
                data?.prophets[0].prophet_id == 3 ||
                data?.prophets[0].prophet_id == 4
                  ? 2000
                  : "",
              "Generic code":
                data?.prophets[0].prophet_id == 3 ||
                data?.prophets[0].prophet_id == 4
                  ? "STOCK"
                  : "",
              "EDI ANA number": data?.edi ?? "N/A",
              "User % authorize rule": 5,
              FromDP:
                formattedDistributionData?.length > 0
                  ? formattedDistributionData[0].from_dp || ""
                  : "",
            },
          ],
          ["sendacrole (Supplier role file)"],

          ["sendacgroup (Sendac group file)"],
          [
            "bankac (Bank account details table)",
            {
              "Supplier code": data?.prophets[0]?.prophet_code?.trim(),
              "Record id": "",
              "Bank sort code": sort_code,
              "Account number": account_number,
              "Country code":
                data?.country_code == "UK" ? "GB" : data?.country_code,
              "Account holder": data.company_name,
              "Currency number": currencyId,
              "BACS currency": data?.bacs_currency_code,
              "Address Line 1": "",
              "Address Line 2": "",
              "BIC/Swift address": swiftBicCode,
              "Internation bank reference code": iban,
              "Account user id": "",
              "Post code": "",
            },
          ],
          [
            "senbnk (Supplier bank link table)",
            {
              "Supplier code": data?.prophets[0]?.prophet_code
                ? data?.prophets[0]?.prophet_code?.trim()
                : "",
              Bankacid: "",

              "Header bank record id": "",
              "Intermediary bank account id": "",
              "Intermediary bank account id Internation bank reference code":
                "",
            },
          ],
          ["contactdet (Supplier personnel contact details)"],
          [
            "organization (Organization)",
            {
              "Organization ID": "",
              "Organization Name": data?.prophets[0]?.prophet_code
                ? data?.prophets[0]?.prophet_code?.trim()
                : "",
            },
          ],
          [
            "orgroles (Organization Roles)",
            {
              "Organization ID": "",
              "Organization Code": data?.prophets[0]?.prophet_code
                ? data?.prophets[0]?.prophet_code?.trim()
                : "",
              "Role Type ID": "", //!in recording there is another column with similar starting name then it goes like ... so not sure whta is the full name fo the column which will be after this
              Selected: "",
              "Organisation ID": "",
              "role Type ID": "",
              "Contact ID": "",
              "Contact ID Email Address": "",
              "Contact ID Telephone": "",
              "Contact ID Fax": "",
            },
          ],
          [
            "sheetSuppliersId",
            {
              id: data?.id,
              supplierName: formattedDistributionData?.company_name,
              isEmergencyAndFinanceNotComplete:
                data?.isEmergencyAndFinanceNotComplete,
              supplierCode: data?.prophets[0]?.prophet_code
                ? data?.prophets[0]?.prophet_code?.trim()
                : "",
            },
          ],
        ];
        const addDataToSheet = (sheetIndex, dataToAdd) => {
          if (filteredInternalExportData[sheetIndex].length === 2) {
            filteredInternalExportData[sheetIndex].push(...dataToAdd);
          } else {
            filteredInternalExportData[sheetIndex] = [
              filteredInternalExportData[sheetIndex][0],
              ...dataToAdd,
            ];
          }
        };

        // Extract contacts and add to the contacts sheet
        const contacts = extractContacts(data?.contacts_json);
        const extractedSendacGroup = extractSendacGroup(
          data.supplier_group,
          data?.id
        );
        let sendacRoleOnRoleNums = multipleSendRoleOnRoleNums(data);
        addDataToSheet(6, contacts);
        addDataToSheet(2, sendacRoleOnRoleNums);
        addDataToSheet(3, extractedSendacGroup);
      }
      let export_ISS_response;
      if (
        rolesArray.includes(1) ||
        rolesArray.includes(2) ||
        rolesArray.includes(3) ||
        rolesArray.includes(4)
      ) {
        let sendacRoleOnRoleNums = multipleSendRoleOnRoleNums(data?.role_num);
        filteredISSExportData = [
          [
            "UlpFil",
            {
              "Distribution point": "",
              Description:
                formattedDistributionData?.length > 0 &&
                (formattedDistributionData[0].from_dp === null ||
                  formattedDistributionData[0].from_dp === undefined)
                  ? formattedDistributionData[0].distributionPoint
                  : "",
              "Service Supplier Code":
                formattedDistributionData?.length > 0 &&
                (formattedDistributionData[0].from_dp === null ||
                  formattedDistributionData[0].from_dp === undefined) &&
                data?.prophets[0]?.prophet_code?.trim(),
              "Default expected stock status":
                formattedDistributionData?.length > 0 &&
                (formattedDistributionData[0].from_dp === null ||
                  formattedDistributionData[0].from_dp === undefined) &&
                "0",
              "Default received stock status":
                formattedDistributionData?.length > 0 &&
                (formattedDistributionData[0].from_dp === null ||
                  formattedDistributionData[0].from_dp === undefined) &&
                "0",
              "Pallets in packhouse":
                formattedDistributionData?.length > 0 &&
                (formattedDistributionData[0].from_dp === null ||
                  formattedDistributionData[0].from_dp === undefined) &&
                "0",
              "Default haulier":
                formattedDistributionData?.length > 0 &&
                (formattedDistributionData[0].from_dp === null ||
                  formattedDistributionData[0].from_dp === undefined) &&
                "ZZZZZ",
              "Default expected location id":
                formattedDistributionData?.length > 0 &&
                (formattedDistributionData[0].from_dp === null ||
                  formattedDistributionData[0].from_dp === undefined) &&
                21,
              "Default receiving location id":
                formattedDistributionData?.length > 0 &&
                (formattedDistributionData[0].from_dp === null ||
                  formattedDistributionData[0].from_dp === undefined) &&
                21,
              "Packhouse location id":
                formattedDistributionData?.length > 0 &&
                (formattedDistributionData[0].from_dp === null ||
                  formattedDistributionData[0].from_dp === undefined) &&
                21,
              "Default pick location id":
                formattedDistributionData?.length > 0 &&
                (formattedDistributionData[0].from_dp === null ||
                  formattedDistributionData[0].from_dp === undefined) &&
                21,
              "Despatch location id":
                formattedDistributionData?.length > 0 &&
                (formattedDistributionData[0].from_dp === null ||
                  formattedDistributionData[0].from_dp === undefined) &&
                21,
              "Default waste location id":
                formattedDistributionData?.length > 0 &&
                (formattedDistributionData[0].from_dp === null ||
                  formattedDistributionData[0].from_dp === undefined) &&
                21,
              "Default pre-pick location id":
                formattedDistributionData?.length > 0 &&
                (formattedDistributionData[0].from_dp === null ||
                  formattedDistributionData[0].from_dp === undefined) &&
                21,
              "Default returns location id":
                formattedDistributionData?.length > 0 &&
                (formattedDistributionData[0].from_dp === null ||
                  formattedDistributionData[0].from_dp === undefined) &&
                21,
              Address: "",
              "Service supplier code": "",
              "EDI Reference Code": "",
              "EDI ANA Code": "",
              "User Integer 1":
                formattedDistributionData?.length > 0 &&
                (formattedDistributionData[0].from_dp === null ||
                  formattedDistributionData[0].from_dp === undefined) &&
                1,
              "Movement resource group": "",
              "Handheld application used":
                formattedDistributionData?.length > 0 &&
                (formattedDistributionData[0].from_dp === null ||
                  formattedDistributionData[0].from_dp === undefined) &&
                "0",
              "Pallets in procure/receiving":
                formattedDistributionData?.length > 0 &&
                (formattedDistributionData[0].from_dp === null ||
                  formattedDistributionData[0].from_dp === undefined) &&
                "0",
              "Operational depo": "",
              "Enabled for masterfile sending": "",
              "Connected registed depot": "",
              "EDI Transmission type of depo": "",
              "Container loading depo":
                formattedDistributionData?.length > 0 &&
                (formattedDistributionData[0].from_dp === null ||
                  formattedDistributionData[0].from_dp === undefined) &&
                "0",
              "Airport depot": "",
              "Sms notification": "",
              Port: "",
              Dormant: "",
              Active:
                formattedDistributionData?.length > 0 &&
                (formattedDistributionData[0].from_dp === null ||
                  formattedDistributionData[0].from_dp === undefined) &&
                1,
              "Ingredient distribution point": "",
              "Show in CE":
                formattedDistributionData?.length > 0 &&
                (formattedDistributionData[0].from_dp === null ||
                  formattedDistributionData[0].from_dp === undefined) &&
                1,
              "Charge direction": "",
              "Pallet receive time": "",
              "User string 3": "",
              "Direct DP":
                formattedDistributionData?.length > 0 &&
                (formattedDistributionData[0].from_dp === null ||
                  formattedDistributionData[0].from_dp === undefined)
                  ? formattedDistributionData[0]?.directDP
                    ? 1
                    : "0"
                  : "",
              "Include on XML":
                formattedDistributionData?.length > 0 &&
                (formattedDistributionData[0].from_dp === null ||
                  formattedDistributionData[0].from_dp === undefined) &&
                "0",
            },
          ],
          [
            "Supplier data",
            {
              "Supplier Active": "N/A",
              "Supplier code": data?.prophets[0]?.prophet_code?.trim(),
              "EDI Partner": "N/A",
              "Supplier name": data.company_name,
              "EDI ANA number": data?.edi ?? "N/A",
              "Producer (supplier)": "N/A",
              "Department number": "N/A",
              "Currency number": data?.currency_id ? data?.currency_id : 1,
              "Global gap number": data?.global_gap_number,
              "Grower group": "N/A",
              "Defra county number": "N/A",
              "Date start": "N/A",
              "Date end": "N/A",
              "Organic cert": data?.organic_certificate_number,
              "Regional cert": data?.chile_certificate_number,
              "Head office": data?.prophets[0]?.prophet_code?.trim(),
              "Country Code": data?.country_code,
              "Distribution point for supplier":
                data?.distribution_points_json?.length > 0
                  ? data?.distribution_points_json[0].from_dp
                  : "N/A",
              "Bool 2": "N/A",
              "Bool 3": "N/A",
              "Address line 1": data?.address_line_1,
              "Address line 2": data?.address_line_2,
              "Address line 3": data?.address_line_3,
              "Address line 4": data?.address_line_4,
              "Post code": data?.postal_code,
              "Currency Number": data?.currency_id ? data?.currency_id : 1,
              "Bank general ledger code": data?.iss_ledger_code
                ? data?.iss_ledger_code
                : "12200",
              "Bank general ledger code Currency number if bank":
                data?.currency_id ? data?.currency_id : 1,
              "Settlement days": data?.payment_terms,
              "Department Number":
                data?.prophets[0]?.prophet_id == 1
                  ? 1
                  : data?.prophets[0]?.prophet_id == 2
                  ? 9
                  : data?.prophets[0].prophet_id == 3
                  ? 3
                  : data?.prophets[0].prophet_id == 4
                  ? 2
                  : "N/A",
              "Area Number": "1",
              Vatable:
                data?.vatable != null ? (data?.vatable ? "1" : "0") : "0",
              Buyer: "1",
              "Billing type": "0",
              "Payment type": data?.payment_type ? data?.payment_type : 2,
              "Expense general ledger code": "N/A",
              "Authorise on register": "N/A",
              "Use % authorise rule": 5,
              "User text 1": "N/A",
              "Mandatory altfil on service jobs": "N/A",
              "Organization ID": "N/A",
              FromDP:
                formattedDistributionData?.length > 0
                  ? formattedDistributionData[0].from_dp || ""
                  : "",
              id: data?.id,
              isEmergencyAndFinanceNotComplete:
                data?.isEmergencyAndFinanceNotComplete,
            },
          ],
          ["sendacrole (Supplier role file)"],
        ];

        const addSendacRoleDataToSheet = (sheetIndex, dataToAdd) => {
          if (filteredISSExportData[sheetIndex].length === 1) {
            filteredISSExportData[sheetIndex].push(...dataToAdd);
          } else {
            filteredISSExportData[sheetIndex] = [
              filteredISSExportData[sheetIndex][0],
              ...dataToAdd,
            ];
          }
        };

        addSendacRoleDataToSheet(2, sendacRoleOnRoleNums);
        export_ISS_response = await exportExcel(
          filteredISSExportData,
          false,
          token,
          company,
          userData,
          prophet_id,
          params.data?.requestor_email,
          ""
        );
      } else {
        export_ISS_response = "Not sent";
      }

      const exportInternal_response = await exportExcel(
        filteredInternalExportData,
        true,
        token,
        company,
        userData,
        prophet_id,
        params.data?.requestor_email,
        ""
      );

      setEmailStatusPopup(true);
      if (
        export_ISS_response &&
        exportInternal_response &&
        export_ISS_response != "Not sent"
      ) {
        setPopUpMessage(
          "Email successfully sent to both Finance Department and ISS Admin Team"
        );
        setISSExportSuccess(true);
        setInternalExportSuccess(true);
      } else if (export_ISS_response && export_ISS_response != "Not sent") {
        setPopUpMessage(
          "Email sent to ISS Admin Team , but not to Finance Department"
        );
        setInternalExportSuccess(true);
      } else if (exportInternal_response && export_ISS_response != "Not sent") {
        setISSExportSuccess(true);
        setPopUpMessage(
          "Email sent to ISS Admin Team, but not to Finance Department"
        );
      } else if (exportInternal_response && export_ISS_response == "Not sent") {
        setPopUpMessage(
          "Email sent to Finance Department, but not to ISS Admin as suppliers with only Haulier or Expense roles are not allowed to export to ISS"
        );
        setInternalExportSuccess(true);
      } else {
        setPopUpMessage(
          "Email not sent to either Finance Department or ISS Admin Team"
        );
      }
    } else {
      if (
        params.data.isEmergencyRequest &&
        (params.data.General === "Incomplete" ||
          params.data.General == "Not Entered")
      ) {
        toast.error("General section needs to complete", {
          position: "top-right",
          autoClose: 3000,
          hideProgressBar: false,
          closeOnClick: true,
          pauseOnHover: false,
          draggable: true,
          progress: undefined,
          theme: "light",
        });
        return;
      } else if (
        params.data.isEmergencyRequest &&
        (data?.roleIds.includes(1) || data?.roleIds?.includes(6)) &&
        !params.data.currency_id
      ) {
        toast.error(
          "Please select a currency and a valid supplier code to export",
          {
            position: "top-right",
            autoClose: 3000,
            hideProgressBar: false,
            closeOnClick: true,
            pauseOnHover: false,
            draggable: true,
            progress: undefined,
            theme: "light",
          }
        );
        return;
      } else {
        toast.error("Supplier details are incomplete or not confirmed.", {
          position: "top-right",
          autoClose: 3000,
          hideProgressBar: false,
          closeOnClick: true,
          pauseOnHover: false,
          draggable: true,
          progress: undefined,
          theme: "light",
        });
        return;
      }
    }
    setSelectedExportType("");
  };

  const disabledClass = "text-gray-500 cursor-not-allowed";
  const handleExportType = (e) => {
    setSelectedExportType(e.target.value);
  };

  return (
    <>
      {/* <ToastContainer limit={1} /> */}
      <div className="flex flex-row gap-4 justify-center text-blue-500">
        <button
          onClick={() =>
            status != "Exported" && status != "Cancelled"
              ? editSupplier()
              : confirmPage()
          }
        >
          {status == "Exported" || status == "Cancelled" ? (
            <FontAwesomeIcon
              icon={faEye}
              size="lg"
              title="View Supplier"
              className="text-skin-primary"
            />
          ) : (
            <FontAwesomeIcon
              icon={faPenToSquare}
              size="lg"
              title="Edit Supplier"
              className="text-skin-primary"
            />
          )}
        </button>
        {status != "Cancelled" && canExport && (
          <button
            onClick={() => openOptionModal(supplier_id)}
            title="Export Supplier"
          >
            <FontAwesomeIcon
              icon={faFileExport}
              size="lg"
              className="text-skin-primary"
            />
          </button>
        )}
        {status != "Cancelled" && status != "Exported" && (
          <button
            onClick={cancelProduct}
            title="Cancel Product"
            className="flex items-center"
          >
            <FontAwesomeIcon
              icon={faXmark}
              size="sm"
              className="border rounded-sm border-skin-primary text-skin-primary m-0 w-[15px] h-[15px]"
            />
          </button>
        )}
      </div>

      <Transition appear show={isCancelOpen} as={Fragment}>
        <Dialog as="div" className="relative z-10" onClose={closeCancelModal}>
          <Transition.Child
            as={Fragment}
            enter="ease-out duration-300"
            enterFrom="opacity-0"
            enterTo="opacity-100"
            leave="ease-in duration-200"
            leaveFrom="opacity-100"
            leaveTo="opacity-0"
          >
            <div className="fixed inset-0 bg-black bg-opacity-25 backdrop-blur-sm" />
          </Transition.Child>

          <div className="fixed inset-0 overflow-y-auto">
            <div className="flex items-center justify-center min-h-full p-4 text-center">
              <Transition.Child
                as={Fragment}
                enter="ease-out duration-300"
                enterFrom="opacity-0 scale-95"
                enterTo="opacity-100 scale-100"
                leave="ease-in duration-200"
                leaveFrom="opacity-100 scale-100"
                leaveTo="opacity-0 scale-95"
              >
                <Dialog.Panel className=" w-[45%] transform overflow-hidden rounded-xl bg-white  text-left align-middle shadow-xl transition-all">
                  {/* <!-- Modal content --> */}
                  <div className="relative bg-white rounded-lg shadow">
                    {/* <!-- Modal header --> */}
                    <div className="flex items-start justify-between p-8 rounded-t">
                      <h3 className="flex flex-row text-xl font-semibold text-gray-900  items-center">
                        <span className="flex justify-center items-center border border-skin-primary text-skin-primary w-[25px] h-[25px] text-center leading-5 rounded-full text-base me-4">
                          <FontAwesomeIcon icon={faInfo} />{" "}
                        </span>{" "}
                        Warning
                      </h3>
                      <button
                        onClick={closeCancelModal}
                        type="button"
                        className="text-black bg-transparent hover:bg-gray-200 hover:text-gray-900 rounded-lg text-xl w-8 h-8 ml-auto inline-flex justify-center items-center "
                        data-modal-hide="default-modal"
                      >
                        <FontAwesomeIcon
                          icon={faXmark}
                          className="text-skin-primary"
                        />{" "}
                      </button>
                    </div>
                    {/* <!-- Modal body --> */}
                    <div className="p-8 py-0 space-y-6">
                      <p className="text-base xl:text-md 2xl:text-lg leading-relaxed mt-2">
                        Are you sure you want to cancel supplier?
                      </p>
                    </div>
                    {/* <!-- Modal footer --> */}
                    <div className="flex items-end p-6 space-x-2 justify-end">
                      <button
                        onClick={closeCancelModal}
                        data-modal-hide="default-modal"
                        type="button"
                        className="border border-skin-primary text-skin-primary focus:ring-4 focus:outline-none focus:ring-blue-300 font-medium rounded-md text-md px-6 py-2 text-center "
                      >
                        No
                      </button>
                      <button
                        onClick={saveModalData}
                        data-modal-hide="default-modal"
                        type="button"
                        className="text-white bg-red-500 hover:bg-red-500 focus:ring-4 focus:outline-none focus:ring-blue-300 font-medium rounded-md text-md px-6 py-2 text-center "
                      >
                        Yes
                      </button>
                    </div>
                  </div>
                </Dialog.Panel>
              </Transition.Child>
            </div>
          </div>
        </Dialog>
      </Transition>
      <Transition appear show={emailStatusPopup} as={Fragment}>
        <Dialog as="div" className="relative z-10" onClose={closeEmailPopup}>
          <Transition.Child
            as={Fragment}
            enter="ease-out duration-300"
            enterFrom="opacity-0"
            enterTo="opacity-100"
            leave="ease-in duration-200"
            leaveFrom="opacity-100"
            leaveTo="opacity-0"
          >
            <div className="fixed inset-0 bg-black bg-opacity-25 backdrop-blur-sm" />
          </Transition.Child>

          <div className="fixed inset-0 overflow-y-auto">
            <div className="flex items-center justify-center min-h-full p-4 text-center">
              <Transition.Child
                as={Fragment}
                enter="ease-out duration-300"
                enterFrom="opacity-0 scale-95"
                enterTo="opacity-100 scale-100"
                leave="ease-in duration-200"
                leaveFrom="opacity-100 scale-100"
                leaveTo="opacity-0 scale-95"
              >
                <Dialog.Panel className=" w-[45%] transform overflow-hidden rounded-xl bg-white  text-left align-middle shadow-xl transition-all">
                  {/* <!-- Modal content --> */}
                  <div className="relative bg-white rounded-lg shadow">
                    {/* <!-- Modal header --> */}
                    <div className="flex items-start justify-between p-8 rounded-t">
                      <h3 className="flex flex-row text-xl font-semibold text-gray-900  items-center">
                        <span className="flex justify-center items-center border border-skin-primary text-skin-primary w-[25px] h-[25px] text-center leading-5 rounded-full text-base me-4">
                          <FontAwesomeIcon icon={faInfo} />{" "}
                        </span>{" "}
                        Status Message
                      </h3>
                      <button
                        onClick={closeEmailPopup}
                        type="button"
                        className="text-black bg-transparent hover:bg-gray-200 hover:text-gray-900 rounded-lg text-xl w-8 h-8 ml-auto inline-flex justify-center items-center "
                        data-modal-hide="default-modal"
                      >
                        <FontAwesomeIcon
                          icon={faXmark}
                          className="text-skin-primary"
                        />{" "}
                      </button>
                    </div>
                    {/* <!-- Modal body --> */}
                    <div className="p-8 py-0 space-y-6">
                      <p className="text-base xl:text-md 2xl:text-lg leading-relaxed mt-2">
                        {popupMessage}
                      </p>
                    </div>
                    {/* <!-- Modal footer --> */}
                    <div className="flex items-end p-6 space-x-2 justify-end">
                      <button
                        onClick={closeEmailPopup}
                        data-modal-hide="default-modal"
                        type="button"
                        className="border text-dark-gray focus:ring-4 focus:outline-none focus:ring-blue-300 font-medium rounded-md text-md px-6 py-2 text-center "
                      >
                        Ok
                      </button>
                    </div>
                  </div>
                </Dialog.Panel>
              </Transition.Child>
            </div>
          </div>
        </Dialog>
      </Transition>
    </>
  );
};

export default actionRenderer;
