"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/service_level",{

/***/ "./components/service_level/ViewDetails.jsx":
/*!**************************************************!*\
  !*** ./components/service_level/ViewDetails.jsx ***!
  \**************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _fluentui_react_components__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @fluentui/react-components */ \"./node_modules/@fluentui/react-components/lib/index.js\");\n/* harmony import */ var _AuditDetails__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./AuditDetails */ \"./components/service_level/AuditDetails.jsx\");\n/* harmony import */ var _services_apiConfig__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/services/apiConfig */ \"./services/apiConfig.js\");\n/* harmony import */ var _DeleteReasonPopover__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./DeleteReasonPopover */ \"./components/service_level/DeleteReasonPopover.jsx\");\n/* harmony import */ var _utils_whatif_utils_getFormattedDate__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/utils/whatif/utils/getFormattedDate */ \"./utils/whatif/utils/getFormattedDate.js\");\n/* harmony import */ var react_toastify__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! react-toastify */ \"./node_modules/react-toastify/dist/react-toastify.esm.mjs\");\n/* harmony import */ var js_cookie__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! js-cookie */ \"./node_modules/js-cookie/dist/js.cookie.mjs\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\nconst ViewDetails = (param)=>{\n    let { data, setData, setMapForReasonsParentsAndTheirCorrespondingChildren, reasonsMasterList, parentReasonList, reasonsData, fetchReasonData, userData, isOpen, setIsOpen, isBulkUpdate, bulkUpdateData, setReasonsData, setAllSelectedProducts, setBulkUpdateData, setIsHeaderChecked, setSelectedRows } = param;\n    _s();\n    const [lockedBy, setLockedBy] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isBeingEdited, setIsBeingEdited] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [selectedReasonDropdownValue, setSelectedReasonDropdownValue] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [subReasonsList, setSubReasonsList] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedSubReasonDropdownValue, setSelectedSubReasonDropdownValue] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [quantity, setQuantity] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [comment, setComment] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [onEditQuantityValue, setOnEditQuantityValue] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [onEditSelectedReason, setOnEditSelectedReason] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [onEditSelectedSubReason, setOnEditSelectedSubReason] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [onEditComment, setOnEditComment] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [deleteId, setDeleteId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [deleteReason, setDeleteReason] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [isDeleteTrue, setIsDeleteTrue] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isValidQuantity, setIsValidQuantity] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [isValidReason, setIsValidReasons] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [isValidSubReason, setIsValidSubReasons] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [isValidEditQuantity, setIsValidEditQuantity] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [isValidEditReason, setIsValidEditReasons] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [isValidEditSubReason, setIsValidEditSubReasons] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [invalidEditQuantityId, setInvalidEditQuantityId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [invalidEditReasonsId, setInvalidEditReasonsId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [invalidEditSubReasonsId, setInvalidEditSubReasonsId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [originalEditQuantity, setOriginalEditQuantity] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [isAuditDetailsOpen, setIsAuditDetailsOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isValidComment, setIsValidComment] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [isOtherSelected, setIsOtherSelected] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [currentData, setCurrentData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(data);\n    const [currentCustomers, setCurrentCustomers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [orderId, setOrderId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isSaveButtonDisabled, setIsSaveButtonDisabled] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        setMapForReasonsParentsAndTheirCorrespondingChildren();\n    }, [\n        reasonsMasterList\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const ids = data.map((item)=>item.ORD_ID);\n        const customers = data.map((item)=>item.CUSTOMER);\n        setOrderId(ids);\n        setCurrentData(data);\n        setCurrentCustomers(customers);\n        return ()=>{\n            setOrderId([]);\n        };\n    }, []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (orderId.length > 0) {\n            fetchReasonData(orderId, currentCustomers);\n        }\n    }, [\n        orderId[0],\n        currentCustomers[0]\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (currentData.length > 0) {\n            setLockedBy(currentData[0].LOCKED_BY);\n        }\n    }, [\n        currentData\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        setLockedBy(data[0].LOCKED_BY);\n    }, [\n        data[0].LOCKED_BY\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (isDeleteTrue) {\n            handleDeleteReason(orderId);\n            setIsDeleteTrue(false);\n        }\n    }, [\n        isDeleteTrue\n    ]);\n    const saveReasons = ()=>{\n        setIsSaveButtonDisabled(true);\n        if (isBulkUpdate) {\n            let totalAddedReasons = bulkUpdateData.totalCasesDifferent;\n            setBulkUpdateData((prev)=>{\n                return {\n                    ...prev,\n                    totalCasesDifferent: 0,\n                    totalCasesAddedReasons: totalAddedReasons\n                };\n            });\n        }\n        const saveData = currentData.map((item)=>{\n            var _reasonsMasterList_filter_, _reasonsMasterList_filter_1;\n            return {\n                quantity: isBulkUpdate ? item.CASES_DIFFERENCE : typeof quantity === \"string\" ? quantity.trim() : quantity,\n                reasons: selectedReasonDropdownValue,\n                reasonsLabel: (_reasonsMasterList_filter_ = reasonsMasterList.filter((r)=>r.id == +selectedReasonDropdownValue)[0]) === null || _reasonsMasterList_filter_ === void 0 ? void 0 : _reasonsMasterList_filter_.reason,\n                subReason: selectedSubReasonDropdownValue,\n                subReasonLabel: (_reasonsMasterList_filter_1 = reasonsMasterList.filter((r)=>r.id === selectedSubReasonDropdownValue)[0]) === null || _reasonsMasterList_filter_1 === void 0 ? void 0 : _reasonsMasterList_filter_1.reason,\n                comment: comment.trim(),\n                orderId: item.ORD_ID,\n                addedBy: userData.email,\n                addedByName: userData.name,\n                custCode: item.CUSTOMER\n            };\n        });\n        const isValid = saveData.every((item)=>{\n            const isQuantityValid = item.quantity && item.quantity !== \"\" && item.quantiy !== 0;\n            const isReasonValid = item.reasons && item !== \"\";\n            const isSubReasonValid = item.subReason && item.subReason !== \"\";\n            if (selectedReasonDropdownValue === \"30\" && !item.comment) {\n                setIsValidComment(false);\n                return;\n            } else {\n                setIsValidComment(true);\n            }\n            // Set individual validation states\n            if (!isQuantityValid) {\n                alert(\"not valid\");\n                setIsValidQuantity(false);\n            }\n            if (!isReasonValid) {\n                setIsValidReasons(false);\n            }\n            if (!isSubReasonValid) {\n                setIsValidSubReasons(false);\n            }\n            return isQuantityValid && isReasonValid && isSubReasonValid;\n        });\n        // If any of the items are invalid, set the overall validation states\n        if (!isValid) {\n            return; // Exit if any validation fails\n        }\n        setIsValidQuantity(true);\n        setIsValidReasons(true);\n        setIsValidSubReasons(true);\n        try {\n            const serverAddress = _services_apiConfig__WEBPACK_IMPORTED_MODULE_3__.apiConfig.serverAddress;\n            fetch(\"\".concat(serverAddress, \"servicelevel/add-new-reason\"), {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                credentials: \"include\",\n                body: JSON.stringify(saveData)\n            }).then((res)=>{\n                if (res.status == 401) {\n                    react_toastify__WEBPACK_IMPORTED_MODULE_6__.toast.error(\"Your session has expired. Please log in again.\");\n                    setTimeout(async ()=>{\n                        await logout();\n                        const redirectUrl = \"/login?redirect=\".concat(encodeURIComponent(window.location.pathname));\n                        router.push(redirectUrl);\n                    }, 3000);\n                    return null;\n                } else if (res.status == 400) {\n                    react_toastify__WEBPACK_IMPORTED_MODULE_6__.toast.error(\"There was an error with your request. Please check your data and try again.\");\n                    return null;\n                } else {\n                    return res.json(); // Ensure you parse the JSON\n                }\n            }).then((json)=>{\n                if (!isBulkUpdate) {\n                    fetchReasonData(orderId[0], currentCustomers[0]);\n                } else {\n                    const reasonsDataArr = json.reduce((acc, item)=>{\n                        const existingEntry = acc.find((entry)=>entry.reason_id === item.reason_id);\n                        if (existingEntry) {\n                            existingEntry.quantity += item.quantity;\n                            existingEntry.id.push(item.id);\n                            existingEntry.order_id.push(item.order_id);\n                            existingEntry.cust_codes.push(item.cust_code);\n                        } else {\n                            acc.push({\n                                added_by: item.added_by,\n                                comment: item.comment,\n                                delete_reason: null,\n                                deleted_by: null,\n                                id: [\n                                    item.id\n                                ],\n                                is_deleted: false,\n                                order_id: [\n                                    item.order_id\n                                ],\n                                quantity: item.quantity,\n                                reason: item.reasonsLabel,\n                                sub_reason: item.subReasonLabel,\n                                reason_id: item.reason_id,\n                                subreason_id: item.subreason_id,\n                                cust_codes: [\n                                    item.cust_code\n                                ]\n                            });\n                        }\n                        return acc;\n                    }, []);\n                    // Set the aggregated reasons data\n                    setReasonsData(reasonsDataArr);\n                }\n                setQuantity(\"\");\n                setComment(\"\");\n                setSelectedReasonDropdownValue(\"\");\n                setSelectedSubReasonDropdownValue(\"\");\n                setIsSaveButtonDisabled(false);\n            });\n        } catch (error) {\n            setIsSaveButtonDisabled(false);\n            console.error(\"Failed to save new reason.\", error);\n        }\n    };\n    const handleEdit = (id, orderId)=>{\n        setIsSaveButtonDisabled(false);\n        // console.log(\"save reasons data\",reasonsData);\n        if (!isValidEditQuantity) {\n            return;\n        }\n        const editData = currentData.map((item, index)=>{\n            var _reasonsMasterList_filter_, _reasonsMasterList_filter_1;\n            return {\n                quantity: isBulkUpdate ? item.CASES_DIFFERENCE : onEditQuantityValue,\n                reasons: onEditSelectedReason,\n                reasonsLabel: (_reasonsMasterList_filter_ = reasonsMasterList.filter((r)=>r.id == +onEditSelectedReason)[0]) === null || _reasonsMasterList_filter_ === void 0 ? void 0 : _reasonsMasterList_filter_.reason,\n                subReason: onEditSelectedSubReason,\n                subReasonLabel: (_reasonsMasterList_filter_1 = reasonsMasterList.filter((r)=>r.id == +onEditSelectedSubReason)[0]) === null || _reasonsMasterList_filter_1 === void 0 ? void 0 : _reasonsMasterList_filter_1.reason,\n                comment: onEditComment.trim(),\n                orderId: item.ORD_ID,\n                id: Array.isArray(id) ? id[index] : id,\n                updatedBy: userData.email,\n                originalEditQuantity: originalEditQuantity,\n                cust_code: item.CUSTOMER\n            };\n        });\n        const isValid = editData.every((item)=>{\n            const isQuantityValid = item.quantity && item.quantity !== \"\";\n            const isReasonValid = item.reasons && item !== \"\";\n            const isSubReasonValid = item.subReason && item.subReason !== \"\";\n            // Set individual validation states\n            if (!isQuantityValid) {\n                setIsValidEditQuantity(false);\n                setInvalidEditQuantityId(item.id);\n            }\n            if (!isReasonValid) {\n                setIsValidEditReasons(false);\n                setInvalidEditReasonsId(item.id);\n            }\n            if (!isSubReasonValid) {\n                setIsValidEditSubReasons(false);\n                setInvalidEditSubReasonsId(item.id);\n            }\n            return isQuantityValid && isReasonValid && isSubReasonValid;\n        });\n        if (!isValid) {\n            return;\n        }\n        setIsValidEditQuantity(true);\n        setIsValidEditReasons(true);\n        setInvalidEditQuantityId(\"\");\n        setInvalidEditReasonsId(\"\");\n        setIsBeingEdited(null);\n        setOriginalEditQuantity(null);\n        setIsValidEditSubReasons(true);\n        setInvalidEditSubReasonsId(\"\");\n        try {\n            const serverAddress = _services_apiConfig__WEBPACK_IMPORTED_MODULE_3__.apiConfig.serverAddress;\n            const user = getCookieData(\"user\");\n            fetch(\"\".concat(serverAddress, \"servicelevel/edit-reason\"), {\n                method: \"PUT\",\n                headers: {\n                    \"Content-Type\": \"application/json\",\n                    Authorization: \"Bearer \".concat(user.token)\n                },\n                body: JSON.stringify(editData)\n            }).then((res)=>{\n                if (res.status == 401) {\n                    react_toastify__WEBPACK_IMPORTED_MODULE_6__.toast.error(\"Your session has expired. Please log in again.\");\n                    setTimeout(()=>{\n                        localStorage.removeItem(\"superUser\");\n                        js_cookie__WEBPACK_IMPORTED_MODULE_7__[\"default\"].remove(\"company\");\n                        js_cookie__WEBPACK_IMPORTED_MODULE_7__[\"default\"].remove(\"ADCompanyName\");\n                        localStorage.removeItem(\"id\");\n                        localStorage.removeItem(\"name\");\n                        localStorage.removeItem(\"role\");\n                        localStorage.removeItem(\"email\");\n                        js_cookie__WEBPACK_IMPORTED_MODULE_7__[\"default\"].remove(\"user\");\n                        js_cookie__WEBPACK_IMPORTED_MODULE_7__[\"default\"].remove(\"theme\");\n                        js_cookie__WEBPACK_IMPORTED_MODULE_7__[\"default\"].remove(\"token\");\n                        const redirectUrl = \"/login?redirect=\".concat(encodeURIComponent(window.location.pathname));\n                        logoutHandler(instance, redirectUrl);\n                    }, 3000);\n                    return null;\n                } else if (res.status == 400) {\n                    react_toastify__WEBPACK_IMPORTED_MODULE_6__.toast.error(\"There was an error with your request. Please check your data and try again.\");\n                    return null;\n                } else {\n                    return res.json(); // Ensure you parse the JSON\n                }\n            }).then((json)=>{\n                if (!isBulkUpdate) {\n                    fetchReasonData(data[0].ORD_ID, currentCustomers[0]);\n                } else {\n                    const reasonsDataArr = editData.reduce((acc, item)=>{\n                        const existingEntry = acc.find((entry)=>parseInt(entry.reason_id) === parseInt(item.reasons));\n                        if (existingEntry) {\n                            existingEntry.quantity += item.quantity;\n                            existingEntry.id.push(item.id);\n                            existingEntry.order_id.push(item.orderId.toString());\n                            existingEntry.cust_codes.push(item.cust_code);\n                        } else {\n                            acc.push({\n                                added_by: item.updatedBy,\n                                comment: item.comment,\n                                delete_reason: null,\n                                deleted_by: null,\n                                id: [\n                                    item.id\n                                ],\n                                is_deleted: false,\n                                order_id: [\n                                    item.orderId.toString()\n                                ],\n                                quantity: item.quantity,\n                                reason: item.reasonsLabel,\n                                sub_reason: item.subReasonLabel,\n                                reason_id: parseInt(item.reasons),\n                                subreason_id: parseInt(item.subReason),\n                                cust_codes: [\n                                    item.cust_code\n                                ]\n                            });\n                        }\n                        return acc;\n                    }, []);\n                    // Set the aggregated reasons data\n                    setReasonsData(reasonsDataArr);\n                }\n            });\n            setIsSaveButtonDisabled(false);\n        } catch (error) {\n            setIsSaveButtonDisabled(false);\n            console.error(\"Failed to save new reason.\", error);\n        }\n    };\n    const handleDeleteReason = (orderId)=>{\n        // console.log(\"save reasons data\",reasonsData);\n        const deleteData = {\n            orderId,\n            deleteReason: deleteReason,\n            id: Array.isArray(deleteId) ? deleteId : [\n                deleteId\n            ],\n            deletedBy: userData.email,\n            deletedByName: userData.name\n        };\n        let totalCasesDifferent = bulkUpdateData.totalCasesAddedReasons;\n        setBulkUpdateData((prev)=>{\n            return {\n                ...prev,\n                totalCasesDifferent: totalCasesDifferent\n            };\n        });\n        try {\n            const serverAddress = _services_apiConfig__WEBPACK_IMPORTED_MODULE_3__.apiConfig.serverAddress;\n            const user = getCookieData(\"user\");\n            fetch(\"\".concat(serverAddress, \"servicelevel/delete-reason\"), {\n                method: \"PUT\",\n                headers: {\n                    \"Content-Type\": \"application/json\",\n                    Authorization: \"Bearer \".concat(user.token)\n                },\n                body: JSON.stringify(deleteData)\n            }).then((res)=>{\n                if (res.status == 401) {\n                    react_toastify__WEBPACK_IMPORTED_MODULE_6__.toast.error(\"Your session has expired. Please log in again.\");\n                    setTimeout(()=>{\n                        localStorage.removeItem(\"superUser\");\n                        js_cookie__WEBPACK_IMPORTED_MODULE_7__[\"default\"].remove(\"company\");\n                        js_cookie__WEBPACK_IMPORTED_MODULE_7__[\"default\"].remove(\"ADCompanyName\");\n                        localStorage.removeItem(\"id\");\n                        localStorage.removeItem(\"name\");\n                        localStorage.removeItem(\"role\");\n                        localStorage.removeItem(\"email\");\n                        js_cookie__WEBPACK_IMPORTED_MODULE_7__[\"default\"].remove(\"user\");\n                        js_cookie__WEBPACK_IMPORTED_MODULE_7__[\"default\"].remove(\"theme\");\n                        js_cookie__WEBPACK_IMPORTED_MODULE_7__[\"default\"].remove(\"token\");\n                        const redirectUrl = \"/login?redirect=\".concat(encodeURIComponent(window.location.pathname));\n                        logoutHandler(instance, redirectUrl);\n                    }, 3000);\n                    return null;\n                } else if (res.status == 400) {\n                    react_toastify__WEBPACK_IMPORTED_MODULE_6__.toast.error(\"There was an error with your request. Please check your data and try again.\");\n                    return null;\n                } else {\n                    return res.json(); // Ensure you parse the JSON\n                }\n            }).then((json)=>{\n                fetchReasonData(orderId, currentCustomers[0]);\n            });\n        } catch (error) {\n            console.error(\"Failed to save new reason.\", error);\n        }\n    };\n    const handleParentDropdownReasonChange = (e, type)=>{\n        let parentId;\n        if (typeof e === \"object\") {\n            parentId = parseInt(e.target.value);\n        } else {\n            parentId = e;\n        }\n        if (type == \"add\") {\n            setSelectedReasonDropdownValue(e.target.value);\n            setIsValidReasons(true);\n            if (e.target.value === \"30\") {\n                setSelectedSubReasonDropdownValue(31);\n                setIsValidComment(true);\n                setIsOtherSelected(true);\n            } else {\n                setSelectedSubReasonDropdownValue(\"\");\n                setIsOtherSelected(false);\n            }\n        }\n        setSubReasonsList(reasonsMasterList.filter((child)=>child.parent_id == parentId));\n    };\n    const handleChildDropdownSubReasonChange = (e)=>{\n        setSelectedSubReasonDropdownValue(parseInt(e.target.value));\n        setIsValidSubReasons(true);\n    };\n    const handleQuantityChange = (e)=>{\n        const value = e.target.value;\n        setQuantity(value);\n        const quantityValue = parseInt(value, 10) || 0;\n        22;\n        if (quantityValue <= 0 || quantityValue > data[0].CASES_DIFFERENCE - data[0].CASES_ADDED_REASONS) {\n            setIsValidQuantity(false);\n        } else {\n            setIsValidQuantity(true);\n        }\n    };\n    const handleCommentChange = (e)=>{\n        const value = e.target.value;\n        setComment(value);\n    };\n    const handleEditCommentChange = (e)=>{\n        const value = e.target.value;\n        setOnEditComment(value);\n    };\n    const handleEditQuantity = (e, reasonId)=>{\n        const value = e.target.value;\n        const quantityValue = parseInt(value, 10) || 0;\n        let totalExistingQuantity = reasonsData.reduce((total, reason)=>{\n            return reason.id === reasonId ? total : total + reason.quantity;\n        }, 0);\n        const maxAllowedQuantity = data[0].CASES_DIFFERENCE - data[0].CASES_ADDED_REASONS + originalEditQuantity;\n        if (quantityValue <= 0 || quantityValue > maxAllowedQuantity) {\n            setInvalidEditQuantityId(reasonId);\n            setIsValidEditQuantity(false);\n        } else {\n            setInvalidEditQuantityId(\"\");\n            setIsValidEditQuantity(true);\n        }\n        setOnEditQuantityValue(value);\n    };\n    const handleOpenChange = async (event, data)=>{\n        setIsOpen(data.open);\n        if (!data.open) {\n            setIsHeaderChecked(false);\n            setLockedBy(false);\n            setIsBeingEdited(null);\n            setOriginalEditQuantity(null);\n            setQuantity(\"\");\n            setComment(\"\");\n            setOnEditQuantityValue(\"\");\n            setOnEditSelectedReason(\"\");\n            setOnEditSelectedSubReason(\"\");\n            setDeleteId(\"\");\n            setDeleteReason(\"\");\n            setInvalidEditQuantityId(\"\");\n            setInvalidEditReasonsId(\"\");\n            setOriginalEditQuantity(\"\");\n            setIsValidQuantity(true);\n            setIsValidReasons(true);\n            setIsValidSubReasons(true);\n            setReasonsData([]);\n            setData([]);\n            setSelectedRows([]);\n            const serverAddress = _services_apiConfig__WEBPACK_IMPORTED_MODULE_3__.apiConfig.serverAddress;\n            const user = getCookieData(\"user\");\n            await fetch(\"\".concat(serverAddress, \"serviceLevel/remove-locks\"), {\n                method: \"POST\",\n                headers: {\n                    Authorization: \"Bearer \".concat(user.token),\n                    Accept: \"application/json\",\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    email: userData.email,\n                    custCode: currentCustomers,\n                    orderId: orderId,\n                    isPayloadRequired: true\n                })\n            }).catch((error)=>{\n                console.log(\"error\", error);\n            });\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!isBulkUpdate && data[0] && data[0].CASES_DIFFERENCE - data[0].CASES_ADDED_REASONS != 0) {\n            setQuantity(String(data[0].CASES_DIFFERENCE - data[0].CASES_ADDED_REASONS));\n        } else {\n            setQuantity(bulkUpdateData.totalCasesDifferent);\n        }\n        if (quantity <= data[0].CASES_DIFFERENCE - data[0].CASES_ADDED_REASONS && quantity != 0) {\n            setIsValidQuantity(true);\n        }\n    }, [\n        data,\n        isBulkUpdate\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fluentui_react_components__WEBPACK_IMPORTED_MODULE_8__.Dialog, {\n                modalType: \"non-modal\",\n                style: {\n                    fontFamily: \"poppinsregular\"\n                },\n                open: isOpen,\n                onOpenChange: handleOpenChange,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fluentui_react_components__WEBPACK_IMPORTED_MODULE_8__.DialogSurface, {\n                    className: \"!max-w-[60%]\",\n                    style: {\n                        fontFamily: \"poppinsregular\"\n                    },\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fluentui_react_components__WEBPACK_IMPORTED_MODULE_8__.DialogBody, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fluentui_react_components__WEBPACK_IMPORTED_MODULE_8__.DialogTitle, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-xl\",\n                                                children: \"View Details\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                                lineNumber: 626,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            !isBulkUpdate && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fluentui_react_components__WEBPACK_IMPORTED_MODULE_8__.Tooltip, {\n                                                content: \"Audit details for order\",\n                                                relationship: \"label\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>setIsAuditDetailsOpen(true),\n                                                    className: \"tooltip-button\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        fill: \"currentcolor\",\n                                                        xmlns: \"http://www.w3.org/2000/svg\",\n                                                        viewBox: \"0 0 512 512\",\n                                                        className: \"w-4 h-5 !text-skin-primary\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            d: \"M16 96a48 48 0 1 1 96 0A48 48 0 1 1 16 96zM64 208a48 48 0 1 1 0 96 48 48 0 1 1 0-96zm0 160a48 48 0 1 1 0 96 48 48 0 1 1 0-96zM191.5 54.4c5.5-4.2 12.3-6.4 19.2-6.4L424 48c13.3 0 24 10.7 24 24l0 48c0 13.3-10.7 24-24 24l-213.3 0c-6.9 0-13.7-2.2-19.2-6.4l-38.4-28.8c-8.5-6.4-8.5-19.2 0-25.6l38.4-28.8zM153.1 243.2l38.4-28.8c5.5-4.2 12.3-6.4 19.2-6.4L488 208c13.3 0 24 10.7 24 24l0 48c0 13.3-10.7 24-24 24l-277.3 0c-6.9 0-13.7-2.2-19.2-6.4l-38.4-28.8c-8.5-6.4-8.5-19.2 0-25.6zm0 160l38.4-28.8c5.5-4.2 12.3-6.4 19.2-6.4L424 368c13.3 0 24 10.7 24 24l0 48c0 13.3-10.7 24-24 24l-213.3 0c-6.9 0-13.7-2.2-19.2-6.4l-38.4-28.8c-8.5-6.4-8.5-19.2 0-25.6z\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                                            lineNumber: 642,\n                                                            columnNumber: 25\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                                        lineNumber: 636,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                                    lineNumber: 632,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                                lineNumber: 628,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                        lineNumber: 625,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"hr\", {\n                                        className: \"border-b border-gray-200\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                        lineNumber: 648,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                lineNumber: 624,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fluentui_react_components__WEBPACK_IMPORTED_MODULE_8__.DialogContent, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-col gap-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex gap-5 justify-between w-full\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-1/4 flex flex-col \",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            htmlFor: \"depotdate\",\n                                                            className: \"text-gray-500\",\n                                                            children: \"Sales Order / Order Det Id\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                                            lineNumber: 656,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"text\",\n                                                            className: \"px-2 2xl:px-3 border rounded-md w-full\",\n                                                            id: \"depotdate\",\n                                                            value: isBulkUpdate ? \"Multiple\" : \"\".concat(currentData[0].ORD_NUMBER, \" / \").concat(currentData[0].ORD_ID),\n                                                            readOnly: true,\n                                                            disabled: true\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                                            lineNumber: 659,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                                    lineNumber: 655,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \" flex w-1/2 flex-col\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            htmlFor: \"depotdate\",\n                                                            className: \"text-gray-500\",\n                                                            children: \"Product\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                                            lineNumber: 673,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"text\",\n                                                            className: \"px-2 2xl:px-3 border rounded-md w-full\",\n                                                            id: \"depotdate\",\n                                                            value: data[0].PRODUCT_DESCRIPTION,\n                                                            readOnly: true,\n                                                            disabled: true\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                                            lineNumber: 676,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                                    lineNumber: 672,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \" flex w-1/4 flex-col justify-end \",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-center rounded-lg w-28 capitalize h-[30px] px-2 py-1 !text-center \".concat(isBulkUpdate ? \"bg-theme-blue2 text-white\" : data[0].ORD_STATUS === \"Cancelled\" ? \"bg-[#ff2929] text-white\" : data[0].ORD_STATUS === \"Open\" ? \"bg-[#54C5ED] text-white\" : data[0].ORD_STATUS === \"Invoiced\" ? \"bg-[#FFAE00] text-white\" : data[0].ORD_STATUS === \"Delivered\" ? \"bg-[#3EAB58] text-white\" : data[0].ORD_STATUS === \"Picked\" ? \"bg-[#FF6C09] text-white\" : data[0].ORD_STATUS === \"CANCELLED-Invoiced\" ? \"bg-cancelled-status text-white\" : \"bg-qtydiff-status !text-gray-700\" // Default style for any other status\n                                                        ),\n                                                        children: isBulkUpdate ? \"Multiple\" : \"\".concat(data[0].ORD_STATUS.toLowerCase())\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                                        lineNumber: 687,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                                    lineNumber: 686,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                            lineNumber: 652,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex gap-5 justify-between w-full\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-1/4 flex flex-col\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            htmlFor: \"depotdate\",\n                                                            className: \"text-gray-500\",\n                                                            children: \"Depot Date\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                                            lineNumber: 714,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"text\",\n                                                            className: \"px-2 2xl:px-3 border rounded-md w-full\",\n                                                            id: \"depotdate\",\n                                                            value: isBulkUpdate ? \"Multiple\" : \"\".concat((0,_utils_whatif_utils_getFormattedDate__WEBPACK_IMPORTED_MODULE_5__.formatDisplay)(data[0].DEPOT_DATE)),\n                                                            readOnly: true,\n                                                            disabled: true\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                                            lineNumber: 717,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                                    lineNumber: 713,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-1/4 flex flex-col\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            htmlFor: \"altfill\",\n                                                            className: \"text-gray-500\",\n                                                            children: \"Altfill\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                                            lineNumber: 731,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"text\",\n                                                            className: \"px-2 2xl:px-3 border rounded-md w-full\",\n                                                            id: \"altfill\",\n                                                            value: data[0].ALTFILID,\n                                                            readOnly: true,\n                                                            disabled: true\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                                            lineNumber: 734,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                                    lineNumber: 730,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-1/4 flex flex-col\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            htmlFor: \"customer\",\n                                                            className: \"text-gray-500\",\n                                                            children: \"Customer\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                                            lineNumber: 744,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"text\",\n                                                            className: \"px-2 2xl:px-3 border rounded-md w-full\",\n                                                            id: \"customer\",\n                                                            value: isBulkUpdate ? \"Multiple\" : \"\".concat(data[0].CUSTOMER),\n                                                            readOnly: true,\n                                                            disabled: true\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                                            lineNumber: 747,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                                    lineNumber: 743,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-1/4 flex flex-col\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            htmlFor: \"category\",\n                                                            className: \"text-gray-500\",\n                                                            children: \"Master Product Code\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                                            lineNumber: 757,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"text\",\n                                                            className: \"px-2 2xl:px-3 border rounded-md w-full\",\n                                                            id: \"category\",\n                                                            value: data[0].MASTER_PRODUCT_CODE,\n                                                            readOnly: true,\n                                                            disabled: true\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                                            lineNumber: 760,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                                    lineNumber: 756,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                            lineNumber: 712,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex gap-5 justify-between w-full\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-1/4 flex flex-col\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            htmlFor: \"casesize\",\n                                                            className: \"text-gray-500\",\n                                                            children: \"Case Size\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                                            lineNumber: 773,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"text\",\n                                                            className: \"px-2 2xl:px-3 border rounded-md w-full\",\n                                                            id: \"casesize\",\n                                                            value: data[0].CASE_SIZE,\n                                                            readOnly: true,\n                                                            disabled: true\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                                            lineNumber: 776,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                                    lineNumber: 772,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-1/4 flex flex-col\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            htmlFor: \"casesord\",\n                                                            className: \"text-gray-500\",\n                                                            children: \"Cases Ordered\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                                            lineNumber: 786,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"text\",\n                                                            className: \"px-2 2xl:px-3 border rounded-md w-full\",\n                                                            id: \"casesord\",\n                                                            value: isBulkUpdate ? bulkUpdateData.totalCasesOrdered : \"\".concat(data[0].CASES_ORIGINAL),\n                                                            readOnly: true,\n                                                            disabled: true\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                                            lineNumber: 789,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                                    lineNumber: 785,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-1/4 flex flex-col\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            htmlFor: \"casedel\",\n                                                            className: \"text-gray-500\",\n                                                            children: \"Cases Delivered\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                                            lineNumber: 803,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"text\",\n                                                            className: \"px-2 2xl:px-3 border rounded-md w-full\",\n                                                            id: \"casedel\",\n                                                            value: isBulkUpdate ? bulkUpdateData.totalCasesDelivered : \"\".concat(data[0].CASES_DELIVERED),\n                                                            readOnly: true,\n                                                            disabled: true\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                                            lineNumber: 806,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                                    lineNumber: 802,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-1/4 flex flex-col\",\n                                                    title: \"The following case differences are the absolute sum of all differences\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            htmlFor: \"casesdiff\",\n                                                            className: \"text-gray-500 font-bold\",\n                                                            children: \"Cases Different\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                                            lineNumber: 823,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"text\",\n                                                            className: \"px-2 2xl:px-3 border rounded-md w-full font-bold\",\n                                                            id: \"casesdiff\",\n                                                            value: isBulkUpdate ? bulkUpdateData.totalCasesDifferent : \"\".concat(data[0].CASES_DIFFERENCE - data[0].CASES_ADDED_REASONS),\n                                                            readOnly: true,\n                                                            disabled: true\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                                            lineNumber: 829,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                                    lineNumber: 819,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                            lineNumber: 771,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        (!isBulkUpdate || isBulkUpdate && reasonsData.length == 0) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"border border-gray-200 p-4 rounded-md\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    style: {\n                                                        fontFamily: \"poppinsregular\"\n                                                    },\n                                                    children: \"Add the reason(s)\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                                    lineNumber: 852,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"hr\", {\n                                                    className: \"border-b border-gray-200\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                                    lineNumber: 855,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex gap-4 justify-between pt-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-[10%] flex flex-col\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    htmlFor: \"quantity\",\n                                                                    className: \"text-gray-500\",\n                                                                    children: \"Quantity\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                                                    lineNumber: 858,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                    type: \"number\",\n                                                                    onChange: handleQuantityChange,\n                                                                    className: \"px-2 2xl:px-3 border rounded-md \".concat(!isValidQuantity && \"!border-red-500\"),\n                                                                    value: quantity,\n                                                                    max: data[0].CASES_DIFFERENCE - data[0].CASES_ADDED_REASONS,\n                                                                    id: \"quantity\",\n                                                                    disabled: isBeingEdited || isBulkUpdate\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                                                    lineNumber: 861,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                                            lineNumber: 857,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-[30%] flex flex-col\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    htmlFor: \"reason\",\n                                                                    className: \"text-gray-500\",\n                                                                    children: \"Reason\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                                                    lineNumber: 877,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                                    onChange: (e)=>handleParentDropdownReasonChange(e, \"add\"),\n                                                                    className: \"px-2 2xl:px-3 border \".concat(!isValidReason && \"!border-red-500\", \" rounded-md w-full h-[31px]\"),\n                                                                    value: selectedReasonDropdownValue,\n                                                                    id: \"reason\",\n                                                                    disabled: isBeingEdited,\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                            value: \"\",\n                                                                            children: \"Select...\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                                                            lineNumber: 892,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        parentReasonList === null || parentReasonList === void 0 ? void 0 : parentReasonList.map((parentReason, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                value: parentReason.id,\n                                                                                children: parentReason.reason\n                                                                            }, \"\".concat(parentReason.id, \"-\").concat(index), false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                                                                lineNumber: 894,\n                                                                                columnNumber: 29\n                                                                            }, undefined))\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                                                    lineNumber: 881,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                                            lineNumber: 876,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-[30%] flex flex-col\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    htmlFor: \"subreasons\",\n                                                                    className: \"text-gray-500\",\n                                                                    children: \"Sub Reason\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                                                    lineNumber: 905,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                                    onChange: handleChildDropdownSubReasonChange,\n                                                                    disabled: !selectedReasonDropdownValue || isBeingEdited || isOtherSelected,\n                                                                    className: \"px-2 2xl:px-3 border rounded-md w-full h-[31px] \".concat(!isValidSubReason && \"!border-red-500\"),\n                                                                    value: selectedSubReasonDropdownValue,\n                                                                    id: \"subreasons\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                            value: \"\",\n                                                                            children: \"Select...\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                                                            lineNumber: 922,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        subReasonsList === null || subReasonsList === void 0 ? void 0 : subReasonsList.map((subReason, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                value: subReason.id,\n                                                                                children: subReason.reason\n                                                                            }, \"\".concat(subReason.id, \"-\").concat(index), false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                                                                lineNumber: 924,\n                                                                                columnNumber: 29\n                                                                            }, undefined))\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                                                    lineNumber: 909,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                                            lineNumber: 904,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-[25%] flex flex-col\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    htmlFor: \"comment\",\n                                                                    className: \"text-gray-500\",\n                                                                    children: \"Comment\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                                                    lineNumber: 934,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                    type: \"text\",\n                                                                    onChange: handleCommentChange,\n                                                                    maxLength: 200,\n                                                                    className: \"px-2 2xl:px-3 border rounded-md w-full \".concat(!isValidComment && \"!border-red-500\"),\n                                                                    id: \"comment\",\n                                                                    value: comment,\n                                                                    disabled: isBeingEdited\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                                                    lineNumber: 937,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                                            lineNumber: 933,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-[5%] flex flex-col\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                className: \"mt-8\",\n                                                                onClick: ()=>saveReasons(),\n                                                                disabled: isSaveButtonDisabled || isBeingEdited || !isValidQuantity,\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                    fill: \"currentcolor\",\n                                                                    xmlns: \"http://www.w3.org/2000/svg\",\n                                                                    viewBox: \"0 0 512 512\",\n                                                                    className: \"w-5 h-5 fill !text-skin-primary\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                        d: \"M57.6 35.8C23.1 20.6-11.3 57.4 6.1 90.9l63 121.2c4.4 8.4 12.6 14.1 22 15.3c0 0 0 0 0 0L266 249.3c3.4 .4 6 3.3 6 6.7s-2.6 6.3-6 6.7L91.1 284.6s0 0 0 0c-9.4 1.2-17.6 6.9-22 15.3L6.1 421.1c-17.4 33.5 17 70.2 51.6 55.1L492.9 285.3c25.5-11.2 25.5-47.4 0-58.6L57.6 35.8z\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                                                        lineNumber: 965,\n                                                                        columnNumber: 29\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                                                    lineNumber: 959,\n                                                                    columnNumber: 27\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                                                lineNumber: 950,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                                            lineNumber: 949,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                                    lineNumber: 856,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                            lineNumber: 851,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"previousreasons flex flex-col gap-3\",\n                                            children: reasonsData === null || reasonsData === void 0 ? void 0 : reasonsData.map((reason, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex flex-col gap-3 bg-gray-100 rounded-md p-4 my-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex gap-4 justify-between pt-3 \",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"w-[10%] flex flex-col\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                htmlFor: \"quantity\",\n                                                                                className: \"text-gray-500\",\n                                                                                children: \"Quantity\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                                                                lineNumber: 985,\n                                                                                columnNumber: 29\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                type: \"number\",\n                                                                                onChange: (e)=>handleEditQuantity(e, reason.id),\n                                                                                className: \"px-2 2xl:px-3 border \".concat(invalidEditQuantityId == reason.id && !isValidEditQuantity && \"!border-red-500\", \" rounded-md w-full\"),\n                                                                                id: \"quantity\",\n                                                                                disabled: isBeingEdited != reason.id || isBulkUpdate,\n                                                                                defaultValue: reason.quantity\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                                                                lineNumber: 988,\n                                                                                columnNumber: 29\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                                                        lineNumber: 984,\n                                                                        columnNumber: 27\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"w-[30%] flex flex-col\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                htmlFor: \"reason1\",\n                                                                                className: \"text-gray-500\",\n                                                                                children: \"Reason\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                                                                lineNumber: 1004,\n                                                                                columnNumber: 29\n                                                                            }, undefined),\n                                                                            isBeingEdited != reason.id ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                type: \"text\",\n                                                                                className: \"px-2 2xl:px-3 border \".concat(invalidEditReasonsId == reason.id && !isValidEditReason && \"!border-red-500\", \" rounded-md w-full\"),\n                                                                                id: \"reason1\",\n                                                                                disabled: isBeingEdited != reason.id,\n                                                                                defaultValue: reason.reason\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                                                                lineNumber: 1008,\n                                                                                columnNumber: 31\n                                                                            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                                                onChange: (e)=>{\n                                                                                    const selectedValue = e.target.value;\n                                                                                    setOnEditSelectedReason(selectedValue);\n                                                                                    setIsValidEditReasons(true);\n                                                                                    setInvalidEditReasonsId(\"\");\n                                                                                    handleParentDropdownReasonChange(e, \"edit\");\n                                                                                },\n                                                                                className: \"px-2 2xl:px-3 border \".concat(invalidEditReasonsId == reason.id && !isValidEditReason && \"!border-red-500\", \" rounded-md w-full h-[31px]\"),\n                                                                                id: \"reason1\",\n                                                                                value: onEditSelectedReason,\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                        value: \"\",\n                                                                                        children: \"Select...\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                                                                        lineNumber: 1036,\n                                                                                        columnNumber: 33\n                                                                                    }, undefined),\n                                                                                    parentReasonList === null || parentReasonList === void 0 ? void 0 : parentReasonList.map((parentReason, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                            value: parentReason.id,\n                                                                                            children: parentReason.reason\n                                                                                        }, \"\".concat(parentReason.id, \"-\").concat(index), false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                                                                            lineNumber: 1038,\n                                                                                            columnNumber: 35\n                                                                                        }, undefined))\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                                                                lineNumber: 1020,\n                                                                                columnNumber: 31\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                                                        lineNumber: 1003,\n                                                                        columnNumber: 27\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"w-[30%] flex flex-col\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                htmlFor: \"subreasons1\",\n                                                                                className: \"text-gray-500\",\n                                                                                children: \"Sub Reason\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                                                                lineNumber: 1049,\n                                                                                columnNumber: 29\n                                                                            }, undefined),\n                                                                            isBeingEdited != reason.id ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                type: \"text\",\n                                                                                onChange: (e)=>setOnEditSelectedSubReason(e.target.value),\n                                                                                className: \"px-2 2xl:px-3 border rounded-md w-full \".concat(invalidEditSubReasonsId == reason.id && !isValidEditSubReason && \"!border-red-500\"),\n                                                                                id: \"subreasons1\",\n                                                                                disabled: isBeingEdited != reason.id,\n                                                                                defaultValue: reason.sub_reason\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                                                                lineNumber: 1056,\n                                                                                columnNumber: 31\n                                                                            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                                                onChange: (e)=>setOnEditSelectedSubReason(e.target.value),\n                                                                                className: \"px-2 2xl:px-3 border rounded-md w-full h-[31px]\",\n                                                                                id: \"subreasons1\",\n                                                                                value: onEditSelectedSubReason,\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                        value: \"\",\n                                                                                        children: \"Select...\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                                                                        lineNumber: 1079,\n                                                                                        columnNumber: 33\n                                                                                    }, undefined),\n                                                                                    subReasonsList === null || subReasonsList === void 0 ? void 0 : subReasonsList.map((subReason)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                            value: subReason.id,\n                                                                                            children: subReason.reason\n                                                                                        }, \"\".concat(subReason.id, \"-\").concat(index), false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                                                                            lineNumber: 1081,\n                                                                                            columnNumber: 35\n                                                                                        }, undefined))\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                                                                lineNumber: 1071,\n                                                                                columnNumber: 31\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                                                        lineNumber: 1048,\n                                                                        columnNumber: 27\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"w-[25%] flex flex-col\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                htmlFor: \"comment\",\n                                                                                className: \"text-gray-500\",\n                                                                                children: \"Comment\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                                                                lineNumber: 1092,\n                                                                                columnNumber: 29\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                type: \"text\",\n                                                                                onChange: handleEditCommentChange,\n                                                                                className: \"px-2 2xl:px-3 border rounded-md w-full\",\n                                                                                id: \"comment\",\n                                                                                disabled: isBeingEdited != reason.id,\n                                                                                defaultValue: reason.comment\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                                                                lineNumber: 1095,\n                                                                                columnNumber: 29\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                                                        lineNumber: 1091,\n                                                                        columnNumber: 27\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"w-[5%] flex flex-col\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                                                        lineNumber: 1104,\n                                                                        columnNumber: 27\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                                                lineNumber: 983,\n                                                                columnNumber: 25\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex justify-between items-center\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex gap-2 items-center\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fluentui_react_components__WEBPACK_IMPORTED_MODULE_8__.Avatar, {\n                                                                                // initials=\"LT\"\n                                                                                color: \"light-teal\",\n                                                                                name: reason.added_by\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                                                                lineNumber: 1108,\n                                                                                columnNumber: 29\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                children: [\n                                                                                    reason.added_by,\n                                                                                    \" \"\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                                                                lineNumber: 1113,\n                                                                                columnNumber: 29\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-gray-400\",\n                                                                                children: reason.date\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                                                                lineNumber: 1114,\n                                                                                columnNumber: 29\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                                                        lineNumber: 1107,\n                                                                        columnNumber: 27\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex justify-end gap-4 pt-2\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_DeleteReasonPopover__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                                setDeleteId: setDeleteId,\n                                                                                setDeleteReason: setDeleteReason,\n                                                                                setIsDeleteTrue: setIsDeleteTrue,\n                                                                                id: reason.id\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                                                                lineNumber: 1117,\n                                                                                columnNumber: 29\n                                                                            }, undefined),\n                                                                            isBeingEdited && isBeingEdited == reason.id ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                                    fill: \"currentcolor\",\n                                                                                    xmlns: \"http://www.w3.org/2000/svg\",\n                                                                                    viewBox: \"0 0 512 512\",\n                                                                                    className: \"w-5 h-5 !text-skin-primary\",\n                                                                                    onClick: ()=>handleEdit(reason.id, data[0].ORD_ID),\n                                                                                    disabled: !isValidEditQuantity || isSaveButtonDisabled,\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                        d: \"M57.6 35.8C23.1 20.6-11.3 57.4 6.1 90.9l63 121.2c4.4 8.4 12.6 14.1 22 15.3c0 0 0 0 0 0L266 249.3c3.4 .4 6 3.3 6 6.7s-2.6 6.3-6 6.7L91.1 284.6s0 0 0 0c-9.4 1.2-17.6 6.9-22 15.3L6.1 421.1c-17.4 33.5 17 70.2 51.6 55.1L492.9 285.3c25.5-11.2 25.5-47.4 0-58.6L57.6 35.8z\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                                                                        lineNumber: 1137,\n                                                                                        columnNumber: 35\n                                                                                    }, undefined)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                                                                    lineNumber: 1125,\n                                                                                    columnNumber: 33\n                                                                                }, undefined)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                                                                lineNumber: 1124,\n                                                                                columnNumber: 31\n                                                                            }, undefined) : !isBulkUpdate ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                className: \"b\",\n                                                                                href: \"\",\n                                                                                onClick: ()=>{\n                                                                                    setIsBeingEdited(reason.id);\n                                                                                    setOriginalEditQuantity(reason.quantity);\n                                                                                    setMapForReasonsParentsAndTheirCorrespondingChildren();\n                                                                                    handleParentDropdownReasonChange(reason.reason_id, \"edit\");\n                                                                                    setOnEditComment(reason.comment);\n                                                                                    setOnEditQuantityValue(reason.quantity);\n                                                                                    setOnEditSelectedReason(reason.reason_id);\n                                                                                    setOnEditSelectedSubReason(reason.subreason_id);\n                                                                                },\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                                    fill: \"currentcolor\",\n                                                                                    xmlns: \"http://www.w3.org/2000/svg\",\n                                                                                    viewBox: \"0 0 512 512\",\n                                                                                    className: \"w-5 h-5 !text-skin-primary\",\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                        d: \"M362.7 19.3L314.3 67.7 444.3 197.7l48.4-48.4c25-25 25-65.5 0-90.5L453.3 19.3c-25-25-65.5-25-90.5 0zm-71 71L58.6 323.5c-10.4 10.4-18 23.3-22.2 37.4L1 481.2C-1.5 489.7 .8 498.8 7 505s15.3 8.5 23.7 6.1l120.3-35.4c14.1-4.2 27-11.8 37.4-22.2L421.7 220.3 291.7 90.3z\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                                                                        lineNumber: 1166,\n                                                                                        columnNumber: 35\n                                                                                    }, undefined)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                                                                    lineNumber: 1160,\n                                                                                    columnNumber: 33\n                                                                                }, undefined)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                                                                lineNumber: 1141,\n                                                                                columnNumber: 31\n                                                                            }, undefined) : \"\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                                                        lineNumber: 1116,\n                                                                        columnNumber: 27\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                                                lineNumber: 1106,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                                        lineNumber: 982,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                }, \"\".concat(reason.id, \"-\").concat(index), false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                                    lineNumber: 981,\n                                                    columnNumber: 21\n                                                }, undefined))\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                            lineNumber: 978,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                    lineNumber: 651,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                lineNumber: 650,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fluentui_react_components__WEBPACK_IMPORTED_MODULE_8__.DialogActions, {\n                                className: \"!mt-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"hr\", {\n                                        className: \"border-b border-gray-200\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                        lineNumber: 1181,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fluentui_react_components__WEBPACK_IMPORTED_MODULE_8__.DialogTrigger, {\n                                        disableButtonEnhancement: true,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            className: \"border rounded-md border-skin-primary text-skin-primary px-5 py-1\",\n                                            children: \"Close\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                            lineNumber: 1183,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                        lineNumber: 1182,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                lineNumber: 1180,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                        lineNumber: 623,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                    lineNumber: 619,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                lineNumber: 613,\n                columnNumber: 7\n            }, undefined),\n            isAuditDetailsOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_AuditDetails__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                orderId: data[0].ORD_ID,\n                isAuditDetailsOpen: isAuditDetailsOpen,\n                setIsAuditDetailsOpen: setIsAuditDetailsOpen\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                lineNumber: 1192,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true);\n};\n_s(ViewDetails, \"fDS39ZFRHz/erTdGHfQx9Du9dMA=\");\n_c = ViewDetails;\n/* harmony default export */ __webpack_exports__[\"default\"] = (ViewDetails);\nvar _c;\n$RefreshReg$(_c, \"ViewDetails\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevExports = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevExports) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports on update so we can compare the boundary\n                // signatures.\n                module.hot.dispose(function (data) {\n                    data.prevExports = currentExports;\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevExports !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevExports, currentExports)) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevExports !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/service_level/ViewDetails.jsx\n"));

/***/ })

});