"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/raw-material-request/add",{

/***/ "./pages/raw-material-request/add/index.js":
/*!*************************************************!*\
  !*** ./pages/raw-material-request/add/index.js ***!
  \*************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __N_SSP: function() { return /* binding */ __N_SSP; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _components_Layout__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/Layout */ \"./components/Layout.js\");\n/* harmony import */ var _components_RawMaterialRequest__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/RawMaterialRequest */ \"./components/RawMaterialRequest.js\");\n/* harmony import */ var _services_apiConfig__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/services/apiConfig */ \"./services/apiConfig.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var react_loader_spinner__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! react-loader-spinner */ \"./node_modules/react-loader-spinner/dist/module.js\");\n/* harmony import */ var react_toastify__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! react-toastify */ \"./node_modules/react-toastify/dist/react-toastify.esm.mjs\");\n/* harmony import */ var react_toastify_dist_ReactToastify_css__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! react-toastify/dist/ReactToastify.css */ \"./node_modules/react-toastify/dist/ReactToastify.css\");\n/* harmony import */ var react_toastify_dist_ReactToastify_css__WEBPACK_IMPORTED_MODULE_8___default = /*#__PURE__*/__webpack_require__.n(react_toastify_dist_ReactToastify_css__WEBPACK_IMPORTED_MODULE_8__);\n/* harmony import */ var _azure_msal_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @azure/msal-react */ \"./node_modules/@azure/msal-react/dist/index.js\");\n/* harmony import */ var _utils_extractCompanyFromEmail__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/utils/extractCompanyFromEmail */ \"./utils/extractCompanyFromEmail.js\");\n/* harmony import */ var _utils_secureStorage__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/utils/secureStorage */ \"./utils/secureStorage.js\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n//\n\n\n\nconst index = (param)=>{\n    let { userData } = param;\n    _s();\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_4__.useRouter)();\n    const [dropdowns, setDropdowns] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_5__.useEffect)(()=>{\n        // const company=Cookies.get(\"company\");\n        const company = (userData === null || userData === void 0 ? void 0 : userData.company) || (0,_utils_extractCompanyFromEmail__WEBPACK_IMPORTED_MODULE_10__.extractCompanyFromEmail)(userData === null || userData === void 0 ? void 0 : userData.email);\n        let prophetId = 1;\n        if (company == \"dpsltd\") {\n            prophetId = 1;\n        } else if (company == \"efcltd\") {\n            prophetId = 3;\n        } else if (company == \"fpp-ltd\") {\n            prophetId = 4;\n        }\n        const fetchData = async ()=>{\n            const serverAddress = _services_apiConfig__WEBPACK_IMPORTED_MODULE_3__.apiConfig.serverAddress;\n            try {\n                const allDropDowns = [\n                    \"reasonForRequest\",\n                    \"masterProductCode\",\n                    \"productType\",\n                    \"organicCertification\",\n                    \"markVariety\",\n                    \"temperatureGrade\",\n                    \"intrastatCommodityCode\",\n                    \"classifiedAllergicTypes\",\n                    \"countryOfOrigin\",\n                    \"brand\",\n                    \"caliberSize\",\n                    \"endCustomer\",\n                    \"variety\",\n                    \"subProductCode\"\n                ];\n                const productType = 0;\n                const res = await fetch(\"\".concat(serverAddress, \"products/get-products-dropdowns-list?prophetId=\").concat(prophetId, \"&productType=\").concat(productType), {\n                    method: \"POST\",\n                    headers: {\n                        Accept: \"application/json\",\n                        \"Content-Type\": \"application/json\"\n                    },\n                    credentials: \"include\",\n                    body: JSON.stringify(allDropDowns)\n                });\n                // console.log(\"dropdownsRequest\",res);\n                if (res.status === 401) {\n                    react_toastify__WEBPACK_IMPORTED_MODULE_7__.toast.error(\"Your session has expired. Please log in again.\");\n                    setTimeout(async ()=>{\n                        await (0,_utils_secureStorage__WEBPACK_IMPORTED_MODULE_11__.logout)();\n                        router.push(\"/login\");\n                    }, 3000);\n                    return null;\n                }\n                const allDropdownsList = await res.json();\n                setDropdowns(allDropdownsList);\n            } catch (error) {\n                console.error(\"Error fetching data\", error);\n            }\n        };\n        fetchData();\n    }, []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Layout__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n        userData: userData,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_toastify__WEBPACK_IMPORTED_MODULE_7__.ToastContainer, {\n                limit: 1\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\raw-material-request\\\\add\\\\index.js\",\n                lineNumber: 87,\n                columnNumber: 7\n            }, undefined),\n            !dropdowns ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    display: \"flex\",\n                    alignItems: \"center\",\n                    justifyContent: \"center\",\n                    height: \"calc(100vh - 100px)\"\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_loader_spinner__WEBPACK_IMPORTED_MODULE_6__.ThreeCircles, {\n                    color: \"#002D73\",\n                    height: 50,\n                    width: 50,\n                    visible: true,\n                    ariaLabel: \"oval-loading\",\n                    secondaryColor: \"#0066FF\",\n                    strokeWidth: 2,\n                    strokeWidthSecondary: 2\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\raw-material-request\\\\add\\\\index.js\",\n                    lineNumber: 98,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\raw-material-request\\\\add\\\\index.js\",\n                lineNumber: 89,\n                columnNumber: 9\n            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_RawMaterialRequest__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                dropdowns: dropdowns,\n                userData: userData,\n                pageType: \"add\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\raw-material-request\\\\add\\\\index.js\",\n                lineNumber: 111,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\raw-material-request\\\\add\\\\index.js\",\n        lineNumber: 86,\n        columnNumber: 5\n    }, undefined);\n};\n_s(index, \"7VX/J9HMM/vAzuwGBGyt+TdLtIQ=\", false, function() {\n    return [\n        next_router__WEBPACK_IMPORTED_MODULE_4__.useRouter\n    ];\n});\nvar __N_SSP = true;\n/* harmony default export */ __webpack_exports__[\"default\"] = (index);\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevExports = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevExports) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports on update so we can compare the boundary\n                // signatures.\n                module.hot.dispose(function (data) {\n                    data.prevExports = currentExports;\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevExports !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevExports, currentExports)) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevExports !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./pages/raw-material-request/add/index.js\n"));

/***/ })

});