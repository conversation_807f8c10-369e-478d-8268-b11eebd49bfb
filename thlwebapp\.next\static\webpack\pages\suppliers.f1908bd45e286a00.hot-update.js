"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/suppliers",{

/***/ "./pages/suppliers.js":
/*!****************************!*\
  !*** ./pages/suppliers.js ***!
  \****************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __N_SSP: function() { return /* binding */ __N_SSP; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _components_Layout__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/Layout */ \"./components/Layout.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _fortawesome_react_fontawesome__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @fortawesome/react-fontawesome */ \"./node_modules/@fortawesome/react-fontawesome/index.es.js\");\n/* harmony import */ var _fortawesome_free_solid_svg_icons__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! @fortawesome/free-solid-svg-icons */ \"./node_modules/@fortawesome/free-solid-svg-icons/index.mjs\");\n/* harmony import */ var ag_grid_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ag-grid-react */ \"./node_modules/ag-grid-react/lib/main.js\");\n/* harmony import */ var ag_grid_react__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(ag_grid_react__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var ag_grid_community_styles_ag_grid_css__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ag-grid-community/styles//ag-grid.css */ \"./node_modules/ag-grid-community/styles/ag-grid.css\");\n/* harmony import */ var ag_grid_community_styles_ag_grid_css__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(ag_grid_community_styles_ag_grid_css__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var ag_grid_community_styles_ag_theme_alpine_css__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ag-grid-community/styles//ag-theme-alpine.css */ \"./node_modules/ag-grid-community/styles/ag-theme-alpine.css\");\n/* harmony import */ var ag_grid_community_styles_ag_theme_alpine_css__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(ag_grid_community_styles_ag_theme_alpine_css__WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var _utils_renderer_actionRenderer__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../utils/renderer/actionRenderer */ \"./utils/renderer/actionRenderer.js\");\n/* harmony import */ var _utils_renderer_nameRenderer__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ../utils/renderer/nameRenderer */ \"./utils/renderer/nameRenderer.js\");\n/* harmony import */ var _utils_renderer_statusRenderer__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ../utils/renderer/statusRenderer */ \"./utils/renderer/statusRenderer.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! next/link */ \"./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_10___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_10__);\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_11___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_11__);\n/* harmony import */ var _services_apiConfig__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/services/apiConfig */ \"./services/apiConfig.js\");\n/* harmony import */ var js_cookie__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! js-cookie */ \"./node_modules/js-cookie/dist/js.cookie.mjs\");\n/* harmony import */ var _utils_auth_auth__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/utils/auth/auth */ \"./utils/auth/auth.js\");\n/* harmony import */ var react_toastify__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! react-toastify */ \"./node_modules/react-toastify/dist/react-toastify.esm.mjs\");\n/* harmony import */ var react_toastify_dist_ReactToastify_css__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! react-toastify/dist/ReactToastify.css */ \"./node_modules/react-toastify/dist/ReactToastify.css\");\n/* harmony import */ var react_toastify_dist_ReactToastify_css__WEBPACK_IMPORTED_MODULE_16___default = /*#__PURE__*/__webpack_require__.n(react_toastify_dist_ReactToastify_css__WEBPACK_IMPORTED_MODULE_16__);\n/* harmony import */ var _utils_loaders_loadingContext__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! @/utils/loaders/loadingContext */ \"./utils/loaders/loadingContext.js\");\n/* harmony import */ var _utils_getCookieData__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! @/utils/getCookieData */ \"./utils/getCookieData.js\");\n/* harmony import */ var _azure_msal_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! @azure/msal-react */ \"./node_modules/@azure/msal-react/dist/index.js\");\n/* harmony import */ var _utils_userContext__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! @/utils/userContext */ \"./utils/userContext.js\");\n/* harmony import */ var _utils_rolePermissionsContext__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! @/utils/rolePermissionsContext */ \"./utils/rolePermissionsContext.js\");\n/* harmony import */ var _barrel_optimize_names_Dialog_Transition_headlessui_react__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=Dialog,Transition!=!@headlessui/react */ \"__barrel_optimize__?names=Dialog,Transition!=!./node_modules/@headlessui/react/dist/headlessui.esm.js\");\n/* harmony import */ var _utils_exportExcel__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! ../utils/exportExcel */ \"./utils/exportExcel.js\");\n/* harmony import */ var _components_supplierCodeRenderer__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! @/components/supplierCodeRenderer */ \"./components/supplierCodeRenderer.js\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n//import 'ag-grid-enterprise';\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst suppliers = (param)=>{\n    let { userData, token } = param;\n    _s();\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_11__.useRouter)();\n    const { permissions, updatePermissions } = (0,_utils_rolePermissionsContext__WEBPACK_IMPORTED_MODULE_21__.usePermissions)();\n    const [pageSize, setPageSize] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(15);\n    const [allRowData, setAllRowData] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [rowData, setRowData] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [isFiltered, setIsFiltered] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [isFilteredName, setIsFilteredName] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const gridRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)();\n    const { setIsLoading } = (0,_utils_loaders_loadingContext__WEBPACK_IMPORTED_MODULE_17__.useLoading)();\n    const [supplierRoles, setSupplierRoles] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [multipleFilterInternalData, setMultipleFilterInternalData] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [containsCancelledSupplier, setContainsCancelledSupplier] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [exportDisabled, setExportDisabled] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(true);\n    const [supplierCheckedValue, setSupplierCheckedValue] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const { instance, accounts } = (0,_azure_msal_react__WEBPACK_IMPORTED_MODULE_19__.useMsal)();\n    const { userDetails } = (0,_utils_userContext__WEBPACK_IMPORTED_MODULE_20__.useUser)();\n    const [isCommonError, setCommonError] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"\");\n    const [isOpenOption, setIsOpenOption] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [isUnExportable, setIsUnExportable] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [unExportableSuppliernames, setUnExportableSupplierNames] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"\");\n    const [supplierUniqueCodeToast, setSupplierUniqueCodeToast] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [supplierCodeValid, setSupplierCodeValid] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(true);\n    const [selectedExportType, setSelectedExportType] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"\");\n    const [prophetId, setProphetId] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"\");\n    const [emailStatusPopup, setEmailStatusPopup] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [popupMessage, setPopUpMessage] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"\");\n    const [internalExportSuccess, setInternalExportSuccess] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [ISSExportSuccess, setISSExportSuccess] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [blockScreen, setBlockScreen] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const user = (0,_utils_getCookieData__WEBPACK_IMPORTED_MODULE_18__.getCookieData)(\"user\");\n    const [selectedStatus, setSelectedStatus] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"default\"); // State to track selected status\n    const closeOptionModal = ()=>{\n        setIsOpenOption(false);\n    };\n    const handleExportType = (e)=>{\n        setSelectedExportType(e.target.value);\n    };\n    const closeEmailPopup = ()=>{\n        setEmailStatusPopup(false);\n        if (internalExportSuccess && ISSExportSuccess) {\n            setStatusChange();\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        if (containsCancelledSupplier) {\n            react_toastify__WEBPACK_IMPORTED_MODULE_15__.toast.error(//todo: change this message to make it better\n            \"Cannot export cancelled supplier.\");\n            setExportDisabled(true);\n        }\n    }, [\n        containsCancelledSupplier\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        if (supplierUniqueCodeToast && !supplierCodeValid) {\n            react_toastify__WEBPACK_IMPORTED_MODULE_15__.toast.error(//todo: change this message to make it better\n            \"Supplier code is not unique and not valid, kindly make sure the supplier code is unique and valid.\");\n        } else if (supplierUniqueCodeToast) {\n            react_toastify__WEBPACK_IMPORTED_MODULE_15__.toast.error(//todo: change this message to make it better\n            \"Supplier code is not unique, kindly make sure the supplier code is unique.\");\n        } else if (!supplierCodeValid) {\n            react_toastify__WEBPACK_IMPORTED_MODULE_15__.toast.error(\"Supplier code is not valid\");\n        }\n    }, [\n        supplierUniqueCodeToast,\n        supplierCodeValid\n    ]);\n    const [company, setCompany] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"\");\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        if (typeof document !== \"undefined\") {\n            document.title = \"Suppliers\";\n        }\n        if (true) {\n            const company = js_cookie__WEBPACK_IMPORTED_MODULE_13__[\"default\"].get(\"company\");\n            if (company) {\n                setCompany(company);\n            }\n            localStorage.removeItem(\"current\");\n            localStorage.removeItem(\"allowedSections\");\n        }\n        js_cookie__WEBPACK_IMPORTED_MODULE_13__[\"default\"].remove(\"prophet\");\n        js_cookie__WEBPACK_IMPORTED_MODULE_13__[\"default\"].remove(\"prophets\");\n        setIsLoading(false);\n        getData(userData).then((data)=>{\n            if (data === null) {\n                return;\n            }\n            const formattedData = data === null || data === void 0 ? void 0 : data.map((row)=>{\n                const roleIds = (row === null || row === void 0 ? void 0 : row.role_ids) ? JSON.parse(row === null || row === void 0 ? void 0 : row.role_ids).map((ele)=>ele.role_id) : [];\n                const roleJson = JSON.parse(row === null || row === void 0 ? void 0 : row.role_json);\n                const formattedRow = {\n                    isActive: row === null || row === void 0 ? void 0 : row.is_active,\n                    prophets: (row === null || row === void 0 ? void 0 : row.prophet_ids) ? JSON.parse(row === null || row === void 0 ? void 0 : row.prophet_ids) : [],\n                    supplier_code: (row === null || row === void 0 ? void 0 : row.prophet_ids) ? JSON.parse(row === null || row === void 0 ? void 0 : row.prophet_ids)[0].prophet_code : [],\n                    company_name: (row === null || row === void 0 ? void 0 : row.name) ? row === null || row === void 0 ? void 0 : row.name : \"Not Entered\",\n                    country_code: row === null || row === void 0 ? void 0 : row.country_code,\n                    payment_type: row === null || row === void 0 ? void 0 : row.payment_type,\n                    payment_type_name: row === null || row === void 0 ? void 0 : row.payment_type_name,\n                    currency_name: row === null || row === void 0 ? void 0 : row.currency_name,\n                    currency_id: row === null || row === void 0 ? void 0 : row.iss_currency_id,\n                    global_gap_number: row === null || row === void 0 ? void 0 : row.global_gap_number,\n                    chile_certificate_number: row === null || row === void 0 ? void 0 : row.chile_certificate_number,\n                    red_tractor: row === null || row === void 0 ? void 0 : row.red_tractor,\n                    organic_certificate_number: row === null || row === void 0 ? void 0 : row.organic_certificate_number,\n                    puc_code: row === null || row === void 0 ? void 0 : row.puc_code,\n                    address_line_1: row === null || row === void 0 ? void 0 : row.address_line_1,\n                    address_line_2: row === null || row === void 0 ? void 0 : row.address_line_2,\n                    address_line_3: row === null || row === void 0 ? void 0 : row.address_line_3,\n                    address_line_4: row === null || row === void 0 ? void 0 : row.address_line_4,\n                    postal_code: row === null || row === void 0 ? void 0 : row.postal_code,\n                    id: row === null || row === void 0 ? void 0 : row.id,\n                    currency: (row === null || row === void 0 ? void 0 : row.currency) ? row === null || row === void 0 ? void 0 : row.currency : \"Not Entered\",\n                    currency_code: (row === null || row === void 0 ? void 0 : row.currency_code) ? row === null || row === void 0 ? void 0 : row.currency_code : \"\",\n                    Compliance: row === null || row === void 0 ? void 0 : row.compliance,\n                    Financials: row === null || row === void 0 ? void 0 : row.financial,\n                    General: row === null || row === void 0 ? void 0 : row.technical,\n                    Procurement: row === null || row === void 0 ? void 0 : row.procurement,\n                    requestor: row === null || row === void 0 ? void 0 : row.requestor_name,\n                    requestor_email: row === null || row === void 0 ? void 0 : row.requestor_email,\n                    companies: (row === null || row === void 0 ? void 0 : row.prophet_names) ? row === null || row === void 0 ? void 0 : row.prophet_names : \"Not Entered\",\n                    role: (row === null || row === void 0 ? void 0 : row.role_names) ? row === null || row === void 0 ? void 0 : row.role_names : \"Not Entered\",\n                    roleJson: (row === null || row === void 0 ? void 0 : row.role_json) ? JSON.parse(row === null || row === void 0 ? void 0 : row.role_json) : [],\n                    roleId: (row === null || row === void 0 ? void 0 : row.role_ids) ? JSON.parse(row === null || row === void 0 ? void 0 : row.role_ids) : [],\n                    roleIds: (row === null || row === void 0 ? void 0 : row.role_ids) ? JSON.parse(row === null || row === void 0 ? void 0 : row.role_ids).map((ele)=>{\n                        return ele.role_id;\n                    }) : [],\n                    supplier_type: row === null || row === void 0 ? void 0 : row.supplier_type_label,\n                    contacts_json: row === null || row === void 0 ? void 0 : row.contacts_json,\n                    distribution_points_json: (row === null || row === void 0 ? void 0 : row.distribution_points_json) ? JSON.parse(row === null || row === void 0 ? void 0 : row.distribution_points_json) : [],\n                    vat_number: row === null || row === void 0 ? void 0 : row.vat_number,\n                    payment_terms: row === null || row === void 0 ? void 0 : row.payment_terms,\n                    sort_bic: row === null || row === void 0 ? void 0 : row.decryptedSort_Bic,\n                    name_branch: row === null || row === void 0 ? void 0 : row.decryptedName_branch,\n                    account_number: row === null || row === void 0 ? void 0 : row.decryptedAccountNumber,\n                    vatable: row === null || row === void 0 ? void 0 : row.vatable,\n                    iss_ledger_code: row === null || row === void 0 ? void 0 : row.iss_ledger_code,\n                    internal_ledger_code: row === null || row === void 0 ? void 0 : row.internal_ledger_code,\n                    intermediatery_account_number: row === null || row === void 0 ? void 0 : row.decryptedIntermediatery_account_number,\n                    bacs_currency_code: row === null || row === void 0 ? void 0 : row.bacs_currency_code,\n                    has_iban: row === null || row === void 0 ? void 0 : row.has_iban,\n                    isProducerSupplier: row === null || row === void 0 ? void 0 : row.product_supplier,\n                    isEmergencyRequest: row === null || row === void 0 ? void 0 : row.emergency_request,\n                    supplier_group: row === null || row === void 0 ? void 0 : row.sendac_groups_json,\n                    supplierLinks: row === null || row === void 0 ? void 0 : row.supplier_links_json,\n                    status: row === null || row === void 0 ? void 0 : row.label,\n                    role_num: row === null || row === void 0 ? void 0 : row.role_nums,\n                    edi: row === null || row === void 0 ? void 0 : row.edi\n                };\n                if ((roleIds.includes(1) || (roleIds === null || roleIds === void 0 ? void 0 : roleIds.includes(6))) && (row === null || row === void 0 ? void 0 : row.emergency_request) && (row === null || row === void 0 ? void 0 : row.financial) !== \"Completed\") {\n                    formattedRow.isEmergencyAndFinanceNotComplete = true;\n                } else {\n                    formattedRow.isEmergencyAndFinanceNotComplete = false;\n                }\n                return formattedRow;\n            });\n            setAllRowData(formattedData);\n            const filteredData = formattedData.length > 0 ? formattedData === null || formattedData === void 0 ? void 0 : formattedData.filter((row)=>row.status !== \"Cancelled\" && row.status !== \"Exported\") : [];\n            setRowData(filteredData);\n            const fetchRolePermissions = async ()=>{\n                try {\n                    let serverAddress = _services_apiConfig__WEBPACK_IMPORTED_MODULE_12__.apiConfig.serverAddress;\n                    const response = await fetch(\"\".concat(serverAddress, \"suppliers/get-role-permissions\"), {\n                        method: \"GET\",\n                        headers: {\n                            Authorization: \"Bearer \".concat(token)\n                        }\n                    });\n                    if (!response.ok) {\n                        throw new Error(\"Request failed with status \".concat(response.status));\n                    }\n                    const result = await response.json();\n                    const rolePermissions = {};\n                    for (const row of result){\n                        var _row_sections_split, _row_sections;\n                        const sectionsArray = row === null || row === void 0 ? void 0 : (_row_sections = row.sections) === null || _row_sections === void 0 ? void 0 : (_row_sections_split = _row_sections.split(\",\")) === null || _row_sections_split === void 0 ? void 0 : _row_sections_split.filter((section)=>(section === null || section === void 0 ? void 0 : section.trim()) !== \"\"); // Split sections string into an array and remove empty values\n                        rolePermissions[row === null || row === void 0 ? void 0 : row.role_id] = sectionsArray;\n                    }\n                    updatePermissions(rolePermissions);\n                } catch (error) {\n                    console.error(error);\n                // setCommonError(error.message);\n                }\n            };\n            fetchRolePermissions();\n        }).catch((error)=>{\n            console.log(error);\n        });\n    }, []);\n    function getData() {\n        const company = js_cookie__WEBPACK_IMPORTED_MODULE_13__[\"default\"].get(\"company\");\n        let serverAddress = _services_apiConfig__WEBPACK_IMPORTED_MODULE_12__.apiConfig.serverAddress;\n        const ADCompanyName = js_cookie__WEBPACK_IMPORTED_MODULE_13__[\"default\"].get(\"ADCompanyName\");\n        let prophetId = 0;\n        if (ADCompanyName == \"FPP\") {\n            prophetId = 4;\n        } else if (ADCompanyName == \"EFC\") {\n            prophetId = 3;\n        } else if (ADCompanyName == \"DPS\") {\n            prophetId = 1;\n        } else if (ADCompanyName == \"DPS MS\") {\n            prophetId = 2;\n        }\n        console.log(\"prophetId\", prophetId);\n        return fetch(\"\".concat(serverAddress, \"suppliers/get-suppliers/\").concat(company, \"/\").concat(prophetId), {\n            method: \"GET\",\n            headers: {\n                Authorization: \"Bearer \".concat(token)\n            }\n        }).then(async (res)=>{\n            if (res.status == 502) {\n                setBlockScreen(true);\n                return;\n            }\n            setBlockScreen(false);\n            if (res.status === 400) {\n                react_toastify__WEBPACK_IMPORTED_MODULE_15__.toast.error(\"There was an error with your request. Please check your data and try again.\");\n                return null;\n            } else if (res.status === 401) {\n                react_toastify__WEBPACK_IMPORTED_MODULE_15__.toast.error(\"Your session has expired. Please log in again.\");\n                setTimeout(()=>{\n                    localStorage.removeItem(\"superUser\");\n                    js_cookie__WEBPACK_IMPORTED_MODULE_13__[\"default\"].remove(\"company\");\n                    js_cookie__WEBPACK_IMPORTED_MODULE_13__[\"default\"].remove(\"ADCompanyName\");\n                    localStorage.removeItem(\"id\");\n                    localStorage.removeItem(\"name\");\n                    localStorage.removeItem(\"role\");\n                    localStorage.removeItem(\"email\");\n                    js_cookie__WEBPACK_IMPORTED_MODULE_13__[\"default\"].remove(\"user\");\n                    js_cookie__WEBPACK_IMPORTED_MODULE_13__[\"default\"].remove(\"theme\");\n                    js_cookie__WEBPACK_IMPORTED_MODULE_13__[\"default\"].remove(\"token\");\n                    const redirectUrl = \"/login?redirect=\".concat(encodeURIComponent(window.location.pathname));\n                    (0,_utils_auth_auth__WEBPACK_IMPORTED_MODULE_14__.logoutHandler)(instance, redirectUrl);\n                }, 3000);\n                return null;\n            }\n            if (res.status === 200) {\n                return res.json();\n            }\n            throw new Error(\"Failed to fetch data\");\n        }).catch((error)=>{\n            setCommonError(error.message);\n        });\n    }\n    function deleteAll() {\n        setIsLoading(true);\n        let serverAddress = _services_apiConfig__WEBPACK_IMPORTED_MODULE_12__.apiConfig.serverAddress;\n        return fetch(\"\".concat(serverAddress, \"suppliers/delete-all\"), {\n            method: \"DELETE\",\n            headers: {\n                Authorization: \"Bearer \".concat(token)\n            }\n        }).then(async (res)=>{\n            if (res.status === 400) {\n                setIsLoading(false);\n                react_toastify__WEBPACK_IMPORTED_MODULE_15__.toast.error(\"There was an error with your request. Please check your data and try again.\");\n            } else if (res.status === 401) {\n                react_toastify__WEBPACK_IMPORTED_MODULE_15__.toast.error(\"Your session has expired. Please log in again.\");\n                setTimeout(()=>{\n                    setIsLoading(false);\n                    localStorage.removeItem(\"superUser\");\n                    js_cookie__WEBPACK_IMPORTED_MODULE_13__[\"default\"].remove(\"company\");\n                    js_cookie__WEBPACK_IMPORTED_MODULE_13__[\"default\"].remove(\"ADCompanyName\");\n                    localStorage.removeItem(\"id\");\n                    localStorage.removeItem(\"name\");\n                    localStorage.removeItem(\"role\");\n                    localStorage.removeItem(\"email\");\n                    js_cookie__WEBPACK_IMPORTED_MODULE_13__[\"default\"].remove(\"user\");\n                    js_cookie__WEBPACK_IMPORTED_MODULE_13__[\"default\"].remove(\"theme\");\n                    js_cookie__WEBPACK_IMPORTED_MODULE_13__[\"default\"].remove(\"token\");\n                    const redirectUrl = \"/login?redirect=\".concat(encodeURIComponent(window.location.pathname));\n                    (0,_utils_auth_auth__WEBPACK_IMPORTED_MODULE_14__.logoutHandler)(instance, redirectUrl);\n                }, 3000);\n            }\n            if (res.status === 200) {\n                react_toastify__WEBPACK_IMPORTED_MODULE_15__.toast.info(\"Delete successfull\");\n                setIsLoading(false);\n                router.reload();\n            }\n            throw new Error(\"Failed to delete\");\n        }).catch((error)=>{\n            setIsLoading(false);\n            console.error(error);\n        });\n    }\n    const handlePageSizeChange = (event)=>{\n        const newPageSize = parseInt(event.target.value, 15);\n        setPageSize(newPageSize);\n        gridRef.current.api.paginationSetPageSize(newPageSize);\n    };\n    const defaultColDef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useMemo)(()=>({\n            sortable: true,\n            filter: false,\n            resizable: true,\n            flex: 1\n        }));\n    const gridOptions = {\n        responsive: true\n    };\n    const CustomCellRenderer = (params)=>{\n        const truncatedText = params === null || params === void 0 ? void 0 : params.value;\n        // params?.value && params?.value?.length > 12\n        //   ? params?.value?.substring(0, 12) + \"...\"\n        //   : params?.value;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n            title: params === null || params === void 0 ? void 0 : params.value,\n            children: truncatedText\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\suppliers.js\",\n            lineNumber: 403,\n            columnNumber: 12\n        }, undefined);\n    };\n    const setStatusChange = ()=>{\n        setIsLoading(true);\n        setTimeout(function() {\n            getData(userData).then((data)=>{\n                if (data === null) {\n                    return;\n                }\n                const formattedData = data === null || data === void 0 ? void 0 : data.map((row)=>{\n                    const roleIds = (row === null || row === void 0 ? void 0 : row.role_ids) ? JSON.parse(row === null || row === void 0 ? void 0 : row.role_ids).map((ele)=>ele.role_id) : [];\n                    const roleJson = JSON.parse(row === null || row === void 0 ? void 0 : row.role_json);\n                    const formattedRow = {\n                        isActive: row === null || row === void 0 ? void 0 : row.is_active,\n                        prophets: (row === null || row === void 0 ? void 0 : row.prophet_ids) ? JSON.parse(row === null || row === void 0 ? void 0 : row.prophet_ids) : [],\n                        supplier_code: (row === null || row === void 0 ? void 0 : row.prophet_ids) ? JSON.parse(row === null || row === void 0 ? void 0 : row.prophet_ids)[0].prophet_code : [],\n                        company_name: (row === null || row === void 0 ? void 0 : row.name) ? row === null || row === void 0 ? void 0 : row.name : \"Not Entered\",\n                        id: row === null || row === void 0 ? void 0 : row.id,\n                        currency: (row === null || row === void 0 ? void 0 : row.currency) ? row === null || row === void 0 ? void 0 : row.currency : \"Not Entered\",\n                        Compliance: row === null || row === void 0 ? void 0 : row.compliance,\n                        Financials: row === null || row === void 0 ? void 0 : row.financial,\n                        General: row === null || row === void 0 ? void 0 : row.technical,\n                        Procurement: row === null || row === void 0 ? void 0 : row.procurement,\n                        requestor: row === null || row === void 0 ? void 0 : row.requestor_name,\n                        companies: (row === null || row === void 0 ? void 0 : row.prophet_names) ? row === null || row === void 0 ? void 0 : row.prophet_names : \"Not Entered\",\n                        role: (row === null || row === void 0 ? void 0 : row.role_names) ? row === null || row === void 0 ? void 0 : row.role_names : \"Not Entered\",\n                        roleId: (row === null || row === void 0 ? void 0 : row.role_ids) ? JSON.parse(row === null || row === void 0 ? void 0 : row.role_ids) : [],\n                        roleIds: (row === null || row === void 0 ? void 0 : row.role_ids) ? JSON.parse(row === null || row === void 0 ? void 0 : row.role_ids).map((ele)=>{\n                            return ele.role_id;\n                        }) : [],\n                        country_code: row === null || row === void 0 ? void 0 : row.country_code,\n                        payment_type: row === null || row === void 0 ? void 0 : row.payment_type,\n                        payment_type_name: row === null || row === void 0 ? void 0 : row.payment_type_name,\n                        currency_name: row === null || row === void 0 ? void 0 : row.currency_name,\n                        currency_id: row === null || row === void 0 ? void 0 : row.iss_currency_id,\n                        global_gap_number: row === null || row === void 0 ? void 0 : row.global_gap_number,\n                        chile_certificate_number: row === null || row === void 0 ? void 0 : row.chile_certificate_number,\n                        red_tractor: row === null || row === void 0 ? void 0 : row.red_tractor,\n                        organic_certificate_number: row === null || row === void 0 ? void 0 : row.organic_certificate_number,\n                        puc_code: row === null || row === void 0 ? void 0 : row.puc_code,\n                        address_line_1: row === null || row === void 0 ? void 0 : row.address_line_1,\n                        address_line_2: row === null || row === void 0 ? void 0 : row.address_line_2,\n                        address_line_3: row === null || row === void 0 ? void 0 : row.address_line_3,\n                        address_line_4: row === null || row === void 0 ? void 0 : row.address_line_4,\n                        postal_code: row === null || row === void 0 ? void 0 : row.postal_code,\n                        currency_code: (row === null || row === void 0 ? void 0 : row.currency_code) ? row === null || row === void 0 ? void 0 : row.currency_code : \"\",\n                        requestor_email: row === null || row === void 0 ? void 0 : row.requestor_email,\n                        companies: (row === null || row === void 0 ? void 0 : row.prophet_names) ? row === null || row === void 0 ? void 0 : row.prophet_names : \"Not Entered\",\n                        vatable: row === null || row === void 0 ? void 0 : row.vatable,\n                        roleJson: (row === null || row === void 0 ? void 0 : row.role_json) ? JSON.parse(row === null || row === void 0 ? void 0 : row.role_json) : [],\n                        supplier_type: row === null || row === void 0 ? void 0 : row.supplier_type_label,\n                        isProducerSupplier: row === null || row === void 0 ? void 0 : row.product_supplier,\n                        isEmergencyRequest: row === null || row === void 0 ? void 0 : row.emergency_request,\n                        supplier_group: row === null || row === void 0 ? void 0 : row.sendac_groups_json,\n                        supplierLinks: row === null || row === void 0 ? void 0 : row.supplier_links_json,\n                        status: row === null || row === void 0 ? void 0 : row.label,\n                        role_num: row === null || row === void 0 ? void 0 : row.role_nums,\n                        contacts_json: row === null || row === void 0 ? void 0 : row.contacts_json,\n                        distribution_points_json: (row === null || row === void 0 ? void 0 : row.distribution_points_json) ? JSON.parse(row === null || row === void 0 ? void 0 : row.distribution_points_json) : [],\n                        vat_number: row === null || row === void 0 ? void 0 : row.vat_number,\n                        payment_terms: row === null || row === void 0 ? void 0 : row.payment_terms,\n                        sort_bic: row === null || row === void 0 ? void 0 : row.decryptedSort_Bic,\n                        name_branch: row === null || row === void 0 ? void 0 : row.decryptedName_branch,\n                        account_number: row === null || row === void 0 ? void 0 : row.decryptedAccountNumber,\n                        iss_ledger_code: row === null || row === void 0 ? void 0 : row.iss_ledger_code,\n                        internal_ledger_code: row === null || row === void 0 ? void 0 : row.internal_ledger_code,\n                        intermediatery_account_number: row === null || row === void 0 ? void 0 : row.decryptedIntermediatery_account_number,\n                        bacs_currency_code: row === null || row === void 0 ? void 0 : row.bacs_currency_code,\n                        has_iban: row === null || row === void 0 ? void 0 : row.has_iban,\n                        edi: row === null || row === void 0 ? void 0 : row.edi\n                    };\n                    if (((roleIds === null || roleIds === void 0 ? void 0 : roleIds.includes(1)) || (roleIds === null || roleIds === void 0 ? void 0 : roleIds.includes(6))) && (row === null || row === void 0 ? void 0 : row.emergency_request) && (row === null || row === void 0 ? void 0 : row.financial) !== \"Completed\") {\n                        formattedRow.isEmergencyAndFinanceNotComplete = true;\n                    } else {\n                        formattedRow.isEmergencyAndFinanceNotComplete = false;\n                    }\n                    return formattedRow;\n                });\n                setAllRowData(formattedData);\n                const filteredData = formattedData.length > 0 ? formattedData === null || formattedData === void 0 ? void 0 : formattedData.filter((row)=>row.status !== \"Completed\" && row.status !== \"Cancelled\" && row.status !== \"Exported\") : [];\n                setRowData(filteredData);\n                setIsLoading(false);\n            }).catch((error)=>{\n                console.log(error);\n                setIsLoading(false);\n            });\n        }, 3000);\n    // console.log('updated id', id)\n    // console.log('updated status', status)\n    // console.log(rowData)\n    // const find_supplier = rowData.filter((item) => item.id === id)\n    // const filterWithId = rowData.filter((item) => item.id !== id)\n    // console.log(find_supplier)\n    // //const rowNode = gridRef.current.api.getRowNode(id.toString());\n    // //console.log(rowNode);\n    // //rowNode.setRowData(...rowData, find_supplier);\n    // setRowData([...filterWithId, ...find_supplier]);\n    };\n    const CustomTooltipComponent = (param)=>/*#__PURE__*/ {\n        let { value } = param;\n        return (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            title: value,\n            children: value\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\suppliers.js\",\n            lineNumber: 538,\n            columnNumber: 5\n        }, undefined);\n    };\n    const columnDefs = [\n        {\n            headerName: \"Supplier Name\",\n            field: \"company_name\",\n            // checkboxSelection: true,\n            checkboxSelection: (params)=>{\n                return params.data.status === \"Cancelled\" ? {\n                    checked: false,\n                    disabled: true\n                } : true;\n            },\n            cellRenderer: _utils_renderer_nameRenderer__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n            headerCheckboxSelection: true,\n            //suppressMenu: true,\n            //suppressSizeToFit: true,\n            //suppressSizeToFit: false,\n            flex: \"8%\",\n            filter: true,\n            cellRendererParams: {\n                setSuppliers: setRowData,\n                setIsFiltered: setIsFiltered,\n                setIsFilteredName: setIsFilteredName\n            }\n        },\n        {\n            headerName: \"Supplier Code\",\n            cellRenderer: _components_supplierCodeRenderer__WEBPACK_IMPORTED_MODULE_23__[\"default\"],\n            field: \"supplier_code\",\n            flex: \"3%\"\n        },\n        {\n            headerName: \"Roles\",\n            field: \"role\",\n            cellRenderer: CustomCellRenderer,\n            tooltipComponent: CustomTooltipComponent,\n            flex: \"3%\"\n        },\n        {\n            headerName: \"Companies\",\n            field: \"companies\",\n            cellRenderer: CustomCellRenderer,\n            tooltipComponent: CustomTooltipComponent,\n            flex: \"4%\"\n        },\n        {\n            headerName: \"Currency\",\n            field: \"currency\",\n            flex: \"3%\",\n            cellRenderer: CustomCellRenderer,\n            tooltipComponent: CustomTooltipComponent,\n            cellStyle: (params)=>{\n                if (params.value == \"Not Entered\") {\n                    return {\n                        color: \"#B31312\"\n                    };\n                }\n                return null;\n            }\n        },\n        {\n            headerName: \"Requestor\",\n            cellRenderer: CustomCellRenderer,\n            tooltipComponent: CustomTooltipComponent,\n            field: \"requestor\",\n            flex: \"4%\"\n        },\n        {\n            headerName: \"General\",\n            cellRenderer: _utils_renderer_statusRenderer__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n            field: \"General\",\n            flex: \"3%\"\n        },\n        {\n            headerName: \"Financial\",\n            cellRenderer: _utils_renderer_statusRenderer__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n            field: \"Financials\",\n            flex: \"3%\"\n        },\n        {\n            headerName: \"Compliance\",\n            field: \"Compliance\",\n            cellRenderer: _utils_renderer_statusRenderer__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n            flex: \"4%\"\n        },\n        {\n            headerName: \"Procurement\",\n            field: \"Procurement\",\n            cellRenderer: _utils_renderer_statusRenderer__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n            flex: \"4%\"\n        },\n        {\n            headerName: \"Status\",\n            field: \"status\",\n            cellRenderer: _utils_renderer_statusRenderer__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n            cellStyle: ()=>({\n                    justifyContent: \"center\"\n                }),\n            flex: \"4%\",\n            hide: false\n        },\n        {\n            field: \"\",\n            cellRenderer: (params)=>(0,_utils_renderer_actionRenderer__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(params, userData, token, company),\n            flex: \"2%\",\n            cellStyle: ()=>({}),\n            sortable: false,\n            cellRendererParams: {\n                setUpdateStatusChange: setStatusChange\n            }\n        },\n        {\n            field: \"role_num\",\n            hide: true\n        }\n    ];\n    const onFilterTextBoxChanged = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(()=>{\n        gridRef.current.api.setQuickFilter(document.getElementById(\"filter-text-box\").value);\n    }, []);\n    const handleFilterToggle = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)((e)=>{\n        const { value } = e.target;\n        setSelectedStatus(value);\n        let filteredData = [];\n        if (value == \"default\") {\n            filteredData = allRowData.filter((row)=>row.status !== \"Cancelled\" && row.status !== \"Exported\");\n        } else {\n            filteredData = allRowData.filter((row)=>row.status === value);\n        }\n        setRowData(filteredData);\n        setExportDisabled(filteredData.length === 0);\n    }, [\n        allRowData\n    ]);\n    const exportFilteredData = async ()=>{\n        let export_ISSresponse = false;\n        let exportInternal_response = false;\n        // setIsOpenOption(false);\n        const gridApi = gridRef.current.api;\n        let filteredData = [];\n        const isInternal = selectedExportType === \"internalExport\";\n        if (isInternal) {\n            gridApi.forEachNodeAfterFilter((node)=>{\n                var _node_data, _node_data1, _node_data2, _node_data3;\n                if (node.data !== undefined && ((_node_data = node.data) === null || _node_data === void 0 ? void 0 : _node_data.status) === \"Completed\" || ((_node_data1 = node.data) === null || _node_data1 === void 0 ? void 0 : _node_data1.status) === \"Exported\" || ((_node_data2 = node.data) === null || _node_data2 === void 0 ? void 0 : _node_data2.isEmergencyRequest) && ((_node_data3 = node.data) === null || _node_data3 === void 0 ? void 0 : _node_data3.General) === \"Complete\") {\n                    filteredData = [\n                        [\n                            {\n                                \"Supplier Active\": \"test\",\n                                \"Haulage cube local\": \"test\",\n                                \"Haulage cube name\": \"test\",\n                                \"update guesstimates type\": \"test\",\n                                \"Organization ID\": \"\",\n                                \"Enforce department\": \"\",\n                                \"Sendac Group\": \"\",\n                                \"Supplier name\": \"\",\n                                \"Supplier type\": \"\",\n                                \"User Lookup 2\": \"\",\n                                \"Address Line 1\": \"\",\n                                \"Address Line 2\": \"\",\n                                \"Address Line 3\": \"\",\n                                \"Address Line 4\": \"\",\n                                \"Post code\": \"\",\n                                \"Country code\": \"\",\n                                \"Payee supplier code\": \"\",\n                                \"Invoice supplier\": \"\",\n                                \"Head office\": \"\",\n                                \"Settlement days\": \"\",\n                                \"Bank General Ledger Code\": \"\",\n                                \"Currency number\": \"\",\n                                \"Currency number / name\": \"\",\n                                \"Bank general ledger code\": \"\",\n                                \"Payment type\": \"\",\n                                \"Country code\": \"\",\n                                Vatable: \"\",\n                                vatable: \"\",\n                                \"Update guesstimates type\": \"\",\n                                \"Area Number\": \"\",\n                                Buyer: \"\",\n                                \"Multiple lot indicator\": \"\",\n                                \"multiple lot indicator\": \"\",\n                                \"Generate Pallet Loading Plan\": \"\",\n                                \"Distribution point for supplier\": \"\",\n                                \"Payment terms\": \"\",\n                                \"Department Number\": \"\"\n                            }\n                        ],\n                        [\n                            {\n                                \"Supplier code\": \"test\",\n                                Sendacroleid: \"test\",\n                                Description: \"test\",\n                                \"Supplier Code Name\": \"\",\n                                Type: \"\",\n                                \"Type Description\": \"\",\n                                GGN: \"\"\n                            }\n                        ],\n                        [\n                            {\n                                \"Contact ID\": \"test\",\n                                \"Supplier Name\": \"test\",\n                                \"Email Address\": \"\",\n                                Telephone: \"\",\n                                Cell: \"\"\n                            }\n                        ],\n                        [\n                            {\n                                \"Organization ID\": \"test\",\n                                \"Organization Name\": \"\"\n                            }\n                        ],\n                        [\n                            {\n                                \"Organization ID\": \"test\",\n                                \"Organization Code\": \"\",\n                                \"Role Type ID\": \"\",\n                                Selected: \"\",\n                                \"Organisation ID\": \"\",\n                                \"Role Type ID\": \"\",\n                                \"Contact ID\": \"\",\n                                \"Contact ID Email Address\": \"\",\n                                \"Contact ID Telephone\": \"\"\n                            }\n                        ]\n                    ];\n                }\n            });\n        } else {\n            gridApi.forEachNodeAfterFilter((node)=>{\n                var _node_data, _node_data1, _node_data2, _node_data3;\n                let rolesArray = node.data.roleId.map((ele)=>{\n                    return ele.role_id;\n                });\n                if (node.data !== undefined && ((_node_data = node.data) === null || _node_data === void 0 ? void 0 : _node_data.status) === \"Completed\" || ((_node_data1 = node.data) === null || _node_data1 === void 0 ? void 0 : _node_data1.status) === \"Exported\" || ((_node_data2 = node.data) === null || _node_data2 === void 0 ? void 0 : _node_data2.isEmergencyRequest) && ((_node_data3 = node.data) === null || _node_data3 === void 0 ? void 0 : _node_data3.General) === \"Complete\") {\n                    var _node_data4, _node_data_prophets__prophet_code, _node_data_prophets_, _node_data5, _node_data_company_name, _node_data6, _node_data7, _node_data8, _node_data9, _node_data10, _node_data11, _node_data12, _node_data13, _node_data_prophets__prophet_code1, _node_data_prophets_1, _node_data14, _node_data15, _node_data16, _node_data17, _node_data18, _node_data19, _node_data20, _node_data21;\n                    const filteredExportData = {\n                        \"Supplier Active\": (node === null || node === void 0 ? void 0 : (_node_data4 = node.data) === null || _node_data4 === void 0 ? void 0 : _node_data4.isActive) ? 1 : 0,\n                        \"Supplier code\": node === null || node === void 0 ? void 0 : (_node_data5 = node.data) === null || _node_data5 === void 0 ? void 0 : (_node_data_prophets_ = _node_data5.prophets[0]) === null || _node_data_prophets_ === void 0 ? void 0 : (_node_data_prophets__prophet_code = _node_data_prophets_.prophet_code) === null || _node_data_prophets__prophet_code === void 0 ? void 0 : _node_data_prophets__prophet_code.trim(),\n                        \"EDI Partner\": \"\",\n                        \"Supplier name\": node === null || node === void 0 ? void 0 : (_node_data6 = node.data) === null || _node_data6 === void 0 ? void 0 : (_node_data_company_name = _node_data6.company_name) === null || _node_data_company_name === void 0 ? void 0 : _node_data_company_name.trim(),\n                        \"Country Code\": node === null || node === void 0 ? void 0 : (_node_data7 = node.data) === null || _node_data7 === void 0 ? void 0 : _node_data7.country_code,\n                        \"Distribution Point for Supplier\": 6,\n                        \"Bank Ledger Code\": node === null || node === void 0 ? void 0 : (_node_data8 = node.data) === null || _node_data8 === void 0 ? void 0 : _node_data8.currency_id,\n                        \"Area Number\": 170,\n                        Vatable: 0,\n                        Buyer: 1,\n                        \"Billing type\": 0,\n                        \"Payment type\": node === null || node === void 0 ? void 0 : (_node_data9 = node.data) === null || _node_data9 === void 0 ? void 0 : _node_data9.payment_type,\n                        \"Currency number\": node === null || node === void 0 ? void 0 : (_node_data10 = node.data) === null || _node_data10 === void 0 ? void 0 : _node_data10.currency_id,\n                        GGN: node === null || node === void 0 ? void 0 : (_node_data11 = node.data) === null || _node_data11 === void 0 ? void 0 : _node_data11.global_gap_number,\n                        \"Organic cert\": node === null || node === void 0 ? void 0 : (_node_data12 = node.data) === null || _node_data12 === void 0 ? void 0 : _node_data12.organic_certificate_number,\n                        \"Regional cert\": node === null || node === void 0 ? void 0 : (_node_data13 = node.data) === null || _node_data13 === void 0 ? void 0 : _node_data13.chile_certificate_number,\n                        \"Head office\": node === null || node === void 0 ? void 0 : (_node_data14 = node.data) === null || _node_data14 === void 0 ? void 0 : (_node_data_prophets_1 = _node_data14.prophets[0]) === null || _node_data_prophets_1 === void 0 ? void 0 : (_node_data_prophets__prophet_code1 = _node_data_prophets_1.prophet_code) === null || _node_data_prophets__prophet_code1 === void 0 ? void 0 : _node_data_prophets__prophet_code1.trim(),\n                        \"Address line 1\": node === null || node === void 0 ? void 0 : (_node_data15 = node.data) === null || _node_data15 === void 0 ? void 0 : _node_data15.address_line_1,\n                        \"Address line 2\": node === null || node === void 0 ? void 0 : (_node_data16 = node.data) === null || _node_data16 === void 0 ? void 0 : _node_data16.address_line_2,\n                        \"Address line 3\": node === null || node === void 0 ? void 0 : (_node_data17 = node.data) === null || _node_data17 === void 0 ? void 0 : _node_data17.address_line_3,\n                        \"Address line 4\": node === null || node === void 0 ? void 0 : (_node_data18 = node.data) === null || _node_data18 === void 0 ? void 0 : _node_data18.address_line_4,\n                        \"Postal code\": node === null || node === void 0 ? void 0 : (_node_data19 = node.data) === null || _node_data19 === void 0 ? void 0 : _node_data19.postal_code,\n                        status: node === null || node === void 0 ? void 0 : (_node_data20 = node.data) === null || _node_data20 === void 0 ? void 0 : _node_data20.status,\n                        id: node === null || node === void 0 ? void 0 : (_node_data21 = node.data) === null || _node_data21 === void 0 ? void 0 : _node_data21.id\n                    };\n                    filteredData.push(filteredExportData);\n                }\n            });\n        }\n        if (filteredData.length === 0) {\n            react_toastify__WEBPACK_IMPORTED_MODULE_15__.toast.error(\"No filtered data to export.\", {\n                position: \"top-right\",\n                autoClose: 5000,\n                hideProgressBar: false,\n                closeOnClick: true,\n                pauseOnHover: false,\n                draggable: true,\n                progress: undefined,\n                theme: \"light\"\n            });\n        } else {\n            if (supplierCheckedValue) {\n                if (true) {\n                    const allStatesData = [\n                        ulpFilData,\n                        supplierActiveData,\n                        roleData,\n                        sendacGroupData,\n                        bankAc,\n                        senBnk,\n                        contactData,\n                        organizationData,\n                        organizationRoleData,\n                        sheetSupplierId\n                    ];\n                    exportInternal_response = await (0,_utils_exportExcel__WEBPACK_IMPORTED_MODULE_22__[\"default\"])(allStatesData, true, token, company, userData, prophetId, userData === null || userData === void 0 ? void 0 : userData.email, \"\");\n                }\n                if (!isUnExportable) {\n                    const allStatesData = [\n                        multipleFilterISSData,\n                        roleData\n                    ];\n                    export_ISSresponse = await (0,_utils_exportExcel__WEBPACK_IMPORTED_MODULE_22__[\"default\"])(allStatesData, false, token, company, userData, prophetId, userData === null || userData === void 0 ? void 0 : userData.email, \"\");\n                }\n                setEmailStatusPopup(true);\n                if (export_ISSresponse && exportInternal_response) {\n                    setPopUpMessage(\"Email sent to Finance Department and ISS admin\");\n                    setISSExportSuccess(true);\n                    setInternalExportSuccess(true);\n                } else if (exportInternal_response && isUnExportable) {\n                    setPopUpMessage(\"Email sent to Finance Department, but suppliers \".concat(unExportableSuppliernames, \" not exported as Hauliers and Expense roles not allowed to be exported to ISS\"));\n                    setInternalExportSuccess(true);\n                } else if (export_ISSresponse) {\n                    setISSExportSuccess(true);\n                    setPopUpMessage(\"Email sent to ISS Admin Team, but not to Finance Department\");\n                } else {\n                    setPopUpMessage(\"Email not sent to either Finance Department or ISS Admin Team\");\n                }\n            }\n        }\n        setSelectedExportType(\"\");\n        gridRef.current.api.deselectAll();\n    };\n    const [supplierActiveData, setSupplierActiveData] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([\n        [\n            \"sendac (Supplier file)\"\n        ]\n    ]);\n    const [roleData, setRoleData] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([\n        [\n            \"sendacrole (Supplier role file)\"\n        ]\n    ]);\n    const [contactData, setContactData] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([\n        [\n            \"contactdet (Supplier personnel contact details)\"\n        ]\n    ]);\n    const [organizationData, setOrganizationData] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([\n        [\n            \"organization (Organization)\"\n        ]\n    ]);\n    const [organizationRoleData, setOrganizationRoleData] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([\n        [\n            \"orgroles (Organization Roles)\"\n        ]\n    ]);\n    const [sendacGroupData, setSendacGroupData] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([\n        [\n            \"sendacgroup (Sendac group file)\"\n        ]\n    ]);\n    const [bankAc, setBankAc] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([\n        [\n            \"bankac (Bank account details table)\"\n        ]\n    ]);\n    const [multipleFilterISSData, setMultipleFilterISSData] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([\n        [\n            \"Supplier Data\"\n        ]\n    ]);\n    const [senBnk, setSenBnk] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([\n        [\n            \"senbnk (Supplier bank link table)\"\n        ]\n    ]);\n    const [ulpFilData, setUlpFilData] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([\n        [\n            \"UlpFil\"\n        ]\n    ]);\n    const [sheetSupplierId, setSheetSupplierId] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([\n        [\n            \"Supplier Id\"\n        ]\n    ]);\n    const handleGridReady = (params)=>{\n        params.api.setColumnDefs(columnDefs);\n    };\n    const extractContacts = (supplierCode, contactsJsonStr, supplierName)=>{\n        try {\n            const contacts = contactsJsonStr ? JSON.parse(contactsJsonStr) : [];\n            if (Array.isArray(contacts)) {\n                return contacts.map((contact)=>({\n                        \"Supplier code\": supplierCode ? supplierCode : \"\",\n                        \"Contact ID\": \"\",\n                        Name: supplierName || \"\",\n                        \"Email Address\": contact.email_id || \"\",\n                        \"Telephone number\": contact.telephone || \"\",\n                        \"Cell phone number\": \"\",\n                        \"Fax number\": \"\",\n                        \"Instant Message\": \"\",\n                        \"Physical Address\": \"\",\n                        \"Postal Address\": \"\",\n                        \"Row verision\": \"\",\n                        \"Created timestamp\": \"\"\n                    }));\n            }\n        } catch (error) {\n            console.error(\"Error parsing contacts_json:\", error);\n        }\n        return [\n            {\n                \"Contact ID\": \"\",\n                Name: supplierName || \"\",\n                \"Email Address\": \"\",\n                \"Telephone number\": \"\",\n                \"Cell phone number\": \"\",\n                \"Fax number\": \"\",\n                \"Instant Message\": \"\",\n                \"Physical Address\": \"\",\n                \"Postal Address\": \"\",\n                \"Row verision\": \"\",\n                \"Created timestamp\": \"\"\n            }\n        ];\n    };\n    const extractSendacGroup = (sendacGroupJson)=>{\n        try {\n            const sendacGroups = sendacGroupJson ? JSON.parse(sendacGroupJson) : [];\n            if (Array.isArray(sendacGroups)) {\n                return sendacGroups === null || sendacGroups === void 0 ? void 0 : sendacGroups.map((group)=>({\n                        \"Supplier group\": \"\",\n                        Description: (group === null || group === void 0 ? void 0 : group.created_by) ? group === null || group === void 0 ? void 0 : group.label : \"\"\n                    }));\n            }\n        } catch (error) {\n            console.error(\"Error parsing contacts_json:\", error);\n        }\n    };\n    const multipleSendRoleOnRoleNums = (data, role_num)=>{\n        var _data_role;\n        const roleNums = role_num === null || role_num === void 0 ? void 0 : role_num.split(\",\").map((num)=>num.trim());\n        const roleNames = data === null || data === void 0 ? void 0 : (_data_role = data.role) === null || _data_role === void 0 ? void 0 : _data_role.split(\",\").map((name)=>name.trim());\n        return roleNums.map((num, index)=>{\n            var _data_prophets_, _data_prophets_1;\n            return {\n                Sendacroleid: \"\",\n                \"Supplier code\": (data === null || data === void 0 ? void 0 : (_data_prophets_ = data.prophets[0]) === null || _data_prophets_ === void 0 ? void 0 : _data_prophets_.prophet_code) ? data.prophets[0].prophet_code.trim() : \"\",\n                Description: (data === null || data === void 0 ? void 0 : (_data_prophets_1 = data.prophets[0]) === null || _data_prophets_1 === void 0 ? void 0 : _data_prophets_1.prophet_code) ? data.prophets[0].prophet_code.trim() : \"\",\n                \"Supplier Code Supplier Name\": data.company_name,\n                Type: num,\n                \"Type Description\": roleNames[index],\n                \"Supplier code Global gap number\": data === null || data === void 0 ? void 0 : data.global_gap_number,\n                \"Created timestamp\": \"\",\n                Active: 1\n            };\n        });\n    };\n    function getGLCode(internal_ledger_code) {\n        if (internal_ledger_code) {\n            return internal_ledger_code;\n        } else return \"\";\n    }\n    const [incompleteToast, setIncompleteToast] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        if (incompleteToast) {\n            react_toastify__WEBPACK_IMPORTED_MODULE_15__.toast.error(\"Supplier details are incomplete.\", {\n                position: \"top-right\",\n                autoClose: 1000,\n                hideProgressBar: false,\n                closeOnClick: true,\n                pauseOnHover: false,\n                draggable: true,\n                progress: undefined,\n                theme: \"light\"\n            });\n        }\n    }, [\n        incompleteToast\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        if (containsCancelledSupplier) {\n            react_toastify__WEBPACK_IMPORTED_MODULE_15__.toast.error(\"Cannot export cancelled supplier.\", {\n                position: \"top-right\",\n                autoClose: 1000,\n                hideProgressBar: false,\n                closeOnClick: true,\n                pauseOnHover: false,\n                draggable: true,\n                progress: undefined,\n                theme: \"light\"\n            });\n        }\n    }, [\n        containsCancelledSupplier\n    ]);\n    const handleCheckboxEvent = (event)=>{\n        const getRowData = event.data;\n        const isSelected = event.node.selected;\n        const selectedRows = gridRef.current.api.getSelectedRows();\n        const prophet_id = getRowData.prophets[0].prophet_id;\n        setProphetId(prophet_id);\n        const extractedValues = selectedRows.map((param)=>{\n            let { status, isEmergencyRequest, General } = param;\n            return {\n                status,\n                isEmergencyRequest,\n                General\n            };\n        });\n        const exportDisabled = extractedValues.some((param)=>{\n            let { status, isEmergencyRequest, General } = param;\n            return !(status === \"Completed\" || status === \"Exported\" || isEmergencyRequest && General === \"Complete\");\n        });\n        const canExport = extractedValues.every((param)=>{\n            let { isEmergencyRequest } = param;\n            return !(!isEmergencyRequest && ((userData === null || userData === void 0 ? void 0 : userData.role_id) != 1 || (userData === null || userData === void 0 ? void 0 : userData.role_id) != 2 || (userData === null || userData === void 0 ? void 0 : userData.role_id) != 5 || (userData === null || userData === void 0 ? void 0 : userData.role_id) != 6));\n        });\n        const isExportableBasedOnCodeUnique = selectedRows.every((row)=>{\n            var _row_prophets_, _row_prophets_1;\n            const codeCount = row === null || row === void 0 ? void 0 : (_row_prophets_ = row.prophets[0]) === null || _row_prophets_ === void 0 ? void 0 : _row_prophets_.code_count;\n            const prophetCode = row === null || row === void 0 ? void 0 : (_row_prophets_1 = row.prophets[0]) === null || _row_prophets_1 === void 0 ? void 0 : _row_prophets_1.prophet_code;\n            if (codeCount && codeCount > 1 && prophetCode && prophetCode !== \"\") {\n                return false;\n            } else if (codeCount && codeCount == 1 && prophetCode && prophetCode !== \"\") {\n                return true;\n            }\n            return false;\n        });\n        const doesContainCancelledSupplier = selectedRows.some((row)=>row.status === \"Cancelled\");\n        const isExportValid = selectedRows.every((row)=>{\n            var _row_prophets_, _row_prophets_1, _row_roleIds, _row_roleIds1;\n            const supCode = row === null || row === void 0 ? void 0 : (_row_prophets_ = row.prophets[0]) === null || _row_prophets_ === void 0 ? void 0 : _row_prophets_.prophet_code;\n            const prophet_id = row === null || row === void 0 ? void 0 : (_row_prophets_1 = row.prophets[0]) === null || _row_prophets_1 === void 0 ? void 0 : _row_prophets_1.prophet_id;\n            const isSupplierAccount = (row === null || row === void 0 ? void 0 : (_row_roleIds = row.roleIds) === null || _row_roleIds === void 0 ? void 0 : _row_roleIds.includes(1)) || (row === null || row === void 0 ? void 0 : (_row_roleIds1 = row.roleIds) === null || _row_roleIds1 === void 0 ? void 0 : _row_roleIds1.includes(6));\n            let currency = (row === null || row === void 0 ? void 0 : row.currency) == \"$\" ? \"\\\\\".concat(row === null || row === void 0 ? void 0 : row.currency) : row === null || row === void 0 ? void 0 : row.currency;\n            let actualCurr;\n            if (currency && currency == \"Not Entered\") {\n                actualCurr = \"\";\n            } else {\n                actualCurr = currency;\n            }\n            let isValid = true;\n            if (isSupplierAccount) {\n                if (prophet_id == 1) {\n                    let regexPattern;\n                    regexPattern = new RegExp(\"^[A-Z0-9]{4}[A-Z0145678]\".concat(actualCurr, \"$\"));\n                    // let regexPattern = new RegExp(`^([A-Z0]{5})${actualCurr}$`);\n                    isValid = regexPattern.test(supCode);\n                } else if (prophet_id == 2) {\n                    let regexPattern;\n                    regexPattern = new RegExp(\"^([A-Z0]{4})9\".concat(actualCurr, \"$\"));\n                    // let regexPattern = new RegExp(`^([A-Z0]{4})9${actualCurr}$`);\n                    isValid = regexPattern.test(supCode);\n                } else if (prophet_id == 3) {\n                    let regexPattern = new RegExp(\"^[A-Z0-9]{4}[A-Z01345678][A-Z0-9]*$\");\n                    isValid = regexPattern.test(supCode) && supCode.length == 6;\n                } else if (prophet_id == 4) {\n                    let regexPattern;\n                    regexPattern = new RegExp(\"^([A-Z0]{4})2\".concat(actualCurr, \"$\"));\n                    isValid = regexPattern.test(supCode);\n                }\n            }\n            return isValid;\n        });\n        if (selectedRows.length > 0) {\n            if (!canExport && (userData === null || userData === void 0 ? void 0 : userData.role_id) != 1 && (userData === null || userData === void 0 ? void 0 : userData.role_id) != 2 && (userData === null || userData === void 0 ? void 0 : userData.role_id) != 5 && (userData === null || userData === void 0 ? void 0 : userData.role_id) != 6) {\n                setExportDisabled(true);\n            } else if (doesContainCancelledSupplier) {\n                setContainsCancelledSupplier(true);\n                setExportDisabled(true);\n            } else if (!isExportableBasedOnCodeUnique) {\n                setSupplierUniqueCodeToast(true);\n                setExportDisabled(true);\n            } else if (!isExportValid) {\n                setSupplierCodeValid(false);\n                setExportDisabled(true);\n            } else {\n                setExportDisabled(exportDisabled);\n            }\n        } else {\n            setExportDisabled(true);\n        }\n        let isUnExportableToISS = false;\n        let supplierNames = [];\n        selectedRows.forEach((row)=>{\n            var _row_roleIds, _row_roleIds1, _row_roleIds2, _row_roleIds3;\n            if (!(row === null || row === void 0 ? void 0 : (_row_roleIds = row.roleIds) === null || _row_roleIds === void 0 ? void 0 : _row_roleIds.includes(1)) && !(row === null || row === void 0 ? void 0 : (_row_roleIds1 = row.roleIds) === null || _row_roleIds1 === void 0 ? void 0 : _row_roleIds1.includes(2)) && !(row === null || row === void 0 ? void 0 : (_row_roleIds2 = row.roleIds) === null || _row_roleIds2 === void 0 ? void 0 : _row_roleIds2.includes(3)) && !(row === null || row === void 0 ? void 0 : (_row_roleIds3 = row.roleIds) === null || _row_roleIds3 === void 0 ? void 0 : _row_roleIds3.includes(4))) {\n                isUnExportableToISS = true;\n                supplierNames.push(row.company_name);\n            }\n        });\n        const supplierNamesString = supplierNames.join(\", \");\n        setIsUnExportable(isUnExportableToISS);\n        setUnExportableSupplierNames(supplierNamesString);\n        if ((getRowData === null || getRowData === void 0 ? void 0 : getRowData.status) == \"Completed\" || (getRowData === null || getRowData === void 0 ? void 0 : getRowData.status) == \"Exported\" || (getRowData === null || getRowData === void 0 ? void 0 : getRowData.isEmergencyRequest) && getRowData.status != \"Cancelled\" && (getRowData === null || getRowData === void 0 ? void 0 : getRowData.General) === \"Complete\") {\n            var _getRowData_roleIds, _getRowData_roleIds1, _getRowData_roleIds2, _getRowData_roleIds3, _getRowData_prophets__prophet_code, _getRowData_prophets_, _getRowData_company_name, _getRowData_prophets__prophet_code1, _getRowData_prophets_1, _getRowData_distribution_points_json, _getRowData_prophets_2, _getRowData_prophets_3, _getRowData_prophets__prophet_code2, _getRowData_prophets_4, _getRowData_prophets_5, _getRowData_prophets_6, _getRowData_distribution_points_json1, _getRowData_distribution_points_json2, _getRowData_prophets__prophet_code3, _getRowData_prophets_7, _getRowData_prophets__prophet_code4, _getRowData_prophets_8, _getRowData_prophets_9, _getRowData_prophets__prophet_code5, _getRowData_prophets_10, _getRowData_prophets_11, _getRowData_prophets__prophet_code6, _getRowData_prophets_12;\n            let regional_cert = \"\";\n            if ((getRowData === null || getRowData === void 0 ? void 0 : (_getRowData_roleIds = getRowData.roleIds) === null || _getRowData_roleIds === void 0 ? void 0 : _getRowData_roleIds.includes(2)) || (getRowData === null || getRowData === void 0 ? void 0 : (_getRowData_roleIds1 = getRowData.roleIds) === null || _getRowData_roleIds1 === void 0 ? void 0 : _getRowData_roleIds1.includes(3))) {\n                if (getRowData.country_code == \"UK\") {\n                    regional_cert = getRowData === null || getRowData === void 0 ? void 0 : getRowData.red_tractor;\n                } else if (getRowData.country_code == \"ZA\") {\n                    regional_cert = getRowData === null || getRowData === void 0 ? void 0 : getRowData.puc_code;\n                } else if (getRowData.country_code == \"CL\") {\n                    regional_cert = getRowData === null || getRowData === void 0 ? void 0 : getRowData.chile_certificate_number;\n                }\n            }\n            let currencyId = \"\";\n            let currencyName = \"\";\n            if ((getRowData === null || getRowData === void 0 ? void 0 : (_getRowData_roleIds2 = getRowData.roleIds) === null || _getRowData_roleIds2 === void 0 ? void 0 : _getRowData_roleIds2.includes(1)) || (getRowData === null || getRowData === void 0 ? void 0 : (_getRowData_roleIds3 = getRowData.roleIds) === null || _getRowData_roleIds3 === void 0 ? void 0 : _getRowData_roleIds3.includes(6))) {\n                currencyId = getRowData === null || getRowData === void 0 ? void 0 : getRowData.currency_id;\n                currencyName = getRowData === null || getRowData === void 0 ? void 0 : getRowData.currency_name;\n            } else {\n                currencyId = 1;\n                currencyName = \"Sterling\";\n            }\n            function getCorrespondingUserLookup(curr) {\n                if (curr == \"GBP\") {\n                    return \"GBPBACS\";\n                } else if (curr == \"EUR\") {\n                    return \"EUROSEPA\";\n                } else if (curr == \"USD\") {\n                    return \"USDPRIORITY\";\n                } else {\n                    return \"\";\n                }\n            }\n            console.log(\"get row data\", getRowData);\n            var _getRowData_edi;\n            const filteredISSExportData = {\n                \"Supplier Active\": \"N/A\",\n                \"Supplier code\": getRowData === null || getRowData === void 0 ? void 0 : (_getRowData_prophets_ = getRowData.prophets[0]) === null || _getRowData_prophets_ === void 0 ? void 0 : (_getRowData_prophets__prophet_code = _getRowData_prophets_.prophet_code) === null || _getRowData_prophets__prophet_code === void 0 ? void 0 : _getRowData_prophets__prophet_code.trim(),\n                \"EDI Partner\": \"N/A\",\n                \"Supplier name\": getRowData === null || getRowData === void 0 ? void 0 : (_getRowData_company_name = getRowData.company_name) === null || _getRowData_company_name === void 0 ? void 0 : _getRowData_company_name.trim(),\n                \"EDI ANA number\": (_getRowData_edi = getRowData === null || getRowData === void 0 ? void 0 : getRowData.edi) !== null && _getRowData_edi !== void 0 ? _getRowData_edi : \"N/A\",\n                \"Producer (supplier)\": \"N/A\",\n                \"Department number\": \"N/A\",\n                \"Currency number\": (getRowData === null || getRowData === void 0 ? void 0 : getRowData.currency_id) ? getRowData === null || getRowData === void 0 ? void 0 : getRowData.currency_id : 1,\n                \"Global gap number\": getRowData === null || getRowData === void 0 ? void 0 : getRowData.global_gap_number,\n                \"Grower group\": \"N/A\",\n                \"Defra county number\": \"N/A\",\n                \"Date start\": \"N/A\",\n                \"Date end\": \"N/A\",\n                \"Organic Cert\": getRowData === null || getRowData === void 0 ? void 0 : getRowData.organic_certificate_number,\n                \"Regional Cert\": regional_cert,\n                \"Head office\": getRowData === null || getRowData === void 0 ? void 0 : (_getRowData_prophets_1 = getRowData.prophets[0]) === null || _getRowData_prophets_1 === void 0 ? void 0 : (_getRowData_prophets__prophet_code1 = _getRowData_prophets_1.prophet_code) === null || _getRowData_prophets__prophet_code1 === void 0 ? void 0 : _getRowData_prophets__prophet_code1.trim(),\n                \"Country Code\": getRowData === null || getRowData === void 0 ? void 0 : getRowData.country_code,\n                \"Distribution point for supplier\": (getRowData === null || getRowData === void 0 ? void 0 : (_getRowData_distribution_points_json = getRowData.distribution_points_json) === null || _getRowData_distribution_points_json === void 0 ? void 0 : _getRowData_distribution_points_json.length) > 0 ? getRowData === null || getRowData === void 0 ? void 0 : getRowData.distribution_points_json[0].from_dp : \"N/A\",\n                \"Bool 2\": \"N/A\",\n                \"Bool 3\": \"N/A\",\n                \"Address line 1\": getRowData === null || getRowData === void 0 ? void 0 : getRowData.address_line_1,\n                \"Address line 2\": getRowData === null || getRowData === void 0 ? void 0 : getRowData.address_line_2,\n                \"Address line 3\": getRowData === null || getRowData === void 0 ? void 0 : getRowData.address_line_3,\n                \"Address line 4\": getRowData === null || getRowData === void 0 ? void 0 : getRowData.address_line_4,\n                \"Post code\": getRowData === null || getRowData === void 0 ? void 0 : getRowData.postal_code,\n                \"Currency Number\": (getRowData === null || getRowData === void 0 ? void 0 : getRowData.currency_id) ? getRowData === null || getRowData === void 0 ? void 0 : getRowData.currency_id : 1,\n                \"Bank general ledger code\": (getRowData === null || getRowData === void 0 ? void 0 : getRowData.iss_ledger_code) ? getRowData === null || getRowData === void 0 ? void 0 : getRowData.iss_ledger_code : \"12200\",\n                \"Bank general ledger code Currency number if bank\": (getRowData === null || getRowData === void 0 ? void 0 : getRowData.currency_id) ? getRowData === null || getRowData === void 0 ? void 0 : getRowData.currency_id : 1,\n                \"Settlement days\": getRowData === null || getRowData === void 0 ? void 0 : getRowData.payment_terms,\n                \"Department Number\": (getRowData === null || getRowData === void 0 ? void 0 : (_getRowData_prophets_2 = getRowData.prophets[0]) === null || _getRowData_prophets_2 === void 0 ? void 0 : _getRowData_prophets_2.prophet_id) == 1 ? 1 : (getRowData === null || getRowData === void 0 ? void 0 : (_getRowData_prophets_3 = getRowData.prophets[0]) === null || _getRowData_prophets_3 === void 0 ? void 0 : _getRowData_prophets_3.prophet_id) == 2 ? 9 : (getRowData === null || getRowData === void 0 ? void 0 : getRowData.prophets[0].prophet_id) == 3 ? 3 : (getRowData === null || getRowData === void 0 ? void 0 : getRowData.prophets[0].prophet_id) == 4 ? 2 : \"N/A\",\n                \"Area Number\": \"1\",\n                Vatable: (getRowData === null || getRowData === void 0 ? void 0 : getRowData.vatable) != null ? (getRowData === null || getRowData === void 0 ? void 0 : getRowData.vatable) ? \"1\" : \"0\" : \"0\",\n                Buyer: \"1\",\n                \"Billing type\": \"0\",\n                \"Payment type\": (getRowData === null || getRowData === void 0 ? void 0 : getRowData.payment_type) ? getRowData === null || getRowData === void 0 ? void 0 : getRowData.payment_type : 2,\n                \"Expense general ledger code\": \"N/A\",\n                \"Authorise on register\": \"N/A\",\n                \"Use % authorise rule\": 5,\n                \"User text 1\": \"N/A\",\n                \"Mandatory altfil on service jobs\": \"N/A\",\n                \"Organization ID\": \"N/A\",\n                id: getRowData === null || getRowData === void 0 ? void 0 : getRowData.id,\n                isEmergencyAndFinanceNotComplete: getRowData === null || getRowData === void 0 ? void 0 : getRowData.isEmergencyAndFinanceNotComplete\n            };\n            var _getRowData_edi1;\n            const newSupplierActiveData = {\n                \"Supplier Active\": (getRowData === null || getRowData === void 0 ? void 0 : getRowData.isActive) ? 1 : 0,\n                \"Haulage cube local\": \"\",\n                \"Haulage cube name\": \"\",\n                \"update guesstimates type\": 1,\n                \"Organization ID\": \"\",\n                \"Vat number 1\": getRowData === null || getRowData === void 0 ? void 0 : getRowData.vat_number,\n                \"Organic cert\": getRowData === null || getRowData === void 0 ? void 0 : getRowData.organic_certificate_number,\n                \"Regional cert\": regional_cert,\n                \"Global gap number\": getRowData === null || getRowData === void 0 ? void 0 : getRowData.global_gap_number,\n                \"Enforce department\": \"\",\n                \"Sendac Group\": (getRowData === null || getRowData === void 0 ? void 0 : getRowData.supplier_group) ? JSON.parse(getRowData === null || getRowData === void 0 ? void 0 : getRowData.supplier_group)[0].value : \"\",\n                \"Supplier code\": getRowData === null || getRowData === void 0 ? void 0 : (_getRowData_prophets_4 = getRowData.prophets[0]) === null || _getRowData_prophets_4 === void 0 ? void 0 : (_getRowData_prophets__prophet_code2 = _getRowData_prophets_4.prophet_code) === null || _getRowData_prophets__prophet_code2 === void 0 ? void 0 : _getRowData_prophets__prophet_code2.trim(),\n                \"Supplier name\": getRowData.company_name,\n                \"Supplier type\": getRowData === null || getRowData === void 0 ? void 0 : getRowData.supplier_type,\n                \"User Lookup 2\": \"\",\n                \"Address Line 1\": getRowData === null || getRowData === void 0 ? void 0 : getRowData.address_line_1,\n                \"Address Line 2\": getRowData === null || getRowData === void 0 ? void 0 : getRowData.address_line_2,\n                \"Address Line 3\": getRowData === null || getRowData === void 0 ? void 0 : getRowData.address_line_3,\n                \"Address Line 4\": getRowData === null || getRowData === void 0 ? void 0 : getRowData.address_line_4,\n                \"Post code\": getRowData === null || getRowData === void 0 ? void 0 : getRowData.postal_code,\n                \"Country code\": getRowData === null || getRowData === void 0 ? void 0 : getRowData.country_code,\n                \"Payee supplier code\": \"\",\n                \"Invoice supplier\": \"\",\n                \"Head office\": \"\",\n                \"Settlement days\": getRowData === null || getRowData === void 0 ? void 0 : getRowData.payment_terms,\n                \"Bank general ledger code Currency number if bank\": currencyId,\n                \"Currency number\": currencyId,\n                \"Currency number Currency name\": currencyName,\n                \"Bank general ledger code\": getGLCode(getRowData === null || getRowData === void 0 ? void 0 : getRowData.internal_ledger_code),\n                \"payment type\": getRowData === null || getRowData === void 0 ? void 0 : getRowData.payment_type,\n                \"Payment type name\": getRowData === null || getRowData === void 0 ? void 0 : getRowData.payment_type_name,\n                \"country code\": getRowData === null || getRowData === void 0 ? void 0 : getRowData.country_code,\n                Vatable: (getRowData === null || getRowData === void 0 ? void 0 : getRowData.vatable) != null ? (getRowData === null || getRowData === void 0 ? void 0 : getRowData.vatable) ? \"1\" : \"0\" : \"0\",\n                \"vatable desc\": (getRowData === null || getRowData === void 0 ? void 0 : getRowData.vatable) != null ? (getRowData === null || getRowData === void 0 ? void 0 : getRowData.vatable) ? \"Vatable\" : \"None vatable\" : \"None vatable\",\n                \"Area Number\": (getRowData === null || getRowData === void 0 ? void 0 : getRowData.prophets[0].prophet_id) == 3 || (getRowData === null || getRowData === void 0 ? void 0 : getRowData.prophets[0].prophet_id) == 4 ? 1 : 7,\n                Buyer: 1,\n                \"Multiple lot indicator\": \"0\",\n                \"multiple lot indicator desc\": \"By Lot\",\n                \"Generate Pallet Loading Plan\": \"\",\n                \"Distribution point for supplier\": 6,\n                \"Payment terms\": \"\",\n                \"Department Number\": (getRowData === null || getRowData === void 0 ? void 0 : (_getRowData_prophets_5 = getRowData.prophets[0]) === null || _getRowData_prophets_5 === void 0 ? void 0 : _getRowData_prophets_5.prophet_id) == 1 ? 1 : (getRowData === null || getRowData === void 0 ? void 0 : (_getRowData_prophets_6 = getRowData.prophets[0]) === null || _getRowData_prophets_6 === void 0 ? void 0 : _getRowData_prophets_6.prophet_id) == 2 ? 9 : (getRowData === null || getRowData === void 0 ? void 0 : getRowData.prophets[0].prophet_id) == 3 ? 3 : (getRowData === null || getRowData === void 0 ? void 0 : getRowData.prophets[0].prophet_id) == 4 ? 2 : \"\",\n                \"Allow credit rebates\": \"\",\n                \"Alternative DP for supplier\": 1,\n                \"Actual posting stops purchase charges\": \"\",\n                \"Authorise on register\": \"\",\n                \"User text 1\": \"\",\n                \"User lookup 1\": (getRowData === null || getRowData === void 0 ? void 0 : getRowData.prophets[0].prophet_id) == 3 || (getRowData === null || getRowData === void 0 ? void 0 : getRowData.prophets[0].prophet_id) == 4 ? getCorrespondingUserLookup(getRowData === null || getRowData === void 0 ? void 0 : getRowData.currency_code) : \"\",\n                \"Receive orders from edi\": \"\",\n                \"Send invoices from edi\": \"\",\n                \"Send orders from edi\": \"\",\n                \"EDI partner\": (getRowData === null || getRowData === void 0 ? void 0 : getRowData.prophets[0].prophet_id) == 3 || (getRowData === null || getRowData === void 0 ? void 0 : getRowData.prophets[0].prophet_id) == 4 ? 2000 : \"\",\n                \"Generic code\": (getRowData === null || getRowData === void 0 ? void 0 : getRowData.prophets[0].prophet_id) == 3 || (getRowData === null || getRowData === void 0 ? void 0 : getRowData.prophets[0].prophet_id) == 4 ? \"STOCK\" : \"\",\n                \"EDI ANA number\": (_getRowData_edi1 = getRowData === null || getRowData === void 0 ? void 0 : getRowData.edi) !== null && _getRowData_edi1 !== void 0 ? _getRowData_edi1 : \"N/\",\n                \"User % authorize rule\": 5,\n                FromDP: (getRowData === null || getRowData === void 0 ? void 0 : (_getRowData_distribution_points_json1 = getRowData.distribution_points_json) === null || _getRowData_distribution_points_json1 === void 0 ? void 0 : _getRowData_distribution_points_json1.length) > 0 ? getRowData.distribution_points_json[0].from_dp || \"\" : \"\"\n            };\n            let UlpFil = {};\n            if ((getRowData === null || getRowData === void 0 ? void 0 : (_getRowData_distribution_points_json2 = getRowData.distribution_points_json) === null || _getRowData_distribution_points_json2 === void 0 ? void 0 : _getRowData_distribution_points_json2.length) > 0 && ((getRowData === null || getRowData === void 0 ? void 0 : getRowData.distribution_points_json[0].from_dp) === null || (getRowData === null || getRowData === void 0 ? void 0 : getRowData.distribution_points_json[0].from_dp) === undefined)) {\n                var _getRowData_distribution_points_json3, _getRowData_prophets__prophet_code7, _getRowData_prophets_13, _getRowData_distribution_points_json4, _getRowData_distribution_points_json_;\n                UlpFil = {\n                    \"Distribution point\": \"\",\n                    Description: (getRowData === null || getRowData === void 0 ? void 0 : (_getRowData_distribution_points_json3 = getRowData.distribution_points_json) === null || _getRowData_distribution_points_json3 === void 0 ? void 0 : _getRowData_distribution_points_json3.length) > 0 && ((getRowData === null || getRowData === void 0 ? void 0 : getRowData.distribution_points_json[0].from_dp) === null || (getRowData === null || getRowData === void 0 ? void 0 : getRowData.distribution_points_json[0].from_dp) === undefined) ? getRowData === null || getRowData === void 0 ? void 0 : getRowData.distribution_points_json[0].name : \"\",\n                    \"Service Supplier Code\": ((getRowData === null || getRowData === void 0 ? void 0 : getRowData.distribution_points_json[0].from_dp) === null || (getRowData === null || getRowData === void 0 ? void 0 : getRowData.distribution_points_json[0].from_dp) === undefined) && (getRowData === null || getRowData === void 0 ? void 0 : (_getRowData_prophets_13 = getRowData.prophets[0]) === null || _getRowData_prophets_13 === void 0 ? void 0 : (_getRowData_prophets__prophet_code7 = _getRowData_prophets_13.prophet_code) === null || _getRowData_prophets__prophet_code7 === void 0 ? void 0 : _getRowData_prophets__prophet_code7.trim()),\n                    \"Default expected stock status\": ((getRowData === null || getRowData === void 0 ? void 0 : getRowData.distribution_points_json[0].from_dp) === null || (getRowData === null || getRowData === void 0 ? void 0 : getRowData.distribution_points_json[0].from_dp) === undefined) && \"0\",\n                    \"Default received stock status\": ((getRowData === null || getRowData === void 0 ? void 0 : getRowData.distribution_points_json[0].from_dp) === null || (getRowData === null || getRowData === void 0 ? void 0 : getRowData.distribution_points_json[0].from_dp) === undefined) && \"0\",\n                    \"Pallets in packhouse\": ((getRowData === null || getRowData === void 0 ? void 0 : getRowData.distribution_points_json[0].from_dp) === null || (getRowData === null || getRowData === void 0 ? void 0 : getRowData.distribution_points_json[0].from_dp) === undefined) && \"0\",\n                    \"Default haulier\": ((getRowData === null || getRowData === void 0 ? void 0 : getRowData.distribution_points_json[0].from_dp) === null || (getRowData === null || getRowData === void 0 ? void 0 : getRowData.distribution_points_json[0].from_dp) === undefined) && \"ZZZZZ\",\n                    \"Default expected location id\": ((getRowData === null || getRowData === void 0 ? void 0 : getRowData.distribution_points_json[0].from_dp) === null || (getRowData === null || getRowData === void 0 ? void 0 : getRowData.distribution_points_json[0].from_dp) === undefined) && 21,\n                    \"Default receiving location id\": ((getRowData === null || getRowData === void 0 ? void 0 : getRowData.distribution_points_json[0].from_dp) === null || (getRowData === null || getRowData === void 0 ? void 0 : getRowData.distribution_points_json[0].from_dp) === undefined) && 21,\n                    \"Packhouse location id\": ((getRowData === null || getRowData === void 0 ? void 0 : getRowData.distribution_points_json[0].from_dp) === null || (getRowData === null || getRowData === void 0 ? void 0 : getRowData.distribution_points_json[0].from_dp) === undefined) && 21,\n                    \"Default pick location id\": ((getRowData === null || getRowData === void 0 ? void 0 : getRowData.distribution_points_json[0].from_dp) === null || (getRowData === null || getRowData === void 0 ? void 0 : getRowData.distribution_points_json[0].from_dp) === undefined) && 21,\n                    \"Despatch location id\": ((getRowData === null || getRowData === void 0 ? void 0 : getRowData.distribution_points_json[0].from_dp) === null || (getRowData === null || getRowData === void 0 ? void 0 : getRowData.distribution_points_json[0].from_dp) === undefined) && 21,\n                    \"Default waste location id\": ((getRowData === null || getRowData === void 0 ? void 0 : getRowData.distribution_points_json[0].from_dp) === null || (getRowData === null || getRowData === void 0 ? void 0 : getRowData.distribution_points_json[0].from_dp) === undefined) && 21,\n                    \"Default pre-pick location id\": ((getRowData === null || getRowData === void 0 ? void 0 : getRowData.distribution_points_json[0].from_dp) === null || (getRowData === null || getRowData === void 0 ? void 0 : getRowData.distribution_points_json[0].from_dp) === undefined) && 21,\n                    \"Default returns location id\": ((getRowData === null || getRowData === void 0 ? void 0 : getRowData.distribution_points_json[0].from_dp) === null || (getRowData === null || getRowData === void 0 ? void 0 : getRowData.distribution_points_json[0].from_dp) === undefined) && 21,\n                    Address: \"\",\n                    \"Service supplier code\": \"\",\n                    \"EDI Reference Code\": \"\",\n                    \"EDI ANA Code\": \"\",\n                    \"User Integer 1\": ((getRowData === null || getRowData === void 0 ? void 0 : getRowData.distribution_points_json[0].from_dp) === null || (getRowData === null || getRowData === void 0 ? void 0 : getRowData.distribution_points_json[0].from_dp) === undefined) && 1,\n                    \"Movement resource group\": \"\",\n                    \"Handheld application used\": ((getRowData === null || getRowData === void 0 ? void 0 : getRowData.distribution_points_json[0].from_dp) === null || (getRowData === null || getRowData === void 0 ? void 0 : getRowData.distribution_points_json[0].from_dp) === undefined) && \"0\",\n                    \"Pallets in procure/receiving\": ((getRowData === null || getRowData === void 0 ? void 0 : getRowData.distribution_points_json[0].from_dp) === null || (getRowData === null || getRowData === void 0 ? void 0 : getRowData.distribution_points_json[0].from_dp) === undefined) && \"0\",\n                    \"Operational depo\": \"\",\n                    \"Enabled for masterfile sending\": \"\",\n                    \"Connected registed depot\": \"\",\n                    \"EDI Transmission type of depo\": \"\",\n                    \"Container loading depo\": ((getRowData === null || getRowData === void 0 ? void 0 : getRowData.distribution_points_json[0].from_dp) === null || (getRowData === null || getRowData === void 0 ? void 0 : getRowData.distribution_points_json[0].from_dp) === undefined) && \"0\",\n                    \"Airport depot\": \"\",\n                    \"Sms notification\": \"\",\n                    Port: \"\",\n                    Dormant: \"\",\n                    Active: ((getRowData === null || getRowData === void 0 ? void 0 : getRowData.distribution_points_json[0].from_dp) === null || (getRowData === null || getRowData === void 0 ? void 0 : getRowData.distribution_points_json[0].from_dp) === undefined) && 1,\n                    \"Ingredient distribution point\": \"\",\n                    \"Show in CE\": ((getRowData === null || getRowData === void 0 ? void 0 : getRowData.distribution_points_json[0].from_dp) === null || (getRowData === null || getRowData === void 0 ? void 0 : getRowData.distribution_points_json[0].from_dp) === undefined) && 1,\n                    \"Charge direction\": \"\",\n                    \"Pallet receive time\": \"\",\n                    \"User string 3\": \"\",\n                    \"Direct DP\": (getRowData === null || getRowData === void 0 ? void 0 : (_getRowData_distribution_points_json4 = getRowData.distribution_points_json) === null || _getRowData_distribution_points_json4 === void 0 ? void 0 : _getRowData_distribution_points_json4.length) > 0 && ((getRowData === null || getRowData === void 0 ? void 0 : getRowData.distribution_points_json[0].from_dp) === null || (getRowData === null || getRowData === void 0 ? void 0 : getRowData.distribution_points_json[0].from_dp) === undefined) ? (getRowData === null || getRowData === void 0 ? void 0 : (_getRowData_distribution_points_json_ = getRowData.distribution_points_json[0]) === null || _getRowData_distribution_points_json_ === void 0 ? void 0 : _getRowData_distribution_points_json_.direct_dp) ? 1 : \"0\" : \"\",\n                    \"Include on XML\": ((getRowData === null || getRowData === void 0 ? void 0 : getRowData.distribution_points_json[0].from_dp) === null || (getRowData === null || getRowData === void 0 ? void 0 : getRowData.distribution_points_json[0].from_dp) === undefined) && \"0\"\n                };\n            }\n            // const newRoleData = {\n            //   Sendacroleid: \"\",\n            //   \"Supplier code\": getRowData?.prophets[0]?.prophet_code?.trim(),\n            //   Description: \"\",\n            //   \"Supplier Code Supplier Name\": getRowData.company_name,\n            //   Type: \"\",\n            //   \"Type Description\": getRowData?.[\"role names\"],\n            //   \"Supplier code Global gap number\": getRowData?.global_gap_number,\n            //   \"Created timestamp\": \"\",\n            //   Active: \"\",\n            // };\n            const newRoleData = multipleSendRoleOnRoleNums(getRowData, getRowData === null || getRowData === void 0 ? void 0 : getRowData.role_num);\n            const extractedSendacGroup = extractSendacGroup(getRowData === null || getRowData === void 0 ? void 0 : getRowData.supplier_group);\n            let sort_code = \"\";\n            let account_number = \"\";\n            let swiftBicCode = \"\";\n            let iban = \"\";\n            const swiftBicRegex = /^([A-Z0-9]{4}[A-Z0-9]{2}[A-Z0-9]{2}|[A-Z0-9]{4}[A-Z0-9]{2}[A-Z0-9]{2}[A-Z0-9]{3})$/;\n            if (swiftBicRegex.test(getRowData === null || getRowData === void 0 ? void 0 : getRowData.sort_bic) && (getRowData === null || getRowData === void 0 ? void 0 : getRowData.has_iban)) {\n                var _getRowData_account_number;\n                sort_code = \"000000\";\n                account_number = getRowData === null || getRowData === void 0 ? void 0 : (_getRowData_account_number = getRowData.account_number) === null || _getRowData_account_number === void 0 ? void 0 : _getRowData_account_number.slice(-8);\n                swiftBicCode = getRowData === null || getRowData === void 0 ? void 0 : getRowData.sort_bic;\n                iban = getRowData === null || getRowData === void 0 ? void 0 : getRowData.account_number;\n            } else if (!(getRowData === null || getRowData === void 0 ? void 0 : getRowData.has_iban) && swiftBicRegex.test(getRowData === null || getRowData === void 0 ? void 0 : getRowData.sort_bic)) {\n                sort_code = \"000000\";\n                account_number = getRowData === null || getRowData === void 0 ? void 0 : getRowData.account_number;\n                swiftBicCode = getRowData === null || getRowData === void 0 ? void 0 : getRowData.sort_bic;\n            } else {\n                sort_code = getRowData === null || getRowData === void 0 ? void 0 : getRowData.sort_bic;\n                account_number = getRowData === null || getRowData === void 0 ? void 0 : getRowData.account_number;\n            }\n            const bankac = {\n                \"Supplier code\": getRowData === null || getRowData === void 0 ? void 0 : (_getRowData_prophets_7 = getRowData.prophets[0]) === null || _getRowData_prophets_7 === void 0 ? void 0 : (_getRowData_prophets__prophet_code3 = _getRowData_prophets_7.prophet_code) === null || _getRowData_prophets__prophet_code3 === void 0 ? void 0 : _getRowData_prophets__prophet_code3.trim(),\n                \"Record id\": \"\",\n                \"Bank sort code\": sort_code,\n                \"Account number\": account_number,\n                \"Country code\": (getRowData === null || getRowData === void 0 ? void 0 : getRowData.country_code) == \"UK\" ? \"GB\" : getRowData === null || getRowData === void 0 ? void 0 : getRowData.country_code,\n                \"Account holder\": getRowData.company_name,\n                \"Currency number\": currencyId,\n                \"BACS currency\": getRowData === null || getRowData === void 0 ? void 0 : getRowData.bacs_currency_code,\n                \"Address Line 1\": \"\",\n                \"Address Line 2\": \"\",\n                \"BIC/Swift address\": swiftBicCode,\n                \"Internation bank reference code\": iban,\n                \"Account user id\": \"\",\n                \"Post code\": \"\"\n            };\n            const senbnk = {\n                \"Supplier code\": \"\",\n                Bankacid: \"\",\n                \"Header bank record id\": \"\",\n                \"Intermediary bank account id\": \"\",\n                \"Intermediary bank account id Internation bank reference code\": \"\"\n            };\n            // Parse and map contacts\n            const contacts = extractContacts(getRowData === null || getRowData === void 0 ? void 0 : (_getRowData_prophets_8 = getRowData.prophets[0]) === null || _getRowData_prophets_8 === void 0 ? void 0 : (_getRowData_prophets__prophet_code4 = _getRowData_prophets_8.prophet_code) === null || _getRowData_prophets__prophet_code4 === void 0 ? void 0 : _getRowData_prophets__prophet_code4.trim(), getRowData.contacts_json, getRowData === null || getRowData === void 0 ? void 0 : getRowData.company_name);\n            const newOrganizationData = {\n                \"Organization ID\": \"\",\n                \"Organization Name\": (getRowData === null || getRowData === void 0 ? void 0 : (_getRowData_prophets_9 = getRowData.prophets[0]) === null || _getRowData_prophets_9 === void 0 ? void 0 : _getRowData_prophets_9.prophet_code) ? getRowData === null || getRowData === void 0 ? void 0 : (_getRowData_prophets_10 = getRowData.prophets[0]) === null || _getRowData_prophets_10 === void 0 ? void 0 : (_getRowData_prophets__prophet_code5 = _getRowData_prophets_10.prophet_code) === null || _getRowData_prophets__prophet_code5 === void 0 ? void 0 : _getRowData_prophets__prophet_code5.trim() : \"\"\n            };\n            const newOrganizationRoleData = {\n                \"Organization ID\": \"\",\n                \"Organization Code\": \"\",\n                \"Role Type ID\": \"\",\n                Selected: \"\",\n                \"Organisation ID\": \"\",\n                \"Role Type ID\": \"\",\n                \"Contact ID\": \"\",\n                \"Contact ID Email Address\": \"\",\n                \"Contact ID Telephone\": \"\",\n                \"Contact ID Fax\": \"\"\n            };\n            const sheetSuppliersId = {\n                id: getRowData === null || getRowData === void 0 ? void 0 : getRowData.id,\n                supplierName: getRowData === null || getRowData === void 0 ? void 0 : getRowData.company_name,\n                isEmergencyAndFinanceNotComplete: getRowData === null || getRowData === void 0 ? void 0 : getRowData.isEmergencyAndFinanceNotComplete,\n                supplierCode: (getRowData === null || getRowData === void 0 ? void 0 : (_getRowData_prophets_11 = getRowData.prophets[0]) === null || _getRowData_prophets_11 === void 0 ? void 0 : _getRowData_prophets_11.prophet_code) ? getRowData === null || getRowData === void 0 ? void 0 : (_getRowData_prophets_12 = getRowData.prophets[0]) === null || _getRowData_prophets_12 === void 0 ? void 0 : (_getRowData_prophets__prophet_code6 = _getRowData_prophets_12.prophet_code) === null || _getRowData_prophets__prophet_code6 === void 0 ? void 0 : _getRowData_prophets__prophet_code6.trim() : \"\"\n            };\n            if (isSelected) {\n                setSupplierActiveData((prev)=>[\n                        ...prev,\n                        newSupplierActiveData\n                    ]);\n                setRoleData((prev)=>[\n                        ...prev,\n                        ...newRoleData\n                    ]);\n                setContactData((prev)=>[\n                        ...prev,\n                        ...contacts\n                    ]);\n                setOrganizationData((prev)=>[\n                        ...prev,\n                        newOrganizationData\n                    ]);\n                setOrganizationRoleData((prev)=>[\n                        ...prev,\n                        newOrganizationRoleData\n                    ]);\n                setSheetSupplierId((prev)=>[\n                        ...prev,\n                        sheetSuppliersId\n                    ]);\n                setSendacGroupData((prev)=>[\n                        ...prev,\n                        ...extractedSendacGroup\n                    ]);\n                setBankAc((prev)=>[\n                        ...prev,\n                        bankac\n                    ]);\n                setSenBnk((prev)=>[\n                        ...prev,\n                        senbnk\n                    ]);\n                if (Object.keys(UlpFil).length > 0) {\n                    setUlpFilData((prev)=>[\n                            ...prev,\n                            UlpFil\n                        ]);\n                }\n                setMultipleFilterISSData((prev)=>[\n                        ...prev,\n                        filteredISSExportData\n                    ]);\n            } else {\n                setMultipleFilterISSData((prev)=>prev.filter((item)=>item.id !== getRowData.id));\n                setSupplierActiveData((prev)=>prev.filter((item, index)=>{\n                        var _getRowData_prophets__prophet_code, _getRowData_prophets_;\n                        return index === 0 || item[\"Supplier code\"] !== (getRowData === null || getRowData === void 0 ? void 0 : (_getRowData_prophets_ = getRowData.prophets[0]) === null || _getRowData_prophets_ === void 0 ? void 0 : (_getRowData_prophets__prophet_code = _getRowData_prophets_.prophet_code) === null || _getRowData_prophets__prophet_code === void 0 ? void 0 : _getRowData_prophets__prophet_code.trim());\n                    }));\n                setUlpFilData((prev)=>prev.filter((item, index)=>{\n                        var _getRowData_prophets__prophet_code, _getRowData_prophets_;\n                        return index === 0 || item[\"Service Supplier Code\"] !== (getRowData === null || getRowData === void 0 ? void 0 : (_getRowData_prophets_ = getRowData.prophets[0]) === null || _getRowData_prophets_ === void 0 ? void 0 : (_getRowData_prophets__prophet_code = _getRowData_prophets_.prophet_code) === null || _getRowData_prophets__prophet_code === void 0 ? void 0 : _getRowData_prophets__prophet_code.trim());\n                    }));\n                setRoleData((prev)=>prev.filter((item, index)=>{\n                        var _getRowData_prophets__prophet_code, _getRowData_prophets_;\n                        return index === 0 || item[\"Supplier code\"] !== (getRowData === null || getRowData === void 0 ? void 0 : (_getRowData_prophets_ = getRowData.prophets[0]) === null || _getRowData_prophets_ === void 0 ? void 0 : (_getRowData_prophets__prophet_code = _getRowData_prophets_.prophet_code) === null || _getRowData_prophets__prophet_code === void 0 ? void 0 : _getRowData_prophets__prophet_code.trim());\n                    }));\n                // setContactData((prev) =>\n                // prev.filter(\n                //   (item, index) => index === 0 || item[\"Supplier code\"] !== contacts[0][\"Supplier code\"]\n                // )\n                // );\n                setContactData((prev)=>prev.filter((item, index)=>{\n                        if (contacts.length > 0) {\n                            return index === 0 || item[\"Supplier code\"] !== contacts[0][\"Supplier code\"];\n                        } else {\n                            // Handle the case when contacts array is empty\n                            return true; // or any other logic based on your requirements\n                        }\n                    }));\n                setOrganizationData((prev)=>prev.filter((item, index)=>{\n                        var _getRowData_prophets__prophet_code, _getRowData_prophets_;\n                        return index === 0 || item[\"Organization Name\"] !== (getRowData === null || getRowData === void 0 ? void 0 : (_getRowData_prophets_ = getRowData.prophets[0]) === null || _getRowData_prophets_ === void 0 ? void 0 : (_getRowData_prophets__prophet_code = _getRowData_prophets_.prophet_code) === null || _getRowData_prophets__prophet_code === void 0 ? void 0 : _getRowData_prophets__prophet_code.trim());\n                    }));\n                setOrganizationRoleData((prev)=>prev.filter((item, index)=>index === 0 || item[\"Organization ID\"] !== \"\"));\n                setSendacGroupData((prev)=>prev.filter((item, index)=>{\n                        var _extractedSendacGroup_;\n                        return index === 0 || item[\"Description\"] !== ((_extractedSendacGroup_ = extractedSendacGroup[0]) === null || _extractedSendacGroup_ === void 0 ? void 0 : _extractedSendacGroup_.Description);\n                    }));\n                setBankAc((prev)=>prev.filter((item, index)=>{\n                        var _getRowData_prophets__prophet_code, _getRowData_prophets_;\n                        return index === 0 || item[\"Supplier code\"] !== (getRowData === null || getRowData === void 0 ? void 0 : (_getRowData_prophets_ = getRowData.prophets[0]) === null || _getRowData_prophets_ === void 0 ? void 0 : (_getRowData_prophets__prophet_code = _getRowData_prophets_.prophet_code) === null || _getRowData_prophets__prophet_code === void 0 ? void 0 : _getRowData_prophets__prophet_code.trim());\n                    }));\n                setSenBnk((prev)=>prev.filter((item, index)=>index === 0 || item[\"Supplier code\"] !== \"\"));\n                setSheetSupplierId((prev)=>prev.filter((item, index)=>index === 0 || item[\"id\"] !== (getRowData === null || getRowData === void 0 ? void 0 : getRowData.id)));\n            }\n            setSupplierCheckedValue(supplierActiveData.length > 0 || roleData.length > 0 || contactData.length > 0 || organizationData.length > 0 || organizationRoleData.length > 0 || bankAc.length > 0 || senBnk.length > 0 || sendacGroupData.length > 0 || ulpFilData.length > 0 || multipleFilterISSData.length > 0);\n        } else {\n            if (event.node.selected) {\n                if (doesContainCancelledSupplier) {\n                    setContainsCancelledSupplier(true);\n                    return;\n                }\n                setIncompleteToast(true);\n                setTimeout(()=>{\n                    setIncompleteToast(false);\n                }, 3000);\n            }\n        }\n    };\n    const clearFiltersHandler = ()=>{\n        setRowData(allRowData);\n        setIsFiltered(false);\n        setIsFilteredName(\"\");\n        setProphetId(\"\");\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_toastify__WEBPACK_IMPORTED_MODULE_15__.ToastContainer, {\n                limit: 1\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\suppliers.js\",\n                lineNumber: 1770,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Layout__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                userData: userData,\n                blockScreen: blockScreen,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mr-20 md:mr-12 lg:mr-14\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-row md:flex-col lg:flex-row justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center border border-light-gray rounded-md px-3 w-auto md:w-fit lg:w-auto md:py-2 lg:py-1 md:mb-3 lg:mb-0 mr-2\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        id: \"default-checkbox\",\n                                                        type: \"radio\",\n                                                        value: \"default\",\n                                                        checked: selectedStatus === \"default\",\n                                                        className: \"w-5 h-5 text-blue border-theme-blue2 rounded\",\n                                                        onChange: handleFilterToggle\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\suppliers.js\",\n                                                        lineNumber: 1777,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        htmlFor: \"default-checkbox\",\n                                                        className: \"p-0 ml-3 labels\",\n                                                        children: \"Default\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\suppliers.js\",\n                                                        lineNumber: 1785,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\suppliers.js\",\n                                                lineNumber: 1776,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\suppliers.js\",\n                                            lineNumber: 1775,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center border border-light-gray rounded-md px-3 w-auto md:w-fit lg:w-auto md:py-2 lg:py-1 md:mb-3 lg:mb-0 mr-2\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        id: \"export-checkbox\",\n                                                        type: \"radio\",\n                                                        value: \"Exported\",\n                                                        checked: selectedStatus === \"Exported\",\n                                                        className: \"w-5 h-5 text-blue border-theme-blue2 rounded\",\n                                                        onChange: handleFilterToggle\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\suppliers.js\",\n                                                        lineNumber: 1792,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        htmlFor: \"export-checkbox\",\n                                                        className: \"p-0 ml-3 labels\",\n                                                        children: \"Exported\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\suppliers.js\",\n                                                        lineNumber: 1800,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\suppliers.js\",\n                                                lineNumber: 1791,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\suppliers.js\",\n                                            lineNumber: 1790,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center border border-light-gray rounded-md px-3 w-auto md:w-fit lg:w-auto md:py-2 lg:py-1 md:mb-3 lg:mb-0 mr-2\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        id: \"completed-radio\",\n                                                        type: \"radio\",\n                                                        value: \"Completed\",\n                                                        checked: selectedStatus === \"Completed\",\n                                                        className: \"w-5 h-5 text-blue border-theme-blue2 rounded\",\n                                                        onChange: handleFilterToggle\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\suppliers.js\",\n                                                        lineNumber: 1807,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        htmlFor: \"completed-radio\",\n                                                        className: \"p-0 ml-3 labels\",\n                                                        children: \"Completed\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\suppliers.js\",\n                                                        lineNumber: 1815,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\suppliers.js\",\n                                                lineNumber: 1806,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\suppliers.js\",\n                                            lineNumber: 1805,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center border border-light-gray rounded-md px-3 w-auto md:w-fit lg:w-auto md:py-2 lg:py-1 md:mb-3 lg:mb-0 mr-2\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        id: \"cancelled-radio\",\n                                                        type: \"radio\",\n                                                        value: \"Cancelled\",\n                                                        checked: selectedStatus === \"Cancelled\",\n                                                        className: \"w-5 h-5 text-blue border-theme-blue2 rounded\",\n                                                        onChange: handleFilterToggle\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\suppliers.js\",\n                                                        lineNumber: 1822,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        htmlFor: \"cancelled-radio\",\n                                                        className: \"p-0 ml-3 labels\",\n                                                        children: [\n                                                            \" \",\n                                                            \"Cancelled\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\suppliers.js\",\n                                                        lineNumber: 1830,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\suppliers.js\",\n                                                lineNumber: 1821,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\suppliers.js\",\n                                            lineNumber: 1820,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        isFiltered && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"p-3 py-1 flex items-center capitalize ml-5\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"b\", {\n                                                            className: \"mr-3\",\n                                                            children: \"Filtered On: \"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\suppliers.js\",\n                                                            lineNumber: 1840,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        \" \",\n                                                        isFilteredName\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\suppliers.js\",\n                                                    lineNumber: 1839,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    type: \"button\",\n                                                    onClick: clearFiltersHandler,\n                                                    className: \"flex h-[20px] border bg-red-500 text-white border-red-500 button rounded-md items-center !px-1 !py-1 ml-1\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fortawesome_react_fontawesome__WEBPACK_IMPORTED_MODULE_3__.FontAwesomeIcon, {\n                                                        icon: _fortawesome_free_solid_svg_icons__WEBPACK_IMPORTED_MODULE_24__.faClose,\n                                                        className: \"fw-bold\",\n                                                        size: \"lg\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\suppliers.js\",\n                                                        lineNumber: 1847,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\suppliers.js\",\n                                                    lineNumber: 1842,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\suppliers.js\",\n                                    lineNumber: 1774,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"relative block w-[47vh] text-gray-400 mt-0 pt-0\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"absolute px-4 py-1 pt-[0.4rem] 2xl:pt-1.5 text-black\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fortawesome_react_fontawesome__WEBPACK_IMPORTED_MODULE_3__.FontAwesomeIcon, {\n                                                        icon: _fortawesome_free_solid_svg_icons__WEBPACK_IMPORTED_MODULE_24__.faSearch,\n                                                        className: \"fw-bold\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\suppliers.js\",\n                                                        lineNumber: 1859,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\suppliers.js\",\n                                                    lineNumber: 1858,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"text\",\n                                                    id: \"filter-text-box\",\n                                                    placeholder: \"Search\",\n                                                    onInput: onFilterTextBoxChanged,\n                                                    className: \"block w-full px-4 pl-10 text-gray-500 placeholder-gray-400 searchbar border rounded-lg appearance-none form-input focus:outline-none\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\suppliers.js\",\n                                                    lineNumber: 1861,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\suppliers.js\",\n                                            lineNumber: 1857,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>exportFilteredData(),\n                                            className: \" border text-skin-primary border-skin-primary button rounded-md items-center !py-1\",\n                                            disabled: exportDisabled ? true : false,\n                                            children: \"Export\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\suppliers.js\",\n                                            lineNumber: 1869,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        userData.email == \"<EMAIL>\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: deleteAll,\n                                            className: \"border text-skin-primary border-skin-primary button rounded-md items-center !py-1\",\n                                            children: \"Delete\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\suppliers.js\",\n                                            lineNumber: 1877,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_10___default()), {\n                                                href: \"/supplier/add\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    className: \"button border border-skin-primary bg-skin-primary text-white rounded-md whitespace-nowrap\",\n                                                    children: \"Add Supplier\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\suppliers.js\",\n                                                    lineNumber: 1886,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\suppliers.js\",\n                                                lineNumber: 1885,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\suppliers.js\",\n                                            lineNumber: 1884,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\suppliers.js\",\n                                    lineNumber: 1856,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\suppliers.js\",\n                            lineNumber: 1773,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"my-5\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative ag-theme-alpine !rounded-md\",\n                                style: {\n                                    height: \"calc(100vh - 151px)\"\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ag_grid_react__WEBPACK_IMPORTED_MODULE_4__.AgGridReact, {\n                                        rowData: rowData,\n                                        ref: gridRef,\n                                        columnDefs: columnDefs,\n                                        defaultColDef: defaultColDef,\n                                        suppressRowClickSelection: true,\n                                        rowSelection: \"multiple\",\n                                        pagination: true,\n                                        paginationPageSize: pageSize,\n                                        onPageSizeChanged: handlePageSizeChange,\n                                        tooltipShowDelay: 0,\n                                        tooltipHideDelay: 1000,\n                                        onGridReady: handleGridReady,\n                                        onRowSelected: handleCheckboxEvent,\n                                        gridOptions: gridOptions\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\suppliers.js\",\n                                        lineNumber: 1898,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-start mt-2 pagination-style\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            htmlFor: \"page-size-select pagination\",\n                                            className: \"inputs\",\n                                            children: [\n                                                \"Show\",\n                                                \" \",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                    id: \"page-size-select\",\n                                                    onChange: handlePageSizeChange,\n                                                    value: pageSize,\n                                                    className: \"focus:outline-none\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: 10,\n                                                            children: \"10\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\suppliers.js\",\n                                                            lineNumber: 1923,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: 15,\n                                                            children: \"15\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\suppliers.js\",\n                                                            lineNumber: 1924,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: 25,\n                                                            children: \"25\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\suppliers.js\",\n                                                            lineNumber: 1925,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: 50,\n                                                            children: \"50\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\suppliers.js\",\n                                                            lineNumber: 1926,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: 100,\n                                                            children: \"100\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\suppliers.js\",\n                                                            lineNumber: 1927,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\suppliers.js\",\n                                                    lineNumber: 1917,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                \" \",\n                                                \"Entries\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\suppliers.js\",\n                                            lineNumber: 1915,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\suppliers.js\",\n                                        lineNumber: 1914,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\suppliers.js\",\n                                lineNumber: 1894,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\suppliers.js\",\n                            lineNumber: 1893,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\suppliers.js\",\n                    lineNumber: 1772,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\suppliers.js\",\n                lineNumber: 1771,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Dialog_Transition_headlessui_react__WEBPACK_IMPORTED_MODULE_25__.Transition, {\n                appear: true,\n                show: isOpenOption,\n                as: react__WEBPACK_IMPORTED_MODULE_2__.Fragment,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Dialog_Transition_headlessui_react__WEBPACK_IMPORTED_MODULE_25__.Dialog, {\n                    as: \"div\",\n                    className: \"relative z-10\",\n                    onClose: setIsOpenOption,\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Dialog_Transition_headlessui_react__WEBPACK_IMPORTED_MODULE_25__.Transition.Child, {\n                            as: react__WEBPACK_IMPORTED_MODULE_2__.Fragment,\n                            enter: \"ease-out duration-300\",\n                            enterFrom: \"opacity-0\",\n                            enterTo: \"opacity-100\",\n                            leave: \"ease-in duration-200\",\n                            leaveFrom: \"opacity-100\",\n                            leaveTo: \"opacity-0\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"fixed inset-0 bg-white bg-opacity-25 backdrop-blur-sm\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\suppliers.js\",\n                                lineNumber: 1947,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\suppliers.js\",\n                            lineNumber: 1938,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"fixed inset-0 overflow-y-auto\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-center min-h-full p-4 text-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Dialog_Transition_headlessui_react__WEBPACK_IMPORTED_MODULE_25__.Transition.Child, {\n                                    as: react__WEBPACK_IMPORTED_MODULE_2__.Fragment,\n                                    enter: \"ease-out duration-300\",\n                                    enterFrom: \"opacity-0 scale-95\",\n                                    enterTo: \"opacity-100 scale-100\",\n                                    leave: \"ease-in duration-200\",\n                                    leaveFrom: \"opacity-100 scale-100\",\n                                    leaveTo: \"opacity-0 scale-95\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Dialog_Transition_headlessui_react__WEBPACK_IMPORTED_MODULE_25__.Dialog.Panel, {\n                                        className: \"transform overflow-hidden rounded-xl bg-white  text-left align-middle shadow-xl transition-all\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-white w-[500px]\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex w-full bg-skin-primary h-[40px] items-center justify-between\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                                className: \"text-white items-center font-poppinsemibold pl-4 text-[20px]\",\n                                                                children: \"Select the export type\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\suppliers.js\",\n                                                                lineNumber: 1964,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fortawesome_react_fontawesome__WEBPACK_IMPORTED_MODULE_3__.FontAwesomeIcon, {\n                                                                icon: _fortawesome_free_solid_svg_icons__WEBPACK_IMPORTED_MODULE_24__.faClose,\n                                                                className: \"pr-4 text-white cursor-pointer\",\n                                                                onClick: closeOptionModal\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\suppliers.js\",\n                                                                lineNumber: 1967,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\suppliers.js\",\n                                                        lineNumber: 1963,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex justify-around items-center px-5\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center pl-0 pt-5\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                        name: \"exportType\",\n                                                                        id: \"internalExport\",\n                                                                        type: \"radio\",\n                                                                        className: \"mr-4\",\n                                                                        value: \"internalExport\",\n                                                                        checked: selectedExportType === \"internalExport\",\n                                                                        onChange: handleExportType\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\suppliers.js\",\n                                                                        lineNumber: 1975,\n                                                                        columnNumber: 25\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        className: \"font-poppinsregular text-[16px] text-charcoal-gray\",\n                                                                        htmlFor: \"internalExport\",\n                                                                        children: \"Internal\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\suppliers.js\",\n                                                                        lineNumber: 1984,\n                                                                        columnNumber: 25\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\suppliers.js\",\n                                                                lineNumber: 1974,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center pl-4 pt-5\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                        type: \"radio\",\n                                                                        name: \"exportType\",\n                                                                        id: \"ISS\",\n                                                                        className: \"mr-4\",\n                                                                        value: \"ISS\",\n                                                                        checked: selectedExportType === \"ISS\",\n                                                                        onChange: handleExportType\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\suppliers.js\",\n                                                                        lineNumber: 1992,\n                                                                        columnNumber: 25\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        htmlFor: \"ISS\",\n                                                                        className: \"font-poppinsregular text-[16px] text-charcoal-gray\",\n                                                                        children: \"ISS\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\suppliers.js\",\n                                                                        lineNumber: 2001,\n                                                                        columnNumber: 25\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\suppliers.js\",\n                                                                lineNumber: 1991,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\suppliers.js\",\n                                                        lineNumber: 1973,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\suppliers.js\",\n                                                lineNumber: 1962,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-center pb-4 pr-4 mt-2\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: exportFilteredData,\n                                                    disabled: !selectedExportType,\n                                                    className: \"font-circularstdbook rounded-md w-[100px] p-1 leading-5 mt-1 py-2 text-center hover:opacity-80 bg-skin-primary text-white\",\n                                                    children: \"Select\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\suppliers.js\",\n                                                    lineNumber: 2011,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\suppliers.js\",\n                                                lineNumber: 2010,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\suppliers.js\",\n                                        lineNumber: 1961,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\suppliers.js\",\n                                    lineNumber: 1952,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\suppliers.js\",\n                                lineNumber: 1951,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\suppliers.js\",\n                            lineNumber: 1950,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\suppliers.js\",\n                    lineNumber: 1937,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\suppliers.js\",\n                lineNumber: 1936,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Dialog_Transition_headlessui_react__WEBPACK_IMPORTED_MODULE_25__.Transition, {\n                appear: true,\n                show: emailStatusPopup,\n                as: react__WEBPACK_IMPORTED_MODULE_2__.Fragment,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Dialog_Transition_headlessui_react__WEBPACK_IMPORTED_MODULE_25__.Dialog, {\n                    as: \"div\",\n                    className: \"relative z-10\",\n                    onClose: closeEmailPopup,\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Dialog_Transition_headlessui_react__WEBPACK_IMPORTED_MODULE_25__.Transition.Child, {\n                            as: react__WEBPACK_IMPORTED_MODULE_2__.Fragment,\n                            enter: \"ease-out duration-300\",\n                            enterFrom: \"opacity-0\",\n                            enterTo: \"opacity-100\",\n                            leave: \"ease-in duration-200\",\n                            leaveFrom: \"opacity-100\",\n                            leaveTo: \"opacity-0\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"fixed inset-0 bg-black bg-opacity-25 backdrop-blur-sm\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\suppliers.js\",\n                                lineNumber: 2036,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\suppliers.js\",\n                            lineNumber: 2027,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"fixed inset-0 overflow-y-auto\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-center min-h-full p-4 text-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Dialog_Transition_headlessui_react__WEBPACK_IMPORTED_MODULE_25__.Transition.Child, {\n                                    as: react__WEBPACK_IMPORTED_MODULE_2__.Fragment,\n                                    enter: \"ease-out duration-300\",\n                                    enterFrom: \"opacity-0 scale-95\",\n                                    enterTo: \"opacity-100 scale-100\",\n                                    leave: \"ease-in duration-200\",\n                                    leaveFrom: \"opacity-100 scale-100\",\n                                    leaveTo: \"opacity-0 scale-95\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Dialog_Transition_headlessui_react__WEBPACK_IMPORTED_MODULE_25__.Dialog.Panel, {\n                                        className: \" w-[45%] transform overflow-hidden rounded-xl bg-white  text-left align-middle shadow-xl transition-all\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative bg-white rounded-lg shadow\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-start justify-between p-8 rounded-t\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"flex flex-row text-xl font-semibold text-gray-900  items-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"flex justify-center items-center border border-skin-primary text-skin-primary w-[25px] h-[25px] text-center leading-5 rounded-full text-base me-4\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fortawesome_react_fontawesome__WEBPACK_IMPORTED_MODULE_3__.FontAwesomeIcon, {\n                                                                            icon: _fortawesome_free_solid_svg_icons__WEBPACK_IMPORTED_MODULE_24__.faInfo\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\suppliers.js\",\n                                                                            lineNumber: 2057,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        \" \"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\suppliers.js\",\n                                                                    lineNumber: 2056,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                \" \",\n                                                                \"Status Message\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\suppliers.js\",\n                                                            lineNumber: 2055,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: closeEmailPopup,\n                                                            type: \"button\",\n                                                            className: \"text-black bg-transparent hover:bg-gray-200 hover:text-gray-900 rounded-lg text-xl w-8 h-8 ml-auto inline-flex justify-center items-center \",\n                                                            \"data-modal-hide\": \"default-modal\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fortawesome_react_fontawesome__WEBPACK_IMPORTED_MODULE_3__.FontAwesomeIcon, {\n                                                                    icon: _fortawesome_free_solid_svg_icons__WEBPACK_IMPORTED_MODULE_24__.faXmark,\n                                                                    className: \"text-skin-primary\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\suppliers.js\",\n                                                                    lineNumber: 2067,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                \" \"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\suppliers.js\",\n                                                            lineNumber: 2061,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\suppliers.js\",\n                                                    lineNumber: 2054,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"p-8 py-0 space-y-6\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-base xl:text-md 2xl:text-lg leading-relaxed mt-2\",\n                                                        children: popupMessage\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\suppliers.js\",\n                                                        lineNumber: 2075,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\suppliers.js\",\n                                                    lineNumber: 2074,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-end p-6 space-x-2 justify-end\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: closeEmailPopup,\n                                                        \"data-modal-hide\": \"default-modal\",\n                                                        type: \"button\",\n                                                        className: \"border text-dark-gray focus:ring-4 focus:outline-none focus:ring-blue-300 font-medium rounded-md text-md px-6 py-2 text-center \",\n                                                        children: \"Ok\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\suppliers.js\",\n                                                        lineNumber: 2081,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\suppliers.js\",\n                                                    lineNumber: 2080,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\suppliers.js\",\n                                            lineNumber: 2052,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\suppliers.js\",\n                                        lineNumber: 2050,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\suppliers.js\",\n                                    lineNumber: 2041,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\suppliers.js\",\n                                lineNumber: 2040,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\suppliers.js\",\n                            lineNumber: 2039,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\suppliers.js\",\n                    lineNumber: 2026,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\suppliers.js\",\n                lineNumber: 2025,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true);\n};\n_s(suppliers, \"H9gtB91Zay9/ScaLTy1kYxLBRMI=\", false, function() {\n    return [\n        next_router__WEBPACK_IMPORTED_MODULE_11__.useRouter,\n        _utils_rolePermissionsContext__WEBPACK_IMPORTED_MODULE_21__.usePermissions,\n        _utils_loaders_loadingContext__WEBPACK_IMPORTED_MODULE_17__.useLoading,\n        _azure_msal_react__WEBPACK_IMPORTED_MODULE_19__.useMsal,\n        _utils_userContext__WEBPACK_IMPORTED_MODULE_20__.useUser\n    ];\n});\nvar __N_SSP = true;\n/* harmony default export */ __webpack_exports__[\"default\"] = (suppliers);\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevExports = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevExports) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports on update so we can compare the boundary\n                // signatures.\n                module.hot.dispose(function (data) {\n                    data.prevExports = currentExports;\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevExports !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevExports, currentExports)) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevExports !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./pages/suppliers.js\n"));

/***/ })

});