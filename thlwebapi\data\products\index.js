"use strict";

const utils = require("../utils");
const config = require("../../config");
const sql = require("mssql");
const logger = require("../../utils/logger");
const { forEach } = require("lodash");
const { sendEmail } = require("../../utils/email");

const createRawMaterials = async (productsData) => {
  try {
    let pool = await sql.connect(config.sql);

    const sqlQueries = await utils.loadSqlQueries("products");
    const result = await pool
      .request()
      .input("company_name", sql.VarChar, productsData.companyId)
      .input("type", sql.Int, 1)
      .input(
        "brand",
        sql.Int,
        productsData.brand !== "" && productsData.brand[0] !== null
          ? parseInt(productsData.brand[0].value)
          : null
      )
      .input(
        "product_type",
        sql.Int,
        productsData.product_type.length > 0 &&
          productsData.product_type[0] !== null
          ? productsData.product_type?.[0].value
          : null
      )
      .input(
        "reason",
        sql.Int,
        productsData.reasonForRequest !== "" &&
          productsData.reasonForRequest[0] !== null
          ? productsData.reasonForRequest[0].value
          : null
      )
      .input("originator", sql.VarChar, productsData.nameOfOriginator)
      .input("originator_email", sql.VarChar, productsData.useremail)
      .input(
        "product_description",
        sql.VarChar,
        productsData.productDescription !== "" &&
          productsData.productDescription[0] !== null
          ? productsData.productDescription
          : null
      )
      .input(
        "suppliers_description",
        sql.VarChar,
        productsData.supplierDescription !== "" &&
          productsData.supplierDescription[0] !== null
          ? productsData.supplierDescription
          : null
      )
      .input(
        "product_code",
        sql.Int,
        productsData.masterProductCode !== "" &&
          productsData.masterProductCode[0] !== null
          ? parseInt(productsData.masterProductCode[0].value)
          : null
      )
      .input(
        "group_id",
        sql.Int,
        productsData.productGroup.length > 0 &&
          productsData.productGroup[0] !== null
          ? productsData.productGroup[0].value
          : null
      )
      .input(
        "mark_variaty",
        sql.Int,
        productsData.markVariety.length > 0 &&
          productsData.markVariety[0] !== null
          ? productsData.markVariety[0].value
          : null
      )
      .input(
        "count_or_size",
        sql.VarChar,
        productsData.countSize !== "" && productsData.countSize[0] !== null
          ? productsData.countSize
          : null
      )
      .input(
        "units_in_outer",
        sql.VarChar,
        productsData.unitsInOuter !== "" &&
          productsData.unitsInOuter[0] !== null
          ? productsData.unitsInOuter
          : null
      )
      .input(
        "cases_per_pallet",
        sql.VarChar,
        productsData.casesPerPallet !== "" &&
          productsData.casesPerPallet[0] !== null
          ? productsData.casesPerPallet
          : null
      )

      .input(
        "outer_net_weight",
        sql.VarChar,
        productsData.netWeightCase !== "" &&
          productsData.netWeightCase[0] !== null
          ? productsData.netWeightCase
          : null
      )
      .input(
        "outer_gross_weight",
        sql.VarChar,
        productsData.grossWeightCase !== "" &&
          productsData.grossWeightCase[0] !== null
          ? productsData.grossWeightCase
          : null
      )
      .input(
        "sub_product_code",
        sql.VarChar,
        productsData.subProductCode !== "" &&
          productsData.subProductCode[0] !== null
          ? productsData.subProductCode
          : null
      )
      .input(
        "temperature_grade",
        sql.Int,
        productsData.temperatureGrade !== "" &&
          productsData.temperatureGrade[0] !== null
          ? parseInt(productsData.temperatureGrade[0].value)
          : null
      )
      .input(
        "class_required",
        sql.VarChar,
        productsData.classRequired !== "" &&
          productsData.classRequired[0] !== null
          ? productsData.classRequired
          : null
      )
      .input(
        "intrastat_commodity_code",
        sql.Int,
        productsData.intrastCommodityCode !== "" &&
          productsData.intrastCommodityCode[0] !== null
          ? parseInt(productsData.intrastCommodityCode[0].value)
          : null
      )
      .input(
        "organic_certificate",
        sql.VarChar,
        productsData.organicCertification &&
          productsData.intrastCommodityCode[0] !== null
          ? productsData.organicCertification[0].value
          : null
      )
      .input(
        "is_classified_allergic_fsa14",
        sql.VarChar,
        productsData.classifiedAllergicTypes !== "" &&
          productsData.classifiedAllergicTypes[0] !== null
          ? productsData.classifiedAllergicTypes[0].value
          : null
      )
      .input(
        "coo",
        sql.Int,
        productsData.countryOfOrigin && productsData.countryOfOrigin[0] !== null
          ? parseInt(productsData.countryOfOrigin[0].value)
          : null
      )
      .input(
        "caliber_size",
        sql.Int,
        productsData.calibreSize && productsData.calibreSize[0] !== null
          ? parseInt(productsData.calibreSize[0].value)
          : null
      )
      .input(
        "delivery_date",
        sql.VarChar,
        productsData.expectedDeliveryDate &&
          productsData.expectedDeliveryDate[0] !== null
          ? productsData.expectedDeliveryDate
          : null
      )
      .input(
        "end_customer",
        sql.Int,
        productsData.endCustomer && productsData.endCustomer[0] !== null
          ? parseInt(productsData.endCustomer[0].value)
          : null
      )
      .input(
        "variety",
        sql.Int,
        productsData.variety && productsData.variety[0] !== null
          ? parseInt(productsData.variety[0].value)
          : null
      )
      .input("status", sql.Int, productsData.status)
      .input("requestor", sql.Int, productsData.requestor)
      .input("request_no", sql.VarChar, productsData.requestNumber)
      .input("submitted_to_iss", sql.Bit, productsData.isSubmitted)
      .input(
        "email_comment",
        sql.VarChar,
        productsData.emailComment !== "" &&
          productsData.emailComment[0] !== null
          ? productsData.emailComment
          : null
      )
      .query(sqlQueries.createRawMaterials);

    // if (productsData.status !== 8) {
    //   let recipientEmails = "<EMAIL>";

    //   const formType = "Raw material request";
    //   const paragraph = `<p>User ${
    //     productsData?.useremail
    //   } created a new ${formType} with request number ${
    //     productsData.requestNumber
    //   } ${
    //     productsData.isSubmitted ? "and submitted the request to ISS." : ""
    //   }</p>`;
    //   let placeholders = {
    //     paragraph: paragraph || "Products",
    //   };
    //   const companyKey = "flrs";
    //   let emailType = "newRawMaterialRequest";
    //   const emailResult = await sendEmail({
    //     placeholders,
    //     emailType,
    //     recipientEmails,
    //     companyKey,
    //     comment,
    //   });
    // }
    // console.log("productsData",productsData);
    function checkIfDataExists(inputFeildData, nestedLabelFeildExists = false) {
      // Check if the array is not empty and the first element is not null
      // console.log("inputFeildData[0]['label']",`${inputFeildData}`,nestedLabelFeildExists? inputFeildData[0]['label'] : inputFeildData);
      if (
        nestedLabelFeildExists &&
        inputFeildData &&
        inputFeildData.length > 0 &&
        inputFeildData[0] !== null
      ) {
        return String(inputFeildData[0]["label"]);
      } else if (
        inputFeildData &&
        inputFeildData.length > 0 &&
        inputFeildData !== null
      ) {
        return inputFeildData;
      } else {
        return false;
      }
    }

    // Example usage
    // const masterProductCodeLabel = getLabelValue(productsData.masterProductCode, 'label');
    // console.log(masterProductCodeLabel);
    const productType = checkIfDataExists(productsData.product_type, true);
    const reasonForRequest = checkIfDataExists(
      productsData.reasonForRequest,
      true
    );
    const expectedDeliveryDate = checkIfDataExists(
      productsData.expectedDeliveryDate
    );
    const productDescription = checkIfDataExists(
      productsData.productDescription
    );
    const supplierDescription = checkIfDataExists(
      productsData.supplierDescription
    );
    const masterProductCode = checkIfDataExists(
      productsData.masterProductCode,
      true
    );
    const productGroup = checkIfDataExists(productsData.productGroup, true);
    const countSize = checkIfDataExists(productsData.countSize);
    const unitsInOuter = checkIfDataExists(productsData.unitsInOuter);
    const casesPerPallet = checkIfDataExists(productsData.casesPerPallet);
    const netWeightCase = checkIfDataExists(productsData.netWeightCase);
    const grossWeightCase = checkIfDataExists(productsData.grossWeightCase);
    const markVariety = checkIfDataExists(productsData.markVariety, true);
    const subProductCode = checkIfDataExists(productsData.subProductCode);
    const temperatureGrade = checkIfDataExists(
      productsData.temperatureGrade,
      true
    );
    const classRequired = checkIfDataExists(productsData.classRequired);
    const intrastCommodityCode = checkIfDataExists(
      productsData.intrastCommodityCode,
      true
    );
    const organicCertification = checkIfDataExists(
      productsData.organicCertification,
      true
    );
    const classifiedAllergicTypes = checkIfDataExists(
      productsData.classifiedAllergicTypes,
      true
    );
    const brand = checkIfDataExists(productsData.brand, true);
    const countryOfOrigin = checkIfDataExists(
      productsData.countryOfOrigin,
      true
    );
    const calibreSize = checkIfDataExists(productsData.calibreSize, true);
    const endCustomer = checkIfDataExists(productsData.endCustomer, true);
    const variety = checkIfDataExists(productsData.variety, true);

    logger.info({
      username: productsData.username,
      type: "success",
      description: `User with email ${productsData.useremail} created ${
        productsData.status === 8 ? `and submitted` : ""
      } new raw material with request number ${result.recordset[0].request_no}
      with details: 

      ${productType ? `product type: ${productType}` : ""}
      ${reasonForRequest ? `, reason for request: ${reasonForRequest}` : ""}
      ${
        expectedDeliveryDate
          ? `, expected first delivery date: ${expectedDeliveryDate}`
          : ""
      }
      ${
        productDescription
          ? `, product information / description: ${productDescription}`
          : ""
      }
      ${
        supplierDescription
          ? `, supplier description: ${supplierDescription}`
          : ""
      }
      ${masterProductCode ? `, master product code: ${masterProductCode}` : ""}
      ${productGroup ? `, product groups: ${productGroup}` : ""}
      ${countSize ? `, count or size: ${countSize}` : ""}
      ${unitsInOuter ? `, units in outer: ${unitsInOuter}` : ""}
      ${casesPerPallet ? `, cases per pallet: ${casesPerPallet}` : ""}
      ${netWeightCase ? `, net weight of case: ${netWeightCase}` : ""}
      ${grossWeightCase ? `, gross weight of case: ${grossWeightCase}` : ""}
      ${markVariety ? `, product mark: ${markVariety}` : ""}
      ${subProductCode ? `, sub product code: ${subProductCode}` : ""}
      ${temperatureGrade ? `, temperature grade: ${temperatureGrade}` : ""}
      ${classRequired ? `, class required: ${classRequired}` : ""}
      ${
        intrastCommodityCode
          ? `, intrastat commodity code: ${intrastCommodityCode}`
          : ""
      }
      ${
        organicCertification
          ? `, organic certification: ${organicCertification}`
          : ""
      }
      ${
        classifiedAllergicTypes
          ? `, FSA14 allergen type: ${classifiedAllergicTypes}`
          : ""
      }
      ${brand ? `, brand: ${brand}` : ""}
      ${countryOfOrigin ? `, country of origin: ${countryOfOrigin}` : ""}
      ${calibreSize ? `, calibre size: ${calibreSize}` : ""}
      ${endCustomer ? `, end customer: ${endCustomer}` : ""}
      ${variety ? `, variety: ${variety}` : ""}
            
      and name of originator ${productsData.nameOfOriginator}`,
      item_id: result.recordset[0].id,
      module_id: 2,
    });
    return result;
  } catch (error) {
    console.error(error);
    logger.error({
      username: productsData.username,
      type: "error",
      description: `User with email ${productsData.useremail} failed to create raw materials: ${error.message}`,
      item_id: null,
      module_id: 2,
    });
    return error.message;
  }
};

//#region Update Raw Materials and Set Flag
const updateRawMaterials = async (productsData, productId) => {
  try {
    let pool = await sql.connect(config.sql);
    const sqlQueries = await utils.loadSqlQueries("products");
    // console.log("new data", productsData);

    const previousData = await pool
      .request()
      .input("id", sql.Int, productId)
      .query(sqlQueries.getRawMaterialsById);
    // console.log("previousData", previousData);

    const parsedPrevData = previousData.recordset[0];
    // console.log("parsedPrevData", parsedPrevData);

    //prev to left previous to right current
    const fieldMappings = {
      product_type: "product_type",
      mark_variety_name: "markVariety",
      temperature_grade_name: "temperatureGrade",
      variety_name: "variety",
      end_customer_name: "endCustomer",
      product_name: "masterProductCode",
      caliber_size_name: "calibreSize",
      brand_name: "brand",
      intrastat_commodity_code_name: "intrastCommodityCode",
      group_name: "productGroup",
      coo_name: "countryOfOrigin",
      reason_type: "reasonForRequest",
      originator: "nameOfOriginator",
      delivery_date: "expectedDeliveryDate",
      product_description: "productDescription",
      suppliers_description: "supplierDescription",
      count_or_size: "countSize",
      units_in_outer: "unitsInOuter",
      cases_per_pallet: "casesPerPallet",
      outer_net_weight: "netWeightCase",
      outer_gross_weight: "grossWeightCase",
      sub_product_code: "subProductCode",
      class_required: "classRequired",
      organic_certificate: "organicCertification",
      is_classified_allergic_fsa14: "classifiedAllergicTypes",
    };

    function compareData(currentData, previousData) {
      const differences = {};
      // console.log("previousData",previousData);
      // console.log("currentData",currentData);
      const compatiblePreviousData = {
        ...previousData,
        product_type:
          previousData.product_type === 1
            ? "Normal"
            : previousData.product_type === 2
            ? "Packaging"
            : null,
        delivery_date:
          previousData.delivery_date === null
            ? ""
            : new Date(previousData.delivery_date).toISOString().split("T")[0],
        intrastat_commodity_code_name:
          previousData?.intrastat_commodity_code_name &&
          previousData?.intrastat_commodity_code_id
            ? `${previousData.intrastat_commodity_code_name}-${previousData.intrastat_commodity_code_id}`
            : "",
      };
      // console.log("compatiblePreviousData",compatiblePreviousData);

      Object.entries(fieldMappings)?.forEach(([fieldName, key]) => {
        const previousValue = compatiblePreviousData[fieldName];
        const currentValue = Array.isArray(currentData[key])
          ? currentData[key][0]?.label
          : currentData[key];

        // Compare values if both fields exist
        // if (previousValue != undefined && currentValue != undefined) {
        if (currentValue && currentValue != undefined && currentValue != "") {
          if (previousValue !== currentValue) {
            // Compare non-array values
            differences[fieldName] = {
              previousValue,
              newValue: currentValue,
            };
          }
        } else if (
          previousValue &&
          previousValue != undefined &&
          previousValue != ""
        ) {
          if (previousValue !== currentValue) {
            // Compare non-array values
            differences[fieldName] = {
              previousValue,
              newValue: null,
            };
          }
        }
      });

      return differences;
    }

    const differences = compareData(productsData, parsedPrevData);

    const result = await pool
      .request()
      .input("id", sql.Int, productId)
      .input(
        "brand",
        sql.Int,
        productsData.brand !== "" && productsData.brand[0] !== null
          ? parseInt(productsData.brand[0].value)
          : null
      )
      .input(
        "product_type",
        sql.Int,
        productsData.product_type.length > 0 &&
          productsData.product_type[0] !== null
          ? productsData.product_type?.[0].value
          : null
      )
      .input(
        "reason",
        sql.Int,
        productsData.reasonForRequest !== "" &&
          productsData.reasonForRequest[0] !== null
          ? productsData.reasonForRequest[0].value
          : null
      )
      .input("originator", sql.VarChar, productsData.nameOfOriginator)
      .input(
        "product_description",
        sql.VarChar,
        productsData.productDescription !== "" &&
          productsData.productDescription !== null
          ? productsData.productDescription
          : null
      )
      .input(
        "suppliers_description",
        sql.VarChar,
        productsData.supplierDescription !== "" &&
          productsData.supplierDescription !== null
          ? productsData.supplierDescription
          : null
      )
      .input(
        "product_code",
        sql.Int,
        productsData.masterProductCode !== "" &&
          productsData.masterProductCode[0] !== null
          ? parseInt(productsData.masterProductCode[0].value)
          : null
      )
      .input(
        "group_id",
        sql.Int,
        productsData.productGroup !== "" &&
          productsData.productGroup[0] !== null
          ? parseInt(productsData.productGroup[0]?.value)
          : null
      )
      .input(
        "mark_variaty",
        sql.Int,
        productsData.markVariety !== "" && productsData.markVariety[0] !== null
          ? productsData.markVariety[0]?.value
          : null
      )
      .input(
        "count_or_size",
        sql.VarChar,
        productsData.countSize !== "" && productsData.countSize !== null
          ? productsData.countSize
          : null
      )
      .input(
        "units_in_outer",
        sql.VarChar,
        productsData.unitsInOuter !== "" && productsData.unitsInOuter !== null
          ? productsData.unitsInOuter
          : null
      )
      .input(
        "cases_per_pallet",
        sql.VarChar,
        productsData.casesPerPallet !== "" &&
          productsData.casesPerPallet !== null
          ? productsData.casesPerPallet
          : null
      )

      .input(
        "outer_net_weight",
        sql.VarChar,
        productsData.netWeightCase !== "" && productsData.netWeightCase !== null
          ? productsData.netWeightCase
          : null
      )
      .input(
        "outer_gross_weight",
        sql.VarChar,
        productsData.grossWeightCase !== "" &&
          productsData.grossWeightCase !== null
          ? productsData.grossWeightCase
          : null
      )
      .input(
        "sub_product_code",
        sql.VarChar,
        productsData.subProductCode !== "" &&
          productsData.subProductCode !== null
          ? productsData.subProductCode
          : null
      )
      .input(
        "delivery_date",
        sql.VarChar,
        productsData.expectedDeliveryDate &&
          productsData.expectedDeliveryDate[0] !== null
          ? productsData.expectedDeliveryDate
          : null
      )
      .input(
        "temperature_grade",
        sql.Int,
        productsData.temperatureGrade !== "" &&
          productsData.temperatureGrade[0] !== null
          ? parseInt(productsData.temperatureGrade[0].value)
          : null
      )
      .input("class_required", sql.VarChar, productsData.classRequired)
      .input(
        "intrastat_commodity_code",
        sql.Int,
        productsData.intrastCommodityCode !== "" &&
          productsData.intrastCommodityCode[0] !== null
          ? parseInt(productsData.intrastCommodityCode[0].value)
          : null
      )
      .input(
        "organic_certificate",
        sql.VarChar,
        productsData.organicCertification.length !== 0 &&
          productsData.intrastCommodityCode[0] !== null
          ? productsData.organicCertification[0].value
          : null
      )
      .input(
        "is_classified_allergic_fsa14",
        sql.VarChar,
        productsData.classifiedAllergicTypes !== "" &&
          productsData.classifiedAllergicTypes[0] !== null
          ? productsData.classifiedAllergicTypes[0].value
          : null
      )
      .input(
        "coo",
        sql.Int,
        productsData.countryOfOrigin && productsData.countryOfOrigin[0] !== null
          ? parseInt(productsData.countryOfOrigin[0].value)
          : null
      )
      .input(
        "caliber_size",
        sql.Int,
        productsData.calibreSize && productsData.calibreSize[0] !== null
          ? parseInt(productsData.calibreSize[0].value)
          : null
      )
      .input(
        "end_customer",
        sql.Int,
        productsData.endCustomer && productsData.endCustomer[0] !== null
          ? parseInt(productsData.endCustomer[0].value)
          : null
      )
      // .input(
      //   "sort_group",
      //   sql.Int,
      //   productsData.sortGroupCode ? productsData.sortGroupCode
      //     : null
      // )
      .input(
        "variety",
        sql.Int,
        productsData.variety && productsData.variety[0] !== null
          ? parseInt(productsData.variety[0].value)
          : null
      )
      .input("submitted_to_iss", sql.Bit, productsData.isSubmitted)
      .input("status", sql.Int, productsData.status)
      .input(
        "email_comment",
        sql.VarChar,
        productsData?.emailComment !== "" && productsData?.emailComment !== null
          ? productsData.emailComment
          : null
      )
      .query(sqlQueries.updateRawMaterials);

    if (productsData.status === 8) {
      await pool
        .request()
        .input("requestNumber", sql.VarChar, productsData.requestNumber)
        .query(sqlQueries.updateIsActiveFlagToFalse);
    }
    console.log("is submitted", productsData.isSubmitted);

    function formatDifferences(differences) {
      let formattedString = "";
      for (const key in differences) {
        const { previousValue, newValue } = differences[key];
        let formattedNewValue = newValue === null ? "empty" : newValue;

        // Check if the previousValue is undefined, null, or an empty string
        if (
          (Array.isArray(previousValue) && previousValue.length === 0) ||
          previousValue === undefined ||
          previousValue === null ||
          previousValue === ""
        ) {
          formattedString += `${key}: Added with value=${formattedNewValue},\n`;
        } else if (formattedNewValue === "empty") {
          formattedString += `${key}: resetted value=${previousValue} to empty\n`;
        } else {
          // Handle special formatting for 'prophets' field
          formattedString += `${key}: previousValue=${previousValue}, newValue=${formattedNewValue},\n`;
        }
      }
      return formattedString;
    }

    const formattedDifferences = formatDifferences(differences);

    if (formattedDifferences != "") {
      logger.info({
        username: productsData.username,
        type: "success",
        description: `User with email ${productsData.useremail} updated ${
          productsData.status === 8 ? `and submitted` : ""
        } raw material with request number ${
          productsData.request_no
        } with changes:\n${formattedDifferences}`,
        item_id: productId,
        module_id: 2,
      });
    }
    return result;
  } catch (error) {
    console.error(error);
    logger.error({
      username: productsData.username,
      type: "error",
      description: `User with email ${productsData.useremail} failed to update raw materials: ${error.message}`,
      item_id: null,
      module_id: 2,
    });
    return error.message;
  }
};
//#endregion

// #region +SubProdCode

const createNewSubProductCode = async (productsData, productId = null) => {
  try {
    let pool = await sql.connect(config.sql);
    const sqlQueries = await utils.loadSqlQueries("products");
    const result = await pool
      .request()
      .input("code", sql.VarChar, productsData.newSubProdCode)
      .input(
        "master_product_code",
        sql.VarChar,
        String(productsData.masterProductCode[0]?.code || productsData[0]?.master_product_code_code)
      )
      .query(sqlQueries.createNewSubProductCode);

    logger.info({
      username: productsData.username ?? productsData.actionedByName ,
      type: "success",
      description: `User with email ${productsData.useremail ?? productsData.
        actionedByEmail} created new sub product code  in request no ${productsData.request_no}, and name of originator ${productsData.nameOfOriginator}`,
      item_id: productId ?? null ,
      module_id: 2,
    });
    return result;
  } catch (error) {
    logger.error({
      username: productsData.username,
      type: "error",
      description: `User with email ${productsData.useremail} failed to create sub product code: ${error.message}`,
      item_id: null,
      module_id: 2,
    });
    console.error(error);
    return error.message;
  }
};

const getCreatedNewSubProductCode = async (productsData,requestNumber = null) => {
  try {let pool = await sql.connect(config.sql);
  const sqlQueries = await utils.loadSqlQueries("products");
  const result = await pool
  .request()
  .input("code", sql.VarChar, productsData.newSubProdCode)
  .input("code", sql.VarChar, productsData[0]?.master_product_code_code)
  .query(sqlQueries.getNewCreatedSubproductcode);}
    catch (error) {
      logger.error({
        username: productsData.username,
        type: "error",
        description: `User with email ${productsData.useremail} failed to create sub product code: ${error.message}`,
        item_id: null,
        module_id: 2,
      });
      console.error(error);
      return error.message;
    }
}
// #endregion

const createFinishedProducts = async (productsData) => {
  try {
    let pool = await sql.connect(config.sql);
    const sqlQueries = await utils.loadSqlQueries("products");
    const result = await pool
      .request()
      .input("reason", sql.Int, productsData.reasonForRequest[0].value)
      .input("delivery_date", sql.VarChar, productsData.expectedDeliveryDate)
      .input("originator", sql.VarChar, productsData.nameOfOriginator)
      .input(
        "product_description",
        sql.VarChar,
        productsData.productDescription
      )
      .input(
        "customer_description",
        sql.VarChar,
        productsData.customerDescription
      )
      .input("product_code", sql.Int, productsData.masterProductCode[0].value)
      .input("group_id", sql.Int, productsData.productGroup?.[0].value)
      .input("mark_variaty", sql.Int, productsData.markVariety[0].value)
      .input("cases_per_pallet", sql.VarChar, productsData.casesPerPallet)
      .input("units_in_outer", sql.VarChar, productsData.unitsInOuter)
      .input("finished_pack_size", sql.VarChar, productsData.finishedPackSize)
      .input("outer_gross_weight", sql.VarChar, productsData.grossWeightCase)
      .input("outer_net_weight", sql.VarChar, productsData.netWeightCase)
      .input("sub_product_code", sql.VarChar, productsData.subProductCode)
      .input("brand", sql.Int, productsData.brand[0].value)
      .input(
        "temperature_grade",
        sql.Int,
        productsData.temperatureGrade[0].value
      )
      .input("class_required", sql.VarChar, productsData.classRequired)
      .input(
        "is_classified_allergic_fsa14",
        sql.VarChar,
        productsData.classifiedAllergenic[0].value
      )
      .input(
        "organic_certificate",
        sql.VarChar,
        productsData.organicCertificationNumber
      )
      .input("box_type_other_comments", sql.VarChar, productsData.boxOuterType)
      .input("is_box_type_colours", sql.Int, productsData.newOuterType[0].value)
      .input("packaging_types", sql.VarChar, productsData.packagingTypes)
      .input("description_net_film", sql.VarChar, productsData.description)
      .input("punnet_tray_type", sql.VarChar, productsData.punnetTrayType)
      .input("machine_format", sql.Int, productsData.machineFormat[0].value)
      .input("pack_label_type", sql.VarChar, productsData.packLabelType)
      .input(
        "end_label_description",
        sql.VarChar,
        productsData.endLabelDescription
      )
      .input("end_customer", sql.Int, productsData.endCustomer[0].value)
      .input(
        "promotional_label_desc",
        sql.VarChar,
        productsData.promotionalLabelDescription
      )
      .input("label_scan_grade", sql.VarChar, productsData.labelScanGrade)
      .input("occ_box_end", sql.VarChar, productsData.occBoxEnd)
      .input("ean_pack_label", sql.VarChar, productsData.eanPackLabel)
      .input("tpnd_trading_unit", sql.VarChar, productsData.tpndTradingUnitNum)
      .input("tpnb_base_unit", sql.VarChar, productsData.tpnbBaesUnitNum)
      .input("display_until", sql.VarChar, productsData.displayUntil)
      .input("best_before", sql.VarChar, productsData.bestBefore)
      .input(
        "is_other_date_code_type",
        sql.VarChar,
        productsData.otherDateCodeType
      )
      .input("f_code", sql.VarChar, productsData.fCode)
      .input("supplier_site_code", sql.VarChar, productsData.supplierSiteCode)
      .input("type", sql.Int, 2)
      .input("company_name", sql.VarChar, productsData.companyId)
      .input("status", sql.Int, productsData.status)
      .input("requestor", sql.Int, productsData.requestor)
      .input("request_no", sql.Int, productsData.request_no)
      .input("submitted_to_iss", sql.Bit, productsData.isSubmitted)
      .query(sqlQueries.createFinishedProducts);

    logger.info({
      username: productsData.username,
      type: "success",
      description: `User with email ${productsData.useremail} created new raw material with request number ${result.recordset[0].request_no}, product code ${productsData.masterProductCode} and name of originator ${productsData.nameOfOriginator}`,
      itemid: result.recordset[0].id,
      module_id: 2,
    });
    return result;
  } catch (error) {
    logger.error({
      username: productsData.username,
      type: "error",
      description: `User with email ${productsData.useremail} failed to create finished product: ${error.message}`,
      item_id: null,
      module_id: 2,
    });
    console.error(error);
    return error.message;
  }
};

const updateFinishedProducts = async (productsData, id) => {
  try {
    let pool = await sql.connect(config.sql);
    const sqlQueries = await utils.loadSqlQueries("products");

    const previousData = await pool
      .request()
      .input("id", sql.Int, id)
      .query(sqlQueries.getFinishedProductsById);

    const parsedPrevData = previousData.recordset[0];

    const fieldMappings = {
      mark_variety_name: "markVarietyLabel",
      reason: "reasonForRequestLabel",
      product_name: "masterProductCodeLabel",
      temperature_grade_name: "temperatureGradeLabel",
      brand_name: "brandLabel",
      end_customer_name: "endCustomerLabel",
      machine_format_name: "machineFormatLabel",
      delivery_date: "expectedDeliveryDate",
      originator: "nameOfOriginator",
      units_in_outer: "unitsInOuter",
      cases_per_pallet: "casesPerPallet",
      outer_net_weight: "netWeightCase",
      outer_gross_weight: "grossWeightCase",
      sub_product_code: "subProductCode",
      class_required: "classRequired",
      is_classified_allergic_fsa14: "classifiedAllergenic",
      is_other_date_code_type: "otherDateCodeType",
      customer_description: "customerDescription",
      finished_pack_size: "finishedPackSize",
      is_box_type_colours: "newOuterTypeLabel",
      packaging_types: "packagingTypes",
      description_net_film: "description",
      punnet_tray_type: "punnetTrayType",
      pack_label_type: "packLabelType",
      end_label_description: "endLabelDescription",
      promotional_label_desc: "promotionalLabelDescription",
      label_scan_grade: "labelScanGrade",
      occ_box_end: "occBoxEnd",
      ean_pack_label: "eanPackLabel",
      tpnd_trading_unit: "tpndTradingUnitNum",
      tpnb_base_unit: "tpnbBaesUnitNum",
      display_until: "displayUntil",
      best_before: "bestBefore",
      f_code: "fCode",
      supplier_site_code: "supplierSiteCode",
    };

    function compareData(currentData, previousData) {
      const differences = {};

      Object.entries(fieldMappings)?.forEach(([fieldName, key]) => {
        const previousValue = previousData[fieldName];
        const currentValue = currentData[key];

        // Compare values if both fields exist
        if (previousValue != undefined && currentValue != undefined) {
          if (previousValue !== currentValue) {
            // Compare non-array values
            differences[fieldName] = {
              previousValue,
              newValue: currentValue,
            };
          }
        }
      });
      return differences;
    }

    const differences = compareData(productsData, parsedPrevData);

    function formatDifferences(differences) {
      let formattedString = "";
      for (const key in differences) {
        const { previousValue, newValue } = differences[key];
        let formattedNewValue = newValue;

        // Check if the previousValue is undefined, null, or an empty string
        if (
          previousValue === undefined ||
          previousValue === null ||
          previousValue === ""
        ) {
          formattedString += `${key}: Added with value=${formattedNewValue}\n`;
        } else {
          // Handle special formatting for 'prophets' field
          formattedString += `${key}: previousValue=${previousValue}, newValue=${formattedNewValue}\n`;
        }
      }
      return formattedString;
    }

    const formattedDifferences = formatDifferences(differences);

    const result = await pool
      .request()
      .input("id", sql.Int, id)
      .input(
        "reason",
        sql.Int,
        parseInt(productsData.reasonForRequest[0].value)
      )
      .input("delivery_date", sql.VarChar, productsData.expectedDeliveryDate)
      .input("originator", sql.VarChar, productsData.nameOfOriginator)
      .input(
        "product_description",
        sql.VarChar,
        productsData.productDescription
      )
      .input(
        "customer_description",
        sql.VarChar,
        productsData.customerDescription
      )
      .input("product_code", sql.Int, productsData.masterProductCode[0].value)
      .input("group_id", sql.Int, productsData.productGroup?.[0].value)
      .input("mark_variaty", sql.Int, productsData.markVariety[0].value)
      .input("cases_per_pallet", sql.VarChar, productsData.casesPerPallet)
      .input("units_in_outer", sql.VarChar, productsData.unitsInOuter)
      .input("finished_pack_size", sql.VarChar, productsData.finishedPackSize)
      .input("outer_gross_weight", sql.VarChar, productsData.grossWeightCase)
      .input("outer_net_weight", sql.VarChar, productsData.netWeightCase)
      .input("sub_product_code", sql.VarChar, productsData.subProductCode)
      .input("brand", sql.Int, productsData.brand[0].value)
      .input(
        "temperature_grade",
        sql.Int,
        productsData.temperatureGrade[0].value
      )
      .input("class_required", sql.VarChar, productsData.classRequired)
      .input(
        "is_classified_allergic_fsa14",
        sql.VarChar,
        productsData.classifiedAllergenic[0].value
      )
      .input(
        "organic_certificate",
        sql.VarChar,
        productsData.organicCertificationNumber
      )
      .input("box_type_other_comments", sql.VarChar, productsData.boxOuterType)
      .input("is_box_type_colours", sql.Int, productsData.newOuterType[0].value)
      .input("packaging_types", sql.VarChar, productsData.packagingTypes)
      .input("description_net_film", sql.VarChar, productsData.description)
      .input("punnet_tray_type", sql.VarChar, productsData.punnetTrayType)
      .input("machine_format", sql.Int, productsData.machineFormat[0].value)
      .input("pack_label_type", sql.VarChar, productsData.packLabelType)
      .input(
        "end_label_description",
        sql.VarChar,
        productsData.endLabelDescription
      )
      .input("end_customer", sql.Int, productsData.endCustomer[0].value)
      .input(
        "promotional_label_desc",
        sql.VarChar,
        productsData.promotionalLabelDescription
      )
      .input("label_scan_grade", sql.VarChar, productsData.labelScanGrade)
      .input("occ_box_end", sql.VarChar, productsData.occBoxEnd)
      .input("ean_pack_label", sql.VarChar, productsData.eanPackLabel)
      .input("tpnd_trading_unit", sql.VarChar, productsData.tpndTradingUnitNum)
      .input("tpnb_base_unit", sql.VarChar, productsData.tpnbBaesUnitNum)
      .input("display_until", sql.VarChar, productsData.displayUntil)
      .input("best_before", sql.VarChar, productsData.bestBefore)
      .input(
        "is_other_date_code_type",
        sql.VarChar,
        productsData.otherDateCodeType
      )
      .input("f_code", sql.VarChar, productsData.fCode)
      .input("supplier_site_code", sql.VarChar, productsData.supplierSiteCode)
      .input("type", sql.Int, 2)
      .input("company_name", sql.VarChar, productsData.companyId)
      .input("submitted_to_iss", sql.Bit, productsData.isSubmitted)
      .query(sqlQueries.updateFinishedProducts);
    if (formattedDifferences != "") {
      logger.info({
        username: productsData.username,
        type: "success",
        description: `User with email ${productsData.useremail} updated finished product with request number ${productsData.request_no} with changes:\n${formattedDifferences}`,
        item_id: id,
        module_id: 2,
      });
    }
    return result;
  } catch (error) {
    console.error(error);
    logger.error({
      username: productsData.username,
      type: "error",
      description: `User with email ${productsData.useremail} failed to update finished product: ${error.message}`,
      item_id: id,
      module_id: 2,
    });
    return error.message;
  }
};
const addUpdateNewVariety = async (productsData) => {
  console.log("products data", productsData);
  console.log(
    "master product code------------------------",
    productsData.masterProductCode
  );

  try {
    if (
      productsData.actionId != 11 &&
      (!productsData.code ||
        !productsData.description ||
        !productsData.masterProductCode)
    ) {
      throw new Error(
        "Code, Description, and Master Product Code are required fields."
      );
    }

    const pool = await sql.connect(config.sql);
    const sqlQueries = await utils.loadSqlQueries("products");
    const company = productsData.company;
    console.log("doing this now");
    let result;
    const requestNumber = productsData.requestNumber || null;
    // #region new request
    let concatenatedRequestInfo = "";
    if (requestNumber == null) {
      console.log("request nuber", requestNumber);
      console.log("creating new request");
      result = await pool
        .request()
        .input("company", sql.VarChar, company)
        .input("code", sql.VarChar, productsData.code)
        .input("description", sql.VarChar, productsData.description)
        .input("masterProductCode", sql.VarChar, productsData.masterProductCode)
        .input("actionId", sql.Int, productsData.actionId)
        .input("actionedByEmail", sql.VarChar, productsData.actionedByEmail)
        .input("actionedByName", sql.VarChar, productsData.actionedByName)
        .input("comment", sql.VarChar, productsData.comment)
        .input("prophetId", sql.Int, productsData.prophetId)
        .input("isTechinicalTeam", sql.Bit, productsData.isTechinicalTeam)
        .query(sqlQueries.nVarietyCreate);
      console.log("new result", result);

      concatenatedRequestInfo = result.recordset.map(
        (item) => `NV${item.request_no}`
      )[0];
      logger.info({
        username: productsData.actionedByEmail,
        type: "success",
        description: `User with email ${productsData.actionedByEmail} created a new variety with request number ${concatenatedRequestInfo}, product code ${productsData.masterProductCode}, variety code ${productsData.code}, variety description ${productsData.description} with comment ${productsData.comment}.`,
        itemid: concatenatedRequestInfo,
        module_id: 2,
      });
    } else if (productsData.actionId != 11) {
      // #region Update request
      console.log("update query running");
      result = await pool
        .request()
        .input("reqNumber", sql.Int, productsData.requestNumber)
        .input("varietyId", sql.Int, productsData.varietyId)
        .input("company", sql.VarChar, company)
        .input("code", sql.VarChar, productsData.code)
        .input("description", sql.VarChar, productsData.description)
        .input("masterProductCode", sql.VarChar, productsData.masterProductCode)
        .input("actionId", sql.Int, productsData.actionId)
        .input("actionedByEmail", sql.VarChar, productsData.actionedByEmail)
        .input("actionedByName", sql.VarChar, productsData.actionedByName)
        .input("comment", sql.VarChar, productsData.comment)
        .input("isTechinicalTeam", sql.Bit, productsData.isTechinicalTeam)
        .query(sqlQueries.nVarietyUpdate);
      console.log("update result", result);
      concatenatedRequestInfo = productsData.concatanatedRequestNumber;
      if (productsData.actionId == 1) {
        logger.info({
          username: productsData.actionedByEmail,
          type: "success",
          description: `User with email ${productsData.actionedByEmail} created another draft with variety ${productsData.code} with description ${productsData.description} with comment ${productsData.comment}. Request ID: - ${concatenatedRequestInfo}`,
          itemid: concatenatedRequestInfo,
          module_id: 2,
        });
      }
    } else {
      result = await pool
        .request()
        .input("reqNumber", sql.Int, productsData.requestNumber)
        .input("varietyId", sql.Int, productsData.varietyId)
        .input("company", sql.VarChar, null)
        .input("code", sql.VarChar, null)
        .input("description", sql.VarChar, null)
        .input("masterProductCode", sql.VarChar, null)
        .input("actionId", sql.Int, productsData.actionId)
        .input("actionedByEmail", sql.VarChar, productsData.actionedByEmail)
        .input("actionedByName", sql.VarChar, productsData.actionedByName)
        .input("comment", sql.VarChar, null)
        .input("isTechinicalTeam", sql.Bit, null)
        .query(sqlQueries.nVarietyUpdate);
      console.log("update result", result);
      concatenatedRequestInfo = productsData.concatanatedRequestNumber;
      logger.info({
        username: productsData.actionedByEmail,
        type: "success",
        description: `User with email ${productsData.actionedByEmail} confirmed the setup of New variety request with code ${productsData.code} and description ${productsData.description} on prophet. Request ID: - ${concatenatedRequestInfo}`,
        itemid: concatenatedRequestInfo,
        module_id: 2,
      });
    }

    let recipientEmails;
    let ccEmail = null;
    let formType = "New Variety";
    let paragraph;
    let emailType;
    let comment;
    const emailSqlQueries = await utils.loadSqlQueries("suppliers");
    const UsersSqlQueries = await utils.loadSqlQueries("users");
    const user = await pool
      .request()
      .input("email", sql.VarChar, productsData.actionedByEmail)
      .query(UsersSqlQueries.userByEmail);
    console.log("user", user);
    let actionedByUserData = user.recordset[0];
    let actualActionId =
      actionedByUserData.department_id == 5 && productsData.actionId == 2
        ? 3
        : productsData.actionId;
    console.log("actual action id", actualActionId);
    console.log("productsData.actionId", productsData.actionId);
    console.log("productsData.prophet_id", productsData.prophetId);
    let recipientEmailsData;
    switch (actualActionId) {
      case 2:
        console.log(
          "productsData.wasRejected",
          productsData.wasRejected == true,
          typeof productsData.wasRejected
        );
        if (productsData.wasRejected == true) {
          emailType = "ResubmittedRequest";
          console.log("entering this");
          paragraph = `<p>The request for variety ${productsData.code} with description ${productsData.description} has been resubmitted by the Requestor with changes. Please log in to review and process it. Request ID: - ${concatenatedRequestInfo}.</p>`;
        } else {
          emailType = "createRequest";
          paragraph = `<p>A new request for variety ${productsData.code} with description ${productsData.description} has been submitted. Please log in to the portal to process it. Request ID: - ${concatenatedRequestInfo}.</p>`;
        }
        recipientEmailsData = await pool
          .request()
          .input("prophet_id", sql.Int, productsData?.prophetId)
          .input("section_id", sql.Int, 6)
          .query(emailSqlQueries.getEmailRecipients);
        recipientEmails = recipientEmailsData.recordset[0].recipients;
        comment = productsData.comment
          ? `<p>Comment: ${productsData.comment}</p>`
          : "";
        logger.info({
          username: productsData.actionedByEmail,
          type: "success",
          description: `User with email ${productsData.actionedByEmail} ${
            productsData.wasRejected == true ? "resubmitted" : "submitted"
          } variety ${productsData.code} with description ${
            productsData.description
          } with comment ${
            productsData.comment
          }. Request ID: ${concatenatedRequestInfo}`,
          itemid: concatenatedRequestInfo,
          module_id: 2,
        });
        break;
      case 3:
        recipientEmailsData = await pool
          .request()
          .input("prophet_id", sql.Int, 5)
          .input("section_id", sql.Int, 6)
          .query(emailSqlQueries.getEmailRecipients);
        recipientEmails = recipientEmailsData.recordset[0].recipients;
        ccEmail = "<EMAIL>;<EMAIL>";
        emailType = "approvedRequestForISS";
        paragraph = `<p>A new variety ${productsData?.code} with description ${productsData.description} has been submitted and forwarded directly for setup. Please log in to the portal to complete the process. Request ID: - ${concatenatedRequestInfo}.</p>`;
        comment = productsData.comment
          ? `<p>Comment: ${productsData.comment}</p>`
          : "";
        logger.info({
          username: productsData.actionedByEmail,
          type: "success",
          description: `User with email ${productsData.actionedByEmail} approved variety ${productsData.code} with description ${productsData.description} with comment ${productsData.comment}. Request ID: ${concatenatedRequestInfo}`,
          itemid: concatenatedRequestInfo,
          module_id: 2,
        });
        break;
      case 4:
        recipientEmails = productsData.originatorEmail;
        // recipientEmails = "<EMAIL>";
        emailType = "rejectedRequest";
        paragraph = `<p>Your request with variety code ${productsData.code} with description ${productsData.description} has been rejected.<br><br> Reason for rejection: ${productsData.comment}.<br><br> Please make the necessary changes and resubmit. ${concatenatedRequestInfo} </p>`;
        comment = "";
        logger.info({
          username: productsData.actionedByEmail,
          type: "success",
          description: `User with email ${productsData.actionedByEmail} rejected variety ${productsData.code} with description ${productsData.description} with comment ${productsData.comment}. Request ID: ${concatenatedRequestInfo}`,
          itemid: concatenatedRequestInfo,
          module_id: 2,
        });
        break;
      case 11:
        recipientEmailsData = await pool
          .request()
          .input("prophet_id", sql.Int, productsData?.prophetId)
          .input("section_id", sql.Int, 6)
          .query(emailSqlQueries.getEmailRecipients);
        recipientEmails = recipientEmailsData.recordset[0].recipients;
        emailType = "completedRequest";
        if (
          productsData.requestCompany == "efcltd" ||
          productsData.requestCompany == "fpp-ltd"
        ) {
          paragraph = `<p>The setup of your variety ${
            productsData.code
          } with description ${
            productsData.description
          } has been completed on ISS ${
            productsData?.requestCompany == "efcltd" ? "EFC" : "FPP"
          } ProphetProphet. Request ID: - ${concatenatedRequestInfo}</p>`;
        } else {
          paragraph = `<p>The setup of your variety ${productsData.code} with description ${productsData.description} has been completed on ISS Prophet. Request ID: - ${concatenatedRequestInfo}</p>`;
        }
        comment = productsData.comment
          ? `<p>Comment: ${productsData.comment}</p>`
          : "";
        logger.info({
          username: productsData.actionedByEmail,
          type: "success",
          description: `User with email ${productsData.actionedByEmail} completed setup for variety ${productsData.code} with description ${productsData.description} with comment ${productsData.comment}. Request ID: ${concatenatedRequestInfo}`,
          itemid: concatenatedRequestInfo,
          module_id: 2,
        });
        break;
      case 5:
        recipientEmailsData = await pool
          .request()
          .input("prophet_id", sql.Int, productsData?.prophetId)
          .input("section_id", sql.Int, 6)
          .query(emailSqlQueries.getEmailRecipients);
        recipientEmails = `${recipientEmailsData.recordset[0].recipients};${productsData.originatorEmail}`;
        emailType = "completedRequest";
        paragraph = `<p>The setup of your variety ${
          productsData.code
        } with description ${
          productsData.description
        } has been completed on ISS Prophet.Please confirm through the portal once the request has been processed on ${
          productsData.requestCompany === "efcltd" ? "EFC" : "FPP"
        } prophet. Request ID: - ${concatenatedRequestInfo}</p>`;
        comment = productsData.comment
          ? `<p>Comment: ${productsData.comment}</p>`
          : "";
        logger.info({
          username: productsData.actionedByEmail,
          type: "success",
          description: `User with email ${productsData.actionedByEmail} completed setup for variety ${productsData.code} with description ${productsData.description} with comment ${productsData.comment}. Request ID: ${concatenatedRequestInfo}`,
          itemid: concatenatedRequestInfo,
          module_id: 2,
        });
        break;
      default:
        paragraph = `<p>User ${productsData?.actionedByName} performed an action with request number ${concatenatedRequestInfo}.</p>`;
        break;
    }
    console.log("recipientEmails", recipientEmails);
    console.log("ccEmail", ccEmail);
    console.log("emailType", emailType);
    console.log("paragraph", paragraph);

    const placeholders = {
      paragraph: paragraph || "Products",
      comment: comment,
    };
    console.log("placeholders", placeholders);
    let companyKey = "";
    if (productsData?.prophetId == 1) {
      companyKey = "dpsltd";
    } else if (productsData?.prophetId == 2) {
      companyKey = "dpsltd";
    } else if (productsData?.prophetId == 3) {
      companyKey = "efcltd";
    } else if (productsData?.prophetId == 4) {
      companyKey = "fpp-ltd";
    }
    console.log("companyKey", companyKey);
    // // Send email notification
    await sendEmail({
      placeholders,
      emailType,
      recipientEmails,
      ccEmail,
      companyKey,
      request_no: concatenatedRequestInfo,
      varietyCode: productsData.code,
      masterProductCode: productsData.masterProductCode,
    });

    if (productsData.isTechinicalTeam && actualActionId == 3) {
      const technicalEmailsData = await pool
        .request()
        .input("prophet_id", sql.Int, productsData?.prophetId)
        .input("section_id", sql.Int, 6)
        .query(emailSqlQueries.getEmailRecipients);
      const technicalEmails = technicalEmailsData.recordset[0].recipients;
      const technicalEmailType = "RequestSubmittedToISS";
      const technicalParagraph = `<p>Your request for variety ${productsData?.code} with description ${productsData.description} has been submitted and forwarded to ISS for setup. Request ID: - ${concatenatedRequestInfo}</p>`;
      const technicalPlaceholders = {
        paragraph: technicalParagraph,
        comment: comment,
      };

      await sendEmail({
        placeholders: technicalPlaceholders,
        emailType: technicalEmailType,
        recipientEmails: technicalEmails,
        ccEmail,
        companyKey,
        request_no: concatenatedRequestInfo,
        varietyCode: productsData.code,
        masterProductCode: productsData.masterProductCode,
      });
    } else if (actualActionId == 11) {
      const requestorRecipientEmails = productsData.originatorEmail;
      const requestorEmailType = "completedRequest";
      let requestorParagraph;
      if (
        productsData.requestCompany == "efcltd" ||
        productsData.requestCompany == "fpp-ltd"
      ) {
        requestorParagraph = `<p>The setup of your variety ${
          productsData.code
        } with description ${
          productsData.description
        } has been successfully completed on ISS and ${
          productsData?.requestCompany == "efcltd" ? "EFC" : "FPP"
        } Prophet. Request ID: - ${concatenatedRequestInfo}</p>`;
      } else {
        requestorParagraph = `<p>The setup of your variety ${productsData.code} with description ${productsData.description} has been successfully completed on ISS Prophet. Request ID: - ${concatenatedRequestInfo}</p>`;
      }
      const requestorComment = productsData.comment
        ? `<p>Comment: ${productsData.comment}</p>`
        : "";
      const requestorPlaceholder = {
        paragraph: requestorParagraph,
        comment: requestorComment,
      };
      console.log("requestor placeholder", requestorPlaceholder);
      await sendEmail({
        placeholders: requestorPlaceholder,
        emailType: requestorEmailType,
        recipientEmails: requestorRecipientEmails,
        ccEmail,
        companyKey,
        request_no: concatenatedRequestInfo,
        varietyCode: productsData.code,
        masterProductCode: productsData.masterProductCode,
      });
    }

    return result;
  } catch (error) {
    // Log error
    logger.error({
      username: productsData.username,
      type: "error",
      description: `User with email ${productsData.actionedByEmail} failed to create or update variety: ${error.message}`,
      item_id: null,
      module_id: 2,
    });
    console.error(error);
    return { error: error.message };
  }
};

const getRequestNumber = async (product_request_type) => {
  // console.log("product_request_type",product_request_type);
  try {
    let pool = await sql.connect(config.sql);
    const sqlQueries = await utils.loadSqlQueries("products");
    const { recordset } = await pool
      .request()
      // .input("company", sql.VarChar, company)
      .input("product_request_type", sql.Int, product_request_type)
      .query(sqlQueries.getRequestNumber);
    return recordset;
  } catch (error) {
    console.log(error);
    return error.message;
  }
};

const getProductGroups = async (type) => {
  try {
    let pool = await sql.connect(config.sql);
    const sqlQueries = await utils.loadSqlQueries("products");
    const { recordset } = await pool
      .request()
      .input("type", sql.Int, type)
      .query(sqlQueries.getProductGroups);
    return recordset;
  } catch (error) {
    console.log(error);
    return error.message;
  }
};

const createRequestNumber = async (
  company,
  request_no,
  product_request_type
) => {
  try {
    let pool = await sql.connect(config.sql);
    const sqlQueries = await utils.loadSqlQueries("products");
    const { recordset } = await pool
      .request()
      .input("company", sql.VarChar, company)
      .input("request_no", sql.VarChar, request_no)
      .input("product_request_type", sql.Int, product_request_type)
      .query(sqlQueries.createRequestNumber);
    return { data: true };
  } catch (error) {
    return error.message;
  }
};

const getProducts = async (company, type_id) => {
  try {
    let pool = await sql.connect(config.sql);
    const sqlQueries = await utils.loadSqlQueries("products");
    console.log("company", company, type_id);
    const { recordset } = await pool
      .request()
      .input("company", sql.VarChar, company)
      .input("type_id", sql.Int, type_id)
      .input("fetch_all", sql.Bit, company)
      .query(sqlQueries.productsList);
    return recordset;
  } catch (error) {
    console.log(error);
    return error.message;
  }
};
const getRawMaterialsById = async (id) => {
  try {
    let pool = await sql.connect(config.sql);
    const sqlQueries = await utils.loadSqlQueries("products");

    const products = await pool
      .request()
      .input("id", sql.Int, id)
      .query(sqlQueries.getRawMaterialsById);

    return products.recordset;
  } catch (error) {
    logger.error({
      username: id,
      type: "error",
      description: `Failed to get Suppliers by id:${error}`,
      module_id: 2,
    });
    console.error(error);
    return error.message;
  }
};

const getFinishedProductsById = async (id) => {
  try {
    let pool = await sql.connect(config.sql);
    const sqlQueries = await utils.loadSqlQueries("products");
    const products = await pool
      .request()
      .input("id", sql.Int, id)
      .query(sqlQueries.getFinishedProductsById);
    return products.recordset;
  } catch (error) {
    logger.error({
      username: id,
      type: "error",
      description: `Failed to get Suppliers by id:${error}`,
      module_id: 2,
    });
    return error.message;
  }
};

const getNVProductsById = async (id) => {
  try {
    const pool = await sql.connect(config.sql);
    const sqlQueries = await utils.loadSqlQueries("products");
    const products = await pool
      .request()
      .input("variety_id", sql.Int, id)
      .query(sqlQueries.nVarietyGetById);

    return products.recordset;
  } catch (error) {
    logger.error({
      username: id,
      type: "error",
      description: `Failed to get New Varieties by ID: ${error.message}`,
      module_id: 2,
    });
    return { error: error.message };
  }
};

const updateStatus = async (data) => {
  try {
    const pool = await sql.connect(config.sql);
    const sqlQueries = await utils.loadSqlQueries("products");
    let products;
    console.log("data on cancellation", data);
    if (data.type == "NV") {
      products = await pool
        .request()
        .input("varietyId", sql.Int, data.productId)
        .input("actionId", sql.Int, data.status == 6 ? 8 : data.status)
        .input("comment", sql.VarChar, data.reason)
        .input("actionedByEmail", sql.VarChar, data.cancelled_by)
        .input("actionedByName", sql.VarChar, data.cancelled_by_name)
        .query(sqlQueries.nVarietyCancelRequest);
    } else if (data.type == "PK") {
      products = await pool
        .request()
        .input("packaging_request_id", sql.Int, data.packaging_request_id)
        .input("actionId", sql.Int, data.actionId)
        .input("actionedByEmail", sql.VarChar, data.actionedByEmail)
        .input("actionedByName", sql.VarChar, data.actionedByName)
        .input("comment", sql.VarChar, data.comment)
        .query(sqlQueries.pkCancel);
    } else {
      products = await pool
        .request()
        .input("id", sql.Int, data.productId)
        .input("status", sql.Int, data.status)
        .input("updated_date", sql.DateTime, data.updated_date)
        .input("cancel_reason", sql.VarChar, data.reason)
        .input("cancelled_date", sql.DateTime, data.updated_date)
        .input("cancelled_by", sql.VarChar, data.cancelled_by)
        .query(sqlQueries.updateProductStatus);
    }
    if (data.type != "NV") {
      let recipientEmails = data.cancelled_by;
      const ccEmail = data?.originator_email;
      const formType =
        data?.type == "RM"
          ? "Raw material request"
          : data?.type == "FG"
          ? "Finished goods"
          : data?.type == "NV"
          ? "New Variety Request"
          : "";
      const paragraph = `<p>User ${data?.cancelled_by} cancelled ${formType} with request number ${data.request_no} and the cancellation reason as below:</p><b>${data?.reason}</b>`;
      let placeholders = {
        paragraph: paragraph || "Products",
        comment: "",
      };
      const companyKey = "flrs";
      let emailType = "cancelRequest";
      const emailResult = await sendEmail({
        placeholders,
        emailType,
        recipientEmails,
        ccEmail,
        companyKey,
      });
    } else if (data.type == "NV") {
      const formType = "New Variety Request";
      const emailSqlQueries = await utils.loadSqlQueries("suppliers");
      let comment = data.reason ? `<p>Comment: ${data.reason}</p>` : "";
      if (data.current_action_id == 3) {
        const ISSEmailRecipientsData = await pool
          .request()
          .input("prophet_id", sql.Int, 5)
          .input("section_id", sql.Int, 6)
          .query(emailSqlQueries.getEmailRecipients);
        const recipientEmails = ISSEmailRecipientsData.recordset[0].recipients;
        const paragraph = `<p>The request for variety ${data?.code} has been cancelled by the Requestor. ${data.request_no}</p>`;

        const placeholders = {
          paragraph: paragraph || "Products",
          comment: comment,
        };
        console.log("placeholders for iss email", placeholders);
        const ccEmail = "";
        const companyKey = "flrs";
        let emailType = "cancelRequest";
        console.log("iss email", recipientEmails);
        const emailResult = await sendEmail({
          placeholders,
          emailType,
          recipientEmails,
          ccEmail,
          companyKey,
          request_no: data.request_no,
        });
      }
      if (data.current_action_id != 1) {
        let technicalEmailRecipientsData = await pool
          .request()
          .input("prophet_id", sql.Int, data.prophetId)
          .input("section_id", sql.Int, 6)
          .query(emailSqlQueries.getEmailRecipients);
        const recipientEmails =
          technicalEmailRecipientsData.recordset[0].recipients;
        const ccEmail = null;
        console.log("technicalEmailRecipients email", recipientEmails);

        const paragraph = `<p>The request for variety ${data?.code} has been cancelled and no further action is required. ${data.request_no}</p>`;

        const placeholders = {
          paragraph: paragraph || "Products",
          comment: comment,
        };
        console.log("placeholders for technical email", placeholders);
        const companyKey = "flrs";
        let emailType = "cancelRequest";
        const emailResult = await sendEmail({
          placeholders,
          emailType,
          recipientEmails,
          ccEmail,
          companyKey,
          request_no: data.request_no,
        });
      }
    }

    logger.info({
      username: data.cancelled_by,
      type: "success",
      description: `User with email: ${data.cancelled_by} cancelled ${
        data.type == "NV" ? "new variety" : "product"
      } request, with request no: ${data.request_no}, and with reason: "${
        data?.reason
      }"`,
      item_id: data.productId,
      module_id: 2,
    });
    return true;
  } catch (error) {
    console.log("Error:", error);
    logger.error({
      username: data.cancelled_by,
      type: "error",
      description: `Failed to update product status by id:${error}`,
      module_id: 2,
    });
    return error.message;
  }
};

const updateUnblock = async (data) => {
  // console.log("data",data);
  try {
    const pool = await sql.connect(config.sql);
    const sqlQueries = await utils.loadSqlQueries("products");
    const products = await pool
      .request()
      .input("id", sql.Int, data.productId)
      .input("submitted_to_iss", sql.Bit, data.submitted_to_iss)
      .input("status", sql.Int, data.status)
      .input("is_active", sql.Int, data.is_active)
      .input("unblocked_reason", sql.VarChar, data.unblocked_reason)
      .input("unblocked_at", sql.DateTime, data.unblocked_at)
      .input("unblocked_by", sql.VarChar, data.unblocked_by)
      .input("request_no", sql.VarChar, data.request_no)
      .query(sqlQueries.updateProductUnblock);

    let recipientEmails = data.email;
    let ccEmail = "";
    if (recipientEmails == data.originatorEmail) {
      ccEmail = "";
    } else {
      ccEmail = data.originatorEmail;
    }
    const formType =
      data?.type == "RM"
        ? "Raw material request"
        : data?.type == "FG"
        ? "Finished goods"
        : "";
    const paragraph = `<p>User ${data?.unblocked_by} unblocked ${formType} with request number ${data.request_no} and the unblock reason as below:</p><b>${data?.unblocked_reason}</b>`;

    let commentPlaceholder = "";

    let placeholders = {
      paragraph: paragraph || "Products",
      comment: commentPlaceholder,
    };
    const companyKey = "flrs";
    let emailType = "unblockRequest";
    const emailResult = await sendEmail({
      placeholders,
      emailType,
      ccEmail,
      recipientEmails,
      companyKey,
    });

    logger.info({
      username: data.name,
      type: "success",
      description: `User with email: ${data.email} unblocked for product request, with request no: ${data.request_no} with reason: "${data?.unblocked_reason}"`,
      item_id: data.productId,
      module_id: 2,
    });

    return products.recordset;
  } catch (error) {
    console.log("Error:", error);
    logger.error({
      username: data.name,
      type: "error",
      description: `Failed to update product status by id:${error}`,
      module_id: 2,
    });
    return error.message;
  }
};

const reasonList = async () => {
  try {
    const pool = await sql.connect(config.sql);
    const sqlQueries = await utils.loadSqlQueries("products");

    const { recordset } = await pool.request().query(sqlQueries.reasonList);

    return recordset || "No records found";
  } catch (error) {
    console.log("Error:", error);
    return error.message;
  }
};
const brandsList = async () => {
  try {
    const pool = await sql.connect(config.sql);
    const sqlQueries = await utils.loadSqlQueries("products");

    const { recordset } = await pool.request().query(sqlQueries.brandsList);

    return recordset || "No records found";
  } catch (error) {
    console.log("Error:", error);
    return error.message;
  }
};
const machineFormatList = async () => {
  try {
    const pool = await sql.connect(config.sql);
    const sqlQueries = await utils.loadSqlQueries("products");

    const { recordset } = await pool
      .request()
      .query(sqlQueries.machineFormatList);

    return recordset || "No records found";
  } catch (error) {
    console.log("Error:", error);
    return error.message;
  }
};
const subProductCodeList = async (prophetId) => {
  try {
    const pool = await sql.connect(config.sql);
    const sqlQuesries = await utils.loadSqlQueries("products");

    const { recordset } = await pool
      .request()
      .input("prophetId", sql.Int, parseInt(prophetId))
      .query(sqlQuesries.subProductCodeList);

    return recordset || "No records found";
  } catch (error) {
    console.log("Error: ", error);
    return error.message;
  }
};
const PackagingTypeList = async (prophetId) => {
  try {
    const pool = await sql.connect(config.sql);
    const sqlQuesries = await utils.loadSqlQueries("products");

    const { recordset } = await pool
      .request()
      .input("prophetId", sql.Int, parseInt(prophetId))
      .query(sqlQuesries.PackagingTypeList);

    return recordset || "No records found";
  } catch (error) {
    console.log("Error: ", error);
    return error.message;
  }
};
const RecyclableOPRLList = async (prophetId) => {
  try {
    const pool = await sql.connect(config.sql);
    const sqlQuesries = await utils.loadSqlQueries("products");

    const { recordset } = await pool
      .request()
      .input("prophetId", sql.Int, parseInt(prophetId))
      .query(sqlQuesries.RecyclableOPRLList);

    return recordset || "No records found";
  } catch (error) {
    console.log("Error: ", error);
    return error.message;
  }
};
const SustainableForestryPaperList = async (prophetId) => {
  try {
    const pool = await sql.connect(config.sql);
    const sqlQuesries = await utils.loadSqlQueries("products");

    const { recordset } = await pool
      .request()
      .input("prophetId", sql.Int, parseInt(prophetId))
      .query(sqlQuesries.SustainableForestryPaperList);

    return recordset || "No records found";
  } catch (error) {
    console.log("Error: ", error);
    return error.message;
  }
};
const TradingBusinessList = async (prophetId) => {
  try {
    const pool = await sql.connect(config.sql);
    const sqlQuesries = await utils.loadSqlQueries("products");

    const { recordset } = await pool
      .request()
      .input("prophetId", sql.Int, parseInt(prophetId))
      .query(sqlQuesries.TradingBusinessList);

    return recordset || "No records found";
  } catch (error) {
    console.log("Error: ", error);
    return error.message;
  }
};

const PackagingReasonList = async (prophetId) => {
  try {
    const pool = await sql.connect(config.sql);
    const sqlQuesries = await utils.loadSqlQueries("products");

    const { recordset } = await pool
      .request()
      .input("prophetId", sql.Int, parseInt(prophetId))
      .query(sqlQuesries.PackagingReasonList);

    return recordset || "No records found";
  } catch (error) {
    console.log("Error: ", error);
    return error.message;
  }
};
const PackagingMaterialTypesList = async (prophetId) => {
  try {
    const pool = await sql.connect(config.sql);
    const sqlQuesries = await utils.loadSqlQueries("products");

    const { recordset } = await pool
      .request()
      .input("prophetId", sql.Int, parseInt(prophetId))
      .query(sqlQuesries.PackagingMaterialTypesList);

    return recordset || "No records found";
  } catch (error) {
    console.log("Error: ", error);
    return error.message;
  }
};
const PackagingMaterialColorsList = async (prophetId) => {
  try {
    const pool = await sql.connect(config.sql);
    const sqlQuesries = await utils.loadSqlQueries("products");

    const { recordset } = await pool
      .request()
      .input("prophetId", sql.Int, parseInt(prophetId))
      .query(sqlQuesries.PackagingMaterialColorsList);

    return recordset || "No records found";
  } catch (error) {
    console.log("Error: ", error);
    return error.message;
  }
};
const caliberSizeList = async () => {
  try {
    const pool = await sql.connect(config.sql);
    const sqlQueries = await utils.loadSqlQueries("products");

    const { recordset } = await pool
      .request()
      .query(sqlQueries.caliberSizeList);
    return recordset || "No records found";
  } catch (error) {
    console.log("Error:", error);
    return error.message;
  }
};
const endCustomersList = async () => {
  try {
    const pool = await sql.connect(config.sql);
    const sqlQueries = await utils.loadSqlQueries("products");

    const { recordset } = await pool
      .request()
      .query(sqlQueries.endCustomersList);

    return recordset || "No records found";
  } catch (error) {
    console.log("Error:", error);
    return error.message;
  }
};
const packagingTypesList = async () => {
  try {
    const pool = await sql.connect(config.sql);
    const sqlQueries = await utils.loadSqlQueries("products");

    const { recordset } = await pool
      .request()
      .query(sqlQueries.packagingTypesList);

    return recordset || "No records found";
  } catch (error) {
    console.log("Error:", error);
    return error.message;
  }
};
const varietyList = async () => {
  try {
    const pool = await sql.connect(config.sql);
    const sqlQueries = await utils.loadSqlQueries("products");

    const { recordset } = await pool.request().query(sqlQueries.varietyList);

    return recordset || "No records found";
  } catch (error) {
    console.log("Error:", error);
    return error.message;
  }
};

const countryOfOrigin = async () => {
  try {
    const pool = await sql.connect(config.sql);
    const sqlQueries = await utils.loadSqlQueries("products");

    const { recordset } = await pool
      .request()
      .query(sqlQueries.countryOfOriginList);

    return recordset || "No records found";
  } catch (error) {
    console.log("Error:", error);
    return error.message;
  }
};

const markVarietyList = async () => {
  try {
    const pool = await sql.connect(config.sql);
    const sqlQueries = await utils.loadSqlQueries("products");

    const { recordset } = await pool
      .request()
      .query(sqlQueries.markVarietyList);

    return recordset || "No records found";
  } catch (error) {
    console.log("Error:", error);
    return error.message;
  }
};
const classifiedAllergicList = async () => {
  try {
    const pool = await sql.connect(config.sql);
    const sqlQueries = await utils.loadSqlQueries("products");

    const { recordset } = await pool
      .request()
      .query(sqlQueries.classifiedAllergicList);

    return recordset || "No records found";
  } catch (error) {
    console.log("Error:", error);
    return error.message;
  }
};
const organicCertificationList = async () => {
  try {
    const pool = await sql.connect(config.sql);
    const sqlQueries = await utils.loadSqlQueries("products");

    const { recordset } = await pool
      .request()
      .query(sqlQueries.organicCertificationList);

    return recordset || "No records found";
  } catch (error) {
    console.log("Error:", error);
    return error.message;
  }
};
const productTypeList = async () => {
  try {
    const pool = await sql.connect(config.sql);
    const sqlQueries = await utils.loadSqlQueries("products");

    const { recordset } = await pool
      .request()
      .query(sqlQueries.productTypeList);

    return recordset || "No records found";
  } catch (error) {
    console.log("Error:", error);
    return error.message;
  }
};
const temperatureGradeList = async () => {
  try {
    const pool = await sql.connect(config.sql);
    const sqlQueries = await utils.loadSqlQueries("products");

    const { recordset } = await pool
      .request()
      .query(sqlQueries.temperatureGradeList);

    return recordset || "No records found";
  } catch (error) {
    console.log("Error:", error);
    return error.message;
  }
};

// const sortGroupList = async () => {
//   try {
//     const pool = await sql.connect(config.sql);
//     const sqlQueries = await utils.loadSqlQueries("products");

//     const { recordset } = await pool
//       .request()
//       .query(sqlQueries.getSortGroup);

//     return recordset || "No records found";
//   } catch (error) {
//     console.log("Error:", error);
//     return error.message;
//   }
// };
const intrastatCommodityCodeList = async () => {
  try {
    const pool = await sql.connect(config.sql);
    const sqlQueries = await utils.loadSqlQueries("products");

    const { recordset } = await pool
      .request()
      .query(sqlQueries.intrastatCommodityCodeList);
    return recordset || "No records found";
  } catch (error) {
    console.log("Error:", error);
    return error.message;
  }
};

const newOuterBoxType = async () => {
  try {
    const pool = await sql.connect(config.sql);
    const sqlQueries = await utils.loadSqlQueries("products");

    const { recordset } = await pool.request().query(sqlQueries.boxTypesList);
    return recordset || "No records found";
  } catch (error) {
    console.log("Error:", error);
    return error.message;
  }
};

const updateCode = async (
  data,
  updateCodeTypeName,
  sqlFileToUpdateCodeInDb
) => {
  try {
    // console.log("sqlFileToUpdateCodeInDb",sqlFileToUpdateCodeInDb)
    const pool = await sql.connect(config.sql);
    const sqlQueries = await utils.loadSqlQueries("products");
    const codeToBeUpdated = await pool
      .request()
      .input("description", sql.VarChar, data.description)
      .input("code", sql.VarChar, data.code)
      .input("value", sql.Int, data.value)
      .input("is_new", sql.Bit, data.is_new)
      .input("table_name", sql.VarChar, data.tableName)
      .input("is_active", sql.Bit, true)
      .input("prophetId", sql.Int, parseInt(data.prophetId))
      .query(sqlQueries[sqlFileToUpdateCodeInDb]);
    logger.info({
      username: data.username,
      type: "success",
      description: `User with email ${data.useremail} updated new reference data for Product ${updateCodeTypeName} code with description ${data.description} and code ${data.code}`,
      item_id: null,
      module_id: 2,
    });
    return codeToBeUpdated.recordset;
  } catch (error) {
    console.log("Error:", error);
    logger.error({
      username: data.username,
      type: "error",
      description: `User with email ${data.useremail} failed to update new reference data for Product ${updateCodeTypeName} code: ${error.message}`,
      item_id: null,
      module_id: 2,
    });
    return error.message;
  }
};

const addMasterCode = async (data) => {
  try {
    const pool = await sql.connect(config.sql);
    const sqlQueries = await utils.loadSqlQueries("products");
    const masterCode = await pool
      .request()
      .input("description", sql.VarChar, data.description)
      .input("code", sql.VarChar, data.code)
      .input("is_new", sql.Bit, data.is_new)
      .input("table_name", sql.VarChar, data.tableName)
      .input("is_active", sql.Bit, true)
      .input("prophetId", sql.Int, parseInt(data.prophetId))
      .query(sqlQueries.addMasterProductCodes);
    logger.info({
      username: data.username,
      type: "success",
      description: `User with email ${data.useremail} added new reference data for Product master code with description ${data.description} and code ${data.code}`,
      item_id: null,
      module_id: 2,
    });
    return masterCode.recordset;
  } catch (error) {
    console.log("Error:", error);
    logger.error({
      username: data.username,
      type: "error",
      description: `User with email ${data.useremail} failed to add new reference data for Product master code: ${error.message}`,
      item_id: null,
      module_id: 2,
    });
    return error.message;
  }
};

const addMarkVariety = async (data) => {
  try {
    const pool = await sql.connect(config.sql);
    const sqlQueries = await utils.loadSqlQueries("products");
    const markVariety = await pool
      .request()
      .input("description", sql.VarChar, data.description)
      .input("code", sql.VarChar, data.code)
      .input("is_new", sql.Bit, data.is_new)
      .input("table_name", sql.VarChar, data.tableName)
      .input("is_active", sql.Bit, true)
      .query(sqlQueries.addMarkVariety);

    logger.info({
      username: data.username,
      type: "success",
      description: `User with email ${data.useremail} added new reference data for Mark/Variety with description ${data.description} and code ${data.code}`,
      item_id: null,
      module_id: 2,
    });
    return markVariety.recordset;
  } catch (error) {
    console.log("Error:", error);
    logger.error({
      username: data.username,
      type: "error",
      description: `User with email ${data.useremail} failed to add new reference data for Mark/Variety: ${error.message}`,
      item_id: null,
      module_id: 2,
    });
    return error.message;
  }
};

const addBrand = async (data) => {
  try {
    const pool = await sql.connect(config.sql);
    const sqlQueries = await utils.loadSqlQueries("products");
    const brand = await pool
      .request()
      .input("description", sql.VarChar, data.description)
      .input("code", sql.VarChar, data.code)
      .input("is_new", sql.Bit, data.is_new)
      .input("table_name", sql.VarChar, data.tableName)
      .input("is_active", sql.Bit, true)
      .query(sqlQueries.addBrand);
    logger.info({
      username: data.username,
      type: "success",
      description: `User with email ${data.useremail} added new reference data for Brand with description ${data.description} and code ${data.code}`,
      item_id: null,
      module_id: 2,
    });
    return brand.recordset;
  } catch (error) {
    console.log("Error:", error);
    logger.error({
      username: data.username,
      type: "error",
      description: `User with email ${data.useremail} failed to add new reference data for Brand: ${error.message}`,
      item_id: null,
      module_id: 2,
    });
    return error.message;
  }
};

const addCaliberSize = async (data) => {
  try {
    const pool = await sql.connect(config.sql);
    const sqlQueries = await utils.loadSqlQueries("products");
    const caliberSize = await pool
      .request()
      .input("description", sql.VarChar, data.description)
      .input("code", sql.VarChar, data.code)
      .input("is_new", sql.Bit, data.is_new)
      .input("table_name", sql.VarChar, data.tableName)
      .input("is_active", sql.Bit, true)
      .query(sqlQueries.addCaliberSize);
    logger.info({
      username: data.username,
      type: "success",
      description: `User with email ${data.useremail} added new reference data for caliber size with description ${data.description} and code ${data.code}`,
      item_id: null,
      module_id: 2,
    });
    return caliberSize.recordset;
  } catch (error) {
    console.log("Error:", error);
    logger.error({
      username: data.username,
      type: "error",
      description: `User with email ${data.useremail} failed to add new reference data for Caliber size: ${error.message}`,
      item_id: null,
      module_id: 2,
    });
    return error.message;
  }
};

const addVariety = async (data) => {
  try {
    const pool = await sql.connect(config.sql);
    const sqlQueries = await utils.loadSqlQueries("products");
    const variety = await pool
      .request()
      .input("description", sql.VarChar, data.description)
      .input("code", sql.VarChar, data.code)
      .input("is_new", sql.Bit, data.is_new)
      .input("table_name", sql.VarChar, data.tableName)
      .input("is_active", sql.Bit, true)
      .query(sqlQueries.addVariety);
    logger.info({
      username: data.username,
      type: "success",
      description: `User with email ${data.useremail} added new reference data for Variety with description ${data.description} and code ${data.code}`,
      item_id: null,
      module_id: 2,
    });
    return variety.recordset;
  } catch (error) {
    console.log("Error:", error);
    logger.error({
      username: data.username,
      type: "error",
      description: `User with email ${data.useremail} failed to add new reference data for Variety: ${error.message}`,
      item_id: null,
      module_id: 2,
    });
    return error.message;
  }
};

const addEndCustomer = async (data) => {
  try {
    const pool = await sql.connect(config.sql);
    const sqlQueries = await utils.loadSqlQueries("products");
    const endCustomer = await pool
      .request()
      .input("description", sql.VarChar, data.description)
      .input("code", sql.VarChar, data.code)
      .input("is_new", sql.Bit, data.is_new)
      .input("table_name", sql.VarChar, data.tableName)
      .input("is_active", sql.Bit, true)
      .query(sqlQueries.addEndCustomer);
    logger.info({
      username: data.username,
      type: "success",
      description: `User with email ${data.useremail} added new reference data for End customer with description ${data.description} and code ${data.code}`,
      item_id: null,
      module_id: 2,
    });
    return endCustomer.recordset;
  } catch (error) {
    console.log("Error:", error);
    logger.error({
      username: data.username,
      type: "error",
      description: `User with email ${data.useremail} failed to add new reference data for End customer: ${error.message}`,
      item_id: null,
      module_id: 2,
    });
    return error.message;
  }
};

const addBoxType = async (data) => {
  try {
    const pool = await sql.connect(config.sql);
    const sqlQueries = await utils.loadSqlQueries("products");
    const boxType = await pool
      .request()
      .input("description", sql.VarChar, data.description)
      .input("code", sql.VarChar, data.code)
      .input("is_new", sql.Bit, data.is_new)
      .input("table_name", sql.VarChar, data.tableName)
      .input("is_active", sql.Bit, true)
      .query(sqlQueries.addBoxType);
    logger.info({
      username: data.username,
      type: "success",
      description: `User with email ${data.useremail} added new reference data for New outer Box type with description ${data.description} and code ${data.code}`,
      item_id: null,
      module_id: 2,
    });
    return boxType.recordset;
  } catch (error) {
    console.log("Error:", error);
    logger.error({
      username: data.username,
      type: "error",
      description: `User with email ${data.useremail} failed to add new reference data for New outer Box type: ${error.message}`,
      item_id: null,
      module_id: 2,
    });
    return error.message;
  }
};

const addCountryOfOrigin = async (data) => {
  try {
    const pool = await sql.connect(config.sql);
    const sqlQueries = await utils.loadSqlQueries("products");
    const countryOfOrigin = await pool
      .request()
      .input("description", sql.VarChar, data.description)
      .input("code", sql.VarChar, data.code)
      .input("is_new", sql.Bit, data.is_new)
      .input("table_name", sql.VarChar, data.tableName)
      .input("is_active", sql.Bit, true)
      .query(sqlQueries.addCountryOfOrigin);
    logger.info({
      username: data.username,
      type: "success",
      description: `User with email ${data.useremail} added new reference data for Country of origin with description ${data.description} and code ${data.code}`,
      item_id: null,
      module_id: 2,
    });
    return countryOfOrigin.recordset;
  } catch (error) {
    console.log("Error:", error);
    logger.error({
      username: data.username,
      type: "error",
      description: `User with email ${data.useremail} failed to add new reference data for Country of origin: ${error.message}`,
      item_id: null,
      module_id: 2,
    });
    return error.message;
  }
};

const getSubProductsByMasterCode = async (master_code, prophetId) => {
  try {
    const pool = await sql.connect(config.sql);
    const sqlQueries = await utils.loadSqlQueries("products");

    const { recordset } = await pool
      .request()
      .input("master_code", sql.VarChar, master_code)
      .input("prophetId", sql.Int, parseInt(prophetId))
      .query(sqlQueries.getSubProductCodes);

    return recordset || [];
  } catch (error) {
    console.log("Error:", error);
    return error.message;
  }
};

const masterProductCodeList = async (prophetId,productType) => {
  try {
    const pool = await sql.connect(config.sql);
    const sqlQueries = await utils.loadSqlQueries("products");
    const { recordset } = await pool
      .request()
      .input("prophetId", sql.Int, parseInt(prophetId))
      .input("productType", sql.Int, parseInt(productType))
      .query(sqlQueries.masterProductCodeList);

    return recordset || "No records found";
  } catch (error) {
    console.log("Error:", error);
    return error.message;
  }
};
const packagingMasterproductCodeList = async (prophetId) => {
  try {
    const pool = await sql.connect(config.sql);
    const sqlQueries = await utils.loadSqlQueries("products");
    const { recordset } = await pool
      .request()
      .input("prophetId", sql.Int, parseInt(prophetId))
      .query(sqlQueries.packagingMasterProductCodes);

    return recordset || "No records found";
  } catch (error) {
    console.log("Error:", error);
    return error.message;
  }
};

const getFilteredVarietyName = async (value, prophetId) => {
  try {
    let pool = await sql.connect(config.sql);
    const sqlQueries = await utils.loadSqlQueries("products");
    const variety = await pool
      .request()
      .input("value", sql.VarChar, value)
      .input("prophetId", sql.Int, prophetId)
      .query(sqlQueries.getFilteredVarietyNames);
    return variety.recordset;
  } catch (error) {
    console.error(error);

    logger.error({
      username: null,
      type: "error",
      description: `Failed to get filtered Variety names:${error}`,
      module_id: 1,
    });
    return error.message;
  }
};

const checkVarietyData = async (
  varietyCode,
  varietyName,
  prophetId,
  requestId
) => {
  try {
    let pool = await sql.connect(config.sql);
    const sqlQueries = await utils.loadSqlQueries("products");
    const variety = await pool
      .request()
      .input("varietyCode", sql.VarChar, varietyCode)
      .input("varietyName", sql.VarChar, varietyName)
      .input("prophetId", sql.Int, prophetId)
      .input("requestId", sql.Int, requestId)
      .query(sqlQueries.checkVariety);
    console.log("variety", variety.recordset);
    return variety.recordset;
  } catch (error) {
    console.error(error);

    logger.error({
      username: null,
      type: "error",
      description: `Failed to get filtered Variety names:${error}`,
      module_id: 1,
    });
    return error.message;
  }
};

//region Packaging
const addUpdatePackagingRequest = async (productsData) => {
  try {
    console.log("request reaching here",productsData)
    // !action id 1=draft created, 2=submitted, 3=setup completed, 4=cancelled
    const pool = await sql.connect(config.sql);
    const sqlQueries = await utils.loadSqlQueries("products");
    const company = productsData.company;
    let result;
    const requestNumber = productsData.request_no || null;
    let concatenatedRequestInfo = "";

    //new request
    if (requestNumber == null) {
      // !if new request
      // !action id will be 1 or 2 for new request
      console.log("request number", requestNumber);
      console.log("creating new request");
      result = await pool
        .request()
        .input(
          "change_launch_date",
          sql.DateTime,
          productsData.change_launch_date
        )
        .input(
          "master_product_code_id",
          sql.Int,
          productsData.master_product_code_id
        )
        .input(
          "existing_packaging_code",
          sql.Int,
          productsData.existing_packaging_code
        )
        .input("end_customer", sql.Int, productsData.end_customer)
        .input("packaging_name", sql.VarChar, productsData.packaging_name)
        .input("type_of_packaging", sql.Int, productsData.type_of_packaging)
        .input("type_of_material", sql.Int, productsData.type_of_material)
        .input("colour_of_material", sql.Int, productsData.colour_of_material)
        .input("dimension_size", sql.VarChar, productsData.dimesion_size)
        .input("component_weight", sql.VarChar, productsData.component_weight)
        .input("recyclable_to_oprl", sql.Int, productsData.recyclable_to_oprl)
        .input(
          "paper_from_sustainable_forestry",
          sql.Int,
          productsData.paper_from_sustainble_forestry
        )
        .input("recyclable_content", sql.Int, productsData.recyclable_content)
        .input("reason_for_request", sql.Int, productsData.reason_for_request)
        .input("supplier", sql.VarChar, productsData.supplier)
        .input("trading_business", sql.Int, productsData.trading_business)
        .input("actionId", sql.Int, productsData.actionId)
        .input("actionedByEmail", sql.VarChar, productsData.actionedByEmail)
        .input("actionedByName", sql.VarChar, productsData.actionedByName)
        .input("comment", sql.VarChar, productsData.comment)
        .input("company", sql.VarChar,company)
        .input("subProdCode", sql.Int,productsData.subProdCode)
        .input("subProdCodeLabel", sql.VarChar,productsData.subProdCodeLabel)
        // productsData[0]?.master_product_code_code
        .query(sqlQueries.pkCreate);
      console.log("new result", result.recordsets);

      concatenatedRequestInfo = result.recordset.map(
        (item) => `PK${item.request_no}`
      )[0];
      //#region updatePK
    } else {
      console.log("in this noewto update");
      // Handle existing requests based on action ID
      if (productsData.actionId == 1 || productsData.actionId == 2) {
        // For action ID 1 (Draft created) or 2 (Submitted)
        // Update the product_packaging_request table and add new status
        result = await pool
          .request()
          .input("reqNumber",           sql.Int,      productsData.request_no)
          .input("packaging_request_id",sql.Int,      productsData.packaging_request_id)
          .input("change_launch_date",  sql.DateTime, productsData.change_launch_date)
          .input("master_product_code_id",sql.Int,    productsData.master_product_code_id)
          .input("existing_packaging_code",sql.Int,   productsData.existing_packaging_code)
          .input("end_customer",        sql.Int,      productsData.end_customer)
          .input("packaging_name",      sql.VarChar,  productsData.packaging_name)
          .input("type_of_packaging",   sql.Int,      productsData.type_of_packaging)
          .input("type_of_material",    sql.Int,      productsData.type_of_material)
          .input("colour_of_material",  sql.Int,      productsData.colour_of_material)
          .input("dimension_size",      sql.VarChar,  productsData.dimesion_size)
          .input("component_weight",    sql.VarChar,  productsData.component_weight)
          .input("recyclable_to_oprl",  sql.Int,      productsData.recyclable_to_oprl)
          .input("paper_from_sustainable_forestry", sql.Int,productsData.paper_from_sustainble_forestry)
          .input("recyclable_content",  sql.Int,      productsData.recyclable_content)
          .input("supplier",            sql.VarChar,  productsData.supplier)
          .input("trading_business",    sql.Int,      productsData.trading_business)
          .input("actionId",            sql.Int,      productsData.actionId)
          .input("actionedByEmail",     sql.VarChar,  productsData.actionedByEmail)
          .input("actionedByName",      sql.VarChar,  productsData.actionedByName)
          .input("comment",             sql.VarChar,  productsData.comment)
          .input("reason",              sql.Int,      productsData.reason_for_request)
          .input("subProdCode",         sql.Int,      productsData.subProdCode)
          .input("subProdCodeLabel",    sql.VarChar,  productsData.subProdCodeLabel)
          .query(sqlQueries.pkUpdate);
        console.log("update result", result);
      } else if (productsData.actionId == 3) {
        // For action ID 3 (Setup Completed)
        // Only update status, don't touch product_packaging_request
        result = await pool
          .request()
          .input(
            "packaging_request_id",
            sql.Int,
            productsData.packaging_request_id
          )
          .input("actionId", sql.Int, productsData.actionId)
          .input("actionedByEmail", sql.VarChar, productsData.actionedByEmail)
          .input("actionedByName", sql.VarChar, productsData.actionedByName)
          .input("comment", sql.VarChar, productsData.comment)
          .query(sqlQueries.pkUpdateStatus);
        console.log("status update result", result);
      }

      concatenatedRequestInfo =
        productsData.PKrequest_no ||
        `PK${productsData.request_no}`;
        console.log("concatenated request infor",concatenatedRequestInfo)
    }

    // Log the action based on action ID
    if (productsData.actionId == 1) {
      logger.info({
        username: productsData.actionedByEmail,
        type: "success",
        description: `User with email ${productsData.actionedByEmail} created a draft packaging request with comment ${productsData.comment}. Request ID: - ${concatenatedRequestInfo}`,
        itemid: concatenatedRequestInfo,
        module_id: 2,
      });
    }

    // Email notification logic
    let recipientEmails;
    let ccEmail = null;
    let formType = "Packaging Request";
    let paragraph;
    let emailType;
    let comment;
    const emailSqlQueries = await utils.loadSqlQueries("suppliers");
    const UsersSqlQueries = await utils.loadSqlQueries("users");

    const user = await pool
      .request()
      .input("email", sql.VarChar, productsData.actionedByEmail)
      .query(UsersSqlQueries.userByEmail);
    console.log("user", user);

    let actualActionId = productsData.actionId;
    console.log("actual action id", actualActionId);

    let recipientEmailsData;
    switch (actualActionId) {
      case 2:
        recipientEmailsData = await pool
          .request()
          .input("prophet_id", sql.Int, 5)
          .input("section_id", sql.Int,7)
          .query(emailSqlQueries.getEmailRecipients);
        recipientEmails = recipientEmailsData.recordset[0].recipients;
        ccEmail = "<EMAIL>";
        emailType ="submittedPackagingRequestForISS";
        paragraph = `<p>There has been a new Packaging Request ID: ${concatenatedRequestInfo} that has been sent for setup.</p><p>Please logon to the portal, review the details and extract the request for setup on Prophet.</p>`;
        comment = productsData.comment
          ? `<p>Comment: ${productsData.comment}</p>`
          : "";
        logger.info({
          username: productsData.actionedByEmail,
          type: "success",
          description: `User with email ${productsData.actionedByEmail} submitted packaging request with comment ${productsData.comment}. Request ID: ${concatenatedRequestInfo}`,
          itemid: concatenatedRequestInfo,
          module_id: 2,
        });
        break;
      case 3:
        recipientEmails = productsData.originatorEmail;
        emailType = "setCompletedByISS";
        paragraph = `<p>The setup for your Packaging Request with Request ID: ${concatenatedRequestInfo} ${productsData.packaging_name} has been completed. All necessary details have been finalised and updated in the system.</p>`;
        comment = "";
        logger.info({
          username: productsData.actionedByEmail,
          type: "success",
          description: `User with email ${productsData.actionedByEmail} completed setup for packaging request ${concatenatedRequestInfo}. ${productsData.comment ? `Comment: ${productsData.comment}` : ''}`,
          itemid: concatenatedRequestInfo,
          module_id: 2,
        });
        break;
      default:
        paragraph = `<p>User ${productsData?.actionedByName} performed an action with request number ${concatenatedRequestInfo}.</p>`;
        break;
    }

    console.log("recipientEmails", recipientEmails);
    console.log("ccEmail", ccEmail);
    console.log("emailType", emailType);
    console.log("paragraph", paragraph);

    const placeholders = {
      paragraph: paragraph || "Packaging Request",
      comment: comment,
    };
    console.log("placeholders", placeholders);

    let companyKey = "";
    if (productsData?.prophetId == 1) {
      companyKey = "dpsltd";
    } else if (productsData?.prophetId == 2) {
      companyKey = "dpsltd";
    } else if (productsData?.prophetId == 3) {
      companyKey = "efcltd";
    } else if (productsData?.prophetId == 4) {
      companyKey = "fpp-ltd";
    } else if (productsData?.prophetId == 5) {
      companyKey = "iss";
    }
    console.log("companyKey", companyKey);

    // Send email notification
    if (recipientEmails && emailType) {
      if (ccEmail === recipientEmails) {
        ccEmail = null;
      }
      console.log("*************BAse ***************");
      await sendEmail({
        placeholders,
        emailType,
        recipientEmails,
        ccEmail,
        companyKey,
        request_no: concatenatedRequestInfo,
        masterProductCode: productsData.masterProductCode,
      });
    }

    if (actualActionId ==3) {
      console.log("wehehehehhehehe actualActionId === 3 email",)
      const issAdmin = await pool
        .request()
        .input("prophet_id", sql.Int, productsData?.prophetId)
        .input("section_id", sql.Int,7)
        .query(emailSqlQueries.getEmailRecipients);
      const issAdminEmails = issAdmin.recordset[0].recipients;
      const issEmailType = "setCompletedByISS";
      const paragraph = `<p>The setup for Packaging Request with Request ID: ${concatenatedRequestInfo} ${productsData.packaging_name} has been completed. All necessary details have been finalised and updated in the system.</p>`;
      const placeholders = {
        paragraph: paragraph,
        comment: comment,
      };

      if (ccEmail === issAdminEmails) {
        ccEmail = null;
      }
      await sendEmail({
        placeholders: placeholders,
        emailType: issEmailType,
        recipientEmails: issAdminEmails,
        ccEmail,
        companyKey,
        request_no: concatenatedRequestInfo,
        varietyCode: productsData.code,
        masterProductCode: productsData.masterProductCode,
      });
    } else if (actualActionId == 2) {
      console.log("DEUX DEUX DEUX NOMBRES DEUX DEUX DEUX")
      const requestorRecipientEmails = productsData.originatorEmail;
      const requestorEmailType = "packagingSubmittedRequest";
      let requestorParagraph = `<p>Your Packaging Request with Request ID: ${concatenatedRequestInfo} ${productsData.packaging_name} has been submitted to ISS Admin for setup. Once ISS Admin have setup up request you will receive a notification on completion.</p>`;
      const requestorComment = productsData.comment
        ? `<p>Comment: ${productsData.comment}</p>`
        : "";
      const requestorPlaceholder = {
        paragraph: requestorParagraph,
        comment: requestorComment,
      };
      console.log("requestor placeholder", requestorPlaceholder);
      if (ccEmail === requestorRecipientEmails) {
        ccEmail = null;
      }
      await sendEmail({
        placeholders: requestorPlaceholder,
        emailType: requestorEmailType,
        recipientEmails: requestorRecipientEmails,
        ccEmail,
        companyKey,
        request_no: concatenatedRequestInfo,
        varietyCode: productsData.code,
        masterProductCode: productsData.masterProductCode,
      });
    }

    return result;
  } catch (error) {
    // Log error
    logger.error({
      username: productsData.username,
      type: "error",
      description: `User with email ${productsData.actionedByEmail} failed to create or update packaging request: ${error.message}`,
      item_id: null,
      module_id: 2,
    });
    console.error(error);
    return { error: error.message };
  }
};

const getPKrequetById = async (id) => {
  try {
    const pool = await sql.connect(config.sql);
    const sqlQueries = await utils.loadSqlQueries("products");
    const packagingRequest = await pool
      .request()
      .input("packaging_request_id", sql.Int, id)
      .query(sqlQueries.getPKById);

    return packagingRequest.recordset;
  } catch (error) {
    console.error("error fetching packaging requests by id(data)",error)
    logger.error({
      username: id,
      type: "error",
      description: `Failed to get New Varieties by ID: ${error.message}`,
      module_id: 2,
    });
    return { error: error.message };
  }
};

module.exports = {
  getFilteredVarietyName,
  getProducts,
  getRawMaterialsById,
  getFinishedProductsById,
  getNVProductsById,
  createRawMaterials,
  createFinishedProducts,
  updateRawMaterials,
  addUpdateNewVariety,
  createNewSubProductCode,
  masterProductCodeList,
  varietyList,
  brandsList,
  endCustomersList,
  caliberSizeList,
  packagingTypesList,
  countryOfOrigin,
  markVarietyList,
  classifiedAllergicList,
  organicCertificationList,
  productTypeList,
  temperatureGradeList,
  reasonList,
  intrastatCommodityCodeList,
  updateStatus,
  newOuterBoxType,
  machineFormatList,
  subProductCodeList,
  updateFinishedProducts,
  updateCode,
  addMasterCode,
  addMarkVariety,
  addBrand,
  addCaliberSize,
  addVariety,
  addEndCustomer,
  addBoxType,
  addCountryOfOrigin,
  updateUnblock,
  getRequestNumber,
  createRequestNumber,
  getProductGroups,
  getSubProductsByMasterCode,
  checkVarietyData,
  addUpdatePackagingRequest,
  getPKrequetById,
  // sortGroupList
  //Pakckging form
  PackagingTypeList,
  RecyclableOPRLList,
  SustainableForestryPaperList,
  TradingBusinessList,
  PackagingReasonList,
  PackagingMaterialTypesList,
  PackagingMaterialColorsList,
  getCreatedNewSubProductCode,
  packagingMasterproductCodeList,
};
