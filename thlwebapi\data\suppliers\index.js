"use strict";

const utils = require("../utils");
const config = require("../../config");
const sql = require("mssql");
const { encryptData, decryptData } = require("../../utils/encryption"); // Adjust the path accordingly
const logger = require("../../utils/logger");
const supplier = require("../../routes/supplier");
const { request } = require("express");
const { sendEmail } = require("../../utils/email");
var cron = require("node-cron");
const { remove } = require("lodash");

// cron.schedule("0 9,14 * * *", async () => {
//   console.log("Checking for incomplete suppliers...");
//   await checkIncompleteSuppliers();
// });

const isSupplierConfirmed = async (
  supplierData,
  roleIds,
  isProphetCodeUnqiue,
  sqlQueries,
  id,
  status,
  pool,
  companyKey
) => {
  const sectionsToCheck = supplierData.allowedSections.filter((section) => {
    if (section === "Procurement") {
      return false;
    }
    if (
      section === "Compliance" &&
      !roleIds?.includes(2) &&
      !roleIds?.includes(3)
    ) {
      return false;
    }
    if (
      section == "Financials" &&
      roleIds?.includes(5) &&
      roleIds?.length == 1
    ) {
      return false;
    }
    return true;
  });

  const statusCheck = {
    General: supplierData.technical,
  };
  if (roleIds.length == 1 && roleIds?.includes(4)) {
  } else if (
    roleIds?.includes(2) ||
    roleIds?.includes(3) ||
    roleIds?.includes(4)
  ) {
    statusCheck.Compliance = supplierData.compliance;
  }
  if (roleIds?.includes(1) || roleIds?.includes(6)) {
    statusCheck.Financials = supplierData.financial;
  }

  if (
    sectionsToCheck.every((section) => section in statusCheck) &&
    sectionsToCheck.every((section) => statusCheck[section] == "Complete") &&
    isProphetCodeUnqiue
  ) {
    let recipient = await pool
      .request()
      .input("prophet_id", sql.Int, supplierData?.prophet_id)
      .input("section_id", sql.Int, 2)
      .query(sqlQueries.getEmailRecipients);
    let recipientEmails =
      recipient.recordset[0]?.recipients + `;${supplierData.requestor_email}`;

    const fullName = supplierData?.requestor_name || "User";
    const firstName = fullName.split(" ")[0];
    let placeholders = {
      User: firstName,
      Supplier_name: supplierData?.supplierName || "Supplier",
      Supplier_id: id,
    };
    let emailType = "supplierConfirmation";

    const result = await sendEmail({
      placeholders,
      emailType,
      recipientEmails,
      companyKey,
    });
    status = 1;
  }
  return status;
};

const getSuppliers = async (company = "all",prophetId) => {
  try {
    let pool = await sql.connect(config.sql);
    const sqlQueries = await utils.loadSqlQueries("suppliers");
    let suppliers = await pool
      .request()
      .input("company", sql.VarChar, company)
      .input("prophetId", sql.Int, prophetId)
      .query(sqlQueries.suppliersListByCompany);

    // suppliers.recordset.length > 0 &&
    //   suppliers.recordset.forEach((supplier) => {
    //     if (supplier?.account_number) {
    //       supplier.decryptedAccountNumber = decryptData(
    //         supplier?.account_number
    //       );
    //     }

    //     if (supplier?.sort_bic) {
    //       supplier.decryptedSort_Bic = decryptData(supplier?.sort_bic);
    //     }

    //     if (supplier?.name_branch) {
    //       supplier.decryptedName_branch = decryptData(supplier?.name_branch);
    //     }

    //     if (supplier?.intermediatery_account_number) {
    //       supplier.decryptedIntermediatery_account_number = decryptData(
    //         supplier?.intermediatery_account_number
    //       );
    //     }
    //   });

    return suppliers.recordset;
  } catch (error) {
    console.error(error);
    logger.error({
      username: null,
      type: "error",
      description: `Failed to get Suppliers:${error}`,
      module_id: 1,
    });
    return error.message;
  }
};

const deleteAll = async () => {
  try {
    let pool = await sql.connect(config.sql);
    const sqlQueries = await utils.loadSqlQueries("suppliers");
    const list = await pool.request().query(sqlQueries.deleteAll);
    return list.recordset;
  } catch (error) {
    console.error(error);
    return error.message;
  }
};

const checkIncompleteSuppliers = async () => {
  try {
    let pool = await sql.connect(config.sql);
    const sqlQueries = await utils.loadSqlQueries("suppliers");
    const allProphetIds = [1, 2, 3, 4];

    for (const prophetId of allProphetIds) {
      let suppliers = await pool
        .request()
        .input("prophet_id", sql.Int, prophetId)
        .query(sqlQueries.getSuppliersListByProphet);

      let prophet_name = "";
      let companyKey = "";

      if (prophetId == 1) {
        prophet_name = "DPS";
        companyKey = "dpsltd";
      } else if (prophetId == 2) {
        prophet_name = "DPS-M&S";
        companyKey = "dpsltd";
      } else if (prophetId == 3) {
        prophet_name = "EFC";
        companyKey = "efcltd";
      } else if (prophetId == 4) {
        companyKey = "fpp-ltd";
      }

      const suppliersByProphet = suppliers.recordset;

      let incompleteGeneralSectionsByEmail = {};
      let incompleteFinanceSections = [];
      let incompleteComplianceSections = [];
      let supplier_code = "";
      for (const supplier of suppliersByProphet) {
        supplier_code = supplier?.prophet_ids
          ? JSON.parse(supplier?.prophet_ids)[0].prophet_code
          : "";

        let roleIdsArray = JSON.parse(supplier?.role_ids ?? "[]");
        let roleIds = roleIdsArray.map((role) => role?.role_id);

        let requestor_email = supplier?.requestor_email;

        if (
          (supplier?.compliance === "Incomplete" ||
            supplier?.compliance === null) &&
          (roleIds?.includes(2) ||
            roleIds?.includes(3) ||
            roleIds?.includes(4)) &&
          !supplier?.emergency_request
        ) {
          incompleteComplianceSections.push({
            supplier_name: supplier.name,
            supplier_code: supplier_code,
          });
        }
        if (
          supplier?.technical === "Incomplete" ||
          supplier?.technical === null
        ) {
          if (!incompleteGeneralSectionsByEmail[requestor_email]) {
            incompleteGeneralSectionsByEmail[requestor_email] = [];
          }

          incompleteGeneralSectionsByEmail[requestor_email].push({
            supplier_name: supplier.name,
            supplier_code: supplier_code,
          });
        }
        if (
          (supplier?.financial === "Incomplete" ||
            supplier?.financial === null ||
            supplier?.financial === "verified") &&
          !supplier?.emergency_request &&
          (roleIds?.includes(1) || roleIds?.includes(6))
        ) {
          incompleteFinanceSections.push({
            supplier_name: supplier.name,
            supplier_code: supplier_code,
          });
        }
      }

      if (incompleteComplianceSections.length > 0) {
        let recipient = await pool
          .request()
          .input("prophet_id", sql.Int, prophetId)
          .input("section_id", sql.Int, 3)
          .query(sqlQueries.getEmailRecipients);

        let recipientEmails = recipient.recordset[0]?.recipients;

        let incompleteSuppliersList = incompleteComplianceSections
          .map(
            (supplier) => `<li>${supplier.supplier_name} <span style='
color: #1b572b;'>${supplier.supplier_code}</span></li>`
          )
          .join("");

        let supplierNoun =
          incompleteComplianceSections.length > 1
            ? "Suppliers have"
            : "Supplier has";

        let placeholders = {
          Incomplete_suppliers: `<ul>${incompleteSuppliersList}</ul>`,
          section_name: "Compliance",
          prophet_name: prophet_name,
          supplierNoun: supplierNoun,
          supplier_code: supplier_code,
        };

        let emailType = "incompleteSuppliers";
        await sendEmail({
          placeholders,
          emailType,
          recipientEmails,
          companyKey,
        });
      }

      if (incompleteFinanceSections.length > 0) {
        let recipient = await pool
          .request()
          .input("prophet_id", sql.Int, prophetId)
          .input("section_id", sql.Int, 2)
          .query(sqlQueries.getEmailRecipients);

        let recipientEmails = recipient.recordset[0]?.recipients;

        let incompleteSuppliersList = incompleteFinanceSections
          .map(
            (supplier) => `<li>${supplier.supplier_name} <span style='
color: #1b572b;'>${supplier.supplier_code}</span></li>`
          )
          .join("");

        let supplierNoun =
          incompleteFinanceSections.length > 1
            ? "Suppliers have"
            : "Supplier has";

        let placeholders = {
          Incomplete_suppliers: `<ul>${incompleteSuppliersList}</ul>`,
          section_name: "Finance",
          prophet_name: prophet_name,
          supplierNoun: supplierNoun,
          supplier_code: supplier_code,
        };

        let emailType = "incompleteSuppliers";
        await sendEmail({
          placeholders,
          emailType,
          recipientEmails,
          companyKey,
        });
      }

      for (const [requestor_email, suppliers] of Object.entries(
        incompleteGeneralSectionsByEmail
      )) {
        let recipientEmails = requestor_email;

        let incompleteSuppliersList = suppliers
          .map(
            (supplier) => `<li>${supplier.supplier_name} <span style='
color: #1b572b;'>${supplier.supplier_code}</span></li>`
          )
          .join("");

        let supplierNoun =
          suppliers.length > 1 ? "Suppliers have" : "Supplier has";

        let placeholders = {
          Incomplete_suppliers: `<ul>${incompleteSuppliersList}</ul>`,
          section_name: "General",
          prophet_name: prophet_name,
          supplierNoun: supplierNoun,
          supplier_code: suppliers.map((s) => s.supplier_code).join(", "),
        };

        let emailType = "incompleteSuppliers";
        await sendEmail({
          placeholders,
          emailType,
          recipientEmails,
          companyKey,
        });
      }
    }
  } catch (error) {
    console.error(error);
  }
};

const getLinkedSuppliers = async (linkedSupplierId, name, company) => {
  try {
    let pool = await sql.connect(config.sql);
    const sqlQueries = await utils.loadSqlQueries("suppliers");
    const list = await pool
      .request()
      .input("linkedSupId", sql.Int, linkedSupplierId)
      .input("company", sql.VarChar, company)
      .query(sqlQueries.suppliersListLinked);
    return list.recordset;
  } catch (error) {
    console.error(error);
    logger.error({
      username: name,
      type: "error",
      description: `Failed to get linked Suppliers:${error}`,
      module_id: 1,
    });
    return error.message;
  }
};

const getProductByProphet = async (prophet_id) => {
  try {
    let pool = await sql.connect(config.sql);
    const sqlQueries = await utils.loadSqlQueries("suppliers");
    const list = await pool
      .request()
      .input("prophet_id", sql.Int, prophet_id)
      .query(sqlQueries.getProductByProphet);
    return list.recordset;
  } catch (error) {
    console.error(error);

    return error.message;
  }
};

const getSupplierRoles = async (name) => {
  try {
    let pool = await sql.connect(config.sql);
    const sqlQueries = await utils.loadSqlQueries("suppliers");

    const list = await pool.request().query(sqlQueries.getSupplierRole);
    return list.recordset;
  } catch (error) {
    console.error(error);

    logger.error({
      username: name,
      type: "error",
      description: `Failed to get Suppliers roles:${error}`,
      module_id: 1,
    });
    return error.message;
  }
};
const getSupplierTypes = async (name) => {
  try {
    let pool = await sql.connect(config.sql);
    const sqlQueries = await utils.loadSqlQueries("suppliers");

    const list = await pool.request().query(sqlQueries.getSupplierTypes);
    return list.recordset;
  } catch (error) {
    console.error(error);

    logger.error({
      username: name,
      type: "error",
      description: `Failed to get Suppliers roles:${error}`,
      module_id: 1,
    });
    return error.message;
  }
};
const getRolePermissions = async (name) => {
  try {
    let pool = await sql.connect(config.sql);
    const sqlQueries = await utils.loadSqlQueries("suppliers");

    const list = await pool.request().query(sqlQueries.getRolePermissions);
    return list.recordset;
  } catch (error) {
    console.error(error);

    logger.error({
      username: name,
      type: "error",
      description: `Failed to get role permissions:${error}`,
      module_id: 1,
    });
    return error.message;
  }
};

const getSupplierLinks = async (name, company) => {
  try {
    let pool = await sql.connect(config.sql);
    const sqlQueries = await utils.loadSqlQueries("suppliers");
    const list = await pool
      .request()
      .input("company", sql.VarChar, company)
      .query(sqlQueries.getSupplierLink);
    return list.recordset;
  } catch (error) {
    console.error(error);

    logger.error({
      username: name,
      type: "error",
      description: `Failed to get Suppliers Links:${error}`,
      module_id: 1,
    });
    return error.message;
  }
};

const getSupplierById = async (id, name) => {
  try {
    let pool = await sql.connect(config.sql);
    const sqlQueries = await utils.loadSqlQueries("suppliers");
    const supplier = await pool
      .request()
      .input("id", sql.Int, id)
      .query(sqlQueries.supplierById);
    if (
      supplier.recordset.length > 0 &&
      supplier.recordset[0]?.account_number
    ) {
      supplier.recordset[0].decryptedAccountNumber = decryptData(
        supplier.recordset[0]?.account_number
      );
    }

    if (supplier.recordset.length > 0 && supplier.recordset[0]?.sort_bic) {
      supplier.recordset[0].decryptedSort_Bic = decryptData(
        supplier.recordset[0]?.sort_bic
      );
    }

    if (supplier.recordset.length > 0 && supplier.recordset[0]?.name_branch) {
      supplier.recordset[0].decryptedName_branch = decryptData(
        supplier.recordset[0]?.name_branch
      );
    }

    if (
      supplier.recordset.length > 0 &&
      supplier.recordset[0]?.intermediatery_account_number
    ) {
      supplier.recordset[0].decryptedIntermediatery_account_number =
        decryptData(supplier.recordset[0]?.intermediatery_account_number);
    }

    return supplier.recordset;
  } catch (error) {
    console.error(error);

    logger.error({
      username: name,
      type: "error",
      description: `Failed to get Suppliers by id:${error}`,
      module_id: 1,
    });
    return error.message;
  }
};

const getFilteredSupplierNames = async (value) => {
  try {
    let pool = await sql.connect(config.sql);
    const sqlQueries = await utils.loadSqlQueries("suppliers");

    const supplier = await pool
      .request()
      .input("value", sql.VarChar, value)
      .query(sqlQueries.getFilteredSupplierNames);
    return supplier.recordset;
  } catch (error) {
    console.error(error);

    logger.error({
      username: null,
      type: "error",
      description: `Failed to get filtered supplier names:${error}`,
      module_id: 1,
    });
    return error.message;
  }
};

const getIfUniqueSupplierCode = async (value, supplierId) => {
  try {
    let pool = await sql.connect(config.sql);
    const sqlQueries = await utils.loadSqlQueries("suppliers");

    const isUnique = await pool
      .request()
      .input("value", sql.VarChar, value)
      .query(sqlQueries.checkUniqueSupplierCode);

    if (
      (isUnique?.recordset?.length === 1 &&
        isUnique?.recordset[0]?.id !== parseInt(supplierId)) ||
      isUnique?.recordset?.length > 1
    ) {
      return false;
    } else {
      return true;
    }
  } catch (error) {
    console.error(error);

    logger.error({
      username: null,
      type: "error",
      description: `Failed to get filtered supplier names:${error}`,
      module_id: 1,
    });
    return error.message;
  }
};
const getFilteredDistribution = async (value, prophet, supplierId) => {
  try {
    let pool = await sql.connect(config.sql);
    const sqlQueries = await utils.loadSqlQueries("suppliers");
    const supplier = await pool
      .request()
      .input("value", sql.VarChar, value)
      .input("prophet", sql.Int, prophet)
      .query(sqlQueries.getDistributionPoints);

    const filteredDistributionPoints = supplier.recordset?.filter((point) => {
      if (point?.supplier_id !== null) {
        return point?.isActive === true;
      } else {
        return true;
      }
    });

    const filteredBySupplierId = filteredDistributionPoints?.filter((point) => {
      if (point?.from_dp !== null) {
        const sameDpCodeEntries = filteredDistributionPoints?.filter(
          (p) => p?.from_dp === point?.from_dp
        );
        if (sameDpCodeEntries?.length > 1) {
          if (point?.supplier_id !== null && point?.supplier_id == supplierId) {
            return true;
          }
          return false;
        } else {
          return true;
        }
      } else {
        if (point?.supplier_id !== null && point?.supplier_id == supplierId) {
          return true;
        } else if (point?.supplier_id === null) {
          return true;
        }
      }
      return false;
    });

    return filteredBySupplierId;
  } catch (error) {
    console.error(error);

    logger.error({
      username: null,
      type: "error",
      description: `Failed to get filtered supplier names:${error}`,
      module_id: 1,
    });
    return error.message;
  }
};
const checkDistribution = async (prophet, supplierId, distributionPoint) => {
  try {
    let pool = await sql.connect(config.sql);
    const sqlQueries = await utils.loadSqlQueries("suppliers");

    const supplier = await pool
      .request()
      .input("value", sql.NVarChar, distributionPoint)
      .input("supplier_id", sql.Int, supplierId)
      .input("prophet", sql.Int, prophet)
      .query(sqlQueries.getDistributionPoints);
    return supplier.recordset;
  } catch (error) {
    console.error(error);

    logger.error({
      username: null,
      type: "error",
      description: `Failed to get filtered supplier names:${error}`,
      module_id: 1,
    });
    return error.message;
  }
};

const getLinksByProphets = async (value, name) => {
  try {
    let pool = await sql.connect(config.sql);
    const sqlQueries = await utils.loadSqlQueries("suppliers");
    const prophet_id = value;

    const getLinks = await pool
      .request()
      .input("prophet_id", sql.NVarChar, prophet_id)
      .query(sqlQueries.getLinksByProphet);

    return getLinks.recordset;
  } catch (error) {
    console.error(error);

    return error.message;
  }
};

const getSendacGroupByProphets = async (value) => {
  try {
    let pool = await sql.connect(config.sql);
    const sqlQueries = await utils.loadSqlQueries("suppliers");
    const prophetsArray = value?.join(",");
    let dpsdata = [];
    let dpsmsdata = [];
    const dps = await pool
      .request()
      .input("prophetsArray", sql.NVarChar, prophetsArray)
      .query(sqlQueries.getSandacGroupByProphet);
    return dps.recordset;
  } catch (error) {
    console.error(error);

    return error.message;
  }
};

const getSendacLinksBySendac = async (value, email, name) => {
  try {
    let pool = await sql.connect(config.sql);
    const sqlQueries = await utils.loadSqlQueries("suppliers");

    const sendacGroup = await pool
      .request()
      .input("sendac_id", sql.Int, value)
      .query(sqlQueries.getSendacLinksBySendac);

    return sendacGroup.recordset;
  } catch (error) {
    console.error(error);

    logger.error({
      username: name,
      type: "error",
      description: `Failed to get links by sendac:${error}`,
      module_id: 1,
    });
    return error.message;
  }
};

const createSendacGroup = async (groupData, name) => {
  try {
    let pool = await sql.connect(config.sql);
    const sqlQueries = await utils.loadSqlQueries("suppliers");

    const checkExistingGroupName = await pool
      .request()
      .input("name", sql.VarChar, groupData?.group_name.trim())
      .input("prophet_id", sql.Int, groupData?.prophet_id[0][0])
      .query(sqlQueries.checkGroupName);

    if (checkExistingGroupName.recordset.length > 0) {
      return { data: "exists" };
    } else {
      const insertSendacGroup = await pool
        .request()
        .input("value", sql.VarChar, null)
        .input("group_name", sql.VarChar, groupData?.group_name)
        .input("prophet_id", sql.Int, groupData?.prophet_id[0][0])
        .input("is_active", sql.Bit, 1)
        .query(sqlQueries.createSendacGroup);
      return insertSendacGroup.recordset;
    }
  } catch (error) {
    console.error(error);

    logger.error({
      username: name,
      type: "error",
      description: `Failed to create sendac groups:${error}`,
      module_id: 1,
    });
    return error.message;
  }
};

const createSupplier = async (supplierData, name, email) => {
  let transaction;
  let supplier_id;
  try {
    let pool = await sql.connect(config.sql);
    transaction = new sql.Transaction(pool);
    await transaction.begin();
    const sqlQueries = await utils.loadSqlQueries("suppliers");

    const insertSupplier = await pool
      .request(transaction)
      .input("name", sql.VarChar, supplierData?.name)
      .input("emergency_request", sql.Bit, supplierData?.emergency_request)
      .input("gdpr_compliant", sql.Bit, supplierData?.gdpr_compliant)
      .input("product_supplier", sql.Bit, supplierData?.product_supplier)
      .input("created_date", sql.DateTime, supplierData?.created_date)
      .input("updated_date", sql.DateTime, supplierData?.updated_date)
      .input("status", sql.Int, supplierData?.status)
      .input("requestor_name", sql.VarChar, supplierData?.requestor_name)
      .input("requestor_email", sql.VarChar, supplierData?.requestor_email)
      .input("company", sql.VarChar, supplierData?.company)
      .input("supplier_type", sql.Int, supplierData?.supplier_type)
      .input(
        "is_code_system_generated",
        sql.Bit,
        supplierData?.isCodeSystemGenerated
      )
      .query(sqlQueries.createSupplier);

    const result = insertSupplier.recordset;
    supplier_id = result[0]?.id;
    const supplier_roles = supplierData?.roles;
    const supplier_prophets = supplierData?.prophets;
    const supplier_sendac_groups = supplierData?.sendac_groups;
    const isInaactiveGroup = supplierData?.isInactiveGroup;

    const supplier_links = supplierData?.supplier_links;

    if (supplier_roles?.length > 0) {
      supplier_roles?.map((item) => {
        let createRoleData = {
          role_id: item,
          supplier_id: supplier_id,
        };

        const createSupplierByRole = pool
          .request(transaction)
          .input("role_id", sql.Int, createRoleData?.role_id)
          .input("supplier_id", sql.Int, createRoleData?.supplier_id)
          .query(sqlQueries.createSupplierByRole);
      });
    }

    supplier_prophets?.map((item) => {
      let createProphetData = {
        prophet_id: item[0],
        prophet_code: item[1]?.trim(),
        supplier_id: supplier_id,
      };

      const createSupplierByProphet = pool
        .request(transaction)
        .input("prophet_id", sql.Int, createProphetData?.prophet_id)
        .input("prophet_code", sql.VarChar, createProphetData?.prophet_code)
        .input("supplier_id", sql.Int, createProphetData?.supplier_id)
        .query(sqlQueries.createSupplierByProphet);
    });

    // new supplier
    // new group or existing group
    // links are the ones which are not in any group
    // so we just have to put them in that selected/new group
    const newSendacGroup = supplierData.newSendacGroup;
    if (newSendacGroup) {
      if (supplier_sendac_groups?.length > 0) {
        supplier_sendac_groups?.map((item) => {
          let createSendacGroupData = {
            value: item?.value,
            group_name: item?.label,
            prophet_id:
              supplier_prophets.length > 0 ? supplier_prophets[0][0] : null,
            isActive: 1,
            supplier_id: supplier_id,
          };

          const createSendacGroup = pool
            .request(transaction)
            .input("value", sql.NVarChar, createSendacGroupData?.value)
            .input(
              "group_name",
              sql.NVarChar,
              createSendacGroupData?.group_name
            )
            .input("prophet_id", sql.Int, createSendacGroupData?.prophet_id)
            .input("is_active", sql.Bit, createSendacGroupData?.isActive)
            .input("created_by", sql.Int, createSendacGroupData?.supplier_id)
            .query(sqlQueries.createSendacGroup);

          createSendacGroup.then(function (result) {
            const sendac_group_id = result.recordset[0]?.id;
            const createSupplierBySendacGroup = pool
              .request(transaction)
              .input("group_id", sql.Int, sendac_group_id)
              .input("supplier_id", sql.Int, supplier_id)
              .query(sqlQueries.createSupplierBySendacGroup);
          });
        });
      }
    } else {
      if (supplier_sendac_groups?.length > 0) {
        if (isInaactiveGroup) {
          const updateInActiveSendacGroup = pool
            .request(transaction)
            .input("value", sql.Int, supplier_sendac_groups[0]?.value)
            .input("prophet_id", sql.Int, supplierData?.prophetCodeNum)
            .input("is_active", sql.Bit, 1)
            .query(sqlQueries.updateInActiveSendacGroup);
        }
        const createSupplierBySendacGroup = pool
          .request(transaction)
          .input("group_id", sql.Int, supplier_sendac_groups[0]?.value)
          .input("supplier_id", sql.Int, supplier_id)
          .query(sqlQueries.createSupplierBySendacGroup);

        if (supplier_links?.length > 0) {
          for (const link of supplier_links) {
            await pool
              .request(transaction)
              .input("group_id", sql.Int, supplier_sendac_groups[0]?.value)
              .input("supplier_id", sql.Int, link?.value)
              .query(sqlQueries.createSupplierBySendacGroup);
          }
        }
      }
    }

    try {
      await transaction.commit();
      if (supplierData?.copy) {
        logger.info({
          username: name,
          type: "success",
          description: `User with email ${email} copied ${supplierData?.prevCompanyName} with new name ${supplierData?.name}`,
          item_id: supplier_id,
          module_id: 1,
        });
      } else {
        logger.info({
          username: name,
          type: "success",
          description: `User with email ${email} added new supplier with name ${supplierData?.name}`,
          item_id: supplier_id,
          module_id: 1,
        });
      }
    } catch (commitError) {
      logger.error({
        username: null,
        type: "error",
        description: `Failed to create Suppliers:${commitError}`,
        module_id: 1,
      });
    }
    // return result;
    return {
      status: 201,
      data: result,
    };
  } catch (error) {
    console.error("error", error);

    if (transaction) {
      await transaction.rollback();
      logger.info({
        username: name ? name : null,
        type: "error",
        description: `User with email ${
          email ? email : null
        } failed to add new supplier: ${error.message}`,
        item_id: supplier_id,
        module_id: 1,
      });
    }
    return {
      status: 400,
      message: error,
    };
  } finally {
    if (transaction) {
      transaction = null;
    }
  }
};

const moveLinks = async (id, data, name, email) => {
  let transaction;
  try {
    let pool = await sql.connect(config.sql);
    transaction = new sql.Transaction(pool);
    await transaction.begin();
    const sqlQueries = await utils.loadSqlQueries("suppliers");
    const supplier_links = data?.links;
    let supplier_sendac_groups;

    if (Array.isArray(data?.selectedSendacGroup)) {
      supplier_sendac_groups = data?.selectedSendacGroup[0];
    } else {
      supplier_sendac_groups = data?.selectedSendacGroup;
    }

    if (supplier_sendac_groups?.value == null) {
      const getendacGroupId = await pool
        .request(transaction)
        .input("group_name", sql.VarChar, supplier_sendac_groups?.label)
        .query(sqlQueries.getSendacGroupId);

      const group_id = getendacGroupId.recordset[0]?.id;

      supplier_sendac_groups.value = group_id;
    }

    let idArrs = [];
    supplier_links?.map((ele) => idArrs?.push(ele?.value));

    idArrs?.push(parseInt(id));

    for (let ele of idArrs) {
      if (supplier_sendac_groups) {
        const deleteSendacGroupBySupplier = await pool
          .request(transaction)
          .input("supplier_id", sql.Int, ele)
          .query(sqlQueries.deleteSendacGroupBySupplier);

        const createSendacGroup = await pool
          .request(transaction)
          .input("group_id", sql.Int, supplier_sendac_groups?.value)
          .input("supplier_id", sql.Int, ele)
          .query(sqlQueries.createSupplierBySendacGroup);
      }
    }

    await transaction.commit();
    let description = "";
    if (data?.oldSendac) {
      description = `User with email ${email} changed sendac group from ${
        data?.oldSendac[0]?.label
      } to ${data?.selectedSendacGroup[0]?.label} and moved links: ${data?.links
        ?.map((link) => link?.label)
        ?.join(", ")}`;
    } else {
      description = `User with email ${email} added new sendac group ${
        data?.selectedSendacGroup[0]?.label
      } and removed links: ${data?.links
        ?.map((link) => link?.label)
        ?.join(", ")}`;
    }

    logger.info({
      username: name,
      type: "success",
      description: description,
      item_id: id,
      module_id: 1,
    });
    return { status: 200 };
  } catch (error) {
    console.error(error);
    if (transaction) {
      logger.error({
        username: null,
        type: "error",
        description: `Failed to move links:${error}`,
        module_id: 1,
      });
      await transaction.rollback();
    }
    return { status: 400 };
  }
};

const removeLinks = async (id, data, name, email) => {
  let transaction;
  try {
    let pool = await sql.connect(config.sql);
    transaction = new sql.Transaction(pool);
    await transaction.begin();

    const sqlQueries = await utils.loadSqlQueries("suppliers");
    const supplier_links = data?.links;

    let supplier_sendac_groups;

    if (Array.isArray(data?.selectedSendacGroup)) {
      supplier_sendac_groups = data?.selectedSendacGroup[0];
    } else {
      supplier_sendac_groups = data?.selectedSendacGroup;
    }

    if (supplier_links?.length > 0) {
      for (const link of supplier_links) {
        const deleteSendacGroupBySupplier = await pool
          .request(transaction)
          .input("supplier_id", sql.Int, link?.value)
          .query(sqlQueries.deleteSendacGroupBySupplier);
      }
    }
    let description = "";
    if (data?.oldSendac) {
      description = `User with email ${email} changed sendac group from ${
        data?.oldSendac[0]?.label
      } to ${
        data?.selectedSendacGroup[0]?.label
      } and removed links: ${data?.links
        ?.map((link) => link?.label)
        ?.join(", ")}`;
    } else {
      description = `User with email ${email} added new sendac group ${
        data?.selectedSendacGroup[0]?.label
      } and removed links: ${data?.links
        ?.map((link) => link?.label)
        ?.join(", ")}`;
    }

    logger.info({
      username: name,
      type: "success",
      description: description,
      item_id: id,
      module_id: 1,
    });
    return { status: 200 };
  } catch (error) {
    console.error(error);

    if (transaction) {
      await transaction.rollback();
    }
    logger.error({
      username: null,
      type: "error",
      description: `Failed to remove links:${error}`,
      module_id: 1,
    });
    return { status: 400 };
  }
};

const updateSupplier = async (id, supplierData, email, name) => {
  let transaction;
  try {
    let pool = await sql.connect(config.sql);
    transaction = new sql.Transaction(pool);
    await transaction.begin();

    const sqlQueries = await utils.loadSqlQueries("suppliers");

    const supplier = await pool
      .request()
      .input("id", sql.Int, id)
      .query(sqlQueries.supplierById);

    const previousData = supplier?.recordset[0];
    let roles = JSON.parse(previousData?.role_ids ?? "[]");
    roles = roles?.map((ele) => ele?.role_id);
    const isInaactiveGroup = supplierData?.isInactiveGroup;
    const prophetCodeNum = supplierData?.prophetCodeNum;
    previousData.roles = roles;

    const parsedProphetsIdCode = JSON.parse(
      previousData?.prophets_id_code ?? "[]"
    );
    const formattedProphetsIdCode = parsedProphetsIdCode?.map(
      ({ prophet_id, prophet_code }) => [prophet_id, prophet_code?.trim()]
    );

    previousData.prophets = formattedProphetsIdCode;

    const parsedSupplierSendacGroup = JSON.parse(
      previousData?.supplier_sendac_group_json ?? "[]"
    );

    // Map each object to the required format
    const formattedSendacGroups = parsedSupplierSendacGroup?.map(
      ({ group_id, group_name }) => ({
        value: group_id,
        label: group_name,
      })
    );

    previousData.sendac_groups = formattedSendacGroups;
    const parsedSupplierLinks = JSON.parse(
      previousData?.supplier_links_json ?? "[]"
    );

    // Map each object to the required format
    const formattedSupplierLinks = parsedSupplierLinks?.map(
      ({ link_id, name, role_id, role_names }) => ({
        value: link_id,
        label: `${name} | ${role_names}`,
        role_id: [role_id],
      })
    );

    previousData.supplier_links = formattedSupplierLinks;

    const fieldMapping = {
      name: "name",
      prophets: "prophets",
      emergency_request: "emergency_request",
      gdpr_compliant: "gdpr_compliant",
      product_supplier: "product_supplier",
      roles: "roles",
    };

    function compareFields(previousData, currentData) {
      const differences = {};

      Object.entries(fieldMapping)?.forEach(([fieldName, key]) => {
        const previousValue = previousData[fieldName];
        const currentValue = currentData[key];

        // Compare values if both fields exist
        if (previousValue !== undefined && currentValue !== undefined) {
          if (Array.isArray(previousValue) && Array.isArray(currentValue)) {
            // Compare arrays
            if (
              JSON.stringify(previousValue) !== JSON.stringify(currentValue)
            ) {
              differences[fieldName] = {
                previousValue,
                newValue: currentValue,
              };
            }
          } else if (previousValue !== currentValue) {
            // Compare non-array values
            differences[fieldName] = {
              previousValue,
              newValue: currentValue,
            };
          }
        }
      });

      return differences;
    }
    const differences = compareFields(previousData, supplierData);
    differences.prophets = {
      previousValue: differences?.prophets?.previousValue?.map(
        ([id, value]) => [id, value]
      ),
      newValue: differences?.prophets?.newValue?.map(([id, value]) => [
        id,
        value,
      ]),
    };

    const extractedData = supplier?.recordset?.map((entry) => ({
      name: entry?.name,
      emergency_request: entry?.emergency_request,
      gdpr_compliant: entry?.gdpr_compliant,
      product_supplier: entry?.product_supplier,
      roles: JSON.parse(entry?.role_ids ?? "[]")?.map(({ role_id }) => role_id),
      supplier_links: JSON.parse(entry?.supplier_links_json ?? "[]"),
      updated_date: entry?.updated_date,
      status: entry?.status,
    }));
    const updateSupplier = await pool
      .request()
      .input("id", sql.Int, id)
      .input("name", sql.VarChar, supplierData?.name)
      .input("emergency_request", sql.Bit, supplierData?.emergency_request)
      .input("gdpr_compliant", sql.Bit, supplierData?.gdpr_compliant)
      .input("product_supplier", sql.Bit, supplierData?.product_supplier)
      .input("updated_date", sql.DateTime, supplierData?.updated_date)
      .input("status", sql.Int, supplierData?.status)
      .input("supplier_type", sql.Int, supplierData?.supplier_type)
      .input(
        "is_code_system_generated",
        sql.Bit,
        supplierData?.isCodeSystemGenerated
      )
      .query(sqlQueries.updateSupplier);

    const supplier_roles = supplierData.roles;

    if (supplier_roles.length > 0) {
      const checkExistingRoles = await pool
        .request()
        .input("id", sql.Int, id)
        .query(sqlQueries.roleListBySupplier);

      if (checkExistingRoles.recordset.length > 0) {
        const deleteRoleBySupplier = await pool
          .request(transaction)
          .input("supplier_id", sql.Int, id)
          .query(sqlQueries.deleteRoleBySupplier);

        for (const role of supplier_roles) {
          await pool
            .request(transaction)
            .input("role_id", sql.Int, role)
            .input("supplier_id", sql.Int, id)
            .query(sqlQueries.createSupplierByRole);
        }
      } else {
        for (const role of supplier_roles) {
          await pool
            .request(transaction)
            .input("role_id", sql.Int, role)
            .input("supplier_id", sql.Int, id)
            .query(sqlQueries.createSupplierByRole);
        }
      }
    }

    const supplier_prophets = supplierData?.prophets;
    if (supplier_prophets?.length > 0) {
      const checkExistingProphets = await pool
        .request()
        .input("id", sql.Int, id)
        .query(sqlQueries.prophetListBySupplier);
      if (checkExistingProphets.recordset?.length > 0) {
        const deleteProphetBySupplier = await pool
          .request(transaction)
          .input("supplier_id", sql.Int, id)
          .query(sqlQueries.deleteProphetBySupplier);

        for (const prophet of supplier_prophets) {
          await pool
            .request(transaction)
            .input("prophet_id", sql.Int, prophet[0])
            .input("prophet_code", sql.VarChar, prophet[1])
            .input("supplier_id", sql.Int, id)
            .query(sqlQueries.createSupplierByProphet);
        }
      } else {
        for (const prophet of supplier_prophets) {
          await pool
            .request(transaction)
            .input("prophet_id", sql.Int, prophet[0])
            .input("prophet_code", sql.VarChar, prophet[1])
            .input("supplier_id", sql.Int, id)
            .query(sqlQueries.createSupplierByProphet);
        }
      }
    }

    const newSendacGroup = supplierData?.newSendacGroup;

    const supplier_sendac_groups = supplierData?.sendac_groups;
    //   move or remove 0 and not new group
    // move or remove 0 and new group
    // move or remove 1 and new group
    // move or remove 1 and not new group

    //   if new sendac group is added then below if condition runs

    if (newSendacGroup) {
      // below if loop creates new sendac group and adds the supplier in it
      if (supplier_sendac_groups?.length > 0) {
        supplier_sendac_groups?.map((item) => {
          let createSendacGroupData = {
            value: item?.value,
            group_name: item?.label,
            prophet_id: supplier_prophets[0][0],
            isActive: 1,
            supplier_id: id,
          };

          const createSupplierBySendacGroup = pool
            .request(transaction)
            .input("value", sql.NVarChar, createSendacGroupData?.value)
            .input(
              "group_name",
              sql.NVarChar,
              createSendacGroupData?.group_name
            )
            .input("prophet_id", sql.Int, createSendacGroupData?.prophet_id)
            .input("created_by", sql.Int, createSendacGroupData?.supplier_id)
            .input("is_active", sql.Bit, createSendacGroupData?.isActive)
            .query(sqlQueries.createSendacGroup);

          createSupplierBySendacGroup.then(function (result) {
            const sendac_group_id = result?.recordset[0]?.id;
            const checkExistingGroups = pool
              .request()
              .input("id", sql.Int, id)
              .query(sqlQueries.sendacGroupListBySupplier);

            checkExistingGroups.then(function (result) {
              if (result.recordset?.length > 0) {
                const deleteSendacGroupBySupplier = pool
                  .request(transaction)
                  .input("supplier_id", sql.Int, id)
                  .query(sqlQueries.deleteSendacGroupBySupplier);
              }
              const createSupplierBySendacGroup = pool
                .request(transaction)
                .input("group_id", sql.Int, sendac_group_id)
                .input("supplier_id", sql.Int, id)
                .query(sqlQueries.createSupplierBySendacGroup);
            });
          });
        });
        // after creating new group and adding the supplier to the group below one of operation will run to remove or move
        // need to fix this
        if (supplierData?.moveOrRemoveLinks) {
          const getMoveRemoveLinks = supplierData?.moveOrRemoveLinks;
          if (
            getMoveRemoveLinks?.sectionName == "sendacOperation" ||
            getMoveRemoveLinks?.sectionName == "moveSendacOperation"
          ) {
            moveLinks(id, getMoveRemoveLinks, name, email);
          }
          if (getMoveRemoveLinks?.sectionName == "removeSendacOperation") {
            removeLinks(id, getMoveRemoveLinks, name, email);
          }
        }
      }
    } else {
      // if it is not a new sendac group then this else part runs
      // below if user has selected sendac group then below if loop runs
      if (supplier_sendac_groups?.length > 0) {
        if (isInaactiveGroup) {
          const updateInActiveSendacGroup = await pool
            .request(transaction)
            .input("value", sql.Int, supplier_sendac_groups[0]?.value)
            .input("prophet_id", sql.Int, prophetCodeNum)
            .input("is_active", sql.Bit, 1)
            .query(sqlQueries.updateInActiveSendacGroup);
        }
        // check if that supplier is part of any group and if exist then delete and insert insert into the selected group
        const checkExistingGroups = await pool
          .request()
          .input("id", sql.Int, id)
          .query(sqlQueries.sendacGroupListBySupplier);

        if (checkExistingGroups.recordset?.length > 0) {
          const deleteSendacGroupBySupplier = await pool
            .request(transaction)
            .input("supplier_id", sql.Int, id)
            .query(sqlQueries.deleteSendacGroupBySupplier);

          await pool
            .request(transaction)
            .input("group_id", sql.Int, supplier_sendac_groups[0]?.value)
            .input("supplier_id", sql.Int, id)
            .query(sqlQueries.createSupplierBySendacGroup);
        } else {
          // if the supplier is not part of any sendac group then directly insert in the selected group
          await pool
            .request(transaction)
            .input("group_id", sql.Int, supplier_sendac_groups[0]?.value)
            .input("supplier_id", sql.Int, id)
            .query(sqlQueries.createSupplierBySendacGroup);
        }

        //   linking part
        // from supplier links array put them in that sendac group
        // run a for loop over the supplier links array and first check if that link id is present in the sendac group table and if it is present then delete and insert with this new group id which is selected else directly insert in that group id

        if (
          supplierData?.supplier_links?.length === 0 &&
          supplierData?.sendac_groups?.length > 0
        ) {
          // Query to check if there are any existing suppliers
          const checkExistingLinksByGroupId = await pool
            .request()
            .input("group_id", sql.Int, supplierData?.sendac_groups[0]?.value)
            .query(sqlQueries.getSendacGroupsByGroupId);

          if (
            checkExistingLinksByGroupId.recordset?.length > 0 &&
            supplierData?.removedLinks?.length > 0
          ) {
            for (const obj of checkExistingLinksByGroupId.recordset) {
              if (obj?.supplier_id != id) {
                const deleteSendacGroupByGroupId = await pool
                  .request(transaction)
                  .input("supplier_id", sql.Int, obj?.supplier_id)
                  .query(sqlQueries.deleteSendacGroupBySupplier);
              }
            }
          }
        } else {
          if (supplierData.roles?.includes(1)) {
            const checkExistingLinksByGroupId = await pool
              .request()
              .input("group_id", sql.Int, supplierData?.sendac_groups[0]?.value)
              .query(sqlQueries.getSendacGroupsByGroupId);

            const initialSupplierIds =
              checkExistingLinksByGroupId.recordset.map((s) => s.supplier_id);
            const previousLinks = initialSupplierIds.filter((s) => s != id);
            for (const supplierLinkId of previousLinks) {
              if (!supplierData?.supplier_links?.includes(supplierLinkId)) {
                const deleteSendacGroupBySupplier = await pool
                  .request(transaction)
                  .input("supplier_id", sql.Int, supplierLinkId)
                  .query(sqlQueries.deleteSendacGroupBySupplier);
              }
            }
            for (const link of supplierData?.supplier_links) {
              const checkExistingLinksBySendac = await pool
                .request()
                .input("supplier_id", sql.Int, link?.value)
                .query(sqlQueries.getSuppliersBySendac);

              // if it exist in the then check if the group id is same as the one which is selected and if it is same then dont do anything else delete and insert

              if (checkExistingLinksBySendac.recordset?.length > 0) {
                // if the selected group is different from the group in which that link exist then delete and insert

                const deleteSendacGroupBySupplier = await pool
                  .request(transaction)
                  .input("supplier_id", sql.Int, link?.value)
                  .query(sqlQueries.deleteSendacGroupBySupplier);

                await pool
                  .request(transaction)
                  .input("group_id", sql.Int, supplier_sendac_groups[0]?.value)
                  .input("supplier_id", sql.Int, link?.value)
                  .query(sqlQueries.createSupplierBySendacGroup);
              } else {
                await pool
                  .request(transaction)
                  .input("group_id", sql.Int, supplier_sendac_groups[0]?.value)
                  .input("supplier_id", sql.Int, link?.value)
                  .query(sqlQueries.createSupplierBySendacGroup);
              }
            }
            // else dont do anything as it means it is in that selected group only
          }
        }
        if (supplierData?.moveOrRemoveLinks) {
          const getMoveRemoveLinks = supplierData?.moveOrRemoveLinks;
          if (getMoveRemoveLinks?.sectionName == "sendacOperation") {
            moveLinks(id, getMoveRemoveLinks, name, email);
          }
          if (getMoveRemoveLinks?.sectionName == "removeSendacOperation") {
            removeLinks(id, getMoveRemoveLinks, name, email);
          }
        }
      } else {
        const checkExistingGroups = await pool
          .request()
          .input("id", sql.Int, id)
          .query(sqlQueries.sendacGroupListBySupplier);

        if (checkExistingGroups.recordset?.length > 0) {
          const deleteSendacGroupBySupplier = await pool
            .request(transaction)
            .input("supplier_id", sql.Int, id)
            .query(sqlQueries.deleteSendacGroupBySupplier);
        }
      }
    }

    const deleteFlags = supplierData?.deleteFlags;
    if (!deleteFlags?.technical) {
      const deleteGeneral = await pool
        .request(transaction)
        .input("id", sql.Int, id)
        .input("name", sql.VarChar, null)
        .input("trading_name", sql.VarChar, supplierData?.trading_name)
        .input("email_id", sql.VarChar, null)
        .input("facsimile", sql.VarChar, null)
        .input("telephone", sql.VarChar, null)
        .input("address_line_1", sql.VarChar, null)
        .input("address_line_2", sql.VarChar, null)
        .input("address_line_3", sql.VarChar, null)
        .input("address_line_4", sql.VarChar, null)
        .input("country", sql.VarChar, null)
        .input("postal_code", sql.VarChar, null)
        .input("updated_date", sql.DateTime, supplierData?.updated_date)
        .input("technical", sql.VarChar, null)
        .input("status", sql.Int, supplierData?.status)
        .query(sqlQueries.updateGeneralSection);

      const checkExistingContacts = await pool
        .request()
        .input("supplier_id", sql.Int, id)
        .query(sqlQueries.contactsList);

      if (checkExistingContacts.recordset?.length > 0) {
        const deleteContactcs = await pool
          .request(transaction)
          .input("supplier_id", sql.Int, id)
          .query(sqlQueries.deleteContacts);
      }
    }

    if (!deleteFlags.financial) {
      const deleteFinancials = await pool
        .request(transaction)
        .input("id", sql.Int, id)
        .input("currency", sql.VarChar, null)
        .input("financial", sql.VarChar, null)
        .input("vat_number", sql.VarChar, null)
        .input("company_registration", sql.VarChar, null)
        .input("country_code", sql.VarChar, null)
        .input("payment_terms", sql.VarChar, null)
        .input("payment_type", sql.VarChar, null)
        .input("validated_procurement_team", sql.Bit, false)
        .input("finance_authorization", sql.Int, 2)
        .input("rejected_reason", sql.VarChar, null)
        .input("finance_authorization_by", sql.VarChar, null)
        .input("finance_authorization_date", sql.DateTime, null)
        .input("validated_by", sql.VarChar, null)
        .input("updated_date", sql.DateTime, supplierData?.updated_date)
        .input("vatable", sql.Int, null)
        .input("validated_date", sql.DateTime, null)
        .input("status", sql.Int, supplierData?.status)

        .query(sqlQueries.updateFinancialsSection);

      const checkExistingDistribution = await pool
        .request()
        .input("supplier_id", sql.Int, id)
        .query(sqlQueries.distributionPointListByID);

      if (checkExistingDistribution.recordset?.length > 0) {
        const deleteDistributionPoint = await pool
          .request(transaction)
          .input("supplier_id", sql.Int, id)
          .query(sqlQueries.deleteDistributionList);
      }
      const checkExistingDeliveries = await pool
        .request()
        .input("supplier_id", sql.Int, id)
        .query(sqlQueries.deliveryListByID);

      if (checkExistingDeliveries.recordset?.length > 0) {
        const deleteDelivery = await pool
          .request(transaction)
          .input("supplier_id", sql.Int, id)
          .query(sqlQueries.deleteDelivery);
      }
      const checkExistingAccounts = await pool
        .request()
        .input("supplier_id", sql.Int, id)
        .query(sqlQueries.bankAccListByID);

      if (checkExistingAccounts.recordset?.length > 0) {
        const deleteAccount = await pool
          .request(transaction)
          .input("supplier_id", sql.Int, id)
          .query(sqlQueries.deleteBankAcc);
      }
    }

    if (!deleteFlags?.procurement) {
      const updateProcurement = await pool
        .request(transaction)
        .input("id", sql.Int, id)
        .input("procurement", sql.VarChar, null)
        .input("updated_date", sql.DateTime, supplierData?.updated_date)
        .input("status", sql.Int, supplierData?.status)

        .query(sqlQueries.updateProcurementCol);
    }

    if (!deleteFlags?.compliance) {
      const updateCompliance = await pool
        .request(transaction)
        .input("id", sql.Int, id)
        .input("compliance", sql.VarChar, null)
        .input("red_tractor", sql.VarChar, null)
        .input("puc_code", sql.VarChar, null)
        .input("chile_certificate_number", sql.VarChar, null)
        .input("organic_certificate_number", sql.VarChar, null)
        .input("global_gap_number", sql.VarChar, null)
        .input("customer_site_code", sql.VarChar, null)
        .input("status", sql.Int, supplierData?.status)
        .input("updated_date", sql.DateTime, supplierData?.updated_date)
        .query(sqlQueries.updateCompliance);

      const checkExistingSendacGroup = await pool
        .request()
        .input("supplier_id", sql.Int, id)
        .query(sqlQueries.checkSendacGroupBySupplier);
    }

    function formatDifferences(differences) {
      let formattedString = "";
      for (const key in differences) {
        const { previousValue, newValue } = differences[key];
        let formattedNewValue = newValue;

        // Handle special formatting for 'prophets' field
        if (key === "prophets") {
          if (newValue && previousValue) {
            const formattedPreviousValue = previousValue
              ?.map((pair) => pair?.join(","))
              ?.join(",");
            formattedNewValue = newValue
              ?.map((pair) => pair?.join(","))
              ?.join(",");
            formattedString += `${key}: previousValue=${formattedPreviousValue}, newValue=${formattedNewValue}\n`;
          }
        } else {
          formattedString += `${key}: previousValue=${previousValue}, newValue=${formattedNewValue}\n`;
        }
      }
      return formattedString;
    }

    const formattedDifferences = formatDifferences(differences);

    try {
      await transaction.commit();

      if (!supplierData?.copy) {
        if (formattedDifferences) {
          logger.info({
            username: name,
            type: "success",
            description: `User with email ${email} updated Supplier data with changes:\n${formattedDifferences}`,
            item_id: id,
            module_id: 1,
          });
        }
      }
    } catch (commitError) {
      return commitError;
    }
    return { status: 200 };
  } catch (error) {
    console.error("error updating supplier", error);

    if (transaction) {
      await transaction.rollback();
      logger.error({
        username: name,
        type: "error",
        description: `User with email ${email} failed to update supplier: ${error.message}`,
        item_id: id,
        module_id: 1,
      });
    }
    return { status: 400 };
  } finally {
    if (transaction) {
      transaction = null;
    }
  }
};

const deleteSupplier = async (id, name) => {
  try {
    let pool = await sql.connect(config.sql);
    const sqlQueries = await utils.loadSqlQueries("suppliers");
    const deleted = await pool
      .request()
      .input("id", sql.Int, id)
      .query(sqlQueries.deleteSupplier);
    return deleted.recordset;
  } catch (error) {
    console.error(error);

    logger.error({
      username: name,
      type: "error",
      description: `User with email ${email} failed to delete supplier: ${error}`,
      item_id: id,
      module_id: 1,
    });
    return error.message;
  }
};

const updateComplianceData = async (id, supplierData, email, name) => {
  let transaction;
  try {
    let pool = await sql.connect(config.sql);
    transaction = new sql.Transaction(pool);
    await transaction.begin();
    const sqlQueries = await utils.loadSqlQueries("suppliers");

    let existingItems = [];
    let roleIds = supplierData?.roleIds;
    let status = supplierData.status;
    const getExistingData = await pool
      .request()
      .input("id", sql.Int, id)
      .query(sqlQueries.supplierById);
    const previousData = getExistingData.recordset[0];
    let prophetId = previousData?.prophets_id_code
      ? JSON.parse(previousData?.prophets_id_code)
      : [];
    let isProphetCodeUnqiue = prophetId[0]?.code_count == 1;
    let companyKey = "";
    if (supplierData?.prophet_id == 1) {
      companyKey = "dpsltd";
    } else if (supplierData?.prophet_id == 2) {
      companyKey = "dpsltd";
    } else if (supplierData?.prophet_id == 3) {
      companyKey = "efcltd";
    } else if (supplierData?.prophet_id == 4) {
      companyKey = "fpp-ltd";
    }
    status = await isSupplierConfirmed(
      supplierData,
      roleIds,
      isProphetCodeUnqiue,
      sqlQueries,
      id,
      status,
      pool,
      companyKey
    );

    if (supplierData?.red_tractor && supplierData?.red_tractor.trim() !== "") {
      const existingRedTractor = await pool
        .request()
        .input("red_tractor", sql.VarChar, supplierData?.red_tractor)
        .query(sqlQueries.getExistingRedTractor);

      if (
        existingRedTractor.recordset?.length > 0 &&
        existingRedTractor.recordset[0]?.red_tractor !== "" &&
        existingRedTractor.recordset[0]?.id != id
      ) {
        existingItems?.push("Red tractor value already exists");
      }
    }

    if (supplierData?.puc_code && supplierData?.puc_code.trim() !== "") {
      const existingPUCCode = await pool
        .request()
        .input("puc_code", sql.VarChar, supplierData?.puc_code)
        .query(sqlQueries.getExistingPUCCode);

      if (
        existingPUCCode.recordset?.length > 0 &&
        existingPUCCode.recordset[0]?.puc_code !== "" &&
        existingPUCCode.recordset[0]?.id != id
      ) {
        existingItems.push("PUC code already exists");
      }
    }

    if (
      supplierData?.customer_site_code &&
      supplierData?.customer_site_code?.trim() !== ""
    ) {
      const existingCustomerSiteCode = await pool
        .request()
        .input(
          "customer_site_code",
          sql.VarChar,
          supplierData?.customer_site_code
        )
        .query(sqlQueries.getExistingCustomerSiteCode);

      if (
        existingCustomerSiteCode.recordset?.length > 0 &&
        existingCustomerSiteCode.recordset[0]?.customer_site_code !== "" &&
        existingCustomerSiteCode.recordset[0]?.id != id
      ) {
        existingItems?.push("Customer site code value already exists");
      }
    }

    if (
      supplierData?.chile_certificate_number &&
      supplierData?.chile_certificate_number?.trim() !== ""
    ) {
      const existingChileCertificateNumber = await pool
        .request()
        .input(
          "chile_certificate_number",
          sql.VarChar,
          supplierData?.chile_certificate_number
        )
        .query(sqlQueries.getExistingChileCertificateNumber);

      if (
        existingChileCertificateNumber.recordset?.length > 0 &&
        existingChileCertificateNumber.recordset[0]
          ?.chile_certificate_number !== "" &&
        existingChileCertificateNumber.recordset[0]?.id != id
      ) {
        existingItems?.push("Chile certificate number already exists");
      }
    }

    if (
      supplierData?.organic_certificate_number &&
      supplierData?.organic_certificate_number?.trim() !== ""
    ) {
      const existingOrganicCertNumber = await pool
        .request()
        .input(
          "organic_certificate_number",
          sql.VarChar,
          supplierData?.organic_certificate_number
        )
        .query(sqlQueries.getExistingOrganicCertNumber);

      if (
        existingOrganicCertNumber.recordset?.length > 0 &&
        existingOrganicCertNumber.recordset[0]?.organic_certificate_number !==
          "" &&
        existingOrganicCertNumber.recordset[0]?.id != id
      ) {
        existingItems?.push("Organic certificate number already exists");
      }
    }

    if (existingItems?.length > 0) {
      return { hasExistingItems: true, items: existingItems?.join(", ") };
    }

    const updateSupplier = await pool
      .request(transaction)
      .input("id", sql.Int, id)
      .input("compliance", sql.VarChar, supplierData?.compliance)
      .input("red_tractor", sql.VarChar, supplierData?.red_tractor)
      .input("puc_code", sql.VarChar, supplierData?.puc_code)
      .input(
        "chile_certificate_number",
        sql.VarChar,
        supplierData?.chile_certificate_number
      )
      .input(
        "organic_certificate_number",
        sql.VarChar,
        supplierData?.organic_certificate_number
      )
      .input("global_gap_number", sql.VarChar, supplierData?.global_gap_number)
      .input(
        "customer_site_code",
        sql.VarChar,
        supplierData?.customer_site_code
      )
      .input("updated_date", sql.DateTime, supplierData?.updated_date)
      .input("status", sql.Int, status)
      .query(sqlQueries.updateCompliance);

    const differences = {};

    const fieldsToSkip = ["compliance", "updated_date", "status"];
    Object.keys(supplierData)?.forEach((field) => {
      if (fieldsToSkip?.includes(field)) {
        return;
      }
      // Check if the field exists in existingData and if the values are different
      if (
        previousData?.hasOwnProperty(field) &&
        previousData[field] !== supplierData[field]
      ) {
        // If values are different, log the difference

        differences[field] = {
          previousValue: previousData[field],
          newValue: supplierData[field],
        };
      }
    });
    const formattedDifferences = Object.entries(differences)?.map(
      ([key, { previousValue, newValue }]) => {
        if (previousValue && newValue) {
          return `${key}: ${newValue} (changed from '${previousValue?.trim()}')`;
        } else if (!previousValue && newValue) {
          return `${key}: ${newValue} (added as a new value)`;
        } else if (previousValue && !newValue) {
          return `${key}: Removed (previously '${previousValue?.trim()}')`;
        } else {
          return `${key}: No change`;
        }
      }
    );

    await transaction.commit();
    if (formattedDifferences?.length > 0) {
      logger.info({
        username: name,
        type: "success",
        description: `User with email ${email} updated Compliance Section data with changes ${formattedDifferences?.join(
          ", "
        )}`,
        item_id: id,
        module_id: 1,
      });
    }

    return { status: 200 };
  } catch (error) {
    console.error(error);

    if (transaction) {
      await transaction.rollback();
      logger.info({
        username: name,
        type: "success",
        description: `User with email ${email} failed to update compliance section: ${error}`,
        item_id: id,
        module_id: 1,
      });
    }
    return { status: 400 };
  } finally {
    if (transaction) {
      transaction = null;
    }
  }
};

const updateFinancialData = async (id, supplierData, email, name, domain) => {
  let transaction;
  try {
    const distributionPointData =
      supplierData?.distribution_points_json &&
      Array.isArray(supplierData?.distribution_points_json)
        ? [...supplierData.distribution_points_json]
        : [];

    const deliveryData =
      supplierData?.delivery_json && Array.isArray(supplierData?.delivery_json)
        ? [...supplierData.delivery_json]
        : [];

    const prophet_id = supplierData?.prophet_id;
    const oldDistributionPoints = distributionPointData?.filter(
      (item) => item?.isOld === true
    );

    const newDistributionPoints = distributionPointData?.filter(
      (item) => item?.isOld === false
    );

    let pool = await sql.connect(config.sql);
    transaction = new sql.Transaction(pool);
    await transaction.begin();

    const sqlQueries = await utils.loadSqlQueries("suppliers");

    const getExistingFinancialData = await pool
      .request()
      .input("id", sql.Int, id)
      .query(sqlQueries.supplierById);

    const previousData = getExistingFinancialData.recordset[0];
    let checkExistingDeliveries = await pool
      .request()
      .input("supplier_id", sql.Int, id)
      .query(sqlQueries.deliveryListByID);

    let checkExistingDistribution = await pool
      .request()
      .input("supplier_id", sql.Int, id)
      .query(sqlQueries.distributionPointListByID);

    const previousDistributionData = checkExistingDistribution?.recordset;

    const previousDeliveriesData = checkExistingDeliveries?.recordset;

    function compareDeliveries(previousDeliveriesData, deliveryData) {
      const updatedDeliveries = [];
      const newDeliveries = [];
      const deletedDeliveries = [];

      // Check for updated deliveries
      for (const previousDelivery of previousDeliveriesData) {
        const matchingDelivery = deliveryData?.find(
          (delivery) => delivery?.id === previousDelivery?.id
        );
        if (matchingDelivery) {
          if (
            matchingDelivery?.delivery_terms !==
              previousDelivery?.delivery_terms ||
            matchingDelivery?.mode_of_transport !==
              previousDelivery?.mode_of_transport
          ) {
            updatedDeliveries?.push(matchingDelivery);
          }
        } else {
          deletedDeliveries?.push(previousDelivery);
        }
      }

      // Check for new deliveries
      for (const delivery of deliveryData) {
        if (
          !previousDeliveriesData?.find(
            (previousDelivery) => previousDelivery?.id === delivery?.id
          )
        ) {
          newDeliveries?.push(delivery);
        }
      }

      return { updatedDeliveries, newDeliveries, deletedDeliveries };
    }
    const { updatedDeliveries, newDeliveries, deletedDeliveries } =
      compareDeliveries(previousDeliveriesData, deliveryData);

    if (previousData && previousData.account_number) {
      previousData.account_number = decryptData(previousData?.account_number);
    }
    if (previousData && previousData?.sort_bic) {
      previousData.sort_bic = decryptData(previousData?.sort_bic);
    }
    if (previousData && previousData?.name_branch) {
      previousData.name_branch = decryptData(previousData?.name_branch);
    }
    if (previousData && previousData?.intermediatery_account_number) {
      previousData.intermediatery_account_number = decryptData(
        previousData?.intermediatery_account_number
      );
    }

    const updatedDistribution = [];
    const deletedDistribution = [];
    const newDistribution = [];

    previousDistributionData?.forEach((previousItem) => {
      const matchingItem = distributionPointData?.find(
        (item) => item?.dpId === previousItem?.id
      );
      if (matchingItem) {
        if (
          matchingItem?.distributionPoint !== previousItem?.distribution_name
        ) {
          updatedDistribution?.push(matchingItem);
        }
      } else {
        deletedDistribution?.push(previousItem);
      }
    });

    distributionPointData?.forEach((item) => {
      if (
        !previousDistributionData?.some(
          (previousItem) => previousItem?.id === item?.dpId
        )
      ) {
        if (item?.isOld === false) {
          newDistribution?.push(item);
        } else if (
          !deletedDistribution?.some(
            (deletedItem) => deletedItem?.id === item?.dpId
          )
        ) {
          newDistribution?.push(item);
        }
      }
    });

    const differences = {};

    const fieldsToSkip = [
      "delivery_json",
      "distribution_points_json",
      "finance_authorization_date",
      "updated_date",
      "currency",
      "country_code",
      "payment_type",
    ];
    let status = supplierData.status;
    // Format changes from updated deliveries
    const updatedDeliveryChanges = updatedDeliveries?.map((delivery) => {
      return `Updated Delivery: ID - ${delivery?.id}, Delivery Terms - ${delivery?.delivery_terms}, Mode of Transport - ${delivery?.mode_of_transport}`;
    });

    // Format changes from deleted deliveries
    const deletedDeliveryChanges = deletedDeliveries?.map((delivery) => {
      return `Deleted Delivery: ID - ${delivery?.id}, Delivery Terms - ${delivery?.delivery_terms}, Mode of Transport - ${delivery?.mode_of_transport}`;
    });

    // Format changes from new deliveries
    const newDeliveryChanges = newDeliveries?.map((delivery) => {
      return `New Delivery: Delivery Terms - ${delivery?.delivery_terms}, Mode of Transport - ${delivery?.mode_of_transport}`;
    });

    // Combine all changes into a single array
    const allDeliveryChanges = [
      ...updatedDeliveryChanges,
      ...deletedDeliveryChanges,
      ...newDeliveryChanges,
    ];

    // Log all changes for deliveries
    // Format changes from updated distribution points
    const updatedDistributionChanges = updatedDistribution?.map((point) => {
      return `Updated Distribution Point: ID - ${point?.dpId}, Distribution Point - ${point?.distributionPoint}`;
    });

    // Format changes from deleted distribution points
    const deletedDistributionChanges = deletedDistribution?.map((point) => {
      return `Deleted Distribution Point: ID - ${point?.distribution_point_id}, Distribution Point - ${point?.distribution_name},`;
    });

    // Format changes from new distribution points
    const newDistributionChanges = newDistribution?.map((point) => {
      return `New Distribution Point: Distribution Point - ${point?.distributionPoint}`;
    });

    // Combine all changes into a single array
    const allDistributionChanges = [
      ...updatedDistributionChanges,
      ...deletedDistributionChanges,
      ...newDistributionChanges,
    ];

    // Log all changes for distribution points
    const fieldMappings = {
      payment_type_name: "payment_type",
      sort_Bic: "sort_bic",
    };
    const sensitiveFields = [
      "account_number",
      "name_branch",
      "sort_Bic",
      "intermediatery_account_number",
    ];

    Object.keys(supplierData)?.forEach((field) => {
      if (fieldsToSkip?.includes(field)) {
        return;
      }

      // Check if the field is sensitive
      const isSensitive = sensitiveFields?.includes(field);

      // Check if the field exists in existingData and if the values are different
      const mappedField = fieldMappings[field] || field;

      if (
        previousData?.hasOwnProperty(mappedField) &&
        previousData[mappedField] !== supplierData[field]
      ) {
        // If values are different, log the difference
        if (isSensitive) {
          differences[field] = true; // Only include the field name
        } else {
          differences[field] = {
            previousValue: previousData[mappedField],
            newValue: supplierData[field],
          };
        }
      }
    });

    const formattedDifferences = Object.entries(differences)
      ?.map(([key, { previousValue, newValue }]) => {
        // Skip if either previousValue or newValue is undefined
        if (previousValue === undefined || newValue === undefined) {
          return null;
        }

        // Construct the formatted difference based on the values
        if (previousValue && newValue) {
          return `${key}: ${newValue} (changed from '${previousValue}')`;
        } else if (!previousValue && newValue) {
          return `${key}: ${newValue} (added as a new value)`;
        } else if (previousValue && !newValue) {
          return `${key}: Removed (previously '${previousValue}')`;
        }
      })
      ?.filter(Boolean); // Filter out any null values

    function extractBooleanDifferences(differences) {
      const booleanChanges = [];

      Object.entries(differences)?.forEach(([key, value]) => {
        if (typeof value === "boolean" && value) {
          booleanChanges?.push(`${key} changed`);
        }
      });

      return booleanChanges;
    }
    let finance_authorization_date = null;
    const booleanChanges = extractBooleanDifferences(differences);
    let roleIds = supplierData?.roleIds;

    let prophetId = previousData?.prophets_id_code
      ? JSON.parse(previousData?.prophets_id_code)
      : [];

    let isProphetCodeUnqiue = prophetId[0]?.code_count == 1;

    const combinedChanges = [...formattedDifferences, ...booleanChanges];

    if (allDeliveryChanges?.length > 0) {
      combinedChanges?.push(...allDeliveryChanges);
    }

    if (allDistributionChanges?.length > 0) {
      combinedChanges?.push(...allDistributionChanges);
    }
    let companyKey = "";
    if (supplierData?.prophet_id == 1) {
      companyKey = "dpsltd";
    } else if (supplierData?.prophet_id == 2) {
      companyKey = "dpsltd";
    } else if (supplierData?.prophet_id == 3) {
      companyKey = "efcltd";
    } else if (supplierData?.prophet_id == 4) {
      companyKey = "fpp-ltd";
    }
    if (supplierData?.validated_procurement_team) {
      const changedFields = [];

      // Check for changes in each field, using strict equality (===) for accurate comparison
      if (supplierData?.account_number != previousData?.account_number) {
        changedFields?.push("account_number");
      }
      if (supplierData?.sort_Bic != previousData.sort_bic) {
        changedFields?.push("sort_Bic");
      }
      if (supplierData?.name_branch != previousData.name_branch) {
        changedFields?.push("name_branch");
      }
      if (
        supplierData?.intermediatery_account_number !=
        previousData?.intermediatery_account_number
      ) {
        changedFields?.push("intermediatery_account_number");
      }
      if (
        supplierData?.finance_authorization !=
          previousData?.finance_authorization &&
        previousData.finance_authorization != 1
      ) {
        changedFields?.push("finance_authorization");
      }
      if (
        supplierData?.finance_authorization_by !=
          previousData?.finance_authorization_by &&
        supplierData.finance_authorization_by != undefined
      ) {
        changedFields?.push("finance_authorization_by");
      }

      if (
        changedFields?.includes("finance_authorization") ||
        changedFields?.includes("finance_authorization_by")
      ) {
        if (supplierData?.finance_authorization == 0) {
          const formattedDate = new Date(
            supplierData?.finance_authorization_date
          ).toLocaleString("en-US", {
            year: "numeric",
            month: "short",
            day: "2-digit",
            hour: "2-digit",
            minute: "2-digit",
            second: "2-digit",
            hour12: true, // Set to false for 24-hour format
          });
          finance_authorization_date = formattedDate;

          // let recipientEmail = supplierData.validatedBy.email;
          // let recipientEmail = "<EMAIL>";
          const recipientEmails = supplierData.requestor_email;

          const fullName = supplierData?.requestor_name || "User";
          const firstName = fullName.split(" ")[0];
          let placeholders = {
            User: firstName,
            Supplier_name: supplierData?.supplierName || "Supplier",
            Approved_by: supplierData?.financeApprovedBy?.name,
            Approved_date: formattedDate,
            Supplier_id: id,
            Domain: domain,
          };
          let emailType = "bankApproval";

          const result = await sendEmail({
            placeholders,
            emailType,
            recipientEmails,
            companyKey,
          });

          // const result = await sendEmail({ subject, body, recipientEmail });
        } else if (supplierData?.finance_authorization == 1) {
          const formattedDate = new Date(
            supplierData?.updated_date
          )?.toLocaleString("en-US", {
            year: "numeric",
            month: "short",
            day: "2-digit",
            hour: "2-digit",
            minute: "2-digit",
            second: "2-digit",
            hour12: true, // Set to false for 24-hour format
          });
          finance_authorization_date = formattedDate;
          // await sendEmail(
          //   "<EMAIL>",
          //   "<EMAIL>",
          //   "Banking Details rejected",
          //   `<p>Hi,</p>
          //   <p>The banking details for Supplier ${supplierData?.supplierName} have been Rejected by ${name} on ${formattedDate}.</p>
          //   <p>The rejected reason is below:-</p>
          //   <p>${supplierData?.rejected_reason}</p>
          //   <p>Please review and update the banking information and validate for approval.</p>
          //   <p>Thanks,</p>
          //   <p>THL Portal</p>
          //   `
          // );

          const fullName = supplierData?.requestor_name || "User";
          const recipientEmails = supplierData.requestor_email;
          const firstName = fullName.split(" ")[0];
          let placeholders = {
            User: firstName,
            Supplier_name: supplierData?.supplierName || "Supplier",
            Approved_by: supplierData?.financeApprovedBy?.name,
            Approved_date: formattedDate,
            Supplier_id: id,
            Domain: domain,
            Rejected_reason: supplierData?.rejected_reason,
          };
          let emailType = "bankRejection";

          const result = await sendEmail({
            placeholders,
            emailType,
            recipientEmails,
            companyKey,
          });
        }
      } else if (
        changedFields?.includes("intermediatery_account_number") ||
        changedFields?.includes("name_branch") ||
        changedFields?.includes("sort_Bic") ||
        changedFields?.includes("account_number")
      ) {
        const formattedDate = new Date(
          supplierData?.updated_date
        )?.toLocaleString("en-US", {
          year: "numeric",
          month: "short",
          day: "2-digit",
          hour: "2-digit",
          minute: "2-digit",
          second: "2-digit",
          hour12: true,
        });
        supplierData.validatedDate = supplierData?.updated_date;

        let recipient = await pool //!get the prophet id here correctly
          .request()
          .input("prophet_id", sql.Int, prophet_id)
          .input("section_id", sql.Int, 2)
          .query(sqlQueries.getEmailRecipients);
        let recipientEmails = recipient.recordset[0]?.recipients;
        const fullName = supplierData?.requestor_name || "User";
        const firstName = fullName.split(" ")[0];
        let placeholders = {
          User: firstName, //!this should be the name of the recipeint
          Supplier_name: supplierData?.supplierName || "Supplier",
          Validated_by: supplierData.validatedBy.name,
          Validated_date: formattedDate,
          Supplier_id: id,
          Domain: domain,
        };
        let emailType = "bankValidated";
        const result = await sendEmail({
          placeholders,
          emailType,
          recipientEmails,
          companyKey,
        });
      }
    }
    status = await isSupplierConfirmed(
      supplierData,
      roleIds,
      isProphetCodeUnqiue,
      sqlQueries,
      id,
      status,
      pool,
      companyKey
    );

    if (supplierData?.account_number) {
      supplierData.account_number = encryptData(supplierData?.account_number);
    }
    if (supplierData?.sort_Bic) {
      supplierData.sort_Bic = encryptData(supplierData?.sort_Bic);
    }
    if (supplierData?.name_branch) {
      supplierData.name_branch = encryptData(supplierData?.name_branch);
    }
    if (supplierData?.intermediatery_account_number) {
      supplierData.intermediatery_account_number = encryptData(
        supplierData?.intermediatery_account_number
      );
    }

    const updateSupplier = await pool
      .request(transaction)
      .input("id", sql.Int, id)
      .input("currency", sql.Int, supplierData?.currency)
      .input("financial", sql.VarChar, supplierData?.financial)
      .input("vat_number", sql.VarChar, supplierData?.vat_number)
      .input(
        "company_registration",
        sql.VarChar,
        supplierData?.company_registration
      )
      .input("country_code", sql.Int, supplierData?.country_code)
      .input("payment_terms", sql.VarChar, supplierData?.payment_terms)
      .input(
        "payment_type",
        sql.Int,
        supplierData?.payment_type ? parseInt(supplierData.payment_type) : null
      )
      .input(
        "validated_procurement_team",
        sql.Bit,
        supplierData?.validated_procurement_team
      )
      .input(
        "finance_authorization_date",
        sql.DateTime,
        finance_authorization_date
      )
      .input(
        "finance_authorization",
        sql.Int,
        supplierData?.finance_authorization
      )
      .input("rejected_reason", sql.VarChar, supplierData?.rejected_reason)
      .input(
        "finance_authorization_by",
        sql.Int,
        supplierData?.financeApprovedBy?.user_id
      )

      .input("validated_by", sql.VarChar, supplierData?.validatedBy?.email)
      .input(
        "vatable",
        sql.Bit,
        supplierData?.vatable == "true"
          ? 1
          : supplierData?.vatable == "false"
          ? 0
          : null
      )
      .input("updated_date", sql.DateTime, supplierData?.updated_date)
      .input("validated_date", sql.DateTime, supplierData?.validatedDate)
      .input("status", sql.Int, status)
      .query(sqlQueries.updateFinancialsSection);

    let distributionIds = oldDistributionPoints?.map((ele) => ele?.dpId);

    for (const distribution of newDistributionPoints) {
      const createDistribution = await pool
        .request(transaction)
        .input("from_dp", sql.Int, null)
        .input("name", sql.NVarChar, distribution?.distributionPoint)
        .input("prophet_id", sql.Int, prophet_id)
        .input("isActive", sql.Bit, true)
        .query(sqlQueries.createDistributionPoints);

      distribution.dpId = createDistribution?.recordset[0]?.id;

      distributionIds?.push(createDistribution?.recordset[0]?.id);
    }

    for (const distribution of deletedDistribution) {
      const updateDistributionId = await pool
        .request(transaction)
        .input("isActive", sql.Bit, 0)
        .input(
          "distribution_point_id",
          sql.Int,
          distribution.distribution_point_id
        )
        .input("supplier_id", sql.Int, distribution.supplier_id)
        .query(sqlQueries.updateDistributionState);
    }

    for (const distribution of oldDistributionPoints) {
      const updateDistributionId = await pool
        .request(transaction)
        .input("isActive", sql.Bit, 0)
        .input("distribution_point_id", sql.Int, distribution.dpId)
        .input("supplier_id", sql.Int, id)
        .query(sqlQueries.updateDistributionState);
    }

    for (const distribution of distributionPointData) {
      console.log("disctribution linking", distribution);
      const newDistributionLink = await pool
        .request(transaction)
        .input("supplier_id", sql.Int, id)
        .input("distribution_point_id", sql.Int, distribution.dpId)
        .input("direct_dp", sql.Bit, distribution.directDP)
        .input("isActive", sql.Bit, true)
        .query(sqlQueries.createDistributionPointsLinking);
    }

    if (deliveryData && deliveryData?.length > 0) {
      if (checkExistingDeliveries.recordset?.length == 0) {
        for (const delivery of deliveryData) {
          const deliveryDataD = await pool
            .request(transaction)
            .input("delivery_terms", sql.VarChar, delivery?.delivery_terms)
            .input(
              "mode_of_transport",
              sql.VarChar,
              delivery?.mode_of_transport
            )
            .input("supplier_id", sql.Int, id)
            .query(sqlQueries?.createDelivery);
        }
      } else {
        if (deletedDeliveries?.length > 0) {
          for (const delivery of deletedDeliveries) {
            const deleteDelivery = await pool
              .request(transaction)
              .input("id", sql.Int, delivery?.id)
              .query(sqlQueries.deleteDeliveries);
          }
        }
        if (updatedDeliveries?.length > 0) {
          for (const delivery of deliveryData) {
            const deliveryDataD = await pool
              .request(transaction)
              .input("delivery_terms", sql.VarChar, delivery?.delivery_terms)
              .input(
                "mode_of_transport",
                sql.VarChar,
                delivery?.mode_of_transport
              )
              .input("id", sql.Int, delivery?.id)
              .query(sqlQueries.updateDelivery);
          }
        }
        if (newDeliveries?.length > 0) {
          for (const delivery of newDeliveries) {
            const deliveryDataD = await pool
              .request(transaction)
              .input("delivery_terms", sql.VarChar, delivery?.delivery_terms)
              .input(
                "mode_of_transport",
                sql.VarChar,
                delivery?.mode_of_transport
              )
              .input("supplier_id", sql.Int, id)
              .query(sqlQueries.createDelivery);
          }
        }
      }
    } else {
      if (checkExistingDeliveries.recordset?.length > 0) {
        const deleteDelivery = await pool
          .request(transaction)
          .input("supplier_id", sql.Int, id)
          .query(sqlQueries.deleteAllDeliveries);
      }
    }

    if (!supplierData?.copy) {
      const checkExistingAccounts = await pool
        .request()
        .input("supplier_id", sql.Int, id)
        .query(sqlQueries.bankAccListByID);

      if (checkExistingAccounts.recordset?.length == 0) {
        const result = await pool
          .request(transaction)
          .input("sort_bic", sql.VarChar, supplierData?.sort_Bic)
          .input("name_branch", sql.Text, supplierData?.name_branch)
          .input("account_number", sql.VarChar, supplierData?.account_number)
          .input(
            "intermediatery_account_number",
            sql.VarChar,
            supplierData?.intermediatery_account_number
          )
          .input("supplier_id", sql.Int, id)
          .input("has_iban", sql.Bit, supplierData?.has_iban)
          .query(sqlQueries.createBankAcc);
      } else {
        const deleteAccount = await pool
          .request(transaction)
          .input("supplier_id", sql.Int, id)
          .query(sqlQueries.deleteBankAcc);

        const accounts = await pool
          .request(transaction)
          .input("sort_bic", sql.VarChar, supplierData?.sort_Bic)
          .input("name_branch", sql.Text, supplierData?.name_branch)
          .input("account_number", sql.VarChar, supplierData?.account_number)
          .input(
            "intermediatery_account_number",
            sql.VarChar,
            supplierData?.intermediatery_account_number
          )
          .input("supplier_id", sql.Int, id)
          .input("has_iban", sql.Bit, supplierData?.has_iban)
          .query(sqlQueries?.createBankAcc);
      }
    }

    await transaction.commit();

    if (
      supplierData?.finance_authorization == 0 ||
      (supplierData?.finance_authorization == 1 &&
        supplierData?.financeApprovedBy)
    ) {
      let financeApprovedByName = supplierData?.financeApprovedBy?.name;
      if (supplierData?.finance_authorization == 0) {
        logger.info({
          username: name,
          type: "success",
          description: `User with email ${email} approved banking details for supplier ${getExistingFinancialData?.recordset[0]?.name}`,
          item_id: id,
          module_id: 1,
        });
      } else if (supplierData?.finance_authorization == 1) {
        logger.info({
          username: name,
          type: "success",
          description: `User with email ${email} rejected banking details for supplier ${getExistingFinancialData?.recordset[0]?.name} with reason "${supplierData?.rejected_reason}"`,
          item_id: id,
          module_id: 1,
        });
      }
    }

    if (combinedChanges?.length > 0) {
      logger.info({
        username: name,
        type: "success",
        description: `User with email ${email} updated financial section with changes: ${combinedChanges?.join(
          ", "
        )}`,
        item_id: id,
        module_id: 1,
      });
    }
    return {
      status: 200,
    };
  } catch (error) {
    console.error(error);

    if (transaction) {
      await transaction.rollback();
      logger.error({
        username: name,
        type: "error",
        description: `User with email ${email} failed to update financial data: "${error.message}"`,
        item_id: id,
        module_id: 1,
      });
    }
    return { status: 400 };
  } finally {
    if (transaction) {
      transaction = null;
    }
  }
};

const updateDTAYData = async (id, supplierData, email, name) => {
  let transaction;
  try {
    const dtay_data_json = [...supplierData?.dtay_data_json];
    let pool = await sql.connect(config.sql);
    transaction = new sql.Transaction(pool);
    await transaction.begin();
    const sqlQueries = await utils.loadSqlQueries("suppliers");

    const updateSupplier = await pool
      .request(transaction)
      .input("id", sql.Int, id)
      .input("procurement", sql.VarChar, supplierData?.procurement)
      .input("updated_date", sql.DateTime, supplierData?.updated_date)
      .input("status", sql.Int, supplierData?.status)
      .query(sqlQueries.updateProcurementCol);

    const checkExistingDtay = await pool
      .request()
      .input("supplier_id", sql.Int, id)
      .query(sqlQueries.dtayListById);

    // Find deleted items
    let newItems = [];
    let newItemsSentence = [];
    let deletedItems = [];
    let deletedItemsSentence = [];

    if (dtay_data_json?.length > 0) {
      newItems = dtay_data_json?.filter((item) => !item?.id);
      deletedItems = checkExistingDtay?.recordset?.filter(
        (item) => !dtay_data_json?.find((dtayItem) => dtayItem?.id === item?.id)
      );

      newItemsSentence = newItems?.map((item) => {
        const details = Object.entries(item)
          ?.filter(([key, value]) => key !== "id")
          ?.map(([key, value]) => `${key}: ${value}`)
          ?.join(", ");
        return `New Item: ${details}`;
      });

      deletedItemsSentence = deletedItems?.map((item) => {
        const details = Object.entries(item)
          ?.filter(([key, value]) => key !== "id")
          ?.map(([key, value]) => `${key}: ${value}`)
          ?.join(", ");
        return `Deleted Item: ${details}`;
      });
    }
    let combinedSentences = [];
    if (newItemsSentence?.length > 0 && deletedItemsSentence?.length > 0) {
      combinedSentences = newItemsSentence?.concat(deletedItemsSentence);
    }

    if (dtay_data_json && dtay_data_json?.length > 0) {
      if (checkExistingDtay.recordset?.length == 0) {
        for (const dtay of dtay_data_json) {
          const distributiondata = await pool
            .request(transaction)
            .input("product_number", sql.Int, dtay?.product_number)
            .input("description", sql.VarChar, dtay?.description)
            .input("end_customer", sql.VarChar, dtay?.end_customer)
            .input("agreed_terms", sql.VarChar, dtay?.agreed_terms)
            .input("brand", sql.VarChar, dtay?.brand)
            .input("pricing", sql.VarChar, dtay?.pricing)
            .input("yields", sql.VarChar, dtay?.yields)
            .input("start_date", sql.Date, dtay?.startDate)
            .input("end_date", sql.Date, dtay?.endDate)
            .input("supplier_id", sql.Int, id)
            .query(sqlQueries.createdtay);
        }
      } else {
        const deleteDtay = await pool
          .request(transaction)
          .input("supplier_id", sql.Int, id)
          .query(sqlQueries.deletedtay);

        for (const dtay of dtay_data_json) {
          const distributiondata = await pool
            .request(transaction)
            .input("product_number", sql.Int, dtay?.product_number)
            .input("description", sql.VarChar, dtay?.description)
            .input("end_customer", sql.VarChar, dtay?.end_customer)
            .input("agreed_terms", sql.VarChar, dtay?.agreed_terms)
            .input("brand", sql.VarChar, dtay?.brand)
            .input("pricing", sql.VarChar, dtay?.pricing)
            .input("yields", sql.VarChar, dtay?.yields)
            .input("start_date", sql.VarChar, dtay?.startDate)
            .input("end_date", sql.VarChar, dtay?.endDate)
            .input("supplier_id", sql.Int, id)
            .query(sqlQueries.createdtay);
        }
      }
    } else {
      if (checkExistingDtay.recordset?.length > 0) {
        const deleteDtay = await pool
          .request(transaction)
          .input("supplier_id", sql.Int, id)
          .query(sqlQueries.deletedtay);
      }
    }

    const commitResult = await transaction.commit();

    if (dtay_data_json && dtay_data_json?.length > 0) {
      let description = `User with email ${email} updated Procurement Section data with changes `;

      if (combinedSentences?.length > 0) {
        description += combinedSentences?.join(", ");
      } else if (newItemsSentence?.length > 0) {
        description += newItemsSentence?.join(", ");
      } else if (deletedItemsSentence?.length > 0) {
        description += deletedItemsSentence?.join(", ");
      }

      logger.info({
        username: name,
        type: "success",
        description,
        item_id: id,
        module_id: 1,
      });
    }

    return { status: 200 };
  } catch (error) {
    console.error(error);

    if (transaction) {
      await transaction.rollback();
      logger.error({
        username: name,
        type: "error",
        description: `User with email ${email} failed to update Procurement data: ${error.message}`,
        item_id: id,
        module_id: 1,
      });
    }
    return { status: 400 };
  } finally {
    if (transaction) {
      transaction = null;
    }
  }
};

const updateStatus = async (id, supplierData, email, name) => {
  let transaction;
  try {
    let pool = await sql.connect(config.sql);
    transaction = new sql.Transaction(pool);
    await transaction.begin();
    const sqlQueries = await utils.loadSqlQueries("suppliers");
    if (!supplierData?.isEmergencyRequest) {
      const checkExistingStatus = await pool
        .request()
        .input("supplier_id", sql.Int, parseInt(id))
        .query(sqlQueries.checkExistingStatus);

      const getStatus = checkExistingStatus?.recordset;
      if (getStatus[0]?.status != 1) {
        if (supplierData?.status == 1) {
          let recipient = await pool
            .request()
            .input("prophet_id", sql.Int, supplierData?.prophetId)
            .input("section_id", sql.Int, 2)
            .query(sqlQueries.getEmailRecipients);
          let companyKey = "";
          if (supplierData?.prophetId == 1) {
            companyKey = "dpsltd";
          } else if (supplierData?.prophetId == 2) {
            companyKey = "dpsltd";
          } else if (supplierData?.prophetId == 3) {
            companyKey = "efcltd";
          } else if (supplierData?.prophetId == 4) {
            companyKey = "fpp-ltd";
          }
          let recipientEmails = recipient.recordset[0]?.recipients;
          const fullName = supplierData?.requestor_name || "User";
          const firstName = fullName.split(" ")[0];
          let placeholders = {
            User: firstName,
            Supplier_name: supplierData?.company_name || "Supplier",
            Supplier_id: id,
          };
          let emailType = "supplierConfirmation";
          const result = await sendEmail({
            placeholders,
            emailType,
            recipientEmails,
            companyKey,
          });
        }
      }
    }

    const updateSupplier = await pool
      .request(transaction)
      .input("id", sql.Int, parseInt(id))
      .input("status", sql.Int, supplierData?.status)
      .input("updated_date", sql.DateTime, supplierData?.updated_date)
      .query(sqlQueries.updateStatus);
    if (supplierData?.type == "confirmPage") {
      logger.info({
        username: name,
        type: "success",
        description: `User with email ${email} confirmed data for ${supplierData?.company_name}`,
        item_id: parseInt(id),
        module_id: 1,
      });
    } else if (supplierData?.type == "cancelProduct") {
      logger.info({
        username: name,
        type: "success",
        description: `User with email ${email} cancalled supplier ${supplierData?.company_name}`,
        item_id: id,
        module_id: 1,
      });
    }

    await transaction.commit();
    //return updateSupplier.recordset;
    return { status: 200 };
  } catch (error) {
    console.error(error);

    if (transaction) {
      await transaction.rollback();
    }
    logger.error({
      username: null,
      type: "error",
      description: `Failed to update status:${error}`,
      module_id: 1,
    });
    return { status: 400 };
  } finally {
    if (transaction) {
      transaction = null;
    }
  }
};

const updateGeneralData = async (id, supplierData, email, name) => {
  let transaction;
  try {
    let pool = await sql.connect(config.sql);
    transaction = new sql.Transaction(pool);
    await transaction.begin();
    const sqlQueries = await utils.loadSqlQueries("suppliers");
    const differences = {};
    let roleIds = supplierData?.roleIds;
    let status = supplierData.status;
    const previousData = await pool
      .request()
      .input("id", sql.Int, id)
      .query(sqlQueries.supplierById);

    const prev = previousData?.recordset[0];
    let prophetId = prev?.prophets_id_code
      ? JSON.parse(prev?.prophets_id_code)
      : [];
    let isProphetCodeUnqiue = prophetId[0]?.code_count == 1;
    let companyKey = "";
    if (supplierData?.prophet_id == 1) {
      companyKey = "dpsltd";
    } else if (supplierData?.prophet_id == 2) {
      companyKey = "dpsltd";
    } else if (supplierData?.prophet_id == 3) {
      companyKey = "efcltd";
    } else if (supplierData?.prophet_id == 4) {
      companyKey = "fpp-ltd";
    }
    status = await isSupplierConfirmed(
      supplierData,
      roleIds,
      isProphetCodeUnqiue,
      sqlQueries,
      id,
      status,
      pool,
      companyKey
    );

    const checkExistingContacts = await pool
      .request()
      .input("supplier_id", sql.Int, id)
      .query(sqlQueries.contactsList);

    const contactsJson = supplierData?.contacts_json
      ? [...supplierData?.contacts_json]
      : [];

    const updatedContacts = [];
    const newContacts = [];
    const deletedContacts = [];
    let existingContacts = checkExistingContacts?.recordset;

    existingContacts?.forEach((existingContact) => {
      const matchingContact = contactsJson?.find(
        (contact) => contact?.id === existingContact?.id
      );
      if (matchingContact) {
        if (
          matchingContact?.supplierName != existingContact?.name ||
          matchingContact?.supplierEmail != existingContact?.email_id ||
          matchingContact?.supplierTelephone != existingContact?.telephone ||
          matchingContact?.TypeOfContact != existingContact?.type_of_contact
        ) {
          updatedContacts?.push(matchingContact);
        }
      } else {
        deletedContacts?.push(existingContact);
      }
    });

    contactsJson?.forEach((contact) => {
      if (!contact?.id) {
        newContacts?.push(contact);
      } else {
        const matchingContact = existingContacts?.find(
          (existingContact) => existingContact?.id === contact?.id
        );
        if (!matchingContact) {
          newContacts?.push(contact);
        }
      }
    });

    // Format changes from new contacts
    const newContactChanges = newContacts?.map((contact) => {
      return `New Contact: Name - ${contact?.supplierName}, Email - ${contact?.supplierEmail}, Telephone - ${contact?.supplierTelephone}, TypeOfContact - ${contact?.TypeOfContact}`;
    });

    // Format changes from updated contacts
    const updatedContactChanges = updatedContacts?.map((contact) => {
      return `Updated Contact: ID - ${contact?.id}, Name - ${contact?.supplierName}, Email - ${contact?.supplierEmail}, Telephone - ${contact?.supplierTelephone}, TypeOfContact - ${contact?.TypeOfContact}`;
    });

    // Format changes from deleted contacts
    const deletedContactChanges = deletedContacts?.map((contact) => {
      return `Deleted Contact: ID - ${contact?.id}, Name - ${contact?.name}, Email - ${contact?.email_id}, Telephone - ${contact?.telephone}, TypeOfContact - ${contact?.type_of_contact}`;
    });

    // Combine all changes into a single array
    const allChanges = [
      ...newContactChanges,
      ...updatedContactChanges,
      ...deletedContactChanges,
    ];

    // Log all changes

    const fieldsToSkip = ["contacts_json", "updated_date", "technical"];
    Object.keys(supplierData)?.forEach((field) => {
      if (fieldsToSkip?.includes(field)) {
        return;
      }
      // Check if the field exists in existingData and if the values are different
      if (prev?.hasOwnProperty(field) && prev[field] !== supplierData[field]) {
        // If values are different, log the difference

        differences[field] = {
          previousValue: prev[field],
          newValue: supplierData[field],
        };
      }
    });

    const formattedDifferences = Object.entries(differences)?.map(
      ([key, { previousValue, newValue }]) => {
        if (previousValue && newValue) {
          return `${key}: ${newValue} (changed from '${previousValue}')`;
        } else if (!previousValue && newValue) {
          return `${key}: ${newValue} (added as a new value)`;
        } else if (previousValue && !newValue) {
          return `${key}: Removed (previously '${previousValue}')`;
        }
      }
    );

    const updateSupplier = await pool
      .request(transaction)
      .input("id", sql.Int, id)
      .input("name", sql.VarChar, supplierData?.name)
      .input("trading_name", sql.VarChar, supplierData?.trading_name)
      .input("email_id", sql.VarChar, supplierData?.email_id)
      .input("facsimile", sql.VarChar, supplierData?.facsimile)
      .input("telephone", sql.VarChar, supplierData?.telephone)
      .input("address_line_1", sql.VarChar, supplierData?.address_line_1)
      .input("address_line_2", sql.VarChar, supplierData?.address_line_2)
      .input("address_line_3", sql.VarChar, supplierData?.address_line_3)
      .input("address_line_4", sql.VarChar, supplierData?.address_line_4)
      .input("country", sql.Int, supplierData?.country)
      .input("postal_code", sql.VarChar, supplierData?.postal_code)
      .input("technical", sql.VarChar, supplierData?.technical)
      .input("updated_date", sql.DateTime, supplierData?.updated_date)
      .input("status", sql.Int, status)
      .input("edi", sql.VarChar, supplierData?.edi)
      .query(sqlQueries.updateGeneralSection);

    if (contactsJson && contactsJson?.length > 0) {
      if (checkExistingContacts.recordset?.length == 0) {
        for (const contact of contactsJson) {
          await pool
            .request(transaction)
            .input("name", sql.VarChar, contact?.supplierName)
            .input("email_id", sql.VarChar, contact?.supplierEmail)
            .input("telephone", sql.VarChar, contact?.supplierTelephone)
            .input("type_of_contact", sql.VarChar, contact?.TypeOfContact)
            .input("supplier_id", sql.Int, id)
            .input("updated_by", sql.Int, supplierData.userId)
            .query(sqlQueries.createContacts);
        }
      } else {
        if (deletedContacts?.length > 0) {
          for (const contact of deletedContacts) {
            const deleteContactcs = await pool
              .request(transaction)
              .input("id", sql.Int, contact?.id)
              .input("updated_by", sql.Int, supplierData?.userId)
              .query(sqlQueries.deleteContact);
          }
        }
        if (updatedContacts?.length > 0) {
          for (const contact of updatedContacts) {
            await pool
              .request(transaction)
              .input("name", sql.VarChar, contact?.supplierName)
              .input("email_id", sql.VarChar, contact?.supplierEmail)
              .input("telephone", sql.VarChar, contact?.supplierTelephone)
              .input("type_of_contact", sql.VarChar, contact?.TypeOfContact)
              .input("supplier_id", sql.Int, id)
              .input("id", sql.Int, contact?.id)
              .input("updated_by", sql.Int, supplierData.userId)
              .query(sqlQueries.updateContact);
          }
        }
        if (newContacts?.length > 0) {
          for (const contact of newContacts) {
            await pool
              .request(transaction)
              .input("name", sql.VarChar, contact?.supplierName)
              .input("email_id", sql.VarChar, contact?.supplierEmail)
              .input("telephone", sql.VarChar, contact?.supplierTelephone)
              .input("type_of_contact", sql.VarChar, contact?.TypeOfContact)
              .input("supplier_id", sql.Int, id)
              .input("updated_by", sql.Int, supplierData.userId)
              .query(sqlQueries.createContacts);
          }
        }
      }
    } else {
      if (checkExistingContacts.recordset?.length > 0) {
        const deleteContactcs = await pool
          .request(transaction)
          .input("supplier_id", sql.Int, id)
          .input("updated_by", sql.Int, supplierData?.userId)
          .query(sqlQueries.deleteAllContacts);
      }
    }

    await transaction.commit();
    if (formattedDifferences?.length != 0 && allChanges?.length != 0) {
      const allChangesDescription = allChanges?.join(", ");
      logger.info({
        username: name,
        type: "success",
        description: `User with email ${email} updated General Section data with changes: ${formattedDifferences} ${allChangesDescription}`,
        item_id: id,
        module_id: 1,
      });
    } else if (formattedDifferences?.length != 0 && allChanges?.length == 0) {
      logger.info({
        username: name,
        type: "success",
        description: `User with email ${email} updated General Section data with changes: ${formattedDifferences}`,
        item_id: id,
        module_id: 1,
      });
    } else if (formattedDifferences?.length == 0 && allChanges?.length != 0) {
      const allChangesDescription = allChanges?.join(", ");
      logger.info({
        username: name,
        type: "success",
        description: `User with email ${email} updated General Section data with changes:${allChangesDescription}`,
        item_id: id,
        module_id: 1,
      });
    }
    return {
      status: 200,
    };
  } catch (error) {
    console.error(error);

    if (transaction) {
      await transaction.rollback();
      logger.error({
        username: name,
        type: "error",
        description: `User with email ${email} failed to update General data: ${error.message}`,
        item_id: id,
        module_id: 1,
      });
    }
    return { status: 400 };
  } finally {
    if (transaction) {
      transaction = null;
    }
  }
};

const checkExistingProphetCode = async (prophetData, name) => {
  let pool = await sql.connect(config.sql);
  try {
    const sqlQueries = await utils.loadSqlQueries("suppliers");

    const deleteProphates = await pool
      .request()
      .input("supplier_id", sql.Int, prophetData[0]?.supplier_id)
      .query(sqlQueries.deleteProphetBySupplier);

    return { data: true };
  } catch (error) {
    console.error(error);

    logger.error({
      username: name,
      type: "error",
      description: `Failed to check existing prophet code:${error}`,
      module_id: 1,
    });
    return error.message;
  }
};

const insertProphetCode = async (prophets, name) => {
  let pool = await sql.connect(config.sql);
  try {
    const sqlQueries = await utils.loadSqlQueries("suppliers");

    if (prophets.length > 0) {
      for (const prophet of prophets) {
        pool
          .request()
          .input("prophet_id", sql.Int, prophet?.prophet_id)
          .input("prophet_code", sql.VarChar, prophet?.prophet_code?.trim())
          .input("supplier_id", sql.Int, prophet?.supplier_id)
          .query(sqlQueries.createSupplierByProphet);
      }
      return { data: true };
    }
  } catch (error) {
    console.error(error);

    logger.error({
      username: name,
      type: "error",
      description: `Failed to insert prophet code:${error}`,
      module_id: 1,
    });
    return error.message;
  }
};

const getCountriesList = async (prophets) => {
  try {
    let pool = await sql.connect(config.sql);
    const sqlQueries = await utils.loadSqlQueries("suppliers");

    const countries = await pool
      .request()
      .input("prophets", sql.Int, prophets)
      .query(sqlQueries.getCountries);

    return countries.recordset;
  } catch (error) {
    console.error(error);

    logger.error({
      username: null,
      type: "error",
      description: `Failed to get countries list:${error}`,
      module_id: 1,
    });
    return error.message;
  }
};

const getCurrenciesList = async (prophets) => {
  try {
    let pool = await sql.connect(config.sql);
    const sqlQueries = await utils.loadSqlQueries("suppliers");
    const currencies = await pool
      .request()
      .input("prophets", sql.Int, prophets)
      .query(sqlQueries.getCurrencies);

    return currencies.recordset;
  } catch (error) {
    console.error(error);

    logger.error({
      username: null,
      type: "error",
      description: `Failed to get Currencies list:${error}`,
      module_id: 1,
    });
    return error.message;
  }
};

const getCountryCodesList = async () => {
  try {
    let pool = await sql.connect(config.sql);
    const sqlQueries = await utils.loadSqlQueries("suppliers");
    const country_codes = await pool
      .request()
      .query(sqlQueries.getCountryCodes);
    return country_codes.recordset;
  } catch (error) {
    console.error(error);

    logger.error({
      username: null,
      type: "error",
      description: `Failed to get country codes list:${error}`,
      module_id: 1,
    });
    return error.message;
  }
};

const getHauliersList = async () => {
  try {
    let pool = await sql.connect(config.sql);
    const sqlQueries = await utils.loadSqlQueries("suppliers");
    const hauliers = await pool.request().query(sqlQueries.getHauliers);
    return hauliers.recordset;
  } catch (error) {
    console.error(error);

    logger.error({
      username: null,
      type: "error",
      description: `Failed to get hauliers list:${error}`,
      module_id: 1,
    });
    return error.message;
  }
};

const getDeliveryTermsList = async (prophets) => {
  try {
    let pool = await sql.connect(config.sql);
    const sqlQueries = await utils.loadSqlQueries("suppliers");
    const delivery_terms = await pool
      .request()
      .input("prophets", sql.Int, prophets)
      .query(sqlQueries.getDeliveryTerms);
    return delivery_terms.recordset;
  } catch (error) {
    console.error(error);

    logger.error({
      username: null,
      type: "error",
      description: `Failed to get delivery terms list:${error}`,
      module_id: 1,
    });
    return error.message;
  }
};

const getTransportsList = async (prophets) => {
  try {
    let pool = await sql.connect(config.sql);
    const sqlQueries = await utils.loadSqlQueries("suppliers");
    const transports = await pool
      .request()
      .input("prophets", sql.Int, prophets)
      .query(sqlQueries.getModeOfTransports);

    return transports.recordset;
  } catch (error) {
    console.error(error);

    logger.error({
      username: null,
      type: "error",
      description: `Failed to get transport list:${error}`,
      module_id: 1,
    });
    return error.message;
  }
};

const getTypeOfContactsList = async () => {
  try {
    let pool = await sql.connect(config.sql);
    const sqlQueries = await utils.loadSqlQueries("suppliers");
    const type_of_contacts = await pool
      .request()
      .query(sqlQueries.getTypeOfContacts);
    return type_of_contacts.recordset;
  } catch (error) {
    console.error(error);

    logger.error({
      username: null,
      type: "error",
      description: `Failed to get types of contacts list:${error}`,
      module_id: 1,
    });
    return error.message;
  }
};

const getPaymentTypesList = async (prophets) => {
  try {
    let pool = await sql.connect(config.sql);
    const sqlQueries = await utils.loadSqlQueries("suppliers");
    const payment_types = await pool
      .request()
      .input("prophets", sql.Int, prophets)
      .query(sqlQueries.getPaymentTypes);

    return payment_types.recordset;
  } catch (error) {
    console.error(error);

    logger.error({
      username: null,
      type: "error",
      description: `Failed to get payment types list:${error}`,
      module_id: 1,
    });
    return error.message;
  }
};

const getAgreedTermsList = async () => {
  try {
    let pool = await sql.connect(config.sql);
    const sqlQueries = await utils.loadSqlQueries("suppliers");
    const agreed_terms = await pool.request().query(sqlQueries.getAgreedTerms);
    return agreed_terms.recordset;
  } catch (error) {
    console.error(error);

    logger.error({
      username: null,
      type: "error",
      description: `Failed to get agreed terms list:${error}`,
      module_id: 1,
    });
    return error.message;
  }
};

const getBrandsList = async () => {
  try {
    let pool = await sql.connect(config.sql);
    const sqlQueries = await utils.loadSqlQueries("suppliers");
    const brands = await pool.request().query(sqlQueries.getBrands);
    return brands.recordset;
  } catch (error) {
    console.error(error);

    logger.error({
      username: null,
      type: "error",
      description: `Failed to get brands list:${error}`,
      module_id: 1,
    });
    return error.message;
  }
};

const getEndCustomersList = async () => {
  try {
    let pool = await sql.connect(config.sql);
    const sqlQueries = await utils.loadSqlQueries("suppliers");
    const end_customers = await pool
      .request()
      .query(sqlQueries.getEndCustomers);
    return end_customers.recordset;
  } catch (error) {
    console.error(error);

    logger.error({
      username: null,
      type: "error",
      description: `Failed to get end customers list:${error}`,
      module_id: 1,
    });
    return error.message;
  }
};
const getSuppliersExtractData = async (id) => {
  try {
    let pool = await sql.connect(config.sql);
    const sqlQueries = await utils.loadSqlQueries("suppliers");
    const suppliers = await pool
      .request()
      .input("id", sql.Int, id)
      .query(sqlQueries.getSuppliersExtract);
    suppliers.recordset.length > 0 &&
      suppliers.recordset.forEach((supplier) => {
        if (supplier?.account_number) {
          supplier.decryptedAccountNumber = decryptData(
            supplier?.account_number
          );
        }

        if (supplier?.sort_bic) {
          supplier.decryptedSort_Bic = decryptData(supplier?.sort_bic);
        }

        if (supplier?.name_branch) {
          supplier.decryptedName_branch = decryptData(supplier?.name_branch);
        }

        if (supplier?.intermediatery_account_number) {
          supplier.decryptedIntermediatery_account_number = decryptData(
            supplier?.intermediatery_account_number
          );
        }
      });
    return suppliers.recordset;
  } catch (error) {
    console.error(error);

    logger.error({
      username: null,
      type: "error",
      description: `Failed to get end customers list:${error}`,
      module_id: 1,
    });
    return error.message;
  }
};

module.exports = {
  getSuppliers,
  getLinkedSuppliers,
  getSupplierRoles,
  getSupplierLinks,
  getSupplierById,
  createSupplier,
  updateComplianceData,
  updateFinancialData,
  updateGeneralData,
  updateDTAYData,
  deleteSupplier,
  updateSupplier,
  updateStatus,
  createSendacGroup,
  getFilteredSupplierNames,
  getRolePermissions,
  getCountriesList,
  getCurrenciesList,
  getEndCustomersList,
  getBrandsList,
  getAgreedTermsList,
  getPaymentTypesList,
  getTypeOfContactsList,
  getTransportsList,
  getDeliveryTermsList,
  getHauliersList,
  getCountryCodesList,
  checkExistingProphetCode,
  insertProphetCode,
  getSendacLinksBySendac,
  getSendacGroupByProphets,
  moveLinks,
  removeLinks,
  getFilteredDistribution,
  checkDistribution,
  getLinksByProphets,
  getProductByProphet,
  deleteAll,
  getIfUniqueSupplierCode,
  getSuppliersExtractData,getSupplierTypes
};
