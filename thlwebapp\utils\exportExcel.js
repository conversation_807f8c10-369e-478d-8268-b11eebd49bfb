import ExcelJS from "exceljs";
import { toast } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import { apiConfig } from "@/services/apiConfig"; 
// todo:move the supplierCode column from the excel file to first column
const exportExcel = async (
  data,
  isInternal,
  token,
  company,
  userData,
  prophet_id,
  requestor_email,
  isMultiple,
  isProductExtract = false,
  onProductSubmit = false,
  productEmailParagraph = "",
  productEmailCommentPlaceholder = "",
  request_no = "",
  varietyRequest = false
) => {
  let serverAddress = apiConfig.serverAddress;
  let isEmergencyAndFinanceNotCompleteObj = [];
  let supplierNames = [];

  const workbook = new ExcelJS.Workbook();
  if (isInternal) {
    data.forEach((sheetData, index) => {
      if (sheetData.length === 0) {
        console.error("sheetData is empty for index:", index);
        return;
      }
      const sheetName = sheetData[0] || `Sheet${index + 1}`;

      let worksheet;

      if (index < 9) {
        if (Array.isArray(sheetName)) {
          const actualSheetName = sheetName[0];
          worksheet = workbook.addWorksheet(actualSheetName);
        } else {
          worksheet = workbook.addWorksheet(sheetName);
        }
      }

      if (sheetData.length > 1) {
        let headers;
        if (index < 9) {
          headers = Object.keys(sheetData[1]);
          worksheet.addRow(headers);
        }
        sheetData.slice(1).forEach((row, internalIndex) => {
          if (index < 9) {
            const rowData = headers.map((header) => row[header] || "");
            if (index != 4 && index != 3) {
              worksheet.addRow(rowData);
            } else if (index == 4) {
              worksheet.addRow(rowData);
            } else if (index == 3 && rowData[1] != "") {
              worksheet.addRow(rowData);
            }
          }

          if (index === 9) {
            supplierNames.push({
              supplierName: row?.supplierName,
              supplierCode: row?.supplierCode,
            });
            if (row.isEmergencyAndFinanceNotComplete) {
              let isEmergencyAndFinanceNotCompleteSupplier = {
                isEmergencyAndFinanceNotComplete:
                  row.isEmergencyAndFinanceNotComplete,
                supplierName: row.supplierName,
              };
              isEmergencyAndFinanceNotCompleteObj.push(
                isEmergencyAndFinanceNotCompleteSupplier
              );
            }
            fetch(`${serverAddress}suppliers/update-supplier/${row.id}`, {
              method: "PUT",
              headers: {
                Accept: "application/json",
                "Content-Type": "application/json",
              },
              credentials: "include",
              body: JSON.stringify({
                sectionName: "updateStatus",
                type: "exportExcel",
                status: 5,
                exported: true,
                updated_date: new Date().toISOString(),
                company_name: row.supplierName,
                to: "Internal",
              }),
            })
              .then((res) => {
                if (res.status === 200) {
                  return res.json();
                }
                if (res.status === 401){
                  toast.error("Your session has expired. Please log in again.");
                  return null;
                }
                return Promise.reject(res);
              })
              .then((json) => {
                if (json.status == 200) {
                  return true;
                }
              })
              .catch((error) => {
                console.log(error);
              });
          }
        });
      }
    });
  } else if (isProductExtract) {
    if (data.length === 0) {
      console.error("sheetData is empty for index:");
      return;
    }

    data.forEach((sheetData) => {
      const sheetName = sheetData[0] || `Sheet${index + 1}`;
      let worksheet;

      if (Array.isArray(sheetName)) {
        const actualSheetName = sheetName[0];
        worksheet = workbook.addWorksheet(actualSheetName);
      } else {
        worksheet = workbook.addWorksheet(sheetName);
      }

      // console.log("Object.keys(sheetData[1])",Object?.keys(sheetData[1])? Object?.keys(sheetData[1]) : 'not there');
      let headers = Object?.keys(sheetData[1])
        ? Object.keys(sheetData[1])
        : "null";

      if (isMultiple) {
        headers = headers.slice(0, -1);
      }
      worksheet.addRow(headers);

      sheetData.slice(1).forEach((row, internalIndex) => {
        let rowData;
        rowData = headers.map((header) => row[header] || "");
        worksheet.addRow(rowData);
      });
    });
  } else {
    if (data.length === 0) {
      console.error("sheetData is empty for index:");
      return;
    }

    data.forEach((row, index) => {
      const sheetName = row[0] || `Sheet${index + 1}`;
      let worksheet;

      if (Array.isArray(sheetName)) {
        const actualSheetName = sheetName[0];
        worksheet = workbook.addWorksheet(actualSheetName);
      } else {
        worksheet = workbook.addWorksheet(sheetName);
      }

      let headers;
      if (index == 1) {
        headers = Object.keys(row[1]).slice(0, -2);
      } else {
        headers = Object.keys(row[1]);
      }

      worksheet.addRow(headers);
      for (let i = 1; i < row.length; i++) {
        if (index == 0 && i > 0) {
          supplierNames.push({
            supplierName: row[i]["Supplier name"],
            supplierCode: row[i]["Supplier code"],
          });
        }
        let rowData;
        if (index == 1) {
          rowData = headers.map((header) => row[i][header] || "").slice(0, -2);
        } else {
          rowData = headers.map((header) => row[i][header] || "");
        }
        worksheet.addRow(rowData);
        if (row[i].isEmergencyAndFinanceNotComplete) {
          let isEmergencyAndFinanceNotCompleteSupplier = {
            isEmergencyAndFinanceNotComplete:
              row[i].isEmergencyAndFinanceNotComplete,
            supplierName: row[i]["Supplier name"],
          };
          isEmergencyAndFinanceNotCompleteObj.push(
            isEmergencyAndFinanceNotCompleteSupplier
          );
        }

        if (index == 1) {
          fetch(`${serverAddress}suppliers/update-supplier/${row[i].id}`, {
            method: "PUT",
            headers: {
              Accept: "application/json",
              "Content-Type": "application/json",
            },
            credentials: "include",
            body: JSON.stringify({
              sectionName: "updateStatus",
              type: "exportExcel",
              status: 5,
              exported: true,
              updated_date: new Date().toISOString(),
              company_name: row["Supplier name"],
              to: "ISS",
            }),
          })
            .then((res) => {
              if (res.status === 200) {
                return res.json();
              }
              // if (res.status === 401){
              //   toast.error("Your session has expired. Please log in again.");
              //   setTimeout(() => {
              //     const redirectUrl = `/login?redirect=${encodeURIComponent(
              //       window.location.pathname
              //     )}`;
              //     logoutHandler(instance, redirectUrl);
              //   }, 3000);
              //   return null;
              // }
              return Promise.reject(res);
            })
            .then((json) => {
              if (json.status == 200) {
                return true;
              }
            })
            .catch((error) => {
              console.log(error);
            });
        }
      }
    });
  }

  const buffer = await workbook.xlsx.writeBuffer();
  const blob = new Blob([buffer], {
    type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
  });

  const blobUrl = URL.createObjectURL(blob);
  const a = document.createElement("a");
  a.style.display = "none";
  a.href = blobUrl;
  const now = new Date();
  const timestamp = `${now.getFullYear()}-${(now.getMonth() + 1)
    .toString()
    .padStart(2, "0")}-${now.getDate().toString().padStart(2, "0")}_${now
    .getHours()
    .toString()
    .padStart(2, "0")}-${now.getMinutes().toString().padStart(2, "0")}-${now
    .getSeconds()
    .toString()
    .padStart(2, "0")}`;

  if (isInternal) {
    a.download = `internal_export_${timestamp}_${
      data[0].length - 1 === 1 ? "S" : "G"
    }.xlsx`;
  } else if (isProductExtract) {
    if (request_no && !varietyRequest) {
      a.download = `${request_no}_product_export.xlsx`;
    } else if (request_no && varietyRequest) {
      a.download = `${request_no}_export.xlsx`;
    } else {
      a.download = `product_export.xlsx`;
    }
  } else {
    a.download = `iss_export_${timestamp}_${
      data.length === 1 ? "S" : "G"
    }.xlsx`;
  }
  let fileName;
  console.log("fle name",fileName);

  if (isInternal) {
    fileName = `internal_export_${timestamp}_${
      data[0].length - 1 === 1 ? "S" : "G"
    }.xlsx`;
  } else if (isProductExtract && !varietyRequest) {
    fileName = request_no
      ? `${request_no}_product_export.xlsx`
      : `product_export.xlsx`;
  } else if (varietyRequest) {
    fileName = request_no ? `${request_no}_NV_export.xlsx` : `NV_export.xlsx`;
  } else {
    fileName = `iss_export_${timestamp}_${data.length === 1 ? "S" : "G"}.xlsx`;
  }
  console.log("fle name --- \n",fileName);
  document.body.appendChild(a);
  a.click();

  // Clean up
  URL.revokeObjectURL(blobUrl);
  document.body.removeChild(a);

  toast.success("Request details extracted successfully.", {
    toastId: 22,
    position: "top-right",
    autoClose: 3000,
    pauseOnHover: false,
  });
  if (!varietyRequest) {
    const formData = new FormData();
    formData.append("file", blob, fileName);

    formData.append("company", company);
    formData.append("prophet_id", prophet_id);
    formData.append("name", userData?.name);
    formData.append("isInternal", isInternal);
    formData.append("exporterEmail", userData?.email);
    formData.append("requestorEmail", requestor_email);
    formData.append("isProductRequest", isProductExtract);
    formData.append("supplierNames", JSON.stringify(supplierNames));

    formData.append("onProductSubmit", onProductSubmit);
    formData.append("request_no", request_no);
    formData.append(
      "productEmailCommentPlaceholder",
      productEmailCommentPlaceholder
    );
    formData.append("productEmailParagraph", productEmailParagraph);

    const serializedData = JSON.stringify(isEmergencyAndFinanceNotCompleteObj);
    formData.append("isEmergencyAndFinanceNotCompleteObj", serializedData);

    const response = await fetch(`${serverAddress}email/send-email`, {
      method: "POST",
      body: formData,
      credentials: "include",
    });

    if (response.ok) {
      toast.success("Email sent", {
        toastId: 22,
        position: "top-right",
        autoClose: 3000,
        pauseOnHover: false,
      });
    } else if (response.status === 401) {
      // console.log("error YES 401");
      toast.error("Failed to send email", {
        toastId: 22,
        position: "top-right", //TODO change to top right
        autoClose: 3000,
        pauseOnHover: false,
      });
      return response.status;
    } else {
      toast.error("Failed to send email", {
        toastId: 22,
        position: "top-right",
        autoClose: 3000,
        pauseOnHover: false,
      });
    }
  }

  return true;
};

export default exportExcel;
