"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/products",{

/***/ "./pages/products.js":
/*!***************************!*\
  !*** ./pages/products.js ***!
  \***************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __N_SSP: function() { return /* binding */ __N_SSP; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _components_Layout__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/Layout */ \"./components/Layout.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _fortawesome_react_fontawesome__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @fortawesome/react-fontawesome */ \"./node_modules/@fortawesome/react-fontawesome/index.es.js\");\n/* harmony import */ var _fortawesome_free_solid_svg_icons__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! @fortawesome/free-solid-svg-icons */ \"./node_modules/@fortawesome/free-solid-svg-icons/index.mjs\");\n/* harmony import */ var ag_grid_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ag-grid-react */ \"./node_modules/ag-grid-react/lib/main.js\");\n/* harmony import */ var ag_grid_react__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(ag_grid_react__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var ag_grid_community_styles_ag_grid_css__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ag-grid-community/styles//ag-grid.css */ \"./node_modules/ag-grid-community/styles/ag-grid.css\");\n/* harmony import */ var ag_grid_community_styles_ag_grid_css__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(ag_grid_community_styles_ag_grid_css__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var ag_grid_community_styles_ag_theme_alpine_css__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ag-grid-community/styles//ag-theme-alpine.css */ \"./node_modules/ag-grid-community/styles/ag-theme-alpine.css\");\n/* harmony import */ var ag_grid_community_styles_ag_theme_alpine_css__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(ag_grid_community_styles_ag_theme_alpine_css__WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_7__);\n/* harmony import */ var react_toastify__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! react-toastify */ \"./node_modules/react-toastify/dist/react-toastify.esm.mjs\");\n/* harmony import */ var react_toastify_dist_ReactToastify_css__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! react-toastify/dist/ReactToastify.css */ \"./node_modules/react-toastify/dist/ReactToastify.css\");\n/* harmony import */ var react_toastify_dist_ReactToastify_css__WEBPACK_IMPORTED_MODULE_9___default = /*#__PURE__*/__webpack_require__.n(react_toastify_dist_ReactToastify_css__WEBPACK_IMPORTED_MODULE_9__);\n/* harmony import */ var _utils_loaders_loadingContext__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/utils/loaders/loadingContext */ \"./utils/loaders/loadingContext.js\");\n/* harmony import */ var react_select__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! react-select */ \"./node_modules/react-select/dist/react-select.esm.js\");\n/* harmony import */ var _utils_rolePermissionsContext__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/utils/rolePermissionsContext */ \"./utils/rolePermissionsContext.js\");\n/* harmony import */ var _utils_renderer_productStatusRenderer__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/utils/renderer/productStatusRenderer */ \"./utils/renderer/productStatusRenderer.js\");\n/* harmony import */ var _utils_renderer_productActionRenderer__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/utils/renderer/productActionRenderer */ \"./utils/renderer/productActionRenderer.js\");\n/* harmony import */ var _services_apiConfig__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/services/apiConfig */ \"./services/apiConfig.js\");\n/* harmony import */ var js_cookie__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! js-cookie */ \"./node_modules/js-cookie/dist/js.cookie.mjs\");\n/* harmony import */ var _azure_msal_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @azure/msal-react */ \"./node_modules/@azure/msal-react/dist/index.js\");\n/* harmony import */ var _components_ProductDialog__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! @/components/ProductDialog */ \"./components/ProductDialog.js\");\n/* harmony import */ var _utils_secureStorage__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! @/utils/secureStorage */ \"./utils/secureStorage.js\");\n/* harmony import */ var _utils_extractCompanyFromEmail__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! @/utils/extractCompanyFromEmail */ \"./utils/extractCompanyFromEmail.js\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst customSelectStyles = {\n    // Default style\n    control: (base)=>({\n            ...base,\n            height: \"28px\",\n            minHeight: \"28px\"\n        }),\n    // Style when the condition is true\n    option: (base, param)=>{\n        let { data } = param;\n        return {\n            ...base,\n            color: data.is_new == true ? \"red\" : \"\"\n        };\n    },\n    valueContainer: (provided, state)=>({\n            ...provided,\n            height: \"26px\",\n            width: \"300px\",\n            padding: \"0 6px\"\n        }),\n    input: (provided, state)=>({\n            ...provided,\n            margin: \"0px\"\n        }),\n    indicatorSeparator: (state)=>({\n            display: \"none\"\n        }),\n    indicatorsContainer: (provided, state)=>({\n            ...provided,\n            height: \"28px\"\n        })\n};\nconst Products = (param)=>{\n    let { userData, PreviousPage, pageTypeId } = param;\n    _s();\n    console.log(\"userData, PreviousPage, pageTypeId\", userData, PreviousPage, pageTypeId);\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_7__.useRouter)();\n    const [pageSize, setPageSize] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(15);\n    const gridRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)();\n    const [isRMChecked, setIsRMChecked] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [isFGChecked, setIsFGChecked] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [isNVChecked, setIsNVChecked] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const { setIsLoading } = (0,_utils_loaders_loadingContext__WEBPACK_IMPORTED_MODULE_10__.useLoading)();\n    const [rowData, setRowData] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [company, setCompany] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"\");\n    const [blockScreen, setBlockScreen] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [typeId, setTypeId] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(pageTypeId);\n    const [isDialogOpen, setIsDialogOpen] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [isIssUser, setIsIssUser] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [isIssProcurementTeamUser, setIsIssProcurementTeamUser] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [isIssAdmin, setisIssAdmin] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [nvStatus, setNVStatus] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [statusOptions] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([\n        {\n            value: \"Draft\",\n            label: \"Draft\"\n        },\n        {\n            value: \"Pending Review\",\n            label: \"Pending Review\"\n        },\n        {\n            value: \"Rejected\",\n            label: \"Rejected\"\n        },\n        {\n            value: \"ISS to Setup\",\n            label: \"ISS to Setup\"\n        },\n        {\n            value: \"Setup Completed\",\n            label: \"Setup Completed\"\n        },\n        {\n            value: \"Cancelled\",\n            label: \"Cancelled\"\n        }\n    ]);\n    const [filteredRowData, setFilteredRowData] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [checkedValue, setCheckedValue] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [incompleteToast, setIncompleteToast] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [searchInput, setSearchInput] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"\");\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        const userCompany = js_cookie__WEBPACK_IMPORTED_MODULE_15__[\"default\"].get(\"company\");\n        if (userCompany === \"issproduce\") {\n            setIsIssUser(true);\n        }\n        console.log(userData);\n        if (userData.department_id === 2) {\n            setIsIssProcurementTeamUser(true);\n            console.log(\"checking if it is an admin\", userData.role);\n            if (userData.role_id === 1) {\n                console.log(\"is admin\");\n                setisIssAdmin(true);\n            }\n        }\n        console.log(\"isIssProcurementTeamUser\", isIssProcurementTeamUser);\n    }, []);\n    const openDialog = ()=>{\n        setIsDialogOpen(true);\n    };\n    const closeDialog = ()=>{\n        setIsDialogOpen(false);\n    };\n    // const [isOpenOption, setIsOpenOption] = useState(false);\n    const [selectedRequestType, setSelectedRequestType] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(PreviousPage);\n    const handleTypeIdChange = (e)=>{\n        setSearchInput(\"\");\n        const filterTextBox = document.getElementById(\"filter-text-box\");\n        filterTextBox.value = \"\";\n        gridRef.current.api.setQuickFilter(\"\");\n        setNVStatus([]);\n        setTypeId(parseInt(e.target.value));\n        if (e.target.value === \"1\") {\n            setSelectedRequestType(\"rawMaterialRequest\");\n        } else if (e.target.value === \"3\") {\n            setSelectedRequestType(\"newVarietyRequest\");\n        } else if (e.target.value === \"4\") {\n            setSelectedRequestType(\"packagingform\");\n        }\n    };\n    // const closeOptionModal = () => {\n    //   setIsOpenOption(false);\n    // };\n    const handleRequestType = (type)=>{\n        setSelectedRequestType(type);\n    };\n    const handleFormType = ()=>{\n        if (selectedRequestType) {\n            localStorage.setItem(\"formType\", selectedRequestType);\n            console.log(selectedRequestType);\n            if (selectedRequestType === \"rawMaterialRequest\") {\n                router.push({\n                    pathname: \"/raw-material-request/add\"\n                });\n            // } else if (selectedRequestType == \"finishedProductRequest\") {\n            //   router.push({\n            //     pathname: `/finished-product-request/add` });\n            } else if (selectedRequestType == \"newVarietyRequest\") {\n                router.push({\n                    pathname: \"/variety/add\"\n                });\n            } else if (selectedRequestType == \"packagingform\") {\n                // router.push({ pathname: `/packaging/add` });\n                router.push({\n                    pathname: \"/packaging-form/add\"\n                }); //TODO\n            }\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        if (incompleteToast) {\n            react_toastify__WEBPACK_IMPORTED_MODULE_8__.toast.error(\"Kindly Submit the Request to Export it.\", {\n                position: \"top-right\",\n                autoClose: 1000,\n                hideProgressBar: false,\n                closeOnClick: true,\n                pauseOnHover: false,\n                draggable: true,\n                progress: undefined,\n                theme: \"light\"\n            });\n        }\n    }, [\n        incompleteToast\n    ]);\n    const [productsExtractData, setProductsExtractData] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([\n        [\n            \"Product Extract Data\"\n        ]\n    ]);\n    const [altfilExtractData, setAltfilExtractData] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([\n        [\n            \"Altfil Extract Data\"\n        ]\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        if (productsExtractData.length > 0) {\n            setCheckedValue(true);\n        } else {\n            setCheckedValue(false);\n        }\n    }, [\n        productsExtractData\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        if (typeof document !== \"undefined\") {\n            document.title = \"Products\";\n        }\n        setIsLoading(false);\n        getData().then((data)=>{\n            console.log(data);\n            const formattedData = data === null || data === void 0 ? void 0 : data.map((row)=>({\n                    id: row.id,\n                    code: row.code,\n                    action_id: row.action_id,\n                    request_no: row.request_no,\n                    type: row.product_type,\n                    product_type_name: row.product_type_label,\n                    product_type_id: row.product_type_id,\n                    reason: (row === null || row === void 0 ? void 0 : row.reason_description) ? row === null || row === void 0 ? void 0 : row.reason_description : \"Not Entered\",\n                    delivery_date: row.delivery_date ? new Date(row.delivery_date).toISOString().split(\"T\")[0] : \"Not Entered\",\n                    // delivery_date: new Date(row.delivery_date),\n                    product_code: row.master_product_code ? row.master_product_code : \"Not Entered\",\n                    product_description: row.product_description ? row.product_description : \"Not Entered\",\n                    originator: row.originator ? row.originator : row.originator_name,\n                    originator_email: row.originator_email,\n                    coo: row.coo ? row.coo : \"Not Entered\",\n                    status: row.status_label,\n                    master_product_code: row === null || row === void 0 ? void 0 : row.master_product_code,\n                    count_or_size: row === null || row === void 0 ? void 0 : row.count_or_size,\n                    units_in_outer: row === null || row === void 0 ? void 0 : row.units_in_outer,\n                    cases_per_pallet: row === null || row === void 0 ? void 0 : row.cases_per_pallet,\n                    outer_net_weight: row === null || row === void 0 ? void 0 : row.outer_net_weight,\n                    outer_gross_weight: row === null || row === void 0 ? void 0 : row.outer_gross_weight,\n                    sub_product_code: row === null || row === void 0 ? void 0 : row.sub_product_code,\n                    temperature_grade: row === null || row === void 0 ? void 0 : row.temperature_grade,\n                    intrastat_commodity_code: row === null || row === void 0 ? void 0 : row.intrastat_commodity_code,\n                    temperature_grade_name: row === null || row === void 0 ? void 0 : row.temperature_grade_name,\n                    intrastat_commodity_code_name: row === null || row === void 0 ? void 0 : row.intrastat_commodity_code_name,\n                    mark_variety_name: row === null || row === void 0 ? void 0 : row.mark_variety_name,\n                    intrastat_commodity_code_id: row === null || row === void 0 ? void 0 : row.intrastat_commodity_code_id,\n                    sort_group_id: row === null || row === void 0 ? void 0 : row.group_id,\n                    company: row === null || row === void 0 ? void 0 : row.company_name,\n                    temperature_grade_id: row === null || row === void 0 ? void 0 : row.temperature_grade_id,\n                    userText4: row === null || row === void 0 ? void 0 : row.userText4,\n                    userText5: row === null || row === void 0 ? void 0 : row.userText5,\n                    userText6: row === null || row === void 0 ? void 0 : row.userText6\n                }));\n            setCompany(formattedData[0].company);\n            setRowData(formattedData);\n            setFilteredRowData(formattedData);\n        }).catch((error)=>{\n            return error;\n        });\n    }, [\n        typeId\n    ]);\n    function getData() {\n        setRowData([]);\n        setFilteredRowData([]);\n        let serverAddress = _services_apiConfig__WEBPACK_IMPORTED_MODULE_14__.apiConfig.serverAddress;\n        const company_name = (userData === null || userData === void 0 ? void 0 : userData.company) || (0,_utils_extractCompanyFromEmail__WEBPACK_IMPORTED_MODULE_19__.extractCompanyFromEmail)(userData === null || userData === void 0 ? void 0 : userData.email);\n        const AdCompany = (userData === null || userData === void 0 ? void 0 : userData.companyName) || (userData === null || userData === void 0 ? void 0 : userData.ADCompanyName);\n        return fetch(\"\".concat(serverAddress, \"products/get-products/\").concat(company_name == \"dpsltd\" && AdCompany == \"DPS MS\" ? \"DPS MS\" : company_name, \"\\n      /\").concat(typeId), {\n            method: \"GET\",\n            headers: {\n                \"Content-Type\": \"application/json\"\n            },\n            credentials: \"include\"\n        }).then(async (res)=>{\n            if (res.status == 502) {\n                setBlockScreen(true);\n                return;\n            }\n            setBlockScreen(false);\n            if (res.status === 200) {\n                return res.json();\n            }\n            if (res.status === 400) {\n                react_toastify__WEBPACK_IMPORTED_MODULE_8__.toast.error(\"There was an error with your request. Please check your data and try again.\");\n            } else if (res.status === 401) {\n                react_toastify__WEBPACK_IMPORTED_MODULE_8__.toast.error(\"Your session has expired. Please log in again.\");\n                setTimeout(async ()=>{\n                    await (0,_utils_secureStorage__WEBPACK_IMPORTED_MODULE_18__.logout)();\n                    router.push(\"/login\");\n                }, 3000);\n            }\n            throw new Error(\"Failed to fetch data\");\n        }).catch((error)=>{\n            console.error(error);\n        });\n    }\n    const handlePageSizeChange = (event)=>{\n        const newPageSize = parseInt(event.target.value, 15);\n        setPageSize(newPageSize);\n        gridRef.current.api.paginationSetPageSize(newPageSize);\n    };\n    const handleStatusChange = (selectedOptions)=>{\n        setNVStatus(selectedOptions);\n        filterData(selectedOptions);\n    };\n    const filterData = (statuses)=>{\n        if (statuses.length == 0) {\n            setFilteredRowData(rowData);\n            return;\n        }\n        const filteredData = rowData.filter((row)=>statuses.some((status)=>status.value === row.status));\n        setFilteredRowData(filteredData.length > 0 ? filteredData : []);\n    };\n    // const clearFilters = () => {\n    //   setSelectedStatuses([]);\n    //   setFilteredRowData(rowData);\n    // };\n    const defaultColDef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useMemo)(()=>({\n            sortable: true,\n            filter: false,\n            resizable: true,\n            flex: 1,\n            suppressMenu: false\n        }));\n    const gridOptions = {\n        responsive: true\n    };\n    const columnDefs = [\n        {\n            headerName: \"Request No.\",\n            field: \"request_no\",\n            // cellRenderer: nameRenderer,\n            suppressMenu: true,\n            suppressSizeToFit: true,\n            suppressSizeToFit: false,\n            cellClass: \"ag-grid-checkbox-cell\",\n            flex: \"2%\",\n            filter: true\n        },\n        {\n            headerName: \"Reason\",\n            field: \"reason\",\n            flex: \"2%\",\n            cellRenderer: _utils_renderer_productStatusRenderer__WEBPACK_IMPORTED_MODULE_12__[\"default\"],\n            hide: typeId == 1 || 4 ? false : 0,\n            cellStyle: (params)=>{\n                if (params.value == \"Not Entered\") {\n                    return {\n                        color: \"#B31312\"\n                    };\n                }\n                return null;\n            }\n        },\n        {\n            headerName: \"Delivery Date\",\n            field: \"delivery_date\",\n            flex: \"2%\",\n            hide: typeId == 1 || 4 ? false : 0,\n            cellRenderer: (params)=>{\n                if (params.value === \"Not Entered\") {\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-[#B31312]\",\n                        children: \"Not Entered\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\products.js\",\n                        lineNumber: 383,\n                        columnNumber: 18\n                    }, undefined);\n                } else {\n                    // params.value\n                    // Format the date from yyyy-mm-dd to dd/mm/yyyy\n                    const dateParts = params.value.split(\"-\");\n                    const formattedDate = dateParts.reverse().join(\"/\");\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: formattedDate\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\products.js\",\n                        lineNumber: 390,\n                        columnNumber: 18\n                    }, undefined);\n                }\n            }\n        },\n        {\n            headerName: \"Product Code\",\n            field: \"product_code\",\n            flex: \"2%\",\n            // cellClass: (params) => {\n            //   return params.value === \"Not Entered\" ? 'not-entered' : '';\n            // }\n            cellStyle: (params)=>{\n                if (params.value == \"Not Entered\") {\n                    return {\n                        color: \"#B31312\"\n                    };\n                }\n                return null;\n            }\n        },\n        {\n            // headerName: \"Product description\",\n            // field: \"product_description\",\n            headerName: \"Product Description\",\n            field: \"product_description\",\n            // cellRenderer: statusRenderer,\n            flex: \"3%\",\n            cellStyle: (params)=>{\n                if (params.value == \"Not Entered\") {\n                    return {\n                        color: \"#B31312\"\n                    };\n                }\n                return null;\n            }\n        },\n        {\n            headerName: \"Originator\",\n            field: \"originator\",\n            headerName: \"Originator\",\n            field: \"originator\",\n            // cellRenderer: statusRenderer,\n            flex: \"2%\",\n            flex: \"2%\"\n        },\n        // {\n        //   headerName: \"COO\",\n        //   field: \"coo\",\n        //   headerName: \"COO\",\n        //   field: \"coo\",\n        //   // cellRenderer: statusRenderer,\n        //   flex: \"2%\",\n        //   cellStyle: (params) => {\n        //     if (params.value == \"Not Entered\") {\n        //       return { color: \"#B31312\" };\n        //     }\n        //     return null;\n        //   },\n        // },\n        {\n            headerName: \"Status\",\n            field: \"status\",\n            cellRenderer: _utils_renderer_productStatusRenderer__WEBPACK_IMPORTED_MODULE_12__[\"default\"],\n            cellStyle: ()=>({\n                    justifyContent: \"center\"\n                }),\n            flex: \"2%\",\n            hide: false\n        },\n        {\n            // field: \"Action(s)\",\n            field: typeId == 3 ? \"Action(s)\" : \"Action(s)\",\n            cellRenderer: (params)=>(0,_utils_renderer_productActionRenderer__WEBPACK_IMPORTED_MODULE_13__[\"default\"])(params, userData, userData.token, company, typeId, setIsLoading, isIssUser),\n            flex: \"2%\",\n            cellStyle: ()=>({}),\n            cellStyle: {\n                justifyContent: \"end\",\n                paddingRight: \"10px\"\n            },\n            sortable: false\n        },\n        {\n            field: \"role_num\",\n            hide: true\n        }\n    ];\n    const handleGridReady = (params)=>{\n        params.api.setColumnDefs(columnDefs);\n    };\n    const onFilterTextBoxChanged = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(()=>{\n        gridRef.current.api.setQuickFilter(document.getElementById(\"filter-text-box\").value);\n        setSearchInput(document.getElementById(\"filter-text-box\").value);\n    }, []);\n    const handleFilterProductType = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)((e)=>{\n        if (e.target.value == \"RM\") {\n            if (e.target.checked) {\n                setIsRMChecked(true);\n                setIsFGChecked(false);\n                setIsNVChecked(false);\n                gridRef.current.api.setQuickFilter(\"RM\");\n            } else {\n                setIsRMChecked(false);\n                gridRef.current.api.setQuickFilter(\"\");\n            }\n        } else if (e.target.value == \"FG\") {\n            if (e.target.checked) {\n                setIsFGChecked(true);\n                setIsRMChecked(false);\n                setIsNVChecked(false);\n                gridRef.current.api.setQuickFilter(\"FG\");\n            } else {\n                setIsFGChecked(false);\n                gridRef.current.api.setQuickFilter(\"\");\n            }\n        } else if (e.target.value == \"NV\") {\n            if (e.target.checked) {\n                setIsNVChecked(true);\n                setIsFGChecked(false);\n                setIsRMChecked(false);\n                gridRef.current.api.setQuickFilter(\"NV\");\n            } else {\n                setIsNVChecked(true);\n                gridRef.current.api.setQuickFilter(\"\");\n            }\n        } else {\n            setIsRMChecked(false);\n            setIsFGChecked(false);\n            setIsNVChecked(false);\n            gridRef.current.api.setQuickFilter(\"\");\n        }\n    }, []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_toastify__WEBPACK_IMPORTED_MODULE_8__.ToastContainer, {\n                limit: 1\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\products.js\",\n                lineNumber: 530,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Layout__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                userData: userData,\n                blockScreen: blockScreen,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mr-20 md:mr-12 lg:mr-14\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-row md:flex-col lg:flex-row justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"border border-light-gray rounded-md px-3 py-1\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"flex items-center gap-2 cursor-pointer\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                type: \"radio\",\n                                                                name: \"requestType\",\n                                                                value: \"1\",\n                                                                className: \"form-radio w-5 h-5 cursor-pointer\",\n                                                                checked: typeId === 1,\n                                                                onChange: handleTypeIdChange\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\products.js\",\n                                                                lineNumber: 538,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"labels\",\n                                                                children: \"Raw Materials\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\products.js\",\n                                                                lineNumber: 546,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\products.js\",\n                                                        lineNumber: 537,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\products.js\",\n                                                    lineNumber: 536,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"border border-light-gray rounded-md px-3 py-1\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"flex items-center gap-2 cursor-pointer\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                type: \"radio\",\n                                                                name: \"requestType\",\n                                                                value: \"3\",\n                                                                className: \"form-radio w-5 h-5 cursor-pointer\",\n                                                                checked: typeId === 3,\n                                                                onChange: handleTypeIdChange\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\products.js\",\n                                                                lineNumber: 562,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"labels\",\n                                                                children: \"New Variety\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\products.js\",\n                                                                lineNumber: 570,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\products.js\",\n                                                        lineNumber: 561,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\products.js\",\n                                                    lineNumber: 560,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                isIssUser && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"border border-light-gray rounded-md px-3 py-1\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"flex items-center gap-2 cursor-pointer\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                type: \"radio\",\n                                                                name: \"requestType\",\n                                                                value: \"4\",\n                                                                className: \"form-radio w-5 h-5 cursor-pointer\",\n                                                                checked: typeId === 4,\n                                                                onChange: handleTypeIdChange\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\products.js\",\n                                                                lineNumber: 576,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"labels\",\n                                                                children: \"Packaging\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\products.js\",\n                                                                lineNumber: 584,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\products.js\",\n                                                        lineNumber: 575,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\products.js\",\n                                                    lineNumber: 574,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\products.js\",\n                                            lineNumber: 535,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"labels\",\n                                            style: {\n                                                marginLeft: \"10px\"\n                                            },\n                                            children: typeId === 3 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_select__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                isMulti: true,\n                                                options: statusOptions,\n                                                onChange: handleStatusChange,\n                                                placeholder: \"Select Status...\",\n                                                className: \"basic-multi-select\",\n                                                classNamePrefix: \"select\",\n                                                value: nvStatus,\n                                                isSearchable: false,\n                                                styles: customSelectStyles\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\products.js\",\n                                                lineNumber: 591,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\products.js\",\n                                            lineNumber: 589,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center px-3 w-auto md:w-fit lg:w-auto md:py-2 lg:py-1 md:mb-3 lg:mb-0\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\products.js\",\n                                            lineNumber: 607,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\products.js\",\n                                    lineNumber: 534,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"relative block w-[47vh] text-gray-400 mt-0 pt-0\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"absolute px-4 py-1 pt-[0.4rem] 2xl:pt-1.5 text-black\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fortawesome_react_fontawesome__WEBPACK_IMPORTED_MODULE_3__.FontAwesomeIcon, {\n                                                        icon: _fortawesome_free_solid_svg_icons__WEBPACK_IMPORTED_MODULE_21__.faSearch,\n                                                        className: \"fw-bold\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\products.js\",\n                                                        lineNumber: 638,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\products.js\",\n                                                    lineNumber: 637,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"text\",\n                                                    id: \"filter-text-box\",\n                                                    placeholder: \"Search...\",\n                                                    onInput: onFilterTextBoxChanged,\n                                                    value: searchInput,\n                                                    className: \"block w-full px-4 pl-10 text-gray-500 placeholder-gray-400 searchbar border rounded-lg appearance-none form-input focus:outline-none\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\products.js\",\n                                                    lineNumber: 640,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\products.js\",\n                                            lineNumber: 636,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ProductDialog__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                            isOpen: openDialog,\n                                            onClose: closeDialog,\n                                            handleFormType: handleFormType,\n                                            selectedRequestType: selectedRequestType,\n                                            isIssUser: isIssUser,\n                                            isIssProcurmentUser: isIssProcurementTeamUser,\n                                            handleRequestType: handleRequestType,\n                                            admin: isIssAdmin\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\products.js\",\n                                            lineNumber: 659,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\products.js\",\n                                    lineNumber: 635,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\products.js\",\n                            lineNumber: 533,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"my-5\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative ag-theme-alpine !rounded-md\",\n                                style: {\n                                    height: \"calc(100vh - 151px)\"\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ag_grid_react__WEBPACK_IMPORTED_MODULE_4__.AgGridReact, {\n                                        rowData: filteredRowData,\n                                        ref: gridRef,\n                                        columnDefs: columnDefs,\n                                        defaultColDef: defaultColDef,\n                                        suppressRowClickSelection: true,\n                                        pagination: true,\n                                        paginationPageSize: pageSize,\n                                        onPageSizeChanged: handlePageSizeChange,\n                                        tooltipShowDelay: 0,\n                                        tooltipHideDelay: 1000,\n                                        onGridReady: handleGridReady,\n                                        gridOptions: gridOptions\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\products.js\",\n                                        lineNumber: 676,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-start mt-2 pagination-style\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            htmlFor: \"page-size-select pagination\",\n                                            className: \"inputs\",\n                                            children: [\n                                                \"Show\",\n                                                \" \",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                    id: \"page-size-select\",\n                                                    onChange: handlePageSizeChange,\n                                                    value: pageSize,\n                                                    className: \"focus:outline-none\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: 10,\n                                                            children: \"10\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\products.js\",\n                                                            lineNumber: 699,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: 15,\n                                                            children: \"15\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\products.js\",\n                                                            lineNumber: 700,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: 25,\n                                                            children: \"25\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\products.js\",\n                                                            lineNumber: 701,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: 50,\n                                                            children: \"50\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\products.js\",\n                                                            lineNumber: 702,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: 100,\n                                                            children: \"100\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\products.js\",\n                                                            lineNumber: 703,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\products.js\",\n                                                    lineNumber: 693,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                \" \",\n                                                \"Entries\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\products.js\",\n                                            lineNumber: 691,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\products.js\",\n                                        lineNumber: 690,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\products.js\",\n                                lineNumber: 672,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\products.js\",\n                            lineNumber: 671,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\products.js\",\n                    lineNumber: 532,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\products.js\",\n                lineNumber: 531,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true);\n};\n_s(Products, \"x02n8KTM2A42sUdrK7Zft5e7jZs=\", false, function() {\n    return [\n        next_router__WEBPACK_IMPORTED_MODULE_7__.useRouter,\n        _utils_loaders_loadingContext__WEBPACK_IMPORTED_MODULE_10__.useLoading\n    ];\n});\n_c = Products;\nvar __N_SSP = true;\n/* harmony default export */ __webpack_exports__[\"default\"] = (Products);\nvar _c;\n$RefreshReg$(_c, \"Products\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevExports = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevExports) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports on update so we can compare the boundary\n                // signatures.\n                module.hot.dispose(function (data) {\n                    data.prevExports = currentExports;\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevExports !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevExports, currentExports)) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevExports !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./pages/products.js\n"));

/***/ })

});