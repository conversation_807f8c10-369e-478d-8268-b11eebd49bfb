"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/_app",{

/***/ "./pages/_app.js":
/*!***********************!*\
  !*** ./pages/_app.js ***!
  \***********************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ App; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _styles_globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/styles/globals.css */ \"./styles/globals.css\");\n/* harmony import */ var _styles_globals_css__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_styles_globals_css__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _utils_auth_msalProvider__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/utils/auth/msalProvider */ \"./utils/auth/msalProvider.jsx\");\n/* harmony import */ var _utils_loaders_loadingContext__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/utils/loaders/loadingContext */ \"./utils/loaders/loadingContext.js\");\n/* harmony import */ var _utils_loaders_overlaySpinner__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/utils/loaders/overlaySpinner */ \"./utils/loaders/overlaySpinner.js\");\n/* harmony import */ var _utils_userContext__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../utils/userContext */ \"./utils/userContext.js\");\n/* harmony import */ var _utils_serviceCustomerContext__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/utils/serviceCustomerContext */ \"./utils/serviceCustomerContext.js\");\n/* harmony import */ var _utils_rolePermissionsContext__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/utils/rolePermissionsContext */ \"./utils/rolePermissionsContext.js\");\n/* harmony import */ var _components_noConnectionAlertBox__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/noConnectionAlertBox */ \"./components/noConnectionAlertBox.js\");\n/* harmony import */ var _utils_themeContext__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/utils/themeContext */ \"./utils/themeContext.js\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\nfunction App(param) {\n    let { Component, pageProps } = param;\n    _s();\n    const [isOpen, setIsOpen] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(true);\n    const isOnline = useNetwork();\n    function useNetwork() {\n        if (true) {\n            // Client-side-only code\n            const [isOnline, setNetwork] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(window.navigator.onLine);\n            (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n                window.addEventListener(\"offline\", ()=>setNetwork(window.navigator.onLine));\n                window.addEventListener(\"online\", ()=>setNetwork(window.navigator.onLine));\n            });\n            return isOnline;\n        }\n    }\n    ;\n    const closeModal = ()=>{\n        setIsOpen(false);\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        if (isOnline) {\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                classNam: \"no-connection\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        children: \"No Internet Connection\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\_app.js\",\n                        lineNumber: 38,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"noConnectionAlertBox\", {\n                        isOpen: isOpen,\n                        closeModal: closeModal\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\_app.js\",\n                        lineNumber: 39,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\_app.js\",\n                lineNumber: 37,\n                columnNumber: 7\n            }, this);\n        }\n    }, []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_utils_auth_msalProvider__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_utils_themeContext__WEBPACK_IMPORTED_MODULE_10__.ThemeProvider, {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_utils_userContext__WEBPACK_IMPORTED_MODULE_6__.UserProvider, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_utils_serviceCustomerContext__WEBPACK_IMPORTED_MODULE_7__.ServiceCustomerProvider, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_utils_rolePermissionsContext__WEBPACK_IMPORTED_MODULE_8__.PermissionsProvider, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_utils_loaders_loadingContext__WEBPACK_IMPORTED_MODULE_4__.LoadingProvider, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_utils_loaders_overlaySpinner__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {}, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\_app.js\",\n                                    lineNumber: 51,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Component, {\n                                    ...pageProps\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\_app.js\",\n                                    lineNumber: 52,\n                                    columnNumber: 11\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\_app.js\",\n                            lineNumber: 50,\n                            columnNumber: 9\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\_app.js\",\n                        lineNumber: 49,\n                        columnNumber: 7\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\_app.js\",\n                    lineNumber: 48,\n                    columnNumber: 7\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\_app.js\",\n                lineNumber: 47,\n                columnNumber: 7\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\_app.js\",\n            lineNumber: 46,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\_app.js\",\n        lineNumber: 45,\n        columnNumber: 5\n    }, this);\n}\n_s(App, \"ZN1wroM70TG0wUXJ8uHLTOJ6UUs=\", true);\n_c = App;\nvar _c;\n$RefreshReg$(_c, \"App\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevExports = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevExports) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports on update so we can compare the boundary\n                // signatures.\n                module.hot.dispose(function (data) {\n                    data.prevExports = currentExports;\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevExports !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevExports, currentExports)) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevExports !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./pages/_app.js\n"));

/***/ }),

/***/ "./utils/serviceCustomerContext.js":
/*!*****************************************!*\
  !*** ./utils/serviceCustomerContext.js ***!
  \*****************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ServiceCustomerProvider: function() { return /* binding */ ServiceCustomerProvider; },\n/* harmony export */   useServiceCustomers: function() { return /* binding */ useServiceCustomers; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n\nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\nconst ServiceCustomersContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)();\nconst ServiceCustomerProvider = (param)=>{\n    let { children } = param;\n    _s();\n    const [serviceCustomers, setServiceCustomers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        value: \"All\",\n        label: \"All Service Customers\"\n    });\n    const updateServiceCustomersList = (newServiceCustomersList)=>{\n        setServiceCustomers(newServiceCustomersList);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ServiceCustomersContext.Provider, {\n        value: {\n            serviceCustomers,\n            updateServiceCustomersList\n        },\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\utils\\\\serviceCustomerContext.js\",\n        lineNumber: 16,\n        columnNumber: 5\n    }, undefined);\n};\n_s(ServiceCustomerProvider, \"LPLn5p4JT+4A/N0+/Ewt9G6hFko=\");\n_c = ServiceCustomerProvider;\nconst useServiceCustomers = ()=>{\n    _s1();\n    return (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(ServiceCustomersContext);\n};\n_s1(useServiceCustomers, \"gDsCjeeItUuvgOWf1v4qoK9RF6k=\");\nvar _c;\n$RefreshReg$(_c, \"ServiceCustomerProvider\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevExports = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevExports) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports on update so we can compare the boundary\n                // signatures.\n                module.hot.dispose(function (data) {\n                    data.prevExports = currentExports;\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevExports !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevExports, currentExports)) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevExports !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./utils/serviceCustomerContext.js\n"));

/***/ })

});