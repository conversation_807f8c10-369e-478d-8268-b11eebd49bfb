import { useRouter } from 'next/router';
import { useEffect } from 'react';

export default function Unauthorized() {
  const router = useRouter();

  useEffect(() => {
    // Auto-redirect to suppliers page after 5 seconds
    const timer = setTimeout(() => {
      router.push('/suppliers');
    }, 5000);

    return () => clearTimeout(timer);
  }, [router]);

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50">
      <div className="max-w-md w-full space-y-8">
        <div>
          <h2 className="mt-6 text-center text-3xl font-extrabold text-gray-900">
            Access Denied
          </h2>
          <p className="mt-2 text-center text-sm text-gray-600">
            You don't have permission to access this page.
          </p>
          <p className="mt-2 text-center text-sm text-gray-600">
            Redirecting to suppliers page in 5 seconds...
          </p>
        </div>
        
        <div className="flex justify-center space-x-4">
          <button
            onClick={() => router.push('/suppliers')}
            className="group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
          >
            Go to Suppliers
          </button>
        </div>
      </div>
    </div>
  );
} 