import React, { useState, useEffect, memo } from "react";
import Select from "react-select";
import { apiConfig } from "@/services/apiConfig";
import FilterBottomBar from "./FilterBottomBar";
import FilterMenu from "../common/FilterMenu";
import {
  filtersBasedOnZeros,
  getProductsFromIdbByMasterCodeAndBusinessUnit,
  replaceCustomerTotalsWithTheNew,
  replaceDataWithTheNew,
  replaceEfcCalenderWithTheNew,
  replaceStdCalenderWithTheNew,
} from "@/utils/whatif/utils/indexedDB";
import getSelectOptions from "@/utils/whatif/utils/getSelectOptions";
import getDistinctBusinessUnitsAndMasterCodes from "@/utils/whatif/utils/getDistinctBusinessUnitsAndMasterCodes";
import ProductDropDown from "./ProductDropDown";
import { Tooltip } from "@fluentui/react-components";
import { getData } from "@/utils/whatif/utils/getProductData";
//
import { useMsal } from "@azure/msal-react";
import Cookies from "js-cookie";
import { toast, ToastContainer } from "react-toastify";
const Filter = (props) => {
  const {
    checkedStates,
    setCheckedStates,
    currentData,
    setCurrentData,
    setCurrentTotal,
    userData,
    toggleQuaterlyTotals,
    loading,
    setLoading,
    setCtxCalenderData,
    ctxCalenderData,
    setNoOfWeeks,
    customerList,
    businessUnits,
    masterCodes,
    productList,
    currentCustomer,
    setCurrentCustomer,
    setCtxStdCalenderData,
    setBusinessUnits,
    setMasterCodes,
    gridStates,
    setGridStates,
    checkboxes,
    setCheckboxes,
    selectWhereTotalIsZero,
    setSelectWhereTotalIsZero,
    selectWhereTotalIsNotZero,
    setSelectWhereTotalIsNotZero,
    currentYear,
    setCurrentYear
  } = props;
  const [isAllQuarterDataFetched, setIsAllQuarterDataFetched] = useState(false);

  let filters = Cookies.get("filters");
  filters = filters ? JSON.parse(filters) : null;

  const { instance } = useMsal();
  const [selectedQuarters, setSelectedQuarters] = useState(
    Object.keys(checkboxes).filter(
      (key) => key !== "all" && checkboxes[key] === true
    )
  );
  const [selectedQuarterString, setSelectedQuarterString] = useState("");

  const [businessUnitsSO, setBusinessUnitSO] = useState([]);
  const [masterCodeSO, setMasterCodeSO] = useState([]);

  const [selectedBusinessUnit, setSelectedBusinessUnit] = useState({
    value:
      filters && filters.bu === "all" ? "all" : filters ? filters.bu : "all",
    label:
      filters && filters.bu === "all"
        ? "All Business Units"
        : filters
        ? filters.bu
        : "All Business Units",
  });
  const [selectedMasterCode, setSelectedMasterCode] = useState({
    value:
      filters && filters.mc === "all" ? "all" : filters ? filters.mc : "all",
    label:
      filters && filters.mc === "all"
        ? "All Master Codes"
        : filters
        ? filters.mc
        : "All Master Codes",
  });

  const [productType, setProductType] = useState("finishedproduct"); //finishedproduct OR rawmaterial
  const [searchPreferedProducts, setSearchPreferedProducts] = useState(false); //Search only the products exclusively searched

  //States for Year dropdown
  const [years, setYears] = useState([]);

  useEffect(() => {
    if (typeof window !== 'undefined') {
      localStorage.setItem("selectedQuarterString", selectedQuarterString);
    }
  }, [selectedQuarterString]);

  const updateData = async (data) => {
    replaceDataWithTheNew(data);

    let dataFiltered = data;
    if (!selectWhereTotalIsZero || !selectWhereTotalIsNotZero) {
      dataFiltered = filtersBasedOnZeros(
        data,
        selectWhereTotalIsZero,
        selectWhereTotalIsNotZero,
        selectedQuarters
      );
    }
    setCurrentData(dataFiltered);

    const result = await getDistinctBusinessUnitsAndMasterCodes(dataFiltered);
    setBusinessUnits(result.businessUnits);
    setMasterCodes(result.masterCodes);

    setSelectedBusinessUnit({ value: "all", label: "All Business Units" });
    setSelectedMasterCode({ value: "all", label: "All Master Codes" });
  };

  const handleCustomerChange = async (data) => {
    
    setLoading(true);
    setSearchPreferedProducts(false);
    if (data) {
      setCurrentCustomer(data.value);

      const json = await getData(
        `get-required-filtered-data/${data.value}?year=${
          currentYear.value
        }&quarters=${selectedQuarterString}&totals=${true}&calendarData=${false}`,
        userData.token
      );
      if (!json) {
        const redirectUrl = `/login?redirect=${encodeURIComponent(window.location.pathname)}`;
logoutHandler(instance,redirectUrl);;
        setLoading(false);
        return;
      }
      if (json?.length == 0) {
        setLoading(false);
        return;
      }
      const dataWithoutLast = json?.whatifData?.slice(0, -1);


      await updateData(dataWithoutLast);
      replaceCustomerTotalsWithTheNew(json?.totals);
      setCurrentTotal(json?.totals);
      setLoading(false);
    }
  };
  const filterOnBusinessUnitAndMaterCode = async (data, type) => {
    if (type === "bu") {
      setSelectedBusinessUnit(data);
    } else if (type === "mc") {
      setSelectedMasterCode(data);
    }

    const products = await getProductsFromIdbByMasterCodeAndBusinessUnit(
      type === "mc"
        ? data.value === "all"
          ? null
          : data.value
        : selectedMasterCode.value === "all"
        ? null
        : selectedMasterCode.value,
      type === "bu"
        ? data.value === "all"
          ? null
          : data.value
        : selectedBusinessUnit.value === "all"
        ? null
        : selectedBusinessUnit.value,
      false,
      productType,
      selectWhereTotalIsZero,
      selectWhereTotalIsNotZero,
      selectedQuarters
    );

    setCurrentData(products);
  };

  const reloadHandler = async () => {
    setLoading(true);
    setSearchPreferedProducts(false);

    const json = await getData(
      `get-required-filtered-data/${currentCustomer}?year=${currentYear.value}&quarters=${selectedQuarterString}&totals=true&calendarData=true`,
      userData.token
    );
    if (!json) {
      const redirectUrl = `/login?redirect=${encodeURIComponent(window.location.pathname)}`;
logoutHandler(instance,redirectUrl);;
      setLoading(false);
      return;
    }
    if (json?.length == 0) {
      setLoading(false);
      return;
    }

    const dataWithoutLast = json?.whatifData?.slice(0, -1);

    replaceDataWithTheNew(dataWithoutLast);
    let dataFiltered = dataWithoutLast;

    dataFiltered = filtersBasedOnZeros(
      dataWithoutLast,
      selectWhereTotalIsZero,
      selectWhereTotalIsNotZero,
      selectedQuarters,
      selectedMasterCode.value === "all" ? null : selectedMasterCode.value,
      selectedBusinessUnit.value === "all" ? null : selectedBusinessUnit.value
    );

    const result = await getDistinctBusinessUnitsAndMasterCodes(
      dataWithoutLast
    );
    setBusinessUnits(result.businessUnits);
    setMasterCodes(result.masterCodes);
    setCurrentData(dataFiltered);
    replaceCustomerTotalsWithTheNew(json.totals);

    setNoOfWeeks(json.defaultCalendarData.length);
    setCtxCalenderData(json.defaultCalendarData);
    replaceEfcCalenderWithTheNew(json.defaultCalendarData);

    setCtxStdCalenderData(json.stdCalendarData);
    replaceStdCalenderWithTheNew(json.stdCalendarData);

    setLoading(false);
  };

  useEffect(() => {
    setSelectedQuarters(
      Object.keys(checkboxes).filter(
        (key) => key !== "all" && checkboxes[key] === true
      )
    );
  }, [checkboxes]);

  useEffect(() => {
    setBusinessUnitSO(
      getSelectOptions(businessUnits, true, "All Business Units")
    );
    setMasterCodeSO(getSelectOptions(masterCodes, true, "All Master Codes"));
  }, [businessUnits, masterCodes]);

  const saveFilterHandler = () => {
    const choices = {
      customer: currentCustomer,
      bu: selectedBusinessUnit.value,
      mc: selectedMasterCode.value,
      totalZero: selectWhereTotalIsZero,
      totalNotZero: selectWhereTotalIsNotZero,
      // quarters: checkboxes,
      columns: checkedStates,
      grids: gridStates,
      selectedProducts: searchPreferedProducts
        ? currentData.map((p) => p.pkey)
        : null,
    };
    
    Cookies.set("filters", JSON.stringify(choices));

    toast.success(`Filters Saved`, {
      theme: "colored",
      autoClose: 3000,
    });
  };

  useEffect(() => {
    const currentDate = new Date();
    const currentMonth = currentDate.getMonth() + 1;
    const currentYear = currentDate.getFullYear();

    const financialYear = currentMonth >= 10 ? currentYear + 1 : currentYear;

    // Generate options from 2012 up to the calculated financial year
    const startYear = 2021;
    const endYear = financialYear;
    const yearOptions = [];

    for (let year = startYear; year <= endYear; year++) {
      yearOptions.push({
        value: `${year}`,
        label: `${year-1} - ${year}`,
      });
    }

    setYears(yearOptions);

    setCurrentYear({
      value: `${financialYear}`,
      label: `${financialYear -1 } - ${financialYear}`,
    });
  }, []);

  const handleCustYearChange = async (data) => {
    setLoading(true);
    setSearchPreferedProducts(false);

    if (data) {
      setIsAllQuarterDataFetched(false)
      setCurrentYear(data);

      const json = await getData(
        `get-required-filtered-data/${currentCustomer}?year=${data.value}&quarters=${selectedQuarterString}&totals=true&calendarData=true`,
        userData.token
      );
      if (!json) {
        const redirectUrl = `/login?redirect=${encodeURIComponent(window.location.pathname)}`;
logoutHandler(instance,redirectUrl);;
        setLoading(false);
        return;
      }
      if (json?.length == 0) {
        setLoading(false);
        return;
      }

      const dataWithoutLast = json?.whatifData?.slice(0, -1);

      replaceDataWithTheNew(dataWithoutLast);
      let dataFiltered = dataWithoutLast;
      dataFiltered = filtersBasedOnZeros(
        dataWithoutLast,
        selectWhereTotalIsZero,
        selectWhereTotalIsNotZero,
        selectedQuarters,
        selectedMasterCode.value === "all" ? null : selectedMasterCode.value,
        selectedBusinessUnit.value === "all" ? null : selectedBusinessUnit.value
      );

      const result = await getDistinctBusinessUnitsAndMasterCodes(
        dataWithoutLast
      );
      setBusinessUnits(result.businessUnits);
      setMasterCodes(result.masterCodes);
      setCurrentData(dataFiltered);
      replaceCustomerTotalsWithTheNew(json.totals);

      setNoOfWeeks(json.defaultCalendarData.length);
      setCtxCalenderData(json.defaultCalendarData);
      replaceEfcCalenderWithTheNew(json.defaultCalendarData);

      setCtxStdCalenderData(json.stdCalendarData);
      replaceStdCalenderWithTheNew(json.stdCalendarData);

      setCurrentTotal(json?.totals);

      setLoading(false);
    }
  };

  if (!businessUnitsSO || !masterCodeSO) {
    return <>Loading...</>;
  }

  
  const [checkedValues, setCheckedValues] = useState({
    weeks: ["EFCWeek"],
    columns: ["skuDescription", "caseSize"],
  });

  const menuItems = [
    {
      header: "WEEKS",
      items: [
        { name: "weeks", value: "EFCWeek", label: "EFC Week" },
        { name: "weeks", value: "calenderWeek", label: "Calendar Week" },
        // { name: "weeks", value: "promoCycles", label: "Promo Cycles" },
        // { name: "weeks", value: "retailerWeek", label: "Retailer Week" },
      ],
    },
    {
      header: "COLUMNS",
      items: [
        { name: "columns", value: "customerCode", label: "Customer Code" },
        { name: "columns", value: "masterProduct", label: "Master Product" },
        { name: "columns", value: "businessUnit", label: "Business Unit" },
        { name: "columns", value: "skuDescription", label: "SKU Description" },
        { name: "columns", value: "altFillID", label: "Alt Fill ID" },
        { name: "columns", value: "caseSize", label: "Case Size" },
      ],
    },
  ];

  return (
    <div>
      <ToastContainer limit={1} />
      <div className="bg-white px-4 py-2 flex flex-row gap-4 items-center w-full shadow-sm">
        <div className="w-1/8 z-20 pr-3 border-r border-gray-300">
          <Select
            options={years}
            placeholder="Select a Year"
            value={currentYear}
            onChange={(e) => handleCustYearChange(e, "yearFilter")}
            className="border-0 border-white"
            isDisabled={loading}
          />
        </div>
        {/* customer Filter start*/}
        <div className="w-1/5 z-20 pr-3 border-r border-gray-300">
          <Select
            options={customerList}
            placeholder="Select a customer"
            defaultValue={
              currentCustomer
                ? {
                    value: currentCustomer,
                    label: currentCustomer,
                  }
                : customerList[1]
            }
            onChange={handleCustomerChange}
            isSearchable={true}
            instanceId="1"
            className="border-0 border-white"
            isDisabled={loading}
          />
        </div>
        {/* customer Filter end*/}

        {/* business unit Filter start*/}
        <div className="w-1/5 z-20 pr-3 border-r border-gray-300">
          <Select
            options={businessUnitsSO}
            placeholder="Select a Business Unit"
            isSearchable={true}
            className="border-0 border-white"
            value={selectedBusinessUnit}
            onChange={(data) => {
              filterOnBusinessUnitAndMaterCode(data, "bu");
              setSearchPreferedProducts(false);
            }}
            isDisabled={loading}
          />
        </div>
        {/* business unit Filter end*/}

        {/* master code Filter start*/}
        <div className="w-1/5 z-20 pr-3 border-r border-gray-300">
          <Select
            options={masterCodeSO}
            placeholder="Select a Master Code"
            isSearchable={true}
            className="border-0 border-white"
            value={selectedMasterCode}
            onChange={(data) => {
              filterOnBusinessUnitAndMaterCode(data, "mc");
              setSearchPreferedProducts(false);
            }}
            isDisabled={loading}
          />
        </div>
        {/* master code Filter end*/}

        <div className="flex flex-1 z-20 pr-3 border-r border-gray-300">
          <ProductDropDown
            loading={loading}
            currentData={currentData}
            setCurrentData={setCurrentData}
            selectedBusinessUnit={selectedBusinessUnit}
            selectedMasterCode={selectedMasterCode}
            productType={productType}
            setProductType={setProductType}
            selectWhereTotalIsZero={selectWhereTotalIsZero}
            setSelectWhereTotalIsZero={setSelectWhereTotalIsZero}
            selectWhereTotalIsNotZero={selectWhereTotalIsNotZero}
            setSelectWhereTotalIsNotZero={setSelectWhereTotalIsNotZero}
            selectedQuarters={selectedQuarters}
            setSearchPreferedProducts={setSearchPreferedProducts}
          />
        </div>

        <div className="flex gap-3  justify-between">
          <Tooltip
            content="Save Filter"
            relationship="label"
            className="!bg-white"
          >
            <button
              type="button"
              className="!border-0 !min-w-fit hover:bg-gray-100 py-2 px-3 rounded-sm"
              disabled={loading}
              onClick={saveFilterHandler}
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                viewBox="0 0 512 512"
                className="w-5 h-5"
                fill="#000"
              >
                <path d="M222.2 319.2c.5 .5 1.1 .8 1.8 .8s1.4-.3 1.8-.8L350.2 187.3c1.2-1.2 1.8-2.9 1.8-4.6c0-3.7-3-6.7-6.7-6.7L288 176c-8.8 0-16-7.2-16-16l0-120c0-4.4-3.6-8-8-8l-80 0c-4.4 0-8 3.6-8 8l0 120c0 8.8-7.2 16-16 16l-57.3 0c-3.7 0-6.7 3-6.7 6.7c0 1.7 .7 3.3 1.8 4.6L222.2 319.2zM224 352c-9.5 0-18.6-3.9-25.1-10.8L74.5 209.2C67.8 202 64 192.5 64 182.7c0-21.4 17.3-38.7 38.7-38.7l41.3 0 0-104c0-22.1 17.9-40 40-40l80 0c22.1 0 40 17.9 40 40l0 104 41.3 0c21.4 0 38.7 17.3 38.7 38.7c0 9.9-3.8 19.3-10.5 26.5L249.1 341.2c-6.5 6.9-15.6 10.8-25.1 10.8zM32 336l0 96c0 26.5 21.5 48 48 48l288 0c26.5 0 48-21.5 48-48l0-96c0-8.8 7.2-16 16-16s16 7.2 16 16l0 96c0 44.2-35.8 80-80 80L80 512c-44.2 0-80-35.8-80-80l0-96c0-8.8 7.2-16 16-16s16 7.2 16 16z" />
              </svg>
            </button>
          </Tooltip>
        </div>

        <div className="flex gap-3  justify-between">
          {/* Menu start*/}
          <FilterMenu
            setCheckedStates={setCheckedStates}
            reloadHandler={reloadHandler}
            menuItems={menuItems}
            checkedValues={checkedValues}
            setCheckedValues={setCheckedValues} // Ensure this is a valid function
            userData={userData}
            customerList={customerList}
          />
          {/* Menu end*/}
        </div>
      </div>
      <FilterBottomBar
        currentSelectedKeys={currentData.map((p) => p.pkey)}
        setCurrentData={setCurrentData}
        toggleQuaterlyTotals={toggleQuaterlyTotals}
        setSelectedQuarterString={setSelectedQuarterString}
        setCtxCalenderData={setCtxCalenderData}
        ctxCalenderData={ctxCalenderData}
        setNoOfWeeks={setNoOfWeeks}
        currentCustomer={currentCustomer}
        setCtxStdCalenderData={setCtxStdCalenderData}
        loading={loading}
        setLoading={setLoading}
        gridStates={gridStates}
        setGridStates={setGridStates}
        checkboxes={checkboxes}
        setCheckboxes={setCheckboxes}
        selectWhereTotalIsZero={selectWhereTotalIsZero}
        selectWhereTotalIsNotZero={selectWhereTotalIsNotZero}
        selectedBusinessUnit={selectedBusinessUnit}
        selectedMasterCode={selectedMasterCode}
        productType={productType}
        searchPreferedProducts={searchPreferedProducts}
        currentYear={currentYear}
        isAllQuarterDataFetched={isAllQuarterDataFetched}
        setIsAllQuarterDataFetched={setIsAllQuarterDataFetched}
      />
    </div>
  );
};

export default Filter;

