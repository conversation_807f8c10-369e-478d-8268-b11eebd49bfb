"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/service_level",{

/***/ "./components/service_level/SLTable.js":
/*!*********************************************!*\
  !*** ./components/service_level/SLTable.js ***!
  \*********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _ViewDetails__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./ViewDetails */ \"./components/service_level/ViewDetails.jsx\");\n/* harmony import */ var _services_apiConfig__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/services/apiConfig */ \"./services/apiConfig.js\");\n/* harmony import */ var _ReasonsDetails__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./ReasonsDetails */ \"./components/service_level/ReasonsDetails.jsx\");\n/* harmony import */ var _utils_getCookieData__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/utils/getCookieData */ \"./utils/getCookieData.js\");\n/* harmony import */ var _common_NoDataFound__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../common/NoDataFound */ \"./components/common/NoDataFound.jsx\");\n/* harmony import */ var _fluentui_react_components__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @fluentui/react-components */ \"./node_modules/@fluentui/react-components/lib/index.js\");\n/* harmony import */ var react_toastify__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! react-toastify */ \"./node_modules/react-toastify/dist/react-toastify.esm.mjs\");\n/* harmony import */ var js_cookie__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! js-cookie */ \"./node_modules/js-cookie/dist/js.cookie.mjs\");\n/* harmony import */ var _utils_secureStorage__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/utils/secureStorage */ \"./utils/secureStorage.js\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n//\n\n\nconst SLTable = (param)=>{\n    let { gridcolumnRefs, gridcolumnWidths, setIsTableRendered, checkedStates, customerSLData, userData, selectedProducts, seeAll, recordCount, setRecordsCount, searchBoxContent, slFilters, selectedMasterProductCode, selectedCustomer, toggle, selectedRows, setSelectedRows, isBulkUpdate, setIsBulkUpdate, bulkUpdateData, isOpen, setIsOpen, setBulkUpdateData, masterProducts, bulkDeleteOrdIds, setBulkDeleteOrdIds, setNoDataExists, setShowLoadingMessage, setAllReasonsSubreasons, allReasonsSubreasons, selectedReasons, setSelectedReasons, selectedSubReasons, setSelectedSubReasons, showLoadingMessage } = param;\n    var _checkedStates_columns, _checkedStates_columns1, _checkedStates_columns2, _checkedStates_columns3, _checkedStates_columns4, _checkedStates_columns5, _checkedStates_columns6, _columnTotals_totalValue;\n    _s();\n    let ADCompanyName = (userData === null || userData === void 0 ? void 0 : userData.companyName) || (userData === null || userData === void 0 ? void 0 : userData.ADCompanyName);\n    const [reasonsMasterList, setReasonsMasterList] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [parentReasonList, setParentReasonList] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [reasonsData, setReasonsData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [allSelectedProducts, setAllSelectedProducts] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isHeaderChecked, setIsHeaderChecked] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [columnTotals, setColumnTotals] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const setMapForReasonsParentsAndTheirCorrespondingChildren = ()=>{\n        setParentReasonList((prevList)=>{\n            const parentReasonIds = reasonsMasterList.filter((typeOfReason)=>typeOfReason.parent_id === null).map((typeOfReason)=>typeOfReason);\n            const uniqueParentIds = new Set([\n                ...prevList,\n                ...parentReasonIds\n            ]);\n            return Array.from(uniqueParentIds); // Update the state\n        });\n    };\n    const handleCheckboxChange = (data)=>{\n        setSelectedRows((prevSelected)=>{\n            let updatedSelected;\n            if (prevSelected.includes(data)) {\n                updatedSelected = prevSelected.filter((item)=>item !== data);\n            } else {\n                updatedSelected = [\n                    ...prevSelected,\n                    data\n                ];\n            }\n            const ordIds = updatedSelected.filter((product)=>product.CASES_ADDED_REASONS > 0).map((product)=>product.ORD_ID);\n            setBulkDeleteOrdIds(ordIds.length > 0 ? ordIds : []);\n            return updatedSelected;\n        });\n    };\n    const handleHeaderCheckboxChange = (data)=>{\n        const selectableRows = data.filter((product)=>(product.LOCKED_BY === null || product.LOCKED_BY === \"\") && product.CASES_DIFFERENCE != 0);\n        const ordIds = selectableRows.filter((product)=>product.CASES_ADDED_REASONS > 0).map((product)=>product.ORD_ID);\n        if (ordIds.length > 0) {\n            setBulkDeleteOrdIds(ordIds);\n        } else {\n            setBulkDeleteOrdIds([]);\n        }\n        if (selectedRows.length === selectableRows.length) {\n            // If all rows are selected, deselect all\n            setSelectedRows([]);\n            setIsHeaderChecked(false);\n        } else {\n            // If not all rows are selected, select all\n            setSelectedRows(selectableRows);\n            setIsHeaderChecked(true);\n        }\n    };\n    //#region getReasons\n    const fetchReasonData = async (orderId, customerName)=>{\n        setReasonsData([]);\n        const serverAddress = _services_apiConfig__WEBPACK_IMPORTED_MODULE_3__.apiConfig.serverAddress;\n        try {\n            const serviceLevelReasons = await fetch(\"\".concat(serverAddress, \"serviceLevel/get-service-level-reasons/\").concat(orderId, \"?customerName=\").concat(customerName), {\n                method: \"GET\",\n                headers: {\n                    Accept: \"application/json\",\n                    \"Content-Type\": \"application/json\"\n                },\n                credentials: \"include\"\n            }).then((res)=>{\n                if (res.status === 400) {\n                    react_toastify__WEBPACK_IMPORTED_MODULE_7__.toast.error(\"There was an error with your request. Please check your data and try again.\");\n                    return null;\n                } else if (res.status === 401) {\n                    react_toastify__WEBPACK_IMPORTED_MODULE_7__.toast.error(\"Your session has expired. Please log in again.\");\n                    setTimeout(async ()=>{\n                        await (0,_utils_secureStorage__WEBPACK_IMPORTED_MODULE_9__.logout)();\n                        const redirectUrl = \"/login?redirect=\".concat(encodeURIComponent(window.location.pathname));\n                        router.push(redirectUrl);\n                    }, 3000);\n                    return null;\n                }\n                if (res.status === 200) {\n                    return res.json();\n                }\n            }) // Ensure you parse the JSON\n            .then((data)=>{\n                if (!isBulkUpdate) {\n                    setReasonsData(data);\n                }\n            });\n        } catch (error) {\n            console.log(\"error in fetching\", error);\n        }\n    };\n    //#endregion\n    //#region getMasterReasons\n    const fetchReasonMaster = async ()=>{\n        // console.log(\"service Level Reasons Master code start\");\n        try {\n            const serverAddress = _services_apiConfig__WEBPACK_IMPORTED_MODULE_3__.apiConfig.serverAddress;\n            const user = (0,_utils_getCookieData__WEBPACK_IMPORTED_MODULE_5__.getCookieData)(\"user\");\n            const serviceLevelReasonsMaster = await fetch(\"\".concat(serverAddress, \"serviceLevel/get-service-level-reasons-master\"), {\n                method: \"GET\",\n                headers: {\n                    Accept: \"application/json\",\n                    \"Content-Type\": \"application/json\",\n                    Authorization: \"Bearer \".concat(user.token)\n                }\n            }).then((res)=>{\n                if (res.status === 400) {\n                    react_toastify__WEBPACK_IMPORTED_MODULE_7__.toast.error(\"There was an error with your request. Please check your data and try again.\");\n                    return null;\n                } else if (res.status === 401) {\n                    react_toastify__WEBPACK_IMPORTED_MODULE_7__.toast.error(\"Your session has expired. Please log in again.\");\n                    setTimeout(()=>{\n                        localStorage.removeItem(\"superUser\");\n                        js_cookie__WEBPACK_IMPORTED_MODULE_8__[\"default\"].remove(\"company\");\n                        js_cookie__WEBPACK_IMPORTED_MODULE_8__[\"default\"].remove(\"ADCompanyName\");\n                        localStorage.removeItem(\"id\");\n                        localStorage.removeItem(\"name\");\n                        localStorage.removeItem(\"role\");\n                        localStorage.removeItem(\"email\");\n                        js_cookie__WEBPACK_IMPORTED_MODULE_8__[\"default\"].remove(\"user\");\n                        js_cookie__WEBPACK_IMPORTED_MODULE_8__[\"default\"].remove(\"theme\");\n                        js_cookie__WEBPACK_IMPORTED_MODULE_8__[\"default\"].remove(\"token\");\n                        const redirectUrl = \"/login?redirect=\".concat(encodeURIComponent(window.location.pathname));\n                        logoutHandler(instance, redirectUrl);\n                    }, 3000);\n                    return null;\n                }\n                if (res.status === 200) {\n                    return res.json();\n                }\n            }) // Ensure you parse the JSON\n            .then((data)=>{\n                setReasonsMasterList(data);\n            });\n        } catch (err) {\n            console.log(\"error in fetching\", err);\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        setIsTableRendered((prev)=>!prev);\n    }, [\n        setIsTableRendered\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        fetchReasonMaster(); // To fetch the master list of reasons\n        setMapForReasonsParentsAndTheirCorrespondingChildren();\n    }, []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const cookieSelectedProducts = (slFilters === null || slFilters === void 0 ? void 0 : slFilters.selectedProducts) || [];\n        const stateSelectedProducts = selectedProducts || [];\n        const combinedSelectedProducts = [\n            ...cookieSelectedProducts.map((product)=>({\n                    label: product.productDescription,\n                    value: product.altFillId\n                })),\n            ...stateSelectedProducts.map((product)=>({\n                    label: product.label,\n                    value: product.value\n                }))\n        ];\n        const customerSLArray = Object.values(customerSLData);\n        let filteredProducts = combinedSelectedProducts.length ? customerSLArray.filter((product)=>combinedSelectedProducts.some((selectedProduct)=>selectedProduct.label === product.PRODUCT_DESCRIPTION && (!searchBoxContent || !!searchBoxContent && product.ORD_NUMBER.toString().includes(searchBoxContent)))) : customerSLArray.filter((product)=>!searchBoxContent || !!searchBoxContent && product.ORD_NUMBER.toString().includes(searchBoxContent));\n        if (selectedMasterProductCode !== \"all\") {\n            filteredProducts = filteredProducts.filter((prod)=>prod.MASTER_PRODUCT_CODE == selectedMasterProductCode);\n        }\n        if (selectedReasons.length > 0) {\n            filteredProducts = filteredProducts.filter((product)=>product.reasons && product.reasons.some((reason)=>selectedReasons.includes(reason.MAIN_REASON_ID)));\n        } else if (selectedSubReasons.length > 0) {\n            filteredProducts = filteredProducts.filter((product)=>product.reasons && product.reasons.some((reason)=>selectedSubReasons.includes(reason.SUB_REASON_ID)));\n        }\n        if (filteredProducts.length > 0) {\n            setAllSelectedProducts(filteredProducts);\n            const visibleRows = filteredProducts.filter((data)=>{\n                if (!seeAll && data.CASES_DIFFERENCE - data.CASES_ADDED_REASONS === 0) {\n                    return false;\n                }\n                return true;\n            });\n            const totals = visibleRows.reduce((acc, data)=>{\n                acc.casesOrdered += data.CASES_ORIGINAL || 0;\n                acc.casesDelivered += data.CASES_DELIVERED || 0;\n                acc.casesDifference += data.CASES_DIFFERENCE || 0;\n                acc.addedReasons += data.CASES_ADDED_REASONS || 0;\n                acc.totalValue += (data.CASE_SIZE || 0) * (data.UNIT_PRICE || 0) * (data.CASES_DIFFERENCE || 0);\n                return acc;\n            }, {\n                casesOrdered: 0,\n                casesDelivered: 0,\n                casesDifference: 0,\n                addedReasons: 0,\n                totalValue: 0\n            });\n            setColumnTotals(totals);\n        }\n        var _filteredProducts_length;\n        setRecordsCount((_filteredProducts_length = filteredProducts === null || filteredProducts === void 0 ? void 0 : filteredProducts.length) !== null && _filteredProducts_length !== void 0 ? _filteredProducts_length : 0);\n    }, [\n        toggle,\n        customerSLData,\n        selectedProducts,\n        slFilters,\n        searchBoxContent,\n        selectedMasterProductCode,\n        selectedReasons,\n        selectedSubReasons\n    ]);\n    const [selectedData, setSelectedData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const handleViewDetailsClick = (data)=>{\n        setSelectedRows([\n            data\n        ]);\n        setIsOpen(true);\n    };\n    var _columnTotals_casesOrdered, _columnTotals_casesDelivered, _columnTotals_casesDifference, _columnTotals_addedReasons, _columnTotals_totalValue1;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"!fontFamily-poppinsregular\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_toastify__WEBPACK_IMPORTED_MODULE_7__.ToastContainer, {\n                limit: 1\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                lineNumber: 328,\n                columnNumber: 7\n            }, undefined),\n            recordCount != 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                className: \"service-level-grid relative table-fixed w-full text-sm !fontFamily-poppinsregular\",\n                cellSpacing: 0,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                    className: \"sticky top-0 z-10 !w-10\",\n                                    ref: gridcolumnRefs.checkboxRef,\n                                    style: {\n                                        left: \"\".concat(0, \"px\")\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-full flex justify-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"checkbox\",\n                                            className: \"w-5 h-5 text-blue border-theme-blue2 rounded accent-skin-primary text-center\",\n                                            disabled: selectedMasterProductCode == \"all\" && masterProducts.length > 2 || selectedCustomer == \"All Customers\",\n                                            onChange: ()=>handleHeaderCheckboxChange(allSelectedProducts),\n                                            checked: isHeaderChecked\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                            lineNumber: 343,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                        lineNumber: 342,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                    lineNumber: 337,\n                                    columnNumber: 15\n                                }, undefined),\n                                checkedStates.columns.depotdate && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                    className: \"sticky top-0 z-10\",\n                                    ref: gridcolumnRefs.depotdate,\n                                    style: {\n                                        left: \"\".concat(gridcolumnWidths.checkboxWidth, \"px\")\n                                    },\n                                    children: \"Depot Date\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                    lineNumber: 359,\n                                    columnNumber: 17\n                                }, undefined),\n                                ADCompanyName == \"Integrated Service Solutions Ltd\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                    className: \"sticky top-0 z-10 !text-center\",\n                                    ref: gridcolumnRefs.serviceCustomers,\n                                    style: {\n                                        left: \"\".concat(gridcolumnWidths.checkboxWidth, \"px\")\n                                    },\n                                    children: [\n                                        \"Service Customers\",\n                                        \" \"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                    lineNumber: 367,\n                                    columnNumber: 69\n                                }, undefined),\n                                checkedStates.columns.weekNo && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                    className: \"sticky top-0  z-10\",\n                                    ref: gridcolumnRefs.weekNo,\n                                    style: {\n                                        left: \"\".concat(ADCompanyName == \"Integrated Service Solutions Ltd\" ? gridcolumnWidths.serviceCustomerswidth : gridcolumnWidths.depotdate, \"px\")\n                                    },\n                                    children: \"Week No\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                    lineNumber: 376,\n                                    columnNumber: 17\n                                }, undefined),\n                                checkedStates.columns.altfill && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                    className: \"sticky top-0  z-10\",\n                                    ref: gridcolumnRefs.altfill,\n                                    style: {\n                                        left: \"\".concat(gridcolumnWidths.weekNo, \"px\")\n                                    },\n                                    children: \"Alt Fill\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                    lineNumber: 387,\n                                    columnNumber: 17\n                                }, undefined),\n                                checkedStates.columns.customer && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                    className: \"!w-28 sticky top-0  z-10 \",\n                                    ref: gridcolumnRefs.customer,\n                                    style: {\n                                        left: \"\".concat(gridcolumnWidths.altfill, \"px\")\n                                    },\n                                    children: \"Customer\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                    lineNumber: 396,\n                                    columnNumber: 17\n                                }, undefined),\n                                checkedStates.columns.salesorder && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                    className: \"sticky top-0  z-10 \",\n                                    ref: gridcolumnRefs.salesorder,\n                                    style: {\n                                        left: \"\".concat(gridcolumnWidths.customer, \"px\")\n                                    },\n                                    children: \"Sales Order\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                    lineNumber: 405,\n                                    columnNumber: 17\n                                }, undefined),\n                                checkedStates.columns.salesOrderId && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                    className: \"sticky top-0  z-10 \",\n                                    ref: gridcolumnRefs.salesOrderId,\n                                    style: {\n                                        left: \"\".concat(gridcolumnWidths.customer, \"px\")\n                                    },\n                                    children: \"Order Det Id\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                    lineNumber: 414,\n                                    columnNumber: 17\n                                }, undefined),\n                                checkedStates.columns.product && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                    className: \"!w-80 sticky top-0  z-10 \",\n                                    ref: gridcolumnRefs.product,\n                                    style: {\n                                        left: \"\".concat(gridcolumnWidths.salesorder, \"px\")\n                                    },\n                                    children: \"Product\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                    lineNumber: 423,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                    className: \"top-0  z-10 !text-center\",\n                                    ref: gridcolumnRefs.casesize,\n                                    style: {\n                                        left: \"\".concat(gridcolumnWidths.product, \"px\")\n                                    },\n                                    children: \"Case Size\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                    lineNumber: 431,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                    className: \"sticky top-0 !text-center\",\n                                    children: \"Cases Ordered\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                    lineNumber: 438,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                    className: \"sticky top-0 !text-center\",\n                                    children: \"Cases Delivered\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                    lineNumber: 439,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                    className: \"sticky top-0 !text-center\",\n                                    children: \"Cases Different\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                    lineNumber: 440,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                    className: \"sticky top-0 !text-center\",\n                                    children: \"Added Reasons\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                    lineNumber: 441,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                    className: \"!w-28 sticky top-0 !text-center\",\n                                    children: \"Order Fulfillment %\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                    lineNumber: 442,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                    className: \"sticky top-0 !text-center\",\n                                    children: \"Unit Price\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                    lineNumber: 445,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                    className: \"sticky top-0 !text-center\",\n                                    children: \"Case Value\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                    lineNumber: 446,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                    className: \"sticky top-0 !text-center\",\n                                    children: \"Total Value\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                    lineNumber: 447,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                    className: \"sticky top-0 !text-center\",\n                                    children: \"Status\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                    lineNumber: 448,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                    className: \"!w-60 relative\",\n                                    children: [\n                                        \"Reason\",\n                                        \" \",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ReasonsDetails__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                            reasonsData: allReasonsSubreasons.reasons,\n                                            selectedReasons: selectedReasons,\n                                            selectedSubReasons: selectedSubReasons,\n                                            setSelectedReasons: setSelectedReasons,\n                                            setSelectedSubReasons: setSelectedSubReasons,\n                                            type: \"reasonsList\",\n                                            seeAll: seeAll\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                            lineNumber: 451,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                    lineNumber: 449,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                    className: \"!w-60 relative\",\n                                    children: [\n                                        \"Sub Reason\",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ReasonsDetails__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                            reasonsData: allReasonsSubreasons.subReasons,\n                                            selectedReasons: selectedReasons,\n                                            selectedSubReasons: selectedSubReasons,\n                                            setSelectedReasons: setSelectedReasons,\n                                            setSelectedSubReasons: setSelectedSubReasons,\n                                            type: \"subReasonsList\",\n                                            seeAll: seeAll\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                            lineNumber: 463,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                    lineNumber: 461,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                    className: \"!w-52\",\n                                    children: \"Comments\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                    lineNumber: 473,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                    className: \"!text-center\",\n                                    children: \"Actions\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                    lineNumber: 474,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                            lineNumber: 336,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                        lineNumber: 335,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                className: \"font-bold bg-[#f3f8ff]\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                        className: \"text-center sticky top-0 font-bold text-sm\",\n                                        style: {\n                                            left: \"\".concat(0, \"px\")\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                        lineNumber: 480,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    (checkedStates === null || checkedStates === void 0 ? void 0 : (_checkedStates_columns = checkedStates.columns) === null || _checkedStates_columns === void 0 ? void 0 : _checkedStates_columns.depotdate) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                        className: \"text-center sticky top-0 font-bold text-sm\",\n                                        style: {\n                                            left: \"\".concat(gridcolumnWidths.checkboxWidth, \"px\")\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                        lineNumber: 485,\n                                        columnNumber: 19\n                                    }, undefined),\n                                    ADCompanyName == \"Integrated Service Solutions Ltd\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                        className: \"text-center sticky top-0 font-bold text-sm\",\n                                        style: {\n                                            left: \"\".concat(gridcolumnWidths.depotDate, \"px\")\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                        lineNumber: 490,\n                                        columnNumber: 71\n                                    }, undefined),\n                                    (checkedStates === null || checkedStates === void 0 ? void 0 : (_checkedStates_columns1 = checkedStates.columns) === null || _checkedStates_columns1 === void 0 ? void 0 : _checkedStates_columns1.weekNo) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                        className: \"sticky top-0 font-bold text-center text-sm\",\n                                        style: {\n                                            left: \"\".concat(gridcolumnWidths.serviceCustomerswidth, \"px\")\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                        lineNumber: 496,\n                                        columnNumber: 19\n                                    }, undefined),\n                                    (checkedStates === null || checkedStates === void 0 ? void 0 : (_checkedStates_columns2 = checkedStates.columns) === null || _checkedStates_columns2 === void 0 ? void 0 : _checkedStates_columns2.altfill) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                        className: \"sticky top-0 font-bold text-sm\",\n                                        style: {\n                                            left: \"\".concat(gridcolumnWidths.weekNo, \"px\")\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                        lineNumber: 502,\n                                        columnNumber: 19\n                                    }, undefined),\n                                    (checkedStates === null || checkedStates === void 0 ? void 0 : (_checkedStates_columns3 = checkedStates.columns) === null || _checkedStates_columns3 === void 0 ? void 0 : _checkedStates_columns3.customer) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                        className: \"sticky top-0 font-bold text-sm\",\n                                        style: {\n                                            left: \"\".concat(gridcolumnWidths.altfill, \"px\")\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                        lineNumber: 508,\n                                        columnNumber: 19\n                                    }, undefined),\n                                    (checkedStates === null || checkedStates === void 0 ? void 0 : (_checkedStates_columns4 = checkedStates.columns) === null || _checkedStates_columns4 === void 0 ? void 0 : _checkedStates_columns4.salesorder) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                        className: \"sticky top-0 font-bold text-sm\",\n                                        style: {\n                                            left: \"\".concat(gridcolumnWidths.customer, \"px\")\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                        lineNumber: 514,\n                                        columnNumber: 19\n                                    }, undefined),\n                                    (checkedStates === null || checkedStates === void 0 ? void 0 : (_checkedStates_columns5 = checkedStates.columns) === null || _checkedStates_columns5 === void 0 ? void 0 : _checkedStates_columns5.salesOrderId) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                        className: \"sticky top-0 font-bold text-sm\",\n                                        style: {\n                                            left: \"\".concat(gridcolumnWidths.customer, \"px\")\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                        lineNumber: 520,\n                                        columnNumber: 19\n                                    }, undefined),\n                                    (checkedStates === null || checkedStates === void 0 ? void 0 : (_checkedStates_columns6 = checkedStates.columns) === null || _checkedStates_columns6 === void 0 ? void 0 : _checkedStates_columns6.product) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                        className: \"sticky top-0 font-bold text-sm\",\n                                        style: {\n                                            left: \"\".concat(gridcolumnWidths.salesorder, \"px\")\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                        lineNumber: 526,\n                                        columnNumber: 19\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                        className: \"text-center top-0 font-bold text-sm\",\n                                        style: {\n                                            left: \"\".concat(gridcolumnWidths.product, \"px\")\n                                        },\n                                        children: \"TOTAL\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                        lineNumber: 531,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                        className: \"!text-center sticky top-0 font-bold text-sm\",\n                                        children: (_columnTotals_casesOrdered = columnTotals.casesOrdered) !== null && _columnTotals_casesOrdered !== void 0 ? _columnTotals_casesOrdered : \"-\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                        lineNumber: 538,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                        className: \"!text-center sticky top-0 font-bold text-sm\",\n                                        children: (_columnTotals_casesDelivered = columnTotals.casesDelivered) !== null && _columnTotals_casesDelivered !== void 0 ? _columnTotals_casesDelivered : \"-\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                        lineNumber: 541,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                        className: \"!text-center sticky top-0 font-bold text-sm\",\n                                        children: (_columnTotals_casesDifference = columnTotals.casesDifference) !== null && _columnTotals_casesDifference !== void 0 ? _columnTotals_casesDifference : \"-\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                        lineNumber: 544,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                        className: \"!text-center sticky top-0 font-bold text-sm\",\n                                        children: (_columnTotals_addedReasons = columnTotals.addedReasons) !== null && _columnTotals_addedReasons !== void 0 ? _columnTotals_addedReasons : \"-\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                        lineNumber: 547,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                        className: \"sticky top-0 font-bold text-sm\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                        lineNumber: 550,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                        className: \"sticky top-0 font-bold text-sm\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                        lineNumber: 551,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                        className: \"sticky top-0 font-bold text-sm\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                        lineNumber: 552,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                        className: \"!text-center sticky top-0 font-bold text-sm\",\n                                        children: ((_columnTotals_totalValue1 = columnTotals.totalValue) !== null && _columnTotals_totalValue1 !== void 0 ? _columnTotals_totalValue1 : \"-\") ? \"\\xa3\".concat((_columnTotals_totalValue = columnTotals.totalValue) === null || _columnTotals_totalValue === void 0 ? void 0 : _columnTotals_totalValue.toFixed(2)) : \"-\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                        lineNumber: 553,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                        className: \"sticky top-0 font-bold text-sm\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                        lineNumber: 559,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                        className: \"sticky top-0 font-bold text-sm\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                        lineNumber: 560,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                        className: \"sticky top-0 font-bold text-sm\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                        lineNumber: 561,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                        className: \"sticky top-0 font-bold text-sm\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                        lineNumber: 562,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                        className: \"sticky top-0 font-bold text-sm\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                        lineNumber: 563,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                lineNumber: 479,\n                                columnNumber: 15\n                            }, undefined),\n                            allSelectedProducts.map((data, index)=>{\n                                var _data_reasons, _checkedStates_columns, _checkedStates_columns1, _checkedStates_columns2, _checkedStates_columns3, _checkedStates_columns4, _checkedStates_columns5, _checkedStates_columns6, _data_SERVICE_LEVEL_PERCENT, _data_UNIT_PRICE, _this, _this1, _data_reasons_, _data_reasons_1, _data_reasons_2;\n                                if (!seeAll && data.CASES_DIFFERENCE - data.CASES_ADDED_REASONS == 0) return;\n                                const tooltip = data.LOCKED_BY ? \"\".concat(data.LOCKED_BY, \" is currently editing this order.\") : data.NEW_LINE_FLAG == 1 ? \"This is an additional order.\" : \"\";\n                                const isSelected = selectedRows === null || selectedRows === void 0 ? void 0 : selectedRows.includes(data);\n                                const rowHighlight = \"\".concat(isSelected ? \"bg-locked-products\" : ((_data_reasons = data.reasons) === null || _data_reasons === void 0 ? void 0 : _data_reasons.length) > 0 && data.CASES_DIFFERENCE - data.CASES_ADDED_REASONS > 0 ? \"bg-needsupdate-status\" : !!data.LOCKED_BY ? \"bg-locked-products\" : data.NEW_LINE_FLAG == 1 ? \"bg-volumechange-status\" : \"\");\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                            className: \"sticky top-0 font-normal text-sm \".concat(rowHighlight ? \"!\".concat(rowHighlight) : \"\"),\n                                            style: {\n                                                left: \"\".concat(0, \"px\")\n                                            },\n                                            title: tooltip,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-full flex justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"checkbox\",\n                                                    title: \"\".concat(data.NEW_LINE_FLAG == 1 ? \"This is an additional order.\" : \"\"),\n                                                    className: \"w-5 h-5 text-blue border-theme-blue2 rounded accent-skin-primary\",\n                                                    onChange: ()=>handleCheckboxChange(data),\n                                                    disabled: selectedMasterProductCode === \"all\" && masterProducts.length > 2 || selectedCustomer === \"All Customers\" || data.LOCKED_BY !== null && data.LOCKED_BY !== \"\" || data.CASES_DIFFERENCE == 0,\n                                                    // disabled={data.CASES_ADDED_REASONS!=0}\n                                                    checked: selectedRows.includes(data)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                                    lineNumber: 601,\n                                                    columnNumber: 23\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                                lineNumber: 600,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                            lineNumber: 593,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        (checkedStates === null || checkedStates === void 0 ? void 0 : (_checkedStates_columns = checkedStates.columns) === null || _checkedStates_columns === void 0 ? void 0 : _checkedStates_columns.depotdate) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                            className: \"sticky top-0 font-normal text-sm \".concat(rowHighlight ? \"!\".concat(rowHighlight) : \"\"),\n                                            style: {\n                                                left: \"\".concat(gridcolumnWidths.checkboxWidth, \"px\")\n                                            },\n                                            title: tooltip,\n                                            children: data.DEPOT_DATE && new Date(data.DEPOT_DATE).toLocaleDateString(\"en-GB\")\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                            lineNumber: 623,\n                                            columnNumber: 21\n                                        }, undefined),\n                                        ADCompanyName == \"Integrated Service Solutions Ltd\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                            className: \"sticky top-0 font-normal text-sm \".concat(rowHighlight ? \"!\".concat(rowHighlight) : \"\"),\n                                            style: {\n                                                left: \"\".concat(gridcolumnWidths.checkboxWidth, \"px\")\n                                            },\n                                            title: tooltip,\n                                            children: data.SERVICE_CUSTOMERS && data.SERVICE_CUSTOMERS\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                            lineNumber: 634,\n                                            columnNumber: 73\n                                        }, undefined),\n                                        (checkedStates === null || checkedStates === void 0 ? void 0 : (_checkedStates_columns1 = checkedStates.columns) === null || _checkedStates_columns1 === void 0 ? void 0 : _checkedStates_columns1.weekNo) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                            className: \"sticky top-0 font-normal text-center text-sm \".concat(rowHighlight ? \"!\".concat(rowHighlight) : \"\"),\n                                            style: {\n                                                left: \"\".concat(gridcolumnWidths.depotdate, \"px\")\n                                            },\n                                            title: tooltip,\n                                            children: data === null || data === void 0 ? void 0 : data.FISCAL_WEEK\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                            lineNumber: 645,\n                                            columnNumber: 21\n                                        }, undefined),\n                                        (checkedStates === null || checkedStates === void 0 ? void 0 : (_checkedStates_columns2 = checkedStates.columns) === null || _checkedStates_columns2 === void 0 ? void 0 : _checkedStates_columns2.altfill) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                            className: \"sticky top-0 font-normal  text-sm \".concat(rowHighlight ? \"!\".concat(rowHighlight) : \"\"),\n                                            style: {\n                                                left: \"\".concat(gridcolumnWidths.weekNo, \"px\")\n                                            },\n                                            title: tooltip,\n                                            children: data.ALTFILID\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                            lineNumber: 656,\n                                            columnNumber: 21\n                                        }, undefined),\n                                        (checkedStates === null || checkedStates === void 0 ? void 0 : (_checkedStates_columns3 = checkedStates.columns) === null || _checkedStates_columns3 === void 0 ? void 0 : _checkedStates_columns3.customer) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                            className: \"sticky top-0 font-normal  text-sm \".concat(rowHighlight ? \"!\".concat(rowHighlight) : \"\"),\n                                            style: {\n                                                left: \"\".concat(gridcolumnWidths.altfill, \"px\")\n                                            },\n                                            title: tooltip,\n                                            children: data.CUSTOMER\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                            lineNumber: 667,\n                                            columnNumber: 21\n                                        }, undefined),\n                                        (checkedStates === null || checkedStates === void 0 ? void 0 : (_checkedStates_columns4 = checkedStates.columns) === null || _checkedStates_columns4 === void 0 ? void 0 : _checkedStates_columns4.salesorder) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                            className: \"sticky top-0 font-normal text-sm \".concat(rowHighlight ? \"!\".concat(rowHighlight) : \"\"),\n                                            style: {\n                                                left: \"\".concat(gridcolumnWidths.customer, \"px\")\n                                            },\n                                            title: tooltip,\n                                            children: data.ORD_NUMBER\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                            lineNumber: 678,\n                                            columnNumber: 21\n                                        }, undefined),\n                                        (checkedStates === null || checkedStates === void 0 ? void 0 : (_checkedStates_columns5 = checkedStates.columns) === null || _checkedStates_columns5 === void 0 ? void 0 : _checkedStates_columns5.salesOrderId) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                            className: \"sticky top-0 font-normal text-sm \".concat(rowHighlight ? \"!\".concat(rowHighlight) : \"\"),\n                                            style: {\n                                                left: \"\".concat(gridcolumnWidths.customer, \"px\")\n                                            },\n                                            title: tooltip,\n                                            children: data.ORD_ID\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                            lineNumber: 689,\n                                            columnNumber: 21\n                                        }, undefined),\n                                        (checkedStates === null || checkedStates === void 0 ? void 0 : (_checkedStates_columns6 = checkedStates.columns) === null || _checkedStates_columns6 === void 0 ? void 0 : _checkedStates_columns6.product) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                            className: \"!w-80 sticky top-0 font-normal text-sm \".concat(rowHighlight ? \"!\".concat(rowHighlight) : \"\"),\n                                            style: {\n                                                left: \"\".concat(gridcolumnWidths.salesorder, \"px\")\n                                            },\n                                            title: tooltip,\n                                            children: data.PRODUCT_DESCRIPTION\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                            lineNumber: 700,\n                                            columnNumber: 21\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                            className: \"top-0 text-center font-normal text-sm \".concat(rowHighlight ? \"!\".concat(rowHighlight) : \"\"),\n                                            style: {\n                                                left: \"\".concat(gridcolumnWidths.product, \"px\")\n                                            },\n                                            title: tooltip,\n                                            children: data.CASE_SIZE\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                            lineNumber: 710,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"!text-center text-sm \".concat(rowHighlight ? \"!\".concat(rowHighlight) : \"\"),\n                                            title: tooltip,\n                                            children: data.CASES_ORIGINAL\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                            lineNumber: 719,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"!text-center text-sm \".concat(rowHighlight ? \"!\".concat(rowHighlight) : \"\"),\n                                            title: tooltip,\n                                            children: data.CASES_DELIVERED\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                            lineNumber: 727,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"!text-center text-sm font-bold \".concat(rowHighlight ? \"!\".concat(rowHighlight) : \"\"),\n                                            title: tooltip,\n                                            children: data.CASES_DIFFERENCE - data.CASES_ADDED_REASONS\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                            lineNumber: 735,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"!text-center text-sm \".concat(rowHighlight ? \"!\".concat(rowHighlight) : \"\"),\n                                            title: tooltip,\n                                            children: data.CASES_ADDED_REASONS\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                            lineNumber: 743,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"!text-center text-sm \".concat(rowHighlight ? \"!\".concat(rowHighlight) : \"\"),\n                                            title: tooltip,\n                                            children: \"\".concat((_data_SERVICE_LEVEL_PERCENT = data.SERVICE_LEVEL_PERCENT) === null || _data_SERVICE_LEVEL_PERCENT === void 0 ? void 0 : _data_SERVICE_LEVEL_PERCENT.toFixed(2), \"%\")\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                            lineNumber: 751,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"!text-center text-sm \".concat(rowHighlight ? \"!\".concat(rowHighlight) : \"\"),\n                                            title: tooltip,\n                                            children: [\n                                                \"\\xa3\",\n                                                (_data_UNIT_PRICE = data.UNIT_PRICE) === null || _data_UNIT_PRICE === void 0 ? void 0 : _data_UNIT_PRICE.toFixed(2)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                            lineNumber: 759,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"!text-center text-sm \".concat(rowHighlight ? \"!\".concat(rowHighlight) : \"\"),\n                                            title: tooltip,\n                                            children: \"\\xa3\".concat((_this = data.CASE_SIZE * data.UNIT_PRICE) === null || _this === void 0 ? void 0 : _this.toFixed(2))\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                            lineNumber: 767,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"!text-center text-sm \".concat(rowHighlight ? \"!\".concat(rowHighlight) : \"\"),\n                                            title: tooltip,\n                                            children: \"\\xa3\".concat((_this1 = data.CASE_SIZE * data.UNIT_PRICE * data.CASES_DIFFERENCE) === null || _this1 === void 0 ? void 0 : _this1.toFixed(2))\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                            lineNumber: 775,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"!text-center text-sm \".concat(rowHighlight ? \"!\".concat(rowHighlight) : \"\"),\n                                            title: tooltip,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"rounded-lg capitalize px-2 py-1 !text-center \".concat(data.ORD_STATUS === \"Cancelled\" ? \"bg-[#ff2929] text-white\" : data.ORD_STATUS === \"Open\" ? \"bg-[#54C5ED] text-white\" : data.ORD_STATUS === \"Invoiced\" ? \"bg-[#FFAE00] text-white\" : data.ORD_STATUS === \"Delivered\" ? \"bg-[#3EAB58] text-white\" : data.ORD_STATUS === \"Picked\" ? \"bg-[#FF6C09] text-white\" : \"bg-qtydiff-status !text-gray-700\" // Default style for any other status\n                                                ),\n                                                title: tooltip,\n                                                children: data.ORD_STATUS.toLowerCase()\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                                lineNumber: 793,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                            lineNumber: 787,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"relative \".concat(rowHighlight ? \"!\".concat(rowHighlight) : \"\"),\n                                            title: tooltip,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: (_data_reasons_ = data.reasons[0]) === null || _data_reasons_ === void 0 ? void 0 : _data_reasons_.MAIN_REASON\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                                    lineNumber: 819,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ReasonsDetails__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                    reasonsData: data.reasons,\n                                                    type: \"reasonsDetails\",\n                                                    seeAll: seeAll\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                                    lineNumber: 820,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                            lineNumber: 812,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"!text-left text-sm capitalize \".concat(rowHighlight ? \"!\".concat(rowHighlight) : \"\"),\n                                            title: tooltip,\n                                            children: (_data_reasons_1 = data.reasons[0]) === null || _data_reasons_1 === void 0 ? void 0 : _data_reasons_1.SUB_REASON\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                            lineNumber: 827,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"!text-left text-sm capitalize \".concat(rowHighlight ? \"!\".concat(rowHighlight) : \"\"),\n                                            title: tooltip,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-full text-truncate2L \",\n                                                children: (_data_reasons_2 = data.reasons[0]) === null || _data_reasons_2 === void 0 ? void 0 : _data_reasons_2.COMMENT\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                                lineNumber: 841,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                            lineNumber: 835,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"!text-center text-sm !bg-[#f3f8ff] \".concat(rowHighlight ? \"!\".concat(rowHighlight) : \"\"),\n                                            title: \"\".concat(!!data.LOCKED_BY ? \"You cannot edit the order while someone is already working on it.\" : \"\"),\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>{\n                                                    handleViewDetailsClick(data);\n                                                    setIsBulkUpdate(false);\n                                                },\n                                                className: \"cursor-pointer\",\n                                                disabled: !!data.LOCKED_BY || data.CASES_DIFFERENCE == 0 || selectedRows.length > 0,\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fluentui_react_components__WEBPACK_IMPORTED_MODULE_10__.Tooltip, {\n                                                    content: \"View Order Details\",\n                                                    relationship: \"label\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        xmlns: \"http://www.w3.org/2000/svg\",\n                                                        viewBox: \"0 0 512 512\",\n                                                        className: \" w-5 h-5 !text-skin-primary \",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            fill: \"currentcolor\",\n                                                            d: \"M480 448c0 17.7-14.3 32-32 32l-256 0c-17.7 0-32-14.3-32-32l0-80-32 0 0 80c0 35.3 28.7 64 64 64l256 0c35.3 0 64-28.7 64-64l0-284.1c0-12.7-5.1-24.9-14.1-33.9L382.1 14.1c-9-9-21.2-14.1-33.9-14.1L192 0c-35.3 0-64 28.7-64 64l0 192 32 0 0-192c0-17.7 14.3-32 32-32l128 0 0 112c0 26.5 21.5 48 48 48l112 0 0 256zm-.5-288L368 160c-8.8 0-16-7.2-16-16l0-111.5c2.8 .7 5.4 2.1 7.4 4.2L475.3 152.6c2.1 2.1 3.5 4.6 4.2 7.4zM283.3 212.7c-6.2-6.2-16.4-6.2-22.6 0s-6.2 16.4 0 22.6L329.4 304 16 304c-8.8 0-16 7.2-16 16s7.2 16 16 16l313.4 0-68.7 68.7c-6.2 6.2-6.2 16.4 0 22.6s16.4 6.2 22.6 0l96-96c6.2-6.2 6.2-16.4 0-22.6l-96-96z\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                                            lineNumber: 877,\n                                                            columnNumber: 27\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                                        lineNumber: 872,\n                                                        columnNumber: 25\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                                    lineNumber: 868,\n                                                    columnNumber: 23\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                                lineNumber: 856,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                            lineNumber: 846,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, index, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                    lineNumber: 592,\n                                    columnNumber: 17\n                                }, undefined);\n                            })\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                        lineNumber: 477,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                lineNumber: 331,\n                columnNumber: 9\n            }, undefined),\n            isOpen && selectedRows.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ViewDetails__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                data: selectedRows,\n                setData: setSelectedRows,\n                setAllSelectedProducts: setAllSelectedProducts,\n                setMapForReasonsParentsAndTheirCorrespondingChildren: setMapForReasonsParentsAndTheirCorrespondingChildren,\n                reasonsMasterList: reasonsMasterList,\n                parentReasonList: parentReasonList,\n                reasonsData: reasonsData,\n                fetchReasonData: fetchReasonData,\n                userData: userData,\n                isOpen: isOpen,\n                setIsOpen: setIsOpen,\n                bulkUpdateData: bulkUpdateData,\n                isBulkUpdate: isBulkUpdate,\n                setReasonsData: setReasonsData,\n                setBulkUpdateData: setBulkUpdateData,\n                setIsHeaderChecked: setIsHeaderChecked,\n                setSelectedRows: setSelectedRows\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                lineNumber: 892,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n        lineNumber: 327,\n        columnNumber: 5\n    }, undefined);\n};\n_s(SLTable, \"vX4vuRMTc2tBY6elYTRITygxPGY=\");\n_c = SLTable;\n/* harmony default export */ __webpack_exports__[\"default\"] = (SLTable);\nvar _c;\n$RefreshReg$(_c, \"SLTable\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevExports = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevExports) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports on update so we can compare the boundary\n                // signatures.\n                module.hot.dispose(function (data) {\n                    data.prevExports = currentExports;\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevExports !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevExports, currentExports)) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevExports !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/service_level/SLTable.js\n"));

/***/ })

});