"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/packaging-form/[productId]/edit",{

/***/ "./public/images/iss_logo.jpg":
/*!************************************!*\
  !*** ./public/images/iss_logo.jpg ***!
  \************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony default export */ __webpack_exports__[\"default\"] = ({\"src\":\"/_next/static/media/iss_logo.2dbf60f2.jpg\",\"height\":551,\"width\":1021,\"blurDataURL\":\"/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Fiss_logo.2dbf60f2.jpg&w=8&q=70\",\"blurWidth\":8,\"blurHeight\":4});//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9wdWJsaWMvaW1hZ2VzL2lzc19sb2dvLmpwZyIsIm1hcHBpbmdzIjoiO0FBQUEsK0RBQWUsQ0FBQyxxTUFBcU0iLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vcHVibGljL2ltYWdlcy9pc3NfbG9nby5qcGc/YmI5YSJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCB7XCJzcmNcIjpcIi9fbmV4dC9zdGF0aWMvbWVkaWEvaXNzX2xvZ28uMmRiZjYwZjIuanBnXCIsXCJoZWlnaHRcIjo1NTEsXCJ3aWR0aFwiOjEwMjEsXCJibHVyRGF0YVVSTFwiOlwiL19uZXh0L2ltYWdlP3VybD0lMkZfbmV4dCUyRnN0YXRpYyUyRm1lZGlhJTJGaXNzX2xvZ28uMmRiZjYwZjIuanBnJnc9OCZxPTcwXCIsXCJibHVyV2lkdGhcIjo4LFwiYmx1ckhlaWdodFwiOjR9OyJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///./public/images/iss_logo.jpg\n"));

/***/ }),

/***/ "./components/Layout.js":
/*!******************************!*\
  !*** ./components/Layout.js ***!
  \******************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getServerSideProps: function() { return /* binding */ getServerSideProps; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _Navbar__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./Navbar */ \"./components/Navbar.js\");\n/* harmony import */ var _Sidebar__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./Sidebar */ \"./components/Sidebar.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/navigation */ \"./node_modules/next/navigation.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(next_navigation__WEBPACK_IMPORTED_MODULE_5__);\n\nvar _s = $RefreshSig$();\n\n\n\n\n\nconst Layout = (param)=>{\n    let { children, userData, company, blockScreen } = param;\n    _s();\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_4__.useRouter)();\n    const currentPath = router.pathname;\n    const [showDesktopViewMessage, setShowDesktopViewMessage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_5__.usePathname)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const handleResize = ()=>{\n            if (window.innerWidth <= 767) {\n                setShowDesktopViewMessage(true);\n            } else {\n                setShowDesktopViewMessage(false);\n            }\n        };\n        window.addEventListener(\"resize\", handleResize);\n        handleResize();\n        return ()=>window.removeEventListener(\"resize\", handleResize);\n    }, []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        id: \"wrapper\",\n        children: showDesktopViewMessage ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"desktop-view-message\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"message-content\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        children: \"Please Open in Desktop View\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\Layout.js\",\n                        lineNumber: 32,\n                        columnNumber: 13\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        children: \"This website is best viewed on a desktop or laptop.\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\Layout.js\",\n                        lineNumber: 33,\n                        columnNumber: 13\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\Layout.js\",\n                lineNumber: 31,\n                columnNumber: 11\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\Layout.js\",\n            lineNumber: 30,\n            columnNumber: 9\n        }, undefined) : blockScreen ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"block-view-message\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"message-content\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            children: \"Service Unavailable\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\Layout.js\",\n                            lineNumber: 40,\n                            columnNumber: 15\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            children: \"We are currently experiencing issues and are working to resolve them as quickly as possible. Please check back later.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\Layout.js\",\n                            lineNumber: 41,\n                            columnNumber: 15\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\Layout.js\",\n                    lineNumber: 39,\n                    columnNumber: 13\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\Layout.js\",\n                lineNumber: 38,\n                columnNumber: 11\n            }, undefined)\n        }, void 0, false) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Sidebar__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                    userData: userData,\n                    companyName: company\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\Layout.js\",\n                    lineNumber: 50,\n                    columnNumber: 11\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"page-container\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Navbar__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                            userData: userData\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\Layout.js\",\n                            lineNumber: 52,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"main-root\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative md:ml-[55px] lg:ml-[60px] py-2 2xl:h-[calc(100%-60px)] \".concat(pathname == \"/whatif\" || pathname == \"/service_level\" ? \"w-[100%-70px] px-0 pl-3 mt-[45px]\" : \"w-full px-8 mt-[60px]\"),\n                                children: children\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\Layout.js\",\n                                lineNumber: 54,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\Layout.js\",\n                            lineNumber: 53,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\Layout.js\",\n                    lineNumber: 51,\n                    columnNumber: 11\n                }, undefined)\n            ]\n        }, void 0, true)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\Layout.js\",\n        lineNumber: 28,\n        columnNumber: 5\n    }, undefined);\n};\n_s(Layout, \"E7wJCME/M0kEq0xEwa8ayz4R7Ag=\", false, function() {\n    return [\n        next_router__WEBPACK_IMPORTED_MODULE_4__.useRouter,\n        next_navigation__WEBPACK_IMPORTED_MODULE_5__.usePathname\n    ];\n});\n_c = Layout;\n/* harmony default export */ __webpack_exports__[\"default\"] = (Layout);\nconst getServerSideProps = async (context)=>{\n    let userData = {};\n    let company = \"\";\n    try {\n        userData = JSON.parse(context.req.cookies.user || \"{}\");\n        company = JSON.parse(context.req.localStorage.company || \"\");\n    } catch (error) {\n        console.error(\"Error parsing userData:\", error);\n    }\n    return {\n        props: {\n            userData,\n            company\n        }\n    };\n};\nvar _c;\n$RefreshReg$(_c, \"Layout\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevExports = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevExports) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports on update so we can compare the boundary\n                // signatures.\n                module.hot.dispose(function (data) {\n                    data.prevExports = currentExports;\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevExports !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevExports, currentExports)) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevExports !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/Layout.js\n"));

/***/ }),

/***/ "./components/Navbar.js":
/*!******************************!*\
  !*** ./components/Navbar.js ***!
  \******************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   themes: function() { return /* binding */ themes; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _fortawesome_react_fontawesome__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @fortawesome/react-fontawesome */ \"./node_modules/@fortawesome/react-fontawesome/index.es.js\");\n/* harmony import */ var _fortawesome_free_solid_svg_icons__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @fortawesome/free-solid-svg-icons */ \"./node_modules/@fortawesome/free-solid-svg-icons/index.mjs\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/link */ \"./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _azure_msal_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @azure/msal-react */ \"./node_modules/@azure/msal-react/dist/index.js\");\n/* harmony import */ var _utils_theme_theme_switcher__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/utils/theme/theme-switcher */ \"./utils/theme/theme-switcher.js\");\n/* harmony import */ var js_cookie__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! js-cookie */ \"./node_modules/js-cookie/dist/js.cookie.mjs\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\nconst themes = [\n    {\n        bg: \"#022d71\",\n        text: \"#022d71\",\n        name: \"DPS\"\n    },\n    {\n        bg: \"#2e9b28\",\n        text: \"#2e9b28\",\n        name: \"EFC\"\n    },\n    {\n        bg: \"#a91e23\",\n        text: \"#a91e23\",\n        name: \"M&S\"\n    },\n    {\n        bg: \"#3d6546\",\n        text: \"#3d6546\",\n        name: \"FPP\"\n    }\n];\nconst Navbar = (param)=>{\n    let { userData } = param;\n    _s();\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_4__.useRouter)();\n    const [pageName, setPageName] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [currentRoute, setCurrentRoute] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const currentPath = router.pathname;\n        setCurrentRoute(currentPath);\n        if (currentPath === \"/finished-product-request/add\") {\n            js_cookie__WEBPACK_IMPORTED_MODULE_7__[\"default\"].set(\"PreviousPage\", \"FG\");\n            setPageName(\"Finished Goods Request\");\n        } else if (currentPath === \"/raw-material-request/add\") {\n            setPageName(\"Raw Material Request\");\n            js_cookie__WEBPACK_IMPORTED_MODULE_7__[\"default\"].set(\"PreviousPage\", \"RM\");\n        } else if (currentPath === \"/packaging-form/add\") {\n            setPageName(\"Packaging Request\");\n        } else if (currentPath === \"/variety/add\") {\n            js_cookie__WEBPACK_IMPORTED_MODULE_7__[\"default\"].set(\"PreviousPage\", \"NV\");\n            setPageName(\"New Variety Request\");\n        } else if (currentPath === \"/variety/[productId]/edit\") {\n            js_cookie__WEBPACK_IMPORTED_MODULE_7__[\"default\"].set(\"PreviousPage\", \"NV\");\n            setPageName(\"Edit New Variety Request\");\n        } else if (currentPath === \"/packaging-form/[productId]/edit\") {\n            js_cookie__WEBPACK_IMPORTED_MODULE_7__[\"default\"].set(\"PreviousPage\", \"PK\");\n            setPageName(\"Edit Packaging Request\");\n        } else if (currentPath === \"/raw-material-request/[productId]/edit\") {\n            js_cookie__WEBPACK_IMPORTED_MODULE_7__[\"default\"].set(\"PreviousPage\", \"RM\");\n            setPageName(\"Edit Raw Material Request\");\n        } else if (currentPath === \"/finished-product-request/[productId]/edit\") {\n            js_cookie__WEBPACK_IMPORTED_MODULE_7__[\"default\"].set(\"PreviousPage\", \"FG\");\n            setPageName(\"Edit Finished Goods Request\");\n        } else if (currentPath === null || currentPath === void 0 ? void 0 : currentPath.endsWith(\"/edit\")) {\n            setPageName(\"Edit Supplier\");\n        } else if (currentPath === null || currentPath === void 0 ? void 0 : currentPath.endsWith(\"/add\")) {\n            setPageName(\"Add Supplier\");\n        } else if (currentPath === null || currentPath === void 0 ? void 0 : currentPath.endsWith(\"/edit/forms\")) {\n            setPageName(\"Supplier Form\");\n        } else if (currentPath === null || currentPath === void 0 ? void 0 : currentPath.endsWith(\"/confirm\")) {\n            setPageName(\"Confirm Details for Supplier\");\n        } else if (currentPath === \"/suppliers\") {\n            setPageName(\"Suppliers\");\n        } else if (currentPath === \"/users\") {\n            setPageName(\"User Management\");\n        } else if (currentPath === \"/viewlogs\") {\n            setPageName(\"View Logs\");\n        } else if (currentPath === \"/products\") {\n            js_cookie__WEBPACK_IMPORTED_MODULE_7__[\"default\"].set(\"PreviousPage\", \"PK\");\n            setPageName(\"Products\");\n        } else if (currentPath === \"/variety\") {\n            setPageName(\"Variety\");\n            js_cookie__WEBPACK_IMPORTED_MODULE_7__[\"default\"].set(\"PreviousPage\", \"NV\");\n        } else if (currentPath === \"/finishedProductRequest\") {\n            js_cookie__WEBPACK_IMPORTED_MODULE_7__[\"default\"].set(\"PreviousPage\", \"FG\");\n            setPageName(\"Finished Product Request\");\n        } else if (currentPath === \"/rawMaterialRequest\") {\n            js_cookie__WEBPACK_IMPORTED_MODULE_7__[\"default\"].set(\"PreviousPage\", \"RM\");\n            setPageName(\"Raw Material Request\");\n        } else if (currentPath === \"/whatif\") {\n            setPageName(\"Whatif\");\n        } else if (currentPath === \"/service_level\" || currentPath === \"/service_level/reports/masterForcast\") {\n            setPageName(\"Service Level\");\n        }\n    }, [\n        router.pathname\n    ]);\n    const baseCompanyOptions = [\n        {\n            value: \"dpsltd\",\n            label: \"DPS\"\n        },\n        {\n            value: \"dpsltdms\",\n            label: \"DPS M&S\"\n        },\n        {\n            value: \"efcltd\",\n            label: \"EFC\"\n        },\n        {\n            value: \"fpp-ltd\",\n            label: \"FPP\"\n        }\n    ];\n    const companyOptions = [\n        ...baseCompanyOptions,\n        ...(userData === null || userData === void 0 ? void 0 : userData.role_id) === 6 ? [\n            {\n                value: \"issproduce\",\n                label: \"ISS\"\n            },\n            {\n                value: \"flrs\",\n                label: \"FLRS\",\n                disabled: true\n            },\n            {\n                value: \"thl\",\n                label: \"THL\",\n                disabled: true\n            }\n        ] : []\n    ];\n    const [selectedCompany, setSelectedCompany] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(js_cookie__WEBPACK_IMPORTED_MODULE_7__[\"default\"].get(\"ADCompanyName\") == \"DPS MS\" ? \"dpsltdms\" : js_cookie__WEBPACK_IMPORTED_MODULE_7__[\"default\"].get(\"company\") || \"\");\n    const handleCompanyChange = (event)=>{\n        console.log(\"e.target.value\", event.target.value);\n        const company = event.target.value;\n        if (company == \"dpsltd\") {\n            js_cookie__WEBPACK_IMPORTED_MODULE_7__[\"default\"].set(\"theme\", \"#022D71\");\n            js_cookie__WEBPACK_IMPORTED_MODULE_7__[\"default\"].set(\"ADCompanyName\", \"DPS\");\n            setSelectedCompany(company);\n            js_cookie__WEBPACK_IMPORTED_MODULE_7__[\"default\"].set(\"company\", company);\n        } else if (company == \"dpsltdms\") {\n            js_cookie__WEBPACK_IMPORTED_MODULE_7__[\"default\"].set(\"theme\", \"#0d6bfc\");\n            js_cookie__WEBPACK_IMPORTED_MODULE_7__[\"default\"].set(\"ADCompanyName\", \"DPS MS\");\n            setSelectedCompany(\"dpsltd\");\n            js_cookie__WEBPACK_IMPORTED_MODULE_7__[\"default\"].set(\"company\", \"dpsltd\");\n        } else if (company == \"efcltd\") {\n            js_cookie__WEBPACK_IMPORTED_MODULE_7__[\"default\"].set(\"theme\", \"#3eab58\");\n            js_cookie__WEBPACK_IMPORTED_MODULE_7__[\"default\"].set(\"ADCompanyName\", \"EFC\");\n            setSelectedCompany(company);\n            js_cookie__WEBPACK_IMPORTED_MODULE_7__[\"default\"].set(\"company\", company);\n        } else if (company == \"fpp-ltd\") {\n            js_cookie__WEBPACK_IMPORTED_MODULE_7__[\"default\"].set(\"theme\", \"#3d6546\");\n            js_cookie__WEBPACK_IMPORTED_MODULE_7__[\"default\"].set(\"ADCompanyName\", \"Fresh Produce Partners\");\n            setSelectedCompany(company);\n            js_cookie__WEBPACK_IMPORTED_MODULE_7__[\"default\"].set(\"company\", company);\n        } else if (company == \"issproduce\") {\n            js_cookie__WEBPACK_IMPORTED_MODULE_7__[\"default\"].set(\"theme\", \"#ABC400\");\n            js_cookie__WEBPACK_IMPORTED_MODULE_7__[\"default\"].set(\"ADCompanyName\", \"Integrated Service Solutions Ltd\");\n            setSelectedCompany(company);\n            js_cookie__WEBPACK_IMPORTED_MODULE_7__[\"default\"].set(\"company\", company);\n        }\n        router.reload();\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"titlebar\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex flex-row justify-between w-full bg-skin-primary h-14\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-row w-full items-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"page-heading cursor-pointer\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fortawesome_react_fontawesome__WEBPACK_IMPORTED_MODULE_2__.FontAwesomeIcon, {\n                                        icon: _fortawesome_free_solid_svg_icons__WEBPACK_IMPORTED_MODULE_8__.faChevronLeft,\n                                        className: \"pageName text-white\",\n                                        onClick: ()=>router.back()\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\Navbar.js\",\n                                        lineNumber: 146,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                        href: currentRoute,\n                                        className: \"ml-4 2xl:text-lg font-poppinssemibold pageName text-white tracking-wide\",\n                                        children: pageName\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\Navbar.js\",\n                                        lineNumber: 151,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\Navbar.js\",\n                                lineNumber: 145,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_utils_theme_theme_switcher__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                className: \"ml-5\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\Navbar.js\",\n                                lineNumber: 158,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\Navbar.js\",\n                        lineNumber: 144,\n                        columnNumber: 11\n                    }, undefined),\n                    ((userData === null || userData === void 0 ? void 0 : userData.role_id) == 5 || (userData === null || userData === void 0 ? void 0 : userData.role_id) == 6) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-row justify-end w-1/2 items-center mr-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                            value: selectedCompany,\n                            onChange: handleCompanyChange,\n                            className: \"bg-white text-black rounded\",\n                            children: companyOptions.map((opt)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                    value: opt.value,\n                                    disabled: opt.disabled,\n                                    children: opt.label\n                                }, opt.value, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\Navbar.js\",\n                                    lineNumber: 168,\n                                    columnNumber: 19\n                                }, undefined))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\Navbar.js\",\n                            lineNumber: 162,\n                            columnNumber: 15\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\Navbar.js\",\n                        lineNumber: 161,\n                        columnNumber: 13\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\Navbar.js\",\n                lineNumber: 143,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\Navbar.js\",\n            lineNumber: 142,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\Navbar.js\",\n        lineNumber: 141,\n        columnNumber: 5\n    }, undefined);\n};\n_s(Navbar, \"8bgZbm+HleyJfidZ9YLsifOPi/U=\", false, function() {\n    return [\n        next_router__WEBPACK_IMPORTED_MODULE_4__.useRouter\n    ];\n});\n_c = Navbar;\n/* harmony default export */ __webpack_exports__[\"default\"] = (Navbar);\nvar _c;\n$RefreshReg$(_c, \"Navbar\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevExports = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevExports) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports on update so we can compare the boundary\n                // signatures.\n                module.hot.dispose(function (data) {\n                    data.prevExports = currentExports;\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevExports !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevExports, currentExports)) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevExports !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/Navbar.js\n"));

/***/ }),

/***/ "./components/SideBarLinks.js":
/*!************************************!*\
  !*** ./components/SideBarLinks.js ***!
  \************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ SideBarLinks; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _fortawesome_free_solid_svg_icons__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @fortawesome/free-solid-svg-icons */ \"./node_modules/@fortawesome/free-solid-svg-icons/index.mjs\");\n/* harmony import */ var _fortawesome_react_fontawesome__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @fortawesome/react-fontawesome */ \"./node_modules/@fortawesome/react-fontawesome/index.es.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _utils_loaders_loadingContext__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/utils/loaders/loadingContext */ \"./utils/loaders/loadingContext.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\nfunction SideBarLinks(param) {\n    let { isSuppliersActive, userData, company, isUsersActive, isLogsActive, isWhatifActive, isServiceLevelActive, isProductsActive, ADCompany, currentPathname } = param;\n    var _process_env_NEXT_PUBLIC_AVAILABLE_MODULES;\n    _s();\n    const availableModules = (_process_env_NEXT_PUBLIC_AVAILABLE_MODULES = \"supplier,products,whatif,serviceLevel,users,logs\") === null || _process_env_NEXT_PUBLIC_AVAILABLE_MODULES === void 0 ? void 0 : _process_env_NEXT_PUBLIC_AVAILABLE_MODULES.split(\",\");\n    const { setIsLoading } = (0,_utils_loaders_loadingContext__WEBPACK_IMPORTED_MODULE_4__.useLoading)();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n        className: \"nav navbar-nav\",\n        children: availableModules && availableModules.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n            children: [\n                availableModules.includes(\"supplier\") && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                    className: \"flex justify-center mb-3\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                        href: \"/suppliers\",\n                        title: \"Supplier\",\n                        className: \"!px-4 !py-3 bg-white rounded-md text-center \".concat(isSuppliersActive ? \"opacity-100\" : \"opacity-70\"),\n                        onClick: (e)=>{\n                            if (isSuppliersActive && (currentPathname === null || currentPathname === void 0 ? void 0 : currentPathname.startsWith(\"/suppliers\"))) {\n                                e.preventDefault();\n                                window.location.reload();\n                            } else {\n                                setIsLoading(true);\n                            }\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fortawesome_react_fontawesome__WEBPACK_IMPORTED_MODULE_1__.FontAwesomeIcon, {\n                            icon: _fortawesome_free_solid_svg_icons__WEBPACK_IMPORTED_MODULE_5__.faTruck,\n                            size: \"lg\",\n                            className: \"text-skin-primary\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\SideBarLinks.js\",\n                            lineNumber: 63,\n                            columnNumber: 17\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\SideBarLinks.js\",\n                        lineNumber: 45,\n                        columnNumber: 15\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\SideBarLinks.js\",\n                    lineNumber: 44,\n                    columnNumber: 13\n                }, this),\n                availableModules.includes(\"products\") && !(userData === null || userData === void 0 ? void 0 : userData.department_id) == 5 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                    className: \"flex justify-center mb-3\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                        href: \"/products\",\n                        title: \"Products\",\n                        className: \"!px-4 !py-3 bg-white rounded-md text-center \".concat(isProductsActive ? \"opacity-100\" : \"opacity-70\"),\n                        onClick: (e)=>{\n                            if (isProductsActive) {\n                                e.preventDefault();\n                                window.location.reload();\n                            } else {\n                                setIsLoading(true);\n                            }\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fortawesome_react_fontawesome__WEBPACK_IMPORTED_MODULE_1__.FontAwesomeIcon, {\n                            icon: _fortawesome_free_solid_svg_icons__WEBPACK_IMPORTED_MODULE_5__.faBoxArchive,\n                            size: \"lg\",\n                            className: \"text-skin-primary\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\SideBarLinks.js\",\n                            lineNumber: 88,\n                            columnNumber: 17\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\SideBarLinks.js\",\n                        lineNumber: 73,\n                        columnNumber: 15\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\SideBarLinks.js\",\n                    lineNumber: 72,\n                    columnNumber: 13\n                }, this),\n                (company == \"efcltd\" || company == \"flrs\" || company == \"thl\") && ((userData === null || userData === void 0 ? void 0 : userData.department_id) == 1 || (userData === null || userData === void 0 ? void 0 : userData.department_id) == 2) && availableModules.includes(\"whatif\") && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                    className: \"flex justify-center mb-3\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                        href: \"/whatif\",\n                        title: \"What if\",\n                        className: \"!px-4 !py-3 bg-white rounded-md text-center \".concat(isWhatifActive ? \"opacity-100\" : \"opacity-70\"),\n                        onClick: (e)=>{\n                            if (isWhatifActive) {\n                                e.preventDefault();\n                                window.location.reload();\n                            } else {\n                                setIsLoading(true);\n                            }\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fortawesome_react_fontawesome__WEBPACK_IMPORTED_MODULE_1__.FontAwesomeIcon, {\n                            icon: _fortawesome_free_solid_svg_icons__WEBPACK_IMPORTED_MODULE_5__.faChartLine,\n                            size: \"lg\",\n                            className: \"text-skin-primary\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\SideBarLinks.js\",\n                            lineNumber: 115,\n                            columnNumber: 19\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\SideBarLinks.js\",\n                        lineNumber: 100,\n                        columnNumber: 17\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\SideBarLinks.js\",\n                    lineNumber: 99,\n                    columnNumber: 15\n                }, this),\n                ((userData === null || userData === void 0 ? void 0 : userData.department_id) == 1 || (userData === null || userData === void 0 ? void 0 : userData.department_id) == 2) && availableModules.includes(\"serviceLevel\") && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                    className: \"flex justify-center mb-3\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                        href: \"/service_level\",\n                        title: \"Service Level\",\n                        className: \"!px-4 !py-3 bg-white rounded-md text-center \".concat(isServiceLevelActive ? \"opacity-100\" : \"opacity-70\"),\n                        onClick: (e)=>{\n                            if (isServiceLevelActive) {\n                                e.preventDefault();\n                                window.location.reload();\n                            } else {\n                                setIsLoading(true);\n                            }\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fortawesome_react_fontawesome__WEBPACK_IMPORTED_MODULE_1__.FontAwesomeIcon, {\n                            icon: _fortawesome_free_solid_svg_icons__WEBPACK_IMPORTED_MODULE_5__.faFileCircleQuestion,\n                            size: \"lg\",\n                            className: \"text-skin-primary\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\SideBarLinks.js\",\n                            lineNumber: 142,\n                            columnNumber: 19\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\SideBarLinks.js\",\n                        lineNumber: 127,\n                        columnNumber: 17\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\SideBarLinks.js\",\n                    lineNumber: 126,\n                    columnNumber: 15\n                }, this),\n                ((userData === null || userData === void 0 ? void 0 : userData.role_id) === 1 || (userData === null || userData === void 0 ? void 0 : userData.role_id) === 5 || (userData === null || userData === void 0 ? void 0 : userData.role_id) === 6) && availableModules.includes(\"users\") && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                    className: \"flex justify-center mb-3\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                        href: \"/users\",\n                        title: \"Users\",\n                        className: \"!px-4 !py-3 bg-white rounded-md text-center \".concat(isUsersActive ? \"opacity-100\" : \"opacity-70\"),\n                        onClick: (e)=>{\n                            if (isUsersActive) {\n                                e.preventDefault();\n                                window.location.reload();\n                            } else {\n                                setIsLoading(true);\n                            }\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fortawesome_react_fontawesome__WEBPACK_IMPORTED_MODULE_1__.FontAwesomeIcon, {\n                            icon: _fortawesome_free_solid_svg_icons__WEBPACK_IMPORTED_MODULE_5__.faUsersLine,\n                            size: \"lg\",\n                            className: \"text-skin-primary\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\SideBarLinks.js\",\n                            lineNumber: 172,\n                            columnNumber: 19\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\SideBarLinks.js\",\n                        lineNumber: 156,\n                        columnNumber: 17\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\SideBarLinks.js\",\n                    lineNumber: 155,\n                    columnNumber: 15\n                }, this),\n                ((userData === null || userData === void 0 ? void 0 : userData.role_id) === 1 || (userData === null || userData === void 0 ? void 0 : userData.role_id) === 5 || (userData === null || userData === void 0 ? void 0 : userData.role_id) === 6) && availableModules.includes(\"logs\") && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                    className: \"flex justify-center mb-3\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                        href: \"/viewlogs\",\n                        title: \"Logs\",\n                        className: \"!px-4 !py-3 bg-white rounded-md text-center \".concat(isLogsActive ? \"opacity-100\" : \"opacity-70\"),\n                        onClick: (e)=>{\n                            if (isLogsActive) {\n                                e.preventDefault();\n                                window.location.reload();\n                            } else {\n                                setIsLoading(true);\n                            }\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fortawesome_react_fontawesome__WEBPACK_IMPORTED_MODULE_1__.FontAwesomeIcon, {\n                            icon: _fortawesome_free_solid_svg_icons__WEBPACK_IMPORTED_MODULE_5__.faList,\n                            size: \"lg\",\n                            className: \"text-skin-primary\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\SideBarLinks.js\",\n                            lineNumber: 201,\n                            columnNumber: 19\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\SideBarLinks.js\",\n                        lineNumber: 185,\n                        columnNumber: 17\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\SideBarLinks.js\",\n                    lineNumber: 184,\n                    columnNumber: 15\n                }, this)\n            ]\n        }, void 0, true)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\SideBarLinks.js\",\n        lineNumber: 40,\n        columnNumber: 5\n    }, this);\n}\n_s(SideBarLinks, \"HE5iXj3OUhzAOBpDJUVF6nKZb8k=\", false, function() {\n    return [\n        _utils_loaders_loadingContext__WEBPACK_IMPORTED_MODULE_4__.useLoading\n    ];\n});\n_c = SideBarLinks;\nvar _c;\n$RefreshReg$(_c, \"SideBarLinks\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevExports = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevExports) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports on update so we can compare the boundary\n                // signatures.\n                module.hot.dispose(function (data) {\n                    data.prevExports = currentExports;\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevExports !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevExports, currentExports)) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevExports !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/SideBarLinks.js\n"));

/***/ }),

/***/ "./components/Sidebar.js":
/*!*******************************!*\
  !*** ./components/Sidebar.js ***!
  \*******************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ Sidebar; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _public_images_dps_logo_png__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../public/images/dps-logo.png */ \"./public/images/dps-logo.png\");\n/* harmony import */ var _public_images_efc_logo_jpg__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../public/images/efc_logo.jpg */ \"./public/images/efc_logo.jpg\");\n/* harmony import */ var _public_images_dps_ms_logo_png__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../public/images/dps_ms_logo.png */ \"./public/images/dps_ms_logo.png\");\n/* harmony import */ var _public_images_fpp_logo_png__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../public/images/fpp_logo.png */ \"./public/images/fpp_logo.png\");\n/* harmony import */ var _public_images_iss_logo_jpg__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../public/images/iss_logo.jpg */ \"./public/images/iss_logo.jpg\");\n/* harmony import */ var _public_images_nav_icon_png__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ../public/images/nav-icon.png */ \"./public/images/nav-icon.png\");\n/* harmony import */ var _public_images_user_png__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ../public/images/user.png */ \"./public/images/user.png\");\n/* harmony import */ var _public_images_loading_img_png__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ../public/images/loading_img.png */ \"./public/images/loading_img.png\");\n/* harmony import */ var _public_images_logout_icon_png__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ../public/images/logout-icon.png */ \"./public/images/logout-icon.png\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! next/link */ \"./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_12___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_12__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! next/image */ \"./node_modules/next/image.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_13___default = /*#__PURE__*/__webpack_require__.n(next_image__WEBPACK_IMPORTED_MODULE_13__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! next/navigation */ \"./node_modules/next/navigation.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_14___default = /*#__PURE__*/__webpack_require__.n(next_navigation__WEBPACK_IMPORTED_MODULE_14__);\n/* harmony import */ var _utils_auth_auth__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! ../utils/auth/auth */ \"./utils/auth/auth.js\");\n/* harmony import */ var _azure_msal_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @azure/msal-react */ \"./node_modules/@azure/msal-react/dist/index.js\");\n/* harmony import */ var js_cookie__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! js-cookie */ \"./node_modules/js-cookie/dist/js.cookie.mjs\");\n/* harmony import */ var react_loader_spinner__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! react-loader-spinner */ \"./node_modules/react-loader-spinner/dist/module.js\");\n/* harmony import */ var _SideBarLinks__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! ./SideBarLinks */ \"./components/SideBarLinks.js\");\n/* provided dependency */ var process = __webpack_require__(/*! process */ \"./node_modules/next/dist/build/polyfills/process.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction Sidebar() {\n    _s();\n    const [userData, setUserData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [company, setCompany] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [ADCompany, setADCompany] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const userCookie = js_cookie__WEBPACK_IMPORTED_MODULE_17__[\"default\"].get(\"user\");\n        const companyCookie = js_cookie__WEBPACK_IMPORTED_MODULE_17__[\"default\"].get(\"company\");\n        const ADcompanyCookie = js_cookie__WEBPACK_IMPORTED_MODULE_17__[\"default\"].get(\"ADCompanyName\");\n        if (userCookie) {\n            setUserData(JSON.parse(userCookie));\n        }\n        if (companyCookie) {\n            setCompany(companyCookie);\n        }\n        if (ADcompanyCookie) {\n            setADCompany(ADcompanyCookie);\n        }\n    }, []);\n    const { instance } = (0,_azure_msal_react__WEBPACK_IMPORTED_MODULE_16__.useMsal)();\n    // const userRoleData = getCookieData(\"user\");\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const currentPathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_14__.usePathname)();\n    const [isSuppliersActive, setIsSuppliersActive] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)((currentPathname === null || currentPathname === void 0 ? void 0 : currentPathname.startsWith(\"/suppliers\")) || (currentPathname === null || currentPathname === void 0 ? void 0 : currentPathname.startsWith(\"/supplier\")));\n    const [isUsersActive, setIsUsersActive] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(currentPathname === \"/users\");\n    const [isLogsActive, setIsLogsActive] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(currentPathname === \"/viewlogs\");\n    const [isProductsActive, setIsProductsActive] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(currentPathname === \"/products\");\n    const [isWhatifActive, setIsWhatifActive] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(currentPathname === \"/whatif\");\n    const [isServiceLevelActive, setIsServiceLevelActive] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(currentPathname === \"/service_level\");\n    const [isRawMaterialRequestActive, setIsRawMaterialRequestActive] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(currentPathname === \"/raw-material-request/add\");\n    const getLogo = (company)=>{\n        if (!company) return;\n        switch(company){\n            case \"dpsltd\":\n                return _public_images_dps_logo_png__WEBPACK_IMPORTED_MODULE_3__[\"default\"];\n            case \"DPS MS\":\n                return _public_images_dps_ms_logo_png__WEBPACK_IMPORTED_MODULE_5__[\"default\"];\n            case \"efcltd\":\n                return _public_images_efc_logo_jpg__WEBPACK_IMPORTED_MODULE_4__[\"default\"];\n            case \"fpp-ltd\":\n                return _public_images_fpp_logo_png__WEBPACK_IMPORTED_MODULE_6__[\"default\"];\n            case \"thl\":\n                return _public_images_efc_logo_jpg__WEBPACK_IMPORTED_MODULE_4__[\"default\"];\n            case \"issproduce\":\n                return _public_images_iss_logo_jpg__WEBPACK_IMPORTED_MODULE_7__[\"default\"];\n            case \"flrs\":\n                return _public_images_efc_logo_jpg__WEBPACK_IMPORTED_MODULE_4__[\"default\"];\n            default:\n                return;\n        }\n    };\n    const getLogoSizeClass = (company)=>{\n        if (!company) return \"h-14 w-100\";\n        switch(company){\n            case \"dpsltd\":\n                return \"!h-16 !w-auto\";\n            default:\n                return \"h-14 w-100\";\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n            id: \"sidemenu\",\n            className: \"navbar navbar-default sidebar bg-skin-primary\",\n            role: \"navigation\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"container-fluid h-full\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_12___default()), {\n                        href: \"/suppliers\",\n                        title: \"Home\",\n                        className: \"z-50\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"brand\",\n                            children: company ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_13___default()), {\n                                src: getLogo(company == \"dpsltd\" && ADCompany == \"DPS MS\" ? \"DPS MS\" : company),\n                                alt: \"logo\",\n                                className: getLogoSizeClass(company == \"dpsltd\" && ADCompany == \"DPS MS\" ? \"DPS MS\" : company)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\Sidebar.js\",\n                                lineNumber: 117,\n                                columnNumber: 17\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_loader_spinner__WEBPACK_IMPORTED_MODULE_18__.Oval, {\n                                    color: \"#002D73\",\n                                    height: 20,\n                                    width: 20,\n                                    visible: true,\n                                    ariaLabel: \"oval-loading\",\n                                    secondaryColor: \"#0066FF\",\n                                    strokeWidth: 2,\n                                    strokeWidthSecondary: 2\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\Sidebar.js\",\n                                    lineNumber: 132,\n                                    columnNumber: 19\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\Sidebar.js\",\n                                lineNumber: 131,\n                                columnNumber: 17\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\Sidebar.js\",\n                            lineNumber: 115,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\Sidebar.js\",\n                        lineNumber: 114,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        id: \"bs-sidebar-navbar-collapse-1\",\n                        className: \"pt-10 w-100 text-center flex flex-col justify-between flex-end items-stretch h-full\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col w-full\",\n                                children: userData && company && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_SideBarLinks__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                    isSuppliersActive: isSuppliersActive,\n                                    userData: userData,\n                                    currentPathname: currentPathname,\n                                    company: company,\n                                    isUsersActive: isUsersActive,\n                                    isLogsActive: isLogsActive,\n                                    isProductsActive: isProductsActive,\n                                    isWhatifActive: isWhatifActive,\n                                    isServiceLevelActive: isServiceLevelActive,\n                                    ADCompany: ADCompany\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\Sidebar.js\",\n                                    lineNumber: 152,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\Sidebar.js\",\n                                lineNumber: 150,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-center flex-col items-center gap-4 my-4 mb-20 cursor-pointer\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        title: \"IT Training Material\",\n                                        href: \"\".concat(process.env.NEXT_PUBLIC_TRAINING_MATERIAL),\n                                        target: \"_blank\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            xmlns: \"http://www.w3.org/2000/svg\",\n                                            viewBox: \"0 0 448 512\",\n                                            fill: \"#FFFF\",\n                                            className: \"w-6\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                d: \"M0 88C0 39.4 39.4 0 88 0L392 0c30.9 0 56 25.1 56 56l0 288c0 22.3-13.1 41.6-32 50.6l0 69.4 8 0c13.3 0 24 10.7 24 24s-10.7 24-24 24L80 512c-44.2 0-80-35.8-80-80c0-2.7 .1-5.4 .4-8L0 424 0 88zM80 400c-17.7 0-32 14.3-32 32s14.3 32 32 32l288 0 0-64L80 400zM48 358.7c9.8-4.3 20.6-6.7 32-6.7l312 0c4.4 0 8-3.6 8-8l0-288c0-4.4-3.6-8-8-8L88 48C65.9 48 48 65.9 48 88l0 270.7zM160 112l8.8-17.7c2.9-5.9 11.4-5.9 14.3 0L192 112l17.7 8.8c5.9 2.9 5.9 11.4 0 14.3L192 144l-8.8 17.7c-2.9 5.9-11.4 5.9-14.3 0L160 144l-17.7-8.8c-5.9-2.9-5.9-11.4 0-14.3L160 112zM264 216l16.6-38.8c2.8-6.5 11.9-6.5 14.7 0L312 216l38.8 16.6c6.5 2.8 6.5 11.9 0 14.7L312 264l-16.6 38.8c-2.8 6.5-11.9 6.5-14.7 0L264 264l-38.8-16.6c-6.5-2.8-6.5-11.9 0-14.7L264 216z\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\Sidebar.js\",\n                                                lineNumber: 178,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\Sidebar.js\",\n                                            lineNumber: 172,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\Sidebar.js\",\n                                        lineNumber: 167,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        title: \"Logout\",\n                                        onClick: ()=>{\n                                            localStorage.removeItem(\"superUser\");\n                                            js_cookie__WEBPACK_IMPORTED_MODULE_17__[\"default\"].remove(\"company\");\n                                            js_cookie__WEBPACK_IMPORTED_MODULE_17__[\"default\"].remove(\"ADCompanyName\");\n                                            localStorage.removeItem(\"id\");\n                                            localStorage.removeItem(\"name\");\n                                            localStorage.removeItem(\"role\");\n                                            localStorage.removeItem(\"email\");\n                                            js_cookie__WEBPACK_IMPORTED_MODULE_17__[\"default\"].remove(\"user\");\n                                            js_cookie__WEBPACK_IMPORTED_MODULE_17__[\"default\"].remove(\"theme\");\n                                            js_cookie__WEBPACK_IMPORTED_MODULE_17__[\"default\"].remove(\"token\");\n                                            const redirectUrl = \"/login?redirect=\".concat(encodeURIComponent(window.location.pathname));\n                                            (0,_utils_auth_auth__WEBPACK_IMPORTED_MODULE_15__.logoutHandler)(instance, redirectUrl);\n                                        },\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_13___default()), {\n                                            src: _public_images_logout_icon_png__WEBPACK_IMPORTED_MODULE_11__[\"default\"],\n                                            alt: \"logout\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\Sidebar.js\",\n                                            lineNumber: 200,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\Sidebar.js\",\n                                        lineNumber: 181,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\Sidebar.js\",\n                                lineNumber: 166,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\Sidebar.js\",\n                        lineNumber: 146,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\Sidebar.js\",\n                lineNumber: 113,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\Sidebar.js\",\n            lineNumber: 108,\n            columnNumber: 7\n        }, this)\n    }, void 0, false);\n}\n_s(Sidebar, \"CqfPHC0NzGwmyxMjqjmKLbgxaBY=\", false, function() {\n    return [\n        _azure_msal_react__WEBPACK_IMPORTED_MODULE_16__.useMsal,\n        next_router__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        next_navigation__WEBPACK_IMPORTED_MODULE_14__.usePathname\n    ];\n});\n_c = Sidebar;\nvar _c;\n$RefreshReg$(_c, \"Sidebar\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevExports = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevExports) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports on update so we can compare the boundary\n                // signatures.\n                module.hot.dispose(function (data) {\n                    data.prevExports = currentExports;\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevExports !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevExports, currentExports)) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevExports !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/Sidebar.js\n"));

/***/ }),

/***/ "./utils/exportExcel.js":
/*!******************************!*\
  !*** ./utils/exportExcel.js ***!
  \******************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var exceljs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! exceljs */ \"./node_modules/exceljs/dist/exceljs.min.js\");\n/* harmony import */ var exceljs__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(exceljs__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react_toastify__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react-toastify */ \"./node_modules/react-toastify/dist/react-toastify.esm.mjs\");\n/* harmony import */ var react_toastify_dist_ReactToastify_css__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-toastify/dist/ReactToastify.css */ \"./node_modules/react-toastify/dist/ReactToastify.css\");\n/* harmony import */ var react_toastify_dist_ReactToastify_css__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react_toastify_dist_ReactToastify_css__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _services_apiConfig__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/services/apiConfig */ \"./services/apiConfig.js\");\n/* harmony import */ var _auth_auth__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./auth/auth */ \"./utils/auth/auth.js\");\n\n\n\n\n\n// todo:move the supplierCode column from the excel file to first column\nconst exportExcel = async function(data, isInternal, token, company, userData, prophet_id, requestor_email, isMultiple) {\n    let isProductExtract = arguments.length > 8 && arguments[8] !== void 0 ? arguments[8] : false, onProductSubmit = arguments.length > 9 && arguments[9] !== void 0 ? arguments[9] : false, productEmailParagraph = arguments.length > 10 && arguments[10] !== void 0 ? arguments[10] : \"\", productEmailCommentPlaceholder = arguments.length > 11 && arguments[11] !== void 0 ? arguments[11] : \"\", request_no = arguments.length > 12 && arguments[12] !== void 0 ? arguments[12] : \"\", varietyRequest = arguments.length > 13 && arguments[13] !== void 0 ? arguments[13] : false;\n    let serverAddress = _services_apiConfig__WEBPACK_IMPORTED_MODULE_3__.apiConfig.serverAddress;\n    let isEmergencyAndFinanceNotCompleteObj = [];\n    let supplierNames = [];\n    const workbook = new (exceljs__WEBPACK_IMPORTED_MODULE_0___default().Workbook)();\n    if (isInternal) {\n        data.forEach((sheetData, index1)=>{\n            if (sheetData.length === 0) {\n                console.error(\"sheetData is empty for index:\", index1);\n                return;\n            }\n            const sheetName = sheetData[0] || \"Sheet\".concat(index1 + 1);\n            let worksheet;\n            if (index1 < 9) {\n                if (Array.isArray(sheetName)) {\n                    const actualSheetName = sheetName[0];\n                    worksheet = workbook.addWorksheet(actualSheetName);\n                } else {\n                    worksheet = workbook.addWorksheet(sheetName);\n                }\n            }\n            if (sheetData.length > 1) {\n                let headers;\n                if (index1 < 9) {\n                    headers = Object.keys(sheetData[1]);\n                    worksheet.addRow(headers);\n                }\n                sheetData.slice(1).forEach((row, internalIndex)=>{\n                    if (index1 < 9) {\n                        const rowData = headers.map((header)=>row[header] || \"\");\n                        if (index1 != 4 && index1 != 3) {\n                            worksheet.addRow(rowData);\n                        } else if (index1 == 4) {\n                            worksheet.addRow(rowData);\n                        } else if (index1 == 3 && rowData[1] != \"\") {\n                            worksheet.addRow(rowData);\n                        }\n                    }\n                    if (index1 === 9) {\n                        supplierNames.push({\n                            supplierName: row === null || row === void 0 ? void 0 : row.supplierName,\n                            supplierCode: row === null || row === void 0 ? void 0 : row.supplierCode\n                        });\n                        if (row.isEmergencyAndFinanceNotComplete) {\n                            let isEmergencyAndFinanceNotCompleteSupplier = {\n                                isEmergencyAndFinanceNotComplete: row.isEmergencyAndFinanceNotComplete,\n                                supplierName: row.supplierName\n                            };\n                            isEmergencyAndFinanceNotCompleteObj.push(isEmergencyAndFinanceNotCompleteSupplier);\n                        }\n                        fetch(\"\".concat(serverAddress, \"suppliers/update-supplier/\").concat(row.id), {\n                            method: \"PUT\",\n                            headers: {\n                                Authorization: \"Bearer \".concat(token),\n                                Accept: \"application/json\",\n                                \"Content-Type\": \"application/json\"\n                            },\n                            body: JSON.stringify({\n                                sectionName: \"updateStatus\",\n                                type: \"exportExcel\",\n                                status: 5,\n                                exported: true,\n                                updated_date: new Date().toISOString(),\n                                company_name: row.supplierName,\n                                to: \"Internal\"\n                            })\n                        }).then((res)=>{\n                            if (res.status === 200) {\n                                return res.json();\n                            }\n                            // if (res.status === 401){\n                            //   toast.error(\"Your session has expired. Please log in again.\");\n                            //   setTimeout(() => {\n                            //     const redirectUrl = `/login?redirect=${encodeURIComponent(\n                            //       window.location.pathname\n                            //     )}`;\n                            //     logoutHandler(instance, redirectUrl);\n                            //   }, 3000);\n                            //   return null;\n                            // }\n                            return Promise.reject(res);\n                        }).then((json)=>{\n                            if (json.status == 200) {\n                                return true;\n                            }\n                        }).catch((error)=>{\n                            console.log(error);\n                        });\n                    }\n                });\n            }\n        });\n    } else if (isProductExtract) {\n        if (data.length === 0) {\n            console.error(\"sheetData is empty for index:\");\n            return;\n        }\n        data.forEach((sheetData)=>{\n            var _Object;\n            const sheetName = sheetData[0] || \"Sheet\".concat(index + 1);\n            let worksheet;\n            if (Array.isArray(sheetName)) {\n                const actualSheetName = sheetName[0];\n                worksheet = workbook.addWorksheet(actualSheetName);\n            } else {\n                worksheet = workbook.addWorksheet(sheetName);\n            }\n            // console.log(\"Object.keys(sheetData[1])\",Object?.keys(sheetData[1])? Object?.keys(sheetData[1]) : 'not there');\n            let headers = ((_Object = Object) === null || _Object === void 0 ? void 0 : _Object.keys(sheetData[1])) ? Object.keys(sheetData[1]) : \"null\";\n            if (isMultiple) {\n                headers = headers.slice(0, -1);\n            }\n            worksheet.addRow(headers);\n            sheetData.slice(1).forEach((row, internalIndex)=>{\n                let rowData;\n                rowData = headers.map((header)=>row[header] || \"\");\n                worksheet.addRow(rowData);\n            });\n        });\n    } else {\n        if (data.length === 0) {\n            console.error(\"sheetData is empty for index:\");\n            return;\n        }\n        data.forEach((row, index1)=>{\n            const sheetName = row[0] || \"Sheet\".concat(index1 + 1);\n            let worksheet;\n            if (Array.isArray(sheetName)) {\n                const actualSheetName = sheetName[0];\n                worksheet = workbook.addWorksheet(actualSheetName);\n            } else {\n                worksheet = workbook.addWorksheet(sheetName);\n            }\n            let headers;\n            if (index1 == 1) {\n                headers = Object.keys(row[1]).slice(0, -2);\n            } else {\n                headers = Object.keys(row[1]);\n            }\n            worksheet.addRow(headers);\n            for(let i = 1; i < row.length; i++){\n                if (index1 == 0 && i > 0) {\n                    supplierNames.push({\n                        supplierName: row[i][\"Supplier name\"],\n                        supplierCode: row[i][\"Supplier code\"]\n                    });\n                }\n                let rowData;\n                if (index1 == 1) {\n                    rowData = headers.map((header)=>row[i][header] || \"\").slice(0, -2);\n                } else {\n                    rowData = headers.map((header)=>row[i][header] || \"\");\n                }\n                worksheet.addRow(rowData);\n                if (row[i].isEmergencyAndFinanceNotComplete) {\n                    let isEmergencyAndFinanceNotCompleteSupplier = {\n                        isEmergencyAndFinanceNotComplete: row[i].isEmergencyAndFinanceNotComplete,\n                        supplierName: row[i][\"Supplier name\"]\n                    };\n                    isEmergencyAndFinanceNotCompleteObj.push(isEmergencyAndFinanceNotCompleteSupplier);\n                }\n                if (index1 == 1) {\n                    fetch(\"\".concat(serverAddress, \"suppliers/update-supplier/\").concat(row[i].id), {\n                        method: \"PUT\",\n                        headers: {\n                            Authorization: \"Bearer \".concat(token),\n                            Accept: \"application/json\",\n                            \"Content-Type\": \"application/json\"\n                        },\n                        body: JSON.stringify({\n                            sectionName: \"updateStatus\",\n                            type: \"exportExcel\",\n                            status: 5,\n                            exported: true,\n                            updated_date: new Date().toISOString(),\n                            company_name: row[\"Supplier name\"],\n                            to: \"ISS\"\n                        })\n                    }).then((res)=>{\n                        if (res.status === 200) {\n                            return res.json();\n                        }\n                        // if (res.status === 401){\n                        //   toast.error(\"Your session has expired. Please log in again.\");\n                        //   setTimeout(() => {\n                        //     const redirectUrl = `/login?redirect=${encodeURIComponent(\n                        //       window.location.pathname\n                        //     )}`;\n                        //     logoutHandler(instance, redirectUrl);\n                        //   }, 3000);\n                        //   return null;\n                        // }\n                        return Promise.reject(res);\n                    }).then((json)=>{\n                        if (json.status == 200) {\n                            return true;\n                        }\n                    }).catch((error)=>{\n                        console.log(error);\n                    });\n                }\n            }\n        });\n    }\n    const buffer = await workbook.xlsx.writeBuffer();\n    const blob = new Blob([\n        buffer\n    ], {\n        type: \"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet\"\n    });\n    const blobUrl = URL.createObjectURL(blob);\n    const a = document.createElement(\"a\");\n    a.style.display = \"none\";\n    a.href = blobUrl;\n    const now = new Date();\n    const timestamp = \"\".concat(now.getFullYear(), \"-\").concat((now.getMonth() + 1).toString().padStart(2, \"0\"), \"-\").concat(now.getDate().toString().padStart(2, \"0\"), \"_\").concat(now.getHours().toString().padStart(2, \"0\"), \"-\").concat(now.getMinutes().toString().padStart(2, \"0\"), \"-\").concat(now.getSeconds().toString().padStart(2, \"0\"));\n    if (isInternal) {\n        a.download = \"internal_export_\".concat(timestamp, \"_\").concat(data[0].length - 1 === 1 ? \"S\" : \"G\", \".xlsx\");\n    } else if (isProductExtract) {\n        if (request_no && !varietyRequest) {\n            a.download = \"\".concat(request_no, \"_product_export.xlsx\");\n        } else if (request_no && varietyRequest) {\n            a.download = \"\".concat(request_no, \"_export.xlsx\");\n        } else {\n            a.download = \"product_export.xlsx\";\n        }\n    } else {\n        a.download = \"iss_export_\".concat(timestamp, \"_\").concat(data.length === 1 ? \"S\" : \"G\", \".xlsx\");\n    }\n    let fileName;\n    console.log(\"fle name\", fileName);\n    if (isInternal) {\n        fileName = \"internal_export_\".concat(timestamp, \"_\").concat(data[0].length - 1 === 1 ? \"S\" : \"G\", \".xlsx\");\n    } else if (isProductExtract && !varietyRequest) {\n        fileName = request_no ? \"\".concat(request_no, \"_product_export.xlsx\") : \"product_export.xlsx\";\n    } else if (varietyRequest) {\n        fileName = request_no ? \"\".concat(request_no, \"_NV_export.xlsx\") : \"NV_export.xlsx\";\n    } else {\n        fileName = \"iss_export_\".concat(timestamp, \"_\").concat(data.length === 1 ? \"S\" : \"G\", \".xlsx\");\n    }\n    console.log(\"fle name --- \\n\", fileName);\n    document.body.appendChild(a);\n    a.click();\n    // Clean up\n    URL.revokeObjectURL(blobUrl);\n    document.body.removeChild(a);\n    react_toastify__WEBPACK_IMPORTED_MODULE_1__.toast.success(\"Request details extracted successfully.\", {\n        toastId: 22,\n        position: \"top-right\",\n        autoClose: 3000,\n        pauseOnHover: false\n    });\n    if (!varietyRequest) {\n        const formData = new FormData();\n        formData.append(\"file\", blob, fileName);\n        formData.append(\"company\", company);\n        formData.append(\"prophet_id\", prophet_id);\n        formData.append(\"name\", userData === null || userData === void 0 ? void 0 : userData.name);\n        formData.append(\"isInternal\", isInternal);\n        formData.append(\"exporterEmail\", userData === null || userData === void 0 ? void 0 : userData.email);\n        formData.append(\"requestorEmail\", requestor_email);\n        formData.append(\"isProductRequest\", isProductExtract);\n        formData.append(\"supplierNames\", JSON.stringify(supplierNames));\n        formData.append(\"onProductSubmit\", onProductSubmit);\n        formData.append(\"request_no\", request_no);\n        formData.append(\"productEmailCommentPlaceholder\", productEmailCommentPlaceholder);\n        formData.append(\"productEmailParagraph\", productEmailParagraph);\n        const serializedData = JSON.stringify(isEmergencyAndFinanceNotCompleteObj);\n        formData.append(\"isEmergencyAndFinanceNotCompleteObj\", serializedData);\n        const response = await fetch(\"\".concat(serverAddress, \"email/send-email\"), {\n            method: \"POST\",\n            body: formData,\n            headers: {\n                Authorization: \"Bearer \".concat(token)\n            }\n        });\n        if (response.ok) {\n            react_toastify__WEBPACK_IMPORTED_MODULE_1__.toast.success(\"Email sent\", {\n                toastId: 22,\n                position: \"top-right\",\n                autoClose: 3000,\n                pauseOnHover: false\n            });\n        } else if (response.status === 401) {\n            // console.log(\"error YES 401\");\n            react_toastify__WEBPACK_IMPORTED_MODULE_1__.toast.error(\"Failed to send email\", {\n                toastId: 22,\n                position: \"top-right\",\n                autoClose: 3000,\n                pauseOnHover: false\n            });\n            return response.status;\n        } else {\n            react_toastify__WEBPACK_IMPORTED_MODULE_1__.toast.error(\"Failed to send email\", {\n                toastId: 22,\n                position: \"top-right\",\n                autoClose: 3000,\n                pauseOnHover: false\n            });\n        }\n    }\n    return true;\n};\n/* harmony default export */ __webpack_exports__[\"default\"] = (exportExcel);\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevExports = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevExports) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports on update so we can compare the boundary\n                // signatures.\n                module.hot.dispose(function (data) {\n                    data.prevExports = currentExports;\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevExports !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevExports, currentExports)) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevExports !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./utils/exportExcel.js\n"));

/***/ })

});