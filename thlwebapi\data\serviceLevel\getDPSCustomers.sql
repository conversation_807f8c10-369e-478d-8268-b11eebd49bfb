select
    distinct cust_customer.hocustcode as [value],
    cust_customer.hocustcode as [label]
from
    FLR_DEV_TEST_dps_BI_lookup.[dbo].sales_dm_stage_dim_sales sales
    join FLR_DEV_TEST_dps_BI_lookup.[dbo].sales_dm_stage_customer cust_customer on cust_customer.custcode = sales.custcode
    join FLR_DEV_TEST_dps_BI_lookup.[dbo].sales_dm_stage_customer delcust_customer on sales.delcustcode = delcust_customer.custcode
WHERE
    case
        when cust_customer.custcode <> 'DPS' then cust_customer.hocustcode
        else delcust_customer.hocustcode
    end NOT IN ('PPACK', 'REJISS', 'SMOVE')
    AND altfilid > 0
    AND delivery_date >= GREATEST(CAST('20231001' AS DATE), @start_date)
    AND delivery_date <= '20250426'
    AND cust_customer.category_no = 1
    AND deptcode = 1
union
select
    distinct delcust_customer.hocustcode as [value],
    delcust_customer.hocustcode as [label]
from
    FLR_DEV_TEST_dps_BI_lookup.[dbo].vw_dps_service_lvl_tc_sales_additional sales
    --join FLR_DEV_TEST_dps_BI_lookup.[dbo].sales_dm_stage_customer cust_customer on cust_customer.custcode = sales.custcode
    join FLR_DEV_TEST_dps_BI_lookup.[dbo].sales_dm_stage_customer delcust_customer on sales.delcustcode = delcust_customer.custcode
WHERE
   delcust_customer.hocustcode NOT IN ('PPACK', 'REJISS', 'SMOVE')
    AND altfilid > 0
    AND delivery_date >= CASE WHEN @start_date > '20250427' THEN @start_date ELSE '20250427' END
    AND delivery_date <= LEAST(CAST(GETDATE() AS DATE), @end_date)
    AND delcust_customer.category_no = 1
    AND deptcode = 1

order by [label]