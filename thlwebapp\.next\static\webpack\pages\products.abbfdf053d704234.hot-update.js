"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/products",{

/***/ "./utils/renderer/productActionRenderer.js":
/*!*************************************************!*\
  !*** ./utils/renderer/productActionRenderer.js ***!
  \*************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _fortawesome_react_fontawesome__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @fortawesome/react-fontawesome */ \"./node_modules/@fortawesome/react-fontawesome/index.es.js\");\n/* harmony import */ var _fortawesome_free_solid_svg_icons__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @fortawesome/free-solid-svg-icons */ \"./node_modules/@fortawesome/free-solid-svg-icons/index.mjs\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _services_apiConfig__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/services/apiConfig */ \"./services/apiConfig.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _exportExcel__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../exportExcel */ \"./utils/exportExcel.js\");\n/* harmony import */ var react_toastify__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! react-toastify */ \"./node_modules/react-toastify/dist/react-toastify.esm.mjs\");\n/* harmony import */ var react_toastify_dist_ReactToastify_css__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! react-toastify/dist/ReactToastify.css */ \"./node_modules/react-toastify/dist/ReactToastify.css\");\n/* harmony import */ var react_toastify_dist_ReactToastify_css__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(react_toastify_dist_ReactToastify_css__WEBPACK_IMPORTED_MODULE_7__);\n/* harmony import */ var _barrel_optimize_names_Dialog_Transition_headlessui_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Dialog,Transition!=!@headlessui/react */ \"__barrel_optimize__?names=Dialog,Transition!=!./node_modules/@headlessui/react/dist/headlessui.esm.js\");\n/* harmony import */ var _azure_msal_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @azure/msal-react */ \"./node_modules/@azure/msal-react/dist/index.js\");\n/* harmony import */ var js_cookie__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! js-cookie */ \"./node_modules/js-cookie/dist/js.cookie.mjs\");\n/* harmony import */ var _secureStorage__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ../secureStorage */ \"./utils/secureStorage.js\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n//\n\n\n\nconst productActionRenderer = (params, userData, company, typeId, setIsLoading, isIssUser)=>{\n    _s();\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const product_id = params.data.id;\n    const data = params.data;\n    const serverAddress = _services_apiConfig__WEBPACK_IMPORTED_MODULE_3__.apiConfig.serverAddress;\n    const [isCancelOpen, setIsCancelOpen] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(false);\n    const [cancelledReasonapi, setCancelledReasonapi] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(\"\");\n    const [isValidCancelReason, setIsValidCancelReason] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(true);\n    const [isEditing, setIsEditing] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(false);\n    const editProduct = ()=>{\n        setIsEditing(true);\n        setIsLoading(true);\n        if (true) {\n            var _params_data;\n            if (params && (params === null || params === void 0 ? void 0 : (_params_data = params.data) === null || _params_data === void 0 ? void 0 : _params_data.type) == \"FG\") {\n                router.push({\n                    pathname: \"/finished-product-request/\".concat(product_id, \"/edit\")\n                });\n            } else if (params && params.data.type == \"RM\") {\n                setIsLoading(true);\n                router.push({\n                    pathname: \"/raw-material-request/\".concat(product_id, \"/edit\")\n                });\n            } else if (params && params.data.type == \"NV\") {\n                setIsLoading(true);\n                router.push({\n                    pathname: \"/variety/\".concat(product_id, \"/edit\")\n                });\n            } else if (params && params.data.type == \"PK\") {\n                setIsLoading(true);\n                router.push({\n                    pathname: \"/packaging-form/\".concat(product_id, \"/edit\")\n                });\n            }\n        }\n    };\n    const handleCancelReason = (data)=>{\n        if (data) {\n            setIsValidCancelReason(true);\n        } else {\n            setIsValidCancelReason(false);\n        }\n    };\n    const trimInputText = (input)=>{\n        return input.trim();\n    };\n    const exportToExcel = async ()=>{\n        if (data.status === \"Submitted\" || data.status === \"Exported\") {\n            let userText3 = \"\";\n            let markVariety = \"\";\n            if (data.company == \"dpsltd\") {\n                userText3 = \"DPS\";\n                markVariety = data === null || data === void 0 ? void 0 : data.mark_variety_name;\n            } else if (data.company == \"efcltd\") {\n                userText3 = \"OFF\";\n                markVariety = \"RM\" + \" \" + (data === null || data === void 0 ? void 0 : data.mark_variety_name);\n            } else if (data.company == \"fpp-ltd\") {\n                userText3 = \"FPP\";\n                markVariety = \"RM\" + \" \" + (data === null || data === void 0 ? void 0 : data.mark_variety_name);\n            } else {\n                userText3 = \"FLRS\"; //TODO: remove this later\n                markVariety = data === null || data === void 0 ? void 0 : data.mark_variety_name;\n            }\n            const filteredExportData = [\n                [\n                    \"Product Extract\",\n                    {\n                        \"User Boolean 1\": \"True\",\n                        \"Master Product Code\": data === null || data === void 0 ? void 0 : data.master_product_code,\n                        \"Commodity Code\": data === null || data === void 0 ? void 0 : data.intrastat_commodity_code_id,\n                        \"User Text 4\": data === null || data === void 0 ? void 0 : data.userText4,\n                        \"User Text 5\": data === null || data === void 0 ? void 0 : data.userText5,\n                        \"User Text 6\": data === null || data === void 0 ? void 0 : data.userText6,\n                        \"Intrastat weight mass\": data === null || data === void 0 ? void 0 : data.outer_gross_weight,\n                        \"Sub Product Code\": data === null || data === void 0 ? void 0 : data.sub_product_code,\n                        \"Mark/variety\": markVariety,\n                        \"Count or size\": data === null || data === void 0 ? void 0 : data.count_or_size,\n                        \"Sort Group Number\": data === null || data === void 0 ? void 0 : data.group_id,\n                        \"User Text 3\": userText3,\n                        \"Product Number\": \"\",\n                        \"Units in Outer\": data === null || data === void 0 ? void 0 : data.units_in_outer,\n                        \"Packs per pallet\": data === null || data === void 0 ? void 0 : data.cases_per_pallet,\n                        \"Sell packs per pallet\": data === null || data === void 0 ? void 0 : data.cases_per_pallet,\n                        \"Weight of outer\": data === null || data === void 0 ? void 0 : data.outer_gross_weight,\n                        \"Product distribution Point\": \"\",\n                        Buyer: 1,\n                        \"Temperature grade\": data === null || data === void 0 ? void 0 : data.temperature_grade_id,\n                        \"Temperature Grade\": data === null || data === void 0 ? void 0 : data.temperature_grade_name,\n                        \"Product Type\": data === null || data === void 0 ? void 0 : data.product_type_id,\n                        \"Product type\": data === null || data === void 0 ? void 0 : data.product_type_name,\n                        Active: \"True\"\n                    }\n                ]\n            ];\n            if (data.company == \"efcltd\" || data.company == \"fpp-ltd\") {\n                filteredExportData.push([\n                    \"ALTFIL Extract\",\n                    {\n                        Active: \"True\",\n                        \"Altfil record id\": \"\",\n                        \"Generic Code\": userText3,\n                        \"Alternate product number\": \"\",\n                        \"Alternate number\": \"\",\n                        \"Alternate bar code number\": \"\",\n                        \"Alternate description\": data === null || data === void 0 ? void 0 : data.sub_product_code,\n                        \"Alternate product Master product code\": \"\",\n                        \"Alternate product number Count or size\": \"\",\n                        \"Alternate product number Gross weight outer\": \"\",\n                        \"Alternate product number Mark/variety\": \"\",\n                        \"Alternate group\": data === null || data === void 0 ? void 0 : data.group_id,\n                        \"Alternate count or size\": data === null || data === void 0 ? void 0 : data.units_in_outer,\n                        \"Alternate prefix\": \"\",\n                        \"Inner product barcode\": \"\",\n                        \"Outer product barcode\": \"\",\n                        \"Alternate product number extension\": \"\",\n                        \"End Customer\": \"\",\n                        Brand: \"\",\n                        \"Display until days\": \"\",\n                        \"GTIN 14\": \"\",\n                        \"Calibre / Size\": \"\",\n                        \"Alternate product number Packs per pallet\": \"\",\n                        \"Inner stock keeping unit\": \"\",\n                        \"Stock keeping unit\": \"\",\n                        \"Customer product code\": \"\",\n                        \"Alternate use standard prefix (1=yes)\": \"1\",\n                        \"User text 1\": \"\"\n                    }\n                ]);\n            // console.log(\n            //   \"filtered export data after creating new array\",\n            //   filteredExportData\n            // );\n            }\n            const productEmailParagraph = \"<p>User \".concat(userData.name, \" submitted a Raw material request with request number \").concat(params.data.request_no, \" \\n          to ISS.\\n       \\n      </p>\");\n            let productEmailCommentPlaceholder = '<p style=\\'\\n      color: #32353e; margin: 0 0 10px 0; padding: 0;font-family: \"HelveticaNeueLight\", \"HelveticaNeue-Light\", \"Helvetica Neue Light\", \"HelveticaNeue\", \"Helvetica Neue\", \"TeXGyreHerosRegular\", \"Helvetica\", \"Tahoma\", \"Geneva\", \"Arial\", sans-serif; font-weight: 300;\\n        font-stretch: normal; font-size: 14px; line-height: 1.7; text-align: left;\\'>Comments: <i>'.concat(params.data.emailComment ? params.data.emailComment : \"-\", \"</i></p>\\n      \");\n            const export_response = await (0,_exportExcel__WEBPACK_IMPORTED_MODULE_5__[\"default\"])(filteredExportData, false, data.company, userData, \"\", params.data.originator_email, false, true, true, productEmailParagraph, productEmailCommentPlaceholder, params.data.request_no);\n            // console.log(\"export_response\", export_response);\n            if (export_response === 401) {\n                react_toastify__WEBPACK_IMPORTED_MODULE_6__.toast.error(\"Your session has expired. Please log in again.\");\n                setTimeout(async ()=>{\n                    await (0,_secureStorage__WEBPACK_IMPORTED_MODULE_10__.logout)();\n                    const redirectUrl = \"/login?redirect=\".concat(encodeURIComponent(window.location.pathname));\n                    router.push(redirectUrl);\n                }, 3000);\n                return null;\n            }\n        } else {\n            react_toastify__WEBPACK_IMPORTED_MODULE_6__.toast.error(\"Kindly Submit the Request to Export it.\", {\n                position: \"top-right\",\n                autoClose: 3000,\n                hideProgressBar: false,\n                closeOnClick: true,\n                pauseOnHover: false,\n                draggable: true,\n                progress: undefined,\n                theme: \"light\"\n            });\n            return;\n        }\n    };\n    const cancelProduct = ()=>{\n        setIsCancelOpen(true);\n    };\n    const closeCancelModal = ()=>{\n        setIsCancelOpen(false);\n    };\n    const getProphetId = ()=>{\n        switch(params.data.company){\n            case \"dpsltd\":\n                return 1;\n            case \"efcltd\":\n                return 3;\n            case \"fpp-ltd\":\n                return 4;\n            default:\n                return 1;\n        }\n    };\n    const saveModalData = ()=>{\n        const prophetId = getProphetId();\n        // return;\n        if (!cancelledReasonapi) {\n            setIsValidCancelReason(false);\n            return;\n        }\n        try {\n            var _params_data;\n            fetch(\"\".concat(serverAddress, \"products/product-update-status\"), {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                credentials: \"include\",\n                body: JSON.stringify({\n                    status: 6,\n                    productId: product_id,\n                    updated_date: new Date().toISOString(),\n                    reason: cancelledReasonapi,\n                    request_no: params.data.request_no,\n                    type: params.data.type,\n                    cancelled_by: userData.email,\n                    cancelled_by_name: userData.name,\n                    cancelled_date: new Date().toISOString(),\n                    originator_email: (_params_data = params.data) === null || _params_data === void 0 ? void 0 : _params_data.originator_email,\n                    current_action_id: params.data.action_id,\n                    prophetId: prophetId,\n                    code: params.data.code\n                })\n            }).then((res)=>{\n                if (res.status === 400) {\n                    react_toastify__WEBPACK_IMPORTED_MODULE_6__.toast.error(\"There was an error with your request. Please check your data and try again.\");\n                }\n                if (res.status === 200) {\n                    return res.json();\n                } else if (res.status === 401) {\n                    react_toastify__WEBPACK_IMPORTED_MODULE_6__.toast.error(\"Your session has expired. Please log in again.\");\n                    setTimeout(async ()=>{\n                        await (0,_secureStorage__WEBPACK_IMPORTED_MODULE_10__.logout)();\n                        const redirectUrl = \"/login?redirect=\".concat(encodeURIComponent(window.location.pathname));\n                        router.push(redirectUrl);\n                    }, 3000);\n                }\n                return null;\n            }).then((json)=>{\n                if (json) {\n                    setIsCancelOpen(false);\n                    if (params.data.type == \"NV\") {\n                        js_cookie__WEBPACK_IMPORTED_MODULE_9__[\"default\"].set(\"PreviousPage\", true);\n                    }\n                    window.location.reload();\n                }\n            });\n        } catch (error) {\n            console.error(\"Failed to cancel product by :\", error);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex flex-row gap-4 justify-start text-blue-500 pl-3\",\n                children: params.data.status == \"Prophet Setup Completed\" || params.data.status == \"Prophet to Setup\" || params.data.status == \"Cancelled\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    title: \"View Request\",\n                    onClick: editProduct,\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fortawesome_react_fontawesome__WEBPACK_IMPORTED_MODULE_1__.FontAwesomeIcon, {\n                        icon: _fortawesome_free_solid_svg_icons__WEBPACK_IMPORTED_MODULE_11__.faEye,\n                        size: \"lg\",\n                        className: \"text-skin-primary\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\utils\\\\renderer\\\\productActionRenderer.js\",\n                        lineNumber: 312,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\utils\\\\renderer\\\\productActionRenderer.js\",\n                    lineNumber: 311,\n                    columnNumber: 11\n                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            disabled: isEditing,\n                            title: \"Edit Request\",\n                            onClick: editProduct,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fortawesome_react_fontawesome__WEBPACK_IMPORTED_MODULE_1__.FontAwesomeIcon, {\n                                icon: _fortawesome_free_solid_svg_icons__WEBPACK_IMPORTED_MODULE_11__.faPenToSquare,\n                                size: \"lg\",\n                                className: \"text-skin-primary\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\utils\\\\renderer\\\\productActionRenderer.js\",\n                                lineNumber: 326,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\utils\\\\renderer\\\\productActionRenderer.js\",\n                            lineNumber: 320,\n                            columnNumber: 13\n                        }, undefined),\n                        params.data.status != \"Setup Completed\" && params.data.status != \"Submitted\" && params.data.status != \"Cancelled\" && !isIssUser && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: cancelProduct,\n                            title: \"Cancel Request\",\n                            className: \"flex items-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fortawesome_react_fontawesome__WEBPACK_IMPORTED_MODULE_1__.FontAwesomeIcon, {\n                                icon: _fortawesome_free_solid_svg_icons__WEBPACK_IMPORTED_MODULE_11__.faXmark,\n                                size: \"sm\",\n                                className: \"border rounded-sm border-skin-primary text-skin-primary m-0 w-[15px] h-[15px]\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\utils\\\\renderer\\\\productActionRenderer.js\",\n                                lineNumber: 341,\n                                columnNumber: 19\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\utils\\\\renderer\\\\productActionRenderer.js\",\n                            lineNumber: 336,\n                            columnNumber: 17\n                        }, undefined),\n                        params.data.status == \"Submitted\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>exportToExcel(params),\n                            title: \"Export Request\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fortawesome_react_fontawesome__WEBPACK_IMPORTED_MODULE_1__.FontAwesomeIcon, {\n                                icon: _fortawesome_free_solid_svg_icons__WEBPACK_IMPORTED_MODULE_11__.faFileExport,\n                                size: \"lg\",\n                                className: \"cursor-pointer text-skin-primary\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\utils\\\\renderer\\\\productActionRenderer.js\",\n                                lineNumber: 353,\n                                columnNumber: 17\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\utils\\\\renderer\\\\productActionRenderer.js\",\n                            lineNumber: 349,\n                            columnNumber: 15\n                        }, undefined)\n                    ]\n                }, void 0, true)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\utils\\\\renderer\\\\productActionRenderer.js\",\n                lineNumber: 308,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Dialog_Transition_headlessui_react__WEBPACK_IMPORTED_MODULE_12__.Transition, {\n                appear: true,\n                show: isCancelOpen,\n                as: react__WEBPACK_IMPORTED_MODULE_4__.Fragment,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Dialog_Transition_headlessui_react__WEBPACK_IMPORTED_MODULE_12__.Dialog, {\n                    as: \"div\",\n                    className: \"relative z-10\",\n                    onClose: closeCancelModal,\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Dialog_Transition_headlessui_react__WEBPACK_IMPORTED_MODULE_12__.Transition.Child, {\n                            as: react__WEBPACK_IMPORTED_MODULE_4__.Fragment,\n                            enter: \"ease-out duration-300\",\n                            enterFrom: \"opacity-0\",\n                            enterTo: \"opacity-100\",\n                            leave: \"ease-in duration-200\",\n                            leaveFrom: \"opacity-100\",\n                            leaveTo: \"opacity-0\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"fixed inset-0 bg-black bg-opacity-25 backdrop-blur-sm\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\utils\\\\renderer\\\\productActionRenderer.js\",\n                                lineNumber: 375,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\utils\\\\renderer\\\\productActionRenderer.js\",\n                            lineNumber: 366,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"fixed inset-0 overflow-y-auto\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-center min-h-full p-4 text-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Dialog_Transition_headlessui_react__WEBPACK_IMPORTED_MODULE_12__.Transition.Child, {\n                                    as: react__WEBPACK_IMPORTED_MODULE_4__.Fragment,\n                                    enter: \"ease-out duration-300\",\n                                    enterFrom: \"opacity-0 scale-95\",\n                                    enterTo: \"opacity-100 scale-100\",\n                                    leave: \"ease-in duration-200\",\n                                    leaveFrom: \"opacity-100 scale-100\",\n                                    leaveTo: \"opacity-0 scale-95\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Dialog_Transition_headlessui_react__WEBPACK_IMPORTED_MODULE_12__.Dialog.Panel, {\n                                        className: \" w-[45%] transform overflow-hidden rounded-xl bg-white  text-left align-middle shadow-xl transition-all\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative bg-white rounded-lg shadow\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-start justify-between p-8 rounded-t\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"flex flex-row text-xl font-semibold text-gray-900  items-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"flex justify-center items-center border border-skin-primary text-skin-primary w-[25px] h-[25px] text-center leading-5 rounded-full text-base me-4\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fortawesome_react_fontawesome__WEBPACK_IMPORTED_MODULE_1__.FontAwesomeIcon, {\n                                                                            icon: _fortawesome_free_solid_svg_icons__WEBPACK_IMPORTED_MODULE_11__.faInfo\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\utils\\\\renderer\\\\productActionRenderer.js\",\n                                                                            lineNumber: 396,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        \" \"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\utils\\\\renderer\\\\productActionRenderer.js\",\n                                                                    lineNumber: 395,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                \" \",\n                                                                \"Cancellation Reason\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\utils\\\\renderer\\\\productActionRenderer.js\",\n                                                            lineNumber: 394,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: closeCancelModal,\n                                                            type: \"button\",\n                                                            className: \"text-black bg-transparent hover:bg-gray-200 hover:text-gray-900 rounded-lg text-xl w-8 h-8 ml-auto inline-flex justify-center items-center \",\n                                                            \"data-modal-hide\": \"default-modal\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fortawesome_react_fontawesome__WEBPACK_IMPORTED_MODULE_1__.FontAwesomeIcon, {\n                                                                    icon: _fortawesome_free_solid_svg_icons__WEBPACK_IMPORTED_MODULE_11__.faXmark,\n                                                                    className: \"text-skin-primary\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\utils\\\\renderer\\\\productActionRenderer.js\",\n                                                                    lineNumber: 406,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                \" \"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\utils\\\\renderer\\\\productActionRenderer.js\",\n                                                            lineNumber: 400,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\utils\\\\renderer\\\\productActionRenderer.js\",\n                                                    lineNumber: 393,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"p-8 py-0 space-y-6\",\n                                                    children: [\n                                                        params.data.type == \"NV\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-base xl:text-md 2xl:text-lg leading-relaxed mt-2\",\n                                                            children: \"Enter Variety Cancellation Reason.\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\utils\\\\renderer\\\\productActionRenderer.js\",\n                                                            lineNumber: 415,\n                                                            columnNumber: 25\n                                                        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-base xl:text-md 2xl:text-lg leading-relaxed mt-2\",\n                                                            children: \"Enter Product Cancellation Reason.\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\utils\\\\renderer\\\\productActionRenderer.js\",\n                                                            lineNumber: 419,\n                                                            columnNumber: 25\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                            className: \"flex flex-col w-full rounded-md p-2 px-3  border border-light-gray2\",\n                                                            rows: \"8\",\n                                                            value: cancelledReasonapi,\n                                                            onChange: (e)=>{\n                                                                setCancelledReasonapi(e.target.value), handleCancelReason(e.target.value);\n                                                            },\n                                                            onBlur: (e)=>{\n                                                                const trimmedValue = trimInputText(e.target.value);\n                                                                setCancelledReasonapi(trimmedValue);\n                                                                handleCancelReason(trimmedValue);\n                                                            },\n                                                            // disabled={(e) => {e.target.value == \"\"}}\n                                                            placeholder: \"Provide reason for cancellation...\",\n                                                            maxlength: \"500\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\utils\\\\renderer\\\\productActionRenderer.js\",\n                                                            lineNumber: 423,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        !isValidCancelReason && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-red-500\",\n                                                            children: \"Please Provide reason for cancellation\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\utils\\\\renderer\\\\productActionRenderer.js\",\n                                                            lineNumber: 441,\n                                                            columnNumber: 25\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\utils\\\\renderer\\\\productActionRenderer.js\",\n                                                    lineNumber: 413,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-end p-6 space-x-2 justify-end\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: saveModalData,\n                                                        \"data-modal-hide\": \"default-modal\",\n                                                        type: \"button\",\n                                                        className: \" bg-skin-primary hover:bg-skin-primary focus:ring-4 focus:outline-none focus:ring-blue-300 font-medium rounded-md text-md text-white mr-1 px-6 py-2 text-center \",\n                                                        children: \"Cancel Request\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\utils\\\\renderer\\\\productActionRenderer.js\",\n                                                        lineNumber: 448,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\utils\\\\renderer\\\\productActionRenderer.js\",\n                                                    lineNumber: 447,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\utils\\\\renderer\\\\productActionRenderer.js\",\n                                            lineNumber: 391,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\utils\\\\renderer\\\\productActionRenderer.js\",\n                                        lineNumber: 389,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\utils\\\\renderer\\\\productActionRenderer.js\",\n                                    lineNumber: 380,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\utils\\\\renderer\\\\productActionRenderer.js\",\n                                lineNumber: 379,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\utils\\\\renderer\\\\productActionRenderer.js\",\n                            lineNumber: 378,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\utils\\\\renderer\\\\productActionRenderer.js\",\n                    lineNumber: 365,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\utils\\\\renderer\\\\productActionRenderer.js\",\n                lineNumber: 364,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true);\n};\n_s(productActionRenderer, \"fd5HB54zKBxFTPaT2nhK9+IoIVg=\", false, function() {\n    return [\n        next_router__WEBPACK_IMPORTED_MODULE_2__.useRouter\n    ];\n});\n/* harmony default export */ __webpack_exports__[\"default\"] = (productActionRenderer);\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevExports = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevExports) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports on update so we can compare the boundary\n                // signatures.\n                module.hot.dispose(function (data) {\n                    data.prevExports = currentExports;\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevExports !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevExports, currentExports)) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevExports !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./utils/renderer/productActionRenderer.js\n"));

/***/ })

});