"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/raw-material-request/add",{

/***/ "./components/DrawerComponent.js":
/*!***************************************!*\
  !*** ./components/DrawerComponent.js ***!
  \***************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DrawerComponent: function() { return /* binding */ DrawerComponent; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _fortawesome_react_fontawesome__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @fortawesome/react-fontawesome */ \"./node_modules/@fortawesome/react-fontawesome/index.es.js\");\n/* harmony import */ var _fortawesome_free_solid_svg_icons__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @fortawesome/free-solid-svg-icons */ \"./node_modules/@fortawesome/free-solid-svg-icons/index.mjs\");\n/* harmony import */ var _fluentui_react_components__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @fluentui/react-components */ \"./node_modules/@fluentui/react-components/lib/index.js\");\n/* harmony import */ var _fluentui_react_icons__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @fluentui/react-icons */ \"./node_modules/@fluentui/react-icons/lib/index.js\");\n/* harmony import */ var ag_grid_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ag-grid-react */ \"./node_modules/ag-grid-react/lib/main.js\");\n/* harmony import */ var ag_grid_react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(ag_grid_react__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _services_apiConfig__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/services/apiConfig */ \"./services/apiConfig.js\");\n/* harmony import */ var react_toastify__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! react-toastify */ \"./node_modules/react-toastify/dist/react-toastify.esm.mjs\");\n/* harmony import */ var react_toastify_dist_ReactToastify_css__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! react-toastify/dist/ReactToastify.css */ \"./node_modules/react-toastify/dist/ReactToastify.css\");\n/* harmony import */ var react_toastify_dist_ReactToastify_css__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(react_toastify_dist_ReactToastify_css__WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var _utils_renderer_productActionRenderer__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/utils/renderer/productActionRenderer */ \"./utils/renderer/productActionRenderer.js\");\n/* harmony import */ var _utils_renderer_productReferenceRenderer__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/utils/renderer/productReferenceRenderer */ \"./utils/renderer/productReferenceRenderer.js\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n // Import useRef and useCallback\n\n\n\n\n\nconst useStyles = (0,_fluentui_react_components__WEBPACK_IMPORTED_MODULE_9__.makeStyles)({\n    root: {\n        ..._fluentui_react_components__WEBPACK_IMPORTED_MODULE_9__.shorthands.border(\"2px\", \"solid\", \"#ccc\"),\n        ..._fluentui_react_components__WEBPACK_IMPORTED_MODULE_9__.shorthands.overflow(\"hidden\"),\n        display: \"flex\",\n        height: \"480px\",\n        backgroundColor: \"#fff\"\n    },\n    content: {\n        ..._fluentui_react_components__WEBPACK_IMPORTED_MODULE_9__.shorthands.flex(1),\n        ..._fluentui_react_components__WEBPACK_IMPORTED_MODULE_9__.shorthands.padding(\"16px\"),\n        display: \"grid\",\n        justifyContent: \"flex-start\",\n        alignItems: \"flex-start\",\n        gridRowGap: _fluentui_react_components__WEBPACK_IMPORTED_MODULE_9__.tokens.spacingVerticalXXL,\n        gridAutoRows: \"max-content\"\n    },\n    field: {\n        display: \"grid\",\n        gridRowGap: _fluentui_react_components__WEBPACK_IMPORTED_MODULE_9__.tokens.spacingVerticalS\n    }\n});\nconst DrawerComponent = (param)=>{\n    let { isDrawerOpen, setIsDrawerOpen, title, dropdownData, placeholderText, legend, max_length, min_length, dataKey, onNewDropdownData, username, useremail, userData, prophetId } = param;\n    _s();\n    const [type, setType] = react__WEBPACK_IMPORTED_MODULE_1__.useState(\"overlay\");\n    const [description, setDescription] = react__WEBPACK_IMPORTED_MODULE_1__.useState(\"\");\n    const [removedRows, setRemovedRows] = react__WEBPACK_IMPORTED_MODULE_1__.useState(\"\");\n    const [value, setValue] = react__WEBPACK_IMPORTED_MODULE_1__.useState(\"\");\n    const [isButtonDisabled, setIsButtonDisabled] = react__WEBPACK_IMPORTED_MODULE_1__.useState(true);\n    const [isUpdateMode, setIsUpdateMode] = react__WEBPACK_IMPORTED_MODULE_1__.useState(false);\n    const [code, setCode] = react__WEBPACK_IMPORTED_MODULE_1__.useState(\"\");\n    const [rowData, setRowData] = react__WEBPACK_IMPORTED_MODULE_1__.useState([]);\n    const [addNewValue, setAddNewValue] = react__WEBPACK_IMPORTED_MODULE_1__.useState(false);\n    //const [keyy, setKey] =  React.useState(dataKey ? dataKey : \"\")\n    const serverAddress = _services_apiConfig__WEBPACK_IMPORTED_MODULE_4__.apiConfig.serverAddress;\n    const [isValidDescription, setisValidDescription] = react__WEBPACK_IMPORTED_MODULE_1__.useState(true);\n    const [isValidCode, setisValidCode] = react__WEBPACK_IMPORTED_MODULE_1__.useState(true);\n    // Define gridRef using useRef\n    const gridRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Define onFilterTextBoxChanged function using useCallback\n    const onFilterTextBoxChanged = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        gridRef.current.api.setQuickFilter(document.getElementById(\"filter-text-box\").value);\n    // setGridApi(params.api);\n    }, []);\n    const formatName = (params)=>{\n        // Check condition and return formatted value\n        // console.log(typeof params.data.is_new);\n        if (params.data.is_new === true) {\n            // console.log(\"inside here\");\n            // console.log(params.data.name);\n            return \"*\".concat(params.data.label);\n        }\n    //return null;\n    };\n    const formatCode = (params)=>{\n        // Check condition and return formatted value\n        if (params.data.is_new === true) {\n            // console.log(params.data.code);\n            return \"*\".concat(params.data.code);\n        }\n    //return null;\n    };\n    // Define defaultColDef and columnDefs using useMemo\n    const defaultColDef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>({\n            sortable: true,\n            filter: false,\n            resizable: true,\n            flex: 1,\n            suppressMenu: false\n        }), []);\n    const trimInputText = (input)=>{\n        return input.trim();\n    };\n    react__WEBPACK_IMPORTED_MODULE_1__.useEffect(()=>{\n        if (!addNewValue) {\n            setRowData(dropdownData);\n        }\n    }, []);\n    const columnDefs = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>[\n            {\n                headerName: \"Name\",\n                field: \"label\",\n                flex: \"3%\",\n                headerClass: \"header-with-border\",\n                cellStyle: {\n                    display: \"flex\"\n                },\n                valueFormatter: formatName,\n                cellStyle: function(params) {\n                    if (params.data.is_new === true || params.data.is_new === 1) {\n                        //Here you can check the value and based on that you can change the color\n                        return {\n                            color: \"red\"\n                        };\n                    } else {\n                        return null;\n                    }\n                }\n            },\n            {\n                headerName: \"Code\",\n                field: \"code\",\n                flex: \"2%\",\n                headerClass: \"header-with-border\",\n                cellStyle: {\n                    display: \"flex\"\n                },\n                valueFormatter: formatCode,\n                cellStyle: function(params) {\n                    if (params.data.is_new === true || params.data.is_new === 1) {\n                        //Here you can check the value and based on that you can change the color\n                        return {\n                            color: \"red\"\n                        };\n                    } else {\n                        return null;\n                    }\n                }\n            },\n            {\n                headerName: \"Is New\",\n                field: \"is_new\",\n                hide: true\n            },\n            {\n                headerName: \"Action\",\n                field: \"\",\n                headerClass: \"header-with-border\",\n                cellRenderer: (params)=>(0,_utils_renderer_productReferenceRenderer__WEBPACK_IMPORTED_MODULE_8__[\"default\"])(params, userData, isUpdateMode),\n                // cellRenderer: () => addRow(),\n                flex: \"2%\",\n                cellStyle: ()=>({\n                        display: \"flex\",\n                        justifyContent: \"center\",\n                        paddingRight: \"20px\"\n                    }),\n                sortable: false,\n                cellRendererParams: {\n                    setCode: setCode,\n                    setDescription: setDescription,\n                    setValue: setValue,\n                    setIsUpdateMode: setIsUpdateMode,\n                    setIsButtonDisabled: setIsButtonDisabled,\n                    isUpdateMode: isUpdateMode\n                },\n                onCellClicked: ()=>{\n                    setisValidDescription(true);\n                    setisValidCode(true);\n                // addRow();\n                }\n            }\n        ], []);\n    // console.log(\"isUpdateMode\",isUpdateMode);\n    // console.log('description: ',description,'\\ncode: ', code,'\\nvalue: ', value)\n    const handelupdate = ()=>{\n        const checkDescription = rowData.find((desc)=>desc.label == description && desc.value != value);\n        // console.log(code);\n        const checkProduct = rowData.find((product)=>product.code == code && product.value != value);\n        if (checkDescription) {\n            setisValidDescription(false);\n        // console.log(\"yes\");\n        } else {\n            setisValidDescription(true);\n        // console.log(\"no\");\n        }\n        if (checkProduct) {\n            setisValidCode(false);\n        // console.log(\"yes yes\");\n        } else {\n            setisValidCode(true);\n        // console.log(\"no no\");\n        }\n        if (!checkDescription && !checkProduct) {\n            const saveData = {\n                value: value,\n                description: description,\n                code: code,\n                is_new: true,\n                tableName: dataKey,\n                username: username,\n                useremail: useremail,\n                prophetId: prophetId\n            };\n            try {\n                fetch(\"\".concat(serverAddress, \"products/update-all-dropdown-value\"), {\n                    method: \"PUT\",\n                    headers: {\n                        \"Content-Type\": \"application/json\"\n                    },\n                    credentials: \"include\",\n                    body: JSON.stringify(saveData)\n                }).then((res)=>{\n                    if (res.status === 401) {\n                        react_toastify__WEBPACK_IMPORTED_MODULE_5__.toast.error(\"Your session has expired. Please log in again.\");\n                        setTimeout(async ()=>{\n                            await logout();\n                            router.push(\"/login\");\n                        }, 3000);\n                    }\n                    if (res.status === 200) {\n                        return res.json();\n                    } else {\n                        react_toastify__WEBPACK_IMPORTED_MODULE_5__.toast.error(\"Failed to save raw material.\");\n                    //setLoading(false);\n                    }\n                }).then((json)=>{\n                    // console.log(json);\n                    if (dataKey == \"masterProductCode\") {\n                        onNewDropdownData(dataKey, json.masterProductCode);\n                        setRowData(json.masterProductCode);\n                        react_toastify__WEBPACK_IMPORTED_MODULE_5__.toast.success(\"Updated New Master Code.\", {\n                            position: \"top-left\"\n                        });\n                    } else if (dataKey == \"markVariety\") {\n                        setRowData(json.markVariety);\n                        onNewDropdownData(dataKey, json.markVariety);\n                        react_toastify__WEBPACK_IMPORTED_MODULE_5__.toast.success(\"Updated New Mark Variety.\", {\n                            position: \"top-left\"\n                        });\n                    } else if (dataKey == \"brand\") {\n                        setRowData(json.brand);\n                        onNewDropdownData(dataKey, json.brand);\n                        react_toastify__WEBPACK_IMPORTED_MODULE_5__.toast.success(\"Updated New Brand.\", {\n                            position: \"top-left\"\n                        });\n                    } else if (dataKey == \"end_customer\") {\n                        setRowData(json.endCustomer);\n                        onNewDropdownData(dataKey, json.endCustomer);\n                        react_toastify__WEBPACK_IMPORTED_MODULE_5__.toast.success(\"Updated New End Customer.\", {\n                            position: \"top-left\"\n                        });\n                    } else if (dataKey == \"countryOfOrigin\") {\n                        setRowData(json.countryOfOrigin);\n                        onNewDropdownData(dataKey, json.countryOfOrigin);\n                        react_toastify__WEBPACK_IMPORTED_MODULE_5__.toast.success(\"Updated New Country of Origin.\", {\n                            position: \"top-left\"\n                        });\n                    } else if (dataKey == \"caliberSize\") {\n                        setRowData(json.caliberSize);\n                        onNewDropdownData(dataKey, json.caliberSize);\n                        react_toastify__WEBPACK_IMPORTED_MODULE_5__.toast.success(\"Updated New Caliber Size.\", {\n                            position: \"top-left\"\n                        });\n                    } else if (dataKey == \"variety\") {\n                        setRowData(json.variety);\n                        onNewDropdownData(dataKey, json.variety);\n                        react_toastify__WEBPACK_IMPORTED_MODULE_5__.toast.success(\"Updated New Variety.\", {\n                            position: \"top-left\"\n                        });\n                    } else if (dataKey == \"newOuterBoxType\") {\n                        setRowData(json.newOuterBoxType);\n                        onNewDropdownData(dataKey, json.newOuterBoxType);\n                        react_toastify__WEBPACK_IMPORTED_MODULE_5__.toast.success(\"Updated New Box Type.\", {\n                            position: \"top-left\"\n                        });\n                    }\n                    setDescription(\"\");\n                    setCode(\"\");\n                    setIsButtonDisabled(true);\n                    setIsUpdateMode(false);\n                    setAddNewValue(true);\n                //setIsDrawerOpen(false);\n                //alert(\"after new add\")\n                });\n            } catch (error) {\n                // toast.error(\"Failed to save reference code.\", {\n                //   position: \"top-left\",\n                // });\n                console.error(\"Failed to save reference code.\", error);\n            //setLoading(false);\n            }\n        } else {\n            // toast.error(\"Product code or description already exist.\", {\n            //   position: \"top-left\",\n            // });\n            console.error(\"Failed to save reference code.\");\n        }\n    };\n    const handleAdd = ()=>{\n        const checkDescription = rowData.find((desc)=>desc.label == description);\n        const checkProduct = rowData.find((product)=>product.code == code);\n        if (checkDescription) {\n            setisValidDescription(false);\n        } else {\n            setisValidDescription(true);\n        }\n        if (checkProduct) {\n            setisValidCode(false);\n        } else {\n            setisValidCode(true);\n        }\n        // console.log('checkDescription: ',checkDescription,'\\n checkProduct: ', checkProduct)\n        if (!checkDescription && !checkProduct) {\n            const saveData = {\n                description: description,\n                code: code,\n                is_new: true,\n                tableName: dataKey,\n                username: username,\n                useremail: useremail,\n                prophetId: prophetId\n            };\n            try {\n                fetch(\"\".concat(serverAddress, \"products/add-all-dropdown-value\"), {\n                    method: \"POST\",\n                    headers: {\n                        \"Content-Type\": \"application/json\",\n                        Authorization: \"Bearer \".concat(userData.token)\n                    },\n                    body: JSON.stringify(saveData)\n                }).then((res)=>{\n                    if (res.status === 200) {\n                        return res.json();\n                    } else {\n                        react_toastify__WEBPACK_IMPORTED_MODULE_5__.toast.error(\"Failed to save raw material.\");\n                    //setLoading(false);\n                    }\n                }).then((json)=>{\n                    // console.log(json);\n                    if (dataKey == \"masterProductCode\") {\n                        onNewDropdownData(dataKey, json.masterProductCode);\n                        setRowData(json.masterProductCode);\n                        react_toastify__WEBPACK_IMPORTED_MODULE_5__.toast.success(\"Added New Master Code.\", {\n                            position: \"top-left\"\n                        });\n                        setDescription(\"\");\n                        setCode(\"\");\n                        setIsButtonDisabled(true);\n                    } else if (dataKey == \"markVariety\") {\n                        setRowData(json.markVariety);\n                        onNewDropdownData(dataKey, json.markVariety);\n                        react_toastify__WEBPACK_IMPORTED_MODULE_5__.toast.success(\"Added New Mark Variety.\", {\n                            position: \"top-left\"\n                        });\n                        setDescription(\"\");\n                        setCode(\"\");\n                        setIsButtonDisabled(true);\n                    } else if (dataKey == \"brand\") {\n                        setRowData(json.brand);\n                        onNewDropdownData(dataKey, json.brand);\n                        react_toastify__WEBPACK_IMPORTED_MODULE_5__.toast.success(\"Added New Brand.\", {\n                            position: \"top-left\"\n                        });\n                        setDescription(\"\");\n                        setCode(\"\");\n                        setIsButtonDisabled(true);\n                    } else if (dataKey == \"end_customer\") {\n                        setRowData(json.endCustomer);\n                        onNewDropdownData(dataKey, json.endCustomer);\n                        react_toastify__WEBPACK_IMPORTED_MODULE_5__.toast.success(\"Added New End Customer.\", {\n                            position: \"top-left\"\n                        });\n                        setDescription(\"\");\n                        setCode(\"\");\n                        setIsButtonDisabled(true);\n                    } else if (dataKey == \"countryOfOrigin\") {\n                        setRowData(json.countryOfOrigin);\n                        onNewDropdownData(dataKey, json.countryOfOrigin);\n                        react_toastify__WEBPACK_IMPORTED_MODULE_5__.toast.success(\"Added New Country of Origin.\", {\n                            position: \"top-left\"\n                        });\n                        setDescription(\"\");\n                        setCode(\"\");\n                        setIsButtonDisabled(true);\n                    } else if (dataKey == \"caliberSize\") {\n                        setRowData(json.caliberSize);\n                        onNewDropdownData(dataKey, json.caliberSize);\n                        react_toastify__WEBPACK_IMPORTED_MODULE_5__.toast.success(\"Added New Caliber Size.\", {\n                            position: \"top-left\"\n                        });\n                        setDescription(\"\");\n                        setCode(\"\");\n                        setIsButtonDisabled(true);\n                    } else if (dataKey == \"variety\") {\n                        setRowData(json.variety);\n                        onNewDropdownData(dataKey, json.variety);\n                        react_toastify__WEBPACK_IMPORTED_MODULE_5__.toast.success(\"Added New Variety.\", {\n                            position: \"top-left\"\n                        });\n                        setDescription(\"\");\n                        setCode(\"\");\n                        setIsButtonDisabled(true);\n                    } else if (dataKey == \"newOuterBoxType\") {\n                        setRowData(json.newOuterBoxType);\n                        onNewDropdownData(dataKey, json.newOuterBoxType);\n                        react_toastify__WEBPACK_IMPORTED_MODULE_5__.toast.success(\"Added New Box Type.\", {\n                            position: \"top-left\"\n                        });\n                        setDescription(\"\");\n                        setCode(\"\");\n                        setIsButtonDisabled(true);\n                    }\n                    setAddNewValue(true);\n                });\n            } catch (error) {\n                console.error(\"Failed to save reference code.\", error);\n            }\n        } else {\n            console.error(\"Failed to save reference code.\");\n        }\n    };\n    const getRowStyle = (params)=>{\n        if (params.data.is_new === true) {\n            // Apply custom styling for rows where is_new is true\n            return {\n                color: \"red !important\"\n            }; // Example background color\n        }\n        return null;\n    };\n    return(// <div className={styles.root}>\n    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_toastify__WEBPACK_IMPORTED_MODULE_5__.ToastContainer, {\n                limit: 1\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\DrawerComponent.js\",\n                lineNumber: 467,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fluentui_react_components__WEBPACK_IMPORTED_MODULE_9__.Drawer, {\n                    type: type,\n                    separator: true,\n                    open: isDrawerOpen,\n                    position: \"end\",\n                    className: \"!bg-white !w-[450px] p-3 px-5 !shadow-lg !border-0\",\n                    onOpenChange: (_, param)=>{\n                        let { open } = param;\n                        return setIsDrawerOpen(open);\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fluentui_react_components__WEBPACK_IMPORTED_MODULE_9__.DrawerHeader, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fluentui_react_components__WEBPACK_IMPORTED_MODULE_9__.DrawerHeaderTitle, {\n                                action: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fluentui_react_components__WEBPACK_IMPORTED_MODULE_9__.Button, {\n                                    appearance: \"subtle\",\n                                    \"aria-label\": \"Close\",\n                                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fluentui_react_icons__WEBPACK_IMPORTED_MODULE_10__.Dismiss24Regular, {}, void 0, false, void 0, void 0),\n                                    onClick: ()=>setIsDrawerOpen(false)\n                                }, void 0, false, void 0, void 0),\n                                className: \"font-bold\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"font-bold\",\n                                    children: title\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\DrawerComponent.js\",\n                                    lineNumber: 489,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\DrawerComponent.js\",\n                                lineNumber: 478,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\DrawerComponent.js\",\n                            lineNumber: 477,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fluentui_react_components__WEBPACK_IMPORTED_MODULE_9__.DrawerBody, {\n                            className: \"!max-h-full !overflow-hidden\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"border rounded-md relative mt-3 mb-3 \",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"absolute -top-3 left-5 bg-white z-50 w-auto inline px-3\",\n                                                style: {\n                                                    opacity: !isUpdateMode ? 1 : 0,\n                                                    transform: !isUpdateMode ? \"scale(1)\" : \"scale(0.9)\",\n                                                    transition: \"opacity 0.1s ease, transform 0.1s ease\",\n                                                    pointerEvents: !isUpdateMode ? \"auto\" : \"none\"\n                                                },\n                                                children: legend\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\DrawerComponent.js\",\n                                                lineNumber: 496,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                onClick: ()=>{\n                                                    setIsUpdateMode(false);\n                                                    setIsButtonDisabled(true);\n                                                    setDescription(\"\");\n                                                    setCode(\"\");\n                                                    // setRemovedRows(\"\");\n                                                    setValue(\"\");\n                                                    setisValidCode(true);\n                                                    setisValidDescription(true);\n                                                // addRow();\n                                                // console.log(\"removedRows\",removedRows);\n                                                },\n                                                style: {\n                                                    opacity: isUpdateMode ? 1 : 0,\n                                                    transform: isUpdateMode ? \"scale(1)\" : \"scale(0.9)\",\n                                                    transition: \"opacity 0.1s ease, transform 0.1s ease\",\n                                                    pointerEvents: isUpdateMode ? \"auto\" : \"none\"\n                                                },\n                                                className: \"absolute flex items-center justify-center border rounded-md border-skin-primary -top-3 right-5 text-skin-primary bg-white z-50 cursor-pointer shadow-sm hover:bg-gray-50 transition-all duration-300 ease-in-out\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fortawesome_react_fontawesome__WEBPACK_IMPORTED_MODULE_2__.FontAwesomeIcon, {\n                                                        icon: _fortawesome_free_solid_svg_icons__WEBPACK_IMPORTED_MODULE_11__.faPlus,\n                                                        className: \"px-1 py-1 text-skin-primary cursor-pointer rotate-45 transition-all duration-300 ease-in-out transform\",\n                                                        title: \"Go back to add new \".concat(title)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\DrawerComponent.js\",\n                                                        lineNumber: 529,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"mr-[5px]\",\n                                                        children: \"Cancel\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\DrawerComponent.js\",\n                                                        lineNumber: 534,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\DrawerComponent.js\",\n                                                lineNumber: 508,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"hidden\",\n                                                name: \"type\",\n                                                value: \"product\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\DrawerComponent.js\",\n                                                lineNumber: 537,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex flex-col w-full p-4 pt-5 overflow-hidden\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        id: \"description\",\n                                                        name: \"description\",\n                                                        maxLength: 50,\n                                                        className: \"w-full py-1 px-2 2xl:px-3 2xl:py-3 border rounded-md\",\n                                                        placeholder: \"Description\",\n                                                        onChange: (e)=>{\n                                                            setDescription(e.target.value);\n                                                            setisValidDescription(true);\n                                                            setIsButtonDisabled(e.target.value === \"\" || code === \"\");\n                                                        },\n                                                        onBlur: (e)=>{\n                                                            const trimmedValue = trimInputText(e.target.value);\n                                                            setDescription(trimmedValue);\n                                                            setisValidDescription(true);\n                                                            setIsButtonDisabled(trimmedValue === \"\" || code === \"\");\n                                                        },\n                                                        // style={{ textTransform: \"capitalize\" }}\n                                                        value: description\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\DrawerComponent.js\",\n                                                        lineNumber: 539,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    !isValidDescription && // ?\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-red-500 text-xs mt-1 ml-1\",\n                                                        children: \"Description Exists Please Enter Different Description\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\DrawerComponent.js\",\n                                                        lineNumber: 562,\n                                                        columnNumber: 23\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex flex-row mt-2 transition-all duration-300 ease-in-out\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex flex-col w-full \",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                        id: \"addNew\",\n                                                                        name: \"add new\",\n                                                                        className: \"w-full py-1 px-2 2xl:px-3 2xl:py-3 border rounded-md \",\n                                                                        onChange: (e)=>{\n                                                                            // console.log(\"max_length\",min_length - 1, e.target.value.length);\n                                                                            // console.log(\"hi:\");\n                                                                            if (e.target.value.length <= max_length) {\n                                                                                setCode(e.target.value.toUpperCase());\n                                                                                setisValidCode(true);\n                                                                                setIsButtonDisabled(e.target.value === \"\" || description === \"\" || (typeof e.target.value === \"number\" ? e.target.value.toString().length < min_length : e.target.value.length < min_length));\n                                                                            }\n                                                                        },\n                                                                        onBlur: (e)=>{\n                                                                            const trimmedValue = trimInputText(e.target.value);\n                                                                            if (trimmedValue.length <= max_length) {\n                                                                                setCode(trimmedValue.toUpperCase());\n                                                                                setisValidCode(true);\n                                                                                setIsButtonDisabled(e.target.value === \"\" || description === \"\" || (typeof e.target.value === \"number\" ? e.target.value.toString().length < min_length : e.target.value.length < min_length));\n                                                                            } else {\n                                                                                setisValidCode(false);\n                                                                            }\n                                                                        },\n                                                                        placeholder: placeholderText,\n                                                                        // style={{ textTransform: \"uppercase\" }}\n                                                                        value: code\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\DrawerComponent.js\",\n                                                                        lineNumber: 574,\n                                                                        columnNumber: 23\n                                                                    }, undefined),\n                                                                    !isValidCode && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-red-500 text-xs mt-1 ml-1\",\n                                                                        children: \"Code Already Exists\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\DrawerComponent.js\",\n                                                                        lineNumber: 615,\n                                                                        columnNumber: 25\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\DrawerComponent.js\",\n                                                                lineNumber: 573,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                className: \"border border-skin-primary text-white bg-skin-primary py-1 px-5 ml-4 \".concat(!isValidCode ? \"mb-[18px]\" : \"\", ' w-[130px] font-medium rounded-md scale-x-100\"\\n                      }'),\n                                                                onClick: isUpdateMode ? handelupdate : handleAdd,\n                                                                disabled: isButtonDisabled,\n                                                                children: isUpdateMode ? \"Update\" : \"Add New\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\DrawerComponent.js\",\n                                                                lineNumber: 620,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\DrawerComponent.js\",\n                                                        lineNumber: 572,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\DrawerComponent.js\",\n                                                lineNumber: 538,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\DrawerComponent.js\",\n                                        lineNumber: 495,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-col\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"relative block w-full text-gray-400 mt-0 pt-0 mb-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"absolute px-4 py-1 pt-[0.4rem] 2xl:pt-1.5 text-black\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fortawesome_react_fontawesome__WEBPACK_IMPORTED_MODULE_2__.FontAwesomeIcon, {\n                                                            icon: _fortawesome_free_solid_svg_icons__WEBPACK_IMPORTED_MODULE_11__.faSearch,\n                                                            className: \"fw-bold\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\DrawerComponent.js\",\n                                                            lineNumber: 636,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\DrawerComponent.js\",\n                                                        lineNumber: 635,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"text\",\n                                                        id: \"filter-text-box\",\n                                                        placeholder: \"Search\",\n                                                        onInput: onFilterTextBoxChanged,\n                                                        className: \"block w-full px-4 pl-10 text-gray-500 placeholder-gray-400 searchbar border rounded-lg appearance-none form-input focus:outline-none\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\DrawerComponent.js\",\n                                                        lineNumber: 638,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\DrawerComponent.js\",\n                                                lineNumber: 634,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"!rounded-md px-5 border border-gray-300\",\n                                                style: {\n                                                    height: \"calc(100vh - 210px)\"\n                                                },\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ag_grid_react__WEBPACK_IMPORTED_MODULE_3__.AgGridReact, {\n                                                    rowData: rowData,\n                                                    ref: gridRef,\n                                                    columnDefs: columnDefs,\n                                                    defaultColDef: defaultColDef,\n                                                    getRowStyle: getRowStyle\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\DrawerComponent.js\",\n                                                    lineNumber: 650,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\DrawerComponent.js\",\n                                                lineNumber: 646,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\DrawerComponent.js\",\n                                        lineNumber: 633,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\DrawerComponent.js\",\n                                lineNumber: 494,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\DrawerComponent.js\",\n                            lineNumber: 493,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\DrawerComponent.js\",\n                    lineNumber: 469,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\DrawerComponent.js\",\n                lineNumber: 468,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true));\n};\n_s(DrawerComponent, \"zpbapB7wDtu66vJweoDKOcOYtUI=\");\n_c = DrawerComponent;\n/* harmony default export */ __webpack_exports__[\"default\"] = (DrawerComponent);\nvar _c;\n$RefreshReg$(_c, \"DrawerComponent\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevExports = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevExports) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports on update so we can compare the boundary\n                // signatures.\n                module.hot.dispose(function (data) {\n                    data.prevExports = currentExports;\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevExports !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevExports, currentExports)) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevExports !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/DrawerComponent.js\n"));

/***/ })

});