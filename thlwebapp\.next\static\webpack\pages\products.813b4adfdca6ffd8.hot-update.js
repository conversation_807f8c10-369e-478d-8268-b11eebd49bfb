"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/products",{

/***/ "./pages/products.js":
/*!***************************!*\
  !*** ./pages/products.js ***!
  \***************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __N_SSP: function() { return /* binding */ __N_SSP; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _components_Layout__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/Layout */ \"./components/Layout.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _fortawesome_react_fontawesome__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @fortawesome/react-fontawesome */ \"./node_modules/@fortawesome/react-fontawesome/index.es.js\");\n/* harmony import */ var _fortawesome_free_solid_svg_icons__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! @fortawesome/free-solid-svg-icons */ \"./node_modules/@fortawesome/free-solid-svg-icons/index.mjs\");\n/* harmony import */ var ag_grid_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ag-grid-react */ \"./node_modules/ag-grid-react/lib/main.js\");\n/* harmony import */ var ag_grid_react__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(ag_grid_react__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var ag_grid_community_styles_ag_grid_css__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ag-grid-community/styles//ag-grid.css */ \"./node_modules/ag-grid-community/styles/ag-grid.css\");\n/* harmony import */ var ag_grid_community_styles_ag_grid_css__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(ag_grid_community_styles_ag_grid_css__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var ag_grid_community_styles_ag_theme_alpine_css__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ag-grid-community/styles//ag-theme-alpine.css */ \"./node_modules/ag-grid-community/styles/ag-theme-alpine.css\");\n/* harmony import */ var ag_grid_community_styles_ag_theme_alpine_css__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(ag_grid_community_styles_ag_theme_alpine_css__WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_7__);\n/* harmony import */ var react_toastify__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! react-toastify */ \"./node_modules/react-toastify/dist/react-toastify.esm.mjs\");\n/* harmony import */ var react_toastify_dist_ReactToastify_css__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! react-toastify/dist/ReactToastify.css */ \"./node_modules/react-toastify/dist/ReactToastify.css\");\n/* harmony import */ var react_toastify_dist_ReactToastify_css__WEBPACK_IMPORTED_MODULE_9___default = /*#__PURE__*/__webpack_require__.n(react_toastify_dist_ReactToastify_css__WEBPACK_IMPORTED_MODULE_9__);\n/* harmony import */ var _utils_loaders_loadingContext__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/utils/loaders/loadingContext */ \"./utils/loaders/loadingContext.js\");\n/* harmony import */ var react_select__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! react-select */ \"./node_modules/react-select/dist/react-select.esm.js\");\n/* harmony import */ var _utils_rolePermissionsContext__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/utils/rolePermissionsContext */ \"./utils/rolePermissionsContext.js\");\n/* harmony import */ var _utils_renderer_productStatusRenderer__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/utils/renderer/productStatusRenderer */ \"./utils/renderer/productStatusRenderer.js\");\n/* harmony import */ var _utils_renderer_productActionRenderer__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/utils/renderer/productActionRenderer */ \"./utils/renderer/productActionRenderer.js\");\n/* harmony import */ var _services_apiConfig__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/services/apiConfig */ \"./services/apiConfig.js\");\n/* harmony import */ var js_cookie__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! js-cookie */ \"./node_modules/js-cookie/dist/js.cookie.mjs\");\n/* harmony import */ var _azure_msal_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @azure/msal-react */ \"./node_modules/@azure/msal-react/dist/index.js\");\n/* harmony import */ var _components_ProductDialog__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! @/components/ProductDialog */ \"./components/ProductDialog.js\");\n/* harmony import */ var _utils_secureStorage__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! @/utils/secureStorage */ \"./utils/secureStorage.js\");\n/* harmony import */ var _utils_extractCompanyFromEmail__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! @/utils/extractCompanyFromEmail */ \"./utils/extractCompanyFromEmail.js\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst customSelectStyles = {\n    // Default style\n    control: (base)=>({\n            ...base,\n            height: \"28px\",\n            minHeight: \"28px\"\n        }),\n    // Style when the condition is true\n    option: (base, param)=>{\n        let { data } = param;\n        return {\n            ...base,\n            color: data.is_new == true ? \"red\" : \"\"\n        };\n    },\n    valueContainer: (provided, state)=>({\n            ...provided,\n            height: \"26px\",\n            width: \"300px\",\n            padding: \"0 6px\"\n        }),\n    input: (provided, state)=>({\n            ...provided,\n            margin: \"0px\"\n        }),\n    indicatorSeparator: (state)=>({\n            display: \"none\"\n        }),\n    indicatorsContainer: (provided, state)=>({\n            ...provided,\n            height: \"28px\"\n        })\n};\nconst Products = (param)=>{\n    let { userData, PreviousPage, pageTypeId } = param;\n    _s();\n    console.log(\"userData, PreviousPage, pageTypeId\", userData, PreviousPage, pageTypeId);\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_7__.useRouter)();\n    const [pageSize, setPageSize] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(15);\n    const gridRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)();\n    const [isRMChecked, setIsRMChecked] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [isFGChecked, setIsFGChecked] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [isNVChecked, setIsNVChecked] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const { setIsLoading } = (0,_utils_loaders_loadingContext__WEBPACK_IMPORTED_MODULE_10__.useLoading)();\n    const [rowData, setRowData] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [company, setCompany] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"\");\n    const [blockScreen, setBlockScreen] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [typeId, setTypeId] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(pageTypeId);\n    const [isDialogOpen, setIsDialogOpen] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [isIssUser, setIsIssUser] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [isIssProcurementTeamUser, setIsIssProcurementTeamUser] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [isIssAdmin, setisIssAdmin] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [nvStatus, setNVStatus] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [statusOptions] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([\n        {\n            value: \"Draft\",\n            label: \"Draft\"\n        },\n        {\n            value: \"Pending Review\",\n            label: \"Pending Review\"\n        },\n        {\n            value: \"Rejected\",\n            label: \"Rejected\"\n        },\n        {\n            value: \"ISS to Setup\",\n            label: \"ISS to Setup\"\n        },\n        {\n            value: \"Setup Completed\",\n            label: \"Setup Completed\"\n        },\n        {\n            value: \"Cancelled\",\n            label: \"Cancelled\"\n        }\n    ]);\n    const [filteredRowData, setFilteredRowData] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [checkedValue, setCheckedValue] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [incompleteToast, setIncompleteToast] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [searchInput, setSearchInput] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"\");\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        // const userCompany = Cookies.get(\"company\");\n        const userCompany = (userData === null || userData === void 0 ? void 0 : userData.company) || (0,_utils_extractCompanyFromEmail__WEBPACK_IMPORTED_MODULE_19__.extractCompanyFromEmail)(userData === null || userData === void 0 ? void 0 : userData.email);\n        if (userCompany === \"issproduce\") {\n            setIsIssUser(true);\n        }\n        console.log(userData);\n        if (userData.department_id === 2) {\n            setIsIssProcurementTeamUser(true);\n            console.log(\"checking if it is an admin\", userData.role);\n            if (userData.role_id === 1) {\n                console.log(\"is admin\");\n                setisIssAdmin(true);\n            }\n        }\n        console.log(\"isIssProcurementTeamUser\", isIssProcurementTeamUser);\n    }, []);\n    const openDialog = ()=>{\n        setIsDialogOpen(true);\n    };\n    const closeDialog = ()=>{\n        setIsDialogOpen(false);\n    };\n    // const [isOpenOption, setIsOpenOption] = useState(false);\n    const [selectedRequestType, setSelectedRequestType] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(PreviousPage);\n    const handleTypeIdChange = (e)=>{\n        setSearchInput(\"\");\n        const filterTextBox = document.getElementById(\"filter-text-box\");\n        filterTextBox.value = \"\";\n        gridRef.current.api.setQuickFilter(\"\");\n        setNVStatus([]);\n        setTypeId(parseInt(e.target.value));\n        if (e.target.value === \"1\") {\n            setSelectedRequestType(\"rawMaterialRequest\");\n        } else if (e.target.value === \"3\") {\n            setSelectedRequestType(\"newVarietyRequest\");\n        } else if (e.target.value === \"4\") {\n            setSelectedRequestType(\"packagingform\");\n        }\n    };\n    // const closeOptionModal = () => {\n    //   setIsOpenOption(false);\n    // };\n    const handleRequestType = (type)=>{\n        setSelectedRequestType(type);\n    };\n    const handleFormType = ()=>{\n        if (selectedRequestType) {\n            localStorage.setItem(\"formType\", selectedRequestType);\n            console.log(selectedRequestType);\n            if (selectedRequestType === \"rawMaterialRequest\") {\n                router.push({\n                    pathname: \"/raw-material-request/add\"\n                });\n            // } else if (selectedRequestType == \"finishedProductRequest\") {\n            //   router.push({\n            //     pathname: `/finished-product-request/add` });\n            } else if (selectedRequestType == \"newVarietyRequest\") {\n                router.push({\n                    pathname: \"/variety/add\"\n                });\n            } else if (selectedRequestType == \"packagingform\") {\n                // router.push({ pathname: `/packaging/add` });\n                router.push({\n                    pathname: \"/packaging-form/add\"\n                }); //TODO\n            }\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        if (incompleteToast) {\n            react_toastify__WEBPACK_IMPORTED_MODULE_8__.toast.error(\"Kindly Submit the Request to Export it.\", {\n                position: \"top-right\",\n                autoClose: 1000,\n                hideProgressBar: false,\n                closeOnClick: true,\n                pauseOnHover: false,\n                draggable: true,\n                progress: undefined,\n                theme: \"light\"\n            });\n        }\n    }, [\n        incompleteToast\n    ]);\n    const [productsExtractData, setProductsExtractData] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([\n        [\n            \"Product Extract Data\"\n        ]\n    ]);\n    const [altfilExtractData, setAltfilExtractData] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([\n        [\n            \"Altfil Extract Data\"\n        ]\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        if (productsExtractData.length > 0) {\n            setCheckedValue(true);\n        } else {\n            setCheckedValue(false);\n        }\n    }, [\n        productsExtractData\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        if (typeof document !== \"undefined\") {\n            document.title = \"Products\";\n        }\n        setIsLoading(false);\n        getData().then((data)=>{\n            console.log(data);\n            const formattedData = data === null || data === void 0 ? void 0 : data.map((row)=>({\n                    id: row.id,\n                    code: row.code,\n                    action_id: row.action_id,\n                    request_no: row.request_no,\n                    type: row.product_type,\n                    product_type_name: row.product_type_label,\n                    product_type_id: row.product_type_id,\n                    reason: (row === null || row === void 0 ? void 0 : row.reason_description) ? row === null || row === void 0 ? void 0 : row.reason_description : \"Not Entered\",\n                    delivery_date: row.delivery_date ? new Date(row.delivery_date).toISOString().split(\"T\")[0] : \"Not Entered\",\n                    // delivery_date: new Date(row.delivery_date),\n                    product_code: row.master_product_code ? row.master_product_code : \"Not Entered\",\n                    product_description: row.product_description ? row.product_description : \"Not Entered\",\n                    originator: row.originator ? row.originator : row.originator_name,\n                    originator_email: row.originator_email,\n                    coo: row.coo ? row.coo : \"Not Entered\",\n                    status: row.status_label,\n                    master_product_code: row === null || row === void 0 ? void 0 : row.master_product_code,\n                    count_or_size: row === null || row === void 0 ? void 0 : row.count_or_size,\n                    units_in_outer: row === null || row === void 0 ? void 0 : row.units_in_outer,\n                    cases_per_pallet: row === null || row === void 0 ? void 0 : row.cases_per_pallet,\n                    outer_net_weight: row === null || row === void 0 ? void 0 : row.outer_net_weight,\n                    outer_gross_weight: row === null || row === void 0 ? void 0 : row.outer_gross_weight,\n                    sub_product_code: row === null || row === void 0 ? void 0 : row.sub_product_code,\n                    temperature_grade: row === null || row === void 0 ? void 0 : row.temperature_grade,\n                    intrastat_commodity_code: row === null || row === void 0 ? void 0 : row.intrastat_commodity_code,\n                    temperature_grade_name: row === null || row === void 0 ? void 0 : row.temperature_grade_name,\n                    intrastat_commodity_code_name: row === null || row === void 0 ? void 0 : row.intrastat_commodity_code_name,\n                    mark_variety_name: row === null || row === void 0 ? void 0 : row.mark_variety_name,\n                    intrastat_commodity_code_id: row === null || row === void 0 ? void 0 : row.intrastat_commodity_code_id,\n                    sort_group_id: row === null || row === void 0 ? void 0 : row.group_id,\n                    company: row === null || row === void 0 ? void 0 : row.company_name,\n                    temperature_grade_id: row === null || row === void 0 ? void 0 : row.temperature_grade_id,\n                    userText4: row === null || row === void 0 ? void 0 : row.userText4,\n                    userText5: row === null || row === void 0 ? void 0 : row.userText5,\n                    userText6: row === null || row === void 0 ? void 0 : row.userText6\n                }));\n            setCompany(formattedData[0].company);\n            setRowData(formattedData);\n            setFilteredRowData(formattedData);\n        }).catch((error)=>{\n            return error;\n        });\n    }, [\n        typeId\n    ]);\n    function getData() {\n        setRowData([]);\n        setFilteredRowData([]);\n        let serverAddress = _services_apiConfig__WEBPACK_IMPORTED_MODULE_14__.apiConfig.serverAddress;\n        const company_name = (userData === null || userData === void 0 ? void 0 : userData.company) || (0,_utils_extractCompanyFromEmail__WEBPACK_IMPORTED_MODULE_19__.extractCompanyFromEmail)(userData === null || userData === void 0 ? void 0 : userData.email);\n        const AdCompany = (userData === null || userData === void 0 ? void 0 : userData.companyName) || (userData === null || userData === void 0 ? void 0 : userData.ADCompanyName);\n        return fetch(\"\".concat(serverAddress, \"products/get-products/\").concat(company_name == \"dpsltd\" && AdCompany == \"DPS MS\" ? \"DPS MS\" : company_name, \"\\n      /\").concat(typeId), {\n            method: \"GET\",\n            headers: {\n                \"Content-Type\": \"application/json\"\n            },\n            credentials: \"include\"\n        }).then(async (res)=>{\n            if (res.status == 502) {\n                setBlockScreen(true);\n                return;\n            }\n            setBlockScreen(false);\n            if (res.status === 200) {\n                return res.json();\n            }\n            if (res.status === 400) {\n                react_toastify__WEBPACK_IMPORTED_MODULE_8__.toast.error(\"There was an error with your request. Please check your data and try again.\");\n            } else if (res.status === 401) {\n                react_toastify__WEBPACK_IMPORTED_MODULE_8__.toast.error(\"Your session has expired. Please log in again.\");\n                setTimeout(async ()=>{\n                    await (0,_utils_secureStorage__WEBPACK_IMPORTED_MODULE_18__.logout)();\n                    router.push(\"/login\");\n                }, 3000);\n            }\n            throw new Error(\"Failed to fetch data\");\n        }).catch((error)=>{\n            console.error(error);\n        });\n    }\n    const handlePageSizeChange = (event)=>{\n        const newPageSize = parseInt(event.target.value, 15);\n        setPageSize(newPageSize);\n        gridRef.current.api.paginationSetPageSize(newPageSize);\n    };\n    const handleStatusChange = (selectedOptions)=>{\n        setNVStatus(selectedOptions);\n        filterData(selectedOptions);\n    };\n    const filterData = (statuses)=>{\n        if (statuses.length == 0) {\n            setFilteredRowData(rowData);\n            return;\n        }\n        const filteredData = rowData.filter((row)=>statuses.some((status)=>status.value === row.status));\n        setFilteredRowData(filteredData.length > 0 ? filteredData : []);\n    };\n    // const clearFilters = () => {\n    //   setSelectedStatuses([]);\n    //   setFilteredRowData(rowData);\n    // };\n    const defaultColDef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useMemo)(()=>({\n            sortable: true,\n            filter: false,\n            resizable: true,\n            flex: 1,\n            suppressMenu: false\n        }));\n    const gridOptions = {\n        responsive: true\n    };\n    const columnDefs = [\n        {\n            headerName: \"Request No.\",\n            field: \"request_no\",\n            // cellRenderer: nameRenderer,\n            suppressMenu: true,\n            suppressSizeToFit: true,\n            suppressSizeToFit: false,\n            cellClass: \"ag-grid-checkbox-cell\",\n            flex: \"2%\",\n            filter: true\n        },\n        {\n            headerName: \"Reason\",\n            field: \"reason\",\n            flex: \"2%\",\n            cellRenderer: _utils_renderer_productStatusRenderer__WEBPACK_IMPORTED_MODULE_12__[\"default\"],\n            hide: typeId == 1 || 4 ? false : 0,\n            cellStyle: (params)=>{\n                if (params.value == \"Not Entered\") {\n                    return {\n                        color: \"#B31312\"\n                    };\n                }\n                return null;\n            }\n        },\n        {\n            headerName: \"Delivery Date\",\n            field: \"delivery_date\",\n            flex: \"2%\",\n            hide: typeId == 1 || 4 ? false : 0,\n            cellRenderer: (params)=>{\n                if (params.value === \"Not Entered\") {\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-[#B31312]\",\n                        children: \"Not Entered\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\products.js\",\n                        lineNumber: 384,\n                        columnNumber: 18\n                    }, undefined);\n                } else {\n                    // params.value\n                    // Format the date from yyyy-mm-dd to dd/mm/yyyy\n                    const dateParts = params.value.split(\"-\");\n                    const formattedDate = dateParts.reverse().join(\"/\");\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: formattedDate\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\products.js\",\n                        lineNumber: 391,\n                        columnNumber: 18\n                    }, undefined);\n                }\n            }\n        },\n        {\n            headerName: \"Product Code\",\n            field: \"product_code\",\n            flex: \"2%\",\n            // cellClass: (params) => {\n            //   return params.value === \"Not Entered\" ? 'not-entered' : '';\n            // }\n            cellStyle: (params)=>{\n                if (params.value == \"Not Entered\") {\n                    return {\n                        color: \"#B31312\"\n                    };\n                }\n                return null;\n            }\n        },\n        {\n            // headerName: \"Product description\",\n            // field: \"product_description\",\n            headerName: \"Product Description\",\n            field: \"product_description\",\n            // cellRenderer: statusRenderer,\n            flex: \"3%\",\n            cellStyle: (params)=>{\n                if (params.value == \"Not Entered\") {\n                    return {\n                        color: \"#B31312\"\n                    };\n                }\n                return null;\n            }\n        },\n        {\n            headerName: \"Originator\",\n            field: \"originator\",\n            headerName: \"Originator\",\n            field: \"originator\",\n            // cellRenderer: statusRenderer,\n            flex: \"2%\",\n            flex: \"2%\"\n        },\n        // {\n        //   headerName: \"COO\",\n        //   field: \"coo\",\n        //   headerName: \"COO\",\n        //   field: \"coo\",\n        //   // cellRenderer: statusRenderer,\n        //   flex: \"2%\",\n        //   cellStyle: (params) => {\n        //     if (params.value == \"Not Entered\") {\n        //       return { color: \"#B31312\" };\n        //     }\n        //     return null;\n        //   },\n        // },\n        {\n            headerName: \"Status\",\n            field: \"status\",\n            cellRenderer: _utils_renderer_productStatusRenderer__WEBPACK_IMPORTED_MODULE_12__[\"default\"],\n            cellStyle: ()=>({\n                    justifyContent: \"center\"\n                }),\n            flex: \"2%\",\n            hide: false\n        },\n        {\n            // field: \"Action(s)\",\n            field: typeId == 3 ? \"Action(s)\" : \"Action(s)\",\n            cellRenderer: (params)=>(0,_utils_renderer_productActionRenderer__WEBPACK_IMPORTED_MODULE_13__[\"default\"])(params, userData, userData.token, company, typeId, setIsLoading, isIssUser),\n            flex: \"2%\",\n            cellStyle: ()=>({}),\n            cellStyle: {\n                justifyContent: \"end\",\n                paddingRight: \"10px\"\n            },\n            sortable: false\n        },\n        {\n            field: \"role_num\",\n            hide: true\n        }\n    ];\n    const handleGridReady = (params)=>{\n        params.api.setColumnDefs(columnDefs);\n    };\n    const onFilterTextBoxChanged = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(()=>{\n        gridRef.current.api.setQuickFilter(document.getElementById(\"filter-text-box\").value);\n        setSearchInput(document.getElementById(\"filter-text-box\").value);\n    }, []);\n    const handleFilterProductType = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)((e)=>{\n        if (e.target.value == \"RM\") {\n            if (e.target.checked) {\n                setIsRMChecked(true);\n                setIsFGChecked(false);\n                setIsNVChecked(false);\n                gridRef.current.api.setQuickFilter(\"RM\");\n            } else {\n                setIsRMChecked(false);\n                gridRef.current.api.setQuickFilter(\"\");\n            }\n        } else if (e.target.value == \"FG\") {\n            if (e.target.checked) {\n                setIsFGChecked(true);\n                setIsRMChecked(false);\n                setIsNVChecked(false);\n                gridRef.current.api.setQuickFilter(\"FG\");\n            } else {\n                setIsFGChecked(false);\n                gridRef.current.api.setQuickFilter(\"\");\n            }\n        } else if (e.target.value == \"NV\") {\n            if (e.target.checked) {\n                setIsNVChecked(true);\n                setIsFGChecked(false);\n                setIsRMChecked(false);\n                gridRef.current.api.setQuickFilter(\"NV\");\n            } else {\n                setIsNVChecked(true);\n                gridRef.current.api.setQuickFilter(\"\");\n            }\n        } else {\n            setIsRMChecked(false);\n            setIsFGChecked(false);\n            setIsNVChecked(false);\n            gridRef.current.api.setQuickFilter(\"\");\n        }\n    }, []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_toastify__WEBPACK_IMPORTED_MODULE_8__.ToastContainer, {\n                limit: 1\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\products.js\",\n                lineNumber: 531,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Layout__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                userData: userData,\n                blockScreen: blockScreen,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mr-20 md:mr-12 lg:mr-14\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-row md:flex-col lg:flex-row justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"border border-light-gray rounded-md px-3 py-1\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"flex items-center gap-2 cursor-pointer\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                type: \"radio\",\n                                                                name: \"requestType\",\n                                                                value: \"1\",\n                                                                className: \"form-radio w-5 h-5 cursor-pointer\",\n                                                                checked: typeId === 1,\n                                                                onChange: handleTypeIdChange\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\products.js\",\n                                                                lineNumber: 539,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"labels\",\n                                                                children: \"Raw Materials\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\products.js\",\n                                                                lineNumber: 547,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\products.js\",\n                                                        lineNumber: 538,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\products.js\",\n                                                    lineNumber: 537,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"border border-light-gray rounded-md px-3 py-1\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"flex items-center gap-2 cursor-pointer\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                type: \"radio\",\n                                                                name: \"requestType\",\n                                                                value: \"3\",\n                                                                className: \"form-radio w-5 h-5 cursor-pointer\",\n                                                                checked: typeId === 3,\n                                                                onChange: handleTypeIdChange\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\products.js\",\n                                                                lineNumber: 563,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"labels\",\n                                                                children: \"New Variety\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\products.js\",\n                                                                lineNumber: 571,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\products.js\",\n                                                        lineNumber: 562,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\products.js\",\n                                                    lineNumber: 561,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                isIssUser && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"border border-light-gray rounded-md px-3 py-1\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"flex items-center gap-2 cursor-pointer\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                type: \"radio\",\n                                                                name: \"requestType\",\n                                                                value: \"4\",\n                                                                className: \"form-radio w-5 h-5 cursor-pointer\",\n                                                                checked: typeId === 4,\n                                                                onChange: handleTypeIdChange\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\products.js\",\n                                                                lineNumber: 577,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"labels\",\n                                                                children: \"Packaging\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\products.js\",\n                                                                lineNumber: 585,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\products.js\",\n                                                        lineNumber: 576,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\products.js\",\n                                                    lineNumber: 575,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\products.js\",\n                                            lineNumber: 536,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"labels\",\n                                            style: {\n                                                marginLeft: \"10px\"\n                                            },\n                                            children: typeId === 3 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_select__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                isMulti: true,\n                                                options: statusOptions,\n                                                onChange: handleStatusChange,\n                                                placeholder: \"Select Status...\",\n                                                className: \"basic-multi-select\",\n                                                classNamePrefix: \"select\",\n                                                value: nvStatus,\n                                                isSearchable: false,\n                                                styles: customSelectStyles\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\products.js\",\n                                                lineNumber: 592,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\products.js\",\n                                            lineNumber: 590,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center px-3 w-auto md:w-fit lg:w-auto md:py-2 lg:py-1 md:mb-3 lg:mb-0\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\products.js\",\n                                            lineNumber: 608,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\products.js\",\n                                    lineNumber: 535,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"relative block w-[47vh] text-gray-400 mt-0 pt-0\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"absolute px-4 py-1 pt-[0.4rem] 2xl:pt-1.5 text-black\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fortawesome_react_fontawesome__WEBPACK_IMPORTED_MODULE_3__.FontAwesomeIcon, {\n                                                        icon: _fortawesome_free_solid_svg_icons__WEBPACK_IMPORTED_MODULE_21__.faSearch,\n                                                        className: \"fw-bold\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\products.js\",\n                                                        lineNumber: 639,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\products.js\",\n                                                    lineNumber: 638,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"text\",\n                                                    id: \"filter-text-box\",\n                                                    placeholder: \"Search...\",\n                                                    onInput: onFilterTextBoxChanged,\n                                                    value: searchInput,\n                                                    className: \"block w-full px-4 pl-10 text-gray-500 placeholder-gray-400 searchbar border rounded-lg appearance-none form-input focus:outline-none\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\products.js\",\n                                                    lineNumber: 641,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\products.js\",\n                                            lineNumber: 637,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ProductDialog__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                            isOpen: openDialog,\n                                            onClose: closeDialog,\n                                            handleFormType: handleFormType,\n                                            selectedRequestType: selectedRequestType,\n                                            isIssUser: isIssUser,\n                                            isIssProcurmentUser: isIssProcurementTeamUser,\n                                            handleRequestType: handleRequestType,\n                                            admin: isIssAdmin\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\products.js\",\n                                            lineNumber: 660,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\products.js\",\n                                    lineNumber: 636,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\products.js\",\n                            lineNumber: 534,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"my-5\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative ag-theme-alpine !rounded-md\",\n                                style: {\n                                    height: \"calc(100vh - 151px)\"\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ag_grid_react__WEBPACK_IMPORTED_MODULE_4__.AgGridReact, {\n                                        rowData: filteredRowData,\n                                        ref: gridRef,\n                                        columnDefs: columnDefs,\n                                        defaultColDef: defaultColDef,\n                                        suppressRowClickSelection: true,\n                                        pagination: true,\n                                        paginationPageSize: pageSize,\n                                        onPageSizeChanged: handlePageSizeChange,\n                                        tooltipShowDelay: 0,\n                                        tooltipHideDelay: 1000,\n                                        onGridReady: handleGridReady,\n                                        gridOptions: gridOptions\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\products.js\",\n                                        lineNumber: 677,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-start mt-2 pagination-style\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            htmlFor: \"page-size-select pagination\",\n                                            className: \"inputs\",\n                                            children: [\n                                                \"Show\",\n                                                \" \",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                    id: \"page-size-select\",\n                                                    onChange: handlePageSizeChange,\n                                                    value: pageSize,\n                                                    className: \"focus:outline-none\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: 10,\n                                                            children: \"10\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\products.js\",\n                                                            lineNumber: 700,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: 15,\n                                                            children: \"15\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\products.js\",\n                                                            lineNumber: 701,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: 25,\n                                                            children: \"25\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\products.js\",\n                                                            lineNumber: 702,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: 50,\n                                                            children: \"50\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\products.js\",\n                                                            lineNumber: 703,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: 100,\n                                                            children: \"100\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\products.js\",\n                                                            lineNumber: 704,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\products.js\",\n                                                    lineNumber: 694,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                \" \",\n                                                \"Entries\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\products.js\",\n                                            lineNumber: 692,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\products.js\",\n                                        lineNumber: 691,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\products.js\",\n                                lineNumber: 673,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\products.js\",\n                            lineNumber: 672,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\products.js\",\n                    lineNumber: 533,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\products.js\",\n                lineNumber: 532,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true);\n};\n_s(Products, \"x02n8KTM2A42sUdrK7Zft5e7jZs=\", false, function() {\n    return [\n        next_router__WEBPACK_IMPORTED_MODULE_7__.useRouter,\n        _utils_loaders_loadingContext__WEBPACK_IMPORTED_MODULE_10__.useLoading\n    ];\n});\n_c = Products;\nvar __N_SSP = true;\n/* harmony default export */ __webpack_exports__[\"default\"] = (Products);\nvar _c;\n$RefreshReg$(_c, \"Products\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevExports = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevExports) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports on update so we can compare the boundary\n                // signatures.\n                module.hot.dispose(function (data) {\n                    data.prevExports = currentExports;\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevExports !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevExports, currentExports)) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevExports !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./pages/products.js\n"));

/***/ })

});