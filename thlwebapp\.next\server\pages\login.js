/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "pages/login";
exports.ids = ["pages/login"];
exports.modules = {

/***/ "./public/images/loginbanner.png":
/*!***************************************!*\
  !*** ./public/images/loginbanner.png ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ({\"src\":\"/_next/static/media/loginbanner.3045a28e.png\",\"height\":1080,\"width\":960,\"blurDataURL\":\"/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Floginbanner.3045a28e.png&w=7&q=70\",\"blurWidth\":7,\"blurHeight\":8});//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9wdWJsaWMvaW1hZ2VzL2xvZ2luYmFubmVyLnBuZyIsIm1hcHBpbmdzIjoiOzs7O0FBQUEsaUVBQWUsQ0FBQywyTUFBMk0iLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly90aGx3ZWJhcHAvLi9wdWJsaWMvaW1hZ2VzL2xvZ2luYmFubmVyLnBuZz82YjRhIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IHtcInNyY1wiOlwiL19uZXh0L3N0YXRpYy9tZWRpYS9sb2dpbmJhbm5lci4zMDQ1YTI4ZS5wbmdcIixcImhlaWdodFwiOjEwODAsXCJ3aWR0aFwiOjk2MCxcImJsdXJEYXRhVVJMXCI6XCIvX25leHQvaW1hZ2U/dXJsPSUyRl9uZXh0JTJGc3RhdGljJTJGbWVkaWElMkZsb2dpbmJhbm5lci4zMDQ1YTI4ZS5wbmcmdz03JnE9NzBcIixcImJsdXJXaWR0aFwiOjcsXCJibHVySGVpZ2h0XCI6OH07Il0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///./public/images/loginbanner.png\n");

/***/ }),

/***/ "./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2Flogin&preferredRegion=&absolutePagePath=.%2Fpages%5Clogin.js&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2Flogin&preferredRegion=&absolutePagePath=.%2Fpages%5Clogin.js&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   getServerSideProps: () => (/* binding */ getServerSideProps),\n/* harmony export */   getStaticPaths: () => (/* binding */ getStaticPaths),\n/* harmony export */   getStaticProps: () => (/* binding */ getStaticProps),\n/* harmony export */   reportWebVitals: () => (/* binding */ reportWebVitals),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   unstable_getServerProps: () => (/* binding */ unstable_getServerProps),\n/* harmony export */   unstable_getServerSideProps: () => (/* binding */ unstable_getServerSideProps),\n/* harmony export */   unstable_getStaticParams: () => (/* binding */ unstable_getStaticParams),\n/* harmony export */   unstable_getStaticPaths: () => (/* binding */ unstable_getStaticPaths),\n/* harmony export */   unstable_getStaticProps: () => (/* binding */ unstable_getStaticProps)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/pages/module.compiled */ \"./node_modules/next/dist/server/future/route-modules/pages/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/build/templates/helpers */ \"./node_modules/next/dist/build/templates/helpers.js\");\n/* harmony import */ var private_next_pages_document__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! private-next-pages/_document */ \"./pages/_document.js\");\n/* harmony import */ var private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! private-next-pages/_app */ \"./pages/_app.js\");\n/* harmony import */ var _pages_login_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./pages\\login.js */ \"./pages/login.js\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__, _pages_login_js__WEBPACK_IMPORTED_MODULE_5__]);\n([private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__, _pages_login_js__WEBPACK_IMPORTED_MODULE_5__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n// @ts-ignore this need to be imported from next/dist to be external\n\n\n\n// Import the app and document modules.\n// @ts-expect-error - replaced by webpack/turbopack loader\n\n// @ts-expect-error - replaced by webpack/turbopack loader\n\n// Import the userland code.\n// @ts-expect-error - replaced by webpack/turbopack loader\n\nconst PagesRouteModule = next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__.PagesRouteModule;\n// Re-export the component (should be the default export).\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_login_js__WEBPACK_IMPORTED_MODULE_5__, \"default\"));\n// Re-export methods.\nconst getStaticProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_login_js__WEBPACK_IMPORTED_MODULE_5__, \"getStaticProps\");\nconst getStaticPaths = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_login_js__WEBPACK_IMPORTED_MODULE_5__, \"getStaticPaths\");\nconst getServerSideProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_login_js__WEBPACK_IMPORTED_MODULE_5__, \"getServerSideProps\");\nconst config = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_login_js__WEBPACK_IMPORTED_MODULE_5__, \"config\");\nconst reportWebVitals = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_login_js__WEBPACK_IMPORTED_MODULE_5__, \"reportWebVitals\");\n// Re-export legacy methods.\nconst unstable_getStaticProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_login_js__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getStaticProps\");\nconst unstable_getStaticPaths = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_login_js__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getStaticPaths\");\nconst unstable_getStaticParams = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_login_js__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getStaticParams\");\nconst unstable_getServerProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_login_js__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getServerProps\");\nconst unstable_getServerSideProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_login_js__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getServerSideProps\");\n// Create and export the route module that will be consumed.\nconst routeModule = new PagesRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.PAGES,\n        page: \"/login\",\n        pathname: \"/login\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\"\n    },\n    components: {\n        App: private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n        Document: private_next_pages_document__WEBPACK_IMPORTED_MODULE_3__[\"default\"]\n    },\n    userland: _pages_login_js__WEBPACK_IMPORTED_MODULE_5__\n});\n\n//# sourceMappingURL=pages.js.map\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2Flogin&preferredRegion=&absolutePagePath=.%2Fpages%5Clogin.js&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!\n");

/***/ }),

/***/ "./components/LoginBanner.js":
/*!***********************************!*\
  !*** ./components/LoginBanner.js ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _public_images_loginbanner_png__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../public/images/loginbanner.png */ \"./public/images/loginbanner.png\");\n\n\nconst LoginBanner = ()=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex w-1/2 flex-col pl-[88px] pt-[80px] justify-center bg-cover relative pb-20 h-[100vh]\",\n        style: {\n            backgroundImage: `url(${_public_images_loginbanner_png__WEBPACK_IMPORTED_MODULE_1__[\"default\"].src})`\n        }\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\LoginBanner.js\",\n        lineNumber: 4,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (LoginBanner);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9jb21wb25lbnRzL0xvZ2luQmFubmVyLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7O0FBQXFEO0FBQ3JELE1BQU1DLGNBQWM7SUFDbEIscUJBQ0UsOERBQUNDO1FBQ0NDLFdBQVU7UUFDVkMsT0FBTztZQUNMQyxpQkFBaUIsQ0FBQyxJQUFJLEVBQUVMLDBFQUFTLENBQUMsQ0FBQyxDQUFDO1FBQ3RDOzs7Ozs7QUFJTjtBQUVBLGlFQUFlQyxXQUFXQSxFQUFDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vdGhsd2ViYXBwLy4vY29tcG9uZW50cy9Mb2dpbkJhbm5lci5qcz84NzhmIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBMb2dpbiBmcm9tIFwiLi4vcHVibGljL2ltYWdlcy9sb2dpbmJhbm5lci5wbmdcIjtcclxuY29uc3QgTG9naW5CYW5uZXIgPSAoKSA9PiB7XHJcbiAgcmV0dXJuIChcclxuICAgIDxkaXZcclxuICAgICAgY2xhc3NOYW1lPVwiZmxleCB3LTEvMiBmbGV4LWNvbCBwbC1bODhweF0gcHQtWzgwcHhdIGp1c3RpZnktY2VudGVyIGJnLWNvdmVyIHJlbGF0aXZlIHBiLTIwIGgtWzEwMHZoXVwiXHJcbiAgICAgIHN0eWxlPXt7XHJcbiAgICAgICAgYmFja2dyb3VuZEltYWdlOiBgdXJsKCR7TG9naW4uc3JjfSlgLFxyXG4gICAgICB9fVxyXG4gICAgPiBcclxuICAgIDwvZGl2PlxyXG4gICk7XHJcbn07XHJcblxyXG5leHBvcnQgZGVmYXVsdCBMb2dpbkJhbm5lcjtcclxuIl0sIm5hbWVzIjpbIkxvZ2luIiwiTG9naW5CYW5uZXIiLCJkaXYiLCJjbGFzc05hbWUiLCJzdHlsZSIsImJhY2tncm91bmRJbWFnZSIsInNyYyJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///./components/LoginBanner.js\n");

/***/ }),

/***/ "./components/LoginSectionSecure.js":
/*!******************************************!*\
  !*** ./components/LoginSectionSecure.js ***!
  \******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/image */ \"./node_modules/next/image.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_image__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _azure_msal_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @azure/msal-react */ \"@azure/msal-react\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _utils_loaders_loadingContext__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/utils/loaders/loadingContext */ \"./utils/loaders/loadingContext.js\");\n/* harmony import */ var react_toastify__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! react-toastify */ \"react-toastify?9094\");\n/* harmony import */ var react_toastify_dist_ReactToastify_css__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! react-toastify/dist/ReactToastify.css */ \"./node_modules/react-toastify/dist/ReactToastify.css\");\n/* harmony import */ var react_toastify_dist_ReactToastify_css__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(react_toastify_dist_ReactToastify_css__WEBPACK_IMPORTED_MODULE_7__);\n/* harmony import */ var _utils_auth_useSecureAuth__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/utils/auth/useSecureAuth */ \"./utils/auth/useSecureAuth.js\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_azure_msal_react__WEBPACK_IMPORTED_MODULE_3__, react_toastify__WEBPACK_IMPORTED_MODULE_6__, _utils_auth_useSecureAuth__WEBPACK_IMPORTED_MODULE_8__]);\n([_azure_msal_react__WEBPACK_IMPORTED_MODULE_3__, react_toastify__WEBPACK_IMPORTED_MODULE_6__, _utils_auth_useSecureAuth__WEBPACK_IMPORTED_MODULE_8__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\n\n\n\n\n\nconst LoginSectionSecure = ()=>{\n    const { accounts } = (0,_azure_msal_react__WEBPACK_IMPORTED_MODULE_3__.useMsal)();\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_4__.useRouter)();\n    const { redirect } = router.query;\n    const { setIsLoading } = (0,_utils_loaders_loadingContext__WEBPACK_IMPORTED_MODULE_5__.useLoading)();\n    const [isMounted, setIsMounted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const { user, isAuthenticated, isLoading: authLoading, secureLogin, secureLoginExistingAccount, secureLogout } = (0,_utils_auth_useSecureAuth__WEBPACK_IMPORTED_MODULE_8__.useSecureAuth)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        setIsMounted(true);\n    }, []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (isAuthenticated && user && isMounted) {\n            const targetUrl = redirect || \"/suppliers\";\n            router.replace(targetUrl);\n        }\n    }, [\n        isAuthenticated,\n        user,\n        redirect,\n        router,\n        isMounted\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (isMounted) {\n            setIsLoading(authLoading);\n        }\n    }, [\n        authLoading,\n        setIsLoading,\n        isMounted\n    ]);\n    const signInHandler = async ()=>{\n        await secureLogin(redirect);\n    };\n    const useThisAccountHandler = async ()=>{\n        await secureLoginExistingAccount(redirect);\n    };\n    const signOutHandler = async ()=>{\n        await secureLogout();\n    };\n    if (!isMounted) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex justify-center items-center\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                    className: \"text-2xl font-bold text-center mb-6 text-gray-800\",\n                    children: \"Welcome to THL Portal\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\LoginSectionSecure.js\",\n                    lineNumber: 58,\n                    columnNumber: 11\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-gray-600\",\n                        children: \"Loading...\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\LoginSectionSecure.js\",\n                        lineNumber: 62,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\LoginSectionSecure.js\",\n                    lineNumber: 61,\n                    columnNumber: 11\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\LoginSectionSecure.js\",\n            lineNumber: 57,\n            columnNumber: 7\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex justify-center items-center\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white rounded-lg shadow-lg p-8 w-96\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-center mb-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_2___default()), {\n                            src: \"/images/terradace-logo.png\",\n                            alt: \"Company Logo\",\n                            width: 160,\n                            height: 50\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\LoginSectionSecure.js\",\n                            lineNumber: 72,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\LoginSectionSecure.js\",\n                        lineNumber: 71,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-2xl font-bold text-center mb-6 text-gray-800\",\n                        children: \"Welcome to THL Portal\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\LoginSectionSecure.js\",\n                        lineNumber: 75,\n                        columnNumber: 9\n                    }, undefined),\n                    accounts?.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"border rounded-lg p-4 bg-gray-50\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                src: \"/images/user-account.png\",\n                                                alt: \"User\",\n                                                width: 40,\n                                                height: 40\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\LoginSectionSecure.js\",\n                                                lineNumber: 83,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"font-medium text-gray-800\",\n                                                        children: accounts[0]?.name\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\LoginSectionSecure.js\",\n                                                        lineNumber: 85,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-600\",\n                                                        children: accounts[0]?.username\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\LoginSectionSecure.js\",\n                                                        lineNumber: 88,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\LoginSectionSecure.js\",\n                                                lineNumber: 84,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\LoginSectionSecure.js\",\n                                        lineNumber: 82,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mt-4 space-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: useThisAccountHandler,\n                                                disabled: authLoading,\n                                                className: \"w-full bg-blue-600 hover:bg-blue-700 disabled:bg-blue-400 text-white font-medium py-2 px-4 rounded-lg transition duration-200\",\n                                                children: authLoading ? \"Signing in...\" : \"Continue as \" + accounts[0]?.name\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\LoginSectionSecure.js\",\n                                                lineNumber: 95,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: signOutHandler,\n                                                disabled: authLoading,\n                                                className: \"w-full bg-gray-600 hover:bg-gray-700 disabled:bg-gray-400 text-white font-medium py-2 px-4 rounded-lg transition duration-200\",\n                                                children: \"Use different account\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\LoginSectionSecure.js\",\n                                                lineNumber: 103,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\LoginSectionSecure.js\",\n                                        lineNumber: 94,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\LoginSectionSecure.js\",\n                                lineNumber: 81,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-gray-500\",\n                                    children: \"or\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\LoginSectionSecure.js\",\n                                    lineNumber: 114,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\LoginSectionSecure.js\",\n                                lineNumber: 113,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\LoginSectionSecure.js\",\n                        lineNumber: 80,\n                        columnNumber: 11\n                    }, undefined) : null,\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: signInHandler,\n                        disabled: authLoading,\n                        className: \"w-full bg-white border border-gray-300 hover:bg-gray-50 disabled:bg-gray-100 text-gray-700 font-medium py-3 px-4 rounded-lg transition duration-200 flex items-center justify-center space-x-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                src: \"/images/microsoft-icon.png\",\n                                alt: \"Microsoft\",\n                                width: 20,\n                                height: 20\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\LoginSectionSecure.js\",\n                                lineNumber: 124,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: authLoading ? \"Signing in...\" : \"Sign in with Microsoft\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\LoginSectionSecure.js\",\n                                lineNumber: 125,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\LoginSectionSecure.js\",\n                        lineNumber: 119,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\LoginSectionSecure.js\",\n                lineNumber: 70,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_toastify__WEBPACK_IMPORTED_MODULE_6__.ToastContainer, {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\LoginSectionSecure.js\",\n                lineNumber: 131,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\LoginSectionSecure.js\",\n        lineNumber: 69,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (LoginSectionSecure);\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/LoginSectionSecure.js\n");

/***/ }),

/***/ "./pages/_app.js":
/*!***********************!*\
  !*** ./pages/_app.js ***!
  \***********************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ App)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _styles_globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/styles/globals.css */ \"./styles/globals.css\");\n/* harmony import */ var _styles_globals_css__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_styles_globals_css__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _utils_auth_msalProvider__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/utils/auth/msalProvider */ \"./utils/auth/msalProvider.jsx\");\n/* harmony import */ var _utils_loaders_loadingContext__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/utils/loaders/loadingContext */ \"./utils/loaders/loadingContext.js\");\n/* harmony import */ var _utils_loaders_overlaySpinner__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/utils/loaders/overlaySpinner */ \"./utils/loaders/overlaySpinner.js\");\n/* harmony import */ var _utils_secureThemeContext__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/utils/secureThemeContext */ \"./utils/secureThemeContext.js\");\n/* harmony import */ var _utils_rolePermissionsContext__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/utils/rolePermissionsContext */ \"./utils/rolePermissionsContext.js\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_utils_auth_msalProvider__WEBPACK_IMPORTED_MODULE_3__]);\n_utils_auth_msalProvider__WEBPACK_IMPORTED_MODULE_3__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\n\n\n\n\n\nfunction App({ Component, pageProps }) {\n    const [isOpen, setIsOpen] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(true);\n    const isOnline = useNetwork();\n    function useNetwork() {\n        if (false) {}\n    }\n    ;\n    const closeModal = ()=>{\n        setIsOpen(false);\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        if (isOnline) {\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                classNam: \"no-connection\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        children: \"No Internet Connection\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\_app.js\",\n                        lineNumber: 36,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"noConnectionAlertBox\", {\n                        isOpen: isOpen,\n                        closeModal: closeModal\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\_app.js\",\n                        lineNumber: 37,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\_app.js\",\n                lineNumber: 35,\n                columnNumber: 7\n            }, this);\n        }\n    }, []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_utils_auth_msalProvider__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_utils_secureThemeContext__WEBPACK_IMPORTED_MODULE_6__.SecureThemeProvider, {\n            initialTheme: pageProps.userData?.theme,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_utils_rolePermissionsContext__WEBPACK_IMPORTED_MODULE_7__.PermissionsProvider, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_utils_loaders_loadingContext__WEBPACK_IMPORTED_MODULE_4__.LoadingProvider, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_utils_loaders_overlaySpinner__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {}, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\_app.js\",\n                            lineNumber: 47,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Component, {\n                            ...pageProps\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\_app.js\",\n                            lineNumber: 48,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\_app.js\",\n                    lineNumber: 46,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\_app.js\",\n                lineNumber: 45,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\_app.js\",\n            lineNumber: 44,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\_app.js\",\n        lineNumber: 43,\n        columnNumber: 5\n    }, this);\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./pages/_app.js\n");

/***/ }),

/***/ "./pages/_document.js":
/*!****************************!*\
  !*** ./pages/_document.js ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Document)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_document__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/document */ \"./node_modules/next/document.js\");\n/* harmony import */ var next_document__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_document__WEBPACK_IMPORTED_MODULE_1__);\n\n\nfunction Document() {\n    return(// <Html lang=\"en\" className=\"dark\">\n    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_document__WEBPACK_IMPORTED_MODULE_1__.Html, {\n        lang: \"en\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_document__WEBPACK_IMPORTED_MODULE_1__.Head, {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\_document.js\",\n                lineNumber: 7,\n                columnNumber: 8\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_document__WEBPACK_IMPORTED_MODULE_1__.Main, {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\_document.js\",\n                        lineNumber: 9,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_document__WEBPACK_IMPORTED_MODULE_1__.NextScript, {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\_document.js\",\n                        lineNumber: 10,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\_document.js\",\n                lineNumber: 8,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\_document.js\",\n        lineNumber: 6,\n        columnNumber: 3\n    }, this));\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9wYWdlcy9fZG9jdW1lbnQuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7O0FBQTZEO0FBRTlDLFNBQVNJO0lBQ3RCLE9BQ0Usb0NBQW9DO2tCQUN0Qyw4REFBQ0osK0NBQUlBO1FBQUNLLE1BQUs7OzBCQUNOLDhEQUFDSiwrQ0FBSUE7Ozs7OzBCQUNOLDhEQUFDSzs7a0NBQ0MsOERBQUNKLCtDQUFJQTs7Ozs7a0NBQ0wsOERBQUNDLHFEQUFVQTs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFJbkIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly90aGx3ZWJhcHAvLi9wYWdlcy9fZG9jdW1lbnQuanM/NTM4YiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBIdG1sLCBIZWFkLCBNYWluLCBOZXh0U2NyaXB0IH0gZnJvbSBcIm5leHQvZG9jdW1lbnRcIjtcclxuXHJcbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIERvY3VtZW50KCkge1xyXG4gIHJldHVybiAoXHJcbiAgICAvLyA8SHRtbCBsYW5nPVwiZW5cIiBjbGFzc05hbWU9XCJkYXJrXCI+XHJcbiAgPEh0bWwgbGFuZz1cImVuXCI+XHJcbiAgICAgICA8SGVhZCAvPlxyXG4gICAgICA8Ym9keT5cclxuICAgICAgICA8TWFpbiAvPlxyXG4gICAgICAgIDxOZXh0U2NyaXB0IC8+XHJcbiAgICAgIDwvYm9keT5cclxuICAgIDwvSHRtbD5cclxuICApO1xyXG59XHJcbiJdLCJuYW1lcyI6WyJIdG1sIiwiSGVhZCIsIk1haW4iLCJOZXh0U2NyaXB0IiwiRG9jdW1lbnQiLCJsYW5nIiwiYm9keSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///./pages/_document.js\n");

/***/ }),

/***/ "./pages/login.js":
/*!************************!*\
  !*** ./pages/login.js ***!
  \************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Login)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_LoginBanner__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/LoginBanner */ \"./components/LoginBanner.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _components_LoginSectionSecure__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/LoginSectionSecure */ \"./components/LoginSectionSecure.js\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_components_LoginSectionSecure__WEBPACK_IMPORTED_MODULE_4__]);\n_components_LoginSectionSecure__WEBPACK_IMPORTED_MODULE_4__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\n\n\nfunction Login() {\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_3__.useRouter)();\n    const [isMounted, setIsMounted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        setIsMounted(true);\n        // Clear any old authentication data\n        if (false) {}\n    }, []);\n    // Check if user is already authenticated\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const checkAuth = async ()=>{\n            if (!isMounted) return;\n            try {\n                const apiBase = \"http://localhost:8081\" || 0;\n                const response = await fetch(`${apiBase}/api/auth/me`, {\n                    method: \"GET\",\n                    credentials: \"include\"\n                });\n                if (response.ok) {\n                    // User is already authenticated, redirect\n                    const { redirect } = router.query;\n                    router.replace(redirect || \"/suppliers\");\n                }\n            } catch (error) {\n            // User not authenticated, stay on login page\n            }\n        };\n        checkAuth();\n    }, [\n        isMounted,\n        router\n    ]);\n    if (!isMounted) {\n        return null; // Prevent hydration mismatch\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex flex-col lg:flex-row min-h-screen\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_LoginBanner__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\login.js\",\n                lineNumber: 52,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 flex items-center justify-center p-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_LoginSectionSecure__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\login.js\",\n                    lineNumber: 54,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\login.js\",\n                lineNumber: 53,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\login.js\",\n        lineNumber: 51,\n        columnNumber: 5\n    }, this);\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./pages/login.js\n");

/***/ }),

/***/ "./utils/auth/authConfig.js":
/*!**********************************!*\
  !*** ./utils/auth/authConfig.js ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   BASE_URL: () => (/* binding */ BASE_URL),\n/* harmony export */   loginRequest: () => (/* binding */ loginRequest),\n/* harmony export */   msalConfig: () => (/* binding */ msalConfig)\n/* harmony export */ });\nconst BASE_URL = `${\"http://localhost:3000\"}/login`;\nconst msalConfig = {\n    auth: {\n        clientId: \"bafc3c7b-820b-4d2d-9a96-9162c4b2c78a\",\n        authority: `https://login.microsoftonline.com/${\"6d90d24f-9602-49e8-8903-eb86dce9656a\"}`,\n        redirectUri: \"/\"\n    },\n    cache: {\n        cacheLocation: \"sessionStorage\",\n        storeAuthStateInCookie: false\n    }\n};\nconst loginRequest = {\n    scopes: [\n        \"user.read\"\n    ]\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi91dGlscy9hdXRoL2F1dGhDb25maWcuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQU8sTUFBTUEsV0FBVyxDQUFDLEVBQUVDLHVCQUFnQyxDQUFDLE1BQU0sQ0FBQyxDQUFDO0FBRTdELE1BQU1HLGFBQWE7SUFDeEJDLE1BQU07UUFDSkMsVUFBVUwsc0NBQWlDO1FBQzNDTyxXQUFXLENBQUMsa0NBQWtDLEVBQUVQLHNDQUFpQyxDQUFDLENBQUM7UUFDbkZTLGFBQWE7SUFDZjtJQUNBQyxPQUFPO1FBQ0xDLGVBQWU7UUFDZkMsd0JBQXdCO0lBQzFCO0FBQ0YsRUFBRTtBQUVLLE1BQU1DLGVBQWU7SUFDMUJDLFFBQVE7UUFBQztLQUFZO0FBQ3ZCLEVBQUUiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly90aGx3ZWJhcHAvLi91dGlscy9hdXRoL2F1dGhDb25maWcuanM/YWM5ZCJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgY29uc3QgQkFTRV9VUkwgPSBgJHtwcm9jZXNzLmVudi5ORVhUX1BVQkxJQ19CQVNFX1VSTH0vbG9naW5gO1xyXG5cclxuZXhwb3J0IGNvbnN0IG1zYWxDb25maWcgPSB7XHJcbiAgYXV0aDoge1xyXG4gICAgY2xpZW50SWQ6IHByb2Nlc3MuZW52Lk5FWFRfUFVCTElDX0NMSUVOVF9JRCxcclxuICAgIGF1dGhvcml0eTogYGh0dHBzOi8vbG9naW4ubWljcm9zb2Z0b25saW5lLmNvbS8ke3Byb2Nlc3MuZW52Lk5FWFRfUFVCTElDX1RFTkFOVF9JRH1gLFxyXG4gICAgcmVkaXJlY3RVcmk6IFwiL1wiLFxyXG4gIH0sXHJcbiAgY2FjaGU6IHtcclxuICAgIGNhY2hlTG9jYXRpb246IFwic2Vzc2lvblN0b3JhZ2VcIiwgLy8gQ2hhbmdlZCBmcm9tIGxvY2FsU3RvcmFnZVxyXG4gICAgc3RvcmVBdXRoU3RhdGVJbkNvb2tpZTogZmFsc2UsXHJcbiAgfSxcclxufTtcclxuXHJcbmV4cG9ydCBjb25zdCBsb2dpblJlcXVlc3QgPSB7XHJcbiAgc2NvcGVzOiBbXCJ1c2VyLnJlYWRcIl0sXHJcbn07XHJcbiJdLCJuYW1lcyI6WyJCQVNFX1VSTCIsInByb2Nlc3MiLCJlbnYiLCJORVhUX1BVQkxJQ19CQVNFX1VSTCIsIm1zYWxDb25maWciLCJhdXRoIiwiY2xpZW50SWQiLCJORVhUX1BVQkxJQ19DTElFTlRfSUQiLCJhdXRob3JpdHkiLCJORVhUX1BVQkxJQ19URU5BTlRfSUQiLCJyZWRpcmVjdFVyaSIsImNhY2hlIiwiY2FjaGVMb2NhdGlvbiIsInN0b3JlQXV0aFN0YXRlSW5Db29raWUiLCJsb2dpblJlcXVlc3QiLCJzY29wZXMiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///./utils/auth/authConfig.js\n");

/***/ }),

/***/ "./utils/auth/msalProvider.jsx":
/*!*************************************!*\
  !*** ./utils/auth/msalProvider.jsx ***!
  \*************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _azure_msal_browser__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @azure/msal-browser */ \"@azure/msal-browser\");\n/* harmony import */ var _azure_msal_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @azure/msal-react */ \"@azure/msal-react\");\n/* harmony import */ var _authConfig__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./authConfig */ \"./utils/auth/authConfig.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_4__);\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_azure_msal_browser__WEBPACK_IMPORTED_MODULE_1__, _azure_msal_react__WEBPACK_IMPORTED_MODULE_2__]);\n([_azure_msal_browser__WEBPACK_IMPORTED_MODULE_1__, _azure_msal_react__WEBPACK_IMPORTED_MODULE_2__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n// components/MsalProvider.tsx\n\n\n\n\n\nconst MsalAuthProvider = ({ children })=>{\n    const msalInstance = new _azure_msal_browser__WEBPACK_IMPORTED_MODULE_1__.PublicClientApplication(_authConfig__WEBPACK_IMPORTED_MODULE_3__.msalConfig);\n    const handlePopup = (event)=>{\n        if (event instanceof Event && event.isTrusted) {\n            msalInstance.handlePopupPromise().catch((error)=>{\n                console.error(\"Error handling popup:\", error);\n            });\n        }\n    };\n    react__WEBPACK_IMPORTED_MODULE_4___default().useEffect(()=>{\n        window.addEventListener(_azure_msal_browser__WEBPACK_IMPORTED_MODULE_1__.EventType.LOGIN_SUCCESS, handlePopup);\n        window.addEventListener(_azure_msal_browser__WEBPACK_IMPORTED_MODULE_1__.EventType.ACQUIRE_TOKEN_SUCCESS, handlePopup);\n        return ()=>{\n            window.removeEventListener(_azure_msal_browser__WEBPACK_IMPORTED_MODULE_1__.EventType.LOGIN_SUCCESS, handlePopup);\n            window.removeEventListener(_azure_msal_browser__WEBPACK_IMPORTED_MODULE_1__.EventType.ACQUIRE_TOKEN_SUCCESS, handlePopup);\n        };\n    }, []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_azure_msal_react__WEBPACK_IMPORTED_MODULE_2__.MsalProvider, {\n        instance: msalInstance,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\utils\\\\auth\\\\msalProvider.jsx\",\n        lineNumber: 27,\n        columnNumber: 10\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (MsalAuthProvider);\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./utils/auth/msalProvider.jsx\n");

/***/ }),

/***/ "./utils/auth/useSecureAuth.js":
/*!*************************************!*\
  !*** ./utils/auth/useSecureAuth.js ***!
  \*************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useSecureAuth: () => (/* binding */ useSecureAuth)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _azure_msal_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @azure/msal-react */ \"@azure/msal-react\");\n/* harmony import */ var _utils_secureStorage__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/utils/secureStorage */ \"./utils/secureStorage.js\");\n/* harmony import */ var react_toastify__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react-toastify */ \"react-toastify?9094\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_azure_msal_react__WEBPACK_IMPORTED_MODULE_2__, react_toastify__WEBPACK_IMPORTED_MODULE_4__]);\n([_azure_msal_react__WEBPACK_IMPORTED_MODULE_2__, react_toastify__WEBPACK_IMPORTED_MODULE_4__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\n\nconst useSecureAuth = ()=>{\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const [isAuthenticated, setIsAuthenticated] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(true);\n    const { instance } = (0,_azure_msal_react__WEBPACK_IMPORTED_MODULE_2__.useMsal)();\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_1__.useRouter)();\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        checkAuthStatus();\n    }, []);\n    const checkAuthStatus = async ()=>{\n        try {\n            const currentUser = await (0,_utils_secureStorage__WEBPACK_IMPORTED_MODULE_3__.getCurrentUser)();\n            if (currentUser) {\n                setUser(currentUser);\n                setIsAuthenticated(true);\n            }\n        } catch (error) {\n            console.error(\"Auth check failed:\", error);\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const secureLogin = async (redirect)=>{\n        setIsLoading(true);\n        try {\n            const requestedScope = `api://${\"bafc3c7b-820b-4d2d-9a96-9162c4b2c78a\"}/access_as_user`;\n            const request = {\n                prompt: \"select_account\",\n                scopes: [\n                    requestedScope\n                ]\n            };\n            const loginObj = await instance.loginPopup(request);\n            instance.setActiveAccount(loginObj.account);\n            // Acquire a separate Graph token for company details\n            const graphToken = await instance.acquireTokenSilent({\n                scopes: [\n                    \"User.Read\"\n                ],\n                account: loginObj.account\n            });\n            // Send both tokens to backend - the backend handles all session management\n            const result = await (0,_utils_secureStorage__WEBPACK_IMPORTED_MODULE_3__.login)(loginObj.accessToken, {\n                graphToken: graphToken.accessToken\n            });\n            if (!result.success) {\n                instance.clearCache();\n                throw new Error(result.error || \"Login failed\");\n            }\n            setUser(result.user);\n            setIsAuthenticated(true);\n            // Log login event - simplified to use session data\n            await logLoginEvent(loginObj.account, result.user);\n            instance.clearCache();\n            if (redirect) {\n                router.replace(redirect);\n            } else {\n                router.replace(\"/suppliers\");\n            }\n            react_toastify__WEBPACK_IMPORTED_MODULE_4__.toast.success(\"Login successful!\", {\n                position: \"top-right\"\n            });\n        } catch (error) {\n            console.error(\"Secure login failed:\", error);\n            react_toastify__WEBPACK_IMPORTED_MODULE_4__.toast.error(error.message || \"Login failed\", {\n                position: \"top-right\"\n            });\n            setIsAuthenticated(false);\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const secureLoginExistingAccount = async (redirect)=>{\n        setIsLoading(true);\n        try {\n            const activeAccount = instance.getActiveAccount();\n            if (!activeAccount) {\n                throw new Error(\"No active account found!\");\n            }\n            const loginObj = await instance.acquireTokenSilent({\n                scopes: [\n                    `api://${\"bafc3c7b-820b-4d2d-9a96-9162c4b2c78a\"}/access_as_user`\n                ],\n                account: activeAccount,\n                forceRefresh: true\n            });\n            // Acquire Graph token\n            const graphToken = await instance.acquireTokenSilent({\n                scopes: [\n                    \"User.Read\"\n                ],\n                account: activeAccount\n            });\n            const result = await (0,_utils_secureStorage__WEBPACK_IMPORTED_MODULE_3__.login)(loginObj.accessToken, {\n                graphToken: graphToken.accessToken\n            });\n            if (!result.success) {\n                throw new Error(result.error || \"Login failed\");\n            }\n            setUser(result.user);\n            setIsAuthenticated(true);\n            await logLoginEvent(activeAccount, result.user);\n            if (redirect) {\n                router.push(redirect);\n            } else {\n                router.push(\"/suppliers\");\n            }\n        } catch (error) {\n            console.error(\"Secure login failed:\", error);\n            react_toastify__WEBPACK_IMPORTED_MODULE_4__.toast.error(error.message || \"Login failed\", {\n                position: \"top-right\"\n            });\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    // Simplified login logging - no localStorage needed\n    const logLoginEvent = async (account, userData)=>{\n        try {\n            const apiBase = \"http://localhost:8081\" || 0;\n            await fetch(`${apiBase}/api/logs/logLogin`, {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                credentials: \"include\",\n                body: JSON.stringify({\n                    userData: account,\n                    company: userData?.ADCompanyName || userData?.companyName\n                })\n            });\n        } catch (error) {\n            console.error(\"Login logging failed:\", error);\n        }\n    };\n    const secureLogout = async ()=>{\n        try {\n            await (0,_utils_secureStorage__WEBPACK_IMPORTED_MODULE_3__.logout)();\n            setUser(null);\n            setIsAuthenticated(false);\n            instance.clearCache();\n            instance.logoutPopup();\n            router.push(\"/login\");\n        } catch (error) {\n            console.error(\"Logout failed:\", error);\n            instance.clearCache();\n            (0,_utils_secureStorage__WEBPACK_IMPORTED_MODULE_3__.clearClientStorage)();\n            setUser(null);\n            setIsAuthenticated(false);\n            instance.logoutPopup();\n        }\n    };\n    return {\n        user,\n        isAuthenticated,\n        isLoading,\n        secureLogin,\n        secureLoginExistingAccount,\n        secureLogout,\n        checkAuthStatus\n    };\n};\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./utils/auth/useSecureAuth.js\n");

/***/ }),

/***/ "./utils/loaders/loadingContext.js":
/*!*****************************************!*\
  !*** ./utils/loaders/loadingContext.js ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   LoadingProvider: () => (/* binding */ LoadingProvider),\n/* harmony export */   useLoading: () => (/* binding */ useLoading)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n// context/LoadingContext.js\n\n\nconst LoadingContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)();\nconst LoadingProvider = ({ children })=>{\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(LoadingContext.Provider, {\n        value: {\n            isLoading,\n            setIsLoading\n        },\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\utils\\\\loaders\\\\loadingContext.js\",\n        lineNumber: 10,\n        columnNumber: 5\n    }, undefined);\n};\nconst useLoading = ()=>{\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(LoadingContext);\n    if (!context) {\n        throw new Error(\"useLoading must be used within a LoadingProvider\");\n    }\n    return context;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi91dGlscy9sb2FkZXJzL2xvYWRpbmdDb250ZXh0LmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7OztBQUFBLDRCQUE0Qjs7QUFDdUM7QUFFbkUsTUFBTUksK0JBQWlCSCxvREFBYUE7QUFFN0IsTUFBTUksa0JBQWtCLENBQUMsRUFBRUMsUUFBUSxFQUFFO0lBQzFDLE1BQU0sQ0FBQ0MsV0FBV0MsYUFBYSxHQUFHTCwrQ0FBUUEsQ0FBQztJQUUzQyxxQkFDRSw4REFBQ0MsZUFBZUssUUFBUTtRQUFDQyxPQUFPO1lBQUVIO1lBQVdDO1FBQWE7a0JBQ3ZERjs7Ozs7O0FBR1AsRUFBRTtBQUVLLE1BQU1LLGFBQWE7SUFDeEIsTUFBTUMsVUFBVVYsaURBQVVBLENBQUNFO0lBQzNCLElBQUksQ0FBQ1EsU0FBUztRQUNaLE1BQU0sSUFBSUMsTUFBTTtJQUNsQjtJQUNBLE9BQU9EO0FBQ1QsRUFBRSIsInNvdXJjZXMiOlsid2VicGFjazovL3RobHdlYmFwcC8uL3V0aWxzL2xvYWRlcnMvbG9hZGluZ0NvbnRleHQuanM/OTA5ZiJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBjb250ZXh0L0xvYWRpbmdDb250ZXh0LmpzXHJcbmltcG9ydCBSZWFjdCwgeyBjcmVhdGVDb250ZXh0LCB1c2VDb250ZXh0LCB1c2VTdGF0ZSB9IGZyb20gXCJyZWFjdFwiO1xyXG5cclxuY29uc3QgTG9hZGluZ0NvbnRleHQgPSBjcmVhdGVDb250ZXh0KCk7XHJcblxyXG5leHBvcnQgY29uc3QgTG9hZGluZ1Byb3ZpZGVyID0gKHsgY2hpbGRyZW4gfSkgPT4ge1xyXG4gIGNvbnN0IFtpc0xvYWRpbmcsIHNldElzTG9hZGluZ10gPSB1c2VTdGF0ZShmYWxzZSk7XHJcblxyXG4gIHJldHVybiAoXHJcbiAgICA8TG9hZGluZ0NvbnRleHQuUHJvdmlkZXIgdmFsdWU9e3sgaXNMb2FkaW5nLCBzZXRJc0xvYWRpbmcgfX0+XHJcbiAgICAgIHtjaGlsZHJlbn1cclxuICAgIDwvTG9hZGluZ0NvbnRleHQuUHJvdmlkZXI+XHJcbiAgKTtcclxufTtcclxuXHJcbmV4cG9ydCBjb25zdCB1c2VMb2FkaW5nID0gKCkgPT4ge1xyXG4gIGNvbnN0IGNvbnRleHQgPSB1c2VDb250ZXh0KExvYWRpbmdDb250ZXh0KTtcclxuICBpZiAoIWNvbnRleHQpIHtcclxuICAgIHRocm93IG5ldyBFcnJvcihcInVzZUxvYWRpbmcgbXVzdCBiZSB1c2VkIHdpdGhpbiBhIExvYWRpbmdQcm92aWRlclwiKTtcclxuICB9XHJcbiAgcmV0dXJuIGNvbnRleHQ7XHJcbn07XHJcbiJdLCJuYW1lcyI6WyJSZWFjdCIsImNyZWF0ZUNvbnRleHQiLCJ1c2VDb250ZXh0IiwidXNlU3RhdGUiLCJMb2FkaW5nQ29udGV4dCIsIkxvYWRpbmdQcm92aWRlciIsImNoaWxkcmVuIiwiaXNMb2FkaW5nIiwic2V0SXNMb2FkaW5nIiwiUHJvdmlkZXIiLCJ2YWx1ZSIsInVzZUxvYWRpbmciLCJjb250ZXh0IiwiRXJyb3IiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///./utils/loaders/loadingContext.js\n");

/***/ }),

/***/ "./utils/loaders/overlaySpinner.js":
/*!*****************************************!*\
  !*** ./utils/loaders/overlaySpinner.js ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react_loader_spinner__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-loader-spinner */ \"react-loader-spinner\");\n/* harmony import */ var react_loader_spinner__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react_loader_spinner__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _loadingContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./loadingContext */ \"./utils/loaders/loadingContext.js\");\n// components/OverlaySpinner.js\n\n\n\n\nconst OverlaySpinner = ()=>{\n    const { isLoading } = (0,_loadingContext__WEBPACK_IMPORTED_MODULE_3__.useLoading)();\n    return isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        style: {\n            display: \"flex\",\n            alignItems: \"center\",\n            justifyContent: \"center\",\n            height: \"100vh\",\n            width: \"100vw\",\n            position: \"fixed\",\n            backgroundColor: \"white\",\n            zIndex: 999999\n        },\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_loader_spinner__WEBPACK_IMPORTED_MODULE_2__.ThreeCircles, {\n            color: \"#002D73\",\n            height: 50,\n            width: 50,\n            visible: isLoading,\n            ariaLabel: \"oval-loading\",\n            secondaryColor: \"#0066FF\",\n            strokeWidth: 2,\n            strokeWidthSecondary: 2\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\utils\\\\loaders\\\\overlaySpinner.js\",\n            lineNumber: 21,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\utils\\\\loaders\\\\overlaySpinner.js\",\n        lineNumber: 9,\n        columnNumber: 5\n    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {}, void 0, false);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (OverlaySpinner);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./utils/loaders/overlaySpinner.js\n");

/***/ }),

/***/ "./utils/rolePermissionsContext.js":
/*!*****************************************!*\
  !*** ./utils/rolePermissionsContext.js ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   PermissionsProvider: () => (/* binding */ PermissionsProvider),\n/* harmony export */   usePermissions: () => (/* binding */ usePermissions)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n\n\nconst PermissionsContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)();\nconst PermissionsProvider = ({ children })=>{\n    const [permissions, setPermissions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const updatePermissions = (newPermissions)=>{\n        setPermissions(newPermissions);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(PermissionsContext.Provider, {\n        value: {\n            permissions,\n            updatePermissions\n        },\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\utils\\\\rolePermissionsContext.js\",\n        lineNumber: 13,\n        columnNumber: 5\n    }, undefined);\n};\nconst usePermissions = ()=>{\n    return (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(PermissionsContext);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi91dGlscy9yb2xlUGVybWlzc2lvbnNDb250ZXh0LmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7QUFBNEQ7QUFFNUQsTUFBTUcsbUNBQXFCSCxvREFBYUE7QUFFakMsTUFBTUksc0JBQXNCLENBQUMsRUFBRUMsUUFBUSxFQUFFO0lBQzlDLE1BQU0sQ0FBQ0MsYUFBYUMsZUFBZSxHQUFHTCwrQ0FBUUEsQ0FBQyxDQUFDO0lBRWhELE1BQU1NLG9CQUFvQixDQUFDQztRQUN6QkYsZUFBZUU7SUFDakI7SUFFQSxxQkFDRSw4REFBQ04sbUJBQW1CTyxRQUFRO1FBQUNDLE9BQU87WUFBRUw7WUFBYUU7UUFBa0I7a0JBQ2xFSDs7Ozs7O0FBR1AsRUFBRTtBQUVLLE1BQU1PLGlCQUFpQjtJQUM1QixPQUFPWCxpREFBVUEsQ0FBQ0U7QUFDcEIsRUFBRSIsInNvdXJjZXMiOlsid2VicGFjazovL3RobHdlYmFwcC8uL3V0aWxzL3JvbGVQZXJtaXNzaW9uc0NvbnRleHQuanM/ZjdhYiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBjcmVhdGVDb250ZXh0LCB1c2VDb250ZXh0LCB1c2VTdGF0ZSB9IGZyb20gJ3JlYWN0JztcclxuXHJcbmNvbnN0IFBlcm1pc3Npb25zQ29udGV4dCA9IGNyZWF0ZUNvbnRleHQoKTtcclxuXHJcbmV4cG9ydCBjb25zdCBQZXJtaXNzaW9uc1Byb3ZpZGVyID0gKHsgY2hpbGRyZW4gfSkgPT4ge1xyXG4gIGNvbnN0IFtwZXJtaXNzaW9ucywgc2V0UGVybWlzc2lvbnNdID0gdXNlU3RhdGUoe30pO1xyXG5cclxuICBjb25zdCB1cGRhdGVQZXJtaXNzaW9ucyA9IChuZXdQZXJtaXNzaW9ucykgPT4ge1xyXG4gICAgc2V0UGVybWlzc2lvbnMobmV3UGVybWlzc2lvbnMpO1xyXG4gIH07XHJcblxyXG4gIHJldHVybiAoXHJcbiAgICA8UGVybWlzc2lvbnNDb250ZXh0LlByb3ZpZGVyIHZhbHVlPXt7IHBlcm1pc3Npb25zLCB1cGRhdGVQZXJtaXNzaW9ucyB9fT5cclxuICAgICAge2NoaWxkcmVufVxyXG4gICAgPC9QZXJtaXNzaW9uc0NvbnRleHQuUHJvdmlkZXI+XHJcbiAgKTtcclxufTtcclxuXHJcbmV4cG9ydCBjb25zdCB1c2VQZXJtaXNzaW9ucyA9ICgpID0+IHtcclxuICByZXR1cm4gdXNlQ29udGV4dChQZXJtaXNzaW9uc0NvbnRleHQpO1xyXG59O1xyXG4iXSwibmFtZXMiOlsiY3JlYXRlQ29udGV4dCIsInVzZUNvbnRleHQiLCJ1c2VTdGF0ZSIsIlBlcm1pc3Npb25zQ29udGV4dCIsIlBlcm1pc3Npb25zUHJvdmlkZXIiLCJjaGlsZHJlbiIsInBlcm1pc3Npb25zIiwic2V0UGVybWlzc2lvbnMiLCJ1cGRhdGVQZXJtaXNzaW9ucyIsIm5ld1Blcm1pc3Npb25zIiwiUHJvdmlkZXIiLCJ2YWx1ZSIsInVzZVBlcm1pc3Npb25zIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///./utils/rolePermissionsContext.js\n");

/***/ }),

/***/ "./utils/secureStorage.js":
/*!********************************!*\
  !*** ./utils/secureStorage.js ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   clearClientStorage: () => (/* binding */ clearClientStorage),\n/* harmony export */   getCurrentUser: () => (/* binding */ getCurrentUser),\n/* harmony export */   isAuthenticated: () => (/* binding */ isAuthenticated),\n/* harmony export */   login: () => (/* binding */ login),\n/* harmony export */   logout: () => (/* binding */ logout),\n/* harmony export */   refreshSession: () => (/* binding */ refreshSession)\n/* harmony export */ });\n// Simple module with functions - no classes\nconst apiBase = \"http://localhost:8081\" || 0;\n// Get current user from server session\nconst getCurrentUser = async ()=>{\n    try {\n        const response = await fetch(`${apiBase}/api/auth/me`, {\n            credentials: \"include\",\n            headers: {\n                \"Content-Type\": \"application/json\"\n            }\n        });\n        if (response.ok) {\n            const data = await response.json();\n            return data.user;\n        }\n        return null;\n    } catch (error) {\n        console.error(\"Failed to get current user:\", error);\n        return null;\n    }\n};\n// Login with MSAL token\nconst login = async (token, additionalData = {})=>{\n    try {\n        const response = await fetch(`${apiBase}/api/auth/login`, {\n            method: \"POST\",\n            headers: {\n                \"Authorization\": `Bearer ${token}`,\n                \"Content-Type\": \"application/json\"\n            },\n            credentials: \"include\",\n            body: JSON.stringify(additionalData)\n        });\n        if (response.ok) {\n            const data = await response.json();\n            return {\n                success: true,\n                user: data.user\n            };\n        } else {\n            const error = await response.json();\n            return {\n                success: false,\n                error: error.message\n            };\n        }\n    } catch (error) {\n        console.error(\"Login failed:\", error);\n        return {\n            success: false,\n            error: error.message\n        };\n    }\n};\n// Logout\nconst logout = async ()=>{\n    try {\n        await fetch(`${apiBase}/api/auth/logout`, {\n            method: \"POST\",\n            credentials: \"include\"\n        });\n        // Clear client-side data\n        clearClientStorage();\n        return true;\n    } catch (error) {\n        console.error(\"Logout failed:\", error);\n        return false;\n    }\n};\n// Clear all client-side storage\nconst clearClientStorage = ()=>{\n    const keysToRemove = [\n        \"superUser\",\n        \"company\",\n        \"id\",\n        \"name\",\n        \"role\",\n        \"email\",\n        \"allowedSections\"\n    ];\n    keysToRemove.forEach((key)=>localStorage.removeItem(key));\n};\n// Check if user is authenticated\nconst isAuthenticated = async ()=>{\n    const user = await getCurrentUser();\n    return !!user;\n};\n// Refresh session (extend expiry)\nconst refreshSession = async ()=>{\n    try {\n        const response = await fetch(`${apiBase}/api/auth/refresh`, {\n            method: \"POST\",\n            credentials: \"include\"\n        });\n        return response.ok;\n    } catch (error) {\n        console.error(\"Session refresh failed:\", error);\n        return false;\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./utils/secureStorage.js\n");

/***/ }),

/***/ "./utils/secureThemeContext.js":
/*!*************************************!*\
  !*** ./utils/secureThemeContext.js ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SecureThemeProvider: () => (/* binding */ SecureThemeProvider),\n/* harmony export */   useSecureTheme: () => (/* binding */ useSecureTheme)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n\n\nconst SecureThemeContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)();\nconst SecureThemeProvider = ({ children, initialTheme = \"#022D71\" })=>{\n    const [themeColor, setThemeColor] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(initialTheme);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    // Initialize theme from session data\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const initializeTheme = async ()=>{\n            try {\n                const apiBase = \"http://localhost:8081\" || 0;\n                // Get theme from session via API call\n                const response = await fetch(`${apiBase}/api/auth/me`, {\n                    method: \"GET\",\n                    credentials: \"include\"\n                });\n                if (response.ok) {\n                    const { user } = await response.json();\n                    if (user?.theme) {\n                        setThemeColor(user.theme);\n                    }\n                }\n            } catch (error) {\n                console.error(\"Error loading theme:\", error);\n            } finally{\n                setIsLoading(false);\n            }\n        };\n        initializeTheme();\n    }, []);\n    // Apply theme to CSS variables\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (false) {}\n    }, [\n        themeColor\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SecureThemeContext.Provider, {\n        value: {\n            themeColor,\n            setThemeColor,\n            isLoading\n        },\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\utils\\\\secureThemeContext.js\",\n        lineNumber: 75,\n        columnNumber: 5\n    }, undefined);\n};\nconst useSecureTheme = ()=>{\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(SecureThemeContext);\n    if (!context) {\n        throw new Error(\"useSecureTheme must be used within a SecureThemeProvider\");\n    }\n    return context;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./utils/secureThemeContext.js\n");

/***/ }),

/***/ "./styles/globals.css":
/*!****************************!*\
  !*** ./styles/globals.css ***!
  \****************************/
/***/ (() => {



/***/ }),

/***/ "next/dist/compiled/next-server/pages.runtime.dev.js":
/*!**********************************************************************!*\
  !*** external "next/dist/compiled/next-server/pages.runtime.dev.js" ***!
  \**********************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/pages.runtime.dev.js");

/***/ }),

/***/ "react":
/*!************************!*\
  !*** external "react" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("react");

/***/ }),

/***/ "react-dom":
/*!****************************!*\
  !*** external "react-dom" ***!
  \****************************/
/***/ ((module) => {

"use strict";
module.exports = require("react-dom");

/***/ }),

/***/ "react-loader-spinner":
/*!***************************************!*\
  !*** external "react-loader-spinner" ***!
  \***************************************/
/***/ ((module) => {

"use strict";
module.exports = require("react-loader-spinner");

/***/ }),

/***/ "react/jsx-dev-runtime":
/*!****************************************!*\
  !*** external "react/jsx-dev-runtime" ***!
  \****************************************/
/***/ ((module) => {

"use strict";
module.exports = require("react/jsx-dev-runtime");

/***/ }),

/***/ "@azure/msal-browser":
/*!**************************************!*\
  !*** external "@azure/msal-browser" ***!
  \**************************************/
/***/ ((module) => {

"use strict";
module.exports = import("@azure/msal-browser");;

/***/ }),

/***/ "@azure/msal-react":
/*!************************************!*\
  !*** external "@azure/msal-react" ***!
  \************************************/
/***/ ((module) => {

"use strict";
module.exports = import("@azure/msal-react");;

/***/ }),

/***/ "react-toastify?9094":
/*!*********************************!*\
  !*** external "react-toastify" ***!
  \*********************************/
/***/ ((module) => {

"use strict";
module.exports = import("react-toastify");;

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc","vendor-chunks/react-toastify"], () => (__webpack_exec__("./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2Flogin&preferredRegion=&absolutePagePath=.%2Fpages%5Clogin.js&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!")));
module.exports = __webpack_exports__;

})();