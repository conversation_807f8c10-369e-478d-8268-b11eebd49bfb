import React, { useState, useEffect, useRef } from "react";
import FilterMenu from "../common/FilterMenu";
import {
  Popover,
  PopoverSurface,
  PopoverTrigger,
  Tooltip,
} from "@fluentui/react-components";
import Select from "react-select";
import { DateRange } from "react-date-range";
import "react-date-range/dist/styles.css";
import "react-date-range/dist/theme/default.css";
import BottomFilter from "./BottomFilter";
import Cookies from "js-cookie";
import { getSLData } from "@/utils/service-level/utils/getSLData";
import { useServiceCustomers } from "@/utils/serviceCustomerContext";
import moment from "moment";
import { toast, ToastContainer } from "react-toastify";
//
import ProductDropDown from "../common/ProductDDCompoent";
import { logout } from "@/utils/secureStorage";

const Filter = ({
  initalDate,
  endDate,
  productList,
  customerList,
  customerSLData,
  selectedCustomer,
  setSelectedCustomer,
  checkedStates,
  setCheckedStates,
  setCustomerSLData,
  setInitialLoading,
  initialLoading,
  selectedProducts,
  setSelectedProducts,
  seeAll,
  setCustomerList,
  setSeeAll,
  slFilters,
  recordsCount,
  searchBoxContent,
  setSearchBoxContent,
  masterProducts,
  setSelectedMasterProductCode,
  selectedMasterProductCode,
  setRecordsCount,
  selectedRows,
  handleBulkUpdate,
  setNoDataExists,
  setShowLoadingMessage,
  setAllReasonsSubreasons,
  allReasonsSubreasons,
  setSelectedReasons,
  setSelectedSubReasons,
  selectedServiceCustomer,
  setSelectedServiceCustomer,
  userData,
}) => {
  const minDate = process.env.NEXT_PUBLIC_MIN_DATE;
  let ADCompanyName = userData?.companyName || userData?.ADCompanyName;
  const [startDateRange, setStartDateRange] = useState(moment(initalDate));
  const { serviceCustomers,updateServiceCustomersList } = useServiceCustomers();
  const [endDateRange, setEndDateRange] = useState(moment(endDate));
  const [currentProducts, setCurrentProducts] = useState([]);
  const [orderType, setOrderType] = useState(3);

  useEffect(() => {
    if (productList?.length > 0) {
      const uniqueNewProducts = productList.filter(
        (product, index, self) =>
          index === self.findIndex((p) => p.value === product.value)
      );
      setCurrentProducts(uniqueNewProducts);
    }
  }, [productList]);

  useEffect(() => {
    let newProducts = [];
    if (customerSLData) {
      newProducts = Object.keys(customerSLData)
        .map((key) => {
          const isSelected =
            selectedProducts.length === 0 ||
            (!!selectedProducts &&
              selectedProducts.some(
                (product) =>
                  product.PRODUCT_DESCRIPTION ===
                    customerSLData[key].PRODUCT_DESCRIPTION &&
                  product.ALTFILID === customerSLData[key].ALTFILID
              ));

          if (isSelected) {
            return {
              label: customerSLData[key].PRODUCT_DESCRIPTION,
              value: customerSLData[key].ALTFILID,
            };
          }

          return null; // return null if not selected
        })
        .filter(Boolean); // remove any null entries
    }

    const uniqueProducts = [];
    const seenValues = new Set();

    newProducts.forEach((product) => {
      if (!seenValues.has(product.value)) {
        seenValues.add(product.value);
        uniqueProducts.push(product);
      }
    });
    setCurrentProducts(uniqueProducts);
  }, [customerSLData]);

  const saveSLFilterHandler = () => {
    const slChoices = {
      start_date: moment(startDateRange, "DD-MM-YYYY").format("MM-DD-YYYY"),
      end_date: moment(endDateRange, "DD-MM-YYYY").format("MM-DD-YYYY"),
      cust_code: selectedCustomer.value,
      columns: checkedStates,
      orderTypeId: orderType,
      masterProductCode: selectedMasterProductCode,
      selectedProducts: selectedProducts.map((product) => ({
        productDescription: product.label,
        altFillId: product.value,
      })),
    };
    Cookies.set("slFilters", JSON.stringify(slChoices));
    toast.success("Filters Saved", {
      theme: "colored",
      autoClose: 3000,
    });
  };
  const [checkedValues, setCheckedValues] = useState({
    columns: ["depotdate", , "customer", "salesorder", "product"],
  });

  useEffect(() => {
    if (slFilters && slFilters.columns) {
      let columnFilters = slFilters?.columns?.columns;
      const trueKeys = Object.keys(columnFilters).filter(
        (key) => columnFilters[key]
      );
      setCheckedValues({ columns: trueKeys });
    }
  }, [slFilters]);

  const menuItems = [
    {
      header: "COLUMNS",
      items: [
        { name: "columns", value: "depotdate", label: "Depot Date" },
        { name: "columns", value: "weekNo", label: "Week No" },
        { name: "columns", value: "altfill", label: "Alt Fill" },
        { name: "columns", value: "customer", label: "Customer" },
        { name: "columns", value: "salesorder", label: "Sales Order" },
        { name: "columns", value: "salesOrderId", label: "Order Det Id" },
        { name: "columns", value: "product", label: "Product" },
      ],
    },
  ];

  const [state, setState] = useState([
    {
      startDate: new Date(initalDate),
      endDate: new Date(endDate),
      key: "selection",
    },
  ]);

  const handleDateChange = async (startDate, endDate) => {
    setInitialLoading(true);
    const formattedStartDate = moment(startDate).format("MM-DD-YYYY");
    const formattedEndDate = moment(endDate).format("MM-DD-YYYY");
    // const company = Cookies.get("company");
    const company = userData?.company;
    // const ADCompanyName = Cookies.get("ADCompanyName");
    const ADCompanyName = userData?.companyName || userData?.ADCompanyName;
    setSelectedReasons([]);
    setSelectedSubReasons([]);

    try {
      if (token && selectedCustomer && selectedCustomer.value) {
        const data = await getSLData(
          `get-initial-sl-data/${selectedCustomer?.value}/${formattedStartDate}/${formattedEndDate}/${seeAll}/${orderType}/${company}/${ADCompanyName}`
        );
        setShowLoadingMessage(true);
        if (!data) {
          setNoDataExists(true);
          setInitialLoading(false);
          toast.error("Your session has expired. Please log in again.");
          setTimeout(async() => {
            await logout();
            const redirectUrl = `/login?redirect=${encodeURIComponent(
              window.location.pathname
            )}`;
            router.push(redirectUrl);
          }, 3000);
          return;
        } else if (data?.length === 0) {
          setNoDataExists(true);
          setInitialLoading(false);
          toast.error(
            "There was an error with your request. Please check your data and try again."
          );
          return;
        } else if (data) {
          setRecordsCount(Object.keys(data?.formattedSLData)?.length ?? 0);
          const uniqueCustomersMap = new Map();
          
          uniqueCustomersMap.set("All Customers", { value: "All Customers", label: "All Customers" });
          
          // Add all other customers, overwriting any duplicates
          data.sLCustomers.forEach(customer => {
            uniqueCustomersMap.set(customer.value, customer);
          });
          
          // Convert the Map values back to an array
          const customers = Array.from(uniqueCustomersMap.values());
          
          setCustomerList(customers);
          // Convert the object into an array
          const dataArray = Object.values(data.formattedSLData);
          if (dataArray.length == 0) {
            setNoDataExists(true);
            setInitialLoading(false);
          }
          // Sort the array
          const sortedData = dataArray.sort((a, b) => {
            // Sort by DEPOT_DATE
            const dateComparison =
              new Date(a.DEPOT_DATE) - new Date(b.DEPOT_DATE);
            if (dateComparison !== 0) return dateComparison;

            // If DEPOT_DATE is the same, sort by CUSTOMER
            const customerComparison = a.CUSTOMER.localeCompare(b.CUSTOMER);
            if (customerComparison !== 0) return customerComparison;

            // If CUSTOMER is the same, sort by PRODUCT_DESCRIPTION
            return a.PRODUCT_DESCRIPTION.localeCompare(b.PRODUCT_DESCRIPTION);
          });

          // // Convert sorted array back to object
          // const sortedObject = sortedData.reduce((acc, item) => {
          //   acc[item.ORD_ID] = item;
          //   return acc;
          // }, {});
          setAllReasonsSubreasons(data.allReasonSubReasons);

          setCustomerSLData(sortedData || []);
          setInitialLoading(false);
        } else {
          console.error("Error fetching data with updated date range.");
          setNoDataExists(true);
          setInitialLoading(false);
          setCustomerSLData([]);
        }
      }
    } catch (error) {
      console.error(
        "Error fetching service-level data on date range change:",
        error
      );
      setNoDataExists(true);
      setInitialLoading(false);
    }
  };

  useEffect(() => {
    const start = moment(state[0].startDate);
    const end = moment(state[0].endDate);
    setStartDateRange(start);
    setEndDateRange(end);
    handleDateChange(state[0].startDate, state[0].endDate);
  }, [state]);

  const handleCustomerChange = async (selectedOption) => {
    setInitialLoading(true);
    setCurrentProducts([]);
    setSelectedProducts([]);
    setSelectedCustomer(selectedOption);
    setSelectedMasterProductCode({
      value: "all",
      label: "All Master Product Codes",
    });

    let cust_code = selectedOption?.value;
    let start_date = moment(state[0].startDate).format("MM-DD-YYYY");
    let end_date = moment(state[0].endDate).format("MM-DD-YYYY");
    // const company = Cookies.get("company");
    const company = userData?.company;
    // const ADCompanyName = Cookies.get("ADCompanyName");
    const ADCompanyName = userData?.companyName || userData?.ADCompanyName;
    setSelectedReasons([]);
    setSelectedSubReasons([]);
    try {
      if (token) {
        const data = await getSLData(
          `get-initial-sl-data/${cust_code}/${start_date}/${end_date}/${seeAll}/${orderType}/${company}/${ADCompanyName}?serviceCustomer=${selectedServiceCustomer.value}`
        );
        setShowLoadingMessage(true);
        if (!data) {
          setNoDataExists(true);
          setInitialLoading(false);
          toast.error("Your session has expired. Please log in again.");
          setTimeout(async() => {
            await logout();
            const redirectUrl = `/login?redirect=${encodeURIComponent(
              window.location.pathname
            )}`;
            router.push(redirectUrl);
          }, 3000);
          return;
        } else if (data?.length === 0) {
          setNoDataExists(true);
          setInitialLoading(false);
          setCustomerSLData([]);
          toast.error(
            "There was an error with your request. Please check your data and try again."
          );
          return;
        } else if (data) {
          const dataArray = Object.values(data.formattedSLData);
          if (dataArray.length == 0) {
            setNoDataExists(true);
            setInitialLoading(false);
          }

          const serviceCustomers = [
            { value: "All Service Customers", label: "All Service Customers" },
            ...data.sLServiceCustomers,
          ];
          updateServiceCustomersList(serviceCustomers);

          // Sort the array
          const sortedData = dataArray.sort((a, b) => {
            // Sort by DEPOT_DATE
            const dateComparison =
              new Date(a.DEPOT_DATE) - new Date(b.DEPOT_DATE);
            if (dateComparison !== 0) return dateComparison;

            // If DEPOT_DATE is the same, sort by CUSTOMER
            const customerComparison = a.CUSTOMER.localeCompare(b.CUSTOMER);
            if (customerComparison !== 0) return customerComparison;

            // If CUSTOMER is the same, sort by PRODUCT_DESCRIPTION
            return a.PRODUCT_DESCRIPTION.localeCompare(b.PRODUCT_DESCRIPTION);
          });
          setAllReasonsSubreasons(data.allReasonSubReasons);

          setCustomerSLData(sortedData || []);
        }
        setInitialLoading(false);
      }
    } catch (error) {
      setNoDataExists(true);
      setInitialLoading(false);
      console.error("Error fetching customer service-level data:", error);
    }
  };
  const handleServiceCustomerChange = async (selectedOption) => {
    setInitialLoading(true);
    setSelectedCustomer({ value: "All Customers", label: "All Customers" });
    setCurrentProducts([]);
    setSelectedProducts([]);
    setSelectedMasterProductCode({
      value: "all",
      label: "All Master Product Codes",
    });

    let selectedServiceCustomer = selectedOption?.value;
    setSelectedServiceCustomer(selectedOption);
    let cust_code = "All Customers";
    let start_date = moment(state[0].startDate).format("MM-DD-YYYY");
    let end_date = moment(state[0].endDate).format("MM-DD-YYYY");
    // const company = Cookies.get("company");
    const company = userData?.company;
    // const ADCompanyName = Cookies.get("ADCompanyName");
    const ADCompanyName = userData?.companyName || userData?.ADCompanyName;
    setSelectedReasons([]);
    setSelectedSubReasons([]);
    try {
      if (userData) {
        const data = await getSLData(
          `get-initial-sl-data/${cust_code}/${start_date}/${end_date}/${seeAll}/${orderType}/${company}/${ADCompanyName}?serviceCustomer=${selectedServiceCustomer}`
        );
        setShowLoadingMessage(true);
        if (!data) {
          setNoDataExists(true);
          setInitialLoading(false);
          toast.error("Your session has expired. Please log in again.");
          setTimeout(async() => {
            await logout();
            const redirectUrl = `/login?redirect=${encodeURIComponent(
              window.location.pathname
            )}`;
            router.push(redirectUrl);
          }, 3000);
          return;
        } else if (data?.length === 0) {
          setNoDataExists(true);
          setInitialLoading(false);
          setCustomerSLData([]);
          toast.error(
            "There was an error with your request. Please check your data and try again."
          );
          return;
        } else if (data) {
          const dataArray = Object.values(data.formattedSLData);
          if (dataArray.length == 0) {
            setNoDataExists(true);
            setInitialLoading(false);
          }

          // Sort the array
          const sortedData = dataArray.sort((a, b) => {
            // Sort by DEPOT_DATE
            const dateComparison =
              new Date(a.DEPOT_DATE) - new Date(b.DEPOT_DATE);
            if (dateComparison !== 0) return dateComparison;

            // If DEPOT_DATE is the same, sort by CUSTOMER
            const customerComparison = a.CUSTOMER.localeCompare(b.CUSTOMER);
            if (customerComparison !== 0) return customerComparison;

            // If CUSTOMER is the same, sort by PRODUCT_DESCRIPTION
            return a.PRODUCT_DESCRIPTION.localeCompare(b.PRODUCT_DESCRIPTION);
          });
          setAllReasonsSubreasons(data.allReasonSubReasons);

          setCustomerSLData(sortedData || []);
          const uniqueCustomersMap = new Map();
          
          uniqueCustomersMap.set("All Customers", { value: "All Customers", label: "All Customers" });
          
          // Add all other customers, overwriting any duplicates
          data.sLCustomers.forEach(customer => {
            uniqueCustomersMap.set(customer.value, customer);
          });
          
          // Convert the Map values back to an array
          const customers = Array.from(uniqueCustomersMap.values());
          
          setCustomerList(customers);
        }
        setInitialLoading(false);
      }
    } catch (error) {
      setNoDataExists(true);
      setInitialLoading(false);
      console.error("Error fetching customer service-level data:", error);
    }
  };

  const reloadHandler = async () => {
    setInitialLoading(true);

    setCurrentProducts([]);
    setSelectedProducts([]);

    let cust_code = selectedCustomer?.value; // Use selectedCustomer.value
    let start_date = moment(state[0].startDate).format("MM-DD-YYYY");
    let end_date = moment(state[0].endDate).format("MM-DD-YYYY");
    const company = Cookies.get("company");
    const ADCompanyName = Cookies.get("ADCompanyName");
    setSelectedReasons([]);
    setSelectedSubReasons([]);
    try {
      if (token && cust_code) {
        const data = await getSLData(
          `get-initial-sl-data/${cust_code}/${start_date}/${end_date}/${seeAll}/${orderType}/${company}/${ADCompanyName}`,
          token
        );
        setShowLoadingMessage(true);
        if (!data) {
          setNoDataExists(true);
          setInitialLoading(false);
          toast.error("Your session has expired. Please log in again.");
          setTimeout(() => {
            localStorage.removeItem("superUser");
            localStorage.removeItem("company");
            localStorage.removeItem("id");
            localStorage.removeItem("name");
            localStorage.removeItem("role");
            localStorage.removeItem("email");
            Cookies.remove("user");
            Cookies.remove("theme");
            Cookies.remove("token");
            const redirectUrl = `/login?redirect=${encodeURIComponent(
              window.location.pathname
            )}`;
            logoutHandler(instance, redirectUrl);
          }, 3000);
          return;
        } else if (data?.length === 0) {
          setNoDataExists(true);
          setInitialLoading(false);
          setCustomerSLData([]);
          toast.error(
            "There was an error with your request. Please check your data and try again."
          );
          return;
        } else if (data) {
          // Set data properly using setters
          const dataArray = Object.values(data.formattedSLData);
          if (dataArray.length == 0) {
            setNoDataExists(true);
            setInitialLoading(false);
          }
          setAllReasonsSubreasons(data.allReasonSubReasons);

          setCustomerSLData(dataArray || []);
          setCurrentProducts(
            data.formattedSLData.map((item) => ({
              label: item.PRODUCT_DESCRIPTION,
              value: item.ALTFILID,
            }))
          );
        }
      }
      setInitialLoading(false);
    } catch (error) {
      setNoDataExists(true);
      setInitialLoading(false);
      console.error("Error fetching customer service-level data:", error);
    }
  };

  return (
    <div className="sticky top-[52px]">
      <ToastContainer limit={1} />
      <div className="flex flex-row bg-white items-center">
        {/* <div className='w-[10%] border-r border-gray-200 px-3'>
          <Select placeholder="Select Year..." />
        </div> */}
        <div className="w-1/5 border-r border-gray-200 px-3">
          <div className="flex flex-row gap-4">
            <Popover>
              <PopoverTrigger disableButtonEnhancement>
                <div
                  className="w-full flex gap-3 cursor-pointer"
                  onClick={() => {}}
                  role="button"
                  tabIndex={0}
                >
                  <input
                    type="text"
                    className="px-2 2xl:px-3 border rounded-[4px] w-1/2 text-sm md:text-base font-medium h-[38px] cursor-pointer"
                    placeholder="Start Date Range..."
                    value={startDateRange?.format("DD/MM/YYYY")}
                    readOnly
                  />
                  <input
                    type="text"
                    className="px-2 2xl:px-3 border rounded-[4px] w-1/2 text-sm md:text-base font-medium h-[38px] cursor-pointer"
                    placeholder="End Date Range..."
                    value={endDateRange.format("DD/MM/YYYY")}
                    readOnly
                  />
                </div>
              </PopoverTrigger>

              <PopoverSurface
                tabIndex={-1}
                style={{ fontFamily: "poppinsregular" }}
              >
                <div>
                  <DateRange
                    editableDateInputs={true}
                    onChange={(item) => {
                      setState([item.selection]);
                    }}
                    moveRangeOnFirstSelection={false}
                    ranges={state}
                    className="depotdaterange"
                    // maxDate={new Date()}
                    minDate={new Date(minDate)}
                  />
                </div>
              </PopoverSurface>
            </Popover>
          </div>
        </div>
        {ADCompanyName == "Integrated Service Solutions Ltd" && (
          <div
            className="w-[15%] border-r border-gray-200 px-3 !text-base !font-medium !text-[#333333]"
            style={{ fontFamily: "poppinsregular" }}
          >
            <Select
              options={serviceCustomers}
              placeholder="Service Customers..."
              onChange={handleServiceCustomerChange}
              value={selectedServiceCustomer}
            />
          </div>
        )}
        <div
          className="w-[15%] border-r border-gray-200 px-3 !text-base !font-medium !text-[#333333]"
          style={{ fontFamily: "poppinsregular" }}
        >
          <Select
            options={customerList}
            placeholder="Select Customer..."
            onChange={handleCustomerChange}
            value={selectedCustomer}
          />
        </div>
        <div className="w-[15%] border-r border-gray-200 px-3">
          <Select
            placeholder="Select Categories..."
            options={masterProducts}
            onChange={(selected) => {
              setSelectedMasterProductCode(selected);
            }}
            value={selectedMasterProductCode}
          />
        </div>
        <div className="flex-1 border-r border-gray-200 px-3">
          <ProductDropDown
            currentProducts={currentProducts}
            setCurrentProducts={setCurrentProducts}
            selectedProducts={selectedProducts}
            setSelectedProducts={setSelectedProducts}
            slFilters={slFilters}
          />
        </div>

        <div
          className="w-[8%] flex justify-around px-3"
          style={{ fontFamily: "poppinsregular" }}
        >
          {/* <Tooltip
            content="Save Filter"
            relationship="label"
            className="!bg-white"
          >
            <button
              type="button"
              className="!border-0 !min-w-fit hover:bg-gray-100 my-2 px-3 rounded-sm"
              onClick={saveSLFilterHandler}
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                viewBox="0 0 512 512"
                className="w-5 h-5"
                fill="#000"
              >
                <path d="M222.2 319.2c.5 .5 1.1 .8 1.8 .8s1.4-.3 1.8-.8L350.2 187.3c1.2-1.2 1.8-2.9 1.8-4.6c0-3.7-3-6.7-6.7-6.7L288 176c-8.8 0-16-7.2-16-16l0-120c0-4.4-3.6-8-8-8l-80 0c-4.4 0-8 3.6-8 8l0 120c0 8.8-7.2 16-16 16l-57.3 0c-3.7 0-6.7 3-6.7 6.7c0 1.7 .7 3.3 1.8 4.6L222.2 319.2zM224 352c-9.5 0-18.6-3.9-25.1-10.8L74.5 209.2C67.8 202 64 192.5 64 182.7c0-21.4 17.3-38.7 38.7-38.7l41.3 0 0-104c0-22.1 17.9-40 40-40l80 0c22.1 0 40 17.9 40 40l0 104 41.3 0c21.4 0 38.7 17.3 38.7 38.7c0 9.9-3.8 19.3-10.5 26.5L249.1 341.2c-6.5 6.9-15.6 10.8-25.1 10.8zM32 336l0 96c0 26.5 21.5 48 48 48l288 0c26.5 0 48-21.5 48-48l0-96c0-8.8 7.2-16 16-16s16 7.2 16 16l0 96c0 44.2-35.8 80-80 80L80 512c-44.2 0-80-35.8-80-80l0-96c0-8.8 7.2-16 16-16s16 7.2 16 16z" />
              </svg>
            </button>
          </Tooltip> */}

          <FilterMenu
            setCheckedStates={setCheckedStates}
            reloadHandler={reloadHandler}
            menuItems={menuItems}
            checkedValues={checkedValues}
            setCheckedValues={setCheckedValues}
            setSelectedMasterProductCode={setSelectedMasterProductCode}
            setSelectedReasons={setSelectedReasons}
            setSelectedSubReasons={setSelectedSubReasons}
            setSelectedProducts={setSelectedProducts}
          />
        </div>
      </div>
      <BottomFilter
        setInitialLoading={setInitialLoading}
        initialLoading={initialLoading}
        startDateRange={startDateRange}
        endDateRange={endDateRange}
        selectedCustomer={selectedCustomer}
        setCustomerSLData={setCustomerSLData}
        setSelectedCustomer={setSelectedCustomer}
        seeAll={seeAll}
        setSeeAll={setSeeAll}
        orderTypeId={orderType}
        recordsCount={recordsCount}
        searchBoxContent={searchBoxContent}
        setSearchBoxContent={setSearchBoxContent}
        selectedRows={selectedRows}
        handleBulkUpdate={handleBulkUpdate}
        setNoDataExists={setNoDataExists}
        setNosetShowLoadingMessageDataExists={setShowLoadingMessage}
        allReasonsSubreasons={allReasonsSubreasons}
        setAllReasonsSubreasons={setAllReasonsSubreasons}
        setSelectedReasons={setSelectedReasons}
        setSelectedSubReasons={setSelectedSubReasons}
      />
    </div>
  );
};

export default Filter;
