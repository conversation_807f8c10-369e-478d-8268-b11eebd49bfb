"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/products",{

/***/ "./pages/products.js":
/*!***************************!*\
  !*** ./pages/products.js ***!
  \***************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __N_SSP: function() { return /* binding */ __N_SSP; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _components_Layout__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/Layout */ \"./components/Layout.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _fortawesome_react_fontawesome__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @fortawesome/react-fontawesome */ \"./node_modules/@fortawesome/react-fontawesome/index.es.js\");\n/* harmony import */ var _fortawesome_free_solid_svg_icons__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! @fortawesome/free-solid-svg-icons */ \"./node_modules/@fortawesome/free-solid-svg-icons/index.mjs\");\n/* harmony import */ var ag_grid_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ag-grid-react */ \"./node_modules/ag-grid-react/lib/main.js\");\n/* harmony import */ var ag_grid_react__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(ag_grid_react__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var ag_grid_community_styles_ag_grid_css__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ag-grid-community/styles//ag-grid.css */ \"./node_modules/ag-grid-community/styles/ag-grid.css\");\n/* harmony import */ var ag_grid_community_styles_ag_grid_css__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(ag_grid_community_styles_ag_grid_css__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var ag_grid_community_styles_ag_theme_alpine_css__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ag-grid-community/styles//ag-theme-alpine.css */ \"./node_modules/ag-grid-community/styles/ag-theme-alpine.css\");\n/* harmony import */ var ag_grid_community_styles_ag_theme_alpine_css__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(ag_grid_community_styles_ag_theme_alpine_css__WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_7__);\n/* harmony import */ var react_toastify__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! react-toastify */ \"./node_modules/react-toastify/dist/react-toastify.esm.mjs\");\n/* harmony import */ var react_toastify_dist_ReactToastify_css__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! react-toastify/dist/ReactToastify.css */ \"./node_modules/react-toastify/dist/ReactToastify.css\");\n/* harmony import */ var react_toastify_dist_ReactToastify_css__WEBPACK_IMPORTED_MODULE_9___default = /*#__PURE__*/__webpack_require__.n(react_toastify_dist_ReactToastify_css__WEBPACK_IMPORTED_MODULE_9__);\n/* harmony import */ var _utils_loaders_loadingContext__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/utils/loaders/loadingContext */ \"./utils/loaders/loadingContext.js\");\n/* harmony import */ var react_select__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! react-select */ \"./node_modules/react-select/dist/react-select.esm.js\");\n/* harmony import */ var _utils_rolePermissionsContext__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/utils/rolePermissionsContext */ \"./utils/rolePermissionsContext.js\");\n/* harmony import */ var _utils_renderer_productStatusRenderer__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/utils/renderer/productStatusRenderer */ \"./utils/renderer/productStatusRenderer.js\");\n/* harmony import */ var _utils_renderer_productActionRenderer__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/utils/renderer/productActionRenderer */ \"./utils/renderer/productActionRenderer.js\");\n/* harmony import */ var _services_apiConfig__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/services/apiConfig */ \"./services/apiConfig.js\");\n/* harmony import */ var js_cookie__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! js-cookie */ \"./node_modules/js-cookie/dist/js.cookie.mjs\");\n/* harmony import */ var _azure_msal_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @azure/msal-react */ \"./node_modules/@azure/msal-react/dist/index.js\");\n/* harmony import */ var _components_ProductDialog__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! @/components/ProductDialog */ \"./components/ProductDialog.js\");\n/* harmony import */ var _utils_secureStorage__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! @/utils/secureStorage */ \"./utils/secureStorage.js\");\n/* harmony import */ var _utils_extractCompanyFromEmail__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! @/utils/extractCompanyFromEmail */ \"./utils/extractCompanyFromEmail.js\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst customSelectStyles = {\n    // Default style\n    control: (base)=>({\n            ...base,\n            height: \"28px\",\n            minHeight: \"28px\"\n        }),\n    // Style when the condition is true\n    option: (base, param)=>{\n        let { data } = param;\n        return {\n            ...base,\n            color: data.is_new == true ? \"red\" : \"\"\n        };\n    },\n    valueContainer: (provided, state)=>({\n            ...provided,\n            height: \"26px\",\n            width: \"300px\",\n            padding: \"0 6px\"\n        }),\n    input: (provided, state)=>({\n            ...provided,\n            margin: \"0px\"\n        }),\n    indicatorSeparator: (state)=>({\n            display: \"none\"\n        }),\n    indicatorsContainer: (provided, state)=>({\n            ...provided,\n            height: \"28px\"\n        })\n};\nconst Products = (param)=>{\n    let { userData, PreviousPage, pageTypeId } = param;\n    _s();\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_7__.useRouter)();\n    const [pageSize, setPageSize] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(15);\n    const gridRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)();\n    const [isRMChecked, setIsRMChecked] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [isFGChecked, setIsFGChecked] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [isNVChecked, setIsNVChecked] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const { setIsLoading } = (0,_utils_loaders_loadingContext__WEBPACK_IMPORTED_MODULE_10__.useLoading)();\n    const [rowData, setRowData] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [company, setCompany] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"\");\n    const [blockScreen, setBlockScreen] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [typeId, setTypeId] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(pageTypeId);\n    const [isDialogOpen, setIsDialogOpen] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [isIssUser, setIsIssUser] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [isIssProcurementTeamUser, setIsIssProcurementTeamUser] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [isIssAdmin, setisIssAdmin] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [nvStatus, setNVStatus] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [statusOptions] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([\n        {\n            value: \"Draft\",\n            label: \"Draft\"\n        },\n        {\n            value: \"Pending Review\",\n            label: \"Pending Review\"\n        },\n        {\n            value: \"Rejected\",\n            label: \"Rejected\"\n        },\n        {\n            value: \"ISS to Setup\",\n            label: \"ISS to Setup\"\n        },\n        {\n            value: \"Setup Completed\",\n            label: \"Setup Completed\"\n        },\n        {\n            value: \"Cancelled\",\n            label: \"Cancelled\"\n        }\n    ]);\n    const [filteredRowData, setFilteredRowData] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [checkedValue, setCheckedValue] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [incompleteToast, setIncompleteToast] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [searchInput, setSearchInput] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"\");\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        const userCompany = js_cookie__WEBPACK_IMPORTED_MODULE_15__[\"default\"].get(\"company\");\n        if (userCompany === \"issproduce\") {\n            setIsIssUser(true);\n        }\n        console.log(userData);\n        if (userData.department_id === 2) {\n            setIsIssProcurementTeamUser(true);\n            console.log(\"checking if it is an admin\", userData.role);\n            if (userData.role_id === 1) {\n                console.log(\"is admin\");\n                setisIssAdmin(true);\n            }\n        }\n        console.log(\"isIssProcurementTeamUser\", isIssProcurementTeamUser);\n    }, []);\n    const openDialog = ()=>{\n        setIsDialogOpen(true);\n    };\n    const closeDialog = ()=>{\n        setIsDialogOpen(false);\n    };\n    // const [isOpenOption, setIsOpenOption] = useState(false);\n    const [selectedRequestType, setSelectedRequestType] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(PreviousPage);\n    const handleTypeIdChange = (e)=>{\n        setSearchInput(\"\");\n        const filterTextBox = document.getElementById(\"filter-text-box\");\n        filterTextBox.value = \"\";\n        gridRef.current.api.setQuickFilter(\"\");\n        setNVStatus([]);\n        setTypeId(parseInt(e.target.value));\n        if (e.target.value === \"1\") {\n            setSelectedRequestType(\"rawMaterialRequest\");\n        } else if (e.target.value === \"3\") {\n            setSelectedRequestType(\"newVarietyRequest\");\n        } else if (e.target.value === \"4\") {\n            setSelectedRequestType(\"packagingform\");\n        }\n    };\n    // const closeOptionModal = () => {\n    //   setIsOpenOption(false);\n    // };\n    const handleRequestType = (type)=>{\n        setSelectedRequestType(type);\n    };\n    const handleFormType = ()=>{\n        if (selectedRequestType) {\n            localStorage.setItem(\"formType\", selectedRequestType);\n            console.log(selectedRequestType);\n            if (selectedRequestType === \"rawMaterialRequest\") {\n                router.push({\n                    pathname: \"/raw-material-request/add\"\n                });\n            // } else if (selectedRequestType == \"finishedProductRequest\") {\n            //   router.push({\n            //     pathname: `/finished-product-request/add` });\n            } else if (selectedRequestType == \"newVarietyRequest\") {\n                router.push({\n                    pathname: \"/variety/add\"\n                });\n            } else if (selectedRequestType == \"packagingform\") {\n                // router.push({ pathname: `/packaging/add` });\n                router.push({\n                    pathname: \"/packaging-form/add\"\n                }); //TODO\n            }\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        if (incompleteToast) {\n            react_toastify__WEBPACK_IMPORTED_MODULE_8__.toast.error(\"Kindly Submit the Request to Export it.\", {\n                position: \"top-right\",\n                autoClose: 1000,\n                hideProgressBar: false,\n                closeOnClick: true,\n                pauseOnHover: false,\n                draggable: true,\n                progress: undefined,\n                theme: \"light\"\n            });\n        }\n    }, [\n        incompleteToast\n    ]);\n    const [productsExtractData, setProductsExtractData] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([\n        [\n            \"Product Extract Data\"\n        ]\n    ]);\n    const [altfilExtractData, setAltfilExtractData] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([\n        [\n            \"Altfil Extract Data\"\n        ]\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        if (productsExtractData.length > 0) {\n            setCheckedValue(true);\n        } else {\n            setCheckedValue(false);\n        }\n    }, [\n        productsExtractData\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        if (typeof document !== \"undefined\") {\n            document.title = \"Products\";\n        }\n        setIsLoading(false);\n        getData().then((data)=>{\n            console.log(data);\n            const formattedData = data === null || data === void 0 ? void 0 : data.map((row)=>({\n                    id: row.id,\n                    code: row.code,\n                    action_id: row.action_id,\n                    request_no: row.request_no,\n                    type: row.product_type,\n                    product_type_name: row.product_type_label,\n                    product_type_id: row.product_type_id,\n                    reason: (row === null || row === void 0 ? void 0 : row.reason_description) ? row === null || row === void 0 ? void 0 : row.reason_description : \"Not Entered\",\n                    delivery_date: row.delivery_date ? new Date(row.delivery_date).toISOString().split(\"T\")[0] : \"Not Entered\",\n                    // delivery_date: new Date(row.delivery_date),\n                    product_code: row.master_product_code ? row.master_product_code : \"Not Entered\",\n                    product_description: row.product_description ? row.product_description : \"Not Entered\",\n                    originator: row.originator ? row.originator : row.originator_name,\n                    originator_email: row.originator_email,\n                    coo: row.coo ? row.coo : \"Not Entered\",\n                    status: row.status_label,\n                    master_product_code: row === null || row === void 0 ? void 0 : row.master_product_code,\n                    count_or_size: row === null || row === void 0 ? void 0 : row.count_or_size,\n                    units_in_outer: row === null || row === void 0 ? void 0 : row.units_in_outer,\n                    cases_per_pallet: row === null || row === void 0 ? void 0 : row.cases_per_pallet,\n                    outer_net_weight: row === null || row === void 0 ? void 0 : row.outer_net_weight,\n                    outer_gross_weight: row === null || row === void 0 ? void 0 : row.outer_gross_weight,\n                    sub_product_code: row === null || row === void 0 ? void 0 : row.sub_product_code,\n                    temperature_grade: row === null || row === void 0 ? void 0 : row.temperature_grade,\n                    intrastat_commodity_code: row === null || row === void 0 ? void 0 : row.intrastat_commodity_code,\n                    temperature_grade_name: row === null || row === void 0 ? void 0 : row.temperature_grade_name,\n                    intrastat_commodity_code_name: row === null || row === void 0 ? void 0 : row.intrastat_commodity_code_name,\n                    mark_variety_name: row === null || row === void 0 ? void 0 : row.mark_variety_name,\n                    intrastat_commodity_code_id: row === null || row === void 0 ? void 0 : row.intrastat_commodity_code_id,\n                    sort_group_id: row === null || row === void 0 ? void 0 : row.group_id,\n                    company: row === null || row === void 0 ? void 0 : row.company_name,\n                    temperature_grade_id: row === null || row === void 0 ? void 0 : row.temperature_grade_id,\n                    userText4: row === null || row === void 0 ? void 0 : row.userText4,\n                    userText5: row === null || row === void 0 ? void 0 : row.userText5,\n                    userText6: row === null || row === void 0 ? void 0 : row.userText6\n                }));\n            setCompany(formattedData[0].company);\n            setRowData(formattedData);\n            setFilteredRowData(formattedData);\n        }).catch((error)=>{\n            return error;\n        });\n    }, [\n        typeId\n    ]);\n    function getData() {\n        setRowData([]);\n        setFilteredRowData([]);\n        let serverAddress = _services_apiConfig__WEBPACK_IMPORTED_MODULE_14__.apiConfig.serverAddress;\n        const company_name = (userData === null || userData === void 0 ? void 0 : userData.company) || (0,_utils_extractCompanyFromEmail__WEBPACK_IMPORTED_MODULE_19__.extractCompanyFromEmail)(userData === null || userData === void 0 ? void 0 : userData.email);\n        const AdCompany = (userData === null || userData === void 0 ? void 0 : userData.companyName) || (userData === null || userData === void 0 ? void 0 : userData.ADCompanyName);\n        return fetch(\"\".concat(serverAddress, \"products/get-products/\").concat(company_name == \"dpsltd\" && AdCompany == \"DPS MS\" ? \"DPS MS\" : company_name, \"\\n      /\").concat(typeId), {\n            method: \"GET\",\n            headers: {\n                \"Content-Type\": \"application/json\"\n            },\n            credentials: \"include\"\n        }).then(async (res)=>{\n            if (res.status == 502) {\n                setBlockScreen(true);\n                return;\n            }\n            setBlockScreen(false);\n            if (res.status === 200) {\n                return res.json();\n            }\n            if (res.status === 400) {\n                react_toastify__WEBPACK_IMPORTED_MODULE_8__.toast.error(\"There was an error with your request. Please check your data and try again.\");\n            } else if (res.status === 401) {\n                react_toastify__WEBPACK_IMPORTED_MODULE_8__.toast.error(\"Your session has expired. Please log in again.\");\n                setTimeout(async ()=>{\n                    await (0,_utils_secureStorage__WEBPACK_IMPORTED_MODULE_18__.logout)();\n                    router.push(\"/login\");\n                }, 3000);\n            }\n            throw new Error(\"Failed to fetch data\");\n        }).catch((error)=>{\n            console.error(error);\n        });\n    }\n    const handlePageSizeChange = (event)=>{\n        const newPageSize = parseInt(event.target.value, 15);\n        setPageSize(newPageSize);\n        gridRef.current.api.paginationSetPageSize(newPageSize);\n    };\n    const handleStatusChange = (selectedOptions)=>{\n        setNVStatus(selectedOptions);\n        filterData(selectedOptions);\n    };\n    const filterData = (statuses)=>{\n        if (statuses.length == 0) {\n            setFilteredRowData(rowData);\n            return;\n        }\n        const filteredData = rowData.filter((row)=>statuses.some((status)=>status.value === row.status));\n        setFilteredRowData(filteredData.length > 0 ? filteredData : []);\n    };\n    // const clearFilters = () => {\n    //   setSelectedStatuses([]);\n    //   setFilteredRowData(rowData);\n    // };\n    const defaultColDef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useMemo)(()=>({\n            sortable: true,\n            filter: false,\n            resizable: true,\n            flex: 1,\n            suppressMenu: false\n        }));\n    const gridOptions = {\n        responsive: true\n    };\n    const columnDefs = [\n        {\n            headerName: \"Request No.\",\n            field: \"request_no\",\n            // cellRenderer: nameRenderer,\n            suppressMenu: true,\n            suppressSizeToFit: true,\n            suppressSizeToFit: false,\n            cellClass: \"ag-grid-checkbox-cell\",\n            flex: \"2%\",\n            filter: true\n        },\n        {\n            headerName: \"Reason\",\n            field: \"reason\",\n            flex: \"2%\",\n            cellRenderer: _utils_renderer_productStatusRenderer__WEBPACK_IMPORTED_MODULE_12__[\"default\"],\n            hide: typeId == 1 || 4 ? false : 0,\n            cellStyle: (params)=>{\n                if (params.value == \"Not Entered\") {\n                    return {\n                        color: \"#B31312\"\n                    };\n                }\n                return null;\n            }\n        },\n        {\n            headerName: \"Delivery Date\",\n            field: \"delivery_date\",\n            flex: \"2%\",\n            hide: typeId == 1 || 4 ? false : 0,\n            cellRenderer: (params)=>{\n                if (params.value === \"Not Entered\") {\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-[#B31312]\",\n                        children: \"Not Entered\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\products.js\",\n                        lineNumber: 383,\n                        columnNumber: 18\n                    }, undefined);\n                } else {\n                    // params.value\n                    // Format the date from yyyy-mm-dd to dd/mm/yyyy\n                    const dateParts = params.value.split(\"-\");\n                    const formattedDate = dateParts.reverse().join(\"/\");\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: formattedDate\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\products.js\",\n                        lineNumber: 390,\n                        columnNumber: 18\n                    }, undefined);\n                }\n            }\n        },\n        {\n            headerName: \"Product Code\",\n            field: \"product_code\",\n            flex: \"2%\",\n            // cellClass: (params) => {\n            //   return params.value === \"Not Entered\" ? 'not-entered' : '';\n            // }\n            cellStyle: (params)=>{\n                if (params.value == \"Not Entered\") {\n                    return {\n                        color: \"#B31312\"\n                    };\n                }\n                return null;\n            }\n        },\n        {\n            // headerName: \"Product description\",\n            // field: \"product_description\",\n            headerName: \"Product Description\",\n            field: \"product_description\",\n            // cellRenderer: statusRenderer,\n            flex: \"3%\",\n            cellStyle: (params)=>{\n                if (params.value == \"Not Entered\") {\n                    return {\n                        color: \"#B31312\"\n                    };\n                }\n                return null;\n            }\n        },\n        {\n            headerName: \"Originator\",\n            field: \"originator\",\n            headerName: \"Originator\",\n            field: \"originator\",\n            // cellRenderer: statusRenderer,\n            flex: \"2%\",\n            flex: \"2%\"\n        },\n        // {\n        //   headerName: \"COO\",\n        //   field: \"coo\",\n        //   headerName: \"COO\",\n        //   field: \"coo\",\n        //   // cellRenderer: statusRenderer,\n        //   flex: \"2%\",\n        //   cellStyle: (params) => {\n        //     if (params.value == \"Not Entered\") {\n        //       return { color: \"#B31312\" };\n        //     }\n        //     return null;\n        //   },\n        // },\n        {\n            headerName: \"Status\",\n            field: \"status\",\n            cellRenderer: _utils_renderer_productStatusRenderer__WEBPACK_IMPORTED_MODULE_12__[\"default\"],\n            cellStyle: ()=>({\n                    justifyContent: \"center\"\n                }),\n            flex: \"2%\",\n            hide: false\n        },\n        {\n            // field: \"Action(s)\",\n            field: typeId == 3 ? \"Action(s)\" : \"Action(s)\",\n            cellRenderer: (params)=>(0,_utils_renderer_productActionRenderer__WEBPACK_IMPORTED_MODULE_13__[\"default\"])(params, userData, userData.token, company, typeId, setIsLoading, isIssUser),\n            flex: \"2%\",\n            cellStyle: ()=>({}),\n            cellStyle: {\n                justifyContent: \"end\",\n                paddingRight: \"10px\"\n            },\n            sortable: false\n        },\n        {\n            field: \"role_num\",\n            hide: true\n        }\n    ];\n    const handleGridReady = (params)=>{\n        params.api.setColumnDefs(columnDefs);\n    };\n    const onFilterTextBoxChanged = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(()=>{\n        gridRef.current.api.setQuickFilter(document.getElementById(\"filter-text-box\").value);\n        setSearchInput(document.getElementById(\"filter-text-box\").value);\n    }, []);\n    const handleFilterProductType = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)((e)=>{\n        if (e.target.value == \"RM\") {\n            if (e.target.checked) {\n                setIsRMChecked(true);\n                setIsFGChecked(false);\n                setIsNVChecked(false);\n                gridRef.current.api.setQuickFilter(\"RM\");\n            } else {\n                setIsRMChecked(false);\n                gridRef.current.api.setQuickFilter(\"\");\n            }\n        } else if (e.target.value == \"FG\") {\n            if (e.target.checked) {\n                setIsFGChecked(true);\n                setIsRMChecked(false);\n                setIsNVChecked(false);\n                gridRef.current.api.setQuickFilter(\"FG\");\n            } else {\n                setIsFGChecked(false);\n                gridRef.current.api.setQuickFilter(\"\");\n            }\n        } else if (e.target.value == \"NV\") {\n            if (e.target.checked) {\n                setIsNVChecked(true);\n                setIsFGChecked(false);\n                setIsRMChecked(false);\n                gridRef.current.api.setQuickFilter(\"NV\");\n            } else {\n                setIsNVChecked(true);\n                gridRef.current.api.setQuickFilter(\"\");\n            }\n        } else {\n            setIsRMChecked(false);\n            setIsFGChecked(false);\n            setIsNVChecked(false);\n            gridRef.current.api.setQuickFilter(\"\");\n        }\n    }, []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_toastify__WEBPACK_IMPORTED_MODULE_8__.ToastContainer, {\n                limit: 1\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\products.js\",\n                lineNumber: 530,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Layout__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                userData: userData,\n                blockScreen: blockScreen,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mr-20 md:mr-12 lg:mr-14\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-row md:flex-col lg:flex-row justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"border border-light-gray rounded-md px-3 py-1\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"flex items-center gap-2 cursor-pointer\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                type: \"radio\",\n                                                                name: \"requestType\",\n                                                                value: \"1\",\n                                                                className: \"form-radio w-5 h-5 cursor-pointer\",\n                                                                checked: typeId === 1,\n                                                                onChange: handleTypeIdChange\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\products.js\",\n                                                                lineNumber: 538,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"labels\",\n                                                                children: \"Raw Materials\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\products.js\",\n                                                                lineNumber: 546,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\products.js\",\n                                                        lineNumber: 537,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\products.js\",\n                                                    lineNumber: 536,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"border border-light-gray rounded-md px-3 py-1\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"flex items-center gap-2 cursor-pointer\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                type: \"radio\",\n                                                                name: \"requestType\",\n                                                                value: \"3\",\n                                                                className: \"form-radio w-5 h-5 cursor-pointer\",\n                                                                checked: typeId === 3,\n                                                                onChange: handleTypeIdChange\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\products.js\",\n                                                                lineNumber: 562,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"labels\",\n                                                                children: \"New Variety\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\products.js\",\n                                                                lineNumber: 570,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\products.js\",\n                                                        lineNumber: 561,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\products.js\",\n                                                    lineNumber: 560,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                isIssUser && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"border border-light-gray rounded-md px-3 py-1\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"flex items-center gap-2 cursor-pointer\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                type: \"radio\",\n                                                                name: \"requestType\",\n                                                                value: \"4\",\n                                                                className: \"form-radio w-5 h-5 cursor-pointer\",\n                                                                checked: typeId === 4,\n                                                                onChange: handleTypeIdChange\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\products.js\",\n                                                                lineNumber: 576,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"labels\",\n                                                                children: \"Packaging\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\products.js\",\n                                                                lineNumber: 584,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\products.js\",\n                                                        lineNumber: 575,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\products.js\",\n                                                    lineNumber: 574,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\products.js\",\n                                            lineNumber: 535,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"labels\",\n                                            style: {\n                                                marginLeft: \"10px\"\n                                            },\n                                            children: typeId === 3 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_select__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                isMulti: true,\n                                                options: statusOptions,\n                                                onChange: handleStatusChange,\n                                                placeholder: \"Select Status...\",\n                                                className: \"basic-multi-select\",\n                                                classNamePrefix: \"select\",\n                                                value: nvStatus,\n                                                isSearchable: false,\n                                                styles: customSelectStyles\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\products.js\",\n                                                lineNumber: 591,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\products.js\",\n                                            lineNumber: 589,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center px-3 w-auto md:w-fit lg:w-auto md:py-2 lg:py-1 md:mb-3 lg:mb-0\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\products.js\",\n                                            lineNumber: 607,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\products.js\",\n                                    lineNumber: 534,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"relative block w-[47vh] text-gray-400 mt-0 pt-0\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"absolute px-4 py-1 pt-[0.4rem] 2xl:pt-1.5 text-black\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fortawesome_react_fontawesome__WEBPACK_IMPORTED_MODULE_3__.FontAwesomeIcon, {\n                                                        icon: _fortawesome_free_solid_svg_icons__WEBPACK_IMPORTED_MODULE_21__.faSearch,\n                                                        className: \"fw-bold\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\products.js\",\n                                                        lineNumber: 638,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\products.js\",\n                                                    lineNumber: 637,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"text\",\n                                                    id: \"filter-text-box\",\n                                                    placeholder: \"Search...\",\n                                                    onInput: onFilterTextBoxChanged,\n                                                    value: searchInput,\n                                                    className: \"block w-full px-4 pl-10 text-gray-500 placeholder-gray-400 searchbar border rounded-lg appearance-none form-input focus:outline-none\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\products.js\",\n                                                    lineNumber: 640,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\products.js\",\n                                            lineNumber: 636,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ProductDialog__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                            isOpen: openDialog,\n                                            onClose: closeDialog,\n                                            handleFormType: handleFormType,\n                                            selectedRequestType: selectedRequestType,\n                                            isIssUser: isIssUser,\n                                            isIssProcurmentUser: isIssProcurementTeamUser,\n                                            handleRequestType: handleRequestType,\n                                            admin: isIssAdmin\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\products.js\",\n                                            lineNumber: 659,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\products.js\",\n                                    lineNumber: 635,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\products.js\",\n                            lineNumber: 533,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"my-5\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative ag-theme-alpine !rounded-md\",\n                                style: {\n                                    height: \"calc(100vh - 151px)\"\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ag_grid_react__WEBPACK_IMPORTED_MODULE_4__.AgGridReact, {\n                                        rowData: filteredRowData,\n                                        ref: gridRef,\n                                        columnDefs: columnDefs,\n                                        defaultColDef: defaultColDef,\n                                        suppressRowClickSelection: true,\n                                        pagination: true,\n                                        paginationPageSize: pageSize,\n                                        onPageSizeChanged: handlePageSizeChange,\n                                        tooltipShowDelay: 0,\n                                        tooltipHideDelay: 1000,\n                                        onGridReady: handleGridReady,\n                                        gridOptions: gridOptions\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\products.js\",\n                                        lineNumber: 676,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-start mt-2 pagination-style\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            htmlFor: \"page-size-select pagination\",\n                                            className: \"inputs\",\n                                            children: [\n                                                \"Show\",\n                                                \" \",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                    id: \"page-size-select\",\n                                                    onChange: handlePageSizeChange,\n                                                    value: pageSize,\n                                                    className: \"focus:outline-none\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: 10,\n                                                            children: \"10\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\products.js\",\n                                                            lineNumber: 699,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: 15,\n                                                            children: \"15\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\products.js\",\n                                                            lineNumber: 700,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: 25,\n                                                            children: \"25\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\products.js\",\n                                                            lineNumber: 701,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: 50,\n                                                            children: \"50\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\products.js\",\n                                                            lineNumber: 702,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: 100,\n                                                            children: \"100\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\products.js\",\n                                                            lineNumber: 703,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\products.js\",\n                                                    lineNumber: 693,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                \" \",\n                                                \"Entries\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\products.js\",\n                                            lineNumber: 691,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\products.js\",\n                                        lineNumber: 690,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\products.js\",\n                                lineNumber: 672,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\products.js\",\n                            lineNumber: 671,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\products.js\",\n                    lineNumber: 532,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\products.js\",\n                lineNumber: 531,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true);\n};\n_s(Products, \"x02n8KTM2A42sUdrK7Zft5e7jZs=\", false, function() {\n    return [\n        next_router__WEBPACK_IMPORTED_MODULE_7__.useRouter,\n        _utils_loaders_loadingContext__WEBPACK_IMPORTED_MODULE_10__.useLoading\n    ];\n});\n_c = Products;\nvar __N_SSP = true;\n/* harmony default export */ __webpack_exports__[\"default\"] = (Products);\nvar _c;\n$RefreshReg$(_c, \"Products\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevExports = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevExports) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports on update so we can compare the boundary\n                // signatures.\n                module.hot.dispose(function (data) {\n                    data.prevExports = currentExports;\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevExports !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevExports, currentExports)) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevExports !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./pages/products.js\n"));

/***/ })

});