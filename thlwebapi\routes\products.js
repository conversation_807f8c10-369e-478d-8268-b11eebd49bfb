"use strict";
const express=require("express");
const productsController=require("../controllers/products");
const router=express.Router();

const {addRawMaterials,getProductsDropdowns,getProducts,getRawMaterialsById,
    updateStatus,addFinishedProducts,getFinishedProductsById,updateRawMaterials,updateFinishedProducts,addUpdateNV 
    ,addAllDropdownValue,updateAllDropdownValue, updateUnblock, getRequestNumber, insertRequestNumber, getProductGroups,getSubProductsByMasterCode
    ,getFilteredVarietyNames,
    getNVProductById,checkVariety,addUpdatePk,getPkById
} = productsController;

const { validateSession } = require("../middleware/sessionAuth");

router.use(validateSession);

router.post("/add-raw-materials",addRawMaterials);
router.post("/update-raw-materials/:id",updateRawMaterials);
router.post("/add-finished-products",addFinishedProducts);
router.post("/update-finished-products/:id",updateFinishedProducts);
router.post("/add-update-varieties" , addUpdateNV);
router.post("/check-variety" , checkVariety);
router.get("/get-nv-product-by-id/:id" , getNVProductById);
router.get("/get-filtered-variety-names/:searchString/:prophetId",getFilteredVarietyNames);
router.post("/get-products-dropdowns-list",getProductsDropdowns);
//*below get-products api used by raw materials, new variety and also for packaging request
router.get("/get-products/:company/:type_id",getProducts);
router.get("/get-raw-materials-by-id/:id",getRawMaterialsById);
router.get("/get-finished-product-by-id/:id",getFinishedProductsById);
router.post("/product-update-status", updateStatus);
router.post("/add-all-dropdown-value", addAllDropdownValue)
router.put("/update-all-dropdown-value", updateAllDropdownValue);
router.post("/product-update-unblock", updateUnblock);
router.get("/get-request-number/:product_request_type", getRequestNumber);
router.post("/insert-request-number", insertRequestNumber);
router.get("/get-product-groups/:type", getProductGroups);
router.get("/get-subproduct-code/:master_code", getSubProductsByMasterCode);

//packaging request form
router.post("/add-update-packaging-request" , addUpdatePk);
router.get("/get-packaging-request-by-id/:id" , getPkById);


module.exports=router;