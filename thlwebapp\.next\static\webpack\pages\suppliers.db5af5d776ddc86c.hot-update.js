"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/suppliers",{

/***/ "./utils/exportExcel.js":
/*!******************************!*\
  !*** ./utils/exportExcel.js ***!
  \******************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var exceljs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! exceljs */ \"./node_modules/exceljs/dist/exceljs.min.js\");\n/* harmony import */ var exceljs__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(exceljs__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react_toastify__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react-toastify */ \"./node_modules/react-toastify/dist/react-toastify.esm.mjs\");\n/* harmony import */ var react_toastify_dist_ReactToastify_css__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-toastify/dist/ReactToastify.css */ \"./node_modules/react-toastify/dist/ReactToastify.css\");\n/* harmony import */ var react_toastify_dist_ReactToastify_css__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react_toastify_dist_ReactToastify_css__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _services_apiConfig__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/services/apiConfig */ \"./services/apiConfig.js\");\n\n\n\n\n// todo:move the supplierCode column from the excel file to first column\nconst exportExcel = async function(data, isInternal, token, company, userData, prophet_id, requestor_email, isMultiple) {\n    let isProductExtract = arguments.length > 8 && arguments[8] !== void 0 ? arguments[8] : false, onProductSubmit = arguments.length > 9 && arguments[9] !== void 0 ? arguments[9] : false, productEmailParagraph = arguments.length > 10 && arguments[10] !== void 0 ? arguments[10] : \"\", productEmailCommentPlaceholder = arguments.length > 11 && arguments[11] !== void 0 ? arguments[11] : \"\", request_no = arguments.length > 12 && arguments[12] !== void 0 ? arguments[12] : \"\", varietyRequest = arguments.length > 13 && arguments[13] !== void 0 ? arguments[13] : false;\n    let serverAddress = _services_apiConfig__WEBPACK_IMPORTED_MODULE_3__.apiConfig.serverAddress;\n    let isEmergencyAndFinanceNotCompleteObj = [];\n    let supplierNames = [];\n    const workbook = new (exceljs__WEBPACK_IMPORTED_MODULE_0___default().Workbook)();\n    if (isInternal) {\n        data.forEach((sheetData, index1)=>{\n            if (sheetData.length === 0) {\n                console.error(\"sheetData is empty for index:\", index1);\n                return;\n            }\n            const sheetName = sheetData[0] || \"Sheet\".concat(index1 + 1);\n            let worksheet;\n            if (index1 < 9) {\n                if (Array.isArray(sheetName)) {\n                    const actualSheetName = sheetName[0];\n                    worksheet = workbook.addWorksheet(actualSheetName);\n                } else {\n                    worksheet = workbook.addWorksheet(sheetName);\n                }\n            }\n            if (sheetData.length > 1) {\n                let headers;\n                if (index1 < 9) {\n                    headers = Object.keys(sheetData[1]);\n                    worksheet.addRow(headers);\n                }\n                sheetData.slice(1).forEach((row, internalIndex)=>{\n                    if (index1 < 9) {\n                        const rowData = headers.map((header)=>row[header] || \"\");\n                        if (index1 != 4 && index1 != 3) {\n                            worksheet.addRow(rowData);\n                        } else if (index1 == 4) {\n                            worksheet.addRow(rowData);\n                        } else if (index1 == 3 && rowData[1] != \"\") {\n                            worksheet.addRow(rowData);\n                        }\n                    }\n                    if (index1 === 9) {\n                        supplierNames.push({\n                            supplierName: row === null || row === void 0 ? void 0 : row.supplierName,\n                            supplierCode: row === null || row === void 0 ? void 0 : row.supplierCode\n                        });\n                        if (row.isEmergencyAndFinanceNotComplete) {\n                            let isEmergencyAndFinanceNotCompleteSupplier = {\n                                isEmergencyAndFinanceNotComplete: row.isEmergencyAndFinanceNotComplete,\n                                supplierName: row.supplierName\n                            };\n                            isEmergencyAndFinanceNotCompleteObj.push(isEmergencyAndFinanceNotCompleteSupplier);\n                        }\n                        fetch(\"\".concat(serverAddress, \"suppliers/update-supplier/\").concat(row.id), {\n                            method: \"PUT\",\n                            headers: {\n                                Accept: \"application/json\",\n                                \"Content-Type\": \"application/json\"\n                            },\n                            credentials: \"include\",\n                            body: JSON.stringify({\n                                sectionName: \"updateStatus\",\n                                type: \"exportExcel\",\n                                status: 5,\n                                exported: true,\n                                updated_date: new Date().toISOString(),\n                                company_name: row.supplierName,\n                                to: \"Internal\"\n                            })\n                        }).then((res)=>{\n                            if (res.status === 200) {\n                                return res.json();\n                            }\n                            if (res.status === 401) {\n                                react_toastify__WEBPACK_IMPORTED_MODULE_1__.toast.error(\"Your session has expired. Please log in again.\");\n                                setTimeout(async ()=>{\n                                    await logout();\n                                }, 3000);\n                                return null;\n                            }\n                            return Promise.reject(res);\n                        }).then((json)=>{\n                            if (json.status == 200) {\n                                return true;\n                            }\n                        }).catch((error)=>{\n                            console.log(error);\n                        });\n                    }\n                });\n            }\n        });\n    } else if (isProductExtract) {\n        if (data.length === 0) {\n            console.error(\"sheetData is empty for index:\");\n            return;\n        }\n        data.forEach((sheetData)=>{\n            var _Object;\n            const sheetName = sheetData[0] || \"Sheet\".concat(index + 1);\n            let worksheet;\n            if (Array.isArray(sheetName)) {\n                const actualSheetName = sheetName[0];\n                worksheet = workbook.addWorksheet(actualSheetName);\n            } else {\n                worksheet = workbook.addWorksheet(sheetName);\n            }\n            // console.log(\"Object.keys(sheetData[1])\",Object?.keys(sheetData[1])? Object?.keys(sheetData[1]) : 'not there');\n            let headers = ((_Object = Object) === null || _Object === void 0 ? void 0 : _Object.keys(sheetData[1])) ? Object.keys(sheetData[1]) : \"null\";\n            if (isMultiple) {\n                headers = headers.slice(0, -1);\n            }\n            worksheet.addRow(headers);\n            sheetData.slice(1).forEach((row, internalIndex)=>{\n                let rowData;\n                rowData = headers.map((header)=>row[header] || \"\");\n                worksheet.addRow(rowData);\n            });\n        });\n    } else {\n        if (data.length === 0) {\n            console.error(\"sheetData is empty for index:\");\n            return;\n        }\n        data.forEach((row, index1)=>{\n            const sheetName = row[0] || \"Sheet\".concat(index1 + 1);\n            let worksheet;\n            if (Array.isArray(sheetName)) {\n                const actualSheetName = sheetName[0];\n                worksheet = workbook.addWorksheet(actualSheetName);\n            } else {\n                worksheet = workbook.addWorksheet(sheetName);\n            }\n            let headers;\n            if (index1 == 1) {\n                headers = Object.keys(row[1]).slice(0, -2);\n            } else {\n                headers = Object.keys(row[1]);\n            }\n            worksheet.addRow(headers);\n            for(let i = 1; i < row.length; i++){\n                if (index1 == 0 && i > 0) {\n                    supplierNames.push({\n                        supplierName: row[i][\"Supplier name\"],\n                        supplierCode: row[i][\"Supplier code\"]\n                    });\n                }\n                let rowData;\n                if (index1 == 1) {\n                    rowData = headers.map((header)=>row[i][header] || \"\").slice(0, -2);\n                } else {\n                    rowData = headers.map((header)=>row[i][header] || \"\");\n                }\n                worksheet.addRow(rowData);\n                if (row[i].isEmergencyAndFinanceNotComplete) {\n                    let isEmergencyAndFinanceNotCompleteSupplier = {\n                        isEmergencyAndFinanceNotComplete: row[i].isEmergencyAndFinanceNotComplete,\n                        supplierName: row[i][\"Supplier name\"]\n                    };\n                    isEmergencyAndFinanceNotCompleteObj.push(isEmergencyAndFinanceNotCompleteSupplier);\n                }\n                if (index1 == 1) {\n                    fetch(\"\".concat(serverAddress, \"suppliers/update-supplier/\").concat(row[i].id), {\n                        method: \"PUT\",\n                        headers: {\n                            Accept: \"application/json\",\n                            \"Content-Type\": \"application/json\"\n                        },\n                        credentials: \"include\",\n                        body: JSON.stringify({\n                            sectionName: \"updateStatus\",\n                            type: \"exportExcel\",\n                            status: 5,\n                            exported: true,\n                            updated_date: new Date().toISOString(),\n                            company_name: row[\"Supplier name\"],\n                            to: \"ISS\"\n                        })\n                    }).then((res)=>{\n                        if (res.status === 200) {\n                            return res.json();\n                        }\n                        // if (res.status === 401){\n                        //   toast.error(\"Your session has expired. Please log in again.\");\n                        //   setTimeout(() => {\n                        //     const redirectUrl = `/login?redirect=${encodeURIComponent(\n                        //       window.location.pathname\n                        //     )}`;\n                        //     logoutHandler(instance, redirectUrl);\n                        //   }, 3000);\n                        //   return null;\n                        // }\n                        return Promise.reject(res);\n                    }).then((json)=>{\n                        if (json.status == 200) {\n                            return true;\n                        }\n                    }).catch((error)=>{\n                        console.log(error);\n                    });\n                }\n            }\n        });\n    }\n    const buffer = await workbook.xlsx.writeBuffer();\n    const blob = new Blob([\n        buffer\n    ], {\n        type: \"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet\"\n    });\n    const blobUrl = URL.createObjectURL(blob);\n    const a = document.createElement(\"a\");\n    a.style.display = \"none\";\n    a.href = blobUrl;\n    const now = new Date();\n    const timestamp = \"\".concat(now.getFullYear(), \"-\").concat((now.getMonth() + 1).toString().padStart(2, \"0\"), \"-\").concat(now.getDate().toString().padStart(2, \"0\"), \"_\").concat(now.getHours().toString().padStart(2, \"0\"), \"-\").concat(now.getMinutes().toString().padStart(2, \"0\"), \"-\").concat(now.getSeconds().toString().padStart(2, \"0\"));\n    if (isInternal) {\n        a.download = \"internal_export_\".concat(timestamp, \"_\").concat(data[0].length - 1 === 1 ? \"S\" : \"G\", \".xlsx\");\n    } else if (isProductExtract) {\n        if (request_no && !varietyRequest) {\n            a.download = \"\".concat(request_no, \"_product_export.xlsx\");\n        } else if (request_no && varietyRequest) {\n            a.download = \"\".concat(request_no, \"_export.xlsx\");\n        } else {\n            a.download = \"product_export.xlsx\";\n        }\n    } else {\n        a.download = \"iss_export_\".concat(timestamp, \"_\").concat(data.length === 1 ? \"S\" : \"G\", \".xlsx\");\n    }\n    let fileName;\n    console.log(\"fle name\", fileName);\n    if (isInternal) {\n        fileName = \"internal_export_\".concat(timestamp, \"_\").concat(data[0].length - 1 === 1 ? \"S\" : \"G\", \".xlsx\");\n    } else if (isProductExtract && !varietyRequest) {\n        fileName = request_no ? \"\".concat(request_no, \"_product_export.xlsx\") : \"product_export.xlsx\";\n    } else if (varietyRequest) {\n        fileName = request_no ? \"\".concat(request_no, \"_NV_export.xlsx\") : \"NV_export.xlsx\";\n    } else {\n        fileName = \"iss_export_\".concat(timestamp, \"_\").concat(data.length === 1 ? \"S\" : \"G\", \".xlsx\");\n    }\n    console.log(\"fle name --- \\n\", fileName);\n    document.body.appendChild(a);\n    a.click();\n    // Clean up\n    URL.revokeObjectURL(blobUrl);\n    document.body.removeChild(a);\n    react_toastify__WEBPACK_IMPORTED_MODULE_1__.toast.success(\"Request details extracted successfully.\", {\n        toastId: 22,\n        position: \"top-right\",\n        autoClose: 3000,\n        pauseOnHover: false\n    });\n    if (!varietyRequest) {\n        const formData = new FormData();\n        formData.append(\"file\", blob, fileName);\n        formData.append(\"company\", company);\n        formData.append(\"prophet_id\", prophet_id);\n        formData.append(\"name\", userData === null || userData === void 0 ? void 0 : userData.name);\n        formData.append(\"isInternal\", isInternal);\n        formData.append(\"exporterEmail\", userData === null || userData === void 0 ? void 0 : userData.email);\n        formData.append(\"requestorEmail\", requestor_email);\n        formData.append(\"isProductRequest\", isProductExtract);\n        formData.append(\"supplierNames\", JSON.stringify(supplierNames));\n        formData.append(\"onProductSubmit\", onProductSubmit);\n        formData.append(\"request_no\", request_no);\n        formData.append(\"productEmailCommentPlaceholder\", productEmailCommentPlaceholder);\n        formData.append(\"productEmailParagraph\", productEmailParagraph);\n        const serializedData = JSON.stringify(isEmergencyAndFinanceNotCompleteObj);\n        formData.append(\"isEmergencyAndFinanceNotCompleteObj\", serializedData);\n        const response = await fetch(\"\".concat(serverAddress, \"email/send-email\"), {\n            method: \"POST\",\n            body: formData,\n            credentials: \"include\"\n        });\n        if (response.ok) {\n            react_toastify__WEBPACK_IMPORTED_MODULE_1__.toast.success(\"Email sent\", {\n                toastId: 22,\n                position: \"top-right\",\n                autoClose: 3000,\n                pauseOnHover: false\n            });\n        } else if (response.status === 401) {\n            // console.log(\"error YES 401\");\n            react_toastify__WEBPACK_IMPORTED_MODULE_1__.toast.error(\"Failed to send email\", {\n                toastId: 22,\n                position: \"top-right\",\n                autoClose: 3000,\n                pauseOnHover: false\n            });\n            return response.status;\n        } else {\n            react_toastify__WEBPACK_IMPORTED_MODULE_1__.toast.error(\"Failed to send email\", {\n                toastId: 22,\n                position: \"top-right\",\n                autoClose: 3000,\n                pauseOnHover: false\n            });\n        }\n    }\n    return true;\n};\n/* harmony default export */ __webpack_exports__[\"default\"] = (exportExcel);\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevExports = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevExports) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports on update so we can compare the boundary\n                // signatures.\n                module.hot.dispose(function (data) {\n                    data.prevExports = currentExports;\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevExports !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevExports, currentExports)) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevExports !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./utils/exportExcel.js\n"));

/***/ })

});