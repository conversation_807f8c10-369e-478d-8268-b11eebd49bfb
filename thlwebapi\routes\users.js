"use strict";
 
const express = require("express");
const userController = require("../controllers/users");
const router = express.Router();
const { requireRole } = require('../middleware/roleAuth');

const {
    getUsers,
    getUserById,
    getUserByEmail,
    getTheme,
    addUser,
    updateUser,
    deleteUser,updateUsers
} = userController;
 
router.use(requireRole([1, 5, 6]));
router.get("/get-users", getUsers);
router.get("/user/:id", getUserById);
router.post("/getUserByEmail", getUserByEmail);
router.post("/getTheme", getTheme);
router.post("/add-user", addUser);
router.put("/update-user/:id", updateUser);
router.delete("/delete-user/:id", deleteUser);
router.post("/update-users", updateUsers);
 
module.exports =router;