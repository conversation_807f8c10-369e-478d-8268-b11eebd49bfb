"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/raw-material-request/add",{

/***/ "./components/DrawerComponent.js":
/*!***************************************!*\
  !*** ./components/DrawerComponent.js ***!
  \***************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DrawerComponent: function() { return /* binding */ DrawerComponent; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _fortawesome_react_fontawesome__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @fortawesome/react-fontawesome */ \"./node_modules/@fortawesome/react-fontawesome/index.es.js\");\n/* harmony import */ var _fortawesome_free_solid_svg_icons__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @fortawesome/free-solid-svg-icons */ \"./node_modules/@fortawesome/free-solid-svg-icons/index.mjs\");\n/* harmony import */ var _fluentui_react_components__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @fluentui/react-components */ \"./node_modules/@fluentui/react-components/lib/index.js\");\n/* harmony import */ var _fluentui_react_icons__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @fluentui/react-icons */ \"./node_modules/@fluentui/react-icons/lib/index.js\");\n/* harmony import */ var ag_grid_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ag-grid-react */ \"./node_modules/ag-grid-react/lib/main.js\");\n/* harmony import */ var ag_grid_react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(ag_grid_react__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _services_apiConfig__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/services/apiConfig */ \"./services/apiConfig.js\");\n/* harmony import */ var react_toastify__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! react-toastify */ \"./node_modules/react-toastify/dist/react-toastify.esm.mjs\");\n/* harmony import */ var react_toastify_dist_ReactToastify_css__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! react-toastify/dist/ReactToastify.css */ \"./node_modules/react-toastify/dist/ReactToastify.css\");\n/* harmony import */ var react_toastify_dist_ReactToastify_css__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(react_toastify_dist_ReactToastify_css__WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var _utils_renderer_productActionRenderer__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/utils/renderer/productActionRenderer */ \"./utils/renderer/productActionRenderer.js\");\n/* harmony import */ var _utils_renderer_productReferenceRenderer__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/utils/renderer/productReferenceRenderer */ \"./utils/renderer/productReferenceRenderer.js\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n // Import useRef and useCallback\n\n\n\n\n\nconst useStyles = (0,_fluentui_react_components__WEBPACK_IMPORTED_MODULE_9__.makeStyles)({\n    root: {\n        ..._fluentui_react_components__WEBPACK_IMPORTED_MODULE_9__.shorthands.border(\"2px\", \"solid\", \"#ccc\"),\n        ..._fluentui_react_components__WEBPACK_IMPORTED_MODULE_9__.shorthands.overflow(\"hidden\"),\n        display: \"flex\",\n        height: \"480px\",\n        backgroundColor: \"#fff\"\n    },\n    content: {\n        ..._fluentui_react_components__WEBPACK_IMPORTED_MODULE_9__.shorthands.flex(1),\n        ..._fluentui_react_components__WEBPACK_IMPORTED_MODULE_9__.shorthands.padding(\"16px\"),\n        display: \"grid\",\n        justifyContent: \"flex-start\",\n        alignItems: \"flex-start\",\n        gridRowGap: _fluentui_react_components__WEBPACK_IMPORTED_MODULE_9__.tokens.spacingVerticalXXL,\n        gridAutoRows: \"max-content\"\n    },\n    field: {\n        display: \"grid\",\n        gridRowGap: _fluentui_react_components__WEBPACK_IMPORTED_MODULE_9__.tokens.spacingVerticalS\n    }\n});\nconst DrawerComponent = (param)=>{\n    let { isDrawerOpen, setIsDrawerOpen, title, dropdownData, placeholderText, legend, max_length, min_length, dataKey, onNewDropdownData, username, useremail, userData, prophetId } = param;\n    _s();\n    const [type, setType] = react__WEBPACK_IMPORTED_MODULE_1__.useState(\"overlay\");\n    const [description, setDescription] = react__WEBPACK_IMPORTED_MODULE_1__.useState(\"\");\n    const [removedRows, setRemovedRows] = react__WEBPACK_IMPORTED_MODULE_1__.useState(\"\");\n    const [value, setValue] = react__WEBPACK_IMPORTED_MODULE_1__.useState(\"\");\n    const [isButtonDisabled, setIsButtonDisabled] = react__WEBPACK_IMPORTED_MODULE_1__.useState(true);\n    const [isUpdateMode, setIsUpdateMode] = react__WEBPACK_IMPORTED_MODULE_1__.useState(false);\n    const [code, setCode] = react__WEBPACK_IMPORTED_MODULE_1__.useState(\"\");\n    const [rowData, setRowData] = react__WEBPACK_IMPORTED_MODULE_1__.useState([]);\n    const [addNewValue, setAddNewValue] = react__WEBPACK_IMPORTED_MODULE_1__.useState(false);\n    //const [keyy, setKey] =  React.useState(dataKey ? dataKey : \"\")\n    const serverAddress = _services_apiConfig__WEBPACK_IMPORTED_MODULE_4__.apiConfig.serverAddress;\n    const [isValidDescription, setisValidDescription] = react__WEBPACK_IMPORTED_MODULE_1__.useState(true);\n    const [isValidCode, setisValidCode] = react__WEBPACK_IMPORTED_MODULE_1__.useState(true);\n    // Define gridRef using useRef\n    const gridRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Define onFilterTextBoxChanged function using useCallback\n    const onFilterTextBoxChanged = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        gridRef.current.api.setQuickFilter(document.getElementById(\"filter-text-box\").value);\n    // setGridApi(params.api);\n    }, []);\n    const formatName = (params)=>{\n        // Check condition and return formatted value\n        // console.log(typeof params.data.is_new);\n        if (params.data.is_new === true) {\n            // console.log(\"inside here\");\n            // console.log(params.data.name);\n            return \"*\".concat(params.data.label);\n        }\n    //return null;\n    };\n    const formatCode = (params)=>{\n        // Check condition and return formatted value\n        if (params.data.is_new === true) {\n            // console.log(params.data.code);\n            return \"*\".concat(params.data.code);\n        }\n    //return null;\n    };\n    // Define defaultColDef and columnDefs using useMemo\n    const defaultColDef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>({\n            sortable: true,\n            filter: false,\n            resizable: true,\n            flex: 1,\n            suppressMenu: false\n        }), []);\n    const trimInputText = (input)=>{\n        return input.trim();\n    };\n    react__WEBPACK_IMPORTED_MODULE_1__.useEffect(()=>{\n        if (!addNewValue) {\n            setRowData(dropdownData);\n        }\n    }, []);\n    const columnDefs = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>[\n            {\n                headerName: \"Name\",\n                field: \"label\",\n                flex: \"3%\",\n                headerClass: \"header-with-border\",\n                cellStyle: {\n                    display: \"flex\"\n                },\n                valueFormatter: formatName,\n                cellStyle: function(params) {\n                    if (params.data.is_new === true || params.data.is_new === 1) {\n                        //Here you can check the value and based on that you can change the color\n                        return {\n                            color: \"red\"\n                        };\n                    } else {\n                        return null;\n                    }\n                }\n            },\n            {\n                headerName: \"Code\",\n                field: \"code\",\n                flex: \"2%\",\n                headerClass: \"header-with-border\",\n                cellStyle: {\n                    display: \"flex\"\n                },\n                valueFormatter: formatCode,\n                cellStyle: function(params) {\n                    if (params.data.is_new === true || params.data.is_new === 1) {\n                        //Here you can check the value and based on that you can change the color\n                        return {\n                            color: \"red\"\n                        };\n                    } else {\n                        return null;\n                    }\n                }\n            },\n            {\n                headerName: \"Is New\",\n                field: \"is_new\",\n                hide: true\n            },\n            {\n                headerName: \"Action\",\n                field: \"\",\n                headerClass: \"header-with-border\",\n                cellRenderer: (params)=>(0,_utils_renderer_productReferenceRenderer__WEBPACK_IMPORTED_MODULE_8__[\"default\"])(params, userData, isUpdateMode),\n                // cellRenderer: () => addRow(),\n                flex: \"2%\",\n                cellStyle: ()=>({\n                        display: \"flex\",\n                        justifyContent: \"center\",\n                        paddingRight: \"20px\"\n                    }),\n                sortable: false,\n                cellRendererParams: {\n                    setCode: setCode,\n                    setDescription: setDescription,\n                    setValue: setValue,\n                    setIsUpdateMode: setIsUpdateMode,\n                    setIsButtonDisabled: setIsButtonDisabled,\n                    isUpdateMode: isUpdateMode\n                },\n                onCellClicked: ()=>{\n                    setisValidDescription(true);\n                    setisValidCode(true);\n                // addRow();\n                }\n            }\n        ], []);\n    // console.log(\"isUpdateMode\",isUpdateMode);\n    // console.log('description: ',description,'\\ncode: ', code,'\\nvalue: ', value)\n    const handelupdate = ()=>{\n        const checkDescription = rowData.find((desc)=>desc.label == description && desc.value != value);\n        // console.log(code);\n        const checkProduct = rowData.find((product)=>product.code == code && product.value != value);\n        if (checkDescription) {\n            setisValidDescription(false);\n        // console.log(\"yes\");\n        } else {\n            setisValidDescription(true);\n        // console.log(\"no\");\n        }\n        if (checkProduct) {\n            setisValidCode(false);\n        // console.log(\"yes yes\");\n        } else {\n            setisValidCode(true);\n        // console.log(\"no no\");\n        }\n        if (!checkDescription && !checkProduct) {\n            const saveData = {\n                value: value,\n                description: description,\n                code: code,\n                is_new: true,\n                tableName: dataKey,\n                username: username,\n                useremail: useremail,\n                prophetId: prophetId\n            };\n            try {\n                fetch(\"\".concat(serverAddress, \"products/update-all-dropdown-value\"), {\n                    method: \"PUT\",\n                    headers: {\n                        \"Content-Type\": \"application/json\"\n                    },\n                    credentials: \"include\",\n                    body: JSON.stringify(saveData)\n                }).then((res)=>{\n                    if (res.status === 401) {\n                        react_toastify__WEBPACK_IMPORTED_MODULE_5__.toast.error(\"Your session has expired. Please log in again.\");\n                        setTimeout(async ()=>{\n                            await logout();\n                            router.push(\"/login\");\n                        }, 3000);\n                    }\n                    if (res.status === 200) {\n                        return res.json();\n                    } else {\n                        react_toastify__WEBPACK_IMPORTED_MODULE_5__.toast.error(\"Failed to save raw material.\");\n                    //setLoading(false);\n                    }\n                }).then((json)=>{\n                    // console.log(json);\n                    if (dataKey == \"masterProductCode\") {\n                        onNewDropdownData(dataKey, json.masterProductCode);\n                        setRowData(json.masterProductCode);\n                        react_toastify__WEBPACK_IMPORTED_MODULE_5__.toast.success(\"Updated New Master Code.\", {\n                            position: \"top-left\"\n                        });\n                    } else if (dataKey == \"markVariety\") {\n                        setRowData(json.markVariety);\n                        onNewDropdownData(dataKey, json.markVariety);\n                        react_toastify__WEBPACK_IMPORTED_MODULE_5__.toast.success(\"Updated New Mark Variety.\", {\n                            position: \"top-left\"\n                        });\n                    } else if (dataKey == \"brand\") {\n                        setRowData(json.brand);\n                        onNewDropdownData(dataKey, json.brand);\n                        react_toastify__WEBPACK_IMPORTED_MODULE_5__.toast.success(\"Updated New Brand.\", {\n                            position: \"top-left\"\n                        });\n                    } else if (dataKey == \"end_customer\") {\n                        setRowData(json.endCustomer);\n                        onNewDropdownData(dataKey, json.endCustomer);\n                        react_toastify__WEBPACK_IMPORTED_MODULE_5__.toast.success(\"Updated New End Customer.\", {\n                            position: \"top-left\"\n                        });\n                    } else if (dataKey == \"countryOfOrigin\") {\n                        setRowData(json.countryOfOrigin);\n                        onNewDropdownData(dataKey, json.countryOfOrigin);\n                        react_toastify__WEBPACK_IMPORTED_MODULE_5__.toast.success(\"Updated New Country of Origin.\", {\n                            position: \"top-left\"\n                        });\n                    } else if (dataKey == \"caliberSize\") {\n                        setRowData(json.caliberSize);\n                        onNewDropdownData(dataKey, json.caliberSize);\n                        react_toastify__WEBPACK_IMPORTED_MODULE_5__.toast.success(\"Updated New Caliber Size.\", {\n                            position: \"top-left\"\n                        });\n                    } else if (dataKey == \"variety\") {\n                        setRowData(json.variety);\n                        onNewDropdownData(dataKey, json.variety);\n                        react_toastify__WEBPACK_IMPORTED_MODULE_5__.toast.success(\"Updated New Variety.\", {\n                            position: \"top-left\"\n                        });\n                    } else if (dataKey == \"newOuterBoxType\") {\n                        setRowData(json.newOuterBoxType);\n                        onNewDropdownData(dataKey, json.newOuterBoxType);\n                        react_toastify__WEBPACK_IMPORTED_MODULE_5__.toast.success(\"Updated New Box Type.\", {\n                            position: \"top-left\"\n                        });\n                    }\n                    setDescription(\"\");\n                    setCode(\"\");\n                    setIsButtonDisabled(true);\n                    setIsUpdateMode(false);\n                    setAddNewValue(true);\n                //setIsDrawerOpen(false);\n                //alert(\"after new add\")\n                });\n            } catch (error) {\n                // toast.error(\"Failed to save reference code.\", {\n                //   position: \"top-left\",\n                // });\n                console.error(\"Failed to save reference code.\", error);\n            //setLoading(false);\n            }\n        } else {\n            // toast.error(\"Product code or description already exist.\", {\n            //   position: \"top-left\",\n            // });\n            console.error(\"Failed to save reference code.\");\n        }\n    };\n    const handleAdd = ()=>{\n        const checkDescription = rowData.find((desc)=>desc.label == description);\n        const checkProduct = rowData.find((product)=>product.code == code);\n        if (checkDescription) {\n            setisValidDescription(false);\n        } else {\n            setisValidDescription(true);\n        }\n        if (checkProduct) {\n            setisValidCode(false);\n        } else {\n            setisValidCode(true);\n        }\n        // console.log('checkDescription: ',checkDescription,'\\n checkProduct: ', checkProduct)\n        if (!checkDescription && !checkProduct) {\n            const saveData = {\n                description: description,\n                code: code,\n                is_new: true,\n                tableName: dataKey,\n                username: username,\n                useremail: useremail,\n                prophetId: prophetId\n            };\n            try {\n                fetch(\"\".concat(serverAddress, \"products/add-all-dropdown-value\"), {\n                    method: \"POST\",\n                    headers: {\n                        \"Content-Type\": \"application/json\",\n                        Authorization: \"Bearer \".concat(userData.token)\n                    },\n                    body: JSON.stringify(saveData)\n                }).then((res)=>{\n                    if (res.status === 200) {\n                        return res.json();\n                    } else {\n                        react_toastify__WEBPACK_IMPORTED_MODULE_5__.toast.error(\"Failed to save raw material.\");\n                    //setLoading(false);\n                    }\n                }).then((json)=>{\n                    // console.log(json);\n                    if (dataKey == \"masterProductCode\") {\n                        onNewDropdownData(dataKey, json.masterProductCode);\n                        setRowData(json.masterProductCode);\n                        react_toastify__WEBPACK_IMPORTED_MODULE_5__.toast.success(\"Added New Master Code.\", {\n                            position: \"top-left\"\n                        });\n                        setDescription(\"\");\n                        setCode(\"\");\n                        setIsButtonDisabled(true);\n                    } else if (dataKey == \"markVariety\") {\n                        setRowData(json.markVariety);\n                        onNewDropdownData(dataKey, json.markVariety);\n                        react_toastify__WEBPACK_IMPORTED_MODULE_5__.toast.success(\"Added New Mark Variety.\", {\n                            position: \"top-left\"\n                        });\n                        setDescription(\"\");\n                        setCode(\"\");\n                        setIsButtonDisabled(true);\n                    } else if (dataKey == \"brand\") {\n                        setRowData(json.brand);\n                        onNewDropdownData(dataKey, json.brand);\n                        react_toastify__WEBPACK_IMPORTED_MODULE_5__.toast.success(\"Added New Brand.\", {\n                            position: \"top-left\"\n                        });\n                        setDescription(\"\");\n                        setCode(\"\");\n                        setIsButtonDisabled(true);\n                    } else if (dataKey == \"end_customer\") {\n                        setRowData(json.endCustomer);\n                        onNewDropdownData(dataKey, json.endCustomer);\n                        react_toastify__WEBPACK_IMPORTED_MODULE_5__.toast.success(\"Added New End Customer.\", {\n                            position: \"top-left\"\n                        });\n                        setDescription(\"\");\n                        setCode(\"\");\n                        setIsButtonDisabled(true);\n                    } else if (dataKey == \"countryOfOrigin\") {\n                        setRowData(json.countryOfOrigin);\n                        onNewDropdownData(dataKey, json.countryOfOrigin);\n                        react_toastify__WEBPACK_IMPORTED_MODULE_5__.toast.success(\"Added New Country of Origin.\", {\n                            position: \"top-left\"\n                        });\n                        setDescription(\"\");\n                        setCode(\"\");\n                        setIsButtonDisabled(true);\n                    } else if (dataKey == \"caliberSize\") {\n                        setRowData(json.caliberSize);\n                        onNewDropdownData(dataKey, json.caliberSize);\n                        react_toastify__WEBPACK_IMPORTED_MODULE_5__.toast.success(\"Added New Caliber Size.\", {\n                            position: \"top-left\"\n                        });\n                        setDescription(\"\");\n                        setCode(\"\");\n                        setIsButtonDisabled(true);\n                    } else if (dataKey == \"variety\") {\n                        setRowData(json.variety);\n                        onNewDropdownData(dataKey, json.variety);\n                        react_toastify__WEBPACK_IMPORTED_MODULE_5__.toast.success(\"Added New Variety.\", {\n                            position: \"top-left\"\n                        });\n                        setDescription(\"\");\n                        setCode(\"\");\n                        setIsButtonDisabled(true);\n                    } else if (dataKey == \"newOuterBoxType\") {\n                        setRowData(json.newOuterBoxType);\n                        onNewDropdownData(dataKey, json.newOuterBoxType);\n                        react_toastify__WEBPACK_IMPORTED_MODULE_5__.toast.success(\"Added New Box Type.\", {\n                            position: \"top-left\"\n                        });\n                        setDescription(\"\");\n                        setCode(\"\");\n                        setIsButtonDisabled(true);\n                    }\n                    setAddNewValue(true);\n                });\n            } catch (error) {\n                console.error(\"Failed to save reference code.\", error);\n            }\n        } else {\n            console.error(\"Failed to save reference code.\");\n        }\n    };\n    const getRowStyle = (params)=>{\n        if (params.data.is_new === true) {\n            // Apply custom styling for rows where is_new is true\n            return {\n                color: \"red !important\"\n            }; // Example background color\n        }\n        return null;\n    };\n    return(// <div className={styles.root}>\n    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_toastify__WEBPACK_IMPORTED_MODULE_5__.ToastContainer, {\n                limit: 1\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\DrawerComponent.js\",\n                lineNumber: 468,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fluentui_react_components__WEBPACK_IMPORTED_MODULE_9__.Drawer, {\n                    type: type,\n                    separator: true,\n                    open: isDrawerOpen,\n                    position: \"end\",\n                    className: \"!bg-white !w-[450px] p-3 px-5 !shadow-lg !border-0\",\n                    onOpenChange: (_, param)=>{\n                        let { open } = param;\n                        return setIsDrawerOpen(open);\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fluentui_react_components__WEBPACK_IMPORTED_MODULE_9__.DrawerHeader, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fluentui_react_components__WEBPACK_IMPORTED_MODULE_9__.DrawerHeaderTitle, {\n                                action: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fluentui_react_components__WEBPACK_IMPORTED_MODULE_9__.Button, {\n                                    appearance: \"subtle\",\n                                    \"aria-label\": \"Close\",\n                                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fluentui_react_icons__WEBPACK_IMPORTED_MODULE_10__.Dismiss24Regular, {}, void 0, false, void 0, void 0),\n                                    onClick: ()=>setIsDrawerOpen(false)\n                                }, void 0, false, void 0, void 0),\n                                className: \"font-bold\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"font-bold\",\n                                    children: title\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\DrawerComponent.js\",\n                                    lineNumber: 490,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\DrawerComponent.js\",\n                                lineNumber: 479,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\DrawerComponent.js\",\n                            lineNumber: 478,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fluentui_react_components__WEBPACK_IMPORTED_MODULE_9__.DrawerBody, {\n                            className: \"!max-h-full !overflow-hidden\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"border rounded-md relative mt-3 mb-3 \",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"absolute -top-3 left-5 bg-white z-50 w-auto inline px-3\",\n                                                style: {\n                                                    opacity: !isUpdateMode ? 1 : 0,\n                                                    transform: !isUpdateMode ? \"scale(1)\" : \"scale(0.9)\",\n                                                    transition: \"opacity 0.1s ease, transform 0.1s ease\",\n                                                    pointerEvents: !isUpdateMode ? \"auto\" : \"none\"\n                                                },\n                                                children: legend\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\DrawerComponent.js\",\n                                                lineNumber: 497,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                onClick: ()=>{\n                                                    setIsUpdateMode(false);\n                                                    setIsButtonDisabled(true);\n                                                    setDescription(\"\");\n                                                    setCode(\"\");\n                                                    // setRemovedRows(\"\");\n                                                    setValue(\"\");\n                                                    setisValidCode(true);\n                                                    setisValidDescription(true);\n                                                // addRow();\n                                                // console.log(\"removedRows\",removedRows);\n                                                },\n                                                style: {\n                                                    opacity: isUpdateMode ? 1 : 0,\n                                                    transform: isUpdateMode ? \"scale(1)\" : \"scale(0.9)\",\n                                                    transition: \"opacity 0.1s ease, transform 0.1s ease\",\n                                                    pointerEvents: isUpdateMode ? \"auto\" : \"none\"\n                                                },\n                                                className: \"absolute flex items-center justify-center border rounded-md border-skin-primary -top-3 right-5 text-skin-primary bg-white z-50 cursor-pointer shadow-sm hover:bg-gray-50 transition-all duration-300 ease-in-out\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fortawesome_react_fontawesome__WEBPACK_IMPORTED_MODULE_2__.FontAwesomeIcon, {\n                                                        icon: _fortawesome_free_solid_svg_icons__WEBPACK_IMPORTED_MODULE_11__.faPlus,\n                                                        className: \"px-1 py-1 text-skin-primary cursor-pointer rotate-45 transition-all duration-300 ease-in-out transform\",\n                                                        title: \"Go back to add new \".concat(title)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\DrawerComponent.js\",\n                                                        lineNumber: 530,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"mr-[5px]\",\n                                                        children: \"Cancel\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\DrawerComponent.js\",\n                                                        lineNumber: 535,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\DrawerComponent.js\",\n                                                lineNumber: 509,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"hidden\",\n                                                name: \"type\",\n                                                value: \"product\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\DrawerComponent.js\",\n                                                lineNumber: 538,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex flex-col w-full p-4 pt-5 overflow-hidden\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        id: \"description\",\n                                                        name: \"description\",\n                                                        maxLength: 50,\n                                                        className: \"w-full py-1 px-2 2xl:px-3 2xl:py-3 border rounded-md\",\n                                                        placeholder: \"Description\",\n                                                        onChange: (e)=>{\n                                                            setDescription(e.target.value);\n                                                            setisValidDescription(true);\n                                                            setIsButtonDisabled(e.target.value === \"\" || code === \"\");\n                                                        },\n                                                        onBlur: (e)=>{\n                                                            const trimmedValue = trimInputText(e.target.value);\n                                                            setDescription(trimmedValue);\n                                                            setisValidDescription(true);\n                                                            setIsButtonDisabled(trimmedValue === \"\" || code === \"\");\n                                                        },\n                                                        // style={{ textTransform: \"capitalize\" }}\n                                                        value: description\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\DrawerComponent.js\",\n                                                        lineNumber: 540,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    !isValidDescription && // ?\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-red-500 text-xs mt-1 ml-1\",\n                                                        children: \"Description Exists Please Enter Different Description\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\DrawerComponent.js\",\n                                                        lineNumber: 563,\n                                                        columnNumber: 23\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex flex-row mt-2 transition-all duration-300 ease-in-out\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex flex-col w-full \",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                        id: \"addNew\",\n                                                                        name: \"add new\",\n                                                                        className: \"w-full py-1 px-2 2xl:px-3 2xl:py-3 border rounded-md \",\n                                                                        onChange: (e)=>{\n                                                                            // console.log(\"max_length\",min_length - 1, e.target.value.length);\n                                                                            // console.log(\"hi:\");\n                                                                            if (e.target.value.length <= max_length) {\n                                                                                setCode(e.target.value.toUpperCase());\n                                                                                setisValidCode(true);\n                                                                                setIsButtonDisabled(e.target.value === \"\" || description === \"\" || (typeof e.target.value === \"number\" ? e.target.value.toString().length < min_length : e.target.value.length < min_length));\n                                                                            }\n                                                                        },\n                                                                        onBlur: (e)=>{\n                                                                            const trimmedValue = trimInputText(e.target.value);\n                                                                            if (trimmedValue.length <= max_length) {\n                                                                                setCode(trimmedValue.toUpperCase());\n                                                                                setisValidCode(true);\n                                                                                setIsButtonDisabled(e.target.value === \"\" || description === \"\" || (typeof e.target.value === \"number\" ? e.target.value.toString().length < min_length : e.target.value.length < min_length));\n                                                                            } else {\n                                                                                setisValidCode(false);\n                                                                            }\n                                                                        },\n                                                                        placeholder: placeholderText,\n                                                                        // style={{ textTransform: \"uppercase\" }}\n                                                                        value: code\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\DrawerComponent.js\",\n                                                                        lineNumber: 575,\n                                                                        columnNumber: 23\n                                                                    }, undefined),\n                                                                    !isValidCode && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-red-500 text-xs mt-1 ml-1\",\n                                                                        children: \"Code Already Exists\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\DrawerComponent.js\",\n                                                                        lineNumber: 616,\n                                                                        columnNumber: 25\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\DrawerComponent.js\",\n                                                                lineNumber: 574,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                className: \"border border-skin-primary text-white bg-skin-primary py-1 px-5 ml-4 \".concat(!isValidCode ? \"mb-[18px]\" : \"\", ' w-[130px] font-medium rounded-md scale-x-100\"\\n                      }'),\n                                                                onClick: isUpdateMode ? handelupdate : handleAdd,\n                                                                disabled: isButtonDisabled,\n                                                                children: isUpdateMode ? \"Update\" : \"Add New\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\DrawerComponent.js\",\n                                                                lineNumber: 621,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\DrawerComponent.js\",\n                                                        lineNumber: 573,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\DrawerComponent.js\",\n                                                lineNumber: 539,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\DrawerComponent.js\",\n                                        lineNumber: 496,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-col\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"relative block w-full text-gray-400 mt-0 pt-0 mb-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"absolute px-4 py-1 pt-[0.4rem] 2xl:pt-1.5 text-black\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fortawesome_react_fontawesome__WEBPACK_IMPORTED_MODULE_2__.FontAwesomeIcon, {\n                                                            icon: _fortawesome_free_solid_svg_icons__WEBPACK_IMPORTED_MODULE_11__.faSearch,\n                                                            className: \"fw-bold\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\DrawerComponent.js\",\n                                                            lineNumber: 637,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\DrawerComponent.js\",\n                                                        lineNumber: 636,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"text\",\n                                                        id: \"filter-text-box\",\n                                                        placeholder: \"Search\",\n                                                        onInput: onFilterTextBoxChanged,\n                                                        className: \"block w-full px-4 pl-10 text-gray-500 placeholder-gray-400 searchbar border rounded-lg appearance-none form-input focus:outline-none\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\DrawerComponent.js\",\n                                                        lineNumber: 639,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\DrawerComponent.js\",\n                                                lineNumber: 635,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"!rounded-md px-5 border border-gray-300\",\n                                                style: {\n                                                    height: \"calc(100vh - 210px)\"\n                                                },\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ag_grid_react__WEBPACK_IMPORTED_MODULE_3__.AgGridReact, {\n                                                    rowData: rowData,\n                                                    ref: gridRef,\n                                                    columnDefs: columnDefs,\n                                                    defaultColDef: defaultColDef,\n                                                    getRowStyle: getRowStyle\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\DrawerComponent.js\",\n                                                    lineNumber: 651,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\DrawerComponent.js\",\n                                                lineNumber: 647,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\DrawerComponent.js\",\n                                        lineNumber: 634,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\DrawerComponent.js\",\n                                lineNumber: 495,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\DrawerComponent.js\",\n                            lineNumber: 494,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\DrawerComponent.js\",\n                    lineNumber: 470,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\DrawerComponent.js\",\n                lineNumber: 469,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true));\n};\n_s(DrawerComponent, \"zpbapB7wDtu66vJweoDKOcOYtUI=\");\n_c = DrawerComponent;\n/* harmony default export */ __webpack_exports__[\"default\"] = (DrawerComponent);\nvar _c;\n$RefreshReg$(_c, \"DrawerComponent\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevExports = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevExports) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports on update so we can compare the boundary\n                // signatures.\n                module.hot.dispose(function (data) {\n                    data.prevExports = currentExports;\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevExports !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevExports, currentExports)) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevExports !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/DrawerComponent.js\n"));

/***/ })

});