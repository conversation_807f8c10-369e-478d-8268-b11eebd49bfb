import React, {useMemo, useState} from 'react';
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import {
  faSearch,
} from "@fortawesome/free-solid-svg-icons";
import Sidebar from './sidebar';
import Navbar from './navbar';
import {AgGridReact} from 'ag-grid-react';

import 'ag-grid-community/styles//ag-grid.css';
import 'ag-grid-community/styles//ag-theme-alpine.css';
import actionRenderer from '../utils/renderer/actionRenderer';
import nameRenderer from '../utils/renderer/nameRenderer';
import statusRenderer from '../utils/renderer/statusRenderer';
import Link from 'next/link';

const SupplierList = () => {
	const [rowData, setData] = useState([
		{
			supplierName:'Packaging house limited',
			roles:'Packhouse',
			companies: 'DPS',
			currency: '£',
			requestor: '<PERSON>',
			compliance: 'Complete',
			financial: 'Incomplete',
			technical: 'Incomplete',
			procurement: 'Incomplete',
		},
		{
			supplierName:'Packaging house limited',
			roles:'Packhouse',
			companies: 'DPS',
			currency: '£',
			requestor: 'Leon Stourt',
			compliance: 'Complete',
			financial: 'Incomplete',
			technical: 'Incomplete',
			procurement: 'Incomplete',
		}
	]);
	
	const defaultColDef = useMemo(() => ({
		sortable: true,
		filter: false,
		resizable: true,
		flex: 1,
		maxWidth: 290,
		suppressMenu: false
	}));
	
	const [columnDefs, setColumnDefs] = useState([
		{ headerName: "Supplier Name", field: "supplierName", checkboxSelection: true, cellRenderer: nameRenderer, headerCheckboxSelection: true, suppressMenu: true,suppressSizeToFit: true, width: 290, maxWidth: 290},
		{ headerName: "Roles", field: "roles" },
		{ headerName: "Companies", field: "companies" },
		{ headerName: "Currency", field: "currency" },
		{ headerName: "Requestor", field: "requestor" },
		{ headerName: "Compliance", field: "compliance", cellRenderer: statusRenderer, cellStyle: () => ({color: '#3EAB58'})},
		{ headerName: "Financial", field: "financial", cellRenderer: statusRenderer, cellStyle: () => ({color: '#FBBE3E'})},
		{ headerName: "Technical", field: "technical", cellRenderer: statusRenderer, cellStyle: () => ({color: '#FBBE3E'})},
		{ headerName: "Procurement", field: "procurement", cellRenderer: statusRenderer, cellStyle: () => ({color: '#B1B2B3'})},
		{
		  field: "",
		  cellRenderer: actionRenderer,
		  cellStyle: () => ({}),
		}
	]);
	
	return(
		<div className="m-5">
			<div className="flex flex-row md:flex-col lg:flex-row justify-between">
				<div className="flex items-center border border-gray-300 rounded-md px-3 w-auto md:w-fit lg:w-auto md:py-3 lg:py-0 md:mb-3 lg:mb-0">
					<input id="checked-checkbox" type="checkbox" value="" className="w-5 h-5 text-blue border-blue-300 rounded " />
					<label htmlFor="checked-checkbox" className="p-0 ml-3 text-md font-medium text-gray-600 dark:text-gray-300">Show exported</label>
				</div>
				<div className="flex gap-6">
					<label className="relative block w-[47vh] text-gray-400 focus-within:text-gray-600 mt-0 pt-0">
					  <span className="absolute px-4 py-2.5 text-black"><FontAwesomeIcon icon={faSearch} className="fw-bold" /></span>
					  <input
						type="text"
						id="filter-text-box"
						placeholder="Search"
						className="block w-full px-4 py-2 2xl:py-3 pl-10 text-gray-500 placeholder-gray-400 bg-white border border-gray-300 rounded-lg appearance-none form-input focus:outline-none"
					  />
					</label>
					<button className="py-2 px-8 border border-blue-500 text-blue-500 rounded-md">Export</button>
					<Link href="/addSupplier">
						<button className="py-2 px-12 border border-blue-500 bg-blue-600 text-white rounded-md whitespace-nowrap">Add supplier</button>
					</Link>	
				</div>
			</div>
			<div className="my-5">
				<button data-modal-target="default-modal" data-modal-toggle="default-modal" type="button" className="text-white bg-blue-600 hover:bg-blue-800 focus:ring-4 focus:outline-none focus:ring-blue-300 font-medium rounded-md text-md px-6 py-2 text-center dark:bg-blue-600 dark:hover:bg-blue-700 dark:focus:ring-blue-800">
					Select prophet
				</button>
				<div className="relative ag-theme-alpine" style={{ height: "calc(100vh - 230px)" }}>
					<AgGridReact
					  rowData={rowData}
					  columnDefs={columnDefs}
					  defaultColDef={defaultColDef}
					  suppressRowClickSelection
					  rowSelection="multiple"
					  pagination={true}
					  paginationPageSize={20}
					  tooltipShowDelay={0}
					  tooltipHideDelay={1000}
					/>
				</div>
			</div>
		</div>
	)
}

export default SupplierList;