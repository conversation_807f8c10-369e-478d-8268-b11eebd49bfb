import { Tooltip } from "@fluentui/react-components";
import Link from "next/link";
import React, { useState, useEffect } from "react";
import { apiConfig } from "@/services/apiConfig";
import {
  filtersBasedOnZeros,
  getCalendersByQuarters,
  getProductsFromIdbByMasterCodeAndBusinessUnit,
  replaceDataWithTheNew,
  replaceEfcCalenderWithTheNew,
  replaceStdCalenderWithTheNew,
} from "@/utils/whatif/utils/indexedDB";
import { getData } from "@/utils/whatif/utils/getProductData";
//
import { useMsal } from "@azure/msal-react";
import Cookies from "js-cookie";
import { toast, ToastContainer } from "react-toastify";
export default function FilterBottomBar({
  currentSelectedKeys,
  setCurrentData,
  toggleQuaterlyTotals,
  ctxCalenderData,
  setCtxCalenderData,
  setNoOfWeeks,
  currentCustomer,
  setCtxStdCalenderData,
  setSelectedQuarterString,
  loading,
  setLoading,
  gridStates,
  setGridStates,
  checkboxes,
  setCheckboxes,
  selectWhereTotalIsZero,
  selectWhereTotalIsNotZero,
  selectedBusinessUnit,
  selectedMasterCode,
  productType,
  searchPreferedProducts,
  currentYear,isAllQuarterDataFetched,setIsAllQuarterDataFetched
}) {
  const [currentQuarter, setCurrentQuarter] = useState("");
  const { instance } = useMsal();

  useEffect(() => {
    if (currentQuarter === "") {
      let thisQuarter = ctxCalenderData[0]?.currentQuarter;
      if (thisQuarter) {
        setSelectedQuarterString(thisQuarter);
        setCurrentQuarter(thisQuarter);

        setCheckboxes((prevState) => ({
          ...prevState,
          q1: thisQuarter === 1,
          q2: thisQuarter === 2,
          q3: thisQuarter === 3,
          q4: thisQuarter === 4,
        }));
      }
    }
  }, [ctxCalenderData]);

  const handleCheckboxChange = async (e) => {
    const { id, checked, disabled } = e.target;

    const otherCheckedBoxes = Object.keys(checkboxes).filter((key) => {
      return key !== id && !!checkboxes[key];
    });
    if (otherCheckedBoxes.length <= 0) return;
    setLoading(true);

    const isAllChecked =
      Object.keys(checkboxes).filter((key) => {
        return key !== "all" && key !== id && !!checkboxes[key];
      }).length === 3 && checked;

    const newCheckboxes = {
      all: id === "all" ? checked : isAllChecked,
      q1:
        id === "all" && checked
          ? true
          : id === "q1"
          ? checked
          : id === "all" && !checked
          ? currentQuarter === 1
          : checkboxes.q1,
      q2:
        id === "all" && checked
          ? true
          : id === "q2"
          ? checked
          : id === "all" && !checked
          ? currentQuarter === 2
          : checkboxes.q2,
      q3:
        id === "all" && checked
          ? true
          : id === "q3"
          ? checked
          : id === "all" && !checked
          ? currentQuarter === 3
          : checkboxes.q3,
      q4:
        id === "all" && checked
          ? true
          : id === "q4"
          ? checked
          : id === "all" && !checked
          ? currentQuarter === 4
          : checkboxes.q4,
    };

    setCheckboxes(newCheckboxes);
    let quarters = [];
    Object.keys(newCheckboxes).forEach((key) => {
      if (key !== "all" && !!newCheckboxes[key]) {
        quarters.push(
          key === "q1" ? 1 : key === "q2" ? 2 : key === "q3" ? 3 : 4
        );
      }
    });

    const quarterString = quarters.join(",");
    setSelectedQuarterString(quarterString);
    await handleQuartersChange(quarters);
  };

  const handleQuartersChange = async (quartersSelected) => {
    const token = Cookies.get("token");

    const selectedQuarters = quartersSelected.map((q) => `q${q}`);

    if (currentCustomer) {
      if (!isAllQuarterDataFetched) {
        const data = await getData(
          `/get-required-filtered-data/${currentCustomer}?year=${currentYear.value}&quarters=1,2,3,4&totals=${false}&calendarData=${true}`,
          token
        );
        if (!data) {
          const redirectUrl = `/login?redirect=${encodeURIComponent(window.location.pathname)}`;
logoutHandler(instance,redirectUrl);;
          setLoading(false);
          return;
        }
        if (data?.length == 0) {
          console.log("error 400");
          setLoading(false);
          return;
        }
        const allQuarterwhatifCustomerData = data?.whatifData.slice(0, -1);

        replaceDataWithTheNew(allQuarterwhatifCustomerData);

        let tableDataFiltered = allQuarterwhatifCustomerData;

        if (!selectWhereTotalIsZero || !selectWhereTotalIsNotZero) {
          tableDataFiltered = filtersBasedOnZeros(
            allQuarterwhatifCustomerData,
            selectWhereTotalIsZero,
            selectWhereTotalIsNotZero,
            selectedQuarters,
            selectedMasterCode.value === "all"
              ? null
              : selectedMasterCode.value,
            selectedBusinessUnit.value === "all"
              ? null
              : selectedBusinessUnit.value
          );
        }

        if (searchPreferedProducts) {
          tableDataFiltered = tableDataFiltered.filter((p) =>
            currentSelectedKeys.includes(p.pkey)
          );
        }

        setCurrentData(tableDataFiltered);
        setIsAllQuarterDataFetched(true);

        setNoOfWeeks(data.defaultCalendarData.length);
        const thisQuartersData = data.defaultCalendarData.filter((data) =>
          quartersSelected.includes(data.fiscalquarter)
        );
        setCtxCalenderData(thisQuartersData);
        replaceEfcCalenderWithTheNew(data.defaultCalendarData);

        setCtxStdCalenderData(data.stdCalendarData);
        replaceStdCalenderWithTheNew(data.stdCalendarData);
        setLoading(false);
      } else {
        const calenderData = await getCalendersByQuarters(quartersSelected);

        let products = await getProductsFromIdbByMasterCodeAndBusinessUnit(
          selectedMasterCode.value === "all" ? null : selectedMasterCode.value,
          selectedBusinessUnit.value === "all"
            ? null
            : selectedBusinessUnit.value,
          true,
          productType,
          selectWhereTotalIsZero,
          selectWhereTotalIsNotZero,
          selectedQuarters
        );

        if (searchPreferedProducts) {
          products = products.filter((p) =>
            currentSelectedKeys.includes(p.pkey)
          );
        }

        setCurrentData(products);
        setNoOfWeeks(calenderData.efCCalender.length);
        setCtxCalenderData(calenderData.efCCalender);
        setCtxStdCalenderData(calenderData.stdCalender);
        setLoading(false);
      }
    } else {
      setProductsList([]);
    }
  };

  const handleGridCheckboxChange = (e) => {
    const { id, checked } = e.target;

    const othersSelectedCount = Object.keys(gridStates).filter(
      (gs) => gs !== id && !!gridStates[gs]
    );
    if (othersSelectedCount.length <= 0 && !checked) return;

    setGridStates((prev) => {
      return { ...prev, [id]: checked };
    });
  };

  return (
    <>
      <ToastContainer limit={1} />
      <div className="flex flex-row items-center justify-between gap-4 px-3 border-b py-1 border-gray-300">
        <div className="flex flex-row gap-1">
          <Tooltip
            content="Break Even Estimate"
            relationship="label"
            className="!bg-white"
          >
            <div className="rounded-full w-5 h-5 bg-be-status"></div>
          </Tooltip>
          <Tooltip content="Confirm" relationship="label" className="!bg-white">
            <div className="rounded-full w-5 h-5 bg-confirm-status"></div>
          </Tooltip>
          <Tooltip
            content="Work In Progress"
            relationship="label"
            className="!bg-white"
          >
            <div className="rounded-full w-5 h-5 bg-wip-status"></div>
          </Tooltip>
          <Tooltip
            content="Supplier Issue"
            relationship="label"
            className="!bg-white"
          >
            <div className="rounded-full w-5 h-5 bg-issue-status"></div>
          </Tooltip>
          <Tooltip
            content="Volumen Change"
            relationship="label"
            className="!bg-white"
          >
            <div className="rounded-full w-5 h-5 bg-volumechange-status"></div>
          </Tooltip>
          <Tooltip
            content="Price Change"
            relationship="label"
            className="!bg-white"
          >
            <div className="rounded-full w-5 h-5 bg-pricechange-status"></div>
          </Tooltip>
           <Tooltip
            content="Locked Products"
            relationship="label"
            className="!bg-white"
          >
            <div className="rounded-full w-5 h-5 bg-locked-products border border-gray-300"></div>
          </Tooltip>
        </div>

        <div className="border-r  border-gray-300 h-9 hidden 2xl:block">
          &nbsp;
        </div>

        {/* Quarter Filter start*/}
        <div className="flex gap-1 2xl:gap-3 w-[20%] xl:w-[20%] min-[1600px]:w-[23%] min-[1610px]:w-[35%] justify-between">
          <input
            className="hidden"
            type="checkbox"
            id="all"
            checked={checkboxes.all}
            onChange={handleCheckboxChange}
            disabled={loading}
          />
          <label
            htmlFor="all"
            className="pointer-cursor  labelcheck border border-gray-300 rounded-full px-4 2xl:px-6 py-1 flex items-center font-medium hover:cursor-pointer"
          >
            All
          </label>

          <input
            className="hidden"
            type="checkbox"
            id="q1"
            checked={checkboxes.q1}
            onChange={handleCheckboxChange}
            disabled={loading}
          />
          <label
            htmlFor="q1"
            className="pointer-cursor  labelcheck border border-gray-300 rounded-full px-4 2xl:px-6 py-1 flex items-center font-medium hover:cursor-pointer"
          >
            Q1 <span className="max-[1600px]:hidden min-[1610px]:inline">(01-13)</span>
          </label>

          <input
            className="hidden"
            type="checkbox"
            id="q2"
            checked={checkboxes.q2}
            onChange={handleCheckboxChange}
            disabled={loading}
          />
          <label
            htmlFor="q2"
            className="pointer-cursor  labelcheck border border-gray-300 rounded-full px-4 2xl:px-6 py-1 flex items-center font-medium hover:cursor-pointer"
          >
            Q2 <span className="max-[1600px]:hidden min-[1610px]:inline">(14-26)</span>
          </label>

          <input
            className="hidden"
            type="checkbox"
            id="q3"
            checked={checkboxes.q3}
            onChange={handleCheckboxChange}
            disabled={loading}
          />
          <label
            htmlFor="q3"
            className="pointer-cursor  labelcheck border border-gray-300 rounded-full px-4 2xl:px-6 py-1 flex items-center font-medium hover:cursor-pointer"
          >
            Q3 <span className="max-[1600px]:hidden min-[1610px]:inline">(27-39)</span>
          </label>

          <input
            className="hidden"
            type="checkbox"
            id="q4"
            checked={checkboxes.q4}
            onChange={handleCheckboxChange}
            disabled={loading}
          />
          <label
            htmlFor="q4"
            className="pointer-cursor  labelcheck border border-gray-300 rounded-full px-4 2xl:px-6 py-1 flex items-center font-medium hover:cursor-pointer"
          >
            Q4 <span className="max-[1600px]:hidden min-[1610px]:inline">(40-52)</span>
          </label>
        </div>
        {/* Quarter Filter end*/}

        <div className="border-r  border-gray-300 h-9 hidden 2xl:block">
          &nbsp;
        </div>

        {/* Section Filter start*/}
        <div className="flex gap-1 2xl:gap-3 w-[48%] xl:w-[48%] min-[1600px]:w-[42%] min-[1610px]:w-[35%] justify-between">
          <input
            className="hidden"
            type="checkbox"
            id="volume"
            checked={gridStates.volume}
            onChange={handleGridCheckboxChange}
            disabled={loading}
          />
          <label
            htmlFor="volume"
            className="pointer-cursor labelcheck border border-gray-300 rounded-full px-4 py-1 flex items-center font-medium hover:cursor-pointer"
          >
            Volume
          </label>

          <input
            className="hidden"
            type="checkbox"
            id="breakeven"
            checked={gridStates.breakeven}
            onChange={handleGridCheckboxChange}
            disabled={loading}
          />
          <label
            htmlFor="breakeven"
            className="pointer-cursor labelcheck border border-gray-300 rounded-full px-4 py-1 flex items-center font-medium hover:cursor-pointer"
          >
            <span className="hidden xl:block">Breakeven</span>
            <span className="block xl:hidden">BE</span>
          </label>

          <input
            className="hidden"
            type="checkbox"
            id="unitprice"
            checked={gridStates.unitprice}
            onChange={handleGridCheckboxChange}
            disabled={loading}
          />
          <label
            htmlFor="unitprice"
            className="pointer-cursor labelcheck border border-gray-300 rounded-full px-4 py-1 flex items-center font-medium hover:cursor-pointer"
          >
            <span className="hidden xl:block">Unit Price</span>
            <span className="block xl:hidden">UP</span>
          </label>

          <input
            className="hidden"
            type="checkbox"
            id="value"
            checked={gridStates.value}
            onChange={handleGridCheckboxChange}
            disabled={loading}
          />
          <label
            htmlFor="value"
            className="pointer-cursor labelcheck border border-gray-300 rounded-full px-4 py-1 flex items-center font-medium hover:cursor-pointer"
          >
            Value
            
          </label>

          <input
            className="hidden"
            type="checkbox"
            id="grossprofit"
            checked={gridStates.grossprofit}
            onChange={handleGridCheckboxChange}
            disabled={loading}
          />
          <label
            htmlFor="grossprofit"
            className="pointer-cursor labelcheck border border-gray-300 rounded-full px-4 py-1 flex items-center font-medium hover:cursor-pointer"
          >
            
            <span className="hidden xl:block">Gross Profit</span>
            <span className="block xl:hidden">GP</span>
          </label>

          <input
            className="hidden"
            type="checkbox"
            id="gppercent"
            checked={gridStates.gppercent}
            onChange={handleGridCheckboxChange}
            disabled={loading}
          />
          <label
            htmlFor="gppercent"
            className="pointer-cursor labelcheck border border-gray-300 rounded-full px-4 py-1 flex items-center font-medium hover:cursor-pointer"
          >
            <span className="hidden xl:block">Gross Profit %</span>
            <span className="block xl:hidden">GP%</span>
          </label>
        </div>
        {/* Section Filter end*/}

        <div className="border-r  border-gray-300 h-9 hidden 2xl:block">
          &nbsp;
        </div>

        <div className="flex flex-row gap-3">
          <Link href="/whatif/reports/masterRollingForecast">
            <div className="bg-white border border-skin-primary rounded-full px-4 py-1 text-skin-primary">
              <span className="hidden xl:inline">Forecast</span> Reports
            </div>
          </Link>

          <button
            className="bg-white border border-skin-primary rounded-full px-4 py-1 text-skin-primary"
            onClick={toggleQuaterlyTotals}
            disabled={loading}
          >
            Totals
          </button>
        </div>
      </div>
    </>
  );
}
