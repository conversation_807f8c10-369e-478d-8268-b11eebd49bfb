"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/supplier/[supplierId]/edit/forms",{

/***/ "./components/GeneralSection.js":
/*!**************************************!*\
  !*** ./components/GeneralSection.js ***!
  \**************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _fortawesome_react_fontawesome__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @fortawesome/react-fontawesome */ \"./node_modules/@fortawesome/react-fontawesome/index.es.js\");\n/* harmony import */ var _fortawesome_free_solid_svg_icons__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! @fortawesome/free-solid-svg-icons */ \"./node_modules/@fortawesome/free-solid-svg-icons/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_Dialog_Transition_headlessui_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=Dialog,Transition!=!@headlessui/react */ \"__barrel_optimize__?names=Dialog,Transition!=!./node_modules/@headlessui/react/dist/headlessui.esm.js\");\n/* harmony import */ var ag_grid_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ag-grid-react */ \"./node_modules/ag-grid-react/lib/main.js\");\n/* harmony import */ var ag_grid_react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(ag_grid_react__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var ag_grid_community_styles_ag_grid_css__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ag-grid-community/styles//ag-grid.css */ \"./node_modules/ag-grid-community/styles/ag-grid.css\");\n/* harmony import */ var ag_grid_community_styles_ag_grid_css__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(ag_grid_community_styles_ag_grid_css__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var ag_grid_community_styles_ag_theme_alpine_css__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ag-grid-community/styles//ag-theme-alpine.css */ \"./node_modules/ag-grid-community/styles/ag-theme-alpine.css\");\n/* harmony import */ var ag_grid_community_styles_ag_theme_alpine_css__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(ag_grid_community_styles_ag_theme_alpine_css__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! next/link */ \"./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var _components_Layout__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/Layout */ \"./components/Layout.js\");\n/* harmony import */ var _components_Steps__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/Steps */ \"./components/Steps.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_9___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_9__);\n/* harmony import */ var _services_apiConfig__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/services/apiConfig */ \"./services/apiConfig.js\");\n/* harmony import */ var react_loader_spinner__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! react-loader-spinner */ \"./node_modules/react-loader-spinner/dist/module.js\");\n/* harmony import */ var react_toastify__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! react-toastify */ \"./node_modules/react-toastify/dist/react-toastify.esm.mjs\");\n/* harmony import */ var react_toastify_dist_ReactToastify_css__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! react-toastify/dist/ReactToastify.css */ \"./node_modules/react-toastify/dist/ReactToastify.css\");\n/* harmony import */ var react_toastify_dist_ReactToastify_css__WEBPACK_IMPORTED_MODULE_13___default = /*#__PURE__*/__webpack_require__.n(react_toastify_dist_ReactToastify_css__WEBPACK_IMPORTED_MODULE_13__);\n/* harmony import */ var _utils_userContext__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/utils/userContext */ \"./utils/userContext.js\");\n/* harmony import */ var _utils_getCookieData__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @/utils/getCookieData */ \"./utils/getCookieData.js\");\n/* harmony import */ var next_dist_server_body_streams__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! next/dist/server/body-streams */ \"./node_modules/next/dist/server/body-streams.js\");\n/* harmony import */ var next_dist_server_body_streams__WEBPACK_IMPORTED_MODULE_16___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_body_streams__WEBPACK_IMPORTED_MODULE_16__);\n/* harmony import */ var _utils_ajaxHandler__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! @/utils/ajaxHandler */ \"./utils/ajaxHandler.js\");\n/* harmony import */ var _utils_ajaxHandler__WEBPACK_IMPORTED_MODULE_17___default = /*#__PURE__*/__webpack_require__.n(_utils_ajaxHandler__WEBPACK_IMPORTED_MODULE_17__);\n/* harmony import */ var _utils_auth_auth__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! @/utils/auth/auth */ \"./utils/auth/auth.js\");\n/* harmony import */ var _azure_msal_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! @azure/msal-react */ \"./node_modules/@azure/msal-react/dist/index.js\");\n/* harmony import */ var _components_ValidationAlertBox__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! @/components/ValidationAlertBox */ \"./components/ValidationAlertBox.js\");\n/* harmony import */ var _utils_loaders_loadingContext__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! @/utils/loaders/loadingContext */ \"./utils/loaders/loadingContext.js\");\n/* harmony import */ var js_cookie__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! js-cookie */ \"./node_modules/js-cookie/dist/js.cookie.mjs\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst GeneralSection = (param)=>{\n    let { data, onSubmit, isEdit, dropdowns, setNavType, navType } = param;\n    var _dropdowns_countries;\n    _s();\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_9__.useRouter)();\n    const user = (0,_utils_getCookieData__WEBPACK_IMPORTED_MODULE_15__.getCookieData)(\"user\");\n    const { supplierId } = router.query;\n    const [tradingName, setTradingName] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [isTradingNameValid, setIsTradingNameValid] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true); //trading name validation\n    const [tradingNameError, setTradingNameError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [allowedSections, setAllowedSections] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [telephone, setTelephone] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [edi, setEdi] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [isTelephoneValid, setIsTelephoneValid] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true); //telephone validation\n    const [telephoneError, setTelephoneError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [email, setEmail] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [isEmailValid, setIsEmailValid] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true); //email validation.\n    const [addressLine1, setAddressLine1] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [isAddressLine1Valid, setIsAddressLine1Valid] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [addressLine1Error, setAddressLine1Error] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [isAddressLine2Valid, setIsAddressLine2Valid] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [addressLine2Error, setAddressLine2Error] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [addressLine2, setAddressLine2] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [addressLine3, setAddressLine3] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [addressLine4, setAddressLine4] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [countryError, setCountryError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [supplierName, setSupplierName] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [contactId, setContactId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isSupplierNameValid, setIsSupplierNameValid] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true); //supplier name validation part\n    const [supplierNameError, setSupplierNameError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [supplierEmail, setSupplierEmail] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [isSupplierEmailValid, setIsSupplierEmailValid] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [supplierTelephone, setSupplierTelephone] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [isSupplierTelephoneValid, setIsSupplierTelephoneValid] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true); //supplier telephone validation.\n    const [supplierTelephoneError, setSupplierTelephoneError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [TypeOfContact, setTypeOfContact] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [atLeastOneContactEntered, setAtLeastOneContactEntered] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isTypeofContactValid, setIsTypeofContactValid] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const gridRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const [contacts_Json, setcontacts_json] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [prophets, setProphets] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [postalCode, setPostalCode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [isPostalCodeValid, setIsPostalCodeValid] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [postalCodeError, setPostalCodeError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [country, setCountry] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [countryName, setCountryName] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isCommonError, setCommonError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const { instance, accounts } = (0,_azure_msal_react__WEBPACK_IMPORTED_MODULE_19__.useMsal)();\n    const [isOpen, setIsOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isCancelled, setIsCancelled] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isContinue, setIsContinue] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [formChange, setFormChange] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [countryChange, setCountryChange] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [status, setStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [isContactVisible, setIsContactVisible] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [role, setRole] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [ggn, setGGN] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [redTractor, setRedTractor] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [isSupplierAccount, setIsSupplierAccount] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [prophetObj, setProphetObj] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const { setIsLoading } = (0,_utils_loaders_loadingContext__WEBPACK_IMPORTED_MODULE_21__.useLoading)();\n    const [prophetsIds, setProphetIds] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)();\n    const validateTradingName = (name)=>{\n        const isValid = (name === null || name === void 0 ? void 0 : name.length) <= 50;\n        setIsTradingNameValid(isValid);\n        setTradingNameError(isValid ? \"\" : \"Trading name must be 50 characters or less.\");\n    };\n    const handleTradingNameChange = (event)=>{\n        setFormChange(true);\n        const newName = event.target.value;\n        setTradingName(newName);\n        validateTradingName(newName.trim());\n    };\n    const handleEmailChange = (event)=>{\n        setFormChange(true);\n        const newEmail = event.target.value;\n        setEmail(newEmail);\n        if (role === null || role === void 0 ? void 0 : role.includes(1)) {\n            setIsEmailValid(validateEmail(newEmail.trim()));\n        }\n    };\n    const validateEmail = (email)=>{\n        const emailRegex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$/;\n        // const emailRegex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,6}$/;\n        return emailRegex.test(email);\n    };\n    const validateTelephone = (telephone)=>{\n        const telephoneRegex = /^(?:\\+\\d{1,3}\\s?)?[\\d\\s]{9,15}$/;\n        const isValid = telephoneRegex.test(telephone);\n        if (role === null || role === void 0 ? void 0 : role.includes(1)) {\n            setIsTelephoneValid(isValid);\n            setTelephoneError(isValid ? \"\" : \"Please enter a valid telephone number.\");\n        }\n    };\n    const handleTelephoneChange = (event)=>{\n        setFormChange(true);\n        const newTelephone = event.target.value;\n        setTelephone(newTelephone);\n        validateTelephone(newTelephone.trim());\n    };\n    const validateAddressLine1 = (line)=>{\n        const isValid = (line === null || line === void 0 ? void 0 : line.length) <= 50;\n        setIsAddressLine1Valid(isValid);\n        setAddressLine1Error(isValid ? \"\" : \"Please add a small address of less than 50 characters.\");\n    };\n    const validateAddressLine2 = (line)=>{\n        const isValid = (line === null || line === void 0 ? void 0 : line.length) <= 50;\n        setIsAddressLine2Valid(isValid);\n        setAddressLine2Error(isValid ? \"\" : \"Please add a small address of less than 50 characters.\");\n    };\n    const handleAddressLine1Change = (event)=>{\n        setFormChange(true);\n        const newAddressLine1 = event.target.value;\n        setAddressLine1(newAddressLine1);\n        validateAddressLine1(newAddressLine1.trim());\n    };\n    const handleAddressLine2Change = (event)=>{\n        setFormChange(true);\n        const newAddressLine2 = event.target.value;\n        setAddressLine2(newAddressLine2);\n        validateAddressLine2(newAddressLine2.trim());\n    };\n    const handleAddressLine3Change = (event)=>{\n        setFormChange(true);\n        setAddressLine3(event.target.value);\n    };\n    const handleAddressLine4Change = (event)=>{\n        setFormChange(true);\n        setAddressLine4(event.target.value);\n    };\n    const handlePostalCodeChange = (event)=>{\n        setFormChange(true);\n        const newPostalCode = event.target.value.toUpperCase(); // postal code\n        setPostalCode(newPostalCode);\n        validatePostalCode(newPostalCode.trim());\n    };\n    const validatePostalCode = (code)=>{\n        // const regex =\n        //   /^[A-Z]{1,2}([0-9]{1,2}|[0-9][A-Z])\\s*[0-9][A-Z]{2}$|^([A-Z]\\d{2,3}\\s?[A-Z]{2})$/;\n        const regex = /^[A-Z]{1,2}([0-9]{1,2}|[0-9][A-Z])\\s*[0-9][A-Z]{2}$|^([A-Z]\\d{2,3}\\s?[A-Z]{2})$|^\\d{5}$/;\n        //const isValid = regex.test(code);\n        const isValid = postalCode !== null || postalCode !== \"\" || postalCode.length > 15 ? true : false;\n        setIsPostalCodeValid(isValid);\n        setPostalCodeError(isValid ? \"\" : \"Please enter a valid UK postal code (XX9 9XX format), zip code (ANN NAA format), or a 5-digit number.\");\n    };\n    const validateSupplierName = (name)=>{\n        const isValid = (name === null || name === void 0 ? void 0 : name.length) <= 50;\n        setIsSupplierNameValid(isValid);\n        setSupplierNameError(isValid ? \"\" : \"Supplier name must be 50 characters or less.\");\n    };\n    const handleSupplierNameChange = (event)=>{\n        setFormChange(true);\n        const newName = event.target.value;\n        setSupplierName(newName);\n        validateSupplierName(newName.trim());\n    };\n    const handleSupplierEmailChange = (event)=>{\n        if (role === null || role === void 0 ? void 0 : role.includes(1)) {\n            setFormChange(true);\n            const newSupplierEmail = event.target.value;\n            setSupplierEmail(newSupplierEmail);\n            setIsSupplierEmailValid(validEmail(newSupplierEmail.trim()));\n        }\n    };\n    const handleSupplierTelephoneChange = (event)=>{\n        setFormChange(true);\n        const newSupplierTelephone = event.target.value;\n        setSupplierTelephone(newSupplierTelephone);\n        validateSupplierTelephone(newSupplierTelephone.trim());\n    };\n    const validateSupplierTelephone = (telephone)=>{\n        const telephoneRegex = /^(?:\\+\\d{1,3}\\s?)?[\\d\\s]{9,15}$/;\n        const isValid = telephoneRegex.test(telephone);\n        setIsSupplierTelephoneValid(isValid);\n        setSupplierTelephoneError(isValid ? \"\" : \"Please enter a valid supplier telephone number.\");\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        var _data_, _data_1, _data_2, _data_3, _data_4, _data_5, _data_6, _data_7, _data_8, _data_9, _data_10, _data_11, _data_12, _data_13, _data_14, _data_15, _data_16, _data_17;\n        const prophetsIdsCookie = js_cookie__WEBPACK_IMPORTED_MODULE_22__[\"default\"].get(\"prophets\");\n        const prophetsIds = prophetsIdsCookie ? parseInt(prophetsIdsCookie, 10) : null;\n        setProphetIds(prophetsIds);\n        if (true) {\n            const sectionsString = localStorage.getItem(\"allowedSections\");\n            if (sectionsString) {\n                const parsedSections = sectionsString.split(\",\");\n                setAllowedSections(parsedSections);\n            }\n        }\n        var _data__trading_name, _ref;\n        setTradingName((_ref = (_data__trading_name = (_data_ = data[0]) === null || _data_ === void 0 ? void 0 : _data_.trading_name) !== null && _data__trading_name !== void 0 ? _data__trading_name : (_data_1 = data[0]) === null || _data_1 === void 0 ? void 0 : _data_1.name) !== null && _ref !== void 0 ? _ref : \"\");\n        var _data__email_id;\n        setEmail((_data__email_id = (_data_2 = data[0]) === null || _data_2 === void 0 ? void 0 : _data_2.email_id) !== null && _data__email_id !== void 0 ? _data__email_id : \"\");\n        var _data__telephone;\n        setTelephone((_data__telephone = (_data_3 = data[0]) === null || _data_3 === void 0 ? void 0 : _data_3.telephone) !== null && _data__telephone !== void 0 ? _data__telephone : \"\");\n        var _data__address_line_1;\n        setAddressLine1((_data__address_line_1 = (_data_4 = data[0]) === null || _data_4 === void 0 ? void 0 : _data_4.address_line_1) !== null && _data__address_line_1 !== void 0 ? _data__address_line_1 : \"\");\n        var _data__address_line_2;\n        setAddressLine2((_data__address_line_2 = (_data_5 = data[0]) === null || _data_5 === void 0 ? void 0 : _data_5.address_line_2) !== null && _data__address_line_2 !== void 0 ? _data__address_line_2 : \"\");\n        var _data__address_line_3;\n        setAddressLine3((_data__address_line_3 = (_data_6 = data[0]) === null || _data_6 === void 0 ? void 0 : _data_6.address_line_3) !== null && _data__address_line_3 !== void 0 ? _data__address_line_3 : \"\");\n        var _data__address_line_4;\n        setAddressLine4((_data__address_line_4 = (_data_7 = data[0]) === null || _data_7 === void 0 ? void 0 : _data_7.address_line_4) !== null && _data__address_line_4 !== void 0 ? _data__address_line_4 : \"\");\n        var _data__postal_code;\n        setPostalCode((_data__postal_code = (_data_8 = data[0]) === null || _data_8 === void 0 ? void 0 : _data_8.postal_code) !== null && _data__postal_code !== void 0 ? _data__postal_code : \"\");\n        var _data__country_id;\n        setCountry((_data__country_id = (_data_9 = data[0]) === null || _data_9 === void 0 ? void 0 : _data_9.country_id) !== null && _data__country_id !== void 0 ? _data__country_id : \"\");\n        var _data__country_name;\n        setCountryName((_data__country_name = (_data_10 = data[0]) === null || _data_10 === void 0 ? void 0 : _data_10.country_name) !== null && _data__country_name !== void 0 ? _data__country_name : \"\");\n        var _data__status;\n        setStatus((_data__status = (_data_11 = data[0]) === null || _data_11 === void 0 ? void 0 : _data_11.status) !== null && _data__status !== void 0 ? _data__status : \"\");\n        setRedTractor((_data_12 = data[0]) === null || _data_12 === void 0 ? void 0 : _data_12.red_tractor);\n        setGGN((_data_13 = data[0]) === null || _data_13 === void 0 ? void 0 : _data_13.global_gap_number);\n        var _data__edi;\n        setEdi((_data__edi = (_data_14 = data[0]) === null || _data_14 === void 0 ? void 0 : _data_14.edi) !== null && _data__edi !== void 0 ? _data__edi : \"\");\n        var _data__role_ids;\n        const role_parse = JSON.parse((_data__role_ids = data[0].role_ids) !== null && _data__role_ids !== void 0 ? _data__role_ids : \"[]\");\n        //const role_ids = role_parse.map()\n        const role_ids = role_parse === null || role_parse === void 0 ? void 0 : role_parse.map((roleId)=>roleId.role_id);\n        if ((role_ids === null || role_ids === void 0 ? void 0 : role_ids.includes(2)) || (role_ids === null || role_ids === void 0 ? void 0 : role_ids.includes(3)) || (role_ids === null || role_ids === void 0 ? void 0 : role_ids.includes(4))) {\n            setIsContactVisible(false);\n        } else {\n            setIsContactVisible(true);\n        }\n        //const role_ids = role_parse?.map((roleId) => roleId.role_id);\n        //console.log(role_ids)\n        setRole(role_ids);\n        const supplierAccountExist = role_ids === null || role_ids === void 0 ? void 0 : role_ids.includes(1);\n        if (supplierAccountExist) {\n            setIsSupplierAccount(\"true\");\n        } else {\n            setIsSupplierAccount(\"false\");\n        }\n        var _data__contacts_json;\n        const contacts_data_json = JSON.parse((_data__contacts_json = (_data_15 = data[0]) === null || _data_15 === void 0 ? void 0 : _data_15.contacts_json) !== null && _data__contacts_json !== void 0 ? _data__contacts_json : \"[]\");\n        const formattedContactsData = contacts_data_json === null || contacts_data_json === void 0 ? void 0 : contacts_data_json.map((row)=>({\n                id: row === null || row === void 0 ? void 0 : row.id,\n                supplierName: row === null || row === void 0 ? void 0 : row.name,\n                supplierEmail: row === null || row === void 0 ? void 0 : row.email_id,\n                supplierTelephone: row === null || row === void 0 ? void 0 : row.telephone,\n                TypeOfContact: row === null || row === void 0 ? void 0 : row.type_of_contact\n            }));\n        setcontacts_json(formattedContactsData);\n        //const existingProphetCode = checkExistingProphetCode(supplierId, user);\n        const supplierName = (_data_16 = data[0]) === null || _data_16 === void 0 ? void 0 : _data_16.name;\n        const prophetIds = (_data_17 = data[0]) === null || _data_17 === void 0 ? void 0 : _data_17.prophets_id_code;\n        setIsLoading(false);\n    }, []);\n    const validEmail = (email)=>{\n        const emailRegex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$/;\n        return emailRegex.test(email);\n    };\n    const savedata = (e)=>{\n        setFormChange(true);\n        e.preventDefault();\n        const newItem = {\n            supplierName: supplierName.trim(),\n            supplierEmail: supplierEmail.trim(),\n            supplierTelephone: supplierTelephone.trim(),\n            TypeOfContact: TypeOfContact.trim(),\n            id: contactId\n        };\n        let errorCount = 0;\n        if (supplierName == \"\") {\n            setIsSupplierNameValid(false);\n            errorCount++;\n        }\n        // if (supplierName.length <= 50) {\n        //   validateSupplierName(supplierName);\n        //   errorCount++;\n        // }\n        if (supplierEmail == \"\" || !validEmail(supplierEmail.trim())) {\n            setIsSupplierEmailValid(false);\n            errorCount++;\n        }\n        if (supplierTelephone == \"\") {\n            setIsSupplierTelephoneValid(false);\n            validateSupplierTelephone(supplierTelephone.trim());\n            errorCount++;\n        }\n        if (TypeOfContact == \"\") {\n            setIsTypeofContactValid(false);\n            errorCount++;\n        }\n        if (errorCount > 0) {\n            return;\n        }\n        setcontacts_json((prevContactsJson)=>{\n            if (!Array.isArray(prevContactsJson)) {\n                return [\n                    newItem\n                ];\n            }\n            return [\n                ...prevContactsJson,\n                newItem\n            ];\n        // setAtLeastOneContactEntered(true); // Update state to indicate at least one contact is entered\n        // return updatedContacts;\n        });\n        setSupplierEmail(\"\");\n        setSupplierName(\"\");\n        setSupplierTelephone(\"\");\n        setTypeOfContact(\"\");\n        setContactId(null);\n        setAtLeastOneContactEntered(true);\n    };\n    const IconsRenderer = (props)=>{\n        let updatedData;\n        const handleDelete = (e)=>{\n            setFormChange(true);\n            e.preventDefault();\n            const rowData = props.data;\n            updatedData = [\n                ...contacts_Json\n            ];\n            const index = updatedData === null || updatedData === void 0 ? void 0 : updatedData.indexOf(rowData);\n            updatedData.splice(index, 1);\n            props.api.applyTransaction({\n                remove: updatedData\n            });\n            setcontacts_json(updatedData);\n        };\n        const handleEdit = (e)=>{\n            setFormChange(true);\n            e.preventDefault();\n            const rowData = props === null || props === void 0 ? void 0 : props.data;\n            updatedData = [\n                ...contacts_Json\n            ];\n            const index = updatedData.indexOf(rowData);\n            updatedData.splice(index, 1);\n            props.api.applyTransaction({\n                remove: updatedData\n            });\n            setcontacts_json(updatedData);\n            setContactId(rowData === null || rowData === void 0 ? void 0 : rowData.id);\n            setSupplierName(rowData === null || rowData === void 0 ? void 0 : rowData.supplierName);\n            setSupplierEmail(rowData === null || rowData === void 0 ? void 0 : rowData.supplierEmail);\n            setSupplierTelephone(rowData === null || rowData === void 0 ? void 0 : rowData.supplierTelephone);\n            var _rowData_TypeOfContact;\n            setTypeOfContact((_rowData_TypeOfContact = rowData === null || rowData === void 0 ? void 0 : rowData.TypeOfContact) !== null && _rowData_TypeOfContact !== void 0 ? _rowData_TypeOfContact : \"\");\n            if (!isSupplierNameValid) {\n                setIsSupplierNameValid(true);\n            }\n            if (!isSupplierEmailValid) {\n                setIsSupplierEmailValid(true);\n            }\n            if (!isSupplierTelephoneValid) {\n                setIsSupplierTelephoneValid(true);\n            }\n            if (!isTypeofContactValid) {\n                setIsTypeofContactValid(true);\n            }\n        };\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex flex-row gap-4 justify-center text-skin-primary\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    onClick: handleEdit,\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fortawesome_react_fontawesome__WEBPACK_IMPORTED_MODULE_2__.FontAwesomeIcon, {\n                        icon: _fortawesome_free_solid_svg_icons__WEBPACK_IMPORTED_MODULE_23__.faPenToSquare\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\GeneralSection.js\",\n                        lineNumber: 444,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\GeneralSection.js\",\n                    lineNumber: 443,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    onClick: handleDelete,\n                    className: \"text-red-500\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fortawesome_react_fontawesome__WEBPACK_IMPORTED_MODULE_2__.FontAwesomeIcon, {\n                        icon: _fortawesome_free_solid_svg_icons__WEBPACK_IMPORTED_MODULE_23__.faTrash\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\GeneralSection.js\",\n                        lineNumber: 447,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\GeneralSection.js\",\n                    lineNumber: 446,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\GeneralSection.js\",\n            lineNumber: 442,\n            columnNumber: 7\n        }, undefined);\n    };\n    const defaultColDef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>({\n            //sortable: true,\n            filter: false,\n            resizable: true,\n            flex: 1,\n            suppressMenu: false\n        }));\n    // const CustomCellRenderer = (params) => {\n    //   const truncatedText =\n    //     params?.value && params?.value?.length > 12\n    //       ? params?.value?.substring(0, 12) + \"...\"\n    //       : params?.value;\n    //   return <span title={params?.value}>{truncatedText}</span>;\n    // };\n    const CustomTooltipComponent = (param)=>/*#__PURE__*/ {\n        let { value } = param;\n        return (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            title: value,\n            children: value\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\GeneralSection.js\",\n            lineNumber: 470,\n            columnNumber: 5\n        }, undefined);\n    };\n    const columnDefs = [\n        {\n            headerName: \"Name\",\n            field: \"supplierName\",\n            tooltipComponent: CustomTooltipComponent,\n            headerClass: \"header-with-border\",\n            cellStyle: {\n                display: \"flex\"\n            },\n            flex: \"2%\"\n        },\n        {\n            headerName: \"Email ID\",\n            field: \"supplierEmail\",\n            tooltipComponent: CustomTooltipComponent,\n            headerClass: \"header-with-border\",\n            cellStyle: {\n                display: \"flex\"\n            },\n            flex: \"2%\"\n        },\n        {\n            headerName: \"Telephone\",\n            field: \"supplierTelephone\",\n            tooltipComponent: CustomTooltipComponent,\n            headerClass: \"header-with-border\",\n            cellStyle: {\n                display: \"flex\"\n            },\n            flex: \"2%\"\n        },\n        {\n            headerName: \"Type of Contact\",\n            field: \"TypeOfContact\",\n            tooltipComponent: CustomTooltipComponent,\n            headerClass: \"header-with-border\",\n            cellStyle: {\n                display: \"flex\"\n            },\n            flex: \"2%\"\n        },\n        {\n            field: \"\",\n            cellRenderer: IconsRenderer,\n            headerClass: \"header-with-border\",\n            cellStyle: {\n                display: \"flex\",\n                justifyContent: \"end\"\n            },\n            flex: \"1%\"\n        }\n    ];\n    function handleTypeOfContactChange(e) {\n        setFormChange(true);\n        setTypeOfContact(e.target.value);\n        setIsTypeofContactValid(true);\n    }\n    const isFormValid = ()=>{\n        // Check validity of all required fields\n        const isTradingNameValid = (tradingName === null || tradingName === void 0 ? void 0 : tradingName.length) <= 50;\n        const isEmailValid = validateEmail(email.trim());\n        const isTelephoneValid = validateTelephone(telephone.trim());\n        const isPostalCodeValid = (postalCode === null || postalCode === void 0 ? void 0 : postalCode.length) <= 8;\n        const isSupplierNameValid = (supplierName === null || supplierName === void 0 ? void 0 : supplierName.length) <= 50;\n        const isSupplierEmailValid = validEmail(supplierEmail.trim());\n        const isSupplierTelephoneValid = validateSupplierTelephone(supplierTelephone.trim());\n        // Check if at least one contact is entered\n        const isAtLeastOneContactEntered = atLeastOneContactEntered;\n        // Set state to update UI based on validity\n        setIsTradingNameValid(isTradingNameValid);\n        setIsEmailValid(isEmailValid);\n        setIsTelephoneValid(isTelephoneValid);\n        setIsPostalCodeValid(isPostalCodeValid);\n        setIsSupplierNameValid(isSupplierNameValid);\n        setIsSupplierEmailValid(isSupplierEmailValid);\n        setIsSupplierTelephoneValid(isSupplierTelephoneValid);\n        // Return true only if all required fields are valid and at least one contact is entered\n        return isTradingNameValid && isEmailValid && isTelephoneValid && isPostalCodeValid && isSupplierNameValid && isSupplierEmailValid && isSupplierTelephoneValid && isAtLeastOneContactEntered;\n    };\n    const handleValidate = (step, isContinue)=>{\n        var _data_;\n        let errorCount = 0;\n        var _data__role_ids;\n        const roles = JSON.parse((_data__role_ids = (_data_ = data[0]) === null || _data_ === void 0 ? void 0 : _data_.role_ids) !== null && _data__role_ids !== void 0 ? _data__role_ids : \"[]\");\n        const role_ids = roles === null || roles === void 0 ? void 0 : roles.map((item)=>item === null || item === void 0 ? void 0 : item.role_id);\n        if (!country) {\n            //alert(country);\n            setCountryError(\"Please select country.\");\n            react_toastify__WEBPACK_IMPORTED_MODULE_12__.toast.error(\"Cannot proceed without selecting a Country. Kindly select a Country\");\n            return;\n        }\n        if (tradingName === \"\") {\n            setIsTradingNameValid(false);\n            setTradingNameError(\"Trading name is required.\");\n            errorCount++;\n        }\n        if (role_ids === null || role_ids === void 0 ? void 0 : role_ids.includes(1)) {\n            if (email === \"\" || !isEmailValid) {\n                setIsEmailValid(false);\n                errorCount++;\n            }\n            if (telephone === \"\" || !isTelephoneValid) {\n                setIsTelephoneValid(false);\n                setTelephoneError(\"Telephone is required.\");\n                errorCount++;\n            }\n        }\n        if (addressLine1 === \"\") {\n            setIsAddressLine1Valid(false);\n            errorCount++;\n            setAddressLine1Error(\"Please add an address. \");\n        }\n        if (addressLine2 === \"\") {\n            setIsAddressLine2Valid(false);\n            errorCount++;\n            setAddressLine2Error(\"Please add an address line 2. \");\n        }\n        if (postalCode === \"\" || !isPostalCodeValid) {\n            setIsPostalCodeValid(false);\n            setPostalCodeError(\"Postal code is required.\");\n            errorCount++;\n        }\n        if (role === null || role === void 0 ? void 0 : role.includes(1)) {\n            if (!contacts_Json || contacts_Json && contacts_Json.length < 1) {\n                errorCount++;\n                react_toastify__WEBPACK_IMPORTED_MODULE_12__.toast.error(\"Please add atleast one contact.\");\n            //return;\n            }\n        }\n        if (errorCount > 0) {\n            setNavType(step);\n            setIsOpen(true);\n        } else {\n            handleSubmit(step, \"Complete\");\n        }\n        if (isCancelled) {\n            setIsCancelled(false);\n            return;\n        }\n        if (isContinue) {\n            handleSubmit(step);\n            setIsOpen(false);\n        }\n    };\n    const handleSubmit = function(step) {\n        let technical = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : \"Incomplete\";\n        //const isFormValidResult = isFormValid();\n        setLoading(true);\n        let serverAddress = _services_apiConfig__WEBPACK_IMPORTED_MODULE_10__.apiConfig.serverAddress;\n        if (formChange) {\n            var _data_, _data_1;\n            let currentStatus;\n            if (status == 3 || status == 4 || status == 1) {\n                currentStatus = 4;\n            } else if (status == 2) {\n                currentStatus = 2;\n            } else {\n                currentStatus = 3;\n            }\n            localStorage.setItem(\"isFormNew\", false);\n            fetch(\"\".concat(serverAddress, \"suppliers/update-supplier/\").concat(supplierId), {\n                method: \"PUT\",\n                headers: {\n                    Accept: \"application/json\",\n                    \"Content-Type\": \"application/json\",\n                    Authorization: \"Bearer \".concat(user === null || user === void 0 ? void 0 : user.token)\n                },\n                body: JSON.stringify({\n                    sectionName: \"generalSection\",\n                    trading_name: tradingName === null || tradingName === void 0 ? void 0 : tradingName.trim(),\n                    telephone: isTelephoneValid ? telephone === null || telephone === void 0 ? void 0 : telephone.trim() : \"\",\n                    email_id: isEmailValid ? email === null || email === void 0 ? void 0 : email.trim() : \"\",\n                    address_line_1: isAddressLine1Valid ? addressLine1 === null || addressLine1 === void 0 ? void 0 : addressLine1.trim() : \"\",\n                    address_line_2: isAddressLine2Valid ? addressLine2 === null || addressLine2 === void 0 ? void 0 : addressLine2.trim() : \"\",\n                    address_line_3: addressLine3 === null || addressLine3 === void 0 ? void 0 : addressLine3.trim(),\n                    address_line_4: addressLine4 === null || addressLine4 === void 0 ? void 0 : addressLine4.trim(),\n                    postal_code: isPostalCodeValid ? postalCode === null || postalCode === void 0 ? void 0 : postalCode.trim() : \"\",\n                    contacts_json: contacts_Json,\n                    country: country,\n                    country_name: countryName ? countryName : null,\n                    technical: technical,\n                    updated_date: new Date().toISOString(),\n                    userId: user === null || user === void 0 ? void 0 : user.user_id,\n                    status: currentStatus,\n                    compliance: data[0].compliance,\n                    procurement: data[0].procurement,\n                    financial: data[0].financial,\n                    allowedSections: allowedSections,\n                    roleIds: role,\n                    prophet_id: prophetsIds,\n                    requestor_email: (_data_ = data[0]) === null || _data_ === void 0 ? void 0 : _data_.requestor_email,\n                    requestor_name: (_data_1 = data[0]) === null || _data_1 === void 0 ? void 0 : _data_1.requestor_name,\n                    edi: edi\n                })\n            }).then((res)=>{\n                if (res.status === 400) {\n                    react_toastify__WEBPACK_IMPORTED_MODULE_12__.toast.error(\"There was an error with your request. Please check your data and try again.\");\n                } else if (res.status === 401) {\n                    react_toastify__WEBPACK_IMPORTED_MODULE_12__.toast.error(\"Your session has expired. Please log in again.\");\n                    setTimeout(()=>{\n                        localStorage.removeItem(\"superUser\");\n                        js_cookie__WEBPACK_IMPORTED_MODULE_22__[\"default\"].remove(\"company\");\n                        js_cookie__WEBPACK_IMPORTED_MODULE_22__[\"default\"].remove(\"ADCompanyName\");\n                        localStorage.removeItem(\"id\");\n                        localStorage.removeItem(\"name\");\n                        localStorage.removeItem(\"role\");\n                        localStorage.removeItem(\"email\");\n                        js_cookie__WEBPACK_IMPORTED_MODULE_22__[\"default\"].remove(\"user\");\n                        js_cookie__WEBPACK_IMPORTED_MODULE_22__[\"default\"].remove(\"theme\");\n                        js_cookie__WEBPACK_IMPORTED_MODULE_22__[\"default\"].remove(\"token\");\n                        const redirectUrl = \"/login?redirect=\".concat(encodeURIComponent(window.location.pathname));\n                        (0,_utils_auth_auth__WEBPACK_IMPORTED_MODULE_18__.logoutHandler)(instance, redirectUrl);\n                    }, 3000);\n                }\n                if (res.status === 200) {\n                    if (step == \"sap\") {\n                        if (isEdit) {\n                            var _data_;\n                            if (isSupplierAccount == \"false\" && countryChange && (((_data_ = data[0]) === null || _data_ === void 0 ? void 0 : _data_.prophets_id_code) || Object.keys(prophetData).length !== 0) && prophetObj.prophet_code != null) {\n                                const result = (0,_utils_ajaxHandler__WEBPACK_IMPORTED_MODULE_17__.addProphetAjaxCall)(prophetObj, user);\n                                result.then((data)=>{\n                                    if (data === null || data === void 0 ? void 0 : data.data) {\n                                        localStorage.removeItem(\"isEdit\");\n                                        router.back();\n                                    }\n                                });\n                            } else {\n                                localStorage.removeItem(\"isEdit\");\n                                router.back();\n                            }\n                        } else {\n                            var _data_1;\n                            if (isSupplierAccount == \"false\" && countryChange && (((_data_1 = data[0]) === null || _data_1 === void 0 ? void 0 : _data_1.prophets_id_code.length) > 0 || Object.keys(prophetData).length !== 0) && prophetObj[0].prophet_code != null) {\n                                const result = (0,_utils_ajaxHandler__WEBPACK_IMPORTED_MODULE_17__.addProphetAjaxCall)(prophetObj, user);\n                                result.then((data)=>{\n                                    if (data === null || data === void 0 ? void 0 : data.data) {\n                                        onSubmit();\n                                    }\n                                });\n                            } else {\n                                onSubmit();\n                            }\n                        }\n                    } else {\n                        router.push({\n                            pathname: \"/suppliers\"\n                        });\n                    }\n                // setLoading(false);\n                }\n                return Promise.reject(res);\n            }).catch((err)=>{\n                setLoading(false);\n                // toast.error(\n                //   `Error saving data in general forms file: ${err.statusText}`,\n                //   {\n                //     position: \"top-right\",\n                //   }\n                // );\n                return err;\n            });\n        } else {\n            if (step == \"sap\") {\n                if (isEdit) {\n                    localStorage.removeItem(\"isEdit\");\n                    router.back();\n                } else {\n                    onSubmit();\n                }\n            } else {\n                router.push({\n                    pathname: \"/suppliers\"\n                });\n            }\n        }\n    };\n    const handleSaveAndExit = ()=>{\n        if (!isFormValid()) {\n            return;\n        }\n        router.push(\"/confirmPage\");\n    };\n    const [prophetData, setProphetData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const handleCountryChange = (e)=>{\n        var _data_;\n        setFormChange(true);\n        setCountryChange(true);\n        if (country) {\n            setCountryError(\"\");\n        }\n        const prophetIds = (_data_ = data[0]) === null || _data_ === void 0 ? void 0 : _data_.prophets_id_code;\n        if (prophetIds && e.target.value && isSupplierAccount === \"false\") {\n            var _prophet_;\n            const selectedCountryName = e.target.options[e.target.selectedIndex].text;\n            const countryCode = selectedCountryName === \"United Kingdom\" ? \"UK\" : \"\";\n            const identifier = countryCode === \"UK\" ? redTractor : ggn;\n            const generateProphetCode = identifier && (identifier === null || identifier === void 0 ? void 0 : identifier.slice(-6).padStart(6, \"X\"));\n            const prophet = JSON.parse(prophetIds !== null && prophetIds !== void 0 ? prophetIds : \"[]\");\n            setProphetData({\n                prophet_id: (_prophet_ = prophet[0]) === null || _prophet_ === void 0 ? void 0 : _prophet_.prophet_id,\n                prophet_code: generateProphetCode && (generateProphetCode === null || generateProphetCode === void 0 ? void 0 : generateProphetCode.toString().trim().toUpperCase()),\n                supplier_id: parseInt(supplierId)\n            });\n            setProphetObj([\n                prophetData\n            ]);\n        }\n    };\n    const closeModal = (e)=>{\n        if (e) {\n            e.preventDefault();\n        }\n        setIsCancelled(true);\n        setIsOpen(false);\n    };\n    const handleContinueSubmit = ()=>{\n        handleValidate(navType, isContinue);\n        setIsOpen(false);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_toastify__WEBPACK_IMPORTED_MODULE_12__.ToastContainer, {\n                limit: 1\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\GeneralSection.js\",\n                lineNumber: 826,\n                columnNumber: 7\n            }, undefined),\n            loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    display: \"flex\",\n                    alignItems: \"center\",\n                    justifyContent: \"center\",\n                    height: \"calc(100vh - 100px)\",\n                    width: \"calc(100vw - 125px)\"\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_loader_spinner__WEBPACK_IMPORTED_MODULE_11__.ThreeCircles, {\n                    color: \"#002D73\",\n                    height: 50,\n                    width: 50,\n                    visible: true,\n                    ariaLabel: \"oval-loading\",\n                    secondaryColor: \"#0066FF\",\n                    strokeWidth: 2,\n                    strokeWidthSecondary: 2\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\GeneralSection.js\",\n                    lineNumber: 837,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\GeneralSection.js\",\n                lineNumber: 828,\n                columnNumber: 9\n            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative panel-container bg-white rounded-lg w-[93%] lg:w-[95%] 2xl:w-[calc(100%-70px)] p-4 pb-0\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex md:flex-row flex-col my-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"px-5 pe-8 mb-0 h-100vh border-e-[1px] border-light-gray \".concat((role === null || role === void 0 ? void 0 : role.includes(1)) ? \"md:w-1/2\" : \"md:w-full\"),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mb-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mb-3\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                    className: \"formtitle pb-1 border-b border-light-gray3\",\n                                                    children: \"Supplier Details\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\GeneralSection.js\",\n                                                    lineNumber: 858,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\GeneralSection.js\",\n                                                lineNumber: 857,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid \".concat((role === null || role === void 0 ? void 0 : role.includes(1)) ? \"lg:grid-cols-2\" : \"lg:grid-cols-3\", \" gap-4  \").concat((role === null || role === void 0 ? void 0 : role.includes(1)) ? \"grid-cols-1\" : \"grid-cols-3\"),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex flex-col\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"labels mb-1\",\n                                                                children: [\n                                                                    \"Trading Name \",\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-red-500\",\n                                                                        children: \"*\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\GeneralSection.js\",\n                                                                        lineNumber: 872,\n                                                                        columnNumber: 36\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\GeneralSection.js\",\n                                                                lineNumber: 871,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                type: \"text\",\n                                                                name: \"Trading_name\",\n                                                                maxLength: 50,\n                                                                value: tradingName,\n                                                                onChange: handleTradingNameChange,\n                                                                className: \"w-full py-1 px-2 2xl:px-3 2xl:py-3 border \".concat(isTradingNameValid ? \"border-light-gray\" : \"!border-red-500\", \" rounded-md\"),\n                                                                required: true,\n                                                                style: {\n                                                                    textTransform: \"capitalize\"\n                                                                },\n                                                                tabIndex: 1\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\GeneralSection.js\",\n                                                                lineNumber: 874,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            !isTradingNameValid && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-red-500 text-sm mt-1\",\n                                                                children: \"Please enter a valid name of max 50 chars.\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\GeneralSection.js\",\n                                                                lineNumber: 890,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\GeneralSection.js\",\n                                                        lineNumber: 870,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex flex-col\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"labels mb-1\",\n                                                                children: [\n                                                                    \"Email ID\",\n                                                                    (role === null || role === void 0 ? void 0 : role.includes(1)) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-red-500\",\n                                                                        children: \"*\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\GeneralSection.js\",\n                                                                        lineNumber: 900,\n                                                                        columnNumber: 25\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\GeneralSection.js\",\n                                                                lineNumber: 897,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                type: \"text\",\n                                                                name: \"email_id\",\n                                                                maxLength: 80,\n                                                                value: email,\n                                                                onChange: handleEmailChange,\n                                                                className: \"w-full py-1 px-2 2xl:px-3 2xl:py-3 border \".concat(isEmailValid ? \"border-light-gray\" : \"border-red-500\", \" rounded-md\"),\n                                                                required: true,\n                                                                tabIndex: 2\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\GeneralSection.js\",\n                                                                lineNumber: 903,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            !isEmailValid && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-red-500 text-sm mt-1\",\n                                                                children: \"Please enter a valid email address.\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\GeneralSection.js\",\n                                                                lineNumber: 916,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\GeneralSection.js\",\n                                                        lineNumber: 896,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex flex-col\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"labels mb-1\",\n                                                                children: [\n                                                                    \"Telephone\",\n                                                                    \" \",\n                                                                    (role === null || role === void 0 ? void 0 : role.includes(1)) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-red-500\",\n                                                                        children: \"*\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\GeneralSection.js\",\n                                                                        lineNumber: 926,\n                                                                        columnNumber: 25\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\GeneralSection.js\",\n                                                                lineNumber: 923,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                type: \"text\",\n                                                                name: \"telephone\",\n                                                                maxLength: 15,\n                                                                value: telephone,\n                                                                onChange: handleTelephoneChange,\n                                                                className: \"w-full py-1 px-2 2xl:px-3 2xl:py-3 border \".concat(isTelephoneValid ? \"border-light-gray\" : \"border-red-500\", \" rounded-md\"),\n                                                                required: true,\n                                                                tabIndex: 3\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\GeneralSection.js\",\n                                                                lineNumber: 929,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            !isTelephoneValid && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-red-500 text-sm mt-1\",\n                                                                children: telephoneError\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\GeneralSection.js\",\n                                                                lineNumber: 944,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\GeneralSection.js\",\n                                                        lineNumber: 922,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex flex-col\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"labels mb-1\",\n                                                                children: \"EDI ANA Number\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\GeneralSection.js\",\n                                                                lineNumber: 950,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                type: \"text\",\n                                                                name: \"edi\",\n                                                                maxLength: 13,\n                                                                value: edi,\n                                                                onChange: (e)=>{\n                                                                    setEdi(e.target.value);\n                                                                    setFormChange(true);\n                                                                },\n                                                                className: \"w-full py-1 px-2 2xl:px-3 2xl:py-3 border rounded-md\",\n                                                                tabIndex: 4\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\GeneralSection.js\",\n                                                                lineNumber: 951,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\GeneralSection.js\",\n                                                        lineNumber: 949,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\GeneralSection.js\",\n                                                lineNumber: 863,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\GeneralSection.js\",\n                                        lineNumber: 856,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mb-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mb-3\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                    className: \"formtitle pb-2 border-b border-light-gray3\",\n                                                    children: \"Address\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\GeneralSection.js\",\n                                                    lineNumber: 969,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\GeneralSection.js\",\n                                                lineNumber: 968,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid \".concat((role === null || role === void 0 ? void 0 : role.includes(1)) ? \"lg:grid-cols-2\" : \"lg:grid-cols-3\", \" grid-cols-1 gap-4\"),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex flex-col\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"labels mb-1\",\n                                                                children: [\n                                                                    \"Address Line 1 \",\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-red-500\",\n                                                                        children: \"*\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\GeneralSection.js\",\n                                                                        lineNumber: 981,\n                                                                        columnNumber: 38\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\GeneralSection.js\",\n                                                                lineNumber: 980,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                type: \"text\",\n                                                                name: \"address_line_1\",\n                                                                maxLength: 50,\n                                                                value: addressLine1,\n                                                                onChange: handleAddressLine1Change,\n                                                                className: \"w-full py-1 px-2 2xl:px-3 2xl:py-3 border \".concat(isAddressLine1Valid ? \"border-light-gray\" : \"border-red-500\", \" rounded-md\"),\n                                                                required: true,\n                                                                tabIndex: 4,\n                                                                style: {\n                                                                    textTransform: \"capitalize\"\n                                                                }\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\GeneralSection.js\",\n                                                                lineNumber: 983,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            !isAddressLine1Valid && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-red-500 text-sm mt-1\",\n                                                                children: addressLine1Error\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\GeneralSection.js\",\n                                                                lineNumber: 999,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\GeneralSection.js\",\n                                                        lineNumber: 979,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex flex-col\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"labels mb-1\",\n                                                                children: [\n                                                                    \"Address Line 2 \",\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-red-500\",\n                                                                        children: \"*\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\GeneralSection.js\",\n                                                                        lineNumber: 1007,\n                                                                        columnNumber: 38\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\GeneralSection.js\",\n                                                                lineNumber: 1006,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                type: \"text\",\n                                                                name: \"address_line_2\",\n                                                                maxLength: 50,\n                                                                value: addressLine2,\n                                                                onChange: handleAddressLine2Change,\n                                                                className: \"w-full py-1 px-2 2xl:px-3 2xl:py-3 border border-light-gray rounded-md \".concat(isAddressLine2Valid ? \"border-light-gray\" : \"border-red-500\"),\n                                                                required: true,\n                                                                tabIndex: 5\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\GeneralSection.js\",\n                                                                lineNumber: 1009,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            !isAddressLine2Valid && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-red-500 text-sm mt-1\",\n                                                                children: addressLine2Error\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\GeneralSection.js\",\n                                                                lineNumber: 1024,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\GeneralSection.js\",\n                                                        lineNumber: 1005,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex flex-col\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"labels mb-1\",\n                                                                children: \"Address Line 3\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\GeneralSection.js\",\n                                                                lineNumber: 1031,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                type: \"text\",\n                                                                name: \"address_line_3\",\n                                                                maxLength: 50,\n                                                                value: addressLine3,\n                                                                onChange: handleAddressLine3Change,\n                                                                className: \"w-full py-1 px-2 2xl:px-3 2xl:py-3 border border-light-gray rounded-md\",\n                                                                tabIndex: 6\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\GeneralSection.js\",\n                                                                lineNumber: 1032,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\GeneralSection.js\",\n                                                        lineNumber: 1030,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex flex-col\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"labels mb-1\",\n                                                                children: \"Address Line 4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\GeneralSection.js\",\n                                                                lineNumber: 1044,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                type: \"text\",\n                                                                name: \"address_line_4\",\n                                                                maxLength: 50,\n                                                                value: addressLine4,\n                                                                onChange: handleAddressLine4Change,\n                                                                className: \"w-full py-1 px-2 2xl:px-3 2xl:py-3 border border-light-gray rounded-md\",\n                                                                tabIndex: 7\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\GeneralSection.js\",\n                                                                lineNumber: 1045,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\GeneralSection.js\",\n                                                        lineNumber: 1043,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex flex-col\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"labels mb-1\",\n                                                                required: true,\n                                                                children: [\n                                                                    \"Country \",\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-red-500\",\n                                                                        children: \"*\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\GeneralSection.js\",\n                                                                        lineNumber: 1058,\n                                                                        columnNumber: 31\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\GeneralSection.js\",\n                                                                lineNumber: 1057,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                                tabIndex: 8,\n                                                                type: \"text\",\n                                                                name: \"country\",\n                                                                value: country,\n                                                                onChange: (e)=>{\n                                                                    setFormChange(true);\n                                                                    const selectedCountryId = e.target.value;\n                                                                    const selectedCountryName = e.target.options[e.target.selectedIndex].text;\n                                                                    setCountry(selectedCountryId);\n                                                                    setCountryName(selectedCountryName);\n                                                                    handleCountryChange(e);\n                                                                },\n                                                                className: \"w-full px-2 2xl:px-3 border border-light-gray rounded-md\",\n                                                                required: true,\n                                                                onBlur: handleCountryChange,\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                        value: \"\",\n                                                                        disabled: true,\n                                                                        children: \"Select...\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\GeneralSection.js\",\n                                                                        lineNumber: 1079,\n                                                                        columnNumber: 23\n                                                                    }, undefined),\n                                                                    (dropdowns === null || dropdowns === void 0 ? void 0 : dropdowns.countries) && (dropdowns === null || dropdowns === void 0 ? void 0 : (_dropdowns_countries = dropdowns.countries) === null || _dropdowns_countries === void 0 ? void 0 : _dropdowns_countries.map((con, key)=>{\n                                                                        var _con_name;\n                                                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                            value: con.id,\n                                                                            defaultValue: (con === null || con === void 0 ? void 0 : (_con_name = con.name) === null || _con_name === void 0 ? void 0 : _con_name.trim()) == (countryName === null || countryName === void 0 ? void 0 : countryName.trim()) ? true : false,\n                                                                            children: con === null || con === void 0 ? void 0 : con.name\n                                                                        }, key, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\GeneralSection.js\",\n                                                                            lineNumber: 1085,\n                                                                            columnNumber: 29\n                                                                        }, undefined);\n                                                                    }))\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\GeneralSection.js\",\n                                                                lineNumber: 1061,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            countryError && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-red-500 text-sm mt-1\",\n                                                                children: countryError\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\GeneralSection.js\",\n                                                                lineNumber: 1100,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\GeneralSection.js\",\n                                                        lineNumber: 1056,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex flex-col\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"labels mb-1\",\n                                                                children: [\n                                                                    \"Postal Code/Zip Code\",\n                                                                    \" \",\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-red-500\",\n                                                                        children: \"*\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\GeneralSection.js\",\n                                                                        lineNumber: 1109,\n                                                                        columnNumber: 23\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\GeneralSection.js\",\n                                                                lineNumber: 1107,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                type: \"text\",\n                                                                name: \"postal_code\",\n                                                                maxLength: 10,\n                                                                value: postalCode,\n                                                                onChange: handlePostalCodeChange,\n                                                                className: \"w-full py-1 px-2 2xl:px-3 2xl:py-3 border \".concat(isPostalCodeValid ? \"border-light-gray\" : \"border-red-500\", \" rounded-md\"),\n                                                                required: true,\n                                                                tabIndex: 9\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\GeneralSection.js\",\n                                                                lineNumber: 1111,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            !isPostalCodeValid && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-red-500 text-sm mt-1\",\n                                                                children: postalCodeError\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\GeneralSection.js\",\n                                                                lineNumber: 1126,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\GeneralSection.js\",\n                                                        lineNumber: 1106,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\GeneralSection.js\",\n                                                lineNumber: 974,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\GeneralSection.js\",\n                                        lineNumber: 967,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\GeneralSection.js\",\n                                lineNumber: 851,\n                                columnNumber: 13\n                            }, undefined),\n                            (role === null || role === void 0 ? void 0 : role.includes(1)) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-x5 ps-8 mb-0 w-1/2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mb-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mb-3\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                    className: \"formtitle pb-1 border-b border-light-gray3\",\n                                                    children: \"Contact\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\GeneralSection.js\",\n                                                    lineNumber: 1138,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\GeneralSection.js\",\n                                                lineNumber: 1137,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex flex-row\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"grid lg:grid-cols-2 grid-cols-1 gap-4 mb-6 w-full\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex flex-col\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        className: \"labels mb-1\",\n                                                                        children: [\n                                                                            \"Name \",\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-red-500\",\n                                                                                children: \"*\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\GeneralSection.js\",\n                                                                                lineNumber: 1151,\n                                                                                columnNumber: 32\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\GeneralSection.js\",\n                                                                        lineNumber: 1150,\n                                                                        columnNumber: 25\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                        type: \"text\",\n                                                                        name: \"supplier_name\",\n                                                                        value: supplierName,\n                                                                        onChange: handleSupplierNameChange,\n                                                                        className: \"w-full py-1 px-2 2xl:px-3 2xl:py-3 border \".concat(isSupplierNameValid ? \"border-light-gray\" : \"!border-red-500\", \" rounded-md\"),\n                                                                        maxLength: 50,\n                                                                        required: true,\n                                                                        style: {\n                                                                            textTransform: \"capitalize\"\n                                                                        }\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\GeneralSection.js\",\n                                                                        lineNumber: 1153,\n                                                                        columnNumber: 25\n                                                                    }, undefined),\n                                                                    !isSupplierNameValid && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-red-500 text-sm mt-1\",\n                                                                        children: \"Please enter a name with max 50 chars.\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\GeneralSection.js\",\n                                                                        lineNumber: 1168,\n                                                                        columnNumber: 27\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\GeneralSection.js\",\n                                                                lineNumber: 1149,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex flex-col\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        className: \"labels mb-1\",\n                                                                        children: [\n                                                                            \"Email ID \",\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-red-500\",\n                                                                                children: \"*\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\GeneralSection.js\",\n                                                                                lineNumber: 1176,\n                                                                                columnNumber: 36\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\GeneralSection.js\",\n                                                                        lineNumber: 1175,\n                                                                        columnNumber: 25\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                        type: \"text\",\n                                                                        name: \"supplier_email_id\",\n                                                                        value: supplierEmail,\n                                                                        onChange: handleSupplierEmailChange,\n                                                                        className: \"w-full py-1 px-2 2xl:px-3 2xl:py-3 border \".concat(isSupplierEmailValid ? \"border-light-gray\" : \"!border-red-500\", \" rounded-md\"),\n                                                                        maxLength: 50,\n                                                                        required: true\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\GeneralSection.js\",\n                                                                        lineNumber: 1178,\n                                                                        columnNumber: 25\n                                                                    }, undefined),\n                                                                    !isSupplierEmailValid && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-red-500 text-sm mt-1\",\n                                                                        children: \"Please enter a valid email address.\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\GeneralSection.js\",\n                                                                        lineNumber: 1192,\n                                                                        columnNumber: 27\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\GeneralSection.js\",\n                                                                lineNumber: 1174,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex flex-col\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        className: \"labels mb-1\",\n                                                                        children: [\n                                                                            \"Telephone \",\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-red-500\",\n                                                                                children: \"*\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\GeneralSection.js\",\n                                                                                lineNumber: 1200,\n                                                                                columnNumber: 37\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\GeneralSection.js\",\n                                                                        lineNumber: 1199,\n                                                                        columnNumber: 25\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"relative\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                type: \"text\",\n                                                                                name: \"supplier_telephone\",\n                                                                                value: supplierTelephone,\n                                                                                onChange: handleSupplierTelephoneChange,\n                                                                                className: \"w-full py-1 px-2 2xl:px-3 2xl:py-3 border \".concat(isSupplierTelephoneValid ? \"border-light-gray\" : \"!border-red-500\", \" rounded-md\"),\n                                                                                maxLength: 15,\n                                                                                required: true\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\GeneralSection.js\",\n                                                                                lineNumber: 1203,\n                                                                                columnNumber: 27\n                                                                            }, undefined),\n                                                                            !isSupplierTelephoneValid && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                className: \"text-red-500 text-sm mt-1\",\n                                                                                children: \"Please enter a valid Telephone number.\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\GeneralSection.js\",\n                                                                                lineNumber: 1217,\n                                                                                columnNumber: 29\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\GeneralSection.js\",\n                                                                        lineNumber: 1202,\n                                                                        columnNumber: 25\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\GeneralSection.js\",\n                                                                lineNumber: 1198,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex flex-col\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        className: \"labels mb-1\",\n                                                                        children: [\n                                                                            \"Type of Contact\",\n                                                                            \" \",\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-red-500\",\n                                                                                children: \"*\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\GeneralSection.js\",\n                                                                                lineNumber: 1227,\n                                                                                columnNumber: 27\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\GeneralSection.js\",\n                                                                        lineNumber: 1225,\n                                                                        columnNumber: 25\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"relative\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                                                className: \"border \".concat(isTypeofContactValid ? \"border-light-gray\" : \"border-bright-red\", \" rounded-md px-2 2xl:px-3\"),\n                                                                                name: \"sendac group\",\n                                                                                onChange: handleTypeOfContactChange,\n                                                                                value: TypeOfContact,\n                                                                                style: {\n                                                                                    width: \"100%\"\n                                                                                },\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                        value: \"\",\n                                                                                        disabled: true,\n                                                                                        children: \"Select...\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\GeneralSection.js\",\n                                                                                        lineNumber: 1241,\n                                                                                        columnNumber: 29\n                                                                                    }, undefined),\n                                                                                    (dropdowns === null || dropdowns === void 0 ? void 0 : dropdowns.type_of_contacts) && (dropdowns === null || dropdowns === void 0 ? void 0 : dropdowns.type_of_contacts.map((contact, key)=>{\n                                                                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                            value: contact.name,\n                                                                                            children: contact.name\n                                                                                        }, key, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\GeneralSection.js\",\n                                                                                            lineNumber: 1248,\n                                                                                            columnNumber: 37\n                                                                                        }, undefined);\n                                                                                    }))\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\GeneralSection.js\",\n                                                                                lineNumber: 1230,\n                                                                                columnNumber: 27\n                                                                            }, undefined),\n                                                                            !isTypeofContactValid ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                className: \"text-red-500 text-sm mt-1 absolute bottom-[-20px]\",\n                                                                                children: \"Please select a type of contact.\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\GeneralSection.js\",\n                                                                                lineNumber: 1266,\n                                                                                columnNumber: 29\n                                                                            }, undefined) : \"\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\GeneralSection.js\",\n                                                                        lineNumber: 1229,\n                                                                        columnNumber: 25\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\GeneralSection.js\",\n                                                                lineNumber: 1224,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\GeneralSection.js\",\n                                                        lineNumber: 1148,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex w-[10%] items-end justify-center mb-6 \",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            onClick: savedata,\n                                                            tabIndex: 0,\n                                                            className: \"px-2 py-1 2xl:px-3.5 2xl:py-1 border border-skin-primary  text-skin-primary  rounded-md ml-8  cursor-pointer \".concat(!isTypeofContactValid && !isSupplierTelephoneValid ? \"mb-6\" : !isSupplierTelephoneValid ? \"mb-6\" : \"mb-0\"),\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fortawesome_react_fontawesome__WEBPACK_IMPORTED_MODULE_2__.FontAwesomeIcon, {\n                                                                icon: _fortawesome_free_solid_svg_icons__WEBPACK_IMPORTED_MODULE_23__.faFloppyDisk\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\GeneralSection.js\",\n                                                                lineNumber: 1287,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\GeneralSection.js\",\n                                                            lineNumber: 1276,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\GeneralSection.js\",\n                                                        lineNumber: 1275,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\GeneralSection.js\",\n                                                lineNumber: 1143,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\GeneralSection.js\",\n                                        lineNumber: 1136,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            height: 200,\n                                            width: \"100%\"\n                                        },\n                                        className: \"general_section\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ag_grid_react__WEBPACK_IMPORTED_MODULE_3__.AgGridReact, {\n                                                defaultColDef: defaultColDef,\n                                                columnDefs: columnDefs,\n                                                rowData: contacts_Json,\n                                                rowHeight: 25,\n                                                ref: gridRef\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\GeneralSection.js\",\n                                                lineNumber: 1296,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"style\", {\n                                                children: \"\\n          .ag-header .ag-header-cell.header-with-border {\\n            border-bottom: 1px solid #ccc; /* Style for header row */\\n          }\\n        \"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\GeneralSection.js\",\n                                                lineNumber: 1303,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\GeneralSection.js\",\n                                        lineNumber: 1292,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\GeneralSection.js\",\n                                lineNumber: 1135,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\GeneralSection.js\",\n                        lineNumber: 850,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-between border-t border-light-gray py-5 bg-white\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    className: \"border border-skin-primary text-skin-primary button px-8 rounded-md\",\n                                    onClick: ()=>router.push({\n                                            pathname: \"/supplier/\".concat(supplierId, \"/edit\")\n                                        }),\n                                    children: \"Previous\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\GeneralSection.js\",\n                                    lineNumber: 1323,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\GeneralSection.js\",\n                                lineNumber: 1322,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        className: \"border border-skin-primary text-skin-primary me-10 py-1 px-8 font-medium rounded-md\",\n                                        onClick: ()=>handleValidate(\"sae\"),\n                                        children: \"Save & Exit\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\GeneralSection.js\",\n                                        lineNumber: 1335,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        className: \"border  border-skin-primary text-white bg-skin-primary rounded-md py-1 px-8 font-medium\",\n                                        onClick: ()=>handleValidate(\"sap\"),\n                                        children: \"Save & Proceed\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\GeneralSection.js\",\n                                        lineNumber: 1341,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\GeneralSection.js\",\n                                lineNumber: 1334,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\GeneralSection.js\",\n                        lineNumber: 1321,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\GeneralSection.js\",\n                lineNumber: 849,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Dialog_Transition_headlessui_react__WEBPACK_IMPORTED_MODULE_24__.Transition, {\n                appear: true,\n                show: isOpen,\n                as: react__WEBPACK_IMPORTED_MODULE_1__.Fragment,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Dialog_Transition_headlessui_react__WEBPACK_IMPORTED_MODULE_24__.Dialog, {\n                    as: \"div\",\n                    className: \"relative z-10\",\n                    onClose: closeModal,\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Dialog_Transition_headlessui_react__WEBPACK_IMPORTED_MODULE_24__.Transition.Child, {\n                            as: react__WEBPACK_IMPORTED_MODULE_1__.Fragment,\n                            enter: \"ease-out duration-300\",\n                            enterFrom: \"opacity-0\",\n                            enterTo: \"opacity-100\",\n                            leave: \"ease-in duration-200\",\n                            leaveFrom: \"opacity-100\",\n                            leaveTo: \"opacity-0\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"fixed inset-0 bg-black bg-opacity-25 backdrop-blur-sm\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\GeneralSection.js\",\n                                lineNumber: 1362,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\GeneralSection.js\",\n                            lineNumber: 1353,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"fixed inset-0 overflow-y-auto\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-center min-h-full p-4 text-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Dialog_Transition_headlessui_react__WEBPACK_IMPORTED_MODULE_24__.Transition.Child, {\n                                    as: react__WEBPACK_IMPORTED_MODULE_1__.Fragment,\n                                    enter: \"ease-out duration-300\",\n                                    enterFrom: \"opacity-0 scale-95\",\n                                    enterTo: \"opacity-100 scale-100\",\n                                    leave: \"ease-in duration-200\",\n                                    leaveFrom: \"opacity-100 scale-100\",\n                                    leaveTo: \"opacity-0 scale-95\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Dialog_Transition_headlessui_react__WEBPACK_IMPORTED_MODULE_24__.Dialog.Panel, {\n                                        className: \" w-[45%] transform overflow-hidden rounded-xl bg-white  text-left align-middle shadow-xl transition-all\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative bg-white rounded-lg shadow\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-start justify-between p-8 rounded-t\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"flex flex-row text-xl font-semibold text-gray-900  items-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"flex justify-center items-center border border-skin-primary text-skin-primary w-[25px] h-[25px] text-center leading-5 rounded-full text-base me-4\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fortawesome_react_fontawesome__WEBPACK_IMPORTED_MODULE_2__.FontAwesomeIcon, {\n                                                                            icon: _fortawesome_free_solid_svg_icons__WEBPACK_IMPORTED_MODULE_23__.faInfo\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\GeneralSection.js\",\n                                                                            lineNumber: 1383,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        \" \"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\GeneralSection.js\",\n                                                                    lineNumber: 1382,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                \" \",\n                                                                \"Warning\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\GeneralSection.js\",\n                                                            lineNumber: 1381,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: closeModal,\n                                                            type: \"button\",\n                                                            className: \"text-black bg-transparent hover:bg-gray-200 hover:text-gray-900 rounded-lg text-xl w-8 h-8 ml-auto inline-flex justify-center items-center \",\n                                                            \"data-modal-hide\": \"default-modal\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fortawesome_react_fontawesome__WEBPACK_IMPORTED_MODULE_2__.FontAwesomeIcon, {\n                                                                    icon: _fortawesome_free_solid_svg_icons__WEBPACK_IMPORTED_MODULE_23__.faXmark,\n                                                                    className: \"text-skin-primary\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\GeneralSection.js\",\n                                                                    lineNumber: 1393,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                \" \"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\GeneralSection.js\",\n                                                            lineNumber: 1387,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\GeneralSection.js\",\n                                                    lineNumber: 1380,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"p-8 py-0 space-y-6\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-base xl:text-md 2xl:text-lg leading-relaxed mt-2\",\n                                                        children: \"Mandatory information missing. Do you want to continue?\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\GeneralSection.js\",\n                                                        lineNumber: 1401,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\GeneralSection.js\",\n                                                    lineNumber: 1400,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-end p-6 space-x-2 justify-end\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: closeModal,\n                                                            \"data-modal-hide\": \"default-modal\",\n                                                            type: \"button\",\n                                                            className: \"border border-skin-primary text-skin-primary focus:ring-4 focus:outline-none focus:ring-blue-300 font-medium rounded-md text-md px-6 py-2 text-center \",\n                                                            children: \"Cancel\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\GeneralSection.js\",\n                                                            lineNumber: 1407,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: handleContinueSubmit,\n                                                            \"data-modal-hide\": \"default-modal\",\n                                                            type: \"button\",\n                                                            className: \"text-white bg-skin-primary hover:bg-skin-primary focus:ring-4 focus:outline-none focus:ring-blue-300 font-medium rounded-md text-md px-6 py-2 text-center \",\n                                                            children: \"Continue\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\GeneralSection.js\",\n                                                            lineNumber: 1415,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\GeneralSection.js\",\n                                                    lineNumber: 1406,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\GeneralSection.js\",\n                                            lineNumber: 1378,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\GeneralSection.js\",\n                                        lineNumber: 1376,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\GeneralSection.js\",\n                                    lineNumber: 1367,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\GeneralSection.js\",\n                                lineNumber: 1366,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\GeneralSection.js\",\n                            lineNumber: 1365,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\GeneralSection.js\",\n                    lineNumber: 1352,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\GeneralSection.js\",\n                lineNumber: 1351,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true);\n};\n_s(GeneralSection, \"xVeX6g8P5fQk8E6x9IOObPn4848=\", false, function() {\n    return [\n        next_router__WEBPACK_IMPORTED_MODULE_9__.useRouter,\n        _azure_msal_react__WEBPACK_IMPORTED_MODULE_19__.useMsal,\n        _utils_loaders_loadingContext__WEBPACK_IMPORTED_MODULE_21__.useLoading\n    ];\n});\n_c = GeneralSection;\n/* harmony default export */ __webpack_exports__[\"default\"] = (GeneralSection);\nvar _c;\n$RefreshReg$(_c, \"GeneralSection\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevExports = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevExports) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports on update so we can compare the boundary\n                // signatures.\n                module.hot.dispose(function (data) {\n                    data.prevExports = currentExports;\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevExports !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevExports, currentExports)) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevExports !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/GeneralSection.js\n"));

/***/ })

});