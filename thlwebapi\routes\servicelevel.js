"use strict";

const express = require("express");
const serviceLevelController = require("../controllers/serviceLevel");

const router = express.Router();

const {
  getSLCustomers,
  getIntialSLData,
  getServiceLevelReasons,
  getServiceLevelAuditReasons,
  getServiceLevelResonsMaster,
  addNewReason,
  editReason,
  deleteReason,
  removeSLLocks,deleteBulkReason
} = serviceLevelController;
router.get("/get-customers", getSLCustomers);
router.get(
  "/get-initial-sl-data/:cust_code/:start_date/:end_date/:seeAll/:orderTypeId/:company/:ADCompanyName",
  getIntialSLData
);

router.get("/get-service-level-reasons/:orderId", getServiceLevelReasons);
router.get(
  "/get-service-level-audit-reasons/:orderId",
  getServiceLevelAuditReasons
);
router.get("/get-service-level-reasons-master", getServiceLevelResonsMaster);
router.post("/add-new-reason", addNewReason);
router.put("/edit-reason", editReason);
router.put("/delete-reason", deleteReason);
router.put("/delete-bulk-reasons", deleteBulkReason);
router.post("/remove-locks", removeSLLocks);

module.exports =router;
