import React, { useEffect, useState } from "react";
import {
  FluentProvider,
  SSRProvider,
  webLightTheme,
} from "@fluentui/react-components";
import Layout from "@/components/Layout";
import SLRoot from "@/components/service_level/SLRoot";
import { getSLData } from "@/utils/service-level/utils/getSLData";
import { useMsal } from "@azure/msal-react";
import { toast, ToastContainer } from "react-toastify";
//
import Cookies from "js-cookie";
import { useLoading } from "@/utils/loaders/loadingContext";
import { useServiceCustomers } from "@/utils/serviceCustomerContext";
import { ThreeCircles } from "react-loader-spinner";

const ServiceLevel = () => {
  const { instance } = useMsal();
  const { setIsLoading } = useLoading();


  // State variables
  const [productList, setProductList] = useState([]);
  const [userData, setUserData] = useState({});
  const [customerList, setCustomerList] = useState([]);
  const {updateServiceCustomersList}=useServiceCustomers()
  const [customerSLData, setCustomerSLData] = useState([]);
  const [currentCustomer, setCurrentCustomer] = useState([]);
  const [isTokenExpired, setIsTokenExpired] = useState(false);
  const [requestError, setRequestError] = useState(false);
  const [initalDate, setInitalDate] = useState("");
  const [endDate, setEndDate] = useState("");
  const [allReasonsSubreasons, setAllReasonsSubreasons] = useState({});
  const [loading, setLoading] = useState(true);
  const [initialDataExists, setInitialDataExists] = useState(null);
  const getLastTwoDays = () => {
    const today = new Date();
    const twoWeeksAgo = new Date(today);
    twoWeeksAgo.setDate(today.getDate() -1);

    const formatDate = (date) => {
      const day = String(date.getDate()).padStart(2, "0");
      const month = String(date.getMonth() + 1).padStart(2, "0");
      const year = date.getFullYear();
      return `${month}-${day}-${year}`;
    };

    return {
      start_date: formatDate(twoWeeksAgo),
      end_date: formatDate(today),
    };
  };

  useEffect(() => {
    if (typeof document !== "undefined") {
      document.title = "Service Level";
    }
    const fetchData = async () => {
      const token = Cookies.get("token");
      const slFilters = JSON.parse(Cookies.get("slFilters") || "{}");
      const orderTypeId = slFilters?.orderTypeId || 3;
      const cust_code = slFilters?.cust_code || "All Customers";
      let start_date = slFilters?.start_date || null;
      let end_date = slFilters?.end_date || null;

      if (
        !start_date ||
        start_date === "null" ||
        !end_date ||
        end_date === "null"
      ) {
        const lastTwoWeeks = getLastTwoDays();
        start_date =
          start_date === "null" || !start_date
            ? lastTwoWeeks.start_date
            : start_date;

        end_date =
          end_date === "null" || !end_date ? lastTwoWeeks.end_date : end_date;
      }

      setInitalDate(start_date);
      setEndDate(end_date);

      if (!token) {
        window.location.href = `/login?redirect=${encodeURIComponent(
          window.location.pathname
        )}`;
        return;
      }
      const company = Cookies.get("company");
      const ADCompanyName = Cookies.get("ADCompanyName");

      try {
        const userDataFromCookies = JSON.parse(Cookies.get("user") || "{}");
        setUserData(userDataFromCookies);

        const data = await getSLData(
          `get-initial-sl-data/${cust_code}/${start_date}/${end_date}/true/${orderTypeId}/${company}/${ADCompanyName}`,
          token
        );
        if (!data) {
          setLoading(false);
          setInitialDataExists(false);
          setIsTokenExpired(true);
        } else if (data?.length === 0) {
          setInitialDataExists(false);
          setRequestError(true);
          setLoading(false);
        } else {
          const uniqueCustomersMap = new Map();
          
          uniqueCustomersMap.set("All Customers", { value: "All Customers", label: "All Customers" });
          
          // Add all other customers, overwriting any duplicates
          data.sLCustomers.forEach(customer => {
            uniqueCustomersMap.set(customer.value, customer);
          });
          
          // Convert the Map values back to an array
          const customers = Array.from(uniqueCustomersMap.values());
          
          setCustomerList(customers);
          if(ADCompanyName=="Integrated Service Solutions Ltd"){
            const slServiceCustomers=[
              {value:"All Service Customers",label:"All Service Customers"},
              ...data?.sLServiceCustomers,
            ]
            updateServiceCustomersList(slServiceCustomers)
          }
          setAllReasonsSubreasons(data.allReasonSubReasons);
          const dataArray = Object.values(data.formattedSLData);
          if (dataArray.length > 0) {
            setInitialDataExists(true);
          }else{
            setInitialDataExists(false);
          }
          const sortedData = Object.values(dataArray).sort((a, b) => {
            const dateComparison =
              new Date(a.DEPOT_DATE) - new Date(b.DEPOT_DATE);
            if (dateComparison !== 0) return dateComparison;
            const customerComparison = a.CUSTOMER.localeCompare(b.CUSTOMER);
            if (customerComparison !== 0) return customerComparison;
            return a.PRODUCT_DESCRIPTION.localeCompare(b.PRODUCT_DESCRIPTION);
          });

          setCustomerSLData(sortedData);
          setProductList(
            dataArray?.map((item) => ({
              label: item.PRODUCT_DESCRIPTION,
              value: item.ALT_FILID,
            }))
          );
          setCurrentCustomer(
            cust_code
              ? { value: cust_code, label: cust_code }
              : customers[0] || {}
          );
          setLoading(false);
        }
      } catch (error) {
        setInitialDataExists(false);
        console.error("Error fetching data:", error);
      }
    };

    fetchData();
    setIsLoading(false);
  }, [instance, setIsLoading]);

  useEffect(() => {
    if (isTokenExpired) {
      toast.error("Your session has expired. Please log in again.");
      setTimeout(() => {
        localStorage.removeItem("superUser ");
        localStorage.removeItem("company");
        localStorage.removeItem("id");
        localStorage.removeItem("name");
        localStorage.removeItem("role");
        localStorage.removeItem("email");
        Cookies.remove("user");
        Cookies.remove("theme");
        Cookies.remove("token");
        const redirectUrl = `/login?redirect=${encodeURIComponent(
          window.location.pathname
        )}`;
        logoutHandler(instance, redirectUrl);
      }, 3000);
    } else if (requestError) {
      toast.error(
        "There was an error with your request. Please check your data and try again."
      );
    }
  }, [isTokenExpired, requestError, instance]);
  return (
    <Layout userData={userData}>
      <ToastContainer limit={1} />
      <SSRProvider>
        <FluentProvider
          theme={webLightTheme}
          className="!bg-transparent"
          style={{ fontFamily: "poppinsregular" }}
        >
          {loading ? (
            <div
              style={{
                display: "flex",
                alignItems: "center",
                justifyContent: "center",
                height: "100vh",
              }}
            >
              <ThreeCircles
                color="#002D73"
                height={50}
                width={50}
                visible={true}
                ariaLabel="oval-loading"
                secondaryColor="#0066FF"
                strokeWidth={2}
                strokeWidthSecondary={2}
              />
            </div>
          ) : (
            <>
              {initialDataExists != null && (
                <SLRoot
                  productList={productList}
                  customerList={customerList}
                  customerSLDataOg={customerSLData}
                  initialDataExists={initialDataExists}
                  userData={userData}
                  currentCustomer={currentCustomer}
                  initalDate={initalDate}
                  endDate={endDate}
                  setCustomerList={setCustomerList}
                  allReasonsSubreasons={allReasonsSubreasons}
                  setAllReasonsSubreasons={setAllReasonsSubreasons}
                />
              )}
            </>
          )}
        </FluentProvider>
      </SSRProvider>
    </Layout>
  );
};

export default ServiceLevel;
