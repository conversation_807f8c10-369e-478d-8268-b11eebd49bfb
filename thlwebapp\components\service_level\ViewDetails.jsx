import React, { useEffect, useState } from "react";
import {
  Dialog,
  DialogTrigger,
  DialogSurface,
  DialogTitle,
  DialogBody,
  DialogActions,
  DialogContent,
  Button,
  Avatar,
  Tooltip,
} from "@fluentui/react-components";
import AuditDetails from "./AuditDetails";
import { apiConfig } from "@/services/apiConfig";
import DeleteReasonPopover from "./DeleteReasonPopover";
import { formatDisplay } from "@/utils/whatif/utils/getFormattedDate";
import { getCookieData } from "@/utils/getCookieData";
import { toast } from "react-toastify";
import Cookies from "js-cookie";
//
import { useMsal } from "@azure/msal-react";

const ViewDetails = ({
  data,
  setData,
  setMapForReasonsParentsAndTheirCorrespondingChildren,
  reasonsMasterList,
  parentReasonList,
  reasonsData,
  fetchReasonData,
  userData,
  isOpen,
  setIsOpen,
  isBulkUpdate,
  bulkUpdateData,
  setReasonsData,
  setAllSelectedProducts,
  setBulkUpdateData,
  setIsHeaderChecked,
  setSelectedRows,
}) => {
  const { instance } = useMsal();

  const [lockedBy, setLockedBy] = useState(null);
  const [isBeingEdited, setIsBeingEdited] = useState(null);
  const [selectedReasonDropdownValue, setSelectedReasonDropdownValue] =
    useState("");
  const [subReasonsList, setSubReasonsList] = useState([]);
  const [selectedSubReasonDropdownValue, setSelectedSubReasonDropdownValue] =
    useState("");
  const [quantity, setQuantity] = useState("");
  const [comment, setComment] = useState("");

  const [onEditQuantityValue, setOnEditQuantityValue] = useState("");
  const [onEditSelectedReason, setOnEditSelectedReason] = useState("");
  const [onEditSelectedSubReason, setOnEditSelectedSubReason] = useState("");
  const [onEditComment, setOnEditComment] = useState("");
  const [deleteId, setDeleteId] = useState("");
  const [deleteReason, setDeleteReason] = useState("");
  const [isDeleteTrue, setIsDeleteTrue] = useState(false);
  const [isValidQuantity, setIsValidQuantity] = useState(true);
  const [isValidReason, setIsValidReasons] = useState(true);
  const [isValidSubReason, setIsValidSubReasons] = useState(true);
  const [isValidEditQuantity, setIsValidEditQuantity] = useState(true);
  const [isValidEditReason, setIsValidEditReasons] = useState(true);
  const [isValidEditSubReason, setIsValidEditSubReasons] = useState(true);
  const [invalidEditQuantityId, setInvalidEditQuantityId] = useState("");
  const [invalidEditReasonsId, setInvalidEditReasonsId] = useState("");
  const [invalidEditSubReasonsId, setInvalidEditSubReasonsId] = useState("");
  const [originalEditQuantity, setOriginalEditQuantity] = useState("");
  const [isAuditDetailsOpen, setIsAuditDetailsOpen] = useState(false);
  const [isValidComment, setIsValidComment] = useState(true);
  const [isOtherSelected, setIsOtherSelected] = useState(false);
  const [currentData, setCurrentData] = useState(data);
  const [currentCustomers, setCurrentCustomers] = useState([]);
  const [orderId, setOrderId] = useState([]);
  const [isSaveButtonDisabled, setIsSaveButtonDisabled] = useState(false);

  useEffect(() => {
    setMapForReasonsParentsAndTheirCorrespondingChildren();
  }, [reasonsMasterList]);

  useEffect(() => {
    const ids = data.map((item) => item.ORD_ID);
    const customers = data.map((item) => item.CUSTOMER);
    setOrderId(ids);
    setCurrentData(data);
    setCurrentCustomers(customers);

    return () => {
      setOrderId([]);
    };
  }, []);

  useEffect(() => {
    if (orderId.length > 0) {
      fetchReasonData(orderId, currentCustomers);
    }
  }, [orderId[0], currentCustomers[0]]);

  useEffect(() => {
    if (currentData.length > 0) {
      setLockedBy(currentData[0].LOCKED_BY);
    }
  }, [currentData]);

  useEffect(() => {
    setLockedBy(data[0].LOCKED_BY);
  }, [data[0].LOCKED_BY]);

  useEffect(() => {
    if (isDeleteTrue) {
      handleDeleteReason(orderId);
      setIsDeleteTrue(false);
    }
  }, [isDeleteTrue]);
  const saveReasons = () => {
    setIsSaveButtonDisabled(true);
    if (isBulkUpdate) {
      let totalAddedReasons = bulkUpdateData.totalCasesDifferent;
      setBulkUpdateData((prev) => {
        return {
          ...prev,
          totalCasesDifferent: 0,
          totalCasesAddedReasons: totalAddedReasons,
        };
      });
    }
    const saveData = currentData.map((item) => ({
      quantity: isBulkUpdate
        ? item.CASES_DIFFERENCE
        : typeof quantity === "string"
        ? quantity.trim()
        : quantity,

      reasons: selectedReasonDropdownValue,
      reasonsLabel: reasonsMasterList.filter(
        (r) => r.id == +selectedReasonDropdownValue
      )[0]?.reason,
      subReason: selectedSubReasonDropdownValue,
      subReasonLabel: reasonsMasterList.filter(
        (r) => r.id === selectedSubReasonDropdownValue
      )[0]?.reason,
      comment: comment.trim(),
      orderId: item.ORD_ID, // Use the order ID from the current item
      addedBy: userData.email,
      addedByName: userData.name,
      custCode: item.CUSTOMER,
    }));

    const isValid = saveData.every((item) => {
      const isQuantityValid =
        item.quantity && item.quantity !== "" && item.quantiy !== 0;
      const isReasonValid = item.reasons && item !== "";
      const isSubReasonValid = item.subReason && item.subReason !== "";

      if (selectedReasonDropdownValue === "30" && !item.comment) {
        setIsValidComment(false);
        return;
      } else {
        setIsValidComment(true);
      }

      // Set individual validation states
      if (!isQuantityValid) {
        alert("not valid");
        setIsValidQuantity(false);
      }
      if (!isReasonValid) {
        setIsValidReasons(false);
      }
      if (!isSubReasonValid) {
        setIsValidSubReasons(false);
      }

      return isQuantityValid && isReasonValid && isSubReasonValid;
    });

    // If any of the items are invalid, set the overall validation states
    if (!isValid) {
      return; // Exit if any validation fails
    }
    setIsValidQuantity(true);
    setIsValidReasons(true);
    setIsValidSubReasons(true);
    try {
      const serverAddress = apiConfig.serverAddress;
      const user = getCookieData("user");
      fetch(`${serverAddress}servicelevel/add-new-reason`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${user.token}`,
        },
        body: JSON.stringify(saveData),
      })
        .then((res) => {
          if (res.status == 401) {
            toast.error("Your session has expired. Please log in again.");
            setTimeout(() => {
              localStorage.removeItem("superUser");
              Cookies.remove("company");
              Cookies.remove("ADCompanyName");
              localStorage.removeItem("id");
              localStorage.removeItem("name");
              localStorage.removeItem("role");
              localStorage.removeItem("email");
              Cookies.remove("user");
              Cookies.remove("theme");
              Cookies.remove("token");
              const redirectUrl = `/login?redirect=${encodeURIComponent(
                window.location.pathname
              )}`;
              logoutHandler(instance, redirectUrl);
            }, 3000);
            return null;
          } else if (res.status == 400) {
            toast.error(
              "There was an error with your request. Please check your data and try again."
            );
            return null;
          } else {
            return res.json(); // Ensure you parse the JSON
          }
        })
        .then((json) => {
          if (!isBulkUpdate) {
            fetchReasonData(orderId[0], currentCustomers[0]);
          } else {
            const reasonsDataArr = json.reduce((acc, item) => {
              const existingEntry = acc.find(
                (entry) => entry.reason_id === item.reason_id
              );
              if (existingEntry) {
                existingEntry.quantity += item.quantity;
                existingEntry.id.push(item.id);
                existingEntry.order_id.push(item.order_id);
                existingEntry.cust_codes.push(item.cust_code);
              } else {
                acc.push({
                  added_by: item.added_by,
                  comment: item.comment,
                  delete_reason: null,
                  deleted_by: null,
                  id: [item.id],
                  is_deleted: false,
                  order_id: [item.order_id],
                  quantity: item.quantity,
                  reason: item.reasonsLabel,
                  sub_reason: item.subReasonLabel,
                  reason_id: item.reason_id,
                  subreason_id: item.subreason_id,
                  cust_codes: [item.cust_code],
                });
              }

              return acc;
            }, []);

            // Set the aggregated reasons data
            setReasonsData(reasonsDataArr);
          }
          setQuantity("");
          setComment("");
          setSelectedReasonDropdownValue("");
          setSelectedSubReasonDropdownValue("");
          setIsSaveButtonDisabled(false);
        });
    } catch (error) {
      setIsSaveButtonDisabled(false);
      console.error("Failed to save new reason.", error);
    }
  };

  const handleEdit = (id, orderId) => {
    setIsSaveButtonDisabled(false);
    // console.log("save reasons data",reasonsData);
    if (!isValidEditQuantity) {
      return;
    }

    const editData = currentData.map((item, index) => ({
      quantity: isBulkUpdate ? item.CASES_DIFFERENCE : onEditQuantityValue,
      reasons: onEditSelectedReason,
      reasonsLabel: reasonsMasterList.filter(
        (r) => r.id == +onEditSelectedReason
      )[0]?.reason,
      subReason: onEditSelectedSubReason,
      subReasonLabel: reasonsMasterList.filter(
        (r) => r.id == +onEditSelectedSubReason
      )[0]?.reason,
      comment: onEditComment.trim(),
      orderId: item.ORD_ID,
      id: Array.isArray(id) ? id[index] : id,
      updatedBy: userData.email,
      originalEditQuantity: originalEditQuantity,
      cust_code: item.CUSTOMER,
    }));
    const isValid = editData.every((item) => {
      const isQuantityValid = item.quantity && item.quantity !== "";
      const isReasonValid = item.reasons && item !== "";
      const isSubReasonValid = item.subReason && item.subReason !== "";

      // Set individual validation states
      if (!isQuantityValid) {
        setIsValidEditQuantity(false);
        setInvalidEditQuantityId(item.id);
      }
      if (!isReasonValid) {
        setIsValidEditReasons(false);
        setInvalidEditReasonsId(item.id);
      }
      if (!isSubReasonValid) {
        setIsValidEditSubReasons(false);
        setInvalidEditSubReasonsId(item.id);
      }

      return isQuantityValid && isReasonValid && isSubReasonValid;
    });

    if (!isValid) {
      return;
    }

    setIsValidEditQuantity(true);
    setIsValidEditReasons(true);
    setInvalidEditQuantityId("");
    setInvalidEditReasonsId("");
    setIsBeingEdited(null);
    setOriginalEditQuantity(null);
    setIsValidEditSubReasons(true);
    setInvalidEditSubReasonsId("");

    try {
      const serverAddress = apiConfig.serverAddress;
      const user = getCookieData("user");

      fetch(`${serverAddress}servicelevel/edit-reason`, {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${user.token}`,
        },
        body: JSON.stringify(editData),
      })
        .then((res) => {
          if (res.status == 401) {
            toast.error("Your session has expired. Please log in again.");
            setTimeout(() => {
              localStorage.removeItem("superUser");
              Cookies.remove("company");
              Cookies.remove("ADCompanyName");
              localStorage.removeItem("id");
              localStorage.removeItem("name");
              localStorage.removeItem("role");
              localStorage.removeItem("email");
              Cookies.remove("user");
              Cookies.remove("theme");
              Cookies.remove("token");
              const redirectUrl = `/login?redirect=${encodeURIComponent(
                window.location.pathname
              )}`;
              logoutHandler(instance, redirectUrl);
            }, 3000);
            return null;
          } else if (res.status == 400) {
            toast.error(
              "There was an error with your request. Please check your data and try again."
            );
            return null;
          } else {
            return res.json(); // Ensure you parse the JSON
          }
        })
        .then((json) => {
          if (!isBulkUpdate) {
            fetchReasonData(data[0].ORD_ID, currentCustomers[0]);
          } else {
            const reasonsDataArr = editData.reduce((acc, item) => {
              const existingEntry = acc.find(
                (entry) => parseInt(entry.reason_id) === parseInt(item.reasons)
              );
              if (existingEntry) {
                existingEntry.quantity += item.quantity;
                existingEntry.id.push(item.id);
                existingEntry.order_id.push(item.orderId.toString());
                existingEntry.cust_codes.push(item.cust_code);
              } else {
                acc.push({
                  added_by: item.updatedBy,
                  comment: item.comment,
                  delete_reason: null,
                  deleted_by: null,
                  id: [item.id],
                  is_deleted: false,
                  order_id: [item.orderId.toString()],
                  quantity: item.quantity,
                  reason: item.reasonsLabel,
                  sub_reason: item.subReasonLabel,
                  reason_id: parseInt(item.reasons),
                  subreason_id: parseInt(item.subReason),
                  cust_codes: [item.cust_code],
                });
              }

              return acc;
            }, []);

            // Set the aggregated reasons data
            setReasonsData(reasonsDataArr);
          }
        });
      setIsSaveButtonDisabled(false);
    } catch (error) {
      setIsSaveButtonDisabled(false);
      console.error("Failed to save new reason.", error);
    }
  };

  const handleDeleteReason = (orderId) => {
    // console.log("save reasons data",reasonsData);
    const deleteData = {
      orderId,
      deleteReason: deleteReason,
      id: Array.isArray(deleteId) ? deleteId : [deleteId],
      deletedBy: userData.email,
      deletedByName: userData.name,
    };
    let totalCasesDifferent = bulkUpdateData.totalCasesAddedReasons;
    setBulkUpdateData((prev) => {
      return {
        ...prev,
        totalCasesDifferent: totalCasesDifferent,
      };
    });
    try {
      const serverAddress = apiConfig.serverAddress;
      const user = getCookieData("user");
      fetch(`${serverAddress}servicelevel/delete-reason`, {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${user.token}`,
        },
        body: JSON.stringify(deleteData),
      })
        .then((res) => {
          if (res.status == 401) {
            toast.error("Your session has expired. Please log in again.");
            setTimeout(() => {
              localStorage.removeItem("superUser");
              Cookies.remove("company");
              Cookies.remove("ADCompanyName");
              localStorage.removeItem("id");
              localStorage.removeItem("name");
              localStorage.removeItem("role");
              localStorage.removeItem("email");
              Cookies.remove("user");
              Cookies.remove("theme");
              Cookies.remove("token");
              const redirectUrl = `/login?redirect=${encodeURIComponent(
                window.location.pathname
              )}`;
              logoutHandler(instance, redirectUrl);
            }, 3000);
            return null;
          } else if (res.status == 400) {
            toast.error(
              "There was an error with your request. Please check your data and try again."
            );
            return null;
          } else {
            return res.json(); // Ensure you parse the JSON
          }
        })
        .then((json) => {
          fetchReasonData(orderId, currentCustomers[0]);
        });
    } catch (error) {
      console.error("Failed to save new reason.", error);
    }
  };

  const handleParentDropdownReasonChange = (e, type) => {
    let parentId;
    if (typeof e === "object") {
      parentId = parseInt(e.target.value);
    } else {
      parentId = e;
    }
    if (type == "add") {
      setSelectedReasonDropdownValue(e.target.value);
      setIsValidReasons(true);
      if (e.target.value === "30") {
        setSelectedSubReasonDropdownValue(31);
        setIsValidComment(true);
        setIsOtherSelected(true);
      } else {
        setSelectedSubReasonDropdownValue("");
        setIsOtherSelected(false);
      }
    }
    setSubReasonsList(
      reasonsMasterList.filter((child) => child.parent_id == parentId)
    );
  };

  const handleChildDropdownSubReasonChange = (e) => {
    setSelectedSubReasonDropdownValue(parseInt(e.target.value));
    setIsValidSubReasons(true);
  };

  const handleQuantityChange = (e) => {
    const value = e.target.value;
    setQuantity(value);

    const quantityValue = parseInt(value, 10) || 0;
    22;
    if (
      quantityValue <= 0 ||
      quantityValue > data[0].CASES_DIFFERENCE - data[0].CASES_ADDED_REASONS
    ) {
      setIsValidQuantity(false);
    } else {
      setIsValidQuantity(true);
    }
  };

  const handleCommentChange = (e) => {
    const value = e.target.value;
    setComment(value);
  };
  const handleEditCommentChange = (e) => {
    const value = e.target.value;
    setOnEditComment(value);
  };
  const handleEditQuantity = (e, reasonId) => {
    const value = e.target.value;
    const quantityValue = parseInt(value, 10) || 0;

    let totalExistingQuantity = reasonsData.reduce((total, reason) => {
      return reason.id === reasonId ? total : total + reason.quantity;
    }, 0);

    const maxAllowedQuantity =
      data[0].CASES_DIFFERENCE -
      data[0].CASES_ADDED_REASONS +
      originalEditQuantity;

    if (quantityValue <= 0 || quantityValue > maxAllowedQuantity) {
      setInvalidEditQuantityId(reasonId);
      setIsValidEditQuantity(false);
    } else {
      setInvalidEditQuantityId("");
      setIsValidEditQuantity(true);
    }

    setOnEditQuantityValue(value);
  };

  const handleOpenChange = async (event, data) => {
    setIsOpen(data.open);
    if (!data.open) {
      setIsHeaderChecked(false);
      setLockedBy(false);
      setIsBeingEdited(null);
      setOriginalEditQuantity(null);
      setQuantity("");
      setComment("");
      setOnEditQuantityValue("");
      setOnEditSelectedReason("");
      setOnEditSelectedSubReason("");
      setDeleteId("");
      setDeleteReason("");
      setInvalidEditQuantityId("");
      setInvalidEditReasonsId("");
      setOriginalEditQuantity("");
      setIsValidQuantity(true);
      setIsValidReasons(true);
      setIsValidSubReasons(true);
      setReasonsData([]);
      setData([]);
      setSelectedRows([]);
      const serverAddress = apiConfig.serverAddress;
      const user = getCookieData("user");
      await fetch(`${serverAddress}serviceLevel/remove-locks`, {
        method: "POST",
        headers: {
          Authorization: `Bearer ${user.token}`,
          Accept: "application/json",
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          email: userData.email,
          custCode: currentCustomers,
          orderId: orderId,
          isPayloadRequired: true,
        }),
      }).catch((error) => {
        console.log("error", error);
      });
    }
  };

  useEffect(() => {
    if (
      !isBulkUpdate &&
      data[0] &&
      data[0].CASES_DIFFERENCE - data[0].CASES_ADDED_REASONS != 0
    ) {
      setQuantity(
        String(data[0].CASES_DIFFERENCE - data[0].CASES_ADDED_REASONS)
      );
    } else {
      setQuantity(bulkUpdateData.totalCasesDifferent);
    }
    if (
      quantity <= data[0].CASES_DIFFERENCE - data[0].CASES_ADDED_REASONS &&
      quantity != 0
    ) {
      setIsValidQuantity(true);
    }
  }, [data, isBulkUpdate]);

  return (
    <>
      <Dialog
        modalType="non-modal"
        style={{ fontFamily: "poppinsregular" }}
        open={isOpen}
        onOpenChange={handleOpenChange}
      >
        <DialogSurface
          className="!max-w-[60%]"
          style={{ fontFamily: "poppinsregular" }}
        >
          <DialogBody>
            <DialogTitle>
              <div className="flex justify-between">
                <span className="text-xl">View Details</span>
                {!isBulkUpdate && (
                  <Tooltip
                    content="Audit details for order"
                    relationship="label"
                  >
                    <button
                      onClick={() => setIsAuditDetailsOpen(true)}
                      className="tooltip-button"
                    >
                      <svg
                        fill="currentcolor"
                        xmlns="http://www.w3.org/2000/svg"
                        viewBox="0 0 512 512"
                        className="w-4 h-5 !text-skin-primary"
                      >
                        <path d="M16 96a48 48 0 1 1 96 0A48 48 0 1 1 16 96zM64 208a48 48 0 1 1 0 96 48 48 0 1 1 0-96zm0 160a48 48 0 1 1 0 96 48 48 0 1 1 0-96zM191.5 54.4c5.5-4.2 12.3-6.4 19.2-6.4L424 48c13.3 0 24 10.7 24 24l0 48c0 13.3-10.7 24-24 24l-213.3 0c-6.9 0-13.7-2.2-19.2-6.4l-38.4-28.8c-8.5-6.4-8.5-19.2 0-25.6l38.4-28.8zM153.1 243.2l38.4-28.8c5.5-4.2 12.3-6.4 19.2-6.4L488 208c13.3 0 24 10.7 24 24l0 48c0 13.3-10.7 24-24 24l-277.3 0c-6.9 0-13.7-2.2-19.2-6.4l-38.4-28.8c-8.5-6.4-8.5-19.2 0-25.6zm0 160l38.4-28.8c5.5-4.2 12.3-6.4 19.2-6.4L424 368c13.3 0 24 10.7 24 24l0 48c0 13.3-10.7 24-24 24l-213.3 0c-6.9 0-13.7-2.2-19.2-6.4l-38.4-28.8c-8.5-6.4-8.5-19.2 0-25.6z" />
                      </svg>
                    </button>
                  </Tooltip>
                )}
              </div>
              <hr className="border-b border-gray-200" />
            </DialogTitle>
            <DialogContent>
              <div className="flex flex-col gap-4">
                <div className="flex gap-5 justify-between w-full">
                  {/* <div className="flex gap-5 w-full"> */}

                  <div className="w-1/4 flex flex-col ">
                    <label htmlFor="depotdate" className="text-gray-500">
                      Sales Order / Order Det Id
                    </label>
                    <input
                      type="text"
                      className="px-2 2xl:px-3 border rounded-md w-full"
                      id="depotdate"
                      value={
                        isBulkUpdate
                          ? "Multiple"
                          : `${currentData[0].ORD_NUMBER} / ${currentData[0].ORD_ID}`
                      }
                      readOnly
                      disabled={true}
                    />
                  </div>
                  <div className=" flex w-1/2 flex-col">
                    <label htmlFor="depotdate" className="text-gray-500">
                      Product
                    </label>
                    <input
                      type="text"
                      className="px-2 2xl:px-3 border rounded-md w-full"
                      id="depotdate"
                      value={data[0].PRODUCT_DESCRIPTION}
                      readOnly
                      disabled={true}
                    />
                  </div>

                  <div className=" flex w-1/4 flex-col justify-end ">
                    <div
                      className={`flex items-center justify-center rounded-lg w-28 capitalize h-[30px] px-2 py-1 !text-center ${
                        isBulkUpdate
                          ? "bg-theme-blue2 text-white"
                          : data[0].ORD_STATUS === "Cancelled"
                          ? "bg-[#ff2929] text-white"
                          : data[0].ORD_STATUS === "Open"
                          ? "bg-[#54C5ED] text-white"
                          : data[0].ORD_STATUS === "Invoiced"
                          ? "bg-[#FFAE00] text-white"
                          : data[0].ORD_STATUS === "Delivered"
                          ? "bg-[#3EAB58] text-white"
                          : data[0].ORD_STATUS === "Picked"
                          ? "bg-[#FF6C09] text-white"
                          : data[0].ORD_STATUS === "CANCELLED-Invoiced"
                          ? "bg-cancelled-status text-white"
                          : "bg-qtydiff-status !text-gray-700" // Default style for any other status
                      }`}
                    >
                      {isBulkUpdate
                        ? "Multiple"
                        : `${data[0].ORD_STATUS.toLowerCase()}`}
                    </div>
                  </div>
                </div>
                <div className="flex gap-5 justify-between w-full">
                  <div className="w-1/4 flex flex-col">
                    <label htmlFor="depotdate" className="text-gray-500">
                      Depot Date
                    </label>
                    <input
                      type="text"
                      className="px-2 2xl:px-3 border rounded-md w-full"
                      id="depotdate"
                      value={
                        isBulkUpdate
                          ? "Multiple"
                          : `${formatDisplay(data[0].DEPOT_DATE)}`
                      }
                      readOnly
                      disabled={true}
                    />
                  </div>
                  <div className="w-1/4 flex flex-col">
                    <label htmlFor="altfill" className="text-gray-500">
                      Altfill
                    </label>
                    <input
                      type="text"
                      className="px-2 2xl:px-3 border rounded-md w-full"
                      id="altfill"
                      value={data[0].ALTFILID}
                      readOnly
                      disabled={true}
                    />
                  </div>
                  <div className="w-1/4 flex flex-col">
                    <label htmlFor="customer" className="text-gray-500">
                      Customer
                    </label>
                    <input
                      type="text"
                      className="px-2 2xl:px-3 border rounded-md w-full"
                      id="customer"
                      value={isBulkUpdate ? "Multiple" : `${data[0].CUSTOMER}`}
                      readOnly
                      disabled={true}
                    />
                  </div>
                  <div className="w-1/4 flex flex-col">
                    <label htmlFor="category" className="text-gray-500">
                      Master Product Code
                    </label>
                    <input
                      type="text"
                      className="px-2 2xl:px-3 border rounded-md w-full"
                      id="category"
                      value={data[0].MASTER_PRODUCT_CODE}
                      readOnly
                      disabled={true}
                    />
                  </div>
                </div>

                <div className="flex gap-5 justify-between w-full">
                  <div className="w-1/4 flex flex-col">
                    <label htmlFor="casesize" className="text-gray-500">
                      Case Size
                    </label>
                    <input
                      type="text"
                      className="px-2 2xl:px-3 border rounded-md w-full"
                      id="casesize"
                      value={data[0].CASE_SIZE}
                      readOnly
                      disabled={true}
                    />
                  </div>
                  <div className="w-1/4 flex flex-col">
                    <label htmlFor="casesord" className="text-gray-500">
                      Cases Ordered
                    </label>
                    <input
                      type="text"
                      className="px-2 2xl:px-3 border rounded-md w-full"
                      id="casesord"
                      value={
                        isBulkUpdate
                          ? bulkUpdateData.totalCasesOrdered
                          : `${data[0].CASES_ORIGINAL}`
                      }
                      readOnly
                      disabled={true}
                    />
                  </div>
                  <div className="w-1/4 flex flex-col">
                    <label htmlFor="casedel" className="text-gray-500">
                      Cases Delivered
                    </label>
                    <input
                      type="text"
                      className="px-2 2xl:px-3 border rounded-md w-full"
                      id="casedel"
                      value={
                        isBulkUpdate
                          ? bulkUpdateData.totalCasesDelivered
                          : `${data[0].CASES_DELIVERED}`
                      }
                      readOnly
                      disabled={true}
                    />
                  </div>
                  <div
                    className="w-1/4 flex flex-col"
                    title="The following case differences are the absolute sum of all differences"
                  >
                    <label
                      htmlFor="casesdiff"
                      className="text-gray-500 font-bold"
                    >
                      Cases Different
                    </label>
                    <input
                      type="text"
                      className="px-2 2xl:px-3 border rounded-md w-full font-bold"
                      id="casesdiff"
                      value={
                        isBulkUpdate
                          ? bulkUpdateData.totalCasesDifferent
                          : `${
                              data[0].CASES_DIFFERENCE -
                              data[0].CASES_ADDED_REASONS
                            }`
                      }
                      readOnly
                      disabled={true}
                    />
                  </div>
                </div>
                {/* 
              //#region add reason  
                */}
                {(!isBulkUpdate ||
                  (isBulkUpdate && reasonsData.length == 0)) && (
                  <div className="border border-gray-200 p-4 rounded-md">
                    <span style={{ fontFamily: "poppinsregular" }}>
                      Add the reason(s)
                    </span>
                    <hr className="border-b border-gray-200" />
                    <div className="flex gap-4 justify-between pt-3">
                      <div className="w-[10%] flex flex-col">
                        <label htmlFor="quantity" className={`text-gray-500`}>
                          Quantity
                        </label>
                        <input
                          type="number"
                          onChange={handleQuantityChange}
                          className={`px-2 2xl:px-3 border rounded-md ${
                            !isValidQuantity && "!border-red-500"
                          }`}
                          value={quantity}
                          max={
                            data[0].CASES_DIFFERENCE -
                            data[0].CASES_ADDED_REASONS
                          } // Optional: Set max attribute to limit input
                          id="quantity"
                          disabled={isBeingEdited || isBulkUpdate}
                        />
                      </div>
                      <div className="w-[30%] flex flex-col">
                        <label htmlFor="reason" className="text-gray-500">
                          Reason
                        </label>
                        {/* <input type="text" className='px-2 2xl:px-3 border rounded-md w-full' id="altfill" /> */}
                        <select
                          onChange={(e) =>
                            handleParentDropdownReasonChange(e, "add")
                          }
                          className={`px-2 2xl:px-3 border ${
                            !isValidReason && "!border-red-500"
                          } rounded-md w-full h-[31px]`}
                          value={selectedReasonDropdownValue}
                          id="reason"
                          disabled={isBeingEdited}
                        >
                          <option value={""}>Select...</option>
                          {parentReasonList?.map((parentReason, index) => (
                            <option
                              key={`${parentReason.id}-${index}`}
                              value={parentReason.id}
                            >
                              {parentReason.reason}
                            </option>
                          ))}
                        </select>
                      </div>

                      <div className="w-[30%] flex flex-col">
                        <label htmlFor="subreasons" className="text-gray-500">
                          Sub Reason
                        </label>
                        {/* <input type="text" className='px-2 2xl:px-3 border rounded-md w-full' id="altfill" /> */}
                        <select
                          onChange={handleChildDropdownSubReasonChange}
                          disabled={
                            !selectedReasonDropdownValue ||
                            isBeingEdited ||
                            isOtherSelected
                          }
                          className={`px-2 2xl:px-3 border rounded-md w-full h-[31px] ${
                            !isValidSubReason && "!border-red-500"
                          }`}
                          value={selectedSubReasonDropdownValue}
                          id="subreasons"
                        >
                          <option value={""}>Select...</option>
                          {subReasonsList?.map((subReason, index) => (
                            <option
                              key={`${subReason.id}-${index}`}
                              value={subReason.id}
                            >
                              {subReason.reason}
                            </option>
                          ))}
                        </select>
                      </div>
                      <div className="w-[25%] flex flex-col">
                        <label htmlFor="comment" className="text-gray-500">
                          Comment
                        </label>
                        <input
                          type="text"
                          onChange={handleCommentChange}
                          maxLength={200}
                          className={`px-2 2xl:px-3 border rounded-md w-full ${
                            !isValidComment && "!border-red-500"
                          }`}
                          id="comment"
                          value={comment}
                          disabled={isBeingEdited}
                        />
                      </div>
                      <div className="w-[5%] flex flex-col">
                        <button
                          className="mt-8"
                          onClick={() => saveReasons()}
                          disabled={
                            isSaveButtonDisabled ||
                            isBeingEdited ||
                            !isValidQuantity
                          }
                        >
                          <svg
                            fill="currentcolor"
                            xmlns="http://www.w3.org/2000/svg"
                            viewBox="0 0 512 512"
                            className="w-5 h-5 fill !text-skin-primary"
                          >
                            <path d="M57.6 35.8C23.1 20.6-11.3 57.4 6.1 90.9l63 121.2c4.4 8.4 12.6 14.1 22 15.3c0 0 0 0 0 0L266 249.3c3.4 .4 6 3.3 6 6.7s-2.6 6.3-6 6.7L91.1 284.6s0 0 0 0c-9.4 1.2-17.6 6.9-22 15.3L6.1 421.1c-17.4 33.5 17 70.2 51.6 55.1L492.9 285.3c25.5-11.2 25.5-47.4 0-58.6L57.6 35.8z" />
                          </svg>
                        </button>
                      </div>
                    </div>
                  </div>
                )}
                {/* 
              //#endregion
               */}
                {
                  //#region Edit comments
                }
                <div className="previousreasons flex flex-col gap-3">
                  {/* {reasonData.users.map((reason) => ( */}
                  {reasonsData?.map((reason, index) => (
                    <div key={`${reason.id}-${index}`}>
                      <div className="flex flex-col gap-3 bg-gray-100 rounded-md p-4 my-1">
                        <div className="flex gap-4 justify-between pt-3 ">
                          <div className="w-[10%] flex flex-col">
                            <label htmlFor="quantity" className="text-gray-500">
                              Quantity
                            </label>
                            <input
                              type="number"
                              onChange={(e) => handleEditQuantity(e, reason.id)}
                              className={`px-2 2xl:px-3 border ${
                                invalidEditQuantityId == reason.id &&
                                !isValidEditQuantity &&
                                "!border-red-500"
                              } rounded-md w-full`}
                              id="quantity"
                              disabled={
                                isBeingEdited != reason.id || isBulkUpdate
                              }
                              defaultValue={reason.quantity}
                            />
                          </div>
                          <div className="w-[30%] flex flex-col">
                            <label htmlFor="reason1" className="text-gray-500">
                              Reason
                            </label>
                            {isBeingEdited != reason.id ? (
                              <input
                                type="text"
                                className={`px-2 2xl:px-3 border ${
                                  invalidEditReasonsId == reason.id &&
                                  !isValidEditReason &&
                                  "!border-red-500"
                                } rounded-md w-full`}
                                id="reason1"
                                disabled={isBeingEdited != reason.id}
                                defaultValue={reason.reason}
                              />
                            ) : (
                              <select
                                onChange={(e) => {
                                  const selectedValue = e.target.value;
                                  setOnEditSelectedReason(selectedValue);
                                  setIsValidEditReasons(true);
                                  setInvalidEditReasonsId("");
                                  handleParentDropdownReasonChange(e, "edit");
                                }}
                                className={`px-2 2xl:px-3 border ${
                                  invalidEditReasonsId == reason.id &&
                                  !isValidEditReason &&
                                  "!border-red-500"
                                } rounded-md w-full h-[31px]`}
                                id="reason1"
                                value={onEditSelectedReason}
                              >
                                <option value={""}>Select...</option>
                                {parentReasonList?.map((parentReason,index) => (
                                  <option
                                    key={`${parentReason.id}-${index}`}
                                    value={parentReason.id}
                                  >
                                    {parentReason.reason}
                                  </option>
                                ))}
                              </select>
                            )}
                          </div>
                          <div className="w-[30%] flex flex-col">
                            <label
                              htmlFor="subreasons1"
                              className="text-gray-500"
                            >
                              Sub Reason
                            </label>
                            {isBeingEdited != reason.id ? (
                              <input
                                type="text"
                                onChange={(e) =>
                                  setOnEditSelectedSubReason(e.target.value)
                                }
                                className={`px-2 2xl:px-3 border rounded-md w-full ${
                                  invalidEditSubReasonsId == reason.id &&
                                  !isValidEditSubReason &&
                                  "!border-red-500"
                                }`}
                                id="subreasons1"
                                disabled={isBeingEdited != reason.id}
                                defaultValue={reason.sub_reason}
                              />
                            ) : (
                              <select
                                onChange={(e) =>
                                  setOnEditSelectedSubReason(e.target.value)
                                }
                                className="px-2 2xl:px-3 border rounded-md w-full h-[31px]"
                                id="subreasons1"
                                value={onEditSelectedSubReason}
                              >
                                <option value={""}>Select...</option>
                                {subReasonsList?.map((subReason) => (
                                  <option
                                    key={`${subReason.id}-${index}`}
                                    value={subReason.id}
                                  >
                                    {subReason.reason}
                                  </option>
                                ))}
                              </select>
                            )}
                          </div>
                          <div className="w-[25%] flex flex-col">
                            <label htmlFor="comment" className="text-gray-500">
                              Comment
                            </label>
                            <input
                              type="text"
                              onChange={handleEditCommentChange}
                              className="px-2 2xl:px-3 border rounded-md w-full"
                              id="comment"
                              disabled={isBeingEdited != reason.id}
                              defaultValue={reason.comment}
                            />
                          </div>
                          <div className="w-[5%] flex flex-col"></div>
                        </div>
                        <div className="flex justify-between items-center">
                          <div className="flex gap-2 items-center">
                            <Avatar
                              // initials="LT"
                              color="light-teal"
                              name={reason.added_by}
                            />
                            <span>{reason.added_by} </span>
                            <span className="text-gray-400">{reason.date}</span>
                          </div>
                          <div className="flex justify-end gap-4 pt-2">
                            <DeleteReasonPopover
                              setDeleteId={setDeleteId}
                              setDeleteReason={setDeleteReason}
                              setIsDeleteTrue={setIsDeleteTrue}
                              id={reason.id}
                            />
                            {isBeingEdited && isBeingEdited == reason.id ? (
                              <button>
                                <svg
                                  fill="currentcolor"
                                  xmlns="http://www.w3.org/2000/svg"
                                  viewBox="0 0 512 512"
                                  className="w-5 h-5 !text-skin-primary"
                                  onClick={() =>
                                    handleEdit(reason.id, data[0].ORD_ID)
                                  }
                                  disabled={
                                    !isValidEditQuantity || isSaveButtonDisabled
                                  }
                                >
                                  <path d="M57.6 35.8C23.1 20.6-11.3 57.4 6.1 90.9l63 121.2c4.4 8.4 12.6 14.1 22 15.3c0 0 0 0 0 0L266 249.3c3.4 .4 6 3.3 6 6.7s-2.6 6.3-6 6.7L91.1 284.6s0 0 0 0c-9.4 1.2-17.6 6.9-22 15.3L6.1 421.1c-17.4 33.5 17 70.2 51.6 55.1L492.9 285.3c25.5-11.2 25.5-47.4 0-58.6L57.6 35.8z" />
                                </svg>
                              </button>
                            ) : !isBulkUpdate ? (
                              <button
                                className="b"
                                href=""
                                onClick={() => {
                                  setIsBeingEdited(reason.id);
                                  setOriginalEditQuantity(reason.quantity);
                                  setMapForReasonsParentsAndTheirCorrespondingChildren();
                                  handleParentDropdownReasonChange(
                                    reason.reason_id,
                                    "edit"
                                  );
                                  setOnEditComment(reason.comment);
                                  setOnEditQuantityValue(reason.quantity);
                                  setOnEditSelectedReason(reason.reason_id);
                                  setOnEditSelectedSubReason(
                                    reason.subreason_id
                                  );
                                }}
                              >
                                <svg
                                  fill="currentcolor"
                                  xmlns="http://www.w3.org/2000/svg"
                                  viewBox="0 0 512 512"
                                  className="w-5 h-5 !text-skin-primary"
                                >
                                  <path d="M362.7 19.3L314.3 67.7 444.3 197.7l48.4-48.4c25-25 25-65.5 0-90.5L453.3 19.3c-25-25-65.5-25-90.5 0zm-71 71L58.6 323.5c-10.4 10.4-18 23.3-22.2 37.4L1 481.2C-1.5 489.7 .8 498.8 7 505s15.3 8.5 23.7 6.1l120.3-35.4c14.1-4.2 27-11.8 37.4-22.2L421.7 220.3 291.7 90.3z" />
                                </svg>
                              </button>
                            ) : (
                              ""
                            )}
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </DialogContent>
            <DialogActions className="!mt-3">
              <hr className="border-b border-gray-200" />
              <DialogTrigger disableButtonEnhancement>
                <button className="border rounded-md border-skin-primary text-skin-primary px-5 py-1">
                  Close
                </button>
              </DialogTrigger>
            </DialogActions>
          </DialogBody>
        </DialogSurface>
      </Dialog>
      {isAuditDetailsOpen && (
        <AuditDetails
          orderId={data[0].ORD_ID}
          isAuditDetailsOpen={isAuditDetailsOpen}
          setIsAuditDetailsOpen={setIsAuditDetailsOpen}
        />
      )}
    </>
  );
};

export default ViewDetails;
