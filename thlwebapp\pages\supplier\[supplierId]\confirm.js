import React, { useMemo, useState, useRef, useEffect } from "react";
import Layout from "@/components/Layout";
import { useRouter } from "next/router";
import Link from "next/link";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faPenToSquare } from "@fortawesome/free-solid-svg-icons";
import { AgGridReact } from "ag-grid-react";
import "ag-grid-community/styles//ag-grid.css";
import "ag-grid-community/styles//ag-theme-alpine.css";
import { apiConfig } from "@/services/apiConfig";
import { ThreeCircles } from "react-loader-spinner";
import Cookies from "js-cookie";
import { ToastContainer, toast } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import { useMsal } from "@azure/msal-react";
import { useLoading } from "@/utils/loaders/loadingContext";
import { logout } from "@/utils/secureStorage";

const confirmPage = ({ userData }) => {
  const router = useRouter();
  const { setIsLoading } = useLoading();
  const { supplierId } = router.query;
  const [supplierData, setSupplierData] = useState({});
  const [status, setStatus] = useState("");
  const [rowData, setRowData] = useState([]);
  const [parsedDeliveryData, setParsedDeliveryData] = useState([]);
  const [distributionData, setDistributionData] = useState([]);
  const [parsedData, setParsedData] = useState([]);
  const [linksName, setLinksName] = useState();
  const [sendacGroups, setSendacGroups] = useState();
  const [loading, setLoading] = useState(false);
  const [addSupplier, setAddSupplier] = useState(false);
  const [allowedSections, setAllowedSections] = useState([]);
  const [formNew, setFormNew] = useState(false);
  const [contacts, setContacts] = useState([]);
  const [roleIds, setRoleIds] = useState([]);
  const [isProphetCodeUnqiue, setIsProphetCodeUnqiue] = useState(false);
  const [isCommonError, setCommonError] = useState("");
  const gridRefDistribution = useRef(null);
  const gridRefDelivery = useRef(null);
  const gridRefProcurement = useRef(null);
  const [prophetCode, setProphetCode] = useState("");
  const [prophetsIds, setProphetIds] = useState();
  const [prophetCodeValid, setProphetCodeValid] = useState(true);
  const [accIbanLabel, setAccIbanLabel] = useState("Account Number/IBAN");
  const [sortBicLabel, setSortBicLabel] = useState(
    "Sort (UK)/SWIFT or BIC (non-UK)"
  );
  const [intAccIbanLabel, setIntAccIbanLabel] = useState(
    "Intermediary Account Number/Intermediary IBAN"
  );
  const [hasIban, setHasIban] = useState();

  const defaultColDef = useMemo(() => ({
    sortable: true,
    filter: false,
    resizable: true,
    flex: 1,
    suppressMenu: false,
  }));

  const [columnDefs, setColumnDefs] = useState([
    {
      headerName: "Product Number",
      field: "product_number",
      suppressMenu: true,
      suppressSizeToFit: true,
      headerClass: "header-with-border",
      flex: "4%",
    },
    {
      headerName: "Description",
      headerClass: "header-with-border",
      field: "description",
      flex: "7%",
    },
    {
      headerName: "Brand",
      field: "brand",
      headerClass: "header-with-border",
      flex: "5%",
    },
    {
      headerName: "End Customer",
      headerClass: "header-with-border",
      field: "end_customer",
      flex: "4%",
    },
    {
      headerName: "Agreed Terms",
      field: "agreed_terms_name",
      headerClass: "header-with-border",
      flex: "7%",
    },
    {
      headerName: "Pricing",
      field: "pricing",
      headerClass: "header-with-border",
      flex: "7%",
    },
    {
      headerName: "Yields",
      field: "yields",
      headerClass: "header-with-border",
      flex: "4%",
    },
    {
      headerName: "Start Date",
      field: "startDate",
      headerClass: "header-with-border",
      flex: "7%",
    },
    {
      headerName: "End Date",
      field: "endDate",
      headerClass: "header-with-border",
      flex: "7%",
    },
  ]);

  const deliveryColDef = [
    {
      field: "tableId",
      value: "delivery",
      hide: true,
    },
    {
      headerName: "Delivery terms",
      field: "delivery_terms",
      headerClass: "header-with-border",
      flex: "1%",
    },
    {
      headerName: "Mode of Transport",
      field: "mode_of_transport",
      headerClass: "header-with-border",
    },
  ];

  const distributionColDef = [
    {
      field: "tableId",
      value: "distribution",
      hide: true,
    },
    {
      headerName: "Distribution Point",
      field: "distributionPoint",
      headerClass: "header-with-border",
    },
    {
      headerName: "Direct DP",
      field: "directDPvalue",
      headerClass: "header-with-border",
    },
  ];
  const contactDef = [
    {
      field: "name",
      value: "name",
    },
    {
      headerName: "Email",
      field: "email_id",
      headerClass: "header-with-border",
    },
    {
      headerName: "Telephone",
      field: "telephone",
      headerClass: "header-with-border",
    },
    {
      headerName: "Type of Contact",
      field: "type_of_contact",
      headerClass: "header-with-border",
    },
  ];

  useEffect(() => {
    setProphetIds(Cookies.get("prophets"));
    let isFormNew;
    if (typeof document !== "undefined") {
      document.title = "Confirm";
    }
    if (typeof window != undefined) {
      isFormNew = localStorage.getItem("isFormNew");
      localStorage.removeItem("isFormNew");
    }
    if (isFormNew) {
      setFormNew(isFormNew);
    }
    const sectionsString = localStorage.getItem("allowedSections");
    if (sectionsString) {
      const parsedSections = sectionsString.split(",");
      setAllowedSections(parsedSections);
    } else {
      console.warn("No allowedSections found in localStorage.");
    }
    try {
      let serverAddress = apiConfig.serverAddress;

      fetch(`${serverAddress}suppliers/get-supplier-by-id/${supplierId}`, {
        method: "GET",
        headers: {
          Accept: "application/json",
          "Content-Type": "application/json",
        },
        credentials: "include",
      })
        .then((res) => {
          if (res.status === 400) {
            toast.error(
              "There was an error with your request. Please check your data and try again."
            );
          } else if (res.status === 401) {
            toast.error("Your session has expired. Please log in again.");
            setTimeout(async () => {
              await logout();
              router.push("/login");
            }, 3000);
          }
          if (res.status === 200) {
            return res.json();
          }
          throw new Error("Failed to fetch data");
        })
        .then((data) => {
          handleSortBICLabel(data[0].decryptedSort_Bic, data[0].has_iban);
          setHasIban(data[0].has_iban);
          setStatus(data[0].status);
          setSupplierData(data[0]);
          const parsedContacts = JSON.parse(data[0].contacts_json);
          const formattedContactsJSON = parsedContacts?.map((row) => ({
            name: row.name,
            email_id: row.email_id,
            telephone: row.telephone,
            type_of_contact: row.type_of_contact,
          }));
          setContacts(formattedContactsJSON);
          const parsedRoleIds = JSON.parse(data[0].role_ids);
          const roleIds = parsedRoleIds.map((ele) => ele.role_id);
          setRoleIds(roleIds);
          const parsed = JSON.parse(data[0].dtay_data_json);
          const parsedDeliveryData = JSON.parse(data[0].delivery_json);
          const parsedDistributionData = JSON.parse(
            data[0].distribution_points_json
          );
          const parsedLinksData = JSON.parse(data[0].supplier_links_json);
          const roleMap = new Map();
          parsedLinksData?.forEach((item) => {
            if (!roleMap.has(item.name)) {
              roleMap.set(item.name, {
                id: item.supplier_id,
                role_id: [],
                roles: [],
              });
            }
            roleMap.get(item.name).roles.push(item.role_names);
            roleMap.get(item.name).role_id.push(item.role_id);
          });

          const result = [...roleMap.entries()].map(
            ([name, { id, role_id, roles }]) => ({
              id,
              name,
              role_id,
              role_names: roles.join(" | "),
            })
          );

          const filterResult = result.filter((item) => item.id != supplierId);

          const formattedLinksData = filterResult?.map((row) => ({
            link_name: row.name,
          }));
          const joinedLinksNames = formattedLinksData
            ?.map((item) => item.link_name)
            .join(", ");
          setLinksName(joinedLinksNames);

          const parsedSendacGroups = JSON.parse(
            data[0].supplier_sendac_group_json
          );

          const formattedSendacData = parsedSendacGroups?.map((row) => ({
            group_name: row.label,
          }));
          const joinedSendacGroup = formattedSendacData
            ?.map((item) => item.group_name)
            .join(", ");
          setSendacGroups(joinedSendacGroup);

          const parsedProphetIds = JSON.parse(data[0]?.prophets_id_code);

          const role_ids = JSON.parse(data[0]?.role_ids);
          const roles = role_ids.map((role) => {
            return role.role_id;
          });

          const prophet_id = parsedProphetIds[0].prophet_id;
          const prophet_code = parsedProphetIds[0].prophet_code;

          const isSupplierAccount = roles?.includes(1) || roles?.includes(6);

          let currency =
            data[0]?.symbol == "$" ? `\\${data[0]?.symbol}` : data[0]?.symbol;
          let actualCurr;
          if (currency && currency == "Not Entered") {
            actualCurr = "";
          } else {
            actualCurr = currency;
          }
          let isValid = true;
          if (isSupplierAccount) {
            if (prophet_id == 1) {
              let regexPattern;

              regexPattern = new RegExp(
                `^[A-Z0-9]{4}[A-Z0145678]${actualCurr}$`
              );

              isValid = regexPattern.test(prophet_code);
            } else if (prophet_id == 2) {
              let regexPattern;

              regexPattern = new RegExp(`^([A-Z0]{4})9${actualCurr}$`);

              // let regexPattern = new RegExp(`^([A-Z0]{4})9${actualCurr}$`);
              isValid = regexPattern.test(prophet_code);
            } else if (prophet_id == 3) {
              let regexPattern = new RegExp(
                `^[A-Z0-9]{4}[A-Z01345678][A-Z0-9]*$`
              );

              isValid =
                regexPattern.test(prophet_code) && prophet_code.length == 6;
            } else if (prophet_id == 4) {
              let regexPattern;

              regexPattern = new RegExp(`^([A-Z0]{4})2${actualCurr}$`);

              isValid = regexPattern.test(prophet_code);
            }
          }

          if (!isValid) {
            setProphetCodeValid(false);
          }

          const isUnique = parsedProphetIds[0].code_count;
          // console.log("is unique",isUni)
          setProphetCode(parsedProphetIds[0].prophet_code);
          if (isUnique > 1) {
            setIsProphetCodeUnqiue(false);
          } else {
            setIsProphetCodeUnqiue(true);
          }

          const formattedData = parsed?.map((row) => ({
            id: row.id,
            product_number: row.product_number,
            description: row.description,
            brand: row.brand ? row.brand : "-",
            end_customer: row.end_customer,
            agreed_terms: row.agreed_terms,
            agreed_terms_name: row.agreed_terms_name,
            pricing: row.pricing,
            yields: row.yields,
            startDate: row.start_date,
            endDate: row.end_date,
          }));

          const formattedDeliveryData = parsedDeliveryData?.map((row) => ({
            delivery_terms: row.delivery_terms,
            mode_of_transport: row.mode_of_transport,
          }));

          setParsedDeliveryData(formattedDeliveryData);
          setParsedData(parsed);
          setRowData(formattedData);

          const distributionData = parsedDistributionData?.map((row) => ({
            distributionPoint: row.distribution_name,
            haulier_name: row.haulier_name,
            directDPvalue: row.direct_dp ? "Yes" : "No",
          }));

          setDistributionData(distributionData ? distributionData : null);
          setLoading(false);
        })
        .catch((error) => {
          toast.error("Error fetching data in confirm file:", error.message, {
            position: "top-right",
          });
        });
    } catch (error) {
      toast.error("Error fetching data in confirm file:", error.message, {
        position: "top-right",
      });
    }
    setIsLoading(false);
  }, []);

  function routeFinancial() {
    localStorage.setItem("isEdit", true);
    router.push({
      pathname: `/supplier/${supplierId}/edit/forms`,
      query: { section: 2 },
    });
  }
  function routeProcurement() {
    setLoading(true);
    localStorage.setItem("isEdit", true);
    router.push({
      pathname: `/supplier/${supplierId}/edit/forms`,
      query: { section: 4 },
    });
  }
  function routeCompliance() {
    localStorage.setItem("isEdit", true);
    router.push({
      pathname: `/supplier/${supplierId}/edit/forms`,
      query: { section: 3 },
    });
  }
  function routeSupplier() {
    localStorage.setItem("isEdit", true);
    router.push({ pathname: `/supplier/${supplierId}/edit` });
  }
  function routeGeneral() {
    localStorage.setItem("isEdit", true);
    router.push({
      pathname: `/supplier/${supplierId}/edit/forms`,
      query: { section: 1 },
    });
  }
  const goBack = () => {
    setLoading(true);
    if (typeof window !== "undefined") {
      let current = localStorage.getItem("current");
      if (current) {
        router.push({
          pathname: `/supplier/${supplierId}/edit/forms`,
          query: { section: current },
        });
      } else {
        router.back();
      }
    }
  };

  function handleSortBICLabel(processedValue, hasIban) {
    const sortCodeRegex = /^([A-Z0-9]{6})$/;
    const swiftBicRegex =
      /^([A-Z0-9]{4}[A-Z0-9]{2}[A-Z0-9]{2}|[A-Z0-9]{4}[A-Z0-9]{2}[A-Z0-9]{2}[A-Z0-9]{3})$/;

    if (hasIban == true) {
      setAccIbanLabel("IBAN");
      setIntAccIbanLabel("Intermediary IBAN");
    } else if (hasIban == false) {
      setAccIbanLabel("Account Number");
      setIntAccIbanLabel("Intermediary Account Number");
    } else {
      setAccIbanLabel("Account Number/IBAN");
      setIntAccIbanLabel("Intermediary Account Number/Intermediary IBAN");
    }

    if (sortCodeRegex.test(processedValue)) {
      setSortBicLabel("Sort Code");
    } else if (swiftBicRegex.test(processedValue)) {
      setSortBicLabel("SWIFT or BIC (non-UK)");
    }
  }

  const handleStatus = () => {
    let serverAddress = apiConfig.serverAddress;
    const fetchStatus = supplierData.status;
    let status = "";

    const sectionsToCheck = allowedSections.filter((section) => {
      if (section === "Procurement") {
        return false;
      }
      if (
        section === "Compliance" &&
        !roleIds?.includes(2) &&
        !roleIds?.includes(3)
      ) {
        return false;
      }
      if (
        section == "Financials" &&
        roleIds?.includes(5) &&
        roleIds?.length == 1
      ) {
        return false;
      }
      return true;
    });

    const statusCheck = {
      General: supplierData.technical,
    };
    if (roleIds.length == 1 && roleIds?.includes(4)) {
    } else if (
      roleIds?.includes(2) ||
      roleIds?.includes(3) ||
      roleIds?.includes(4)
    ) {
      statusCheck.Compliance = supplierData.compliance;
    }
    if (roleIds?.includes(1) || roleIds?.includes(6)) {
      statusCheck.Financials = supplierData.financial;
    }

    if (fetchStatus == 2) {
      if (
        sectionsToCheck.every((section) => section in statusCheck) &&
        sectionsToCheck.every((section) => statusCheck[section] == "Complete")
      ) {
        status = 1;
      } else {
        status = 3;
      }
    } else if (
      sectionsToCheck.every((section) => section in statusCheck) &&
      sectionsToCheck.every((section) => statusCheck[section] == "Complete")
    ) {
      status = 1;
    } else if (fetchStatus == 1) {
      status = 1;
    } else if (fetchStatus == 3 && formNew == false) {
      status = 4;
    } else {
      status = fetchStatus;
    }
    setLoading(true);
    fetch(`${serverAddress}suppliers/update-supplier/${supplierId}`, {
      method: "PUT",
      headers: {
        Accept: "application/json",
        "Content-Type": "application/json",
      },
      credentials: "include",
      body: JSON.stringify({
        sectionName: "updateStatus",
        type: "confirmPage",
        status: status,
        updated_date: new Date().toISOString(),
        company_name: supplierData.name,
        isEmergencyRequest: supplierData.isEmergencyRequest,
        requestor_name: supplierData.requestor_name,
        prophetId: prophetsIds,
      }),
    })
      .then((res) => {
        if (res.status === 400) {
          toast.error(
            "There was an error with your request. Please check your data and try again."
          );
        } else if (res.status === 401) {
          toast.error("Your session has expired. Please log in again.");
          setTimeout(async () => {
            await logout();
            router.push("/login");
          }, 3000);
        }
        if (res.status === 200) {
          return res.json();
        }
        return Promise.reject(res);
      })
      .then((json) => {
        toast.success("Supplier data confirmed successfully", {
          position: "top-right",
        });
        setTimeout(() => {
          if (addSupplier) {
            router.replace("/supplier/add");
          } else {
            router.replace("/suppliers");
          }
        }, 4000);
      })
      .catch((err) => {
        setLoading(false);
        toast.error("Error updating status in confirm file:", err.statusText, {
          position: "top-right",
        });
        return err;
      });
  };

  return (
    <Layout userData={userData}>
      <ToastContainer limit={1} />
      {loading ? (
        <div
          style={{
            display: "flex",
            alignItems: "center",
            justifyContent: "center",
            height: "100vh",
          }}
        >
          <ThreeCircles
            color="#002D73"
            height={50}
            width={50}
            visible={true}
            ariaLabel="oval-loading"
            secondaryColor="#0066FF"
            strokeWidth={2}
            strokeWidthSecondary={2}
          />
        </div>
      ) : (
        <div className="relative panel-container bg-white rounded-lg w-[96%] 2xl:w-[calc(100%-70px)] 2xl:h-full p-4 pb-0">
          <div className="m-3">
            <div className="mb-8">
              <div className="mb-3 flex justify-between items-center border-b border-skin-primary">
                <h4 className="subtitles pb-1 w-2/6">Supplier Details</h4>
                {status != 5 && status != 6 ? (
                  <span
                    className="text-skin-primary text-xl cursor-pointer"
                    onClick={routeSupplier}
                  >
                    <FontAwesomeIcon icon={faPenToSquare} />{" "}
                  </span>
                ) : (
                  ""
                )}
              </div>
              <div className="flex">
                <div className="flex flex-col w-1/2">
                  <div className="flex">
                    <label className="labels mb-2 pr-[10px]">
                      Supplier Code:{" "}
                    </label>
                    <p className="ml-5 confirmInputs">
                      {prophetCode}{" "}
                      <span className="text-red-500 text-[10px]">
                        {prophetCode && !isProphetCodeUnqiue
                          ? "(Supplier code is not unique)"
                          : prophetCode && !prophetCodeValid
                          ? "(Supplier code is not valid)"
                          : ""}
                      </span>
                    </p>
                  </div>
                  <div className="flex">
                    <label className="labels mb-2 pr-[10px]">
                      Supplier Name:{" "}
                    </label>
                    <p className="ml-4 confirmInputs">{supplierData.name}</p>
                  </div>
                  <div className="flex">
                    <label className="labels mb-2 pr-[5px]">
                      Prophet System:{" "}
                    </label>
                    <p className="ml-4 confirmInputs">
                      {supplierData.prophet_names}
                    </p>
                  </div>
                  <div className="flex">
                    <label className="labels mb-2 pr-[18px]">
                      Supplier Roles:{" "}
                    </label>
                    <p className="ml-4 confirmInputs">
                      {supplierData.role_names}
                    </p>
                  </div>
                  <div className="flex">
                    <label className="labels mb-2 pr-[12px]">
                      SENDAC Group:{" "}
                    </label>
                    <p
                      className="ml-4 confirmInputs"
                      style={{ textTransform: "capitalize" }}
                    >
                      {sendacGroups}
                    </p>
                  </div>
                  <div className="flex">
                    <label className="labels mb-2 w-[115px]">Linked To: </label>
                    <p
                      className="confirmInputs"
                      style={{ textTransform: "capitalize" }}
                    >
                      {linksName}
                    </p>
                  </div>
                </div>
                <div className="flex flex-col w-1/2 leading-8">
                  <div className="flex">
                    <label className="labels mb-2 pr-[70px]">
                      GDPR Compliant:{" "}
                    </label>
                    <span className="ml-4 confirmInputs">
                      {supplierData &&
                      typeof supplierData.gdpr_compliant === "boolean"
                        ? supplierData.gdpr_compliant
                          ? "Yes"
                          : "No"
                        : null}
                    </span>
                  </div>
                  <div className="flex">
                    <label className="labels mb-2 ">
                      Emergency Supplier Request:{" "}
                    </label>
                    <span className="ml-4 confirmInputs">
                      {supplierData &&
                      typeof supplierData.emergency_request === "boolean"
                        ? supplierData.emergency_request
                          ? "Yes"
                          : "No"
                        : null}
                    </span>
                  </div>
                </div>
              </div>
            </div>

            {allowedSections?.includes("General") ? (
              <div className="mb-8">
                <div className="py-3 flex justify-between items-center border-b border-skin-primary">
                  <div className="flex items-end">
                    <h4 className="subtitles pb-1">General</h4>
                    <span
                      className={`ml-3 border ${
                        supplierData &&
                        allowedSections?.includes("General") &&
                        supplierData.technical == "Complete"
                          ? "border-bright-green text-bright-green px-3 button rounded-md"
                          : supplierData.technical == "Incomplete"
                          ? "border-[#FBB522] text-[#FBB522] px-3 button rounded-md"
                          : supplierData.technical == null ||
                            supplierData.technical == "undefined"
                          ? "border-bright-red text-bright-red button rounded-md px-3"
                          : ""
                      } `}
                    >
                      {supplierData.technical == "Complete"
                        ? "Complete"
                        : "Incomplete"}
                    </span>
                  </div>
                  {status != 5 && status != 6 ? (
                    <span
                      className="text-skin-primary text-xl cursor-pointer"
                      onClick={routeGeneral}
                    >
                      <FontAwesomeIcon icon={faPenToSquare} />
                    </span>
                  ) : (
                    ""
                  )}
                </div>
                <div className="flex flex-col">
                  <div className="flex flex-col w-full">
                    <h4 className="my-5 formtitle bg-[#F3F8FF] w-full">
                      Supplier
                    </h4>
                  </div>
                  <div className="grid grid-cols-4">
                    <label className="labels mb-2 pt-0 ">
                      Trading Name:{" "}
                      <span className="ml-4 confirmInputs">
                        {supplierData.trading_name}
                      </span>
                    </label>
                    <label className="labels mb-2 pt-0 ">
                      Telephone:{" "}
                      <span className="ml-4 confirmInputs">
                        {supplierData.telephone}
                      </span>
                    </label>
                    <label className="labels mb-2 pt-0 ">
                      Email ID:{" "}
                      <span className="ml-4 confirmInputs">
                        {supplierData.email_id}
                      </span>
                    </label>
                  </div>
                </div>
                <div className="flex flex-col">
                  <div className="flex flex-col w-full">
                    <h4 className="my-5 formtitle bg-[#F3F8FF] w-full">
                      Address
                    </h4>
                  </div>
                  <div className="grid grid-cols-4">
                    <label className="labels mb-2 pt-0 ">
                      Address Line 1:{" "}
                      <span className="ml-4 confirmInputs">
                        {supplierData.address_line_1}
                      </span>
                    </label>
                    <label className="labels mb-2 pt-0 ">
                      Address Line 2:{" "}
                      <span className="ml-4 confirmInputs">
                        {supplierData.address_line_2}
                      </span>
                    </label>
                    <label className="labels mb-2 pt-0 ">
                      Address Line 3:{" "}
                      <span className="ml-4 confirmInputs">
                        {supplierData.address_line_3}
                      </span>
                    </label>
                    <label className="labels mb-2 pt-0 ">
                      Address Line 4:{" "}
                      <span className="ml-4 confirmInputs">
                        {supplierData.address_line_4}
                      </span>
                    </label>
                    <label className="labels mb-2 pt-0 ">
                      Country:{" "}
                      <span className="ml-[46px] confirmInputs">
                        {supplierData.country_name}
                      </span>
                    </label>
                    <label className="labels mb-2 pt-0 ">
                      Postal Code/Zip Code:{" "}
                      <span className="ml-[28px] confirmInputs">
                        {supplierData.postal_code}
                      </span>
                    </label>
                  </div>
                </div>
                {roleIds?.includes(1) && (
                  <div className="flex flex-col">
                    <div className="flex flex-col w-full">
                      <h4 className="my-5 formtitle bg-[#F3F8FF] w-full pb-1">
                        Contact
                      </h4>
                    </div>

                    <div
                      className="relative ag-theme-alpine"
                      style={{ height: 220 }}
                    >
                      <AgGridReact
                        rowData={contacts}
                        columnDefs={contactDef}
                        defaultColDef={defaultColDef}
                        tooltipShowDelay={0}
                        tooltipHideDelay={1000}
                      />
                    </div>
                  </div>
                )}
              </div>
            ) : (
              void 0
            )}

            {allowedSections?.includes("Financials") ? (
              <div className="mb-8">
                <div className="py-3 flex justify-between items-center border-b border-skin-primary">
                  <div className="flex items-end">
                    <h4 className="subtitles pb-1">Financials</h4>
                    <span
                      className={`ml-3 border ${
                        supplierData && supplierData.financial == "Complete"
                          ? "border-bright-green text-bright-green px-3 button rounded-md "
                          : supplierData.financial == "Incomplete" ||
                            supplierData.financial == "Rejected" ||
                            supplierData.financial == "Verified"
                          ? "border-[#FBB522] text-[#FBB522] button rounded-md px-3"
                          : supplierData.financial == null ||
                            supplierData.financial == "Not Entered" ||
                            supplierData.financial == "undefined"
                          ? "border-bright-red button text-bright-red  rounded-md px-3 button"
                          : ""
                      } `}
                    >
                      {supplierData.financial == "Complete"
                        ? "Complete"
                        : supplierData.financial == "Incomplete"
                        ? "Incomplete"
                        : supplierData.financial == "Rejected"
                        ? "Rejected"
                        : supplierData.financial == "Verified"
                        ? "Verified"
                        : "Not Entered"}
                    </span>
                  </div>
                  <div onClick={routeFinancial} className="cursor-pointer">
                    {status != 5 && status != 6 ? (
                      <span className="text-skin-primary text-xl">
                        <FontAwesomeIcon icon={faPenToSquare} />
                      </span>
                    ) : (
                      ""
                    )}
                  </div>
                </div>
                <div className="flex flex-col">
                  <div className="flex flex-col w-full">
                    <h4 className="my-5 formtitle bg-[#F3F8FF] w-full">
                      Billing
                    </h4>
                  </div>
                  <div className="grid grid-cols-4">
                    <label className="labels mb-2 pt-0 ">
                      VAT Number:{" "}
                      <span className="ml-4 confirmInputs">
                        {supplierData.vat_number}
                      </span>
                    </label>
                    <label className="labels mb-2 pt-0 ">
                      Company Registration:{" "}
                      <span className="ml-4 confirmInputs">
                        {supplierData.company_registration}
                      </span>
                    </label>
                    <label className="labels mb-2 pt-0 ">
                      Currency:{" "}
                      <span className="ml-4 confirmInputs">
                        {supplierData.currency_name}
                      </span>
                    </label>
                    <label className="labels mb-2 pt-0 ">
                      Country Code:{" "}
                      <span className="ml-4 confirmInputs">
                        {supplierData.country_code}
                      </span>
                    </label>
                    <label className="labels mb-2 pt-0 ">
                      Payment Terms:{" "}
                      <span className="ml-4 confirmInputs">
                        {supplierData.payment_terms}
                      </span>
                    </label>
                    <label className="labels mb-2 pt-0 ">
                      Payment Type:{" "}
                      <span className="ml-4 confirmInputs">
                        {supplierData.payment_type}
                      </span>
                    </label>
                    <label className="labels mb-2 pt-0 ">
                      Vatable:{" "}
                      <span className="ml-4 confirmInputs">
                        {supplierData.vatable
                          ? "Vatable"
                          : supplierData?.vatable == false
                          ? "None vatable"
                          : ""}
                      </span>
                    </label>
                  </div>
                </div>
                <div className="flex flex-col">
                  <div className="flex flex-col w-full">
                    <h4 className="my-5 formtitle bg-[#F3F8FF] w-full">
                      Banking
                    </h4>
                  </div>
                  <div className="grid grid-cols-4">
                    <div className="flex ">
                      <label className="labels mb-2 pt-0  ">
                        {sortBicLabel}:{" "}
                      </label>
                      <span className="ml-4 confirmInputs ">
                        {supplierData.decryptedSort_Bic}
                      </span>
                    </div>
                    <div className="flex ">
                      <label className="labels mb-2 pt-0  ">
                        Bank Name & Branch:{" "}
                      </label>
                      <span className="ml-4 confirmInputs ">
                        {supplierData.decryptedName_branch}
                      </span>
                    </div>
                    <div className="flex ">
                      <label className="labels mb-2 pt-0  ">
                        {accIbanLabel}:{" "}
                      </label>
                      <span className="ml-4 confirmInputs">
                        {supplierData.decryptedAccountNumber}
                      </span>
                    </div>
                    <div className="flex ">
                      <label className="labels mb-2 pt-0  ">
                        {intAccIbanLabel}:{" "}
                      </label>
                      <span className="ml-4 confirmInputs ">
                        {supplierData.decryptedIntermediatery_account_number}
                      </span>
                    </div>
                    <div className="flex ">
                      <label className="labels mb-2 pt-0  ">
                        Validation by Procurement Team:{" "}
                      </label>
                      <span
                        className={`ml-4 confirmInputs ${
                          supplierData.validated_procurement_team
                            ? "text-bright-green"
                            : "text-bright-red"
                        }`}
                      >
                        {supplierData &&
                        supplierData.validated_procurement_team == true
                          ? "Confirmed"
                          : supplierData.validated_procurement_team == false
                          ? "Not Confirmed"
                          : ""}
                      </span>
                    </div>
                    <div className="flex  ">
                      <label className="labels mb-2 pt-0  ">
                        Validation by Financial Team:{" "}
                      </label>
                      <span
                        className={`ml-4 confirmInputs ${
                          supplierData.finance_authorization == 0
                            ? "text-bright-green"
                            : supplierData.finance_authorization == 1
                            ? "text-bright-red"
                            : "text-bright-yellow"
                        }`}
                      >
                        {supplierData && supplierData.finance_authorization == 0
                          ? "Approved"
                          : supplierData.finance_authorization == 1
                          ? "Rejected"
                          : supplierData.finance_authorization == 2
                          ? "Yet to Review"
                          : ""}{" "}
                      </span>
                    </div>
                  </div>
                </div>
                <div className="flex w-full justify-around gap-4">
                  <div className="flex flex-col w-[75%]">
                    <div className="flex flex-col w-full">
                      <h4 className="my-5 formtitle bg-[#F3F8FF] w-full">
                        Delivery
                      </h4>
                    </div>

                    <div
                      className="relative ag-theme-alpine"
                      style={{ height: 220 }}
                    >
                      <AgGridReact
                        id="1"
                        rowData={parsedDeliveryData ? parsedDeliveryData : []}
                        columnDefs={deliveryColDef}
                        defaultColDef={defaultColDef}
                        ref={gridRefDelivery}
                        tooltipShowDelay={0}
                        tooltipHideDelay={1000}
                      />
                    </div>
                  </div>

                  <div className="flex flex-col w-[25%]">
                    <div className="flex flex-col w-full">
                      <h4 className="my-5 formtitle bg-[#F3F8FF] w-full">
                        Distribution Point
                      </h4>
                    </div>

                    <div
                      className="relative ag-theme-alpine"
                      style={{ height: 220 }}
                    >
                      <AgGridReact
                        id="2"
                        rowData={distributionData ? distributionData : []}
                        columnDefs={distributionColDef}
                        defaultColDef={defaultColDef}
                        ref={gridRefDistribution}
                        tooltipShowDelay={0}
                        tooltipHideDelay={1000}
                      />
                    </div>
                  </div>
                </div>
              </div>
            ) : (
              void 0
            )}

            {allowedSections?.includes("Compliance") ? (
              <div className="mb-8">
                <div className="py-3 flex justify-between items-center border-b border-skin-primary">
                  <div className="flex items-end">
                    <h4 className="subtitles pb-1">Compliance</h4>
                    <span
                      className={`${
                        supplierData && supplierData.compliance == "Complete"
                          ? "border-bright-green text-bright-green px-3 button rounded-md ml-3 border"
                          : supplierData.compliance == "Incomplete"
                          ? "border-[#FBB522] text-[#FBB522] ml-3 border px-3 button rounded-md"
                          : supplierData.compliance == "Not Entered" ||
                            supplierData.compliance == "undefined" ||
                            supplierData.compliance == null
                          ? "border-bright-red text-bright-red ml-3 border px-3 button rounded-md"
                          : ""
                      } `}
                    >
                      {supplierData.compliance
                        ? supplierData.compliance
                        : "Not Entered"}
                    </span>
                  </div>
                  {status != 5 && status != 6 ? (
                    <span
                      className="text-skin-primary text-xl cursor-pointer"
                      onClick={routeCompliance}
                    >
                      <FontAwesomeIcon icon={faPenToSquare} />
                    </span>
                  ) : (
                    ""
                  )}
                </div>
                <div className="flex flex-col">
                  <div className="flex flex-col w-full">
                    <h4 className="my-5 formtitle bg-[#F3F8FF] w-full">
                      Regional Certification
                    </h4>
                  </div>
                  <div className="grid grid-cols-4">
                    <label className="labels mb-2 pt-0 ">
                      Red Tactor (UK):{" "}
                      <span className="ml-4 confirmInputs">
                        {supplierData.red_tractor}
                      </span>
                    </label>
                    <label className="labels mb-2 pt-0 ">
                      Puc Code (RSA):{" "}
                      <span className="ml-4 confirmInputs">
                        {supplierData.puc_code}
                      </span>
                    </label>
                    <label className="labels mb-2 pt-0 ">
                      Chile Cert:{" "}
                      <span className="ml-4 confirmInputs">
                        {supplierData.chile_certificate_number}
                      </span>
                    </label>
                  </div>
                </div>
                <div className="flex flex-col">
                  <div className="flex flex-col w-full">
                    <h4 className="my-5 formtitle bg-[#F3F8FF] w-full">
                      Others
                    </h4>
                  </div>
                  <div className="grid grid-cols-4">
                    <label className="labels mb-2 pt-0 ">
                      Organic Certification Number:{" "}
                      <span className="ml-4 confirmInputs">
                        {supplierData.organic_certificate_number}
                      </span>
                    </label>
                    <label className="labels mb-2 pt-0 ">
                      Global Gap Number:{" "}
                      <span className="ml-4 confirmInputs">
                        {supplierData.global_gap_number}
                      </span>
                    </label>
                    <label className="labels mb-2 pt-0 ">
                      Customer Site Code:{" "}
                      <span className="ml-4 confirmInputs">
                        {supplierData.customer_site_code}
                      </span>
                    </label>
                  </div>
                </div>
              </div>
            ) : (
              void 0
            )}

            {allowedSections?.includes("Procurement") ? (
              <div className="mb-8">
                <div className="py-3 flex justify-between items-center border-b border-skin-primary mb-2">
                  <div className="flex items-end">
                    <h4 className="subtitles pb-1">Procurement</h4>
                    <span
                      className={`${
                        supplierData && supplierData.procurement == "Complete"
                          ? "border-bright-green text-bright-green px-5 button rounded-md ml-3 border"
                          : supplierData.procurement == "Not Entered" ||
                            supplierData.procurement == null
                          ? "border-bright-red text-bright-red ml-3 border button rounded-md"
                          : ""
                      } `}
                    >
                      {supplierData.procurement
                        ? supplierData.procurement
                        : "Not Entered"}
                    </span>
                  </div>

                  {status != 5 && status != 6 ? (
                    <span
                      className="text-skin-primary text-xl cursor-pointer"
                      onClick={routeProcurement}
                    >
                      <FontAwesomeIcon icon={faPenToSquare} />{" "}
                    </span>
                  ) : (
                    ""
                  )}
                </div>
                <div className="flex flex-col">
                  <div
                    className="relative ag-theme-alpine"
                    style={{ height: 220 }}
                  >
                    <AgGridReact
                      id="3"
                      rowData={rowData ? rowData : []}
                      columnDefs={columnDefs}
                      defaultColDef={defaultColDef}
                      ref={gridRefProcurement}
                      tooltipShowDelay={0}
                      tooltipHideDelay={1000}
                    />
                  </div>
                </div>
              </div>
            ) : (
              void 0
            )}

            <div className="flex justify-between py-5 border-t border-gray-300">
              {status != 5 && status != 6 ? (
                <div className="flex items-center mb-2">
                  <input
                    id="checked-checkbox"
                    type="checkbox"
                    checked={addSupplier}
                    onChange={(e) => {
                      setAddSupplier(!addSupplier);
                      //handleChangeAnother(e)
                    }}
                    className="w-5 h-5 text-blue bg-white border-theme-blue2 rounded accent-skin-primary"
                  />
                  <label
                    htmlFor="checked-checkbox"
                    className="p-0 ml-3 confirmInputs-600 dark:text-black-600"
                  >
                    Add Another Supplier
                  </label>
                </div>
              ) : (
                ""
              )}

              <div className="flex gap-6">
                <button
                  className="border border-skin-primary text-skin-primary rounded-md button px-8"
                  onClick={goBack}
                >
                  Back
                </button>
                {status != 5 && status != 6 ? (
                  <button
                    onClick={handleStatus}
                    className="border border-skin-primary bg-skin-primary text-white rounded-md button px-8"
                  >
                    Confirm
                  </button>
                ) : (
                  ""
                )}
              </div>
            </div>
          </div>
        </div>
      )}
    </Layout>
  );
};

export default confirmPage;
export const getServerSideProps = async (context) => {
  try {
    // Use secure session validation
    const sessionId = context.req.cookies.thl_session;

    if (!sessionId) {
    return {
      redirect: {
          destination: `/login?redirect=${encodeURIComponent(context.resolvedUrl)}`,
        permanent: false,
      },
    };
  }

    // Validate session with our backend API
    const apiBase = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:8081';
    
    try {
      const response = await fetch(`${apiBase}/api/auth/me`, {
        method: 'GET',
        headers: {
          'Cookie': `thl_session=${sessionId}`,
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        // Session invalid or expired - redirect to login
        return {
          redirect: {
            destination: `/login?redirect=${encodeURIComponent(context.resolvedUrl)}`,
            permanent: false,
          },
        };
      }

      const { user } = await response.json();
      
      // Check if user has permission to access supplier form
      // Typically all authenticated users can access supplier forms, but you can add role restrictions here if needed
      
      return {
        props: {
          userData: user,
        },
      };

    } catch (fetchError) {
      console.error('Session validation failed:', fetchError);
      return {
        redirect: {
          destination: `/login?redirect=${encodeURIComponent(context.resolvedUrl)}`,
          permanent: false,
        },
      };
    }

  } catch (error) {
    console.error('Authentication error:', error);
    return {
      redirect: {
        destination: `/login?redirect=${encodeURIComponent(context.resolvedUrl)}`,
        permanent: false,
      },
    };
  }
}
