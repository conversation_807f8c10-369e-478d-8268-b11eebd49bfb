"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/service_level",{

/***/ "./components/service_level/ViewDetails.jsx":
/*!**************************************************!*\
  !*** ./components/service_level/ViewDetails.jsx ***!
  \**************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _fluentui_react_components__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @fluentui/react-components */ \"./node_modules/@fluentui/react-components/lib/index.js\");\n/* harmony import */ var _AuditDetails__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./AuditDetails */ \"./components/service_level/AuditDetails.jsx\");\n/* harmony import */ var _services_apiConfig__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/services/apiConfig */ \"./services/apiConfig.js\");\n/* harmony import */ var _DeleteReasonPopover__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./DeleteReasonPopover */ \"./components/service_level/DeleteReasonPopover.jsx\");\n/* harmony import */ var _utils_whatif_utils_getFormattedDate__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/utils/whatif/utils/getFormattedDate */ \"./utils/whatif/utils/getFormattedDate.js\");\n/* harmony import */ var react_toastify__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! react-toastify */ \"./node_modules/react-toastify/dist/react-toastify.esm.mjs\");\n/* harmony import */ var js_cookie__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! js-cookie */ \"./node_modules/js-cookie/dist/js.cookie.mjs\");\n/* harmony import */ var _utils_secureStorage__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/utils/secureStorage */ \"./utils/secureStorage.js\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\nconst ViewDetails = (param)=>{\n    let { data, setData, setMapForReasonsParentsAndTheirCorrespondingChildren, reasonsMasterList, parentReasonList, reasonsData, fetchReasonData, userData, isOpen, setIsOpen, isBulkUpdate, bulkUpdateData, setReasonsData, setAllSelectedProducts, setBulkUpdateData, setIsHeaderChecked, setSelectedRows } = param;\n    _s();\n    const [lockedBy, setLockedBy] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isBeingEdited, setIsBeingEdited] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [selectedReasonDropdownValue, setSelectedReasonDropdownValue] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [subReasonsList, setSubReasonsList] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedSubReasonDropdownValue, setSelectedSubReasonDropdownValue] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [quantity, setQuantity] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [comment, setComment] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [onEditQuantityValue, setOnEditQuantityValue] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [onEditSelectedReason, setOnEditSelectedReason] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [onEditSelectedSubReason, setOnEditSelectedSubReason] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [onEditComment, setOnEditComment] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [deleteId, setDeleteId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [deleteReason, setDeleteReason] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [isDeleteTrue, setIsDeleteTrue] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isValidQuantity, setIsValidQuantity] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [isValidReason, setIsValidReasons] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [isValidSubReason, setIsValidSubReasons] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [isValidEditQuantity, setIsValidEditQuantity] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [isValidEditReason, setIsValidEditReasons] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [isValidEditSubReason, setIsValidEditSubReasons] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [invalidEditQuantityId, setInvalidEditQuantityId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [invalidEditReasonsId, setInvalidEditReasonsId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [invalidEditSubReasonsId, setInvalidEditSubReasonsId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [originalEditQuantity, setOriginalEditQuantity] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [isAuditDetailsOpen, setIsAuditDetailsOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isValidComment, setIsValidComment] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [isOtherSelected, setIsOtherSelected] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [currentData, setCurrentData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(data);\n    const [currentCustomers, setCurrentCustomers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [orderId, setOrderId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isSaveButtonDisabled, setIsSaveButtonDisabled] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        setMapForReasonsParentsAndTheirCorrespondingChildren();\n    }, [\n        reasonsMasterList\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const ids = data.map((item)=>item.ORD_ID);\n        const customers = data.map((item)=>item.CUSTOMER);\n        setOrderId(ids);\n        setCurrentData(data);\n        setCurrentCustomers(customers);\n        return ()=>{\n            setOrderId([]);\n        };\n    }, []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (orderId.length > 0) {\n            fetchReasonData(orderId, currentCustomers);\n        }\n    }, [\n        orderId[0],\n        currentCustomers[0]\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (currentData.length > 0) {\n            setLockedBy(currentData[0].LOCKED_BY);\n        }\n    }, [\n        currentData\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        setLockedBy(data[0].LOCKED_BY);\n    }, [\n        data[0].LOCKED_BY\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (isDeleteTrue) {\n            handleDeleteReason(orderId);\n            setIsDeleteTrue(false);\n        }\n    }, [\n        isDeleteTrue\n    ]);\n    const saveReasons = ()=>{\n        setIsSaveButtonDisabled(true);\n        if (isBulkUpdate) {\n            let totalAddedReasons = bulkUpdateData.totalCasesDifferent;\n            setBulkUpdateData((prev)=>{\n                return {\n                    ...prev,\n                    totalCasesDifferent: 0,\n                    totalCasesAddedReasons: totalAddedReasons\n                };\n            });\n        }\n        const saveData = currentData.map((item)=>{\n            var _reasonsMasterList_filter_, _reasonsMasterList_filter_1;\n            return {\n                quantity: isBulkUpdate ? item.CASES_DIFFERENCE : typeof quantity === \"string\" ? quantity.trim() : quantity,\n                reasons: selectedReasonDropdownValue,\n                reasonsLabel: (_reasonsMasterList_filter_ = reasonsMasterList.filter((r)=>r.id == +selectedReasonDropdownValue)[0]) === null || _reasonsMasterList_filter_ === void 0 ? void 0 : _reasonsMasterList_filter_.reason,\n                subReason: selectedSubReasonDropdownValue,\n                subReasonLabel: (_reasonsMasterList_filter_1 = reasonsMasterList.filter((r)=>r.id === selectedSubReasonDropdownValue)[0]) === null || _reasonsMasterList_filter_1 === void 0 ? void 0 : _reasonsMasterList_filter_1.reason,\n                comment: comment.trim(),\n                orderId: item.ORD_ID,\n                addedBy: userData.email,\n                addedByName: userData.name,\n                custCode: item.CUSTOMER\n            };\n        });\n        const isValid = saveData.every((item)=>{\n            const isQuantityValid = item.quantity && item.quantity !== \"\" && item.quantiy !== 0;\n            const isReasonValid = item.reasons && item !== \"\";\n            const isSubReasonValid = item.subReason && item.subReason !== \"\";\n            if (selectedReasonDropdownValue === \"30\" && !item.comment) {\n                setIsValidComment(false);\n                return;\n            } else {\n                setIsValidComment(true);\n            }\n            // Set individual validation states\n            if (!isQuantityValid) {\n                alert(\"not valid\");\n                setIsValidQuantity(false);\n            }\n            if (!isReasonValid) {\n                setIsValidReasons(false);\n            }\n            if (!isSubReasonValid) {\n                setIsValidSubReasons(false);\n            }\n            return isQuantityValid && isReasonValid && isSubReasonValid;\n        });\n        // If any of the items are invalid, set the overall validation states\n        if (!isValid) {\n            return; // Exit if any validation fails\n        }\n        setIsValidQuantity(true);\n        setIsValidReasons(true);\n        setIsValidSubReasons(true);\n        try {\n            const serverAddress = _services_apiConfig__WEBPACK_IMPORTED_MODULE_3__.apiConfig.serverAddress;\n            fetch(\"\".concat(serverAddress, \"servicelevel/add-new-reason\"), {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                credentials: \"include\",\n                body: JSON.stringify(saveData)\n            }).then((res)=>{\n                if (res.status == 401) {\n                    react_toastify__WEBPACK_IMPORTED_MODULE_6__.toast.error(\"Your session has expired. Please log in again.\");\n                    setTimeout(async ()=>{\n                        await (0,_utils_secureStorage__WEBPACK_IMPORTED_MODULE_8__.logout)();\n                        const redirectUrl = \"/login?redirect=\".concat(encodeURIComponent(window.location.pathname));\n                        router.push(redirectUrl);\n                    }, 3000);\n                    return null;\n                } else if (res.status == 400) {\n                    react_toastify__WEBPACK_IMPORTED_MODULE_6__.toast.error(\"There was an error with your request. Please check your data and try again.\");\n                    return null;\n                } else {\n                    return res.json(); // Ensure you parse the JSON\n                }\n            }).then((json)=>{\n                if (!isBulkUpdate) {\n                    fetchReasonData(orderId[0], currentCustomers[0]);\n                } else {\n                    const reasonsDataArr = json.reduce((acc, item)=>{\n                        const existingEntry = acc.find((entry)=>entry.reason_id === item.reason_id);\n                        if (existingEntry) {\n                            existingEntry.quantity += item.quantity;\n                            existingEntry.id.push(item.id);\n                            existingEntry.order_id.push(item.order_id);\n                            existingEntry.cust_codes.push(item.cust_code);\n                        } else {\n                            acc.push({\n                                added_by: item.added_by,\n                                comment: item.comment,\n                                delete_reason: null,\n                                deleted_by: null,\n                                id: [\n                                    item.id\n                                ],\n                                is_deleted: false,\n                                order_id: [\n                                    item.order_id\n                                ],\n                                quantity: item.quantity,\n                                reason: item.reasonsLabel,\n                                sub_reason: item.subReasonLabel,\n                                reason_id: item.reason_id,\n                                subreason_id: item.subreason_id,\n                                cust_codes: [\n                                    item.cust_code\n                                ]\n                            });\n                        }\n                        return acc;\n                    }, []);\n                    // Set the aggregated reasons data\n                    setReasonsData(reasonsDataArr);\n                }\n                setQuantity(\"\");\n                setComment(\"\");\n                setSelectedReasonDropdownValue(\"\");\n                setSelectedSubReasonDropdownValue(\"\");\n                setIsSaveButtonDisabled(false);\n            });\n        } catch (error) {\n            setIsSaveButtonDisabled(false);\n            console.error(\"Failed to save new reason.\", error);\n        }\n    };\n    const handleEdit = (id, orderId)=>{\n        setIsSaveButtonDisabled(false);\n        // console.log(\"save reasons data\",reasonsData);\n        if (!isValidEditQuantity) {\n            return;\n        }\n        const editData = currentData.map((item, index)=>{\n            var _reasonsMasterList_filter_, _reasonsMasterList_filter_1;\n            return {\n                quantity: isBulkUpdate ? item.CASES_DIFFERENCE : onEditQuantityValue,\n                reasons: onEditSelectedReason,\n                reasonsLabel: (_reasonsMasterList_filter_ = reasonsMasterList.filter((r)=>r.id == +onEditSelectedReason)[0]) === null || _reasonsMasterList_filter_ === void 0 ? void 0 : _reasonsMasterList_filter_.reason,\n                subReason: onEditSelectedSubReason,\n                subReasonLabel: (_reasonsMasterList_filter_1 = reasonsMasterList.filter((r)=>r.id == +onEditSelectedSubReason)[0]) === null || _reasonsMasterList_filter_1 === void 0 ? void 0 : _reasonsMasterList_filter_1.reason,\n                comment: onEditComment.trim(),\n                orderId: item.ORD_ID,\n                id: Array.isArray(id) ? id[index] : id,\n                updatedBy: userData.email,\n                originalEditQuantity: originalEditQuantity,\n                cust_code: item.CUSTOMER\n            };\n        });\n        const isValid = editData.every((item)=>{\n            const isQuantityValid = item.quantity && item.quantity !== \"\";\n            const isReasonValid = item.reasons && item !== \"\";\n            const isSubReasonValid = item.subReason && item.subReason !== \"\";\n            // Set individual validation states\n            if (!isQuantityValid) {\n                setIsValidEditQuantity(false);\n                setInvalidEditQuantityId(item.id);\n            }\n            if (!isReasonValid) {\n                setIsValidEditReasons(false);\n                setInvalidEditReasonsId(item.id);\n            }\n            if (!isSubReasonValid) {\n                setIsValidEditSubReasons(false);\n                setInvalidEditSubReasonsId(item.id);\n            }\n            return isQuantityValid && isReasonValid && isSubReasonValid;\n        });\n        if (!isValid) {\n            return;\n        }\n        setIsValidEditQuantity(true);\n        setIsValidEditReasons(true);\n        setInvalidEditQuantityId(\"\");\n        setInvalidEditReasonsId(\"\");\n        setIsBeingEdited(null);\n        setOriginalEditQuantity(null);\n        setIsValidEditSubReasons(true);\n        setInvalidEditSubReasonsId(\"\");\n        try {\n            const serverAddress = _services_apiConfig__WEBPACK_IMPORTED_MODULE_3__.apiConfig.serverAddress;\n            fetch(\"\".concat(serverAddress, \"servicelevel/edit-reason\"), {\n                method: \"PUT\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                credentials: \"include\",\n                body: JSON.stringify(editData)\n            }).then((res)=>{\n                if (res.status == 401) {\n                    react_toastify__WEBPACK_IMPORTED_MODULE_6__.toast.error(\"Your session has expired. Please log in again.\");\n                    setTimeout(async ()=>{\n                        await (0,_utils_secureStorage__WEBPACK_IMPORTED_MODULE_8__.logout)();\n                        const redirectUrl = \"/login?redirect=\".concat(encodeURIComponent(window.location.pathname));\n                        router.push(redirectUrl);\n                    }, 3000);\n                    return null;\n                } else if (res.status == 400) {\n                    react_toastify__WEBPACK_IMPORTED_MODULE_6__.toast.error(\"There was an error with your request. Please check your data and try again.\");\n                    return null;\n                } else {\n                    return res.json(); // Ensure you parse the JSON\n                }\n            }).then((json)=>{\n                if (!isBulkUpdate) {\n                    fetchReasonData(data[0].ORD_ID, currentCustomers[0]);\n                } else {\n                    const reasonsDataArr = editData.reduce((acc, item)=>{\n                        const existingEntry = acc.find((entry)=>parseInt(entry.reason_id) === parseInt(item.reasons));\n                        if (existingEntry) {\n                            existingEntry.quantity += item.quantity;\n                            existingEntry.id.push(item.id);\n                            existingEntry.order_id.push(item.orderId.toString());\n                            existingEntry.cust_codes.push(item.cust_code);\n                        } else {\n                            acc.push({\n                                added_by: item.updatedBy,\n                                comment: item.comment,\n                                delete_reason: null,\n                                deleted_by: null,\n                                id: [\n                                    item.id\n                                ],\n                                is_deleted: false,\n                                order_id: [\n                                    item.orderId.toString()\n                                ],\n                                quantity: item.quantity,\n                                reason: item.reasonsLabel,\n                                sub_reason: item.subReasonLabel,\n                                reason_id: parseInt(item.reasons),\n                                subreason_id: parseInt(item.subReason),\n                                cust_codes: [\n                                    item.cust_code\n                                ]\n                            });\n                        }\n                        return acc;\n                    }, []);\n                    // Set the aggregated reasons data\n                    setReasonsData(reasonsDataArr);\n                }\n            });\n            setIsSaveButtonDisabled(false);\n        } catch (error) {\n            setIsSaveButtonDisabled(false);\n            console.error(\"Failed to save new reason.\", error);\n        }\n    };\n    const handleDeleteReason = (orderId)=>{\n        // console.log(\"save reasons data\",reasonsData);\n        const deleteData = {\n            orderId,\n            deleteReason: deleteReason,\n            id: Array.isArray(deleteId) ? deleteId : [\n                deleteId\n            ],\n            deletedBy: userData.email,\n            deletedByName: userData.name\n        };\n        let totalCasesDifferent = bulkUpdateData.totalCasesAddedReasons;\n        setBulkUpdateData((prev)=>{\n            return {\n                ...prev,\n                totalCasesDifferent: totalCasesDifferent\n            };\n        });\n        try {\n            const serverAddress = _services_apiConfig__WEBPACK_IMPORTED_MODULE_3__.apiConfig.serverAddress;\n            fetch(\"\".concat(serverAddress, \"servicelevel/delete-reason\"), {\n                method: \"PUT\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                credentials: \"include\",\n                body: JSON.stringify(deleteData)\n            }).then((res)=>{\n                if (res.status == 401) {\n                    react_toastify__WEBPACK_IMPORTED_MODULE_6__.toast.error(\"Your session has expired. Please log in again.\");\n                    setTimeout(()=>{\n                        localStorage.removeItem(\"superUser\");\n                        js_cookie__WEBPACK_IMPORTED_MODULE_7__[\"default\"].remove(\"company\");\n                        js_cookie__WEBPACK_IMPORTED_MODULE_7__[\"default\"].remove(\"ADCompanyName\");\n                        localStorage.removeItem(\"id\");\n                        localStorage.removeItem(\"name\");\n                        localStorage.removeItem(\"role\");\n                        localStorage.removeItem(\"email\");\n                        js_cookie__WEBPACK_IMPORTED_MODULE_7__[\"default\"].remove(\"user\");\n                        js_cookie__WEBPACK_IMPORTED_MODULE_7__[\"default\"].remove(\"theme\");\n                        js_cookie__WEBPACK_IMPORTED_MODULE_7__[\"default\"].remove(\"token\");\n                        const redirectUrl = \"/login?redirect=\".concat(encodeURIComponent(window.location.pathname));\n                        logoutHandler(instance, redirectUrl);\n                    }, 3000);\n                    return null;\n                } else if (res.status == 400) {\n                    react_toastify__WEBPACK_IMPORTED_MODULE_6__.toast.error(\"There was an error with your request. Please check your data and try again.\");\n                    return null;\n                } else {\n                    return res.json(); // Ensure you parse the JSON\n                }\n            }).then((json)=>{\n                fetchReasonData(orderId, currentCustomers[0]);\n            });\n        } catch (error) {\n            console.error(\"Failed to save new reason.\", error);\n        }\n    };\n    const handleParentDropdownReasonChange = (e, type)=>{\n        let parentId;\n        if (typeof e === \"object\") {\n            parentId = parseInt(e.target.value);\n        } else {\n            parentId = e;\n        }\n        if (type == \"add\") {\n            setSelectedReasonDropdownValue(e.target.value);\n            setIsValidReasons(true);\n            if (e.target.value === \"30\") {\n                setSelectedSubReasonDropdownValue(31);\n                setIsValidComment(true);\n                setIsOtherSelected(true);\n            } else {\n                setSelectedSubReasonDropdownValue(\"\");\n                setIsOtherSelected(false);\n            }\n        }\n        setSubReasonsList(reasonsMasterList.filter((child)=>child.parent_id == parentId));\n    };\n    const handleChildDropdownSubReasonChange = (e)=>{\n        setSelectedSubReasonDropdownValue(parseInt(e.target.value));\n        setIsValidSubReasons(true);\n    };\n    const handleQuantityChange = (e)=>{\n        const value = e.target.value;\n        setQuantity(value);\n        const quantityValue = parseInt(value, 10) || 0;\n        22;\n        if (quantityValue <= 0 || quantityValue > data[0].CASES_DIFFERENCE - data[0].CASES_ADDED_REASONS) {\n            setIsValidQuantity(false);\n        } else {\n            setIsValidQuantity(true);\n        }\n    };\n    const handleCommentChange = (e)=>{\n        const value = e.target.value;\n        setComment(value);\n    };\n    const handleEditCommentChange = (e)=>{\n        const value = e.target.value;\n        setOnEditComment(value);\n    };\n    const handleEditQuantity = (e, reasonId)=>{\n        const value = e.target.value;\n        const quantityValue = parseInt(value, 10) || 0;\n        let totalExistingQuantity = reasonsData.reduce((total, reason)=>{\n            return reason.id === reasonId ? total : total + reason.quantity;\n        }, 0);\n        const maxAllowedQuantity = data[0].CASES_DIFFERENCE - data[0].CASES_ADDED_REASONS + originalEditQuantity;\n        if (quantityValue <= 0 || quantityValue > maxAllowedQuantity) {\n            setInvalidEditQuantityId(reasonId);\n            setIsValidEditQuantity(false);\n        } else {\n            setInvalidEditQuantityId(\"\");\n            setIsValidEditQuantity(true);\n        }\n        setOnEditQuantityValue(value);\n    };\n    const handleOpenChange = async (event, data)=>{\n        setIsOpen(data.open);\n        if (!data.open) {\n            setIsHeaderChecked(false);\n            setLockedBy(false);\n            setIsBeingEdited(null);\n            setOriginalEditQuantity(null);\n            setQuantity(\"\");\n            setComment(\"\");\n            setOnEditQuantityValue(\"\");\n            setOnEditSelectedReason(\"\");\n            setOnEditSelectedSubReason(\"\");\n            setDeleteId(\"\");\n            setDeleteReason(\"\");\n            setInvalidEditQuantityId(\"\");\n            setInvalidEditReasonsId(\"\");\n            setOriginalEditQuantity(\"\");\n            setIsValidQuantity(true);\n            setIsValidReasons(true);\n            setIsValidSubReasons(true);\n            setReasonsData([]);\n            setData([]);\n            setSelectedRows([]);\n            const serverAddress = _services_apiConfig__WEBPACK_IMPORTED_MODULE_3__.apiConfig.serverAddress;\n            const user = getCookieData(\"user\");\n            await fetch(\"\".concat(serverAddress, \"serviceLevel/remove-locks\"), {\n                method: \"POST\",\n                headers: {\n                    Authorization: \"Bearer \".concat(user.token),\n                    Accept: \"application/json\",\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    email: userData.email,\n                    custCode: currentCustomers,\n                    orderId: orderId,\n                    isPayloadRequired: true\n                })\n            }).catch((error)=>{\n                console.log(\"error\", error);\n            });\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!isBulkUpdate && data[0] && data[0].CASES_DIFFERENCE - data[0].CASES_ADDED_REASONS != 0) {\n            setQuantity(String(data[0].CASES_DIFFERENCE - data[0].CASES_ADDED_REASONS));\n        } else {\n            setQuantity(bulkUpdateData.totalCasesDifferent);\n        }\n        if (quantity <= data[0].CASES_DIFFERENCE - data[0].CASES_ADDED_REASONS && quantity != 0) {\n            setIsValidQuantity(true);\n        }\n    }, [\n        data,\n        isBulkUpdate\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fluentui_react_components__WEBPACK_IMPORTED_MODULE_9__.Dialog, {\n                modalType: \"non-modal\",\n                style: {\n                    fontFamily: \"poppinsregular\"\n                },\n                open: isOpen,\n                onOpenChange: handleOpenChange,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fluentui_react_components__WEBPACK_IMPORTED_MODULE_9__.DialogSurface, {\n                    className: \"!max-w-[60%]\",\n                    style: {\n                        fontFamily: \"poppinsregular\"\n                    },\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fluentui_react_components__WEBPACK_IMPORTED_MODULE_9__.DialogBody, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fluentui_react_components__WEBPACK_IMPORTED_MODULE_9__.DialogTitle, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-xl\",\n                                                children: \"View Details\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                                lineNumber: 616,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            !isBulkUpdate && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fluentui_react_components__WEBPACK_IMPORTED_MODULE_9__.Tooltip, {\n                                                content: \"Audit details for order\",\n                                                relationship: \"label\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>setIsAuditDetailsOpen(true),\n                                                    className: \"tooltip-button\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        fill: \"currentcolor\",\n                                                        xmlns: \"http://www.w3.org/2000/svg\",\n                                                        viewBox: \"0 0 512 512\",\n                                                        className: \"w-4 h-5 !text-skin-primary\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            d: \"M16 96a48 48 0 1 1 96 0A48 48 0 1 1 16 96zM64 208a48 48 0 1 1 0 96 48 48 0 1 1 0-96zm0 160a48 48 0 1 1 0 96 48 48 0 1 1 0-96zM191.5 54.4c5.5-4.2 12.3-6.4 19.2-6.4L424 48c13.3 0 24 10.7 24 24l0 48c0 13.3-10.7 24-24 24l-213.3 0c-6.9 0-13.7-2.2-19.2-6.4l-38.4-28.8c-8.5-6.4-8.5-19.2 0-25.6l38.4-28.8zM153.1 243.2l38.4-28.8c5.5-4.2 12.3-6.4 19.2-6.4L488 208c13.3 0 24 10.7 24 24l0 48c0 13.3-10.7 24-24 24l-277.3 0c-6.9 0-13.7-2.2-19.2-6.4l-38.4-28.8c-8.5-6.4-8.5-19.2 0-25.6zm0 160l38.4-28.8c5.5-4.2 12.3-6.4 19.2-6.4L424 368c13.3 0 24 10.7 24 24l0 48c0 13.3-10.7 24-24 24l-213.3 0c-6.9 0-13.7-2.2-19.2-6.4l-38.4-28.8c-8.5-6.4-8.5-19.2 0-25.6z\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                                            lineNumber: 632,\n                                                            columnNumber: 25\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                                        lineNumber: 626,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                                    lineNumber: 622,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                                lineNumber: 618,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                        lineNumber: 615,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"hr\", {\n                                        className: \"border-b border-gray-200\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                        lineNumber: 638,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                lineNumber: 614,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fluentui_react_components__WEBPACK_IMPORTED_MODULE_9__.DialogContent, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-col gap-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex gap-5 justify-between w-full\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-1/4 flex flex-col \",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            htmlFor: \"depotdate\",\n                                                            className: \"text-gray-500\",\n                                                            children: \"Sales Order / Order Det Id\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                                            lineNumber: 646,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"text\",\n                                                            className: \"px-2 2xl:px-3 border rounded-md w-full\",\n                                                            id: \"depotdate\",\n                                                            value: isBulkUpdate ? \"Multiple\" : \"\".concat(currentData[0].ORD_NUMBER, \" / \").concat(currentData[0].ORD_ID),\n                                                            readOnly: true,\n                                                            disabled: true\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                                            lineNumber: 649,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                                    lineNumber: 645,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \" flex w-1/2 flex-col\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            htmlFor: \"depotdate\",\n                                                            className: \"text-gray-500\",\n                                                            children: \"Product\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                                            lineNumber: 663,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"text\",\n                                                            className: \"px-2 2xl:px-3 border rounded-md w-full\",\n                                                            id: \"depotdate\",\n                                                            value: data[0].PRODUCT_DESCRIPTION,\n                                                            readOnly: true,\n                                                            disabled: true\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                                            lineNumber: 666,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                                    lineNumber: 662,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \" flex w-1/4 flex-col justify-end \",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-center rounded-lg w-28 capitalize h-[30px] px-2 py-1 !text-center \".concat(isBulkUpdate ? \"bg-theme-blue2 text-white\" : data[0].ORD_STATUS === \"Cancelled\" ? \"bg-[#ff2929] text-white\" : data[0].ORD_STATUS === \"Open\" ? \"bg-[#54C5ED] text-white\" : data[0].ORD_STATUS === \"Invoiced\" ? \"bg-[#FFAE00] text-white\" : data[0].ORD_STATUS === \"Delivered\" ? \"bg-[#3EAB58] text-white\" : data[0].ORD_STATUS === \"Picked\" ? \"bg-[#FF6C09] text-white\" : data[0].ORD_STATUS === \"CANCELLED-Invoiced\" ? \"bg-cancelled-status text-white\" : \"bg-qtydiff-status !text-gray-700\" // Default style for any other status\n                                                        ),\n                                                        children: isBulkUpdate ? \"Multiple\" : \"\".concat(data[0].ORD_STATUS.toLowerCase())\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                                        lineNumber: 677,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                                    lineNumber: 676,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                            lineNumber: 642,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex gap-5 justify-between w-full\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-1/4 flex flex-col\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            htmlFor: \"depotdate\",\n                                                            className: \"text-gray-500\",\n                                                            children: \"Depot Date\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                                            lineNumber: 704,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"text\",\n                                                            className: \"px-2 2xl:px-3 border rounded-md w-full\",\n                                                            id: \"depotdate\",\n                                                            value: isBulkUpdate ? \"Multiple\" : \"\".concat((0,_utils_whatif_utils_getFormattedDate__WEBPACK_IMPORTED_MODULE_5__.formatDisplay)(data[0].DEPOT_DATE)),\n                                                            readOnly: true,\n                                                            disabled: true\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                                            lineNumber: 707,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                                    lineNumber: 703,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-1/4 flex flex-col\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            htmlFor: \"altfill\",\n                                                            className: \"text-gray-500\",\n                                                            children: \"Altfill\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                                            lineNumber: 721,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"text\",\n                                                            className: \"px-2 2xl:px-3 border rounded-md w-full\",\n                                                            id: \"altfill\",\n                                                            value: data[0].ALTFILID,\n                                                            readOnly: true,\n                                                            disabled: true\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                                            lineNumber: 724,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                                    lineNumber: 720,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-1/4 flex flex-col\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            htmlFor: \"customer\",\n                                                            className: \"text-gray-500\",\n                                                            children: \"Customer\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                                            lineNumber: 734,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"text\",\n                                                            className: \"px-2 2xl:px-3 border rounded-md w-full\",\n                                                            id: \"customer\",\n                                                            value: isBulkUpdate ? \"Multiple\" : \"\".concat(data[0].CUSTOMER),\n                                                            readOnly: true,\n                                                            disabled: true\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                                            lineNumber: 737,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                                    lineNumber: 733,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-1/4 flex flex-col\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            htmlFor: \"category\",\n                                                            className: \"text-gray-500\",\n                                                            children: \"Master Product Code\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                                            lineNumber: 747,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"text\",\n                                                            className: \"px-2 2xl:px-3 border rounded-md w-full\",\n                                                            id: \"category\",\n                                                            value: data[0].MASTER_PRODUCT_CODE,\n                                                            readOnly: true,\n                                                            disabled: true\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                                            lineNumber: 750,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                                    lineNumber: 746,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                            lineNumber: 702,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex gap-5 justify-between w-full\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-1/4 flex flex-col\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            htmlFor: \"casesize\",\n                                                            className: \"text-gray-500\",\n                                                            children: \"Case Size\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                                            lineNumber: 763,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"text\",\n                                                            className: \"px-2 2xl:px-3 border rounded-md w-full\",\n                                                            id: \"casesize\",\n                                                            value: data[0].CASE_SIZE,\n                                                            readOnly: true,\n                                                            disabled: true\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                                            lineNumber: 766,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                                    lineNumber: 762,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-1/4 flex flex-col\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            htmlFor: \"casesord\",\n                                                            className: \"text-gray-500\",\n                                                            children: \"Cases Ordered\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                                            lineNumber: 776,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"text\",\n                                                            className: \"px-2 2xl:px-3 border rounded-md w-full\",\n                                                            id: \"casesord\",\n                                                            value: isBulkUpdate ? bulkUpdateData.totalCasesOrdered : \"\".concat(data[0].CASES_ORIGINAL),\n                                                            readOnly: true,\n                                                            disabled: true\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                                            lineNumber: 779,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                                    lineNumber: 775,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-1/4 flex flex-col\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            htmlFor: \"casedel\",\n                                                            className: \"text-gray-500\",\n                                                            children: \"Cases Delivered\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                                            lineNumber: 793,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"text\",\n                                                            className: \"px-2 2xl:px-3 border rounded-md w-full\",\n                                                            id: \"casedel\",\n                                                            value: isBulkUpdate ? bulkUpdateData.totalCasesDelivered : \"\".concat(data[0].CASES_DELIVERED),\n                                                            readOnly: true,\n                                                            disabled: true\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                                            lineNumber: 796,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                                    lineNumber: 792,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-1/4 flex flex-col\",\n                                                    title: \"The following case differences are the absolute sum of all differences\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            htmlFor: \"casesdiff\",\n                                                            className: \"text-gray-500 font-bold\",\n                                                            children: \"Cases Different\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                                            lineNumber: 813,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"text\",\n                                                            className: \"px-2 2xl:px-3 border rounded-md w-full font-bold\",\n                                                            id: \"casesdiff\",\n                                                            value: isBulkUpdate ? bulkUpdateData.totalCasesDifferent : \"\".concat(data[0].CASES_DIFFERENCE - data[0].CASES_ADDED_REASONS),\n                                                            readOnly: true,\n                                                            disabled: true\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                                            lineNumber: 819,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                                    lineNumber: 809,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                            lineNumber: 761,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        (!isBulkUpdate || isBulkUpdate && reasonsData.length == 0) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"border border-gray-200 p-4 rounded-md\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    style: {\n                                                        fontFamily: \"poppinsregular\"\n                                                    },\n                                                    children: \"Add the reason(s)\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                                    lineNumber: 842,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"hr\", {\n                                                    className: \"border-b border-gray-200\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                                    lineNumber: 845,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex gap-4 justify-between pt-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-[10%] flex flex-col\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    htmlFor: \"quantity\",\n                                                                    className: \"text-gray-500\",\n                                                                    children: \"Quantity\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                                                    lineNumber: 848,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                    type: \"number\",\n                                                                    onChange: handleQuantityChange,\n                                                                    className: \"px-2 2xl:px-3 border rounded-md \".concat(!isValidQuantity && \"!border-red-500\"),\n                                                                    value: quantity,\n                                                                    max: data[0].CASES_DIFFERENCE - data[0].CASES_ADDED_REASONS,\n                                                                    id: \"quantity\",\n                                                                    disabled: isBeingEdited || isBulkUpdate\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                                                    lineNumber: 851,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                                            lineNumber: 847,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-[30%] flex flex-col\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    htmlFor: \"reason\",\n                                                                    className: \"text-gray-500\",\n                                                                    children: \"Reason\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                                                    lineNumber: 867,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                                    onChange: (e)=>handleParentDropdownReasonChange(e, \"add\"),\n                                                                    className: \"px-2 2xl:px-3 border \".concat(!isValidReason && \"!border-red-500\", \" rounded-md w-full h-[31px]\"),\n                                                                    value: selectedReasonDropdownValue,\n                                                                    id: \"reason\",\n                                                                    disabled: isBeingEdited,\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                            value: \"\",\n                                                                            children: \"Select...\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                                                            lineNumber: 882,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        parentReasonList === null || parentReasonList === void 0 ? void 0 : parentReasonList.map((parentReason, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                value: parentReason.id,\n                                                                                children: parentReason.reason\n                                                                            }, \"\".concat(parentReason.id, \"-\").concat(index), false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                                                                lineNumber: 884,\n                                                                                columnNumber: 29\n                                                                            }, undefined))\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                                                    lineNumber: 871,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                                            lineNumber: 866,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-[30%] flex flex-col\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    htmlFor: \"subreasons\",\n                                                                    className: \"text-gray-500\",\n                                                                    children: \"Sub Reason\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                                                    lineNumber: 895,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                                    onChange: handleChildDropdownSubReasonChange,\n                                                                    disabled: !selectedReasonDropdownValue || isBeingEdited || isOtherSelected,\n                                                                    className: \"px-2 2xl:px-3 border rounded-md w-full h-[31px] \".concat(!isValidSubReason && \"!border-red-500\"),\n                                                                    value: selectedSubReasonDropdownValue,\n                                                                    id: \"subreasons\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                            value: \"\",\n                                                                            children: \"Select...\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                                                            lineNumber: 912,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        subReasonsList === null || subReasonsList === void 0 ? void 0 : subReasonsList.map((subReason, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                value: subReason.id,\n                                                                                children: subReason.reason\n                                                                            }, \"\".concat(subReason.id, \"-\").concat(index), false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                                                                lineNumber: 914,\n                                                                                columnNumber: 29\n                                                                            }, undefined))\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                                                    lineNumber: 899,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                                            lineNumber: 894,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-[25%] flex flex-col\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    htmlFor: \"comment\",\n                                                                    className: \"text-gray-500\",\n                                                                    children: \"Comment\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                                                    lineNumber: 924,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                    type: \"text\",\n                                                                    onChange: handleCommentChange,\n                                                                    maxLength: 200,\n                                                                    className: \"px-2 2xl:px-3 border rounded-md w-full \".concat(!isValidComment && \"!border-red-500\"),\n                                                                    id: \"comment\",\n                                                                    value: comment,\n                                                                    disabled: isBeingEdited\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                                                    lineNumber: 927,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                                            lineNumber: 923,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-[5%] flex flex-col\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                className: \"mt-8\",\n                                                                onClick: ()=>saveReasons(),\n                                                                disabled: isSaveButtonDisabled || isBeingEdited || !isValidQuantity,\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                    fill: \"currentcolor\",\n                                                                    xmlns: \"http://www.w3.org/2000/svg\",\n                                                                    viewBox: \"0 0 512 512\",\n                                                                    className: \"w-5 h-5 fill !text-skin-primary\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                        d: \"M57.6 35.8C23.1 20.6-11.3 57.4 6.1 90.9l63 121.2c4.4 8.4 12.6 14.1 22 15.3c0 0 0 0 0 0L266 249.3c3.4 .4 6 3.3 6 6.7s-2.6 6.3-6 6.7L91.1 284.6s0 0 0 0c-9.4 1.2-17.6 6.9-22 15.3L6.1 421.1c-17.4 33.5 17 70.2 51.6 55.1L492.9 285.3c25.5-11.2 25.5-47.4 0-58.6L57.6 35.8z\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                                                        lineNumber: 955,\n                                                                        columnNumber: 29\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                                                    lineNumber: 949,\n                                                                    columnNumber: 27\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                                                lineNumber: 940,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                                            lineNumber: 939,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                                    lineNumber: 846,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                            lineNumber: 841,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"previousreasons flex flex-col gap-3\",\n                                            children: reasonsData === null || reasonsData === void 0 ? void 0 : reasonsData.map((reason, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex flex-col gap-3 bg-gray-100 rounded-md p-4 my-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex gap-4 justify-between pt-3 \",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"w-[10%] flex flex-col\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                htmlFor: \"quantity\",\n                                                                                className: \"text-gray-500\",\n                                                                                children: \"Quantity\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                                                                lineNumber: 975,\n                                                                                columnNumber: 29\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                type: \"number\",\n                                                                                onChange: (e)=>handleEditQuantity(e, reason.id),\n                                                                                className: \"px-2 2xl:px-3 border \".concat(invalidEditQuantityId == reason.id && !isValidEditQuantity && \"!border-red-500\", \" rounded-md w-full\"),\n                                                                                id: \"quantity\",\n                                                                                disabled: isBeingEdited != reason.id || isBulkUpdate,\n                                                                                defaultValue: reason.quantity\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                                                                lineNumber: 978,\n                                                                                columnNumber: 29\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                                                        lineNumber: 974,\n                                                                        columnNumber: 27\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"w-[30%] flex flex-col\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                htmlFor: \"reason1\",\n                                                                                className: \"text-gray-500\",\n                                                                                children: \"Reason\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                                                                lineNumber: 994,\n                                                                                columnNumber: 29\n                                                                            }, undefined),\n                                                                            isBeingEdited != reason.id ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                type: \"text\",\n                                                                                className: \"px-2 2xl:px-3 border \".concat(invalidEditReasonsId == reason.id && !isValidEditReason && \"!border-red-500\", \" rounded-md w-full\"),\n                                                                                id: \"reason1\",\n                                                                                disabled: isBeingEdited != reason.id,\n                                                                                defaultValue: reason.reason\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                                                                lineNumber: 998,\n                                                                                columnNumber: 31\n                                                                            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                                                onChange: (e)=>{\n                                                                                    const selectedValue = e.target.value;\n                                                                                    setOnEditSelectedReason(selectedValue);\n                                                                                    setIsValidEditReasons(true);\n                                                                                    setInvalidEditReasonsId(\"\");\n                                                                                    handleParentDropdownReasonChange(e, \"edit\");\n                                                                                },\n                                                                                className: \"px-2 2xl:px-3 border \".concat(invalidEditReasonsId == reason.id && !isValidEditReason && \"!border-red-500\", \" rounded-md w-full h-[31px]\"),\n                                                                                id: \"reason1\",\n                                                                                value: onEditSelectedReason,\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                        value: \"\",\n                                                                                        children: \"Select...\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                                                                        lineNumber: 1026,\n                                                                                        columnNumber: 33\n                                                                                    }, undefined),\n                                                                                    parentReasonList === null || parentReasonList === void 0 ? void 0 : parentReasonList.map((parentReason, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                            value: parentReason.id,\n                                                                                            children: parentReason.reason\n                                                                                        }, \"\".concat(parentReason.id, \"-\").concat(index), false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                                                                            lineNumber: 1028,\n                                                                                            columnNumber: 35\n                                                                                        }, undefined))\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                                                                lineNumber: 1010,\n                                                                                columnNumber: 31\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                                                        lineNumber: 993,\n                                                                        columnNumber: 27\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"w-[30%] flex flex-col\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                htmlFor: \"subreasons1\",\n                                                                                className: \"text-gray-500\",\n                                                                                children: \"Sub Reason\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                                                                lineNumber: 1039,\n                                                                                columnNumber: 29\n                                                                            }, undefined),\n                                                                            isBeingEdited != reason.id ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                type: \"text\",\n                                                                                onChange: (e)=>setOnEditSelectedSubReason(e.target.value),\n                                                                                className: \"px-2 2xl:px-3 border rounded-md w-full \".concat(invalidEditSubReasonsId == reason.id && !isValidEditSubReason && \"!border-red-500\"),\n                                                                                id: \"subreasons1\",\n                                                                                disabled: isBeingEdited != reason.id,\n                                                                                defaultValue: reason.sub_reason\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                                                                lineNumber: 1046,\n                                                                                columnNumber: 31\n                                                                            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                                                onChange: (e)=>setOnEditSelectedSubReason(e.target.value),\n                                                                                className: \"px-2 2xl:px-3 border rounded-md w-full h-[31px]\",\n                                                                                id: \"subreasons1\",\n                                                                                value: onEditSelectedSubReason,\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                        value: \"\",\n                                                                                        children: \"Select...\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                                                                        lineNumber: 1069,\n                                                                                        columnNumber: 33\n                                                                                    }, undefined),\n                                                                                    subReasonsList === null || subReasonsList === void 0 ? void 0 : subReasonsList.map((subReason)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                            value: subReason.id,\n                                                                                            children: subReason.reason\n                                                                                        }, \"\".concat(subReason.id, \"-\").concat(index), false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                                                                            lineNumber: 1071,\n                                                                                            columnNumber: 35\n                                                                                        }, undefined))\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                                                                lineNumber: 1061,\n                                                                                columnNumber: 31\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                                                        lineNumber: 1038,\n                                                                        columnNumber: 27\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"w-[25%] flex flex-col\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                htmlFor: \"comment\",\n                                                                                className: \"text-gray-500\",\n                                                                                children: \"Comment\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                                                                lineNumber: 1082,\n                                                                                columnNumber: 29\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                type: \"text\",\n                                                                                onChange: handleEditCommentChange,\n                                                                                className: \"px-2 2xl:px-3 border rounded-md w-full\",\n                                                                                id: \"comment\",\n                                                                                disabled: isBeingEdited != reason.id,\n                                                                                defaultValue: reason.comment\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                                                                lineNumber: 1085,\n                                                                                columnNumber: 29\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                                                        lineNumber: 1081,\n                                                                        columnNumber: 27\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"w-[5%] flex flex-col\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                                                        lineNumber: 1094,\n                                                                        columnNumber: 27\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                                                lineNumber: 973,\n                                                                columnNumber: 25\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex justify-between items-center\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex gap-2 items-center\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fluentui_react_components__WEBPACK_IMPORTED_MODULE_9__.Avatar, {\n                                                                                // initials=\"LT\"\n                                                                                color: \"light-teal\",\n                                                                                name: reason.added_by\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                                                                lineNumber: 1098,\n                                                                                columnNumber: 29\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                children: [\n                                                                                    reason.added_by,\n                                                                                    \" \"\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                                                                lineNumber: 1103,\n                                                                                columnNumber: 29\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-gray-400\",\n                                                                                children: reason.date\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                                                                lineNumber: 1104,\n                                                                                columnNumber: 29\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                                                        lineNumber: 1097,\n                                                                        columnNumber: 27\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex justify-end gap-4 pt-2\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_DeleteReasonPopover__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                                setDeleteId: setDeleteId,\n                                                                                setDeleteReason: setDeleteReason,\n                                                                                setIsDeleteTrue: setIsDeleteTrue,\n                                                                                id: reason.id\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                                                                lineNumber: 1107,\n                                                                                columnNumber: 29\n                                                                            }, undefined),\n                                                                            isBeingEdited && isBeingEdited == reason.id ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                                    fill: \"currentcolor\",\n                                                                                    xmlns: \"http://www.w3.org/2000/svg\",\n                                                                                    viewBox: \"0 0 512 512\",\n                                                                                    className: \"w-5 h-5 !text-skin-primary\",\n                                                                                    onClick: ()=>handleEdit(reason.id, data[0].ORD_ID),\n                                                                                    disabled: !isValidEditQuantity || isSaveButtonDisabled,\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                        d: \"M57.6 35.8C23.1 20.6-11.3 57.4 6.1 90.9l63 121.2c4.4 8.4 12.6 14.1 22 15.3c0 0 0 0 0 0L266 249.3c3.4 .4 6 3.3 6 6.7s-2.6 6.3-6 6.7L91.1 284.6s0 0 0 0c-9.4 1.2-17.6 6.9-22 15.3L6.1 421.1c-17.4 33.5 17 70.2 51.6 55.1L492.9 285.3c25.5-11.2 25.5-47.4 0-58.6L57.6 35.8z\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                                                                        lineNumber: 1127,\n                                                                                        columnNumber: 35\n                                                                                    }, undefined)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                                                                    lineNumber: 1115,\n                                                                                    columnNumber: 33\n                                                                                }, undefined)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                                                                lineNumber: 1114,\n                                                                                columnNumber: 31\n                                                                            }, undefined) : !isBulkUpdate ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                className: \"b\",\n                                                                                href: \"\",\n                                                                                onClick: ()=>{\n                                                                                    setIsBeingEdited(reason.id);\n                                                                                    setOriginalEditQuantity(reason.quantity);\n                                                                                    setMapForReasonsParentsAndTheirCorrespondingChildren();\n                                                                                    handleParentDropdownReasonChange(reason.reason_id, \"edit\");\n                                                                                    setOnEditComment(reason.comment);\n                                                                                    setOnEditQuantityValue(reason.quantity);\n                                                                                    setOnEditSelectedReason(reason.reason_id);\n                                                                                    setOnEditSelectedSubReason(reason.subreason_id);\n                                                                                },\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                                    fill: \"currentcolor\",\n                                                                                    xmlns: \"http://www.w3.org/2000/svg\",\n                                                                                    viewBox: \"0 0 512 512\",\n                                                                                    className: \"w-5 h-5 !text-skin-primary\",\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                        d: \"M362.7 19.3L314.3 67.7 444.3 197.7l48.4-48.4c25-25 25-65.5 0-90.5L453.3 19.3c-25-25-65.5-25-90.5 0zm-71 71L58.6 323.5c-10.4 10.4-18 23.3-22.2 37.4L1 481.2C-1.5 489.7 .8 498.8 7 505s15.3 8.5 23.7 6.1l120.3-35.4c14.1-4.2 27-11.8 37.4-22.2L421.7 220.3 291.7 90.3z\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                                                                        lineNumber: 1156,\n                                                                                        columnNumber: 35\n                                                                                    }, undefined)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                                                                    lineNumber: 1150,\n                                                                                    columnNumber: 33\n                                                                                }, undefined)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                                                                lineNumber: 1131,\n                                                                                columnNumber: 31\n                                                                            }, undefined) : \"\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                                                        lineNumber: 1106,\n                                                                        columnNumber: 27\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                                                lineNumber: 1096,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                                        lineNumber: 972,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                }, \"\".concat(reason.id, \"-\").concat(index), false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                                    lineNumber: 971,\n                                                    columnNumber: 21\n                                                }, undefined))\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                            lineNumber: 968,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                    lineNumber: 641,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                lineNumber: 640,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fluentui_react_components__WEBPACK_IMPORTED_MODULE_9__.DialogActions, {\n                                className: \"!mt-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"hr\", {\n                                        className: \"border-b border-gray-200\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                        lineNumber: 1171,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fluentui_react_components__WEBPACK_IMPORTED_MODULE_9__.DialogTrigger, {\n                                        disableButtonEnhancement: true,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            className: \"border rounded-md border-skin-primary text-skin-primary px-5 py-1\",\n                                            children: \"Close\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                            lineNumber: 1173,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                        lineNumber: 1172,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                lineNumber: 1170,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                        lineNumber: 613,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                    lineNumber: 609,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                lineNumber: 603,\n                columnNumber: 7\n            }, undefined),\n            isAuditDetailsOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_AuditDetails__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                orderId: data[0].ORD_ID,\n                isAuditDetailsOpen: isAuditDetailsOpen,\n                setIsAuditDetailsOpen: setIsAuditDetailsOpen\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                lineNumber: 1182,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true);\n};\n_s(ViewDetails, \"fDS39ZFRHz/erTdGHfQx9Du9dMA=\");\n_c = ViewDetails;\n/* harmony default export */ __webpack_exports__[\"default\"] = (ViewDetails);\nvar _c;\n$RefreshReg$(_c, \"ViewDetails\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevExports = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevExports) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports on update so we can compare the boundary\n                // signatures.\n                module.hot.dispose(function (data) {\n                    data.prevExports = currentExports;\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevExports !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevExports, currentExports)) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevExports !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/service_level/ViewDetails.jsx\n"));

/***/ })

});