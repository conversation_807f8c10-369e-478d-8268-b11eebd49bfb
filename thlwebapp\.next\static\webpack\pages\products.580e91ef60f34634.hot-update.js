"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/products",{

/***/ "./utils/renderer/productStatusRenderer.js":
/*!*************************************************!*\
  !*** ./utils/renderer/productStatusRenderer.js ***!
  \*************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n\nvar _s = $RefreshSig$();\n\nconst colorMap = {\n    reason: {\n        New: \"#3255F4\",\n        Change: \"#49BB7F\",\n        Replacement: \"#ff7f00\",\n        Contingency: \"#49B47F\",\n        default: \"#9A9A9A\"\n    },\n    type: {\n        RM: \"#0066FF\",\n        FG: \"#25AE65\",\n        PK: \"#49BB7F\",\n        default: \"#9A9A9A\"\n    },\n    status: {\n        Complete: \"#fff\",\n        New: \"#fff\",\n        Cancelled: \"#fff\",\n        Contingency: \"#fff\",\n        default: \"#fff\"\n    }\n};\nconst statusColumnStyles = {\n    Completed: {\n        width: \"140px\",\n        backgroundColor: \"#25AE65\",\n        borderRadius: \"5px\",\n        padding: \"5px\",\n        textAlign: \"center\",\n        color: \"white\"\n    },\n    Replacement: {\n        width: \"190px\",\n        color: \"white\",\n        backgroundColor: \"#ff7f00\",\n        borderRadius: \"5px\",\n        padding: \"5px\",\n        textAlign: \"center\"\n    },\n    Submitted: {\n        width: \"140px\",\n        backgroundColor: \"#25AE65\",\n        borderRadius: \"5px\",\n        padding: \"5px\",\n        textAlign: \"center\",\n        color: \"white\"\n    },\n    Updated: {\n        width: \"140px\",\n        color: \"white\",\n        backgroundColor: \"#0066FF\",\n        borderRadius: \"5px\",\n        padding: \"5px\",\n        textAlign: \"center\"\n    },\n    New: {\n        width: \"140px\",\n        backgroundColor: \"#54C5ED\",\n        borderRadius: \"5px\",\n        padding: \"5px\",\n        textAlign: \"center\"\n    },\n    Cancelled: {\n        width: \"140px\",\n        backgroundColor: \"#FF6D29\",\n        borderRadius: \"5px\",\n        padding: \"5px\",\n        textAlign: \"center\"\n    },\n    Draft: {\n        width: \"140px\",\n        backgroundColor: \"#54C5ED\",\n        borderRadius: \"5px\",\n        padding: \"5px\",\n        textAlign: \"center\",\n        color: \"white\"\n    },\n    \"Pending Review\": {\n        backgroundColor: \"#0066FF\",\n        width: \"140px\",\n        borderRadius: \"5px\",\n        padding: \"5px\",\n        textAlign: \"center\",\n        color: \"white\"\n    },\n    \"ISS to Setup\": {\n        width: \"140px\",\n        color: \"white\",\n        backgroundColor: \"#6E3EAB\",\n        borderRadius: \"5px\",\n        padding: \"5px\",\n        textAlign: \"center\"\n    },\n    \"Prophet to Setup\": {\n        width: \"140px\",\n        color: \"white\",\n        backgroundColor: \"#AB6E3E\",\n        borderRadius: \"5px\",\n        padding: \"5px\",\n        textAlign: \"center\"\n    },\n    Rejected: {\n        width: \"140px\",\n        color: \"white\",\n        backgroundColor: \"#F93647\",\n        borderRadius: \"5px\",\n        padding: \"5px\",\n        textAlign: \"center\"\n    },\n    \"Prophet Setup Completed\": {\n        width: \"140px\",\n        color: \"white\",\n        backgroundColor: \"#25AE65\",\n        borderRadius: \"5px\",\n        padding: \"5px\",\n        textAlign: \"center\"\n    },\n    Contingency: {\n        width: \"140px\",\n        color: \"white\",\n        backgroundColor: \"#F93647\",\n        borderRadius: \"5px\",\n        padding: \"10px\",\n        textAlign: \"center\"\n    },\n    \"To be Setup\": {\n        width: \"140px\",\n        color: \"white\",\n        backgroundColor: \"#6E3EAB\",\n        borderRadius: \"5px\",\n        padding: \"5px\",\n        textAlign: \"center\"\n    },\n    \"Setup Complete\": {\n        width: \"140px\",\n        color: \"white\",\n        backgroundColor: \"#F93647\",\n        borderRadius: \"5px\",\n        padding: \"5px\",\n        textAlign: \"center\"\n    }\n};\nconst productStatusRenderer = (params)=>{\n    _s();\n    const isStatusField = params.colDef.field === \"status\";\n    const isReasonField = params.colDef.field === \"reason\";\n    const color = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        var _params_value_trim, _params_value;\n        const fieldMap = colorMap[params.colDef.field];\n        const normalizedValue = (_params_value = params.value) === null || _params_value === void 0 ? void 0 : (_params_value_trim = _params_value.trim) === null || _params_value_trim === void 0 ? void 0 : _params_value_trim.call(_params_value);\n        return fieldMap ? fieldMap[normalizedValue] || fieldMap.default : undefined;\n    }, [\n        params.colDef.field,\n        params.value\n    ]);\n    let valueToDisplay;\n    if (params.value === \"Prophet Setup Completed\") {\n        if (params.data.company === \"dpsltd\" || params.data.company === \"iss\") {\n            valueToDisplay = \"ISS Setup Completed\";\n        } else if (params.data.company === \"efcltd\") {\n            valueToDisplay = \"EFC Setup Completed\";\n        } else if (params.data.company === \"fpp-ltd\") {\n            valueToDisplay = \"FPP Setup Completed\";\n        } else {\n            valueToDisplay = \"Setup Completed\";\n        }\n    } else if (params.value === \"Prophet to Setup\") {\n        if (params.data.company === \"efcltd\") {\n            valueToDisplay = \"EFC to Setup\";\n        } else if (params.data.company === \"fpp-ltd\") {\n            valueToDisplay = \"FPP to Setup\";\n        } else {\n            valueToDisplay = \"ISS to Setup\";\n        }\n    } else {\n        valueToDisplay = params.value;\n    }\n    const spanStyle = {\n        width: \"90px\",\n        textAlign: \"left\",\n        display: \"inline-block\",\n        verticalAlign: \"middle\",\n        lineHeight: \"24px\",\n        height: \"32px\",\n        ...isStatusField ? statusColumnStyles[params.value] || {} : {\n            color\n        },\n        ...isReasonField && {\n            border: \"1px solid \".concat(color),\n            borderRadius: \"0.375rem\",\n            textAlign: \"center\"\n        }\n    };\n    console.log(\"\");\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n        style: spanStyle,\n        children: valueToDisplay\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\utils\\\\renderer\\\\productStatusRenderer.js\",\n        lineNumber: 197,\n        columnNumber: 10\n    }, undefined);\n};\n_s(productStatusRenderer, \"/WjuwVs/0HBZwnTqmkaDvvE9h4w=\");\n/* harmony default export */ __webpack_exports__[\"default\"] = (productStatusRenderer);\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevExports = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevExports) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports on update so we can compare the boundary\n                // signatures.\n                module.hot.dispose(function (data) {\n                    data.prevExports = currentExports;\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevExports !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevExports, currentExports)) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevExports !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./utils/renderer/productStatusRenderer.js\n"));

/***/ })

});