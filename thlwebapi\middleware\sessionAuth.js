"use strict";

const sessionManager = require("./sessionManager");
const jwt = require("jsonwebtoken");
const jwksClient = require("jwks-rsa");
const userData = require("../data/users");

const SESSION_CONFIG = {
  cookieName: 'thl_session',
  maxAge: 8 * 60 * 60 * 1000, // 8 hours
  secure: process.env.NODE_ENV === 'production',
  sameSite: 'strict'
};

const MSAL_CONFIG = {
  CLIENT_ID: process.env.CLIENT_ID,
  TENANT_ID: process.env.TENANT_ID,
};

// JWKS clients for token verification
const jwksClientV1 = () => {
  return jwksClient({
    jwksUri: `https://login.microsoftonline.com/${MSAL_CONFIG.TENANT_ID}/discovery/keys`,
    cache: true,
    cacheMaxEntries: 5,
    cacheMaxAge: 10 * 60 * 1000,
  });
};

const jwksClientV2 = () => {
  return jwksClient({
    jwksUri: `https://login.microsoftonline.com/${MSAL_CONFIG.TENANT_ID}/discovery/v2.0/keys`,
    cache: true,
    cacheMaxEntries: 5,
    cacheMaxAge: 10 * 60 * 1000,
  });
};

// Verify token from request body (sent during login)
const verifyTokenFromBody = async (token) => {
  if (!token) {
    return false;
  }

  try {
    const decodedForDebug = jwt.decode(token, { complete: true });
    const tokenIssuer = decodedForDebug.payload.iss;
    const tokenAudience = decodedForDebug.payload.aud;
    
    const validIssuers = [
      `https://sts.windows.net/${MSAL_CONFIG.TENANT_ID}/`,
      `https://login.microsoftonline.com/${MSAL_CONFIG.TENANT_ID}/v2.0`,
    ];
    
    const validAudiences = [
      MSAL_CONFIG.CLIENT_ID,
      `api://${MSAL_CONFIG.CLIENT_ID}`,
    ];
    
    if (!validIssuers.includes(tokenIssuer) || !validAudiences.includes(tokenAudience)) {
      return false;
    }
    
    const dynamicVerifyOptions = {
      algorithms: ["RS256"],
      issuer: tokenIssuer,
      audience: tokenAudience,
    };
    
    let jwksClientInstance;
    if (tokenIssuer.includes('sts.windows.net')) {
      jwksClientInstance = jwksClientV1();
    } else {
      jwksClientInstance = jwksClientV2();
    }

    const decoded = await new Promise((resolve, reject) => {
      jwt.verify(token, (header, callback) => {
        jwksClientInstance.getSigningKey(header.kid, (err, key) => {
          if (err) {
            console.error('Failed to get signing key:', err);
            return callback(err);
          }
          const signingKey = key.getPublicKey();
          callback(null, signingKey);
        });
      }, dynamicVerifyOptions, (err, decoded) => {
        if (err) reject(err);
        else resolve(decoded);
      });
    });

    return {
      name: decoded.given_name + " " + decoded.family_name,
      preferred_username: decoded.upn || decoded.preferred_username || decoded.email,
      user_id: decoded.sub,
      roles: decoded.roles || [],
      scopes: (decoded.scp || decoded.scope || "").split(' ')
    };

  } catch (error) {
    console.error("❌ Token verification failed:", error.message);
    return false;
  }
};

// Helper function to extract company from email
const extractCompanyName = (email) => {
  if (!email) return 'unknown';
  const domain = email.split("@")[1];
  return domain.split(".")[0];
};

// Helper function to get company details from Microsoft Graph
const getCompanyDetails = async (graphToken) => {
  try {
    console.log("Calling Graph API with token...");
    const response = await fetch(
      "https://graph.microsoft.com/v1.0/me?$select=displayName,mail,companyName",
      {
        headers: {
          Authorization: `Bearer ${graphToken}`,
          "Content-Type": "application/json",
        },
      }
    );
    
    if (response.ok) {
      const data = await response.json();
      console.log("Graph API response:", data);
      return data;
    } else {
      const error = await response.json();
      console.error("Graph API error:", error);
      return null;
    }
  } catch (error) {
    console.error("Error fetching company details:", error);
    return null;
  }
};

// Helper function to get user role and department from database
const getUserRoleData = async (email) => {
  try {
    const userResult = await userData.getUserByEmail(email);
    if (userResult && userResult.length > 0) {
      return {
        user_id: userResult[0].user_id,
        role_id: userResult[0].role_id,
        department_id: userResult[0].department_id,
        created_date: userResult[0].created_date
      };
    }
    return null;
  } catch (error) {
    console.error("Error fetching user role data:", error);
    return null;
  }
};

const createUserSession = async (req, res, next) => {
  try {
    // Get token from Authorization header (sent during login)
    const authHeader = req.headers.authorization;
    const token = authHeader?.replace("Bearer ", "");
    
    if (!token) {
      return res.status(401).json({ error: 'No token provided' });
    }

    // Verify the token
    const user = await verifyTokenFromBody(token);
    
    if (!user) {
      return res.status(401).json({ error: 'Invalid token' });
    }

    // Extract company information from email
    const company = extractCompanyName(user.preferred_username);
    
    // Get Graph token from request body (sent from frontend)
    const { graphToken } = req.body || {};
    
    let ADCompanyName = company.toUpperCase(); // fallback
    let userName = user.name; // fallback to JWT token name
    
    // Try to get actual company name and display name from Microsoft Graph if token provided
    if (graphToken) {
      const companyDetails = await getCompanyDetails(graphToken);
      if (companyDetails) {
        if (companyDetails.companyName) {
          ADCompanyName = companyDetails.companyName;
          console.log("Retrieved company name from Graph:", ADCompanyName);
        }
        if (companyDetails.displayName) {
          userName = companyDetails.displayName;
          console.log("Retrieved display name from Graph:", userName);
        }
      }
    }

    // Get user role and department data from database
    const roleData = await getUserRoleData(user.preferred_username);
    
    // Get theme data for the company
    let themeData = null;
    try {
      const themeResult = await userData.getTheme(company, ADCompanyName);
      themeData = themeResult && themeResult.length > 0 ? themeResult[0] : null;
      console.log("Retrieved theme data:", themeData);
    } catch (error) {
      console.error("Error fetching theme:", error);
    }

    const sessionUserData = {
      id: user.preferred_username || user.name,
      name: userName,
      email: user.preferred_username,
      company: company,
      companyName: ADCompanyName,
      ADCompanyName: ADCompanyName,
      user_id: roleData?.user_id || null,
      role_id: roleData?.role_id || null,
      department_id: roleData?.department_id || null,
      roles: roleData ? [roleData.role_id] : [],
      theme: themeData?.themeColour || '#022D71' // Default theme color
    };

    console.log("Creating session with userData:", sessionUserData);

    const { sessionId } = sessionManager.createSession(
      sessionUserData.id, 
      sessionUserData, 
      SESSION_CONFIG.maxAge
    );

    res.cookie(SESSION_CONFIG.cookieName, sessionId, {
      httpOnly: true,
      secure: SESSION_CONFIG.secure,
      sameSite: SESSION_CONFIG.sameSite,
      maxAge: SESSION_CONFIG.maxAge,
      path: '/'
    });

    req.session = { id: sessionId, user: sessionUserData };
    
    next();
  } catch (error) {
    console.error('Session creation failed:', error);
    res.status(500).json({ error: 'Session creation failed' });
  }
};

const validateSession = (req, res, next) => {
  const sessionId = req.cookies[SESSION_CONFIG.cookieName];
  
  if (!sessionId) {
    return res.status(401).json({ error: 'No session found' });
  }

  const session = sessionManager.getSession(sessionId);
  
  if (!session) {
    res.clearCookie(SESSION_CONFIG.cookieName);
    return res.status(401).json({ error: 'Invalid session' });
  }

  req.session = {
    id: session.id,
    user: session.userData
  };

  next();
};

const destroySession = (req, res) => {
  const sessionId = req.cookies[SESSION_CONFIG.cookieName];
  
  if (sessionId) {
    sessionManager.destroySession(sessionId);
  }
  
  res.clearCookie(SESSION_CONFIG.cookieName);
  res.json({ message: 'Logged out successfully' });
};

module.exports = {
  createUserSession,
  validateSession,
  destroySession,
  SESSION_CONFIG
}; 