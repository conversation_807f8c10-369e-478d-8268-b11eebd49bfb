"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/users",{

/***/ "./public/images/hoverphoneicon.png":
/*!******************************************!*\
  !*** ./public/images/hoverphoneicon.png ***!
  \******************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony default export */ __webpack_exports__[\"default\"] = ({\"src\":\"/_next/static/media/hoverphoneicon.5924667a.png\",\"height\":24,\"width\":24,\"blurDataURL\":\"/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Fhoverphoneicon.5924667a.png&w=8&q=70\",\"blurWidth\":8,\"blurHeight\":8});//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9wdWJsaWMvaW1hZ2VzL2hvdmVycGhvbmVpY29uLnBuZyIsIm1hcHBpbmdzIjoiO0FBQUEsK0RBQWUsQ0FBQyw4TUFBOE0iLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vcHVibGljL2ltYWdlcy9ob3ZlcnBob25laWNvbi5wbmc/MzBiZSJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCB7XCJzcmNcIjpcIi9fbmV4dC9zdGF0aWMvbWVkaWEvaG92ZXJwaG9uZWljb24uNTkyNDY2N2EucG5nXCIsXCJoZWlnaHRcIjoyNCxcIndpZHRoXCI6MjQsXCJibHVyRGF0YVVSTFwiOlwiL19uZXh0L2ltYWdlP3VybD0lMkZfbmV4dCUyRnN0YXRpYyUyRm1lZGlhJTJGaG92ZXJwaG9uZWljb24uNTkyNDY2N2EucG5nJnc9OCZxPTcwXCIsXCJibHVyV2lkdGhcIjo4LFwiYmx1ckhlaWdodFwiOjh9OyJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///./public/images/hoverphoneicon.png\n"));

/***/ }),

/***/ "./public/images/iss_logo.png":
/*!************************************!*\
  !*** ./public/images/iss_logo.png ***!
  \************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony default export */ __webpack_exports__[\"default\"] = ({\"src\":\"/_next/static/media/iss_logo.2c5aed03.png\",\"height\":99,\"width\":200,\"blurDataURL\":\"/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Fiss_logo.2c5aed03.png&w=8&q=70\",\"blurWidth\":8,\"blurHeight\":4});//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9wdWJsaWMvaW1hZ2VzL2lzc19sb2dvLnBuZyIsIm1hcHBpbmdzIjoiO0FBQUEsK0RBQWUsQ0FBQyxtTUFBbU0iLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vcHVibGljL2ltYWdlcy9pc3NfbG9nby5wbmc/ZTMxNCJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCB7XCJzcmNcIjpcIi9fbmV4dC9zdGF0aWMvbWVkaWEvaXNzX2xvZ28uMmM1YWVkMDMucG5nXCIsXCJoZWlnaHRcIjo5OSxcIndpZHRoXCI6MjAwLFwiYmx1ckRhdGFVUkxcIjpcIi9fbmV4dC9pbWFnZT91cmw9JTJGX25leHQlMkZzdGF0aWMlMkZtZWRpYSUyRmlzc19sb2dvLjJjNWFlZDAzLnBuZyZ3PTgmcT03MFwiLFwiYmx1cldpZHRoXCI6OCxcImJsdXJIZWlnaHRcIjo0fTsiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///./public/images/iss_logo.png\n"));

/***/ }),

/***/ "./public/images/loading_img.png":
/*!***************************************!*\
  !*** ./public/images/loading_img.png ***!
  \***************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony default export */ __webpack_exports__[\"default\"] = ({\"src\":\"/_next/static/media/loading_img.a396d230.png\",\"height\":859,\"width\":840,\"blurDataURL\":\"/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Floading_img.a396d230.png&w=8&q=70\",\"blurWidth\":8,\"blurHeight\":8});//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9wdWJsaWMvaW1hZ2VzL2xvYWRpbmdfaW1nLnBuZyIsIm1hcHBpbmdzIjoiO0FBQUEsK0RBQWUsQ0FBQywwTUFBME0iLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vcHVibGljL2ltYWdlcy9sb2FkaW5nX2ltZy5wbmc/YzYyMCJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCB7XCJzcmNcIjpcIi9fbmV4dC9zdGF0aWMvbWVkaWEvbG9hZGluZ19pbWcuYTM5NmQyMzAucG5nXCIsXCJoZWlnaHRcIjo4NTksXCJ3aWR0aFwiOjg0MCxcImJsdXJEYXRhVVJMXCI6XCIvX25leHQvaW1hZ2U/dXJsPSUyRl9uZXh0JTJGc3RhdGljJTJGbWVkaWElMkZsb2FkaW5nX2ltZy5hMzk2ZDIzMC5wbmcmdz04JnE9NzBcIixcImJsdXJXaWR0aFwiOjgsXCJibHVySGVpZ2h0XCI6OH07Il0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///./public/images/loading_img.png\n"));

/***/ }),

/***/ "./public/images/nav-icon.png":
/*!************************************!*\
  !*** ./public/images/nav-icon.png ***!
  \************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony default export */ __webpack_exports__[\"default\"] = ({\"src\":\"/_next/static/media/nav-icon.ac2d8883.png\",\"height\":22,\"width\":25,\"blurDataURL\":\"/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Fnav-icon.ac2d8883.png&w=8&q=70\",\"blurWidth\":8,\"blurHeight\":7});//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9wdWJsaWMvaW1hZ2VzL25hdi1pY29uLnBuZyIsIm1hcHBpbmdzIjoiO0FBQUEsK0RBQWUsQ0FBQyxrTUFBa00iLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vcHVibGljL2ltYWdlcy9uYXYtaWNvbi5wbmc/M2U5MyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCB7XCJzcmNcIjpcIi9fbmV4dC9zdGF0aWMvbWVkaWEvbmF2LWljb24uYWMyZDg4ODMucG5nXCIsXCJoZWlnaHRcIjoyMixcIndpZHRoXCI6MjUsXCJibHVyRGF0YVVSTFwiOlwiL19uZXh0L2ltYWdlP3VybD0lMkZfbmV4dCUyRnN0YXRpYyUyRm1lZGlhJTJGbmF2LWljb24uYWMyZDg4ODMucG5nJnc9OCZxPTcwXCIsXCJibHVyV2lkdGhcIjo4LFwiYmx1ckhlaWdodFwiOjd9OyJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///./public/images/nav-icon.png\n"));

/***/ }),

/***/ "./public/images/navbarPhone.png":
/*!***************************************!*\
  !*** ./public/images/navbarPhone.png ***!
  \***************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony default export */ __webpack_exports__[\"default\"] = ({\"src\":\"/_next/static/media/navbarPhone.5adfc417.png\",\"height\":24,\"width\":24,\"blurDataURL\":\"/_next/image?url=%2F_next%2Fstatic%2Fmedia%2FnavbarPhone.5adfc417.png&w=8&q=70\",\"blurWidth\":8,\"blurHeight\":8});//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9wdWJsaWMvaW1hZ2VzL25hdmJhclBob25lLnBuZyIsIm1hcHBpbmdzIjoiO0FBQUEsK0RBQWUsQ0FBQyx3TUFBd00iLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vcHVibGljL2ltYWdlcy9uYXZiYXJQaG9uZS5wbmc/ZDMxMCJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCB7XCJzcmNcIjpcIi9fbmV4dC9zdGF0aWMvbWVkaWEvbmF2YmFyUGhvbmUuNWFkZmM0MTcucG5nXCIsXCJoZWlnaHRcIjoyNCxcIndpZHRoXCI6MjQsXCJibHVyRGF0YVVSTFwiOlwiL19uZXh0L2ltYWdlP3VybD0lMkZfbmV4dCUyRnN0YXRpYyUyRm1lZGlhJTJGbmF2YmFyUGhvbmUuNWFkZmM0MTcucG5nJnc9OCZxPTcwXCIsXCJibHVyV2lkdGhcIjo4LFwiYmx1ckhlaWdodFwiOjh9OyJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///./public/images/navbarPhone.png\n"));

/***/ }),

/***/ "./public/images/user-account.png":
/*!****************************************!*\
  !*** ./public/images/user-account.png ***!
  \****************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony default export */ __webpack_exports__[\"default\"] = ({\"src\":\"/_next/static/media/user-account.e319c333.png\",\"height\":61,\"width\":61,\"blurDataURL\":\"/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Fuser-account.e319c333.png&w=8&q=70\",\"blurWidth\":8,\"blurHeight\":8});//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9wdWJsaWMvaW1hZ2VzL3VzZXItYWNjb3VudC5wbmciLCJtYXBwaW5ncyI6IjtBQUFBLCtEQUFlLENBQUMsME1BQTBNIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL3B1YmxpYy9pbWFnZXMvdXNlci1hY2NvdW50LnBuZz9mM2FiIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IHtcInNyY1wiOlwiL19uZXh0L3N0YXRpYy9tZWRpYS91c2VyLWFjY291bnQuZTMxOWMzMzMucG5nXCIsXCJoZWlnaHRcIjo2MSxcIndpZHRoXCI6NjEsXCJibHVyRGF0YVVSTFwiOlwiL19uZXh0L2ltYWdlP3VybD0lMkZfbmV4dCUyRnN0YXRpYyUyRm1lZGlhJTJGdXNlci1hY2NvdW50LmUzMTljMzMzLnBuZyZ3PTgmcT03MFwiLFwiYmx1cldpZHRoXCI6OCxcImJsdXJIZWlnaHRcIjo4fTsiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///./public/images/user-account.png\n"));

/***/ }),

/***/ "./public/images/user.png":
/*!********************************!*\
  !*** ./public/images/user.png ***!
  \********************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony default export */ __webpack_exports__[\"default\"] = ({\"src\":\"/_next/static/media/user.c45dfbc8.png\",\"height\":28,\"width\":28,\"blurDataURL\":\"/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Fuser.c45dfbc8.png&w=8&q=70\",\"blurWidth\":8,\"blurHeight\":8});//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9wdWJsaWMvaW1hZ2VzL3VzZXIucG5nIiwibWFwcGluZ3MiOiI7QUFBQSwrREFBZSxDQUFDLDBMQUEwTCIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9wdWJsaWMvaW1hZ2VzL3VzZXIucG5nPzg0ZDgiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQge1wic3JjXCI6XCIvX25leHQvc3RhdGljL21lZGlhL3VzZXIuYzQ1ZGZiYzgucG5nXCIsXCJoZWlnaHRcIjoyOCxcIndpZHRoXCI6MjgsXCJibHVyRGF0YVVSTFwiOlwiL19uZXh0L2ltYWdlP3VybD0lMkZfbmV4dCUyRnN0YXRpYyUyRm1lZGlhJTJGdXNlci5jNDVkZmJjOC5wbmcmdz04JnE9NzBcIixcImJsdXJXaWR0aFwiOjgsXCJibHVySGVpZ2h0XCI6OH07Il0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///./public/images/user.png\n"));

/***/ }),

/***/ "./components/Layout.js":
/*!******************************!*\
  !*** ./components/Layout.js ***!
  \******************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getServerSideProps: function() { return /* binding */ getServerSideProps; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _Navbar__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./Navbar */ \"./components/Navbar.js\");\n/* harmony import */ var _Sidebar__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./Sidebar */ \"./components/Sidebar.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/navigation */ \"./node_modules/next/navigation.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(next_navigation__WEBPACK_IMPORTED_MODULE_5__);\n\nvar _s = $RefreshSig$();\n\n\n\n\n\nconst Layout = (param)=>{\n    let { children, userData, company, blockScreen } = param;\n    _s();\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_4__.useRouter)();\n    const currentPath = router.pathname;\n    const [showDesktopViewMessage, setShowDesktopViewMessage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_5__.usePathname)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const handleResize = ()=>{\n            if (window.innerWidth <= 767) {\n                setShowDesktopViewMessage(true);\n            } else {\n                setShowDesktopViewMessage(false);\n            }\n        };\n        window.addEventListener(\"resize\", handleResize);\n        handleResize();\n        return ()=>window.removeEventListener(\"resize\", handleResize);\n    }, []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        id: \"wrapper\",\n        children: showDesktopViewMessage ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"desktop-view-message\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"message-content\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        children: \"Please Open in Desktop View\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\Layout.js\",\n                        lineNumber: 32,\n                        columnNumber: 13\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        children: \"This website is best viewed on a desktop or laptop.\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\Layout.js\",\n                        lineNumber: 33,\n                        columnNumber: 13\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\Layout.js\",\n                lineNumber: 31,\n                columnNumber: 11\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\Layout.js\",\n            lineNumber: 30,\n            columnNumber: 9\n        }, undefined) : blockScreen ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"block-view-message\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"message-content\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            children: \"Service Unavailable\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\Layout.js\",\n                            lineNumber: 40,\n                            columnNumber: 15\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            children: \"We are currently experiencing issues and are working to resolve them as quickly as possible. Please check back later.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\Layout.js\",\n                            lineNumber: 41,\n                            columnNumber: 15\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\Layout.js\",\n                    lineNumber: 39,\n                    columnNumber: 13\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\Layout.js\",\n                lineNumber: 38,\n                columnNumber: 11\n            }, undefined)\n        }, void 0, false) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Sidebar__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                    userData: userData,\n                    companyName: company\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\Layout.js\",\n                    lineNumber: 50,\n                    columnNumber: 11\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"page-container\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Navbar__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                            userData: userData\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\Layout.js\",\n                            lineNumber: 52,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"main-root\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative md:ml-[55px] lg:ml-[60px] py-2 2xl:h-[calc(100%-60px)] \".concat(pathname == \"/whatif\" || pathname == \"/service_level\" ? \"w-[100%-70px] px-0 pl-3 mt-[45px]\" : \"w-full px-8 mt-[60px]\"),\n                                children: children\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\Layout.js\",\n                                lineNumber: 54,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\Layout.js\",\n                            lineNumber: 53,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\Layout.js\",\n                    lineNumber: 51,\n                    columnNumber: 11\n                }, undefined)\n            ]\n        }, void 0, true)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\Layout.js\",\n        lineNumber: 28,\n        columnNumber: 5\n    }, undefined);\n};\n_s(Layout, \"E7wJCME/M0kEq0xEwa8ayz4R7Ag=\", false, function() {\n    return [\n        next_router__WEBPACK_IMPORTED_MODULE_4__.useRouter,\n        next_navigation__WEBPACK_IMPORTED_MODULE_5__.usePathname\n    ];\n});\n_c = Layout;\n/* harmony default export */ __webpack_exports__[\"default\"] = (Layout);\nconst getServerSideProps = async (context)=>{\n    let userData = {};\n    let company = \"\";\n    try {\n        userData = JSON.parse(context.req.cookies.user || \"{}\");\n        company = JSON.parse(context.req.localStorage.company || \"\");\n        console.log(\"company\", company);\n    } catch (error) {\n        console.error(\"Error parsing userData:\", error);\n    }\n    return {\n        props: {\n            userData,\n            company\n        }\n    };\n};\nvar _c;\n$RefreshReg$(_c, \"Layout\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevExports = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevExports) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports on update so we can compare the boundary\n                // signatures.\n                module.hot.dispose(function (data) {\n                    data.prevExports = currentExports;\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevExports !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevExports, currentExports)) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevExports !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/Layout.js\n"));

/***/ }),

/***/ "./components/Navbar.js":
/*!******************************!*\
  !*** ./components/Navbar.js ***!
  \******************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   themes: function() { return /* binding */ themes; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _fortawesome_react_fontawesome__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @fortawesome/react-fontawesome */ \"./node_modules/@fortawesome/react-fontawesome/index.es.js\");\n/* harmony import */ var _fortawesome_free_solid_svg_icons__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @fortawesome/free-solid-svg-icons */ \"./node_modules/@fortawesome/free-solid-svg-icons/index.mjs\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/link */ \"./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/image */ \"./node_modules/next/image.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_image__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _public_images_user_account_png__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../public/images/user-account.png */ \"./public/images/user-account.png\");\n/* harmony import */ var _public_images_navbarPhone_png__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../public/images/navbarPhone.png */ \"./public/images/navbarPhone.png\");\n/* harmony import */ var _public_images_hoverphoneicon_png__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../public/images/hoverphoneicon.png */ \"./public/images/hoverphoneicon.png\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_8___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_8__);\n/* harmony import */ var _utils_auth_auth__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/utils/auth/auth */ \"./utils/auth/auth.js\");\n/* harmony import */ var _azure_msal_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @azure/msal-react */ \"./node_modules/@azure/msal-react/dist/index.js\");\n/* harmony import */ var _utils_theme_theme_switcher__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/utils/theme/theme-switcher */ \"./utils/theme/theme-switcher.js\");\n/* harmony import */ var _utils_themeContext__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/utils/themeContext */ \"./utils/themeContext.js\");\n/* harmony import */ var _utils_theme_theme__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/utils/theme/theme */ \"./utils/theme/theme.js\");\n/* harmony import */ var js_cookie__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! js-cookie */ \"./node_modules/js-cookie/dist/js.cookie.mjs\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst themes = [\n    {\n        bg: \"#022d71\",\n        text: \"#022d71\",\n        name: \"DPS\"\n    },\n    {\n        bg: \"#2e9b28\",\n        text: \"#2e9b28\",\n        name: \"EFC\"\n    },\n    {\n        bg: \"#a91e23\",\n        text: \"#a91e23\",\n        name: \"M&S\"\n    },\n    {\n        bg: \"#3d6546\",\n        text: \"#3d6546\",\n        name: \"FPP\"\n    }\n];\nconst Navbar = (param)=>{\n    let { userData } = param;\n    _s();\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_8__.useRouter)();\n    const [isHovered, setIsHovered] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [name, setName] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [role, setRole] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [pageName, setPageName] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const { instance } = (0,_azure_msal_react__WEBPACK_IMPORTED_MODULE_10__.useMsal)();\n    const [currentRoute, setCurrentRoute] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [isUserMainTester, setIsUserMainTester] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const currentPath = router.pathname;\n        setCurrentRoute(currentPath);\n        if (currentPath === \"/finished-product-request/add\") {\n            js_cookie__WEBPACK_IMPORTED_MODULE_14__[\"default\"].set(\"PreviousPage\", \"FG\");\n            setPageName(\"Finished Goods Request\");\n        } else if (currentPath === \"/raw-material-request/add\") {\n            setPageName(\"Raw Material Request\");\n            js_cookie__WEBPACK_IMPORTED_MODULE_14__[\"default\"].set(\"PreviousPage\", \"RM\");\n        } else if (currentPath === \"/packaging-form/add\") {\n            setPageName(\"Packaging Request\");\n        } else if (currentPath === \"/variety/add\") {\n            js_cookie__WEBPACK_IMPORTED_MODULE_14__[\"default\"].set(\"PreviousPage\", \"NV\");\n            setPageName(\"New Variety Request\");\n        } else if (currentPath === \"/variety/[productId]/edit\") {\n            js_cookie__WEBPACK_IMPORTED_MODULE_14__[\"default\"].set(\"PreviousPage\", \"NV\");\n            setPageName(\"Edit New Variety Request\");\n        } else if (currentPath === \"/packaging-form/[productId]/edit\") {\n            js_cookie__WEBPACK_IMPORTED_MODULE_14__[\"default\"].set(\"PreviousPage\", \"PK\");\n            setPageName(\"Edit Packaging Request\");\n        } else if (currentPath === \"/raw-material-request/[productId]/edit\") {\n            js_cookie__WEBPACK_IMPORTED_MODULE_14__[\"default\"].set(\"PreviousPage\", \"RM\");\n            setPageName(\"Edit Raw Material Request\");\n        } else if (currentPath === \"/finished-product-request/[productId]/edit\") {\n            js_cookie__WEBPACK_IMPORTED_MODULE_14__[\"default\"].set(\"PreviousPage\", \"FG\");\n            setPageName(\"Edit Finished Goods Request\");\n        } else if (currentPath === null || currentPath === void 0 ? void 0 : currentPath.endsWith(\"/edit\")) {\n            setPageName(\"Edit Supplier\");\n        } else if (currentPath === null || currentPath === void 0 ? void 0 : currentPath.endsWith(\"/add\")) {\n            setPageName(\"Add Supplier\");\n        } else if (currentPath === null || currentPath === void 0 ? void 0 : currentPath.endsWith(\"/edit/forms\")) {\n            setPageName(\"Supplier Form\");\n        } else if (currentPath === null || currentPath === void 0 ? void 0 : currentPath.endsWith(\"/confirm\")) {\n            setPageName(\"Confirm Details for Supplier\");\n        } else if (currentPath === \"/suppliers\") {\n            setPageName(\"Suppliers\");\n        } else if (currentPath === \"/users\") {\n            setPageName(\"User Management\");\n        } else if (currentPath === \"/viewlogs\") {\n            setPageName(\"View Logs\");\n        } else if (currentPath === \"/products\") {\n            js_cookie__WEBPACK_IMPORTED_MODULE_14__[\"default\"].set(\"PreviousPage\", \"PK\");\n            setPageName(\"Products\");\n        } else if (currentPath === \"/variety\") {\n            setPageName(\"Variety\");\n            js_cookie__WEBPACK_IMPORTED_MODULE_14__[\"default\"].set(\"PreviousPage\", \"NV\");\n        } else if (currentPath === \"/finishedProductRequest\") {\n            js_cookie__WEBPACK_IMPORTED_MODULE_14__[\"default\"].set(\"PreviousPage\", \"FG\");\n            setPageName(\"Finished Product Request\");\n        } else if (currentPath === \"/rawMaterialRequest\") {\n            js_cookie__WEBPACK_IMPORTED_MODULE_14__[\"default\"].set(\"PreviousPage\", \"RM\");\n            setPageName(\"Raw Material Request\");\n        } else if (currentPath === \"/whatif\") {\n            setPageName(\"Whatif\");\n        } else if (currentPath === \"/service_level\" || currentPath === \"/service_level/reports/masterForcast\") {\n            setPageName(\"Service Level\");\n        }\n    }, [\n        router.pathname\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        var _process_env_NEXT_PUBLIC_TESTER_EMAIL;\n        const testEmails = ((_process_env_NEXT_PUBLIC_TESTER_EMAIL = \"<EMAIL>;<EMAIL>\") === null || _process_env_NEXT_PUBLIC_TESTER_EMAIL === void 0 ? void 0 : _process_env_NEXT_PUBLIC_TESTER_EMAIL.split(\";\")) || [];\n        if (testEmails.includes(userData === null || userData === void 0 ? void 0 : userData.email)) {\n            setIsUserMainTester(true);\n        }\n    }, [\n        userData === null || userData === void 0 ? void 0 : userData.email\n    ]);\n    const handleMouseEnter = ()=>{\n        setIsHovered(true);\n    };\n    const handleMouseLeave = ()=>{\n        setIsHovered(false);\n    };\n    const [selectedCompany, setSelectedCompany] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(js_cookie__WEBPACK_IMPORTED_MODULE_14__[\"default\"].get(\"company\") || \"\");\n    const [selectedADCompanyName, setSelectedADCompanyName] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(js_cookie__WEBPACK_IMPORTED_MODULE_14__[\"default\"].get(\"ADCompanyName\") || \"\");\n    const handleCompanyChange = (event)=>{\n        const company = event.target.value;\n        if (company == \"dpsltd\") {\n            js_cookie__WEBPACK_IMPORTED_MODULE_14__[\"default\"].set(\"theme\", \"#022D71\"); //!this will change\n            js_cookie__WEBPACK_IMPORTED_MODULE_14__[\"default\"].set(\"ADCompanyName\", \"DPS\");\n        } else if (company == \"efcltd\") {\n            js_cookie__WEBPACK_IMPORTED_MODULE_14__[\"default\"].set(\"theme\", \"#3eab58\");\n            js_cookie__WEBPACK_IMPORTED_MODULE_14__[\"default\"].set(\"ADCompanyName\", \"EFC\");\n        } else if (company == \"fpp-ltd\") {\n            js_cookie__WEBPACK_IMPORTED_MODULE_14__[\"default\"].set(\"theme\", \"#3d6546\");\n            js_cookie__WEBPACK_IMPORTED_MODULE_14__[\"default\"].set(\"ADCompanyName\", \"Fresh Produce Partners\");\n        } else if (company == \"thl\") {\n            js_cookie__WEBPACK_IMPORTED_MODULE_14__[\"default\"].set(\"theme\", \"#61CBD0\");\n            js_cookie__WEBPACK_IMPORTED_MODULE_14__[\"default\"].set(\"ADCompanyName\", \"THL\");\n        } else if (company == \"flrs\") {\n            js_cookie__WEBPACK_IMPORTED_MODULE_14__[\"default\"].set(\"theme\", \"#270634\");\n            js_cookie__WEBPACK_IMPORTED_MODULE_14__[\"default\"].set(\"ADCompanyName\", \"FLR Spectron Ltd\");\n        } else if (company == \"issproduce\") {\n            js_cookie__WEBPACK_IMPORTED_MODULE_14__[\"default\"].set(\"theme\", \"#ABC400\");\n            js_cookie__WEBPACK_IMPORTED_MODULE_14__[\"default\"].set(\"ADCompanyName\", \"ISS\");\n        }\n        setSelectedCompany(company);\n        js_cookie__WEBPACK_IMPORTED_MODULE_14__[\"default\"].set(\"company\", company);\n        router.reload();\n    };\n    const handleADCompanyNameChange = (event)=>{\n        const company = event.target.value;\n        if (company == \"DPS MS\") {\n            js_cookie__WEBPACK_IMPORTED_MODULE_14__[\"default\"].set(\"theme\", \"#0d6bfc\"); //!this will change\n        } else if (company == \"DPS\") {\n            js_cookie__WEBPACK_IMPORTED_MODULE_14__[\"default\"].set(\"theme\", \"#022D71\");\n        }\n        setSelectedADCompanyName(company);\n        js_cookie__WEBPACK_IMPORTED_MODULE_14__[\"default\"].set(\"ADCompanyName\", company);\n        router.reload();\n    };\n    const imageSrc = isHovered ? _public_images_hoverphoneicon_png__WEBPACK_IMPORTED_MODULE_7__[\"default\"] : _public_images_navbarPhone_png__WEBPACK_IMPORTED_MODULE_6__[\"default\"];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"titlebar\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex flex-row justify-between w-full bg-skin-primary h-14\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-row w-full items-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"page-heading cursor-pointer\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fortawesome_react_fontawesome__WEBPACK_IMPORTED_MODULE_2__.FontAwesomeIcon, {\n                                        icon: _fortawesome_free_solid_svg_icons__WEBPACK_IMPORTED_MODULE_15__.faChevronLeft,\n                                        className: \"pageName text-white\",\n                                        onClick: ()=>router.back()\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\Navbar.js\",\n                                        lineNumber: 171,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                        href: currentRoute,\n                                        className: \"ml-4 2xl:text-lg font-poppinssemibold pageName text-white tracking-wide\",\n                                        children: pageName\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\Navbar.js\",\n                                        lineNumber: 176,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\Navbar.js\",\n                                lineNumber: 170,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_utils_theme_theme_switcher__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                className: \"ml-5\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\Navbar.js\",\n                                lineNumber: 183,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\Navbar.js\",\n                        lineNumber: 169,\n                        columnNumber: 11\n                    }, undefined),\n                    isUserMainTester && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                value: selectedCompany,\n                                onChange: handleCompanyChange,\n                                className: \"ml-5 bg-white text-black rounded p-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: \"\",\n                                        disabled: true,\n                                        children: \"Select Company\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\Navbar.js\",\n                                        lineNumber: 192,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: \"dpsltd\",\n                                        children: \"DPS Ltd\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\Navbar.js\",\n                                        lineNumber: 195,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: \"efcltd\",\n                                        children: \"EFC Ltd\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\Navbar.js\",\n                                        lineNumber: 196,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: \"fpp-ltd\",\n                                        children: \"FPP Ltd\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\Navbar.js\",\n                                        lineNumber: 197,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: \"flrs\",\n                                        children: \"FLRS\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\Navbar.js\",\n                                        lineNumber: 198,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: \"thl\",\n                                        children: \"THL\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\Navbar.js\",\n                                        lineNumber: 199,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: \"issproduce\",\n                                        children: \"ISS\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\Navbar.js\",\n                                        lineNumber: 200,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\Navbar.js\",\n                                lineNumber: 187,\n                                columnNumber: 15\n                            }, undefined),\n                            js_cookie__WEBPACK_IMPORTED_MODULE_14__[\"default\"].get(\"company\") == \"dpsltd\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                value: selectedADCompanyName,\n                                onChange: handleADCompanyNameChange,\n                                className: \"ml-5 bg-white text-black rounded p-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: \"\",\n                                        disabled: true,\n                                        children: \"Select Company\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\Navbar.js\",\n                                        lineNumber: 208,\n                                        columnNumber: 19\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: \"DPS\",\n                                        children: \"DPS Ltd\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\Navbar.js\",\n                                        lineNumber: 211,\n                                        columnNumber: 19\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: \"DPS MS\",\n                                        children: \"DPS MS\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\Navbar.js\",\n                                        lineNumber: 212,\n                                        columnNumber: 19\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\Navbar.js\",\n                                lineNumber: 203,\n                                columnNumber: 17\n                            }, undefined)\n                        ]\n                    }, void 0, true),\n                    \" \",\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-row justify-end w-full items-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center h-full g1ap-5 pr-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg  text-white tracking-wider\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\Navbar.js\",\n                                    lineNumber: 219,\n                                    columnNumber: 15\n                                }, undefined),\n                                \" \"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\Navbar.js\",\n                            lineNumber: 218,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\Navbar.js\",\n                        lineNumber: 217,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\Navbar.js\",\n                lineNumber: 168,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\Navbar.js\",\n            lineNumber: 167,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\Navbar.js\",\n        lineNumber: 166,\n        columnNumber: 5\n    }, undefined);\n};\n_s(Navbar, \"su2DqULmXxyw+SX0CuTpkOzKprM=\", false, function() {\n    return [\n        next_router__WEBPACK_IMPORTED_MODULE_8__.useRouter,\n        _azure_msal_react__WEBPACK_IMPORTED_MODULE_10__.useMsal\n    ];\n});\n_c = Navbar;\n/* harmony default export */ __webpack_exports__[\"default\"] = (Navbar);\nvar _c;\n$RefreshReg$(_c, \"Navbar\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevExports = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevExports) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports on update so we can compare the boundary\n                // signatures.\n                module.hot.dispose(function (data) {\n                    data.prevExports = currentExports;\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevExports !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevExports, currentExports)) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevExports !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/Navbar.js\n"));

/***/ }),

/***/ "./components/SideBarLinks.js":
/*!************************************!*\
  !*** ./components/SideBarLinks.js ***!
  \************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ SideBarLinks; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _fortawesome_free_solid_svg_icons__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @fortawesome/free-solid-svg-icons */ \"./node_modules/@fortawesome/free-solid-svg-icons/index.mjs\");\n/* harmony import */ var _fortawesome_react_fontawesome__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @fortawesome/react-fontawesome */ \"./node_modules/@fortawesome/react-fontawesome/index.es.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _utils_loaders_loadingContext__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/utils/loaders/loadingContext */ \"./utils/loaders/loadingContext.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\nfunction SideBarLinks(param) {\n    let { isSuppliersActive, userData, company, isUsersActive, isLogsActive, isWhatifActive, isServiceLevelActive, isProductsActive } = param;\n    var _process_env_NEXT_PUBLIC_AVAILABLE_MODULES;\n    _s();\n    const availableModules = (_process_env_NEXT_PUBLIC_AVAILABLE_MODULES = \"supplier,products,whatif,serviceLevel,users,logs\") === null || _process_env_NEXT_PUBLIC_AVAILABLE_MODULES === void 0 ? void 0 : _process_env_NEXT_PUBLIC_AVAILABLE_MODULES.split(\",\");\n    const { setIsLoading } = (0,_utils_loaders_loadingContext__WEBPACK_IMPORTED_MODULE_4__.useLoading)();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n        className: \"nav navbar-nav\",\n        children: availableModules && availableModules.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n            children: [\n                availableModules.includes(\"supplier\") && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                    className: \"flex justify-center mb-3\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                        href: \"/suppliers\",\n                        title: \"Supplier\",\n                        className: \"!px-4 !py-3 bg-white rounded-md text-center \".concat(isSuppliersActive ? \"opacity-100\" : \"opacity-70\"),\n                        onClick: ()=>{\n                            setIsLoading(true);\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fortawesome_react_fontawesome__WEBPACK_IMPORTED_MODULE_1__.FontAwesomeIcon, {\n                            icon: _fortawesome_free_solid_svg_icons__WEBPACK_IMPORTED_MODULE_5__.faTruck,\n                            size: \"lg\",\n                            className: \"text-skin-primary\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\SideBarLinks.js\",\n                            lineNumber: 51,\n                            columnNumber: 17\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\SideBarLinks.js\",\n                        lineNumber: 43,\n                        columnNumber: 15\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\SideBarLinks.js\",\n                    lineNumber: 42,\n                    columnNumber: 13\n                }, this),\n                availableModules.includes(\"products\") && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                    className: \"flex justify-center mb-3\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                        href: \"/products\",\n                        title: \"Products\",\n                        className: \"!px-4 !py-3 bg-white rounded-md text-center \".concat(isProductsActive ? \"opacity-100\" : \"opacity-70\"),\n                        onClick: ()=>{\n                            setIsLoading(true);\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fortawesome_react_fontawesome__WEBPACK_IMPORTED_MODULE_1__.FontAwesomeIcon, {\n                            icon: _fortawesome_free_solid_svg_icons__WEBPACK_IMPORTED_MODULE_5__.faBoxArchive,\n                            size: \"lg\",\n                            className: \"text-skin-primary\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\SideBarLinks.js\",\n                            lineNumber: 69,\n                            columnNumber: 17\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\SideBarLinks.js\",\n                        lineNumber: 61,\n                        columnNumber: 15\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\SideBarLinks.js\",\n                    lineNumber: 60,\n                    columnNumber: 13\n                }, this),\n                (company == \"efcltd\" || company == \"flrs\" || company == \"thl\") && ((userData === null || userData === void 0 ? void 0 : userData.department_id) == 1 || (userData === null || userData === void 0 ? void 0 : userData.department_id) == 2) && availableModules.includes(\"whatif\") && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                    className: \"flex justify-center mb-3\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                        href: \"/whatif\",\n                        title: \"What if\",\n                        className: \"!px-4 !py-3 bg-white rounded-md text-center \".concat(isWhatifActive ? \"opacity-100\" : \"opacity-70\"),\n                        onClick: ()=>{\n                            setIsLoading(true);\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fortawesome_react_fontawesome__WEBPACK_IMPORTED_MODULE_1__.FontAwesomeIcon, {\n                            icon: _fortawesome_free_solid_svg_icons__WEBPACK_IMPORTED_MODULE_5__.faChartLine,\n                            size: \"lg\",\n                            className: \"text-skin-primary\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\SideBarLinks.js\",\n                            lineNumber: 89,\n                            columnNumber: 19\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\SideBarLinks.js\",\n                        lineNumber: 81,\n                        columnNumber: 17\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\SideBarLinks.js\",\n                    lineNumber: 80,\n                    columnNumber: 15\n                }, this),\n                ((userData === null || userData === void 0 ? void 0 : userData.department_id) == 1 || (userData === null || userData === void 0 ? void 0 : userData.department_id) == 2) && availableModules.includes(\"serviceLevel\") && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                    className: \"flex justify-center mb-3\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                        href: \"/service_level\",\n                        title: \"Service Level\",\n                        className: \"!px-4 !py-3 bg-white rounded-md text-center \".concat(isServiceLevelActive ? \"opacity-100\" : \"opacity-70\"),\n                        onClick: ()=>{\n                            setIsLoading(true);\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fortawesome_react_fontawesome__WEBPACK_IMPORTED_MODULE_1__.FontAwesomeIcon, {\n                            icon: _fortawesome_free_solid_svg_icons__WEBPACK_IMPORTED_MODULE_5__.faFileCircleQuestion,\n                            size: \"lg\",\n                            className: \"text-skin-primary\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\SideBarLinks.js\",\n                            lineNumber: 109,\n                            columnNumber: 19\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\SideBarLinks.js\",\n                        lineNumber: 101,\n                        columnNumber: 17\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\SideBarLinks.js\",\n                    lineNumber: 100,\n                    columnNumber: 15\n                }, this),\n                (userData === null || userData === void 0 ? void 0 : userData.role_id) === 1 && availableModules.includes(\"users\") && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                    className: \"flex justify-center mb-3\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                        href: \"/users\",\n                        title: \"Users\",\n                        className: \"!px-4 !py-3 bg-white rounded-md text-center \".concat(isUsersActive ? \"opacity-100\" : \"opacity-70\"),\n                        onClick: ()=>{\n                            setIsLoading(true);\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fortawesome_react_fontawesome__WEBPACK_IMPORTED_MODULE_1__.FontAwesomeIcon, {\n                            icon: _fortawesome_free_solid_svg_icons__WEBPACK_IMPORTED_MODULE_5__.faUsersLine,\n                            size: \"lg\",\n                            className: \"text-skin-primary\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\SideBarLinks.js\",\n                            lineNumber: 129,\n                            columnNumber: 17\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\SideBarLinks.js\",\n                        lineNumber: 120,\n                        columnNumber: 15\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\SideBarLinks.js\",\n                    lineNumber: 119,\n                    columnNumber: 13\n                }, this),\n                (userData === null || userData === void 0 ? void 0 : userData.role_id) === 1 && availableModules.includes(\"logs\") && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                    className: \"flex justify-center mb-3\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                        href: \"/viewlogs\",\n                        title: \"Logs\",\n                        className: \"!px-4 !py-3 bg-white rounded-md text-center \".concat(isLogsActive ? \"opacity-100\" : \"opacity-70\"),\n                        onClick: ()=>{\n                            setIsLoading(true);\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fortawesome_react_fontawesome__WEBPACK_IMPORTED_MODULE_1__.FontAwesomeIcon, {\n                            icon: _fortawesome_free_solid_svg_icons__WEBPACK_IMPORTED_MODULE_5__.faList,\n                            size: \"lg\",\n                            className: \"text-skin-primary\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\SideBarLinks.js\",\n                            lineNumber: 148,\n                            columnNumber: 17\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\SideBarLinks.js\",\n                        lineNumber: 139,\n                        columnNumber: 15\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\SideBarLinks.js\",\n                    lineNumber: 138,\n                    columnNumber: 13\n                }, this)\n            ]\n        }, void 0, true)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\SideBarLinks.js\",\n        lineNumber: 38,\n        columnNumber: 5\n    }, this);\n}\n_s(SideBarLinks, \"HE5iXj3OUhzAOBpDJUVF6nKZb8k=\", false, function() {\n    return [\n        _utils_loaders_loadingContext__WEBPACK_IMPORTED_MODULE_4__.useLoading\n    ];\n});\n_c = SideBarLinks;\nvar _c;\n$RefreshReg$(_c, \"SideBarLinks\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevExports = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevExports) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports on update so we can compare the boundary\n                // signatures.\n                module.hot.dispose(function (data) {\n                    data.prevExports = currentExports;\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevExports !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevExports, currentExports)) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevExports !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/SideBarLinks.js\n"));

/***/ }),

/***/ "./components/Sidebar.js":
/*!*******************************!*\
  !*** ./components/Sidebar.js ***!
  \*******************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ Sidebar; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _public_images_dps_logo_png__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../public/images/dps-logo.png */ \"./public/images/dps-logo.png\");\n/* harmony import */ var _public_images_efc_logo_jpg__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../public/images/efc_logo.jpg */ \"./public/images/efc_logo.jpg\");\n/* harmony import */ var _public_images_dps_ms_logo_png__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../public/images/dps_ms_logo.png */ \"./public/images/dps_ms_logo.png\");\n/* harmony import */ var _public_images_fpp_logo_png__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../public/images/fpp_logo.png */ \"./public/images/fpp_logo.png\");\n/* harmony import */ var _public_images_iss_logo_png__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../public/images/iss_logo.png */ \"./public/images/iss_logo.png\");\n/* harmony import */ var _public_images_nav_icon_png__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ../public/images/nav-icon.png */ \"./public/images/nav-icon.png\");\n/* harmony import */ var _public_images_user_png__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ../public/images/user.png */ \"./public/images/user.png\");\n/* harmony import */ var _public_images_loading_img_png__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ../public/images/loading_img.png */ \"./public/images/loading_img.png\");\n/* harmony import */ var _public_images_logout_icon_png__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ../public/images/logout-icon.png */ \"./public/images/logout-icon.png\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! next/link */ \"./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_12___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_12__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! next/image */ \"./node_modules/next/image.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_13___default = /*#__PURE__*/__webpack_require__.n(next_image__WEBPACK_IMPORTED_MODULE_13__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! next/navigation */ \"./node_modules/next/navigation.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_14___default = /*#__PURE__*/__webpack_require__.n(next_navigation__WEBPACK_IMPORTED_MODULE_14__);\n/* harmony import */ var _utils_auth_auth__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! ../utils/auth/auth */ \"./utils/auth/auth.js\");\n/* harmony import */ var _azure_msal_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @azure/msal-react */ \"./node_modules/@azure/msal-react/dist/index.js\");\n/* harmony import */ var js_cookie__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! js-cookie */ \"./node_modules/js-cookie/dist/js.cookie.mjs\");\n/* harmony import */ var react_loader_spinner__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! react-loader-spinner */ \"./node_modules/react-loader-spinner/dist/module.js\");\n/* harmony import */ var _SideBarLinks__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! ./SideBarLinks */ \"./components/SideBarLinks.js\");\n/* provided dependency */ var process = __webpack_require__(/*! process */ \"./node_modules/next/dist/build/polyfills/process.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction Sidebar() {\n    _s();\n    const [userData, setUserData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [company, setCompany] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [ADCompany, setADCompany] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const userCookie = js_cookie__WEBPACK_IMPORTED_MODULE_17__[\"default\"].get(\"user\");\n        const companyCookie = js_cookie__WEBPACK_IMPORTED_MODULE_17__[\"default\"].get(\"company\");\n        const ADcompanyCookie = js_cookie__WEBPACK_IMPORTED_MODULE_17__[\"default\"].get(\"ADCompanyName\");\n        if (userCookie) {\n            setUserData(JSON.parse(userCookie));\n        }\n        if (companyCookie) {\n            setCompany(companyCookie);\n        }\n        if (ADcompanyCookie) {\n            setADCompany(ADcompanyCookie);\n        }\n    }, []);\n    const { instance } = (0,_azure_msal_react__WEBPACK_IMPORTED_MODULE_16__.useMsal)();\n    // const userRoleData = getCookieData(\"user\");\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const currentPathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_14__.usePathname)();\n    const [isSuppliersActive, setIsSuppliersActive] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)((currentPathname === null || currentPathname === void 0 ? void 0 : currentPathname.startsWith(\"/suppliers\")) || (currentPathname === null || currentPathname === void 0 ? void 0 : currentPathname.startsWith(\"/supplier\")));\n    const [isUsersActive, setIsUsersActive] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(currentPathname === \"/users\");\n    const [isLogsActive, setIsLogsActive] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(currentPathname === \"/viewlogs\");\n    const [isProductsActive, setIsProductsActive] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(currentPathname === \"/products\");\n    const [isWhatifActive, setIsWhatifActive] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(currentPathname === \"/whatif\");\n    const [isServiceLevelActive, setIsServiceLevelActive] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(currentPathname === \"/service_level\");\n    const [isRawMaterialRequestActive, setIsRawMaterialRequestActive] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(currentPathname === \"/raw-material-request/add\");\n    const getLogo = (company)=>{\n        if (!company) return;\n        switch(company){\n            case \"dpsltd\":\n                return _public_images_dps_logo_png__WEBPACK_IMPORTED_MODULE_3__[\"default\"];\n            case \"DPS MS\":\n                return _public_images_dps_ms_logo_png__WEBPACK_IMPORTED_MODULE_5__[\"default\"];\n            case \"efcltd\":\n                return _public_images_efc_logo_jpg__WEBPACK_IMPORTED_MODULE_4__[\"default\"];\n            case \"fpp-ltd\":\n                return _public_images_fpp_logo_png__WEBPACK_IMPORTED_MODULE_6__[\"default\"];\n            case \"thl\":\n                return _public_images_efc_logo_jpg__WEBPACK_IMPORTED_MODULE_4__[\"default\"];\n            case \"issproduce\":\n                return _public_images_iss_logo_png__WEBPACK_IMPORTED_MODULE_7__[\"default\"];\n            case \"flrs\":\n                return _public_images_efc_logo_jpg__WEBPACK_IMPORTED_MODULE_4__[\"default\"];\n            case \"issproduce\":\n                return _public_images_efc_logo_jpg__WEBPACK_IMPORTED_MODULE_4__[\"default\"];\n            default:\n                return;\n        }\n    };\n    const getLogoSizeClass = (company)=>{\n        if (!company) return \"h-14 w-100\";\n        switch(company){\n            case \"dpsltd\":\n                return \"!h-16 !w-auto\";\n            default:\n                return \"h-14 w-100\";\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n            id: \"sidemenu\",\n            className: \"navbar navbar-default sidebar bg-skin-primary\",\n            role: \"navigation\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"container-fluid h-full\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_12___default()), {\n                        href: \"/suppliers\",\n                        title: \"Home\",\n                        className: \"z-50\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"brand\",\n                            children: company ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_13___default()), {\n                                src: getLogo(company == \"dpsltd\" && ADCompany == \"DPS MS\" ? \"DPS MS\" : company),\n                                alt: \"logo\",\n                                className: getLogoSizeClass(company == \"dpsltd\" && ADCompany == \"DPS MS\" ? \"DPS MS\" : company)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\Sidebar.js\",\n                                lineNumber: 119,\n                                columnNumber: 17\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_loader_spinner__WEBPACK_IMPORTED_MODULE_18__.Oval, {\n                                    color: \"#002D73\",\n                                    height: 20,\n                                    width: 20,\n                                    visible: true,\n                                    ariaLabel: \"oval-loading\",\n                                    secondaryColor: \"#0066FF\",\n                                    strokeWidth: 2,\n                                    strokeWidthSecondary: 2\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\Sidebar.js\",\n                                    lineNumber: 134,\n                                    columnNumber: 19\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\Sidebar.js\",\n                                lineNumber: 133,\n                                columnNumber: 17\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\Sidebar.js\",\n                            lineNumber: 117,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\Sidebar.js\",\n                        lineNumber: 116,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        id: \"bs-sidebar-navbar-collapse-1\",\n                        className: \"pt-10 w-100 text-center flex flex-col justify-between flex-end items-stretch h-full\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col w-full\",\n                                children: userData && company && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_SideBarLinks__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                    isSuppliersActive: isSuppliersActive,\n                                    userData: userData,\n                                    company: company,\n                                    isUsersActive: isUsersActive,\n                                    isLogsActive: isLogsActive,\n                                    isProductsActive: isProductsActive,\n                                    isWhatifActive: isWhatifActive,\n                                    isServiceLevelActive: isServiceLevelActive\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\Sidebar.js\",\n                                    lineNumber: 154,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\Sidebar.js\",\n                                lineNumber: 152,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-center flex-col items-center gap-4 my-4 mb-20 cursor-pointer\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        title: \"IT Training Material\",\n                                        href: \"\".concat(process.env.NEXT_PUBLIC_TRAINING_MATERIAL),\n                                        target: \"_blank\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            xmlns: \"http://www.w3.org/2000/svg\",\n                                            viewBox: \"0 0 448 512\",\n                                            fill: \"#FFFF\",\n                                            className: \"w-6\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                d: \"M0 88C0 39.4 39.4 0 88 0L392 0c30.9 0 56 25.1 56 56l0 288c0 22.3-13.1 41.6-32 50.6l0 69.4 8 0c13.3 0 24 10.7 24 24s-10.7 24-24 24L80 512c-44.2 0-80-35.8-80-80c0-2.7 .1-5.4 .4-8L0 424 0 88zM80 400c-17.7 0-32 14.3-32 32s14.3 32 32 32l288 0 0-64L80 400zM48 358.7c9.8-4.3 20.6-6.7 32-6.7l312 0c4.4 0 8-3.6 8-8l0-288c0-4.4-3.6-8-8-8L88 48C65.9 48 48 65.9 48 88l0 270.7zM160 112l8.8-17.7c2.9-5.9 11.4-5.9 14.3 0L192 112l17.7 8.8c5.9 2.9 5.9 11.4 0 14.3L192 144l-8.8 17.7c-2.9 5.9-11.4 5.9-14.3 0L160 144l-17.7-8.8c-5.9-2.9-5.9-11.4 0-14.3L160 112zM264 216l16.6-38.8c2.8-6.5 11.9-6.5 14.7 0L312 216l38.8 16.6c6.5 2.8 6.5 11.9 0 14.7L312 264l-16.6 38.8c-2.8 6.5-11.9 6.5-14.7 0L264 264l-38.8-16.6c-6.5-2.8-6.5-11.9 0-14.7L264 216z\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\Sidebar.js\",\n                                                lineNumber: 178,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\Sidebar.js\",\n                                            lineNumber: 172,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\Sidebar.js\",\n                                        lineNumber: 167,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        title: \"Logout\",\n                                        onClick: ()=>{\n                                            localStorage.removeItem(\"superUser\");\n                                            js_cookie__WEBPACK_IMPORTED_MODULE_17__[\"default\"].remove(\"company\");\n                                            js_cookie__WEBPACK_IMPORTED_MODULE_17__[\"default\"].remove(\"ADCompanyName\");\n                                            localStorage.removeItem(\"id\");\n                                            localStorage.removeItem(\"name\");\n                                            localStorage.removeItem(\"role\");\n                                            localStorage.removeItem(\"email\");\n                                            js_cookie__WEBPACK_IMPORTED_MODULE_17__[\"default\"].remove(\"user\");\n                                            js_cookie__WEBPACK_IMPORTED_MODULE_17__[\"default\"].remove(\"theme\");\n                                            js_cookie__WEBPACK_IMPORTED_MODULE_17__[\"default\"].remove(\"token\");\n                                            const redirectUrl = \"/login?redirect=\".concat(encodeURIComponent(window.location.pathname));\n                                            (0,_utils_auth_auth__WEBPACK_IMPORTED_MODULE_15__.logoutHandler)(instance, redirectUrl);\n                                        },\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_13___default()), {\n                                            src: _public_images_logout_icon_png__WEBPACK_IMPORTED_MODULE_11__[\"default\"],\n                                            alt: \"logout\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\Sidebar.js\",\n                                            lineNumber: 200,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\Sidebar.js\",\n                                        lineNumber: 181,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\Sidebar.js\",\n                                lineNumber: 166,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\Sidebar.js\",\n                        lineNumber: 148,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\Sidebar.js\",\n                lineNumber: 115,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\Sidebar.js\",\n            lineNumber: 110,\n            columnNumber: 7\n        }, this)\n    }, void 0, false);\n}\n_s(Sidebar, \"CqfPHC0NzGwmyxMjqjmKLbgxaBY=\", false, function() {\n    return [\n        _azure_msal_react__WEBPACK_IMPORTED_MODULE_16__.useMsal,\n        next_router__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        next_navigation__WEBPACK_IMPORTED_MODULE_14__.usePathname\n    ];\n});\n_c = Sidebar;\nvar _c;\n$RefreshReg$(_c, \"Sidebar\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevExports = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevExports) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports on update so we can compare the boundary\n                // signatures.\n                module.hot.dispose(function (data) {\n                    data.prevExports = currentExports;\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevExports !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevExports, currentExports)) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevExports !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/Sidebar.js\n"));

/***/ }),

/***/ "./pages/users.js":
/*!************************!*\
  !*** ./pages/users.js ***!
  \************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __N_SSP: function() { return /* binding */ __N_SSP; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _components_Layout__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/Layout */ \"./components/Layout.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _fortawesome_react_fontawesome__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @fortawesome/react-fontawesome */ \"./node_modules/@fortawesome/react-fontawesome/index.es.js\");\n/* harmony import */ var _fortawesome_free_solid_svg_icons__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! @fortawesome/free-solid-svg-icons */ \"./node_modules/@fortawesome/free-solid-svg-icons/index.mjs\");\n/* harmony import */ var ag_grid_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ag-grid-react */ \"./node_modules/ag-grid-react/lib/main.js\");\n/* harmony import */ var ag_grid_react__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(ag_grid_react__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var ag_grid_community_styles_ag_grid_css__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ag-grid-community/styles//ag-grid.css */ \"./node_modules/ag-grid-community/styles/ag-grid.css\");\n/* harmony import */ var ag_grid_community_styles_ag_grid_css__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(ag_grid_community_styles_ag_grid_css__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var ag_grid_community_styles_ag_theme_alpine_css__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ag-grid-community/styles//ag-theme-alpine.css */ \"./node_modules/ag-grid-community/styles/ag-theme-alpine.css\");\n/* harmony import */ var ag_grid_community_styles_ag_theme_alpine_css__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(ag_grid_community_styles_ag_theme_alpine_css__WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_7__);\n/* harmony import */ var _services_apiConfig__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/services/apiConfig */ \"./services/apiConfig.js\");\n/* harmony import */ var _utils_renderer_roleRenderer__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/utils/renderer/roleRenderer */ \"./utils/renderer/roleRenderer.js\");\n/* harmony import */ var _utils_renderer_departmentRenderer__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/utils/renderer/departmentRenderer */ \"./utils/renderer/departmentRenderer.js\");\n/* harmony import */ var js_cookie__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! js-cookie */ \"./node_modules/js-cookie/dist/js.cookie.mjs\");\n/* harmony import */ var react_toastify__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! react-toastify */ \"./node_modules/react-toastify/dist/react-toastify.esm.mjs\");\n/* harmony import */ var react_toastify_dist_ReactToastify_css__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! react-toastify/dist/ReactToastify.css */ \"./node_modules/react-toastify/dist/ReactToastify.css\");\n/* harmony import */ var react_toastify_dist_ReactToastify_css__WEBPACK_IMPORTED_MODULE_13___default = /*#__PURE__*/__webpack_require__.n(react_toastify_dist_ReactToastify_css__WEBPACK_IMPORTED_MODULE_13__);\n/* harmony import */ var _utils_userContext__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/utils/userContext */ \"./utils/userContext.js\");\n/* harmony import */ var _utils_getCookieData__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @/utils/getCookieData */ \"./utils/getCookieData.js\");\n/* harmony import */ var _utils_loaders_loadingContext__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @/utils/loaders/loadingContext */ \"./utils/loaders/loadingContext.js\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst users = (param)=>{\n    let { userData } = param;\n    _s();\n    const { setIsLoading } = (0,_utils_loaders_loadingContext__WEBPACK_IMPORTED_MODULE_16__.useLoading)();\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_7__.useRouter)();\n    const { userDetails, updateToken } = (0,_utils_userContext__WEBPACK_IMPORTED_MODULE_14__.useUser)();\n    const [pageSize, setPageSize] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(15);\n    const [rowData, setRowData] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [email, setEmail] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"\");\n    const [role, setRole] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"\");\n    const [department, setDepartment] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"\");\n    const [editData, setEditData] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)({});\n    const [validEmail, setValidEmail] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [validRole, setValidRole] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [validDepartment, setValidDepartment] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [isToken, setToken] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"\");\n    const [isEdit, setIsEdit] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [userId, setUserId] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(0);\n    const [salesDepartment, setSalesDepartment] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [procurementDepartment, setProcurementDepartment] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [financialDepartment, setFinancialDepartment] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [technicalDepartment, setTechnicalDepartment] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [isCommonError, setCommonError] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"\");\n    const gridRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)();\n    const emailRegex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$/;\n    const user = (0,_utils_getCookieData__WEBPACK_IMPORTED_MODULE_15__.getCookieData)(\"user\");\n    const IconsRenderer = (props)=>{\n        let updatedData;\n        const handleDelete = (event)=>{\n            const allData = props.data;\n            if (allData.userId == user.user_id) {\n                react_toastify__WEBPACK_IMPORTED_MODULE_12__.toast.error(\"Loggedin user can't delete their own email\", {\n                    position: \"top-right\"\n                });\n                return false;\n            }\n            let serverAddress = _services_apiConfig__WEBPACK_IMPORTED_MODULE_8__.apiConfig.serverAddress;\n            fetch(\"\".concat(serverAddress, \"users/delete-user/\").concat(allData.userId), {\n                method: \"DELETE\",\n                headers: {\n                    Authorization: \"Bearer \".concat(user.token),\n                    Accept: \"application/json\",\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    username: user.name,\n                    useremail: user.email\n                })\n            }).then((res)=>{\n                if (res.status === 400) {\n                    react_toastify__WEBPACK_IMPORTED_MODULE_12__.toast.error(\"There was an error with your request. Please check your data and try again.\");\n                } else if (res.status === 401) {\n                    react_toastify__WEBPACK_IMPORTED_MODULE_12__.toast.error(\"Your session has expired. Please log in again.\");\n                    setTimeout(()=>{\n                        localStorage.removeItem(\"superUser\");\n                        js_cookie__WEBPACK_IMPORTED_MODULE_11__[\"default\"].remove(\"company\");\n                        js_cookie__WEBPACK_IMPORTED_MODULE_11__[\"default\"].remove(\"ADCompanyName\");\n                        localStorage.removeItem(\"id\");\n                        localStorage.removeItem(\"name\");\n                        localStorage.removeItem(\"role\");\n                        localStorage.removeItem(\"email\");\n                        js_cookie__WEBPACK_IMPORTED_MODULE_11__[\"default\"].remove(\"user\");\n                        js_cookie__WEBPACK_IMPORTED_MODULE_11__[\"default\"].remove(\"theme\");\n                        js_cookie__WEBPACK_IMPORTED_MODULE_11__[\"default\"].remove(\"token\");\n                        const redirectUrl = \"/login?redirect=\".concat(encodeURIComponent(window.location.pathname));\n                        logoutHandler(instance, redirectUrl);\n                        ;\n                    }, 3000);\n                }\n                if (res.status === 200 || res.status == 409) {\n                    return res.json();\n                }\n                throw new Error(\"Failed to fetch data\");\n            }).then((json)=>{\n                if (json.status == 200) {\n                    updatedData = [\n                        ...rowData\n                    ];\n                    const index = updatedData.indexOf(allData);\n                    updatedData.splice(index, 1);\n                    props.api.applyTransaction({\n                        remove: updatedData\n                    });\n                    setRowData(updatedData);\n                    react_toastify__WEBPACK_IMPORTED_MODULE_12__.toast.success(json.message, {\n                        position: \"top-right\"\n                    });\n                }\n            }).catch((error)=>{\n            // toast.error(error.statusText, {\n            //   position: \"top-right\",\n            // });\n            });\n        };\n        const handleEdit = ()=>{\n            const editedData = props.data;\n            setEditData(editedData);\n            updatedData = [\n                ...rowData\n            ];\n            const index = updatedData.indexOf(editedData);\n            updatedData.splice(index, 1);\n            props.api.applyTransaction({\n                remove: updatedData\n            });\n            setRowData(updatedData);\n            setEmail(editedData.email);\n            setRole(editedData.role);\n            setDepartment(editedData.department);\n            setUserId(editedData.userId);\n            setIsEdit(true);\n        };\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex flex-row gap-4 justify-center text-skin-primary\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    onClick: handleEdit,\n                    disabled: isEdit,\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fortawesome_react_fontawesome__WEBPACK_IMPORTED_MODULE_3__.FontAwesomeIcon, {\n                        icon: _fortawesome_free_solid_svg_icons__WEBPACK_IMPORTED_MODULE_17__.faPenToSquare\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\users.js\",\n                        lineNumber: 151,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\users.js\",\n                    lineNumber: 150,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    onClick: handleDelete,\n                    className: \"text-red-500\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fortawesome_react_fontawesome__WEBPACK_IMPORTED_MODULE_3__.FontAwesomeIcon, {\n                        icon: _fortawesome_free_solid_svg_icons__WEBPACK_IMPORTED_MODULE_17__.faTrash\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\users.js\",\n                        lineNumber: 154,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\users.js\",\n                    lineNumber: 153,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\users.js\",\n            lineNumber: 149,\n            columnNumber: 7\n        }, undefined);\n    };\n    // useEffect(() => {\n    //   const roleIdFromCookies = cookies.role_id;\n    //   if (roleIdFromCookies !== 1) {\n    //     router.push('/unauthorized'); // Assuming you're using Next.js router\n    //   }\n    // }, []);\n    const handleSelectDepartment = (e)=>{\n        if (e.target.value == 1) {\n            if (e.target.checked) {\n                setSalesDepartment(true);\n                setProcurementDepartment(false);\n                setFinancialDepartment(false);\n                setTechnicalDepartment(false);\n            } else {\n                setSalesDepartment(false);\n                setProcurementDepartment(false);\n                setFinancialDepartment(false);\n                setTechnicalDepartment(false);\n            }\n        } else if (e.target.value == 2) {\n            if (e.target.checked) {\n                setSalesDepartment(false);\n                setProcurementDepartment(true);\n                setFinancialDepartment(false);\n                setTechnicalDepartment(false);\n            } else {\n                setSalesDepartment(false);\n                setProcurementDepartment(false);\n                setFinancialDepartment(false);\n                setTechnicalDepartment(false);\n            }\n        } else if (e.target.value == 3) {\n            if (e.target.checked) {\n                setSalesDepartment(false);\n                setProcurementDepartment(false);\n                setFinancialDepartment(true);\n                setTechnicalDepartment(false);\n            } else {\n                setSalesDepartment(false);\n                setProcurementDepartment(false);\n                setFinancialDepartment(false);\n                setTechnicalDepartment(false);\n            }\n        } else if (e.target.value == 5) {\n            if (e.target.checked) {\n                setSalesDepartment(false);\n                setProcurementDepartment(false);\n                setFinancialDepartment(false);\n                setTechnicalDepartment(true);\n            } else {\n                setSalesDepartment(false);\n                setProcurementDepartment(false);\n                setFinancialDepartment(false);\n                setTechnicalDepartment(true);\n            }\n        } else {\n            setSalesDepartment(false);\n            setProcurementDepartment(false);\n            setFinancialDepartment(false);\n            setTechnicalDepartment(false);\n        }\n    };\n    function handleSubmit() {\n        setIsEdit(false);\n        if (email && role) {\n            let serverAddress = _services_apiConfig__WEBPACK_IMPORTED_MODULE_8__.apiConfig.serverAddress;\n            if (isEdit) {\n                fetch(\"\".concat(serverAddress, \"users/update-user/\").concat(userId), {\n                    method: \"PUT\",\n                    headers: {\n                        Authorization: \"Bearer \".concat(user.token),\n                        Accept: \"application/json\",\n                        \"Content-Type\": \"application/json\"\n                    },\n                    body: JSON.stringify({\n                        role_id: role,\n                        email: email === null || email === void 0 ? void 0 : email.trim(),\n                        user: {\n                            username: user.name,\n                            useremail: user.email\n                        },\n                        department_id: department\n                    })\n                }).then((res)=>{\n                    if (res.status === 400 || res.status === 401) {\n                        setCommonError(\"Invalid Token\");\n                    }\n                    if (res.status === 200 || res.status == 409) {\n                        return res.json();\n                    }\n                    throw new Error(\"Failed to fetch data\");\n                }).then((json)=>{\n                    const newItem = {\n                        email: json[0].email,\n                        role: json[0].role_id,\n                        userId: json[0].user_id,\n                        department: json[0].department_id\n                    };\n                    setRowData([\n                        ...rowData,\n                        newItem\n                    ]);\n                    setEmail(\"\");\n                    setRole(\"\");\n                    setDepartment(\"\");\n                    react_toastify__WEBPACK_IMPORTED_MODULE_12__.toast.success(\"User successfully updated\", {\n                        position: \"top-right\"\n                    });\n                }).catch((error)=>{\n                // toast.error(error.statusText, {\n                //   position: \"top-right\",\n                // });\n                });\n            } else {\n                fetch(\"\".concat(serverAddress, \"users/add-user\"), {\n                    method: \"POST\",\n                    headers: {\n                        Authorization: \"Bearer \".concat(user.token),\n                        Accept: \"application/json\",\n                        \"Content-Type\": \"application/json\"\n                    },\n                    body: JSON.stringify({\n                        role_id: role,\n                        department_id: department,\n                        email: email === null || email === void 0 ? void 0 : email.trim(),\n                        user: {\n                            username: user.name,\n                            useremail: user.email\n                        }\n                    })\n                }).then((res)=>{\n                    if (res.status === 400 || res.status === 401) {\n                        setCommonError(\"Invalid Token\");\n                    }\n                    if (res.status === 200 || res.status == 409) {\n                        return res.json();\n                    }\n                    throw new Error(\"Failed to fetch data\");\n                }).then((json)=>{\n                    if (json.data == \"exists\") {\n                        react_toastify__WEBPACK_IMPORTED_MODULE_12__.toast.error(\"User is already exists.\", {\n                            position: \"top-right\"\n                        });\n                        return;\n                    }\n                    if (json.length > 0) {\n                        const newItem = {\n                            email: email,\n                            role: role,\n                            userId: json[0].id,\n                            department: department\n                        };\n                        setRowData([\n                            ...rowData,\n                            newItem\n                        ]);\n                        setEmail(\"\");\n                        react_toastify__WEBPACK_IMPORTED_MODULE_12__.toast.success(\"User successfully created\", {\n                            position: \"top-right\"\n                        });\n                    }\n                }).catch((error)=>{\n                // console.log(error);\n                // toast.error(error.statusText, {\n                //   position: \"top-right\",\n                // });\n                });\n            }\n        } else {\n            setValidEmail(true);\n            setValidRole(true);\n            setValidDepartment(true);\n        }\n    }\n    function handleCancel() {\n        setIsEdit(false);\n        if (isEdit) {\n            const newItem = {\n                email: editData.email,\n                role: editData.role,\n                userId: editData.userId,\n                department: editData.department\n            };\n            setRowData([\n                ...rowData,\n                newItem\n            ]);\n        }\n        setEmail(\"\");\n        setRole(\"\");\n        setDepartment(\"\");\n        setEditData({});\n        setValidDepartment(false);\n        setValidRole(false);\n        setValidEmail(false);\n    }\n    const handleUserValidation = ()=>{\n        if (email && emailRegex.test(email)) {\n            setValidEmail(false);\n        } else {\n            setValidEmail(true);\n        }\n        if (validRole) {\n            setValidRole(true);\n        } else {\n            setValidRole(false);\n        }\n        if (validDepartment) {\n            setValidDepartment(true);\n        } else {\n            setValidDepartment(false);\n        }\n        return true;\n    };\n    function getData() {\n        let serverAddress = _services_apiConfig__WEBPACK_IMPORTED_MODULE_8__.apiConfig.serverAddress;\n        return fetch(\"\".concat(serverAddress, \"users/get-users\"), {\n            method: \"GET\",\n            headers: {\n                Accept: \"application/json\",\n                \"Content-Type\": \"application/json\",\n                Authorization: \"Bearer \".concat(user.token)\n            }\n        }).then((res)=>{\n            if (res.status === 400 || res.status === 401) {\n                setCommonError(\"Invalid Token\");\n            }\n            if (res.status === 200) {\n                return res.json();\n            }\n            throw new Error(\"Failed to fetch data\");\n        }).catch((error)=>{\n            react_toastify__WEBPACK_IMPORTED_MODULE_12__.toast.error(\"Failed to fetch data\", error.message, {\n                position: \"top-right\"\n            });\n        });\n    }\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        setIsLoading(false);\n        if (typeof document !== \"undefined\") {\n            document.title = \"Users\";\n        }\n        if (true) {\n            localStorage.removeItem(\"supplier_id\");\n        }\n        getData().then((data)=>{\n            const formattedData = data === null || data === void 0 ? void 0 : data.map((row)=>({\n                    email: row.email,\n                    role: row.role_id,\n                    userId: row.user_id,\n                    department: row.department_id\n                }));\n            setRowData(formattedData);\n        });\n    }, []);\n    const handlePageSizeChange = (event)=>{\n        const newPageSize = parseInt(event.target.value, 15);\n        setPageSize(newPageSize);\n        gridRef.current.api.paginationSetPageSize(newPageSize);\n    };\n    const defaultColDef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useMemo)(()=>({\n            sortable: true,\n            filter: false,\n            resizable: true,\n            flex: 1,\n            suppressMenu: false\n        }));\n    const columnDefs = [\n        {\n            headerName: \"Email\",\n            field: \"email\",\n            suppressMenu: true,\n            suppressSizeToFit: true,\n            flex: \"6%\"\n        },\n        {\n            headerName: \"Role\",\n            field: \"role\",\n            cellRenderer: _utils_renderer_roleRenderer__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n            flex: \"4%\",\n            hide: false\n        },\n        {\n            headerName: \"Department\",\n            field: \"department\",\n            cellRenderer: _utils_renderer_departmentRenderer__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n            flex: \"4%\",\n            hide: false\n        },\n        {\n            field: \"\",\n            cellRenderer: IconsRenderer,\n            flex: \"4%\",\n            cellStyle: ()=>({})\n        },\n        {\n            field: \"userId\",\n            //cellRenderer: IconsRenderer,\n            flex: \"4%\",\n            cellStyle: ()=>({}),\n            hide: true,\n            suppressFiltersToolPanel: true\n        }\n    ];\n    const onFilterTextBoxChanged = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(()=>{\n        gridRef.current.api.setQuickFilter(document.getElementById(\"filter-text-box\").value);\n    }, []);\n    const handleGridReady = (params)=>{\n        params.api.setColumnDefs(columnDefs);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_toastify__WEBPACK_IMPORTED_MODULE_12__.ToastContainer, {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\users.js\",\n                lineNumber: 491,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Layout__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                userData: userData,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-row justify-between w-[95%] gap-8 pe-1\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-[50%]\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-row md:flex-col lg:flex-row items-end\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex gap-6 w-full justify-end\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"relative block w-[47vh] text-gray-400 focus-within:text-gray-600 mt-0 pt-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"absolute px-4 py-1 text-black\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fortawesome_react_fontawesome__WEBPACK_IMPORTED_MODULE_3__.FontAwesomeIcon, {\n                                                        icon: _fortawesome_free_solid_svg_icons__WEBPACK_IMPORTED_MODULE_17__.faSearch,\n                                                        className: \"fw-bold\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\users.js\",\n                                                        lineNumber: 499,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\users.js\",\n                                                    lineNumber: 498,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"text\",\n                                                    id: \"filter-text-box\",\n                                                    placeholder: \"Search\",\n                                                    onInput: onFilterTextBoxChanged,\n                                                    className: \"block w-full px-4 pl-10 text-gray-500 placeholder-gray-400 searchbar border rounded-lg appearance-none form-input focus:outline-none\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\users.js\",\n                                                    lineNumber: 501,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\users.js\",\n                                            lineNumber: 497,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\users.js\",\n                                        lineNumber: 496,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\users.js\",\n                                    lineNumber: 495,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"my-5\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative ag-theme-alpine\",\n                                        style: {\n                                            height: \"calc(100vh - 150px)\"\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ag_grid_react__WEBPACK_IMPORTED_MODULE_4__.AgGridReact, {\n                                                rowData: rowData,\n                                                ref: gridRef,\n                                                columnDefs: columnDefs,\n                                                defaultColDef: defaultColDef,\n                                                suppressRowClickSelection: true,\n                                                rowSelection: \"multiple\",\n                                                pagination: true,\n                                                paginationPageSize: pageSize,\n                                                onPageSizeChanged: handlePageSizeChange,\n                                                tooltipShowDelay: 0,\n                                                tooltipHideDelay: 1000,\n                                                onGridReady: handleGridReady\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\users.js\",\n                                                lineNumber: 516,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-start mt-2 pagination-style\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    htmlFor: \"page-size-select pagination\",\n                                                    className: \"inputs\",\n                                                    children: [\n                                                        \"Show\",\n                                                        \" \",\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                            id: \"page-size-select\",\n                                                            onChange: handlePageSizeChange,\n                                                            value: pageSize,\n                                                            className: \"focus:outline-none\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                    value: 10,\n                                                                    children: \"10\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\users.js\",\n                                                                    lineNumber: 542,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                    value: 15,\n                                                                    children: \"15\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\users.js\",\n                                                                    lineNumber: 543,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                    value: 25,\n                                                                    children: \"25\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\users.js\",\n                                                                    lineNumber: 544,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                    value: 50,\n                                                                    children: \"50\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\users.js\",\n                                                                    lineNumber: 545,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                    value: 100,\n                                                                    children: \"100\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\users.js\",\n                                                                    lineNumber: 546,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\users.js\",\n                                                            lineNumber: 536,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        \" \",\n                                                        \"entries\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\users.js\",\n                                                    lineNumber: 531,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\users.js\",\n                                                lineNumber: 530,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\users.js\",\n                                        lineNumber: 512,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\users.js\",\n                                    lineNumber: 511,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\users.js\",\n                            lineNumber: 494,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-[50%] mt-14\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative panel-container contentsectionbg rounded-lg w-full 2xl:w-[calc(100%-70px)] p-4 pb-0 shadow-lg\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"m-3 mb-0\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mb-3\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                className: \"formtitle pb-1\",\n                                                children: \"Assign user role \"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\users.js\",\n                                                lineNumber: 558,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\users.js\",\n                                            lineNumber: 557,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex flex-col mb-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"labels mb-1\",\n                                                    children: [\n                                                        \"Email \",\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-red-500\",\n                                                            children: \"*\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\users.js\",\n                                                            lineNumber: 562,\n                                                            columnNumber: 27\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\users.js\",\n                                                    lineNumber: 561,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"email\",\n                                                    maxLength: 80,\n                                                    name: \"Email\",\n                                                    value: email,\n                                                    onChange: (e)=>{\n                                                        setEmail(e.target.value), handleUserValidation;\n                                                    },\n                                                    className: \"w-full px-2 2xl:px-3 border border-light-gray rounded-md searchbar\",\n                                                    onBlur: handleUserValidation\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\users.js\",\n                                                    lineNumber: 564,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                validEmail && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-red-500\",\n                                                    children: \"Please enter valid email address\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\users.js\",\n                                                    lineNumber: 575,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\users.js\",\n                                            lineNumber: 560,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex flex-col mb-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"me-5 labels\",\n                                                    children: [\n                                                        \"Role \",\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-red-500\",\n                                                            children: \"*\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\users.js\",\n                                                            lineNumber: 582,\n                                                            columnNumber: 26\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\users.js\",\n                                                    lineNumber: 581,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex mt-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                    id: \"approver-radio\",\n                                                                    type: \"radio\",\n                                                                    value: \"2\",\n                                                                    checked: role == 2,\n                                                                    onChange: ()=>{\n                                                                        setRole(2), handleUserValidation;\n                                                                    },\n                                                                    onBlur: handleUserValidation,\n                                                                    className: \"ml-0 w-4 h-4 text-blue-600 bg-gray-100 border-light-gray focus:ring-blue-500 focus:ring-2 hover:cursor-pointer\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\users.js\",\n                                                                    lineNumber: 586,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    htmlFor: \"approver-radio\",\n                                                                    className: \"p-0 ml-2 me-5 text-blackcolor hover:cursor-pointer\",\n                                                                    children: \"Approver\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\users.js\",\n                                                                    lineNumber: 597,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\users.js\",\n                                                            lineNumber: 585,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                        id: \"admin-radio\",\n                                                                        type: \"radio\",\n                                                                        value: \"1\",\n                                                                        checked: role == 1,\n                                                                        onChange: ()=>{\n                                                                            setRole(1), handleUserValidation;\n                                                                        },\n                                                                        onBlur: handleUserValidation,\n                                                                        className: \"ml-0 w-4 h-4 text-blue-600 bg-gray-100 border-light-gray focus:ring-blue-500 focus:ring-2 hover:cursor-pointer\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\users.js\",\n                                                                        lineNumber: 607,\n                                                                        columnNumber: 25\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        htmlFor: \"admin-radio\",\n                                                                        className: \"p-0 ml-2 me-5 text-blackcolor hover:cursor-pointer\",\n                                                                        children: \"Administrator\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\users.js\",\n                                                                        lineNumber: 618,\n                                                                        columnNumber: 25\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\users.js\",\n                                                                lineNumber: 606,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\users.js\",\n                                                            lineNumber: 605,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                        id: \"user-radio\",\n                                                                        type: \"radio\",\n                                                                        value: \"4\",\n                                                                        checked: role == 4,\n                                                                        onChange: ()=>{\n                                                                            setRole(4), handleUserValidation;\n                                                                        },\n                                                                        onBlur: handleUserValidation,\n                                                                        className: \"ml-0 w-4 h-4 text-blue-600 bg-gray-100 border-light-gray focus:ring-blue-500 focus:ring-2 hover:cursor-pointer\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\users.js\",\n                                                                        lineNumber: 628,\n                                                                        columnNumber: 25\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        htmlFor: \"user-radio\",\n                                                                        className: \"p-0 ml-2 me-5 text-blackcolor hover:cursor-pointer\",\n                                                                        children: \"User\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\users.js\",\n                                                                        lineNumber: 639,\n                                                                        columnNumber: 25\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\users.js\",\n                                                                lineNumber: 627,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\users.js\",\n                                                            lineNumber: 626,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\users.js\",\n                                                    lineNumber: 584,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                validRole && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-red-500\",\n                                                    children: \"Select role\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\users.js\",\n                                                    lineNumber: 649,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\users.js\",\n                                            lineNumber: 580,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex flex-col mb-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"me-5 labels\",\n                                                    children: [\n                                                        \"Department \",\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-red-500\",\n                                                            children: \"*\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\users.js\",\n                                                            lineNumber: 654,\n                                                            columnNumber: 32\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\users.js\",\n                                                    lineNumber: 653,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex mt-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                    id: \"department-sales\",\n                                                                    type: \"radio\",\n                                                                    value: 1,\n                                                                    checked: department == 1,\n                                                                    onChange: (e)=>{\n                                                                        setDepartment(1);\n                                                                        handleSelectDepartment(e);\n                                                                    },\n                                                                    onBlur: handleUserValidation,\n                                                                    className: \"ml-0 w-4 h-4 text-blue-600 bg-gray-100 border-light-gray focus:ring-blue-500 focus:ring-2 hover:cursor-pointer\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\users.js\",\n                                                                    lineNumber: 658,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    htmlFor: \"department-sales\",\n                                                                    className: \"p-0 ml-2 me-5 text-blackcolor hover:cursor-pointer\",\n                                                                    children: \"Sales\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\users.js\",\n                                                                    lineNumber: 670,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\users.js\",\n                                                            lineNumber: 657,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                    id: \"department-procurement\",\n                                                                    type: \"radio\",\n                                                                    value: 2,\n                                                                    checked: department == 2,\n                                                                    onChange: (e)=>{\n                                                                        setDepartment(2);\n                                                                        handleSelectDepartment(e);\n                                                                    },\n                                                                    onBlur: handleUserValidation,\n                                                                    className: \"ml-0 w-4 h-4 text-blue-600 bg-gray-100 border-light-gray focus:ring-blue-500 focus:ring-2 hover:cursor-pointer\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\users.js\",\n                                                                    lineNumber: 678,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    htmlFor: \"department-procurement\",\n                                                                    className: \"p-0 ml-2 me-5 text-blackcolor hover:cursor-pointer\",\n                                                                    children: \"Procurement\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\users.js\",\n                                                                    lineNumber: 690,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\users.js\",\n                                                            lineNumber: 677,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                    id: \"department-financial\",\n                                                                    type: \"radio\",\n                                                                    value: 3,\n                                                                    checked: department == 3,\n                                                                    onChange: (e)=>{\n                                                                        setDepartment(3);\n                                                                        handleSelectDepartment(e);\n                                                                    },\n                                                                    onBlur: handleUserValidation,\n                                                                    className: \"ml-0 w-4 h-4 text-blue-600 bg-gray-100 border-light-gray focus:ring-blue-500 focus:ring-2 hover:cursor-pointer\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\users.js\",\n                                                                    lineNumber: 698,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    htmlFor: \"department-financial\",\n                                                                    className: \"p-0 ml-2 me-5 text-blackcolor hover:cursor-pointer\",\n                                                                    children: \"Financial\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\users.js\",\n                                                                    lineNumber: 710,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\users.js\",\n                                                            lineNumber: 697,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                    id: \"department-technical\",\n                                                                    type: \"radio\",\n                                                                    value: 5,\n                                                                    checked: department == 5,\n                                                                    onChange: (e)=>{\n                                                                        setDepartment(5);\n                                                                        handleSelectDepartment(e);\n                                                                    },\n                                                                    onBlur: handleUserValidation,\n                                                                    className: \"ml-0 w-4 h-4 text-blue-600 bg-gray-100 border-light-gray focus:ring-blue-500 focus:ring-2 hover:cursor-pointer\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\users.js\",\n                                                                    lineNumber: 718,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    htmlFor: \"department-technical\",\n                                                                    className: \"p-0 ml-2 me-5 text-blackcolor hover:cursor-pointer\",\n                                                                    children: \"Technical\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\users.js\",\n                                                                    lineNumber: 730,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\users.js\",\n                                                            lineNumber: 717,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\users.js\",\n                                                    lineNumber: 656,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                validDepartment && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-red-500\",\n                                                    children: \"Select Departement\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\users.js\",\n                                                    lineNumber: 759,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\users.js\",\n                                            lineNumber: 652,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex justify-end pb-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    className: \"border border-skin-primary text-skin-primary me-10 py-1 px-8 font-medium rounded-md\",\n                                                    onClick: handleCancel,\n                                                    children: \"Cancel\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\users.js\",\n                                                    lineNumber: 763,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    className: \"border border-skin-primary bg-skin-primary text-white rounded-md py-1 px-8 font-medium\",\n                                                    onClick: handleSubmit,\n                                                    disabled: email && emailRegex.test(email) && role && department ? false : true,\n                                                    children: \"Save\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\users.js\",\n                                                    lineNumber: 769,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\users.js\",\n                                            lineNumber: 762,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\users.js\",\n                                    lineNumber: 556,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\users.js\",\n                                lineNumber: 555,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\users.js\",\n                            lineNumber: 554,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\users.js\",\n                    lineNumber: 493,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\users.js\",\n                lineNumber: 492,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true);\n};\n_s(users, \"KE0fG4x6rloQ6DnpnPKDImS3gas=\", false, function() {\n    return [\n        _utils_loaders_loadingContext__WEBPACK_IMPORTED_MODULE_16__.useLoading,\n        next_router__WEBPACK_IMPORTED_MODULE_7__.useRouter,\n        _utils_userContext__WEBPACK_IMPORTED_MODULE_14__.useUser\n    ];\n});\nvar __N_SSP = true;\n/* harmony default export */ __webpack_exports__[\"default\"] = (users);\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevExports = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevExports) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports on update so we can compare the boundary\n                // signatures.\n                module.hot.dispose(function (data) {\n                    data.prevExports = currentExports;\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevExports !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevExports, currentExports)) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevExports !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9wYWdlcy91c2Vycy5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBQXlDO0FBTzFCO0FBQ2tEO0FBS3RCO0FBQ0M7QUFDRztBQUNRO0FBQ2Y7QUFDUztBQUNRO0FBQ1k7QUFDckM7QUFDdUI7QUFDUjtBQUNEO0FBQ1E7QUFDTTtBQUU1RCxNQUFNc0IsUUFBUTtRQUFDLEVBQUVDLFFBQVEsRUFBRTs7SUFDdkIsTUFBTSxFQUFFQyxZQUFZLEVBQUUsR0FBR0gsMEVBQVVBO0lBRXJDLE1BQU1JLFNBQVNiLHNEQUFTQTtJQUN4QixNQUFNLEVBQUVjLFdBQVcsRUFBRUMsV0FBVyxFQUFFLEdBQUdSLDREQUFPQTtJQUM1QyxNQUFNLENBQUNTLFVBQVVDLFlBQVksR0FBRzFCLCtDQUFRQSxDQUFDO0lBQ3pDLE1BQU0sQ0FBQzJCLFNBQVNDLFdBQVcsR0FBRzVCLCtDQUFRQSxDQUFDLEVBQUU7SUFDekMsTUFBTSxDQUFDNkIsT0FBT0MsU0FBUyxHQUFHOUIsK0NBQVFBLENBQUM7SUFDbkMsTUFBTSxDQUFDK0IsTUFBTUMsUUFBUSxHQUFHaEMsK0NBQVFBLENBQUM7SUFDakMsTUFBTSxDQUFDaUMsWUFBWUMsY0FBYyxHQUFHbEMsK0NBQVFBLENBQUM7SUFDN0MsTUFBTSxDQUFDbUMsVUFBVUMsWUFBWSxHQUFHcEMsK0NBQVFBLENBQUMsQ0FBQztJQUUxQyxNQUFNLENBQUNxQyxZQUFZQyxjQUFjLEdBQUd0QywrQ0FBUUEsQ0FBQztJQUM3QyxNQUFNLENBQUN1QyxXQUFXQyxhQUFhLEdBQUd4QywrQ0FBUUEsQ0FBQztJQUMzQyxNQUFNLENBQUN5QyxpQkFBaUJDLG1CQUFtQixHQUFHMUMsK0NBQVFBLENBQUM7SUFDdkQsTUFBTSxDQUFDMkMsU0FBU0MsU0FBUyxHQUFHNUMsK0NBQVFBLENBQUM7SUFDckMsTUFBTSxDQUFDNkMsUUFBUUMsVUFBVSxHQUFHOUMsK0NBQVFBLENBQUM7SUFDckMsTUFBTSxDQUFDK0MsUUFBUUMsVUFBVSxHQUFHaEQsK0NBQVFBLENBQUM7SUFDckMsTUFBTSxDQUFDaUQsaUJBQWlCQyxtQkFBbUIsR0FBR2xELCtDQUFRQSxDQUFDO0lBQ3ZELE1BQU0sQ0FBQ21ELHVCQUF1QkMseUJBQXlCLEdBQUdwRCwrQ0FBUUEsQ0FBQztJQUNuRSxNQUFNLENBQUNxRCxxQkFBcUJDLHVCQUF1QixHQUFHdEQsK0NBQVFBLENBQUM7SUFDL0QsTUFBTSxDQUFDdUQscUJBQXNCQyx1QkFBdUIsR0FBR3hELCtDQUFRQSxDQUFDO0lBQ2hFLE1BQU0sQ0FBQ3lELGVBQWVDLGVBQWUsR0FBRzFELCtDQUFRQSxDQUFDO0lBQ2pELE1BQU0yRCxVQUFVMUQsNkNBQU1BO0lBRXRCLE1BQU0yRCxhQUFhO0lBQ25CLE1BQU1DLE9BQU81QyxvRUFBYUEsQ0FBQztJQUUzQixNQUFNNkMsZ0JBQWdCLENBQUNDO1FBQ3JCLElBQUlDO1FBRUosTUFBTUMsZUFBZSxDQUFDQztZQUNwQixNQUFNQyxVQUFVSixNQUFNSyxJQUFJO1lBRTFCLElBQUlELFFBQVFwQixNQUFNLElBQUljLEtBQUtRLE9BQU8sRUFBRTtnQkFDbEN0RCxrREFBS0EsQ0FBQ3VELEtBQUssQ0FBQyw4Q0FBOEM7b0JBQ3hEQyxVQUFVO2dCQUNaO2dCQUNBLE9BQU87WUFDVDtZQUNBLElBQUlDLGdCQUFnQjlELDBEQUFTQSxDQUFDOEQsYUFBYTtZQUMzQ0MsTUFBTSxHQUFxQ04sT0FBbENLLGVBQWMsc0JBQW1DLE9BQWZMLFFBQVFwQixNQUFNLEdBQUk7Z0JBQzNEMkIsUUFBUTtnQkFDUkMsU0FBUztvQkFDUEMsZUFBZSxVQUFxQixPQUFYZixLQUFLZ0IsS0FBSztvQkFDbkNDLFFBQVE7b0JBQ1IsZ0JBQWdCO2dCQUNsQjtnQkFDQUMsTUFBTUMsS0FBS0MsU0FBUyxDQUFDO29CQUNuQkMsVUFBVXJCLEtBQUtzQixJQUFJO29CQUNuQkMsV0FBV3ZCLEtBQUtoQyxLQUFLO2dCQUN2QjtZQUNGLEdBQ0d3RCxJQUFJLENBQUMsQ0FBQ0M7Z0JBQ0wsSUFBSUEsSUFBSUMsTUFBTSxLQUFLLEtBQUs7b0JBQ3RCeEUsa0RBQUtBLENBQUN1RCxLQUFLLENBQ1Q7Z0JBRUosT0FBTyxJQUFJZ0IsSUFBSUMsTUFBTSxLQUFLLEtBQUs7b0JBQzdCeEUsa0RBQUtBLENBQUN1RCxLQUFLLENBQUM7b0JBQ1prQixXQUFXO3dCQUNUQyxhQUFhQyxVQUFVLENBQUM7d0JBQ3RCN0UseURBQWMsQ0FBQzt3QkFDZkEseURBQWMsQ0FBQzt3QkFDZjRFLGFBQWFDLFVBQVUsQ0FBQzt3QkFDeEJELGFBQWFDLFVBQVUsQ0FBQzt3QkFDeEJELGFBQWFDLFVBQVUsQ0FBQzt3QkFDeEJELGFBQWFDLFVBQVUsQ0FBQzt3QkFDeEI3RSx5REFBYyxDQUFDO3dCQUNmQSx5REFBYyxDQUFDO3dCQUNmQSx5REFBYyxDQUFDO3dCQUNqQixNQUFNK0UsY0FBYyxtQkFBZ0UsT0FBN0NDLG1CQUFtQkMsT0FBT0MsUUFBUSxDQUFDQyxRQUFRO3dCQUNoR0MsY0FBY0MsVUFBU047O29CQUNYLEdBQUc7Z0JBQ0w7Z0JBQ0EsSUFBSU4sSUFBSUMsTUFBTSxLQUFLLE9BQU9ELElBQUlDLE1BQU0sSUFBSSxLQUFLO29CQUMzQyxPQUFPRCxJQUFJYSxJQUFJO2dCQUNqQjtnQkFDQSxNQUFNLElBQUlDLE1BQU07WUFDbEIsR0FDQ2YsSUFBSSxDQUFDLENBQUNjO2dCQUNMLElBQUlBLEtBQUtaLE1BQU0sSUFBSSxLQUFLO29CQUN0QnZCLGNBQWM7MkJBQUlyQztxQkFBUTtvQkFFMUIsTUFBTTBFLFFBQVFyQyxZQUFZc0MsT0FBTyxDQUFDbkM7b0JBQ2xDSCxZQUFZdUMsTUFBTSxDQUFDRixPQUFPO29CQUUxQnRDLE1BQU15QyxHQUFHLENBQUNDLGdCQUFnQixDQUFDO3dCQUFFZCxRQUFRM0I7b0JBQVk7b0JBRWpEcEMsV0FBV29DO29CQUVYakQsa0RBQUtBLENBQUMyRixPQUFPLENBQUNQLEtBQUtRLE9BQU8sRUFBRTt3QkFDMUJwQyxVQUFVO29CQUNaO2dCQUNGO1lBQ0YsR0FDQ3FDLEtBQUssQ0FBQyxDQUFDdEM7WUFDTixrQ0FBa0M7WUFDbEMsMkJBQTJCO1lBQzNCLE1BQU07WUFDUjtRQUNKO1FBRUEsTUFBTXVDLGFBQWE7WUFDakIsTUFBTUMsYUFBYS9DLE1BQU1LLElBQUk7WUFDN0JoQyxZQUFZMEU7WUFFWjlDLGNBQWM7bUJBQUlyQzthQUFRO1lBQzFCLE1BQU0wRSxRQUFRckMsWUFBWXNDLE9BQU8sQ0FBQ1E7WUFDbEM5QyxZQUFZdUMsTUFBTSxDQUFDRixPQUFPO1lBQzFCdEMsTUFBTXlDLEdBQUcsQ0FBQ0MsZ0JBQWdCLENBQUM7Z0JBQUVkLFFBQVEzQjtZQUFZO1lBQ2pEcEMsV0FBV29DO1lBQ1hsQyxTQUFTZ0YsV0FBV2pGLEtBQUs7WUFDekJHLFFBQVE4RSxXQUFXL0UsSUFBSTtZQUN2QkcsY0FBYzRFLFdBQVc3RSxVQUFVO1lBQ25DZSxVQUFVOEQsV0FBVy9ELE1BQU07WUFDM0JELFVBQVU7UUFDWjtRQUVBLHFCQUNFLDhEQUFDaUU7WUFBSUMsV0FBVTs7OEJBQ2IsOERBQUNDO29CQUFPQyxTQUFTTDtvQkFBWU0sVUFBVXRFOzhCQUNyQyw0RUFBQ3pDLDJFQUFlQTt3QkFBQ2dILE1BQU03Ryw2RUFBYUE7Ozs7Ozs7Ozs7OzhCQUV0Qyw4REFBQzBHO29CQUFPQyxTQUFTakQ7b0JBQWMrQyxXQUFVOzhCQUN2Qyw0RUFBQzVHLDJFQUFlQTt3QkFBQ2dILE1BQU05Ryx1RUFBT0E7Ozs7Ozs7Ozs7Ozs7Ozs7O0lBSXRDO0lBQ0Esb0JBQW9CO0lBQ3BCLCtDQUErQztJQUMvQyxtQ0FBbUM7SUFDbkMsNEVBQTRFO0lBQzVFLE1BQU07SUFDTixVQUFVO0lBRVYsTUFBTStHLHlCQUF5QixDQUFDQztRQUM5QixJQUFHQSxFQUFFQyxNQUFNLENBQUNDLEtBQUssSUFBSSxHQUFFO1lBQ3JCLElBQUdGLEVBQUVDLE1BQU0sQ0FBQ0UsT0FBTyxFQUFDO2dCQUNsQnZFLG1CQUFtQjtnQkFDbkJFLHlCQUF5QjtnQkFDekJFLHVCQUF1QjtnQkFDdkJFLHVCQUF1QjtZQUN6QixPQUFPO2dCQUNMTixtQkFBbUI7Z0JBQ25CRSx5QkFBeUI7Z0JBQ3pCRSx1QkFBdUI7Z0JBQ3ZCRSx1QkFBdUI7WUFDekI7UUFDRixPQUFPLElBQUc4RCxFQUFFQyxNQUFNLENBQUNDLEtBQUssSUFBSSxHQUFFO1lBQzVCLElBQUdGLEVBQUVDLE1BQU0sQ0FBQ0UsT0FBTyxFQUFDO2dCQUNsQnZFLG1CQUFtQjtnQkFDbkJFLHlCQUF5QjtnQkFDekJFLHVCQUF1QjtnQkFDdkJFLHVCQUF1QjtZQUN6QixPQUFPO2dCQUNMTixtQkFBbUI7Z0JBQ25CRSx5QkFBeUI7Z0JBQ3pCRSx1QkFBdUI7Z0JBQ3ZCRSx1QkFBdUI7WUFDekI7UUFDRixPQUFPLElBQUc4RCxFQUFFQyxNQUFNLENBQUNDLEtBQUssSUFBSSxHQUFFO1lBQzVCLElBQUdGLEVBQUVDLE1BQU0sQ0FBQ0UsT0FBTyxFQUFDO2dCQUNsQnZFLG1CQUFtQjtnQkFDbkJFLHlCQUF5QjtnQkFDekJFLHVCQUF1QjtnQkFDdkJFLHVCQUF1QjtZQUN6QixPQUFPO2dCQUNMTixtQkFBbUI7Z0JBQ25CRSx5QkFBeUI7Z0JBQ3pCRSx1QkFBdUI7Z0JBQ3ZCRSx1QkFBdUI7WUFDekI7UUFDRixPQUFPLElBQUc4RCxFQUFFQyxNQUFNLENBQUNDLEtBQUssSUFBSSxHQUFFO1lBQzVCLElBQUdGLEVBQUVDLE1BQU0sQ0FBQ0UsT0FBTyxFQUFDO2dCQUNsQnZFLG1CQUFtQjtnQkFDbkJFLHlCQUF5QjtnQkFDekJFLHVCQUF1QjtnQkFDdkJFLHVCQUF1QjtZQUN6QixPQUFPO2dCQUNMTixtQkFBbUI7Z0JBQ25CRSx5QkFBeUI7Z0JBQ3pCRSx1QkFBdUI7Z0JBQ3ZCRSx1QkFBdUI7WUFDekI7UUFDRixPQUFPO1lBQ0xOLG1CQUFtQjtZQUNuQkUseUJBQXlCO1lBQ3pCRSx1QkFBdUI7WUFDdkJFLHVCQUF1QjtRQUN6QjtJQUNGO0lBRUEsU0FBU2tFO1FBQ1A1RSxVQUFVO1FBQ1YsSUFBSWpCLFNBQVNFLE1BQU07WUFDakIsSUFBSXlDLGdCQUFnQjlELDBEQUFTQSxDQUFDOEQsYUFBYTtZQUMzQyxJQUFJM0IsUUFBUTtnQkFDVjRCLE1BQU0sR0FBcUMxQixPQUFsQ3lCLGVBQWMsc0JBQTJCLE9BQVB6QixTQUFVO29CQUNuRDJCLFFBQVE7b0JBQ1JDLFNBQVM7d0JBQ1BDLGVBQWUsVUFBcUIsT0FBWGYsS0FBS2dCLEtBQUs7d0JBQ25DQyxRQUFRO3dCQUNSLGdCQUFnQjtvQkFDbEI7b0JBQ0FDLE1BQU1DLEtBQUtDLFNBQVMsQ0FBQzt3QkFDbkIwQyxTQUFTNUY7d0JBQ1RGLEtBQUssRUFBRUEsa0JBQUFBLDRCQUFBQSxNQUFPK0YsSUFBSTt3QkFDbEIvRCxNQUFNOzRCQUNKcUIsVUFBVXJCLEtBQUtzQixJQUFJOzRCQUNuQkMsV0FBV3ZCLEtBQUtoQyxLQUFLO3dCQUN2Qjt3QkFDQWdHLGVBQWU1RjtvQkFDakI7Z0JBQ0YsR0FDR29ELElBQUksQ0FBQyxDQUFDQztvQkFDTCxJQUFJQSxJQUFJQyxNQUFNLEtBQUssT0FBT0QsSUFBSUMsTUFBTSxLQUFLLEtBQUs7d0JBQzVDN0IsZUFBZTtvQkFDakI7b0JBQ0EsSUFBSTRCLElBQUlDLE1BQU0sS0FBSyxPQUFPRCxJQUFJQyxNQUFNLElBQUksS0FBSzt3QkFDM0MsT0FBT0QsSUFBSWEsSUFBSTtvQkFDakI7b0JBQ0EsTUFBTSxJQUFJQyxNQUFNO2dCQUNsQixHQUNDZixJQUFJLENBQUMsQ0FBQ2M7b0JBQ0wsTUFBTTJCLFVBQVU7d0JBQ2RqRyxPQUFPc0UsSUFBSSxDQUFDLEVBQUUsQ0FBQ3RFLEtBQUs7d0JBQ3BCRSxNQUFNb0UsSUFBSSxDQUFDLEVBQUUsQ0FBQ3dCLE9BQU87d0JBQ3JCNUUsUUFBUW9ELElBQUksQ0FBQyxFQUFFLENBQUM5QixPQUFPO3dCQUN2QnBDLFlBQVlrRSxJQUFJLENBQUMsRUFBRSxDQUFDMEIsYUFBYTtvQkFDbkM7b0JBQ0FqRyxXQUFXOzJCQUFJRDt3QkFBU21HO3FCQUFRO29CQUNoQ2hHLFNBQVM7b0JBQ1RFLFFBQVE7b0JBQ1JFLGNBQWM7b0JBQ2RuQixrREFBS0EsQ0FBQzJGLE9BQU8sQ0FBQyw2QkFBNkI7d0JBQ3pDbkMsVUFBVTtvQkFDWjtnQkFDRixHQUNDcUMsS0FBSyxDQUFDLENBQUN0QztnQkFDTixrQ0FBa0M7Z0JBQ2xDLDJCQUEyQjtnQkFDM0IsTUFBTTtnQkFDUjtZQUNKLE9BQU87Z0JBQ0xHLE1BQU0sR0FBaUIsT0FBZEQsZUFBYyxtQkFBaUI7b0JBQ3RDRSxRQUFRO29CQUNSQyxTQUFTO3dCQUNQQyxlQUFlLFVBQXFCLE9BQVhmLEtBQUtnQixLQUFLO3dCQUNuQ0MsUUFBUTt3QkFDUixnQkFBZ0I7b0JBQ2xCO29CQUNBQyxNQUFNQyxLQUFLQyxTQUFTLENBQUM7d0JBQ25CMEMsU0FBUzVGO3dCQUNUOEYsZUFBZTVGO3dCQUNmSixLQUFLLEVBQUVBLGtCQUFBQSw0QkFBQUEsTUFBTytGLElBQUk7d0JBQ2xCL0QsTUFBTTs0QkFDSnFCLFVBQVVyQixLQUFLc0IsSUFBSTs0QkFDbkJDLFdBQVd2QixLQUFLaEMsS0FBSzt3QkFDdkI7b0JBQ0Y7Z0JBQ0YsR0FDR3dELElBQUksQ0FBQyxDQUFDQztvQkFDTCxJQUFJQSxJQUFJQyxNQUFNLEtBQUssT0FBT0QsSUFBSUMsTUFBTSxLQUFLLEtBQUs7d0JBQzVDN0IsZUFBZTtvQkFDakI7b0JBQ0EsSUFBSTRCLElBQUlDLE1BQU0sS0FBSyxPQUFPRCxJQUFJQyxNQUFNLElBQUksS0FBSzt3QkFDM0MsT0FBT0QsSUFBSWEsSUFBSTtvQkFDakI7b0JBQ0EsTUFBTSxJQUFJQyxNQUFNO2dCQUNsQixHQUNDZixJQUFJLENBQUMsQ0FBQ2M7b0JBQ0wsSUFBSUEsS0FBSy9CLElBQUksSUFBSSxVQUFVO3dCQUN6QnJELGtEQUFLQSxDQUFDdUQsS0FBSyxDQUFDLDJCQUEyQjs0QkFDckNDLFVBQVU7d0JBQ1o7d0JBQ0E7b0JBQ0Y7b0JBQ0EsSUFBSTRCLEtBQUs0QixNQUFNLEdBQUcsR0FBRzt3QkFDbkIsTUFBTUQsVUFBVTs0QkFDZGpHLE9BQU9BOzRCQUNQRSxNQUFNQTs0QkFDTmdCLFFBQVFvRCxJQUFJLENBQUMsRUFBRSxDQUFDNkIsRUFBRTs0QkFDbEIvRixZQUFZQTt3QkFDZDt3QkFDQUwsV0FBVzsrQkFBSUQ7NEJBQVNtRzt5QkFBUTt3QkFDaENoRyxTQUFTO3dCQUNUZixrREFBS0EsQ0FBQzJGLE9BQU8sQ0FBQyw2QkFBNkI7NEJBQ3pDbkMsVUFBVTt3QkFDWjtvQkFDRjtnQkFDRixHQUNDcUMsS0FBSyxDQUFDLENBQUN0QztnQkFDTixzQkFBc0I7Z0JBQ3RCLGtDQUFrQztnQkFDbEMsMkJBQTJCO2dCQUMzQixNQUFNO2dCQUNSO1lBQ0o7UUFDRixPQUFPO1lBQ0xoQyxjQUFjO1lBQ2RFLGFBQWE7WUFDYkUsbUJBQW1CO1FBQ3JCO0lBQ0Y7SUFFQSxTQUFTdUY7UUFDUG5GLFVBQVU7UUFDVixJQUFHRCxRQUFPO1lBQ1IsTUFBTWlGLFVBQVU7Z0JBQ2RqRyxPQUFPTSxTQUFTTixLQUFLO2dCQUNyQkUsTUFBTUksU0FBU0osSUFBSTtnQkFDbkJnQixRQUFRWixTQUFTWSxNQUFNO2dCQUN2QmQsWUFBWUUsU0FBU0YsVUFBVTtZQUNqQztZQUNBTCxXQUFXO21CQUFJRDtnQkFBU21HO2FBQVE7UUFFbEM7UUFDQWhHLFNBQVM7UUFDVEUsUUFBUTtRQUNSRSxjQUFjO1FBQ2RFLFlBQVksQ0FBQztRQUNiTSxtQkFBbUI7UUFDbkJGLGFBQWE7UUFDYkYsY0FBYztJQUNoQjtJQUVBLE1BQU00Rix1QkFBdUI7UUFDM0IsSUFBSXJHLFNBQVMrQixXQUFXdUUsSUFBSSxDQUFDdEcsUUFBUTtZQUNuQ1MsY0FBYztRQUNoQixPQUFPO1lBQ0xBLGNBQWM7UUFDaEI7UUFFQSxJQUFJQyxXQUFXO1lBQ2JDLGFBQWE7UUFDZixPQUFPO1lBQ0xBLGFBQWE7UUFDZjtRQUVBLElBQUlDLGlCQUFpQjtZQUNuQkMsbUJBQW1CO1FBQ3JCLE9BQU87WUFDTEEsbUJBQW1CO1FBQ3JCO1FBRUEsT0FBTztJQUNUO0lBRUEsU0FBUzBGO1FBQ1AsSUFBSTVELGdCQUFnQjlELDBEQUFTQSxDQUFDOEQsYUFBYTtRQUUzQyxPQUFPQyxNQUFNLEdBQWlCLE9BQWRELGVBQWMsb0JBQWtCO1lBQzlDRSxRQUFRO1lBQ1JDLFNBQVM7Z0JBQ1BHLFFBQVE7Z0JBQ1IsZ0JBQWdCO2dCQUNoQkYsZUFBZSxVQUFxQixPQUFYZixLQUFLZ0IsS0FBSztZQUNyQztRQUNGLEdBQ0dRLElBQUksQ0FBQyxDQUFDQztZQUNMLElBQUlBLElBQUlDLE1BQU0sS0FBSyxPQUFPRCxJQUFJQyxNQUFNLEtBQUssS0FBSztnQkFDNUM3QixlQUFlO1lBQ2pCO1lBQ0EsSUFBSTRCLElBQUlDLE1BQU0sS0FBSyxLQUFLO2dCQUN0QixPQUFPRCxJQUFJYSxJQUFJO1lBQ2pCO1lBQ0EsTUFBTSxJQUFJQyxNQUFNO1FBQ2xCLEdBQ0NRLEtBQUssQ0FBQyxDQUFDdEM7WUFDTnZELGtEQUFLQSxDQUFDdUQsS0FBSyxDQUFDLHdCQUF3QkEsTUFBTXFDLE9BQU8sRUFBRTtnQkFDakRwQyxVQUFVO1lBQ1o7UUFDRjtJQUNKO0lBRUFwRSxnREFBU0EsQ0FBQztRQUNSa0IsYUFBYTtRQUNiLElBQUcsT0FBT2dILGFBQVksYUFBWTtZQUNoQ0EsU0FBU0MsS0FBSyxHQUFDO1FBQ2pCO1FBQ0EsSUFBSSxJQUE2QixFQUFFO1lBQ2pDN0MsYUFBYUMsVUFBVSxDQUFDO1FBQzFCO1FBRUEwQyxVQUFVL0MsSUFBSSxDQUFDLENBQUNqQjtZQUNkLE1BQU1tRSxnQkFBZ0JuRSxpQkFBQUEsMkJBQUFBLEtBQU1vRSxHQUFHLENBQUMsQ0FBQ0MsTUFBUztvQkFDeEM1RyxPQUFPNEcsSUFBSTVHLEtBQUs7b0JBQ2hCRSxNQUFNMEcsSUFBSWQsT0FBTztvQkFDakI1RSxRQUFRMEYsSUFBSXBFLE9BQU87b0JBQ25CcEMsWUFBYXdHLElBQUlaLGFBQWE7Z0JBQ2hDO1lBQ0FqRyxXQUFXMkc7UUFDYjtJQUNGLEdBQUcsRUFBRTtJQUVMLE1BQU1HLHVCQUF1QixDQUFDeEU7UUFDNUIsTUFBTXlFLGNBQWNDLFNBQVMxRSxNQUFNcUQsTUFBTSxDQUFDQyxLQUFLLEVBQUU7UUFDakQ5RixZQUFZaUg7UUFDWmhGLFFBQVFrRixPQUFPLENBQUNyQyxHQUFHLENBQUNzQyxxQkFBcUIsQ0FBQ0g7SUFDNUM7SUFFQSxNQUFNSSxnQkFBZ0JoSiw4Q0FBT0EsQ0FBQyxJQUFPO1lBQ25DaUosVUFBVTtZQUNWQyxRQUFRO1lBQ1JDLFdBQVc7WUFDWEMsTUFBTTtZQUNOQyxjQUFjO1FBQ2hCO0lBRUEsTUFBTUMsYUFBYTtRQUNqQjtZQUNFQyxZQUFZO1lBQ1pDLE9BQU87WUFDUEgsY0FBYztZQUNkSSxtQkFBbUI7WUFDbkJMLE1BQU07UUFDUjtRQUNBO1lBQ0VHLFlBQVk7WUFDWkMsT0FBTztZQUNQRSxjQUFjOUksb0VBQVlBO1lBQzFCd0ksTUFBTTtZQUNOTyxNQUFNO1FBQ1I7UUFDQTtZQUNFSixZQUFZO1lBQ1pDLE9BQU87WUFDUEUsY0FBYzdJLDJFQUFrQkE7WUFDaEN1SSxNQUFNO1lBQ05PLE1BQU07UUFDUjtRQUNBO1lBQ0VILE9BQU87WUFDUEUsY0FBYzNGO1lBQ2RxRixNQUFNO1lBQ05RLFdBQVcsSUFBTyxFQUFDO1FBQ3JCO1FBQ0E7WUFDRUosT0FBTztZQUNQLDhCQUE4QjtZQUM5QkosTUFBTTtZQUNOUSxXQUFXLElBQU8sRUFBQztZQUNuQkQsTUFBTTtZQUNORSwwQkFBMEI7UUFDNUI7S0FDRDtJQUVELE1BQU1DLHlCQUF5QjNKLGtEQUFXQSxDQUFDO1FBQ3pDeUQsUUFBUWtGLE9BQU8sQ0FBQ3JDLEdBQUcsQ0FBQ3NELGNBQWMsQ0FDaEN6QixTQUFTMEIsY0FBYyxDQUFDLG1CQUFtQnZDLEtBQUs7SUFFcEQsR0FBRyxFQUFFO0lBRUwsTUFBTXdDLGtCQUFrQixDQUFDQztRQUN2QkEsT0FBT3pELEdBQUcsQ0FBQzBELGFBQWEsQ0FBQ2I7SUFDM0I7SUFHQSxxQkFDRTs7MEJBQ0UsOERBQUN2SSwyREFBY0E7Ozs7OzBCQUNmLDhEQUFDakIsMERBQU1BO2dCQUFDdUIsVUFBVUE7MEJBQ2hCLDRFQUFDMkY7b0JBQUlDLFdBQVU7O3NDQUNiLDhEQUFDRDs0QkFBSUMsV0FBVTs7OENBQ2IsOERBQUNEO29DQUFJQyxXQUFVOzhDQUNiLDRFQUFDRDt3Q0FBSUMsV0FBVTtrREFDYiw0RUFBQ21EOzRDQUFNbkQsV0FBVTs7OERBQ2YsOERBQUNvRDtvREFBS3BELFdBQVU7OERBQ2QsNEVBQUM1RywyRUFBZUE7d0RBQUNnSCxNQUFNL0csd0VBQVFBO3dEQUFFMkcsV0FBVTs7Ozs7Ozs7Ozs7OERBRTdDLDhEQUFDcUQ7b0RBQ0NDLE1BQUs7b0RBQ0x0QyxJQUFHO29EQUNIdUMsYUFBWTtvREFDWkMsU0FBU1g7b0RBQ1Q3QyxXQUFVOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzhDQUtsQiw4REFBQ0Q7b0NBQUlDLFdBQVU7OENBQ2IsNEVBQUNEO3dDQUNDQyxXQUFVO3dDQUNWeUQsT0FBTzs0Q0FBRUMsUUFBUTt3Q0FBc0I7OzBEQUV2Qyw4REFBQ2xLLHNEQUFXQTtnREFDVm1CLFNBQVNBO2dEQUNUZ0osS0FBS2hIO2dEQUNMMEYsWUFBWUE7Z0RBQ1pOLGVBQWVBO2dEQUNmNkIseUJBQXlCO2dEQUN6QkMsY0FBYTtnREFDYkMsWUFBWTtnREFDWkMsb0JBQW9CdEo7Z0RBQ3BCdUosbUJBQW1CdEM7Z0RBQ25CdUMsa0JBQWtCO2dEQUNsQkMsa0JBQWtCO2dEQUNsQkMsYUFBYW5COzs7Ozs7MERBRWYsOERBQUNqRDtnREFBSUMsV0FBVTswREFDYiw0RUFBQ21EO29EQUNDaUIsU0FBUTtvREFDUnBFLFdBQVU7O3dEQUNYO3dEQUNNO3NFQUNMLDhEQUFDcUU7NERBQ0NyRCxJQUFHOzREQUNIc0QsVUFBVTVDOzREQUNWbEIsT0FBTy9GOzREQUNQdUYsV0FBVTs7OEVBRVYsOERBQUN1RTtvRUFBTy9ELE9BQU87OEVBQUk7Ozs7Ozs4RUFDbkIsOERBQUMrRDtvRUFBTy9ELE9BQU87OEVBQUk7Ozs7Ozs4RUFDbkIsOERBQUMrRDtvRUFBTy9ELE9BQU87OEVBQUk7Ozs7Ozs4RUFDbkIsOERBQUMrRDtvRUFBTy9ELE9BQU87OEVBQUk7Ozs7Ozs4RUFDbkIsOERBQUMrRDtvRUFBTy9ELE9BQU87OEVBQUs7Ozs7Ozs7Ozs7Ozt3REFDWjt3REFBSTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7c0NBT3hCLDhEQUFDVDs0QkFBSUMsV0FBVTtzQ0FDYiw0RUFBQ0Q7Z0NBQUlDLFdBQVU7MENBQ2IsNEVBQUNEO29DQUFJQyxXQUFVOztzREFDYiw4REFBQ0Q7NENBQUlDLFdBQVU7c0RBQ2IsNEVBQUN3RTtnREFBR3hFLFdBQVU7MERBQWlCOzs7Ozs7Ozs7OztzREFFakMsOERBQUNEOzRDQUFJQyxXQUFVOzs4REFDYiw4REFBQ21EO29EQUFNbkQsV0FBVTs7d0RBQWM7c0VBQ3ZCLDhEQUFDb0Q7NERBQUtwRCxXQUFVO3NFQUFlOzs7Ozs7Ozs7Ozs7OERBRXZDLDhEQUFDcUQ7b0RBQ0NDLE1BQUs7b0RBQU9tQixXQUFXO29EQUN2QnRHLE1BQUs7b0RBQ0xxQyxPQUFPM0Y7b0RBQ1B5SixVQUFVLENBQUNoRTt3REFDVHhGLFNBQVN3RixFQUFFQyxNQUFNLENBQUNDLEtBQUssR0FBR1U7b0RBQzVCO29EQUNBbEIsV0FBVTtvREFDVjBFLFFBQVF4RDs7Ozs7O2dEQUVUN0YsNEJBQ0MsOERBQUMrSDtvREFBS3BELFdBQVU7OERBQWU7Ozs7Ozs7Ozs7OztzREFLbkMsOERBQUNEOzRDQUFJQyxXQUFVOzs4REFDYiw4REFBQ21EO29EQUFNbkQsV0FBVTs7d0RBQWM7c0VBQ3hCLDhEQUFDb0Q7NERBQUtwRCxXQUFVO3NFQUFlOzs7Ozs7Ozs7Ozs7OERBRXRDLDhEQUFDRDtvREFBSUMsV0FBVTs7c0VBQ2IsOERBQUNEOzREQUFJQyxXQUFVOzs4RUFDYiw4REFBQ3FEO29FQUNDckMsSUFBRztvRUFDSHNDLE1BQUs7b0VBQ0w5QyxPQUFNO29FQUNOQyxTQUFTMUYsUUFBUTtvRUFDakJ1SixVQUFVO3dFQUNSdEosUUFBUSxJQUFJa0c7b0VBQ2Q7b0VBQ0F3RCxRQUFReEQ7b0VBQ1JsQixXQUFVOzs7Ozs7OEVBRVosOERBQUNtRDtvRUFDQ2lCLFNBQVE7b0VBQ1JwRSxXQUFVOzhFQUNYOzs7Ozs7Ozs7Ozs7c0VBS0gsOERBQUNEOzREQUFJQyxXQUFVO3NFQUNiLDRFQUFDRDtnRUFBSUMsV0FBVTs7a0ZBQ2IsOERBQUNxRDt3RUFDQ3JDLElBQUc7d0VBQ0hzQyxNQUFLO3dFQUNMOUMsT0FBTTt3RUFDTkMsU0FBUzFGLFFBQVE7d0VBQ2pCdUosVUFBVTs0RUFDUnRKLFFBQVEsSUFBSWtHO3dFQUNkO3dFQUNBd0QsUUFBUXhEO3dFQUNSbEIsV0FBVTs7Ozs7O2tGQUVaLDhEQUFDbUQ7d0VBQ0NpQixTQUFRO3dFQUNScEUsV0FBVTtrRkFDWDs7Ozs7Ozs7Ozs7Ozs7Ozs7c0VBS0wsOERBQUNEOzREQUFJQyxXQUFVO3NFQUNiLDRFQUFDRDtnRUFBSUMsV0FBVTs7a0ZBQ2IsOERBQUNxRDt3RUFDQ3JDLElBQUc7d0VBQ0hzQyxNQUFLO3dFQUNMOUMsT0FBTTt3RUFDTkMsU0FBUzFGLFFBQVE7d0VBQ2pCdUosVUFBVTs0RUFDUnRKLFFBQVEsSUFBSWtHO3dFQUNkO3dFQUNBd0QsUUFBUXhEO3dFQUNSbEIsV0FBVTs7Ozs7O2tGQUVaLDhEQUFDbUQ7d0VBQ0NpQixTQUFRO3dFQUNScEUsV0FBVTtrRkFDWDs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Z0RBTU56RSwyQkFDQyw4REFBQzZIO29EQUFLcEQsV0FBVTs4REFBZTs7Ozs7Ozs7Ozs7O3NEQUduQyw4REFBQ0Q7NENBQUlDLFdBQVU7OzhEQUNiLDhEQUFDbUQ7b0RBQU1uRCxXQUFVOzt3REFBYztzRUFDbEIsOERBQUNvRDs0REFBS3BELFdBQVU7c0VBQWU7Ozs7Ozs7Ozs7Ozs4REFFNUMsOERBQUNEO29EQUFJQyxXQUFVOztzRUFDYiw4REFBQ0Q7NERBQUlDLFdBQVU7OzhFQUNiLDhEQUFDcUQ7b0VBQ0dyQyxJQUFHO29FQUNIc0MsTUFBSztvRUFDTDlDLE9BQU87b0VBQ1BDLFNBQVN4RixjQUFjO29FQUN2QnFKLFVBQVUsQ0FBQ2hFO3dFQUNUcEYsY0FBYzt3RUFDZG1GLHVCQUF1QkM7b0VBQ3pCO29FQUNBb0UsUUFBUXhEO29FQUNSbEIsV0FBVTs7Ozs7OzhFQUVaLDhEQUFDbUQ7b0VBQ0NpQixTQUFRO29FQUNScEUsV0FBVTs4RUFDWDs7Ozs7Ozs7Ozs7O3NFQUlMLDhEQUFDRDs0REFBSUMsV0FBVTs7OEVBQ2IsOERBQUNxRDtvRUFDR3JDLElBQUc7b0VBQ0hzQyxNQUFLO29FQUNMOUMsT0FBTztvRUFDUEMsU0FBU3hGLGNBQWM7b0VBQ3ZCcUosVUFBVSxDQUFDaEU7d0VBQ1RwRixjQUFjO3dFQUNkbUYsdUJBQXVCQztvRUFDekI7b0VBQ0FvRSxRQUFReEQ7b0VBQ1JsQixXQUFVOzs7Ozs7OEVBRVosOERBQUNtRDtvRUFDQ2lCLFNBQVE7b0VBQ1JwRSxXQUFVOzhFQUNYOzs7Ozs7Ozs7Ozs7c0VBSUwsOERBQUNEOzREQUFJQyxXQUFVOzs4RUFDYiw4REFBQ3FEO29FQUNHckMsSUFBRztvRUFDSHNDLE1BQUs7b0VBQ0w5QyxPQUFPO29FQUNQQyxTQUFTeEYsY0FBYztvRUFDdkJxSixVQUFVLENBQUNoRTt3RUFDVHBGLGNBQWM7d0VBQ2RtRix1QkFBdUJDO29FQUN6QjtvRUFDQW9FLFFBQVF4RDtvRUFDUmxCLFdBQVU7Ozs7Ozs4RUFFWiw4REFBQ21EO29FQUNDaUIsU0FBUTtvRUFDUnBFLFdBQVU7OEVBQ1g7Ozs7Ozs7Ozs7OztzRUFJTCw4REFBQ0Q7NERBQUlDLFdBQVU7OzhFQUNiLDhEQUFDcUQ7b0VBQ0dyQyxJQUFHO29FQUNIc0MsTUFBSztvRUFDTDlDLE9BQU87b0VBQ1BDLFNBQVN4RixjQUFjO29FQUN2QnFKLFVBQVUsQ0FBQ2hFO3dFQUNUcEYsY0FBYzt3RUFDZG1GLHVCQUF1QkM7b0VBQ3pCO29FQUNBb0UsUUFBUXhEO29FQUNSbEIsV0FBVTs7Ozs7OzhFQUVaLDhEQUFDbUQ7b0VBQ0NpQixTQUFRO29FQUNScEUsV0FBVTs4RUFDWDs7Ozs7Ozs7Ozs7Ozs7Ozs7O2dEQXlCUnZFLGlDQUNHLDhEQUFDMkg7b0RBQUtwRCxXQUFVOzhEQUFlOzs7Ozs7Ozs7Ozs7c0RBR25DLDhEQUFDRDs0Q0FBSUMsV0FBVTs7OERBQ2IsOERBQUNDO29EQUNDRCxXQUFVO29EQUNWRSxTQUFTZTs4REFDVjs7Ozs7OzhEQUdELDhEQUFDaEI7b0RBQ0NELFdBQVU7b0RBQ1ZFLFNBQVNRO29EQUNUUCxVQUNFdEYsU0FBUytCLFdBQVd1RSxJQUFJLENBQUN0RyxVQUFVRSxRQUFRRSxhQUFhLFFBQVE7OERBRWpFOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQVdyQjtHQXJ2Qk1kOztRQUN1QkQsc0VBQVVBO1FBRXRCVCxrREFBU0E7UUFDYU8sd0RBQU9BOzs7O0FBbXZCOUMsK0RBQWVHLEtBQUtBLEVBQUMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vcGFnZXMvdXNlcnMuanM/MDA2MCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgTGF5b3V0IGZyb20gXCJAL2NvbXBvbmVudHMvTGF5b3V0XCI7XHJcbmltcG9ydCBSZWFjdCwge1xyXG4gIHVzZU1lbW8sXHJcbiAgdXNlU3RhdGUsXHJcbiAgdXNlUmVmLFxyXG4gIHVzZUNhbGxiYWNrLFxyXG4gIHVzZUVmZmVjdCxcclxufSBmcm9tIFwicmVhY3RcIjtcclxuaW1wb3J0IHsgRm9udEF3ZXNvbWVJY29uIH0gZnJvbSBcIkBmb3J0YXdlc29tZS9yZWFjdC1mb250YXdlc29tZVwiO1xyXG5pbXBvcnQge1xyXG4gIGZhU2VhcmNoLFxyXG4gIGZhVHJhc2gsXHJcbiAgZmFQZW5Ub1NxdWFyZSxcclxufSBmcm9tIFwiQGZvcnRhd2Vzb21lL2ZyZWUtc29saWQtc3ZnLWljb25zXCI7XHJcbmltcG9ydCB7IEFnR3JpZFJlYWN0IH0gZnJvbSBcImFnLWdyaWQtcmVhY3RcIjtcclxuaW1wb3J0IFwiYWctZ3JpZC1jb21tdW5pdHkvc3R5bGVzLy9hZy1ncmlkLmNzc1wiO1xyXG5pbXBvcnQgXCJhZy1ncmlkLWNvbW11bml0eS9zdHlsZXMvL2FnLXRoZW1lLWFscGluZS5jc3NcIjtcclxuaW1wb3J0IHsgdXNlUm91dGVyIH0gZnJvbSBcIm5leHQvcm91dGVyXCI7XHJcbmltcG9ydCB7IGFwaUNvbmZpZyB9IGZyb20gXCJAL3NlcnZpY2VzL2FwaUNvbmZpZ1wiO1xyXG5pbXBvcnQgcm9sZVJlbmRlcmVyIGZyb20gXCJAL3V0aWxzL3JlbmRlcmVyL3JvbGVSZW5kZXJlclwiO1xyXG5pbXBvcnQgZGVwYXJ0bWVudFJlbmRlcmVyIGZyb20gXCJAL3V0aWxzL3JlbmRlcmVyL2RlcGFydG1lbnRSZW5kZXJlclwiO1xyXG5pbXBvcnQgQ29va2llcyBmcm9tIFwianMtY29va2llXCI7XHJcbmltcG9ydCB7IFRvYXN0Q29udGFpbmVyLCB0b2FzdCB9IGZyb20gXCJyZWFjdC10b2FzdGlmeVwiO1xyXG5pbXBvcnQgXCJyZWFjdC10b2FzdGlmeS9kaXN0L1JlYWN0VG9hc3RpZnkuY3NzXCI7XHJcbmltcG9ydCB7IHVzZVVzZXIgfSBmcm9tIFwiQC91dGlscy91c2VyQ29udGV4dFwiO1xyXG5pbXBvcnQgeyBnZXRDb29raWVEYXRhIH0gZnJvbSBcIkAvdXRpbHMvZ2V0Q29va2llRGF0YVwiO1xyXG5pbXBvcnQgeyB1c2VMb2FkaW5nIH0gZnJvbSBcIkAvdXRpbHMvbG9hZGVycy9sb2FkaW5nQ29udGV4dFwiO1xyXG5cclxuY29uc3QgdXNlcnMgPSAoeyB1c2VyRGF0YSB9KSA9PiB7XHJcbiAgICBjb25zdCB7IHNldElzTG9hZGluZyB9ID0gdXNlTG9hZGluZygpO1xyXG5cclxuICBjb25zdCByb3V0ZXIgPSB1c2VSb3V0ZXIoKTtcclxuICBjb25zdCB7IHVzZXJEZXRhaWxzLCB1cGRhdGVUb2tlbiB9ID0gdXNlVXNlcigpO1xyXG4gIGNvbnN0IFtwYWdlU2l6ZSwgc2V0UGFnZVNpemVdID0gdXNlU3RhdGUoMTUpO1xyXG4gIGNvbnN0IFtyb3dEYXRhLCBzZXRSb3dEYXRhXSA9IHVzZVN0YXRlKFtdKTtcclxuICBjb25zdCBbZW1haWwsIHNldEVtYWlsXSA9IHVzZVN0YXRlKFwiXCIpO1xyXG4gIGNvbnN0IFtyb2xlLCBzZXRSb2xlXSA9IHVzZVN0YXRlKFwiXCIpO1xyXG4gIGNvbnN0IFtkZXBhcnRtZW50LCBzZXREZXBhcnRtZW50XSA9IHVzZVN0YXRlKFwiXCIpO1xyXG4gIGNvbnN0IFtlZGl0RGF0YSwgc2V0RWRpdERhdGFdID0gdXNlU3RhdGUoe30pO1xyXG5cclxuICBjb25zdCBbdmFsaWRFbWFpbCwgc2V0VmFsaWRFbWFpbF0gPSB1c2VTdGF0ZShmYWxzZSk7XHJcbiAgY29uc3QgW3ZhbGlkUm9sZSwgc2V0VmFsaWRSb2xlXSA9IHVzZVN0YXRlKGZhbHNlKTtcclxuICBjb25zdCBbdmFsaWREZXBhcnRtZW50LCBzZXRWYWxpZERlcGFydG1lbnRdID0gdXNlU3RhdGUoZmFsc2UpO1xyXG4gIGNvbnN0IFtpc1Rva2VuLCBzZXRUb2tlbl0gPSB1c2VTdGF0ZShcIlwiKTtcclxuICBjb25zdCBbaXNFZGl0LCBzZXRJc0VkaXRdID0gdXNlU3RhdGUoZmFsc2UpO1xyXG4gIGNvbnN0IFt1c2VySWQsIHNldFVzZXJJZF0gPSB1c2VTdGF0ZSgwKTtcclxuICBjb25zdCBbc2FsZXNEZXBhcnRtZW50LCBzZXRTYWxlc0RlcGFydG1lbnRdID0gdXNlU3RhdGUoZmFsc2UpO1xyXG4gIGNvbnN0IFtwcm9jdXJlbWVudERlcGFydG1lbnQsIHNldFByb2N1cmVtZW50RGVwYXJ0bWVudF0gPSB1c2VTdGF0ZShmYWxzZSk7XHJcbiAgY29uc3QgW2ZpbmFuY2lhbERlcGFydG1lbnQsIHNldEZpbmFuY2lhbERlcGFydG1lbnRdID0gdXNlU3RhdGUoZmFsc2UpO1xyXG4gIGNvbnN0IFt0ZWNobmljYWxEZXBhcnRtZW50ICwgc2V0VGVjaG5pY2FsRGVwYXJ0bWVudF0gPSB1c2VTdGF0ZShmYWxzZSk7XHJcbiAgY29uc3QgW2lzQ29tbW9uRXJyb3IsIHNldENvbW1vbkVycm9yXSA9IHVzZVN0YXRlKFwiXCIpO1xyXG4gIGNvbnN0IGdyaWRSZWYgPSB1c2VSZWYoKTtcclxuXHJcbiAgY29uc3QgZW1haWxSZWdleCA9IC9eW2EtekEtWjAtOS5fJSstXStAW2EtekEtWjAtOS4tXStcXC5bYS16QS1aXXsyLH0kLztcclxuICBjb25zdCB1c2VyID0gZ2V0Q29va2llRGF0YShcInVzZXJcIik7XHJcblxyXG4gIGNvbnN0IEljb25zUmVuZGVyZXIgPSAocHJvcHMpID0+IHtcclxuICAgIGxldCB1cGRhdGVkRGF0YTtcclxuXHJcbiAgICBjb25zdCBoYW5kbGVEZWxldGUgPSAoZXZlbnQpID0+IHtcclxuICAgICAgY29uc3QgYWxsRGF0YSA9IHByb3BzLmRhdGE7XHJcblxyXG4gICAgICBpZiAoYWxsRGF0YS51c2VySWQgPT0gdXNlci51c2VyX2lkKSB7XHJcbiAgICAgICAgdG9hc3QuZXJyb3IoXCJMb2dnZWRpbiB1c2VyIGNhbid0IGRlbGV0ZSB0aGVpciBvd24gZW1haWxcIiwge1xyXG4gICAgICAgICAgcG9zaXRpb246IFwidG9wLXJpZ2h0XCIsXHJcbiAgICAgICAgfSk7XHJcbiAgICAgICAgcmV0dXJuIGZhbHNlOyBcclxuICAgICAgfVxyXG4gICAgICBsZXQgc2VydmVyQWRkcmVzcyA9IGFwaUNvbmZpZy5zZXJ2ZXJBZGRyZXNzO1xyXG4gICAgICBmZXRjaChgJHtzZXJ2ZXJBZGRyZXNzfXVzZXJzL2RlbGV0ZS11c2VyLyR7YWxsRGF0YS51c2VySWR9YCwge1xyXG4gICAgICAgIG1ldGhvZDogXCJERUxFVEVcIixcclxuICAgICAgICBoZWFkZXJzOiB7XHJcbiAgICAgICAgICBBdXRob3JpemF0aW9uOiBgQmVhcmVyICR7dXNlci50b2tlbn1gLFxyXG4gICAgICAgICAgQWNjZXB0OiBcImFwcGxpY2F0aW9uL2pzb25cIixcclxuICAgICAgICAgIFwiQ29udGVudC1UeXBlXCI6IFwiYXBwbGljYXRpb24vanNvblwiLFxyXG4gICAgICAgIH0sXHJcbiAgICAgICAgYm9keTogSlNPTi5zdHJpbmdpZnkoe1xyXG4gICAgICAgICAgdXNlcm5hbWU6IHVzZXIubmFtZSxcclxuICAgICAgICAgIHVzZXJlbWFpbDogdXNlci5lbWFpbCxcclxuICAgICAgICB9KSxcclxuICAgICAgfSlcclxuICAgICAgICAudGhlbigocmVzKSA9PiB7XHJcbiAgICAgICAgICBpZiAocmVzLnN0YXR1cyA9PT0gNDAwKSB7XHJcbiAgICAgICAgICAgIHRvYXN0LmVycm9yKFxyXG4gICAgICAgICAgICAgIFwiVGhlcmUgd2FzIGFuIGVycm9yIHdpdGggeW91ciByZXF1ZXN0LiBQbGVhc2UgY2hlY2sgeW91ciBkYXRhIGFuZCB0cnkgYWdhaW4uXCJcclxuICAgICAgICAgICAgKTtcclxuICAgICAgICAgIH0gZWxzZSBpZiAocmVzLnN0YXR1cyA9PT0gNDAxKSB7XHJcbiAgICAgICAgICAgIHRvYXN0LmVycm9yKFwiWW91ciBzZXNzaW9uIGhhcyBleHBpcmVkLiBQbGVhc2UgbG9nIGluIGFnYWluLlwiKTtcclxuICAgICAgICAgICAgc2V0VGltZW91dCgoKSA9PiB7XHJcbiAgICAgICAgICAgICAgbG9jYWxTdG9yYWdlLnJlbW92ZUl0ZW0oXCJzdXBlclVzZXJcIik7XHJcbiAgICAgICAgICAgICAgICBDb29raWVzLnJlbW92ZShcImNvbXBhbnlcIilcclxuICAgICAgICAgICAgICAgIENvb2tpZXMucmVtb3ZlKFwiQURDb21wYW55TmFtZVwiKTtcclxuICAgICAgICAgICAgICAgIGxvY2FsU3RvcmFnZS5yZW1vdmVJdGVtKFwiaWRcIik7XHJcbiAgICAgICAgICAgICAgICBsb2NhbFN0b3JhZ2UucmVtb3ZlSXRlbShcIm5hbWVcIik7XHJcbiAgICAgICAgICAgICAgICBsb2NhbFN0b3JhZ2UucmVtb3ZlSXRlbShcInJvbGVcIik7XHJcbiAgICAgICAgICAgICAgICBsb2NhbFN0b3JhZ2UucmVtb3ZlSXRlbShcImVtYWlsXCIpO1xyXG4gICAgICAgICAgICAgICAgQ29va2llcy5yZW1vdmUoXCJ1c2VyXCIpXHJcbiAgICAgICAgICAgICAgICBDb29raWVzLnJlbW92ZShcInRoZW1lXCIpXHJcbiAgICAgICAgICAgICAgICBDb29raWVzLnJlbW92ZShcInRva2VuXCIpXHJcbiAgICAgICAgICAgICAgY29uc3QgcmVkaXJlY3RVcmwgPSBgL2xvZ2luP3JlZGlyZWN0PSR7ZW5jb2RlVVJJQ29tcG9uZW50KHdpbmRvdy5sb2NhdGlvbi5wYXRobmFtZSl9YDtcclxubG9nb3V0SGFuZGxlcihpbnN0YW5jZSxyZWRpcmVjdFVybCk7O1xyXG4gICAgICAgICAgICB9LCAzMDAwKTtcclxuICAgICAgICAgIH1cclxuICAgICAgICAgIGlmIChyZXMuc3RhdHVzID09PSAyMDAgfHwgcmVzLnN0YXR1cyA9PSA0MDkpIHtcclxuICAgICAgICAgICAgcmV0dXJuIHJlcy5qc29uKCk7XHJcbiAgICAgICAgICB9XHJcbiAgICAgICAgICB0aHJvdyBuZXcgRXJyb3IoXCJGYWlsZWQgdG8gZmV0Y2ggZGF0YVwiKTtcclxuICAgICAgICB9KVxyXG4gICAgICAgIC50aGVuKChqc29uKSA9PiB7XHJcbiAgICAgICAgICBpZiAoanNvbi5zdGF0dXMgPT0gMjAwKSB7XHJcbiAgICAgICAgICAgIHVwZGF0ZWREYXRhID0gWy4uLnJvd0RhdGFdO1xyXG5cclxuICAgICAgICAgICAgY29uc3QgaW5kZXggPSB1cGRhdGVkRGF0YS5pbmRleE9mKGFsbERhdGEpO1xyXG4gICAgICAgICAgICB1cGRhdGVkRGF0YS5zcGxpY2UoaW5kZXgsIDEpO1xyXG5cclxuICAgICAgICAgICAgcHJvcHMuYXBpLmFwcGx5VHJhbnNhY3Rpb24oeyByZW1vdmU6IHVwZGF0ZWREYXRhIH0pO1xyXG5cclxuICAgICAgICAgICAgc2V0Um93RGF0YSh1cGRhdGVkRGF0YSk7XHJcblxyXG4gICAgICAgICAgICB0b2FzdC5zdWNjZXNzKGpzb24ubWVzc2FnZSwge1xyXG4gICAgICAgICAgICAgIHBvc2l0aW9uOiBcInRvcC1yaWdodFwiLFxyXG4gICAgICAgICAgICB9KTtcclxuICAgICAgICAgIH1cclxuICAgICAgICB9KVxyXG4gICAgICAgIC5jYXRjaCgoZXJyb3IpID0+IHtcclxuICAgICAgICAgIC8vIHRvYXN0LmVycm9yKGVycm9yLnN0YXR1c1RleHQsIHtcclxuICAgICAgICAgIC8vICAgcG9zaXRpb246IFwidG9wLXJpZ2h0XCIsXHJcbiAgICAgICAgICAvLyB9KTtcclxuICAgICAgICB9KTtcclxuICAgIH07XHJcblxyXG4gICAgY29uc3QgaGFuZGxlRWRpdCA9ICgpID0+IHtcclxuICAgICAgY29uc3QgZWRpdGVkRGF0YSA9IHByb3BzLmRhdGFcclxuICAgICAgc2V0RWRpdERhdGEoZWRpdGVkRGF0YSk7IFxyXG5cclxuICAgICAgdXBkYXRlZERhdGEgPSBbLi4ucm93RGF0YV07XHJcbiAgICAgIGNvbnN0IGluZGV4ID0gdXBkYXRlZERhdGEuaW5kZXhPZihlZGl0ZWREYXRhKTtcclxuICAgICAgdXBkYXRlZERhdGEuc3BsaWNlKGluZGV4LCAxKTtcclxuICAgICAgcHJvcHMuYXBpLmFwcGx5VHJhbnNhY3Rpb24oeyByZW1vdmU6IHVwZGF0ZWREYXRhIH0pO1xyXG4gICAgICBzZXRSb3dEYXRhKHVwZGF0ZWREYXRhKTtcclxuICAgICAgc2V0RW1haWwoZWRpdGVkRGF0YS5lbWFpbCk7XHJcbiAgICAgIHNldFJvbGUoZWRpdGVkRGF0YS5yb2xlKTtcclxuICAgICAgc2V0RGVwYXJ0bWVudChlZGl0ZWREYXRhLmRlcGFydG1lbnQpO1xyXG4gICAgICBzZXRVc2VySWQoZWRpdGVkRGF0YS51c2VySWQpO1xyXG4gICAgICBzZXRJc0VkaXQodHJ1ZSk7XHJcbiAgICB9O1xyXG5cclxuICAgIHJldHVybiAoXHJcbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBmbGV4LXJvdyBnYXAtNCBqdXN0aWZ5LWNlbnRlciB0ZXh0LXNraW4tcHJpbWFyeVwiPlxyXG4gICAgICAgIDxidXR0b24gb25DbGljaz17aGFuZGxlRWRpdH0gZGlzYWJsZWQ9e2lzRWRpdH0+XHJcbiAgICAgICAgICA8Rm9udEF3ZXNvbWVJY29uIGljb249e2ZhUGVuVG9TcXVhcmV9IC8+XHJcbiAgICAgICAgPC9idXR0b24+XHJcbiAgICAgICAgPGJ1dHRvbiBvbkNsaWNrPXtoYW5kbGVEZWxldGV9IGNsYXNzTmFtZT1cInRleHQtcmVkLTUwMFwiPlxyXG4gICAgICAgICAgPEZvbnRBd2Vzb21lSWNvbiBpY29uPXtmYVRyYXNofSAvPlxyXG4gICAgICAgIDwvYnV0dG9uPlxyXG4gICAgICA8L2Rpdj5cclxuICAgICk7XHJcbiAgfTtcclxuICAvLyB1c2VFZmZlY3QoKCkgPT4ge1xyXG4gIC8vICAgY29uc3Qgcm9sZUlkRnJvbUNvb2tpZXMgPSBjb29raWVzLnJvbGVfaWQ7XHJcbiAgLy8gICBpZiAocm9sZUlkRnJvbUNvb2tpZXMgIT09IDEpIHtcclxuICAvLyAgICAgcm91dGVyLnB1c2goJy91bmF1dGhvcml6ZWQnKTsgLy8gQXNzdW1pbmcgeW91J3JlIHVzaW5nIE5leHQuanMgcm91dGVyXHJcbiAgLy8gICB9XHJcbiAgLy8gfSwgW10pO1xyXG5cclxuICBjb25zdCBoYW5kbGVTZWxlY3REZXBhcnRtZW50ID0gKGUpID0+IHtcclxuICAgIGlmKGUudGFyZ2V0LnZhbHVlID09IDEpe1xyXG4gICAgICBpZihlLnRhcmdldC5jaGVja2VkKXtcclxuICAgICAgICBzZXRTYWxlc0RlcGFydG1lbnQodHJ1ZSk7XHJcbiAgICAgICAgc2V0UHJvY3VyZW1lbnREZXBhcnRtZW50KGZhbHNlKTtcclxuICAgICAgICBzZXRGaW5hbmNpYWxEZXBhcnRtZW50KGZhbHNlKTtcclxuICAgICAgICBzZXRUZWNobmljYWxEZXBhcnRtZW50KGZhbHNlKTtcclxuICAgICAgfSBlbHNlIHtcclxuICAgICAgICBzZXRTYWxlc0RlcGFydG1lbnQoZmFsc2UpO1xyXG4gICAgICAgIHNldFByb2N1cmVtZW50RGVwYXJ0bWVudChmYWxzZSk7XHJcbiAgICAgICAgc2V0RmluYW5jaWFsRGVwYXJ0bWVudChmYWxzZSk7XHJcbiAgICAgICAgc2V0VGVjaG5pY2FsRGVwYXJ0bWVudChmYWxzZSk7XHJcbiAgICAgIH1cclxuICAgIH0gZWxzZSBpZihlLnRhcmdldC52YWx1ZSA9PSAyKXtcclxuICAgICAgaWYoZS50YXJnZXQuY2hlY2tlZCl7XHJcbiAgICAgICAgc2V0U2FsZXNEZXBhcnRtZW50KGZhbHNlKTtcclxuICAgICAgICBzZXRQcm9jdXJlbWVudERlcGFydG1lbnQodHJ1ZSk7XHJcbiAgICAgICAgc2V0RmluYW5jaWFsRGVwYXJ0bWVudChmYWxzZSk7XHJcbiAgICAgICAgc2V0VGVjaG5pY2FsRGVwYXJ0bWVudChmYWxzZSk7XHJcbiAgICAgIH0gZWxzZSB7XHJcbiAgICAgICAgc2V0U2FsZXNEZXBhcnRtZW50KGZhbHNlKTtcclxuICAgICAgICBzZXRQcm9jdXJlbWVudERlcGFydG1lbnQoZmFsc2UpO1xyXG4gICAgICAgIHNldEZpbmFuY2lhbERlcGFydG1lbnQoZmFsc2UpO1xyXG4gICAgICAgIHNldFRlY2huaWNhbERlcGFydG1lbnQoZmFsc2UpO1xyXG4gICAgICB9XHJcbiAgICB9IGVsc2UgaWYoZS50YXJnZXQudmFsdWUgPT0gMyl7XHJcbiAgICAgIGlmKGUudGFyZ2V0LmNoZWNrZWQpe1xyXG4gICAgICAgIHNldFNhbGVzRGVwYXJ0bWVudChmYWxzZSk7XHJcbiAgICAgICAgc2V0UHJvY3VyZW1lbnREZXBhcnRtZW50KGZhbHNlKTtcclxuICAgICAgICBzZXRGaW5hbmNpYWxEZXBhcnRtZW50KHRydWUpO1xyXG4gICAgICAgIHNldFRlY2huaWNhbERlcGFydG1lbnQoZmFsc2UpO1xyXG4gICAgICB9IGVsc2Uge1xyXG4gICAgICAgIHNldFNhbGVzRGVwYXJ0bWVudChmYWxzZSk7XHJcbiAgICAgICAgc2V0UHJvY3VyZW1lbnREZXBhcnRtZW50KGZhbHNlKTtcclxuICAgICAgICBzZXRGaW5hbmNpYWxEZXBhcnRtZW50KGZhbHNlKTtcclxuICAgICAgICBzZXRUZWNobmljYWxEZXBhcnRtZW50KGZhbHNlKTtcclxuICAgICAgfVxyXG4gICAgfSBlbHNlIGlmKGUudGFyZ2V0LnZhbHVlID09IDUpe1xyXG4gICAgICBpZihlLnRhcmdldC5jaGVja2VkKXtcclxuICAgICAgICBzZXRTYWxlc0RlcGFydG1lbnQoZmFsc2UpO1xyXG4gICAgICAgIHNldFByb2N1cmVtZW50RGVwYXJ0bWVudChmYWxzZSk7XHJcbiAgICAgICAgc2V0RmluYW5jaWFsRGVwYXJ0bWVudChmYWxzZSk7XHJcbiAgICAgICAgc2V0VGVjaG5pY2FsRGVwYXJ0bWVudCh0cnVlKTtcclxuICAgICAgfSBlbHNlIHtcclxuICAgICAgICBzZXRTYWxlc0RlcGFydG1lbnQoZmFsc2UpO1xyXG4gICAgICAgIHNldFByb2N1cmVtZW50RGVwYXJ0bWVudChmYWxzZSk7XHJcbiAgICAgICAgc2V0RmluYW5jaWFsRGVwYXJ0bWVudChmYWxzZSk7XHJcbiAgICAgICAgc2V0VGVjaG5pY2FsRGVwYXJ0bWVudCh0cnVlKTtcclxuICAgICAgfVxyXG4gICAgfSBlbHNlIHtcclxuICAgICAgc2V0U2FsZXNEZXBhcnRtZW50KGZhbHNlKTtcclxuICAgICAgc2V0UHJvY3VyZW1lbnREZXBhcnRtZW50KGZhbHNlKTtcclxuICAgICAgc2V0RmluYW5jaWFsRGVwYXJ0bWVudChmYWxzZSk7XHJcbiAgICAgIHNldFRlY2huaWNhbERlcGFydG1lbnQoZmFsc2UpO1xyXG4gICAgfVxyXG4gIH1cclxuXHJcbiAgZnVuY3Rpb24gaGFuZGxlU3VibWl0KCkge1xyXG4gICAgc2V0SXNFZGl0KGZhbHNlKTtcclxuICAgIGlmIChlbWFpbCAmJiByb2xlKSB7XHJcbiAgICAgIGxldCBzZXJ2ZXJBZGRyZXNzID0gYXBpQ29uZmlnLnNlcnZlckFkZHJlc3M7XHJcbiAgICAgIGlmIChpc0VkaXQpIHtcclxuICAgICAgICBmZXRjaChgJHtzZXJ2ZXJBZGRyZXNzfXVzZXJzL3VwZGF0ZS11c2VyLyR7dXNlcklkfWAsIHtcclxuICAgICAgICAgIG1ldGhvZDogXCJQVVRcIixcclxuICAgICAgICAgIGhlYWRlcnM6IHtcclxuICAgICAgICAgICAgQXV0aG9yaXphdGlvbjogYEJlYXJlciAke3VzZXIudG9rZW59YCxcclxuICAgICAgICAgICAgQWNjZXB0OiBcImFwcGxpY2F0aW9uL2pzb25cIixcclxuICAgICAgICAgICAgXCJDb250ZW50LVR5cGVcIjogXCJhcHBsaWNhdGlvbi9qc29uXCIsXHJcbiAgICAgICAgICB9LFxyXG4gICAgICAgICAgYm9keTogSlNPTi5zdHJpbmdpZnkoe1xyXG4gICAgICAgICAgICByb2xlX2lkOiByb2xlLFxyXG4gICAgICAgICAgICBlbWFpbDogZW1haWw/LnRyaW0oKSxcclxuICAgICAgICAgICAgdXNlcjoge1xyXG4gICAgICAgICAgICAgIHVzZXJuYW1lOiB1c2VyLm5hbWUsXHJcbiAgICAgICAgICAgICAgdXNlcmVtYWlsOiB1c2VyLmVtYWlsLFxyXG4gICAgICAgICAgICB9LFxyXG4gICAgICAgICAgICBkZXBhcnRtZW50X2lkOiBkZXBhcnRtZW50XHJcbiAgICAgICAgICB9KSxcclxuICAgICAgICB9KVxyXG4gICAgICAgICAgLnRoZW4oKHJlcykgPT4ge1xyXG4gICAgICAgICAgICBpZiAocmVzLnN0YXR1cyA9PT0gNDAwIHx8IHJlcy5zdGF0dXMgPT09IDQwMSkge1xyXG4gICAgICAgICAgICAgIHNldENvbW1vbkVycm9yKFwiSW52YWxpZCBUb2tlblwiKTtcclxuICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICBpZiAocmVzLnN0YXR1cyA9PT0gMjAwIHx8IHJlcy5zdGF0dXMgPT0gNDA5KSB7XHJcbiAgICAgICAgICAgICAgcmV0dXJuIHJlcy5qc29uKCk7XHJcbiAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgdGhyb3cgbmV3IEVycm9yKFwiRmFpbGVkIHRvIGZldGNoIGRhdGFcIik7XHJcbiAgICAgICAgICB9KVxyXG4gICAgICAgICAgLnRoZW4oKGpzb24pID0+IHtcclxuICAgICAgICAgICAgY29uc3QgbmV3SXRlbSA9IHtcclxuICAgICAgICAgICAgICBlbWFpbDoganNvblswXS5lbWFpbCxcclxuICAgICAgICAgICAgICByb2xlOiBqc29uWzBdLnJvbGVfaWQsXHJcbiAgICAgICAgICAgICAgdXNlcklkOiBqc29uWzBdLnVzZXJfaWQsXHJcbiAgICAgICAgICAgICAgZGVwYXJ0bWVudDoganNvblswXS5kZXBhcnRtZW50X2lkXHJcbiAgICAgICAgICAgIH07XHJcbiAgICAgICAgICAgIHNldFJvd0RhdGEoWy4uLnJvd0RhdGEsIG5ld0l0ZW1dKTtcclxuICAgICAgICAgICAgc2V0RW1haWwoXCJcIik7XHJcbiAgICAgICAgICAgIHNldFJvbGUoXCJcIik7XHJcbiAgICAgICAgICAgIHNldERlcGFydG1lbnQoXCJcIik7XHJcbiAgICAgICAgICAgIHRvYXN0LnN1Y2Nlc3MoXCJVc2VyIHN1Y2Nlc3NmdWxseSB1cGRhdGVkXCIsIHtcclxuICAgICAgICAgICAgICBwb3NpdGlvbjogXCJ0b3AtcmlnaHRcIixcclxuICAgICAgICAgICAgfSk7XHJcbiAgICAgICAgICB9KVxyXG4gICAgICAgICAgLmNhdGNoKChlcnJvcikgPT4ge1xyXG4gICAgICAgICAgICAvLyB0b2FzdC5lcnJvcihlcnJvci5zdGF0dXNUZXh0LCB7XHJcbiAgICAgICAgICAgIC8vICAgcG9zaXRpb246IFwidG9wLXJpZ2h0XCIsXHJcbiAgICAgICAgICAgIC8vIH0pO1xyXG4gICAgICAgICAgfSk7XHJcbiAgICAgIH0gZWxzZSB7XHJcbiAgICAgICAgZmV0Y2goYCR7c2VydmVyQWRkcmVzc311c2Vycy9hZGQtdXNlcmAsIHtcclxuICAgICAgICAgIG1ldGhvZDogXCJQT1NUXCIsXHJcbiAgICAgICAgICBoZWFkZXJzOiB7XHJcbiAgICAgICAgICAgIEF1dGhvcml6YXRpb246IGBCZWFyZXIgJHt1c2VyLnRva2VufWAsXHJcbiAgICAgICAgICAgIEFjY2VwdDogXCJhcHBsaWNhdGlvbi9qc29uXCIsXHJcbiAgICAgICAgICAgIFwiQ29udGVudC1UeXBlXCI6IFwiYXBwbGljYXRpb24vanNvblwiLFxyXG4gICAgICAgICAgfSxcclxuICAgICAgICAgIGJvZHk6IEpTT04uc3RyaW5naWZ5KHtcclxuICAgICAgICAgICAgcm9sZV9pZDogcm9sZSxcclxuICAgICAgICAgICAgZGVwYXJ0bWVudF9pZDogZGVwYXJ0bWVudCxcclxuICAgICAgICAgICAgZW1haWw6IGVtYWlsPy50cmltKCksXHJcbiAgICAgICAgICAgIHVzZXI6IHtcclxuICAgICAgICAgICAgICB1c2VybmFtZTogdXNlci5uYW1lLFxyXG4gICAgICAgICAgICAgIHVzZXJlbWFpbDogdXNlci5lbWFpbCxcclxuICAgICAgICAgICAgfSxcclxuICAgICAgICAgIH0pLFxyXG4gICAgICAgIH0pXHJcbiAgICAgICAgICAudGhlbigocmVzKSA9PiB7XHJcbiAgICAgICAgICAgIGlmIChyZXMuc3RhdHVzID09PSA0MDAgfHwgcmVzLnN0YXR1cyA9PT0gNDAxKSB7XHJcbiAgICAgICAgICAgICAgc2V0Q29tbW9uRXJyb3IoXCJJbnZhbGlkIFRva2VuXCIpO1xyXG4gICAgICAgICAgICB9XHJcbiAgICAgICAgICAgIGlmIChyZXMuc3RhdHVzID09PSAyMDAgfHwgcmVzLnN0YXR1cyA9PSA0MDkpIHtcclxuICAgICAgICAgICAgICByZXR1cm4gcmVzLmpzb24oKTtcclxuICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICB0aHJvdyBuZXcgRXJyb3IoXCJGYWlsZWQgdG8gZmV0Y2ggZGF0YVwiKTtcclxuICAgICAgICAgIH0pXHJcbiAgICAgICAgICAudGhlbigoanNvbikgPT4ge1xyXG4gICAgICAgICAgICBpZiAoanNvbi5kYXRhID09IFwiZXhpc3RzXCIpIHtcclxuICAgICAgICAgICAgICB0b2FzdC5lcnJvcihcIlVzZXIgaXMgYWxyZWFkeSBleGlzdHMuXCIsIHtcclxuICAgICAgICAgICAgICAgIHBvc2l0aW9uOiBcInRvcC1yaWdodFwiLFxyXG4gICAgICAgICAgICAgIH0pO1xyXG4gICAgICAgICAgICAgIHJldHVybjtcclxuICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICBpZiAoanNvbi5sZW5ndGggPiAwKSB7XHJcbiAgICAgICAgICAgICAgY29uc3QgbmV3SXRlbSA9IHtcclxuICAgICAgICAgICAgICAgIGVtYWlsOiBlbWFpbCxcclxuICAgICAgICAgICAgICAgIHJvbGU6IHJvbGUsXHJcbiAgICAgICAgICAgICAgICB1c2VySWQ6IGpzb25bMF0uaWQsXHJcbiAgICAgICAgICAgICAgICBkZXBhcnRtZW50OiBkZXBhcnRtZW50LFxyXG4gICAgICAgICAgICAgIH07XHJcbiAgICAgICAgICAgICAgc2V0Um93RGF0YShbLi4ucm93RGF0YSwgbmV3SXRlbV0pO1xyXG4gICAgICAgICAgICAgIHNldEVtYWlsKFwiXCIpO1xyXG4gICAgICAgICAgICAgIHRvYXN0LnN1Y2Nlc3MoXCJVc2VyIHN1Y2Nlc3NmdWxseSBjcmVhdGVkXCIsIHtcclxuICAgICAgICAgICAgICAgIHBvc2l0aW9uOiBcInRvcC1yaWdodFwiLFxyXG4gICAgICAgICAgICAgIH0pO1xyXG4gICAgICAgICAgICB9XHJcbiAgICAgICAgICB9KVxyXG4gICAgICAgICAgLmNhdGNoKChlcnJvcikgPT4ge1xyXG4gICAgICAgICAgICAvLyBjb25zb2xlLmxvZyhlcnJvcik7XHJcbiAgICAgICAgICAgIC8vIHRvYXN0LmVycm9yKGVycm9yLnN0YXR1c1RleHQsIHtcclxuICAgICAgICAgICAgLy8gICBwb3NpdGlvbjogXCJ0b3AtcmlnaHRcIixcclxuICAgICAgICAgICAgLy8gfSk7XHJcbiAgICAgICAgICB9KTtcclxuICAgICAgfVxyXG4gICAgfSBlbHNlIHtcclxuICAgICAgc2V0VmFsaWRFbWFpbCh0cnVlKTtcclxuICAgICAgc2V0VmFsaWRSb2xlKHRydWUpO1xyXG4gICAgICBzZXRWYWxpZERlcGFydG1lbnQodHJ1ZSk7XHJcbiAgICB9XHJcbiAgfVxyXG5cclxuICBmdW5jdGlvbiBoYW5kbGVDYW5jZWwoKSB7XHJcbiAgICBzZXRJc0VkaXQoZmFsc2UpO1xyXG4gICAgaWYoaXNFZGl0KXtcclxuICAgICAgY29uc3QgbmV3SXRlbSA9IHtcclxuICAgICAgICBlbWFpbDogZWRpdERhdGEuZW1haWwsXHJcbiAgICAgICAgcm9sZTogZWRpdERhdGEucm9sZSxcclxuICAgICAgICB1c2VySWQ6IGVkaXREYXRhLnVzZXJJZCxcclxuICAgICAgICBkZXBhcnRtZW50OiBlZGl0RGF0YS5kZXBhcnRtZW50XHJcbiAgICAgIH07XHJcbiAgICAgIHNldFJvd0RhdGEoWy4uLnJvd0RhdGEsIG5ld0l0ZW1dKTtcclxuXHJcbiAgICB9XHJcbiAgICBzZXRFbWFpbChcIlwiKTtcclxuICAgIHNldFJvbGUoXCJcIik7XHJcbiAgICBzZXREZXBhcnRtZW50KFwiXCIpO1xyXG4gICAgc2V0RWRpdERhdGEoe30pXHJcbiAgICBzZXRWYWxpZERlcGFydG1lbnQoZmFsc2UpO1xyXG4gICAgc2V0VmFsaWRSb2xlKGZhbHNlKTtcclxuICAgIHNldFZhbGlkRW1haWwoZmFsc2UpO1xyXG4gIH1cclxuXHJcbiAgY29uc3QgaGFuZGxlVXNlclZhbGlkYXRpb24gPSAoKSA9PiB7XHJcbiAgICBpZiAoZW1haWwgJiYgZW1haWxSZWdleC50ZXN0KGVtYWlsKSkge1xyXG4gICAgICBzZXRWYWxpZEVtYWlsKGZhbHNlKTtcclxuICAgIH0gZWxzZSB7XHJcbiAgICAgIHNldFZhbGlkRW1haWwodHJ1ZSk7XHJcbiAgICB9XHJcblxyXG4gICAgaWYgKHZhbGlkUm9sZSkge1xyXG4gICAgICBzZXRWYWxpZFJvbGUodHJ1ZSk7XHJcbiAgICB9IGVsc2Uge1xyXG4gICAgICBzZXRWYWxpZFJvbGUoZmFsc2UpO1xyXG4gICAgfVxyXG5cclxuICAgIGlmICh2YWxpZERlcGFydG1lbnQpIHtcclxuICAgICAgc2V0VmFsaWREZXBhcnRtZW50KHRydWUpO1xyXG4gICAgfSBlbHNlIHtcclxuICAgICAgc2V0VmFsaWREZXBhcnRtZW50KGZhbHNlKTtcclxuICAgIH1cclxuXHJcbiAgICByZXR1cm4gdHJ1ZTtcclxuICB9O1xyXG5cclxuICBmdW5jdGlvbiBnZXREYXRhKCkge1xyXG4gICAgbGV0IHNlcnZlckFkZHJlc3MgPSBhcGlDb25maWcuc2VydmVyQWRkcmVzcztcclxuXHJcbiAgICByZXR1cm4gZmV0Y2goYCR7c2VydmVyQWRkcmVzc311c2Vycy9nZXQtdXNlcnNgLCB7XHJcbiAgICAgIG1ldGhvZDogXCJHRVRcIixcclxuICAgICAgaGVhZGVyczoge1xyXG4gICAgICAgIEFjY2VwdDogXCJhcHBsaWNhdGlvbi9qc29uXCIsXHJcbiAgICAgICAgXCJDb250ZW50LVR5cGVcIjogXCJhcHBsaWNhdGlvbi9qc29uXCIsXHJcbiAgICAgICAgQXV0aG9yaXphdGlvbjogYEJlYXJlciAke3VzZXIudG9rZW59YCxcclxuICAgICAgfSxcclxuICAgIH0pXHJcbiAgICAgIC50aGVuKChyZXMpID0+IHtcclxuICAgICAgICBpZiAocmVzLnN0YXR1cyA9PT0gNDAwIHx8IHJlcy5zdGF0dXMgPT09IDQwMSkge1xyXG4gICAgICAgICAgc2V0Q29tbW9uRXJyb3IoXCJJbnZhbGlkIFRva2VuXCIpO1xyXG4gICAgICAgIH1cclxuICAgICAgICBpZiAocmVzLnN0YXR1cyA9PT0gMjAwKSB7XHJcbiAgICAgICAgICByZXR1cm4gcmVzLmpzb24oKTtcclxuICAgICAgICB9XHJcbiAgICAgICAgdGhyb3cgbmV3IEVycm9yKFwiRmFpbGVkIHRvIGZldGNoIGRhdGFcIik7XHJcbiAgICAgIH0pXHJcbiAgICAgIC5jYXRjaCgoZXJyb3IpID0+IHtcclxuICAgICAgICB0b2FzdC5lcnJvcihcIkZhaWxlZCB0byBmZXRjaCBkYXRhXCIsIGVycm9yLm1lc3NhZ2UsIHtcclxuICAgICAgICAgIHBvc2l0aW9uOiBcInRvcC1yaWdodFwiLFxyXG4gICAgICAgIH0pO1xyXG4gICAgICB9KTtcclxuICB9XHJcblxyXG4gIHVzZUVmZmVjdCgoKSA9PiB7XHJcbiAgICBzZXRJc0xvYWRpbmcoZmFsc2UpO1xyXG4gICAgaWYodHlwZW9mIGRvY3VtZW50ICE9PVwidW5kZWZpbmVkXCIpe1xyXG4gICAgICBkb2N1bWVudC50aXRsZT1cIlVzZXJzXCJcclxuICAgIH1cclxuICAgIGlmICh0eXBlb2Ygd2luZG93ICE9PSBcInVuZGVmaW5lZFwiKSB7XHJcbiAgICAgIGxvY2FsU3RvcmFnZS5yZW1vdmVJdGVtKFwic3VwcGxpZXJfaWRcIik7XHJcbiAgICB9XHJcblxyXG4gICAgZ2V0RGF0YSgpLnRoZW4oKGRhdGEpID0+IHtcclxuICAgICAgY29uc3QgZm9ybWF0dGVkRGF0YSA9IGRhdGE/Lm1hcCgocm93KSA9PiAoe1xyXG4gICAgICAgIGVtYWlsOiByb3cuZW1haWwsXHJcbiAgICAgICAgcm9sZTogcm93LnJvbGVfaWQsXHJcbiAgICAgICAgdXNlcklkOiByb3cudXNlcl9pZCxcclxuICAgICAgICBkZXBhcnRtZW50IDogcm93LmRlcGFydG1lbnRfaWRcclxuICAgICAgfSkpO1xyXG4gICAgICBzZXRSb3dEYXRhKGZvcm1hdHRlZERhdGEpO1xyXG4gICAgfSk7XHJcbiAgfSwgW10pO1xyXG5cclxuICBjb25zdCBoYW5kbGVQYWdlU2l6ZUNoYW5nZSA9IChldmVudCkgPT4ge1xyXG4gICAgY29uc3QgbmV3UGFnZVNpemUgPSBwYXJzZUludChldmVudC50YXJnZXQudmFsdWUsIDE1KTtcclxuICAgIHNldFBhZ2VTaXplKG5ld1BhZ2VTaXplKTtcclxuICAgIGdyaWRSZWYuY3VycmVudC5hcGkucGFnaW5hdGlvblNldFBhZ2VTaXplKG5ld1BhZ2VTaXplKTtcclxuICB9O1xyXG5cclxuICBjb25zdCBkZWZhdWx0Q29sRGVmID0gdXNlTWVtbygoKSA9PiAoe1xyXG4gICAgc29ydGFibGU6IHRydWUsXHJcbiAgICBmaWx0ZXI6IGZhbHNlLFxyXG4gICAgcmVzaXphYmxlOiB0cnVlLFxyXG4gICAgZmxleDogMSxcclxuICAgIHN1cHByZXNzTWVudTogZmFsc2UsXHJcbiAgfSkpO1xyXG5cclxuICBjb25zdCBjb2x1bW5EZWZzID0gW1xyXG4gICAge1xyXG4gICAgICBoZWFkZXJOYW1lOiBcIkVtYWlsXCIsXHJcbiAgICAgIGZpZWxkOiBcImVtYWlsXCIsXHJcbiAgICAgIHN1cHByZXNzTWVudTogdHJ1ZSxcclxuICAgICAgc3VwcHJlc3NTaXplVG9GaXQ6IHRydWUsXHJcbiAgICAgIGZsZXg6IFwiNiVcIixcclxuICAgIH0sXHJcbiAgICB7XHJcbiAgICAgIGhlYWRlck5hbWU6IFwiUm9sZVwiLFxyXG4gICAgICBmaWVsZDogXCJyb2xlXCIsXHJcbiAgICAgIGNlbGxSZW5kZXJlcjogcm9sZVJlbmRlcmVyLFxyXG4gICAgICBmbGV4OiBcIjQlXCIsXHJcbiAgICAgIGhpZGU6IGZhbHNlLFxyXG4gICAgfSxcclxuICAgIHtcclxuICAgICAgaGVhZGVyTmFtZTogXCJEZXBhcnRtZW50XCIsXHJcbiAgICAgIGZpZWxkOiBcImRlcGFydG1lbnRcIixcclxuICAgICAgY2VsbFJlbmRlcmVyOiBkZXBhcnRtZW50UmVuZGVyZXIsXHJcbiAgICAgIGZsZXg6IFwiNCVcIixcclxuICAgICAgaGlkZTogZmFsc2UsXHJcbiAgICB9LFxyXG4gICAge1xyXG4gICAgICBmaWVsZDogXCJcIixcclxuICAgICAgY2VsbFJlbmRlcmVyOiBJY29uc1JlbmRlcmVyLFxyXG4gICAgICBmbGV4OiBcIjQlXCIsXHJcbiAgICAgIGNlbGxTdHlsZTogKCkgPT4gKHt9KSxcclxuICAgIH0sXHJcbiAgICB7XHJcbiAgICAgIGZpZWxkOiBcInVzZXJJZFwiLFxyXG4gICAgICAvL2NlbGxSZW5kZXJlcjogSWNvbnNSZW5kZXJlcixcclxuICAgICAgZmxleDogXCI0JVwiLFxyXG4gICAgICBjZWxsU3R5bGU6ICgpID0+ICh7fSksXHJcbiAgICAgIGhpZGU6IHRydWUsXHJcbiAgICAgIHN1cHByZXNzRmlsdGVyc1Rvb2xQYW5lbDogdHJ1ZSxcclxuICAgIH0sXHJcbiAgXTtcclxuXHJcbiAgY29uc3Qgb25GaWx0ZXJUZXh0Qm94Q2hhbmdlZCA9IHVzZUNhbGxiYWNrKCgpID0+IHtcclxuICAgIGdyaWRSZWYuY3VycmVudC5hcGkuc2V0UXVpY2tGaWx0ZXIoXHJcbiAgICAgIGRvY3VtZW50LmdldEVsZW1lbnRCeUlkKFwiZmlsdGVyLXRleHQtYm94XCIpLnZhbHVlXHJcbiAgICApO1xyXG4gIH0sIFtdKTtcclxuXHJcbiAgY29uc3QgaGFuZGxlR3JpZFJlYWR5ID0gKHBhcmFtcykgPT4ge1xyXG4gICAgcGFyYW1zLmFwaS5zZXRDb2x1bW5EZWZzKGNvbHVtbkRlZnMpO1xyXG4gIH07XHJcblxyXG5cclxuICByZXR1cm4gKFxyXG4gICAgPD5cclxuICAgICAgPFRvYXN0Q29udGFpbmVyIC8+XHJcbiAgICAgIDxMYXlvdXQgdXNlckRhdGE9e3VzZXJEYXRhfT5cclxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggZmxleC1yb3cganVzdGlmeS1iZXR3ZWVuIHctWzk1JV0gZ2FwLTggcGUtMVwiPlxyXG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ3LVs1MCVdXCI+XHJcbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBmbGV4LXJvdyBtZDpmbGV4LWNvbCBsZzpmbGV4LXJvdyBpdGVtcy1lbmRcIj5cclxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggZ2FwLTYgdy1mdWxsIGp1c3RpZnktZW5kXCI+XHJcbiAgICAgICAgICAgICAgICA8bGFiZWwgY2xhc3NOYW1lPVwicmVsYXRpdmUgYmxvY2sgdy1bNDd2aF0gdGV4dC1ncmF5LTQwMCBmb2N1cy13aXRoaW46dGV4dC1ncmF5LTYwMCBtdC0wIHB0LTFcIj5cclxuICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwiYWJzb2x1dGUgcHgtNCBweS0xIHRleHQtYmxhY2tcIj5cclxuICAgICAgICAgICAgICAgICAgICA8Rm9udEF3ZXNvbWVJY29uIGljb249e2ZhU2VhcmNofSBjbGFzc05hbWU9XCJmdy1ib2xkXCIgLz5cclxuICAgICAgICAgICAgICAgICAgPC9zcGFuPlxyXG4gICAgICAgICAgICAgICAgICA8aW5wdXRcclxuICAgICAgICAgICAgICAgICAgICB0eXBlPVwidGV4dFwiXHJcbiAgICAgICAgICAgICAgICAgICAgaWQ9XCJmaWx0ZXItdGV4dC1ib3hcIlxyXG4gICAgICAgICAgICAgICAgICAgIHBsYWNlaG9sZGVyPVwiU2VhcmNoXCJcclxuICAgICAgICAgICAgICAgICAgICBvbklucHV0PXtvbkZpbHRlclRleHRCb3hDaGFuZ2VkfVxyXG4gICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImJsb2NrIHctZnVsbCBweC00IHBsLTEwIHRleHQtZ3JheS01MDAgcGxhY2Vob2xkZXItZ3JheS00MDAgc2VhcmNoYmFyIGJvcmRlciByb3VuZGVkLWxnIGFwcGVhcmFuY2Utbm9uZSBmb3JtLWlucHV0IGZvY3VzOm91dGxpbmUtbm9uZVwiXHJcbiAgICAgICAgICAgICAgICAgIC8+XHJcbiAgICAgICAgICAgICAgICA8L2xhYmVsPlxyXG4gICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJteS01XCI+XHJcbiAgICAgICAgICAgICAgPGRpdlxyXG4gICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwicmVsYXRpdmUgYWctdGhlbWUtYWxwaW5lXCJcclxuICAgICAgICAgICAgICAgIHN0eWxlPXt7IGhlaWdodDogXCJjYWxjKDEwMHZoIC0gMTUwcHgpXCIgfX1cclxuICAgICAgICAgICAgICA+XHJcbiAgICAgICAgICAgICAgICA8QWdHcmlkUmVhY3RcclxuICAgICAgICAgICAgICAgICAgcm93RGF0YT17cm93RGF0YX1cclxuICAgICAgICAgICAgICAgICAgcmVmPXtncmlkUmVmfVxyXG4gICAgICAgICAgICAgICAgICBjb2x1bW5EZWZzPXtjb2x1bW5EZWZzfVxyXG4gICAgICAgICAgICAgICAgICBkZWZhdWx0Q29sRGVmPXtkZWZhdWx0Q29sRGVmfVxyXG4gICAgICAgICAgICAgICAgICBzdXBwcmVzc1Jvd0NsaWNrU2VsZWN0aW9uXHJcbiAgICAgICAgICAgICAgICAgIHJvd1NlbGVjdGlvbj1cIm11bHRpcGxlXCJcclxuICAgICAgICAgICAgICAgICAgcGFnaW5hdGlvbj17dHJ1ZX1cclxuICAgICAgICAgICAgICAgICAgcGFnaW5hdGlvblBhZ2VTaXplPXtwYWdlU2l6ZX1cclxuICAgICAgICAgICAgICAgICAgb25QYWdlU2l6ZUNoYW5nZWQ9e2hhbmRsZVBhZ2VTaXplQ2hhbmdlfVxyXG4gICAgICAgICAgICAgICAgICB0b29sdGlwU2hvd0RlbGF5PXswfVxyXG4gICAgICAgICAgICAgICAgICB0b29sdGlwSGlkZURlbGF5PXsxMDAwfVxyXG4gICAgICAgICAgICAgICAgICBvbkdyaWRSZWFkeT17aGFuZGxlR3JpZFJlYWR5fVxyXG4gICAgICAgICAgICAgICAgLz5cclxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBqdXN0aWZ5LXN0YXJ0IG10LTIgcGFnaW5hdGlvbi1zdHlsZVwiPlxyXG4gICAgICAgICAgICAgICAgICA8bGFiZWxcclxuICAgICAgICAgICAgICAgICAgICBodG1sRm9yPVwicGFnZS1zaXplLXNlbGVjdCBwYWdpbmF0aW9uXCJcclxuICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJpbnB1dHNcIlxyXG4gICAgICAgICAgICAgICAgICA+XHJcbiAgICAgICAgICAgICAgICAgICAgU2hvd3tcIiBcIn1cclxuICAgICAgICAgICAgICAgICAgICA8c2VsZWN0XHJcbiAgICAgICAgICAgICAgICAgICAgICBpZD1cInBhZ2Utc2l6ZS1zZWxlY3RcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgb25DaGFuZ2U9e2hhbmRsZVBhZ2VTaXplQ2hhbmdlfVxyXG4gICAgICAgICAgICAgICAgICAgICAgdmFsdWU9e3BhZ2VTaXplfVxyXG4gICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiZm9jdXM6b3V0bGluZS1ub25lXCJcclxuICAgICAgICAgICAgICAgICAgICA+XHJcbiAgICAgICAgICAgICAgICAgICAgICA8b3B0aW9uIHZhbHVlPXsxMH0+MTA8L29wdGlvbj5cclxuICAgICAgICAgICAgICAgICAgICAgIDxvcHRpb24gdmFsdWU9ezE1fT4xNTwvb3B0aW9uPlxyXG4gICAgICAgICAgICAgICAgICAgICAgPG9wdGlvbiB2YWx1ZT17MjV9PjI1PC9vcHRpb24+XHJcbiAgICAgICAgICAgICAgICAgICAgICA8b3B0aW9uIHZhbHVlPXs1MH0+NTA8L29wdGlvbj5cclxuICAgICAgICAgICAgICAgICAgICAgIDxvcHRpb24gdmFsdWU9ezEwMH0+MTAwPC9vcHRpb24+XHJcbiAgICAgICAgICAgICAgICAgICAgPC9zZWxlY3Q+e1wiIFwifVxyXG4gICAgICAgICAgICAgICAgICAgIGVudHJpZXNcclxuICAgICAgICAgICAgICAgICAgPC9sYWJlbD5cclxuICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ3LVs1MCVdIG10LTE0XCI+XHJcbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwicmVsYXRpdmUgcGFuZWwtY29udGFpbmVyIGNvbnRlbnRzZWN0aW9uYmcgcm91bmRlZC1sZyB3LWZ1bGwgMnhsOnctW2NhbGMoMTAwJS03MHB4KV0gcC00IHBiLTAgc2hhZG93LWxnXCI+XHJcbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJtLTMgbWItMFwiPlxyXG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJtYi0zXCI+XHJcbiAgICAgICAgICAgICAgICAgIDxoNCBjbGFzc05hbWU9XCJmb3JtdGl0bGUgcGItMVwiPkFzc2lnbiB1c2VyIHJvbGUgPC9oND5cclxuICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGZsZXgtY29sIG1iLTZcIj5cclxuICAgICAgICAgICAgICAgICAgPGxhYmVsIGNsYXNzTmFtZT1cImxhYmVscyBtYi0xXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgRW1haWwgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC1yZWQtNTAwXCI+Kjwvc3Bhbj5cclxuICAgICAgICAgICAgICAgICAgPC9sYWJlbD5cclxuICAgICAgICAgICAgICAgICAgPGlucHV0XHJcbiAgICAgICAgICAgICAgICAgICAgdHlwZT1cImVtYWlsXCJtYXhMZW5ndGg9ezgwfVxyXG4gICAgICAgICAgICAgICAgICAgIG5hbWU9XCJFbWFpbFwiXHJcbiAgICAgICAgICAgICAgICAgICAgdmFsdWU9e2VtYWlsfVxyXG4gICAgICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4ge1xyXG4gICAgICAgICAgICAgICAgICAgICAgc2V0RW1haWwoZS50YXJnZXQudmFsdWUpLCBoYW5kbGVVc2VyVmFsaWRhdGlvbjtcclxuICAgICAgICAgICAgICAgICAgICB9fVxyXG4gICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInctZnVsbCBweC0yIDJ4bDpweC0zIGJvcmRlciBib3JkZXItbGlnaHQtZ3JheSByb3VuZGVkLW1kIHNlYXJjaGJhclwiXHJcbiAgICAgICAgICAgICAgICAgICAgb25CbHVyPXtoYW5kbGVVc2VyVmFsaWRhdGlvbn1cclxuICAgICAgICAgICAgICAgICAgLz5cclxuICAgICAgICAgICAgICAgICAge3ZhbGlkRW1haWwgJiYgKFxyXG4gICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtcmVkLTUwMFwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgUGxlYXNlIGVudGVyIHZhbGlkIGVtYWlsIGFkZHJlc3NcclxuICAgICAgICAgICAgICAgICAgICA8L3NwYW4+XHJcbiAgICAgICAgICAgICAgICAgICl9XHJcbiAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBmbGV4LWNvbCBtYi02XCI+XHJcbiAgICAgICAgICAgICAgICAgIDxsYWJlbCBjbGFzc05hbWU9XCJtZS01IGxhYmVsc1wiPlxyXG4gICAgICAgICAgICAgICAgICAgIFJvbGUgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC1yZWQtNTAwXCI+Kjwvc3Bhbj5cclxuICAgICAgICAgICAgICAgICAgPC9sYWJlbD5cclxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IG10LTFcIj5cclxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICA8aW5wdXRcclxuICAgICAgICAgICAgICAgICAgICAgICAgaWQ9XCJhcHByb3Zlci1yYWRpb1wiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIHR5cGU9XCJyYWRpb1wiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIHZhbHVlPVwiMlwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIGNoZWNrZWQ9e3JvbGUgPT0gMn1cclxuICAgICAgICAgICAgICAgICAgICAgICAgb25DaGFuZ2U9eygpID0+IHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICBzZXRSb2xlKDIpLCBoYW5kbGVVc2VyVmFsaWRhdGlvbjtcclxuICAgICAgICAgICAgICAgICAgICAgICAgfX1cclxuICAgICAgICAgICAgICAgICAgICAgICAgb25CbHVyPXtoYW5kbGVVc2VyVmFsaWRhdGlvbn1cclxuICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwibWwtMCB3LTQgaC00IHRleHQtYmx1ZS02MDAgYmctZ3JheS0xMDAgYm9yZGVyLWxpZ2h0LWdyYXkgZm9jdXM6cmluZy1ibHVlLTUwMCBmb2N1czpyaW5nLTIgaG92ZXI6Y3Vyc29yLXBvaW50ZXJcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgLz5cclxuICAgICAgICAgICAgICAgICAgICAgIDxsYWJlbFxyXG4gICAgICAgICAgICAgICAgICAgICAgICBodG1sRm9yPVwiYXBwcm92ZXItcmFkaW9cIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJwLTAgbWwtMiBtZS01IHRleHQtYmxhY2tjb2xvciBob3ZlcjpjdXJzb3ItcG9pbnRlclwiXHJcbiAgICAgICAgICAgICAgICAgICAgICA+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIEFwcHJvdmVyXHJcbiAgICAgICAgICAgICAgICAgICAgICA8L2xhYmVsPlxyXG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxyXG5cclxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXhcIj5cclxuICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXJcIj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgPGlucHV0XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgaWQ9XCJhZG1pbi1yYWRpb1wiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgdHlwZT1cInJhZGlvXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICB2YWx1ZT1cIjFcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIGNoZWNrZWQ9e3JvbGUgPT0gMX1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICBvbkNoYW5nZT17KCkgPT4ge1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgc2V0Um9sZSgxKSwgaGFuZGxlVXNlclZhbGlkYXRpb247XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgfX1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICBvbkJsdXI9e2hhbmRsZVVzZXJWYWxpZGF0aW9ufVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cIm1sLTAgdy00IGgtNCB0ZXh0LWJsdWUtNjAwIGJnLWdyYXktMTAwIGJvcmRlci1saWdodC1ncmF5IGZvY3VzOnJpbmctYmx1ZS01MDAgZm9jdXM6cmluZy0yIGhvdmVyOmN1cnNvci1wb2ludGVyXCIgXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIC8+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDxsYWJlbFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIGh0bWxGb3I9XCJhZG1pbi1yYWRpb1wiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwicC0wIG1sLTIgbWUtNSB0ZXh0LWJsYWNrY29sb3IgaG92ZXI6Y3Vyc29yLXBvaW50ZXJcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICA+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgQWRtaW5pc3RyYXRvclxyXG4gICAgICAgICAgICAgICAgICAgICAgICA8L2xhYmVsPlxyXG4gICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4XCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDxpbnB1dFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIGlkPVwidXNlci1yYWRpb1wiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgdHlwZT1cInJhZGlvXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICB2YWx1ZT1cIjRcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIGNoZWNrZWQ9e3JvbGUgPT0gNH1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICBvbkNoYW5nZT17KCkgPT4ge1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgc2V0Um9sZSg0KSwgaGFuZGxlVXNlclZhbGlkYXRpb247XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgfX1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICBvbkJsdXI9e2hhbmRsZVVzZXJWYWxpZGF0aW9ufVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cIm1sLTAgdy00IGgtNCB0ZXh0LWJsdWUtNjAwIGJnLWdyYXktMTAwIGJvcmRlci1saWdodC1ncmF5IGZvY3VzOnJpbmctYmx1ZS01MDAgZm9jdXM6cmluZy0yIGhvdmVyOmN1cnNvci1wb2ludGVyXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgLz5cclxuICAgICAgICAgICAgICAgICAgICAgICAgPGxhYmVsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgaHRtbEZvcj1cInVzZXItcmFkaW9cIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInAtMCBtbC0yIG1lLTUgdGV4dC1ibGFja2NvbG9yIGhvdmVyOmN1cnNvci1wb2ludGVyXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIFVzZXJcclxuICAgICAgICAgICAgICAgICAgICAgICAgPC9sYWJlbD5cclxuICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICAgICAge3ZhbGlkUm9sZSAmJiAoXHJcbiAgICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC1yZWQtNTAwXCI+U2VsZWN0IHJvbGU8L3NwYW4+XHJcbiAgICAgICAgICAgICAgICAgICl9XHJcbiAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBmbGV4LWNvbCBtYi02XCI+XHJcbiAgICAgICAgICAgICAgICAgIDxsYWJlbCBjbGFzc05hbWU9XCJtZS01IGxhYmVsc1wiPlxyXG4gICAgICAgICAgICAgICAgICAgIERlcGFydG1lbnQgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC1yZWQtNTAwXCI+Kjwvc3Bhbj5cclxuICAgICAgICAgICAgICAgICAgPC9sYWJlbD5cclxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IG10LTFcIj5cclxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICA8aW5wdXRcclxuICAgICAgICAgICAgICAgICAgICAgICAgICBpZD1cImRlcGFydG1lbnQtc2FsZXNcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIHR5cGU9XCJyYWRpb1wiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgdmFsdWU9ezF9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgY2hlY2tlZD17ZGVwYXJ0bWVudCA9PSAxfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4ge1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgc2V0RGVwYXJ0bWVudCgxKTsgXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBoYW5kbGVTZWxlY3REZXBhcnRtZW50KGUpO1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIH19XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgb25CbHVyPXtoYW5kbGVVc2VyVmFsaWRhdGlvbn1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJtbC0wIHctNCBoLTQgdGV4dC1ibHVlLTYwMCBiZy1ncmF5LTEwMCBib3JkZXItbGlnaHQtZ3JheSBmb2N1czpyaW5nLWJsdWUtNTAwIGZvY3VzOnJpbmctMiBob3ZlcjpjdXJzb3ItcG9pbnRlclwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIC8+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDxsYWJlbFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIGh0bWxGb3I9XCJkZXBhcnRtZW50LXNhbGVzXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJwLTAgbWwtMiBtZS01IHRleHQtYmxhY2tjb2xvciBob3ZlcjpjdXJzb3ItcG9pbnRlclwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgID5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICBTYWxlc1xyXG4gICAgICAgICAgICAgICAgICAgICAgICA8L2xhYmVsPlxyXG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXJcIj5cclxuICAgICAgICAgICAgICAgICAgICAgIDxpbnB1dFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIGlkPVwiZGVwYXJ0bWVudC1wcm9jdXJlbWVudFwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgdHlwZT1cInJhZGlvXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICB2YWx1ZT17Mn1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICBjaGVja2VkPXtkZXBhcnRtZW50ID09IDJ9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBzZXREZXBhcnRtZW50KDIpO1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgaGFuZGxlU2VsZWN0RGVwYXJ0bWVudChlKVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIH19XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgb25CbHVyPXtoYW5kbGVVc2VyVmFsaWRhdGlvbn1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJtbC0wIHctNCBoLTQgdGV4dC1ibHVlLTYwMCBiZy1ncmF5LTEwMCBib3JkZXItbGlnaHQtZ3JheSBmb2N1czpyaW5nLWJsdWUtNTAwIGZvY3VzOnJpbmctMiBob3ZlcjpjdXJzb3ItcG9pbnRlclwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIC8+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDxsYWJlbFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIGh0bWxGb3I9XCJkZXBhcnRtZW50LXByb2N1cmVtZW50XCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJwLTAgbWwtMiBtZS01IHRleHQtYmxhY2tjb2xvciBob3ZlcjpjdXJzb3ItcG9pbnRlclwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgID5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICBQcm9jdXJlbWVudFxyXG4gICAgICAgICAgICAgICAgICAgICAgICA8L2xhYmVsPlxyXG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXJcIj5cclxuICAgICAgICAgICAgICAgICAgICAgIDxpbnB1dFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIGlkPVwiZGVwYXJ0bWVudC1maW5hbmNpYWxcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIHR5cGU9XCJyYWRpb1wiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgdmFsdWU9ezN9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgY2hlY2tlZD17ZGVwYXJ0bWVudCA9PSAzfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4ge1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgc2V0RGVwYXJ0bWVudCgzKTtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGhhbmRsZVNlbGVjdERlcGFydG1lbnQoZSlcclxuICAgICAgICAgICAgICAgICAgICAgICAgICB9fVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIG9uQmx1cj17aGFuZGxlVXNlclZhbGlkYXRpb259XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwibWwtMCB3LTQgaC00IHRleHQtYmx1ZS02MDAgYmctZ3JheS0xMDAgYm9yZGVyLWxpZ2h0LWdyYXkgZm9jdXM6cmluZy1ibHVlLTUwMCBmb2N1czpyaW5nLTIgaG92ZXI6Y3Vyc29yLXBvaW50ZXJcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAvPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICA8bGFiZWxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICBodG1sRm9yPVwiZGVwYXJ0bWVudC1maW5hbmNpYWxcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInAtMCBtbC0yIG1lLTUgdGV4dC1ibGFja2NvbG9yIGhvdmVyOmN1cnNvci1wb2ludGVyXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIEZpbmFuY2lhbFxyXG4gICAgICAgICAgICAgICAgICAgICAgICA8L2xhYmVsPlxyXG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXJcIj5cclxuICAgICAgICAgICAgICAgICAgICAgIDxpbnB1dFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIGlkPVwiZGVwYXJ0bWVudC10ZWNobmljYWxcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIHR5cGU9XCJyYWRpb1wiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgdmFsdWU9ezV9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgY2hlY2tlZD17ZGVwYXJ0bWVudCA9PSA1fVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4ge1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgc2V0RGVwYXJ0bWVudCg1KTtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGhhbmRsZVNlbGVjdERlcGFydG1lbnQoZSlcclxuICAgICAgICAgICAgICAgICAgICAgICAgICB9fVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIG9uQmx1cj17aGFuZGxlVXNlclZhbGlkYXRpb259XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwibWwtMCB3LTQgaC00IHRleHQtYmx1ZS02MDAgYmctZ3JheS0xMDAgYm9yZGVyLWxpZ2h0LWdyYXkgZm9jdXM6cmluZy1ibHVlLTUwMCBmb2N1czpyaW5nLTIgaG92ZXI6Y3Vyc29yLXBvaW50ZXJcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAvPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICA8bGFiZWxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICBodG1sRm9yPVwiZGVwYXJ0bWVudC10ZWNobmljYWxcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInAtMCBtbC0yIG1lLTUgdGV4dC1ibGFja2NvbG9yIGhvdmVyOmN1cnNvci1wb2ludGVyXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIFRlY2huaWNhbFxyXG4gICAgICAgICAgICAgICAgICAgICAgICA8L2xhYmVsPlxyXG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgICAgICAgIHsvKiA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICA8aW5wdXRcclxuICAgICAgICAgICAgICAgICAgICAgICAgICBpZD1cImRlcGFydG1lbnQtY2hlY2tib3gtM1wiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgdHlwZT1cInJhZGlvXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICB2YWx1ZT17NH1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICBjaGVja2VkPXtkZXBhcnRtZW50ID09IDR9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBzZXREZXBhcnRtZW50KDQpO1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgaGFuZGxlU2VsZWN0RGVwYXJ0bWVudChlKVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIH19XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgb25CbHVyPXtoYW5kbGVVc2VyVmFsaWRhdGlvbn1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJtbC0wIHctNCBoLTQgdGV4dC1ibHVlLTYwMCBiZy1ncmF5LTEwMCBib3JkZXItbGlnaHQtZ3JheSBmb2N1czpyaW5nLWJsdWUtNTAwIGZvY3VzOnJpbmctMlwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIC8+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDxsYWJlbFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIGh0bWxGb3I9XCJhcHByb3Zlci1yYWRpb1wiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwicC0wIG1sLTIgbWUtNSB0ZXh0LWJsYWNrY29sb3JcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICA+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgTm9uZVxyXG4gICAgICAgICAgICAgICAgICAgICAgICA8L2xhYmVsPlxyXG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PiAqL31cclxuICAgICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgICB7dmFsaWREZXBhcnRtZW50ICYmIChcclxuICAgICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LXJlZC01MDBcIj5TZWxlY3QgRGVwYXJ0ZW1lbnQ8L3NwYW4+XHJcbiAgICAgICAgICAgICAgICAgICl9XHJcbiAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBqdXN0aWZ5LWVuZCBwYi00XCI+XHJcbiAgICAgICAgICAgICAgICAgIDxidXR0b25cclxuICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJib3JkZXIgYm9yZGVyLXNraW4tcHJpbWFyeSB0ZXh0LXNraW4tcHJpbWFyeSBtZS0xMCBweS0xIHB4LTggZm9udC1tZWRpdW0gcm91bmRlZC1tZFwiXHJcbiAgICAgICAgICAgICAgICAgICAgb25DbGljaz17aGFuZGxlQ2FuY2VsfVxyXG4gICAgICAgICAgICAgICAgICA+XHJcbiAgICAgICAgICAgICAgICAgICAgQ2FuY2VsXHJcbiAgICAgICAgICAgICAgICAgIDwvYnV0dG9uPlxyXG4gICAgICAgICAgICAgICAgICA8YnV0dG9uXHJcbiAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiYm9yZGVyIGJvcmRlci1za2luLXByaW1hcnkgYmctc2tpbi1wcmltYXJ5IHRleHQtd2hpdGUgcm91bmRlZC1tZCBweS0xIHB4LTggZm9udC1tZWRpdW1cIlxyXG4gICAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9e2hhbmRsZVN1Ym1pdH1cclxuICAgICAgICAgICAgICAgICAgICBkaXNhYmxlZD17XHJcbiAgICAgICAgICAgICAgICAgICAgICBlbWFpbCAmJiBlbWFpbFJlZ2V4LnRlc3QoZW1haWwpICYmIHJvbGUgJiYgZGVwYXJ0bWVudCA/IGZhbHNlIDogdHJ1ZVxyXG4gICAgICAgICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgICAgICAgICA+XHJcbiAgICAgICAgICAgICAgICAgICAgU2F2ZVxyXG4gICAgICAgICAgICAgICAgICA8L2J1dHRvbj5cclxuICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgIDwvZGl2PlxyXG4gICAgICA8L0xheW91dD5cclxuICAgIDwvPlxyXG4gICk7XHJcbn07XHJcblxyXG5leHBvcnQgZGVmYXVsdCB1c2VycztcclxuXHJcbmV4cG9ydCBjb25zdCBnZXRTZXJ2ZXJTaWRlUHJvcHMgPSBhc3luYyAoY29udGV4dCkgPT4ge1xyXG4gIGNvbnN0IHVzZXJEYXRhID0gY29udGV4dC5yZXEuY29va2llcy51c2VyO1xyXG5cclxuICBjb25zdCB0b2tlbiA9IGNvbnRleHQucmVxLmNvb2tpZXMudG9rZW47XHJcblxyXG4gIGlmICghdG9rZW4pIHtcclxuICAgIHJldHVybiB7XHJcbiAgICAgIHJlZGlyZWN0OiB7XHJcbiAgICAgICAgZGVzdGluYXRpb246IGAvbG9naW4/cmVkaXJlY3Q9JHtlbmNvZGVVUklDb21wb25lbnQoY29udGV4dC5yZXNvbHZlZFVybCl9YCxcclxuICAgICAgICBwZXJtYW5lbnQ6IGZhbHNlLFxyXG4gICAgICB9LFxyXG4gICAgfTtcclxuICB9XHJcblxyXG4gIHRyeSB7XHJcbiAgICBjb25zdCBwYXJzZWREYXRhID0gSlNPTi5wYXJzZSh1c2VyRGF0YSk7XHJcblxyXG4gICAgaWYgKHBhcnNlZERhdGEucm9sZV9pZCAhPT0gMSkge1xyXG4gICAgICAvLyBSZWRpcmVjdCBmb3IgdW5hdXRob3JpemVkIHJvbGVzLCBidXQgc3RpbGwgcGFzcyBwYXJzZWREYXRhXHJcbiAgICAgIHJldHVybiB7XHJcbiAgICAgICAgcmVkaXJlY3Q6IHtcclxuICAgICAgICAgIGRlc3RpbmF0aW9uOiBcIi9zdXBwbGllcnNcIixcclxuICAgICAgICAgIHBlcm1hbmVudDogZmFsc2UsXHJcbiAgICAgICAgICBwcm9wczogeyB1c2VyRGF0YTogcGFyc2VkRGF0YSB9LCAvLyBQYXNzIHBhcnNlZERhdGEgYXMgcHJvcHMgaW4gdGhlIHJlZGlyZWN0XHJcbiAgICAgICAgfSxcclxuICAgICAgfTtcclxuICAgIH1cclxuXHJcbiAgICAvLyBQYXNzIHBhcnNlZERhdGEgZm9yIGF1dGhvcml6ZWQgcm9sZXMgYXMgd2VsbFxyXG4gICAgcmV0dXJuIHtcclxuICAgICAgcHJvcHM6IHsgdXNlckRhdGE6IHBhcnNlZERhdGEgfSwgLy8gUGFzcyBwYXJzZWREYXRhIGFzIHByb3BzIGluIHRoZSByZWRpcmVjdFxyXG4gICAgfTtcclxuICB9IGNhdGNoIChlcnJvcikge1xyXG4gICAgY29uc29sZS5lcnJvcihcIkVycm9yIHBhcnNpbmcgdXNlciBkYXRhIGZyb20gY29va2llOlwiLCBlcnJvcik7XHJcbiAgICByZXR1cm4ge1xyXG4gICAgICBwcm9wczogeyB1c2VyRGF0YTogbnVsbCB9LCAvLyBJbmRpY2F0ZSBlcnJvciBvciBpbnZhbGlkIGNvb2tpZVxyXG4gICAgfTtcclxuICB9XHJcbn07XHJcbiJdLCJuYW1lcyI6WyJMYXlvdXQiLCJSZWFjdCIsInVzZU1lbW8iLCJ1c2VTdGF0ZSIsInVzZVJlZiIsInVzZUNhbGxiYWNrIiwidXNlRWZmZWN0IiwiRm9udEF3ZXNvbWVJY29uIiwiZmFTZWFyY2giLCJmYVRyYXNoIiwiZmFQZW5Ub1NxdWFyZSIsIkFnR3JpZFJlYWN0IiwidXNlUm91dGVyIiwiYXBpQ29uZmlnIiwicm9sZVJlbmRlcmVyIiwiZGVwYXJ0bWVudFJlbmRlcmVyIiwiQ29va2llcyIsIlRvYXN0Q29udGFpbmVyIiwidG9hc3QiLCJ1c2VVc2VyIiwiZ2V0Q29va2llRGF0YSIsInVzZUxvYWRpbmciLCJ1c2VycyIsInVzZXJEYXRhIiwic2V0SXNMb2FkaW5nIiwicm91dGVyIiwidXNlckRldGFpbHMiLCJ1cGRhdGVUb2tlbiIsInBhZ2VTaXplIiwic2V0UGFnZVNpemUiLCJyb3dEYXRhIiwic2V0Um93RGF0YSIsImVtYWlsIiwic2V0RW1haWwiLCJyb2xlIiwic2V0Um9sZSIsImRlcGFydG1lbnQiLCJzZXREZXBhcnRtZW50IiwiZWRpdERhdGEiLCJzZXRFZGl0RGF0YSIsInZhbGlkRW1haWwiLCJzZXRWYWxpZEVtYWlsIiwidmFsaWRSb2xlIiwic2V0VmFsaWRSb2xlIiwidmFsaWREZXBhcnRtZW50Iiwic2V0VmFsaWREZXBhcnRtZW50IiwiaXNUb2tlbiIsInNldFRva2VuIiwiaXNFZGl0Iiwic2V0SXNFZGl0IiwidXNlcklkIiwic2V0VXNlcklkIiwic2FsZXNEZXBhcnRtZW50Iiwic2V0U2FsZXNEZXBhcnRtZW50IiwicHJvY3VyZW1lbnREZXBhcnRtZW50Iiwic2V0UHJvY3VyZW1lbnREZXBhcnRtZW50IiwiZmluYW5jaWFsRGVwYXJ0bWVudCIsInNldEZpbmFuY2lhbERlcGFydG1lbnQiLCJ0ZWNobmljYWxEZXBhcnRtZW50Iiwic2V0VGVjaG5pY2FsRGVwYXJ0bWVudCIsImlzQ29tbW9uRXJyb3IiLCJzZXRDb21tb25FcnJvciIsImdyaWRSZWYiLCJlbWFpbFJlZ2V4IiwidXNlciIsIkljb25zUmVuZGVyZXIiLCJwcm9wcyIsInVwZGF0ZWREYXRhIiwiaGFuZGxlRGVsZXRlIiwiZXZlbnQiLCJhbGxEYXRhIiwiZGF0YSIsInVzZXJfaWQiLCJlcnJvciIsInBvc2l0aW9uIiwic2VydmVyQWRkcmVzcyIsImZldGNoIiwibWV0aG9kIiwiaGVhZGVycyIsIkF1dGhvcml6YXRpb24iLCJ0b2tlbiIsIkFjY2VwdCIsImJvZHkiLCJKU09OIiwic3RyaW5naWZ5IiwidXNlcm5hbWUiLCJuYW1lIiwidXNlcmVtYWlsIiwidGhlbiIsInJlcyIsInN0YXR1cyIsInNldFRpbWVvdXQiLCJsb2NhbFN0b3JhZ2UiLCJyZW1vdmVJdGVtIiwicmVtb3ZlIiwicmVkaXJlY3RVcmwiLCJlbmNvZGVVUklDb21wb25lbnQiLCJ3aW5kb3ciLCJsb2NhdGlvbiIsInBhdGhuYW1lIiwibG9nb3V0SGFuZGxlciIsImluc3RhbmNlIiwianNvbiIsIkVycm9yIiwiaW5kZXgiLCJpbmRleE9mIiwic3BsaWNlIiwiYXBpIiwiYXBwbHlUcmFuc2FjdGlvbiIsInN1Y2Nlc3MiLCJtZXNzYWdlIiwiY2F0Y2giLCJoYW5kbGVFZGl0IiwiZWRpdGVkRGF0YSIsImRpdiIsImNsYXNzTmFtZSIsImJ1dHRvbiIsIm9uQ2xpY2siLCJkaXNhYmxlZCIsImljb24iLCJoYW5kbGVTZWxlY3REZXBhcnRtZW50IiwiZSIsInRhcmdldCIsInZhbHVlIiwiY2hlY2tlZCIsImhhbmRsZVN1Ym1pdCIsInJvbGVfaWQiLCJ0cmltIiwiZGVwYXJ0bWVudF9pZCIsIm5ld0l0ZW0iLCJsZW5ndGgiLCJpZCIsImhhbmRsZUNhbmNlbCIsImhhbmRsZVVzZXJWYWxpZGF0aW9uIiwidGVzdCIsImdldERhdGEiLCJkb2N1bWVudCIsInRpdGxlIiwiZm9ybWF0dGVkRGF0YSIsIm1hcCIsInJvdyIsImhhbmRsZVBhZ2VTaXplQ2hhbmdlIiwibmV3UGFnZVNpemUiLCJwYXJzZUludCIsImN1cnJlbnQiLCJwYWdpbmF0aW9uU2V0UGFnZVNpemUiLCJkZWZhdWx0Q29sRGVmIiwic29ydGFibGUiLCJmaWx0ZXIiLCJyZXNpemFibGUiLCJmbGV4Iiwic3VwcHJlc3NNZW51IiwiY29sdW1uRGVmcyIsImhlYWRlck5hbWUiLCJmaWVsZCIsInN1cHByZXNzU2l6ZVRvRml0IiwiY2VsbFJlbmRlcmVyIiwiaGlkZSIsImNlbGxTdHlsZSIsInN1cHByZXNzRmlsdGVyc1Rvb2xQYW5lbCIsIm9uRmlsdGVyVGV4dEJveENoYW5nZWQiLCJzZXRRdWlja0ZpbHRlciIsImdldEVsZW1lbnRCeUlkIiwiaGFuZGxlR3JpZFJlYWR5IiwicGFyYW1zIiwic2V0Q29sdW1uRGVmcyIsImxhYmVsIiwic3BhbiIsImlucHV0IiwidHlwZSIsInBsYWNlaG9sZGVyIiwib25JbnB1dCIsInN0eWxlIiwiaGVpZ2h0IiwicmVmIiwic3VwcHJlc3NSb3dDbGlja1NlbGVjdGlvbiIsInJvd1NlbGVjdGlvbiIsInBhZ2luYXRpb24iLCJwYWdpbmF0aW9uUGFnZVNpemUiLCJvblBhZ2VTaXplQ2hhbmdlZCIsInRvb2x0aXBTaG93RGVsYXkiLCJ0b29sdGlwSGlkZURlbGF5Iiwib25HcmlkUmVhZHkiLCJodG1sRm9yIiwic2VsZWN0Iiwib25DaGFuZ2UiLCJvcHRpb24iLCJoNCIsIm1heExlbmd0aCIsIm9uQmx1ciJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///./pages/users.js\n"));

/***/ }),

/***/ "./utils/auth/auth.js":
/*!****************************!*\
  !*** ./utils/auth/auth.js ***!
  \****************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getIdTokeSilently: function() { return /* binding */ getIdTokeSilently; },\n/* harmony export */   getToken: function() { return /* binding */ getToken; },\n/* harmony export */   logoutHandler: function() { return /* binding */ logoutHandler; },\n/* harmony export */   setUserCookie: function() { return /* binding */ setUserCookie; },\n/* harmony export */   useAuth: function() { return /* binding */ useAuth; },\n/* harmony export */   useUser: function() { return /* binding */ useUser; }\n/* harmony export */ });\n/* harmony import */ var _authConfig__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./authConfig */ \"./utils/auth/authConfig.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _services_apiConfig__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/services/apiConfig */ \"./services/apiConfig.js\");\n/* harmony import */ var js_cookie__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! js-cookie */ \"./node_modules/js-cookie/dist/js.cookie.mjs\");\n/* harmony import */ var react_toastify__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react-toastify */ \"./node_modules/react-toastify/dist/react-toastify.esm.mjs\");\n/* harmony import */ var react_toastify_dist_ReactToastify_css__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! react-toastify/dist/ReactToastify.css */ \"./node_modules/react-toastify/dist/ReactToastify.css\");\n/* harmony import */ var react_toastify_dist_ReactToastify_css__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(react_toastify_dist_ReactToastify_css__WEBPACK_IMPORTED_MODULE_5__);\n// auth.js\nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\n\n\n\nconst getToken = async ()=>{\n    try {\n        const accounts = msalInstance.getAllAccounts();\n        if (accounts.length === 0) {\n            throw new Error(\"No account found\");\n        }\n        const tokenResponse = await msalInstance.acquireTokenSilent({\n            scopes: _authConfig__WEBPACK_IMPORTED_MODULE_0__.loginRequest.scopes,\n            account: accounts[0]\n        });\n        return tokenResponse.accessToken;\n    } catch (error) {\n        console.error(\"Failed to get token:\", error);\n        throw error;\n    }\n};\nconst logoutHandler = async (instance, redirectUrl)=>{\n    instance.clearCache();\n    localStorage.removeItem(\"superUser\");\n    localStorage.removeItem(\"company\");\n    localStorage.removeItem(\"id\");\n    localStorage.removeItem(\"name\");\n    localStorage.removeItem(\"role\");\n    localStorage.removeItem(\"email\");\n    js_cookie__WEBPACK_IMPORTED_MODULE_3__[\"default\"].remove(\"user\");\n    js_cookie__WEBPACK_IMPORTED_MODULE_3__[\"default\"].remove(\"theme\");\n    js_cookie__WEBPACK_IMPORTED_MODULE_3__[\"default\"].remove(\"token\");\n    js_cookie__WEBPACK_IMPORTED_MODULE_3__[\"default\"].remove(\"company\");\n    js_cookie__WEBPACK_IMPORTED_MODULE_3__[\"default\"].remove(\"ADCompanyName\");\n    instance.logout({\n        postLogoutRedirectUri: redirectUrl || _authConfig__WEBPACK_IMPORTED_MODULE_0__.BASE_URL\n    }).catch((error)=>{\n        console.error(\"Logout error\", error);\n    });\n};\nconst getIdTokeSilently = async (instance, account)=>{\n    if (account) {\n        try {\n            // Provide the scopes you need for your application\n            const request = {\n                account: account,\n                scopes: [\n                    \"User.Read\"\n                ]\n            };\n            const response = await instance.acquireTokenSilent(request);\n            return response.accessToken;\n        } catch (error) {\n            console.error(\"Silent login error\", error);\n            return null;\n        }\n    }\n    return null;\n};\nconst setUserCookie = async (data, instance, account, updateToken)=>{\n    let serverAddress = _services_apiConfig__WEBPACK_IMPORTED_MODULE_2__.apiConfig.serverAddress;\n    fetch(\"\".concat(serverAddress, \"users/getUserByEmail\"), {\n        method: \"POST\",\n        headers: {\n            Authorization: \"Bearer \".concat(data.accessToken),\n            Accept: \"application/json\",\n            \"Content-Type\": \"application/json\"\n        },\n        body: JSON.stringify({\n            email: data.account.username,\n            name: data.account.name\n        })\n    }).then((res)=>{\n        // if(res.status === 400 || res.status === 401){\n        //   setTimeout(function(){\n        //     logoutHandler(instance);\n        //     router.push('/login');\n        //   }, 1000);\n        // }\n        if (res.status === 200) {\n            return res.json();\n        }\n        return Promise.reject(res);\n    }).then((json)=>{\n        let res = json;\n        if (res.length > 0) {\n            const userdetails = {\n                user_id: res[0].user_id,\n                name: data.account.name,\n                email: data.account.username,\n                role: res[0].role_id,\n                token: data.accessToken,\n                role_id: res[0].role_id,\n                department_id: res[0].department_id\n            };\n            localStorage.setItem(\"superUser\", res[0].role_id);\n            js_cookie__WEBPACK_IMPORTED_MODULE_3__[\"default\"].set(\"user\", JSON.stringify(userdetails), {\n                expires: 365\n            });\n            updateToken(userdetails);\n        // //const tokenStatus = updateToken(res, data.accessToken);\n        // if (tokenStatus) {\n        //   // sessionStorage.setItem(\"useremail\", res[0].email);\n        //   // router.push(\"/suppliers\");\n        // } else {\n        //   sessionStorage.clear();\n        //   router.push({ pathname: \"/login\", query: { message: \"text\" } });\n        // }\n        } else {\n            js_cookie__WEBPACK_IMPORTED_MODULE_3__[\"default\"].set(\"user\", JSON.stringify({\n                email: data.account.username,\n                name: data.account.name,\n                token: data.accessToken,\n                role: null\n            }));\n            updateToken({\n                email: data.account.username,\n                name: data.account.name\n            });\n        }\n    }).catch((error)=>{\n        react_toastify__WEBPACK_IMPORTED_MODULE_4__.toast.error(error.statusText, {\n            position: \"top-right\"\n        });\n    });\n    return true;\n};\nconst useAuth = ()=>{\n    _s();\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_1__.useRouter)();\n    const login = async ()=>{\n        try {\n            let msalResponse = await msalInstance.loginPopup(_authConfig__WEBPACK_IMPORTED_MODULE_0__.loginRequest);\n            getAccountDetails(msalResponse);\n        //router.push(\"/suppliers\");\n        } catch (error) {\n            console.error(\"Failed to log in:\", error);\n        }\n    };\n    const updateToken = (resData, token)=>{\n        let res = resData;\n        let serverAddress = _services_apiConfig__WEBPACK_IMPORTED_MODULE_2__.apiConfig.serverAddress;\n        let userData = {\n            id: res[0].user_id,\n            role_id: res[0].role_id,\n            first_name: res[0].first_name,\n            last_name: res[0].last_name,\n            email: res[0].email,\n            mobile_no: res[0].mobile_no,\n            password: res[0].password,\n            status: res[0].status,\n            created_date: res[0].created_date,\n            token: token\n        };\n        fetch(\"\".concat(serverAddress, \"users/update-user/\").concat(res[0].user_id), {\n            method: \"PUT\",\n            headers: {\n                //Authorization: `Bearer ${token}`,\n                Accept: \"application/json\",\n                \"Content-Type\": \"application/json\"\n            },\n            body: JSON.stringify(userData)\n        }).then((res)=>{\n            if (res.status === 200) {\n                return res.json();\n            }\n            return Promise.reject(res);\n        }).then((json)=>{\n            let res = json;\n            if (res.length > 0) {\n                return true;\n            } else {\n                return false;\n            }\n        });\n    };\n    const isLoggedIn = ()=>{\n        return !!msalInstance.getAllAccounts().length;\n    };\n    return {\n        login,\n        logout,\n        isLoggedIn\n    };\n};\n_s(useAuth, \"fN7XvhJ+p5oE6+Xlo0NJmXpxjC8=\", false, function() {\n    return [\n        next_router__WEBPACK_IMPORTED_MODULE_1__.useRouter\n    ];\n});\nconst useUser = ()=>{\n    _s1();\n    const [user, setUser] = useState(null);\n    const { instance } = useMsal();\n    const activeAccount = instance.getActiveAccount();\n    useEffect(()=>{\n        const getUser = async ()=>{\n            try {\n                if (activeAccount) {\n                    const accessToken = await instance.acquireTokenSilent({\n                        scopes: [\n                            \"User.Read\",\n                            \"Directory.Read.All\",\n                            \"User.ReadBasic.All\"\n                        ]\n                    });\n                    const userResponse = await fetch(\"https://graph.microsoft.com/v1.0/me\", {\n                        headers: {\n                            Authorization: \"Bearer \".concat(accessToken.accessToken)\n                        }\n                    });\n                    if (!userResponse.ok) {\n                        throw new Error(\"Request failed with status \".concat(userResponse.status));\n                    }\n                    const userData = await userResponse.json();\n                    setUser(userData);\n                }\n            } catch (error) {\n                console.error(\"Error fetching user data:\", error);\n            }\n        };\n        const intervalId = setInterval(getUser, 1000); // Refresh user data every second\n        return ()=>{\n            clearInterval(intervalId); // Clean up interval on component unmount\n        };\n    }, [\n        activeAccount,\n        instance\n    ]);\n    return user;\n};\n_s1(useUser, \"j5U2A8YVrGIZ6m01RV+TAEjV0cQ=\", true);\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevExports = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevExports) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports on update so we can compare the boundary\n                // signatures.\n                module.hot.dispose(function (data) {\n                    data.prevExports = currentExports;\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevExports !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevExports, currentExports)) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevExports !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./utils/auth/auth.js\n"));

/***/ }),

/***/ "./utils/getCookieData.js":
/*!********************************!*\
  !*** ./utils/getCookieData.js ***!
  \********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getCookieData: function() { return /* binding */ getCookieData; }\n/* harmony export */ });\n/* harmony import */ var js_cookie__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! js-cookie */ \"./node_modules/js-cookie/dist/js.cookie.mjs\");\n\nconst getCookieData = (cookieName)=>{\n    const cookieData = js_cookie__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(cookieName);\n    if (cookieData) {\n        return JSON.parse(cookieData);\n    }\n    return null;\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevExports = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevExports) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports on update so we can compare the boundary\n                // signatures.\n                module.hot.dispose(function (data) {\n                    data.prevExports = currentExports;\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevExports !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevExports, currentExports)) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevExports !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi91dGlscy9nZXRDb29raWVEYXRhLmpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQWdDO0FBRXpCLE1BQU1DLGdCQUFnQixDQUFDQztJQUM1QixNQUFNQyxhQUFhSCxxREFBVyxDQUFDRTtJQUUvQixJQUFJQyxZQUFZO1FBQ2QsT0FBT0UsS0FBS0MsS0FBSyxDQUFDSDtJQUNwQjtJQUVBLE9BQU87QUFDVCxFQUFFIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL3V0aWxzL2dldENvb2tpZURhdGEuanM/YjUwNyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgQ29va2llcyBmcm9tIFwianMtY29va2llXCI7XHJcblxyXG5leHBvcnQgY29uc3QgZ2V0Q29va2llRGF0YSA9IChjb29raWVOYW1lKSA9PiB7XHJcbiAgY29uc3QgY29va2llRGF0YSA9IENvb2tpZXMuZ2V0KGNvb2tpZU5hbWUpO1xyXG5cclxuICBpZiAoY29va2llRGF0YSkge1xyXG4gICAgcmV0dXJuIEpTT04ucGFyc2UoY29va2llRGF0YSk7XHJcbiAgfVxyXG5cclxuICByZXR1cm4gbnVsbDtcclxufTtcclxuIl0sIm5hbWVzIjpbIkNvb2tpZXMiLCJnZXRDb29raWVEYXRhIiwiY29va2llTmFtZSIsImNvb2tpZURhdGEiLCJnZXQiLCJKU09OIiwicGFyc2UiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///./utils/getCookieData.js\n"));

/***/ }),

/***/ "./utils/renderer/departmentRenderer.js":
/*!**********************************************!*\
  !*** ./utils/renderer/departmentRenderer.js ***!
  \**********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n\nvar _s = $RefreshSig$();\n\nconst colorMap = {\n    1: {\n        background: \"rgba(62, 171, 88, 0.2)\",\n        text: \"#3EAB58\"\n    },\n    2: {\n        background: \"rgba(0, 102, 255, 0.2)\",\n        text: \"#0066FF\"\n    },\n    3: {\n        background: \"rgb(211, 211, 211, 0.7)\",\n        text: \"#000000\"\n    },\n    5: {\n        background: \"rgba(255, 212, 169, 0.5)\",\n        text: \"#b37858\"\n    },\n    default: {\n        background: \"transparent\",\n        text: \"#9A9A9A\"\n    }\n};\nconst departmentRenderer = (params)=>{\n    _s();\n    const department = params.value;\n    const { background, text } = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>colorMap[department] || colorMap.default, [\n        department\n    ]);\n    const spanStyle = {\n        backgroundColor: background,\n        width: \"95px\",\n        textAlign: \"center\",\n        display: \"inline-block\",\n        verticalAlign: \"middle\",\n        lineHeight: \"24px\",\n        height: \"32px\",\n        color: text,\n        padding: \"6px\",\n        borderRadius: \"10px\"\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n        style: spanStyle,\n        children: department == 1 ? \"Sales\" : department == 2 ? \"Procurement\" : department == 3 ? \"Financial\" : department == 5 ? \"Technical\" : \"\"\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\utils\\\\renderer\\\\departmentRenderer.js\",\n        lineNumber: 47,\n        columnNumber: 5\n    }, undefined);\n};\n_s(departmentRenderer, \"oG5WjEbAEsX/uyBWjLiCJK7fJI8=\");\n/* harmony default export */ __webpack_exports__[\"default\"] = (departmentRenderer);\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevExports = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevExports) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports on update so we can compare the boundary\n                // signatures.\n                module.hot.dispose(function (data) {\n                    data.prevExports = currentExports;\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevExports !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevExports, currentExports)) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevExports !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./utils/renderer/departmentRenderer.js\n"));

/***/ }),

/***/ "./utils/renderer/roleRenderer.js":
/*!****************************************!*\
  !*** ./utils/renderer/roleRenderer.js ***!
  \****************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n\nvar _s = $RefreshSig$();\n\nconst colorMap = {\n    1: {\n        background: \"rgba(62, 171, 88, 0.2)\",\n        text: \"#3EAB58\"\n    },\n    2: {\n        background: \"rgba(0, 102, 255, 0.2)\",\n        text: \"#0066FF\"\n    },\n    4: {\n        background: \"rgb(211, 211, 211, 0.7)\",\n        text: \"#000000\"\n    },\n    default: {\n        background: \"rgba(154, 154, 154, 0.2)\",\n        text: \"#9A9A9A\"\n    }\n};\nconst roleRenderer = (params)=>{\n    _s();\n    const role = params.value;\n    const { background, text } = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>colorMap[role] || colorMap.default, [\n        role\n    ]);\n    // if (role === 4) {\n    //   return null; // Return null to hide the component\n    // }\n    const spanStyle = {\n        backgroundColor: background,\n        width: \"95px\",\n        textAlign: \"center\",\n        display: \"inline-block\",\n        verticalAlign: \"middle\",\n        lineHeight: \"24px\",\n        height: \"32px\",\n        color: text,\n        padding: \"6px\",\n        borderRadius: \"10px\"\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n        style: spanStyle,\n        children: role == 1 ? \"Administrator\" : role === 2 ? \"Approver\" : \"User\"\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\utils\\\\renderer\\\\roleRenderer.js\",\n        lineNumber: 48,\n        columnNumber: 5\n    }, undefined);\n};\n_s(roleRenderer, \"oG5WjEbAEsX/uyBWjLiCJK7fJI8=\");\n/* harmony default export */ __webpack_exports__[\"default\"] = (roleRenderer);\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevExports = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevExports) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports on update so we can compare the boundary\n                // signatures.\n                module.hot.dispose(function (data) {\n                    data.prevExports = currentExports;\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevExports !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevExports, currentExports)) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevExports !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./utils/renderer/roleRenderer.js\n"));

/***/ }),

/***/ "./utils/theme/theme-switcher.js":
/*!***************************************!*\
  !*** ./utils/theme/theme-switcher.js ***!
  \***************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _theme__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./theme */ \"./utils/theme/theme.js\");\n/* harmony import */ var _utils_themeContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/utils/themeContext */ \"./utils/themeContext.js\");\n// components/ThemeSwitcher.js\n\nvar _s = $RefreshSig$();\n\n\n\nconst themes = [\n    {\n        bg: \"#022D71\",\n        text: \"#022D71\",\n        name: \"blue\"\n    },\n    {\n        bg: \"#3eab58\",\n        text: \"#3eab58\",\n        name: \"green\"\n    },\n    {\n        bg: \"#f93647\",\n        text: \"#f93647\",\n        name: \"red\"\n    },\n    {\n        bg: \"#ffb522\",\n        text: \"#ffb522\",\n        name: \"yellow\"\n    },\n    {\n        bg: \"#d190ee\",\n        text: \"#d190ee\",\n        name: \"purple\"\n    }\n];\nconst ThemeSwitcher = ()=>{\n    _s();\n    const [theme, setTheme] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const { themeColor, setThemeColor } = (0,_utils_themeContext__WEBPACK_IMPORTED_MODULE_3__.useTheme)();\n    const selectThemeHandler = (e)=>{\n        setTheme(e.target.value);\n    };\n    // useEffect(() => {\n    //   if (typeof window !== \"undefined\") {\n    //     const existingStyleElement = document.getElementById(\"theme-style\");\n    //     if (existingStyleElement) {\n    //       // If it exists, remove the old style element\n    //       existingStyleElement.remove();\n    //     }\n    //     const $style = document.createElement(\"style\");\n    //     $style.id = \"theme-style\";\n    //     document.head.appendChild($style);\n    //     //Read cookie here. If present use that else default\n    //     const primaryColor = getRGBColor(themes[theme].bg, \"primary\");\n    //     const textColor = getRGBColor(\n    //       getAccessibleColor(themes[theme].text),\n    //       \"a11y\"\n    //     );\n    //     //set the primarey theme colour and the text colour based on the user prference\n    //     $style.innerHTML = `:root {${primaryColor} ${textColor}}`;\n    //   }\n    // }, [theme]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (true) {\n            const existingStyleElement = document.getElementById(\"theme-style\");\n            if (existingStyleElement) {\n                existingStyleElement.remove();\n            }\n            const $style = document.createElement(\"style\");\n            $style.id = \"theme-style\";\n            document.head.appendChild($style);\n            // Assuming themeColor is a hex value directly usable\n            const primaryColor = (0,_theme__WEBPACK_IMPORTED_MODULE_2__.getRGBColor)(themeColor, \"primary\");\n            const textColor = (0,_theme__WEBPACK_IMPORTED_MODULE_2__.getRGBColor)((0,_theme__WEBPACK_IMPORTED_MODULE_2__.getAccessibleColor)(themeColor), \"a11y\");\n            $style.innerHTML = \":root {\".concat(primaryColor, \" \").concat(textColor, \"}\");\n        }\n    }, [\n        themeColor\n    ]);\n    return(// <select onChange={selectThemeHandler} className=\"ml-5\">\n    //   {themes.map((theme, key) => (\n    //     <option key={key} value={key}>\n    //       {\" \"}\n    //       {theme.name}\n    //     </option>\n    //   ))}\n    // </select>\n    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {}, void 0, false));\n};\n_s(ThemeSwitcher, \"hRsgJS2615KWJ/3PBveK/f2gHJA=\", false, function() {\n    return [\n        _utils_themeContext__WEBPACK_IMPORTED_MODULE_3__.useTheme\n    ];\n});\n_c = ThemeSwitcher;\n/* harmony default export */ __webpack_exports__[\"default\"] = (ThemeSwitcher);\nvar _c;\n$RefreshReg$(_c, \"ThemeSwitcher\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevExports = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevExports) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports on update so we can compare the boundary\n                // signatures.\n                module.hot.dispose(function (data) {\n                    data.prevExports = currentExports;\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevExports !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevExports, currentExports)) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevExports !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./utils/theme/theme-switcher.js\n"));

/***/ }),

/***/ "./utils/theme/theme.js":
/*!******************************!*\
  !*** ./utils/theme/theme.js ***!
  \******************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getAccessibleColor: function() { return /* binding */ getAccessibleColor; },\n/* harmony export */   getRGBColor: function() { return /* binding */ getRGBColor; }\n/* harmony export */ });\nconst getRGBColor = (hex, type)=>{\n    let color = hex.replace(/#/g, \"\");\n    // rgb values\n    var r = parseInt(color.substr(0, 2), 16);\n    var g = parseInt(color.substr(2, 2), 16);\n    var b = parseInt(color.substr(4, 2), 16);\n    return \"--color-\".concat(type, \": \").concat(r, \", \").concat(g, \", \").concat(b, \";\");\n};\nconst getAccessibleColor = (hex)=>{\n    let color = hex.replace(/#/g, \"\");\n    // rgb values\n    var r = parseInt(color.substr(0, 2), 16);\n    var g = parseInt(color.substr(2, 2), 16);\n    var b = parseInt(color.substr(4, 2), 16);\n    var yiq = (r * 299 + g * 587 + b * 114) / 1000;\n    return yiq >= 128 ? \"#000000\" : \"#FFFFFF\";\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevExports = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevExports) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports on update so we can compare the boundary\n                // signatures.\n                module.hot.dispose(function (data) {\n                    data.prevExports = currentExports;\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevExports !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevExports, currentExports)) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevExports !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./utils/theme/theme.js\n"));

/***/ })

});