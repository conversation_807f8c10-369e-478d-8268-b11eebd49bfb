import Layout from "@/components/Layout";
import RawMaterialRequest from "@/components/RawMaterialRequest";
import { apiConfig } from "@/services/apiConfig";
import Cookies from "js-cookie";
import { useRouter } from "next/router";
import React, { useEffect, useState } from "react";
import { ThreeCircles } from "react-loader-spinner";
import { toast, ToastContainer } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
//
import { useMsal } from "@azure/msal-react";


const index = ({ userData }) => {
  const { instance, accounts } = useMsal();
  const [dropdowns, setDropdowns] = useState(null);
  const [rawMaterialData, setRawMaterialData] = useState(null);
  const router = useRouter();
  const { productId } = router.query;

  useEffect(() => {
    const company = Cookies.get("company");
    let prophetId = 1;
    if (company == "dpsltd") {
      prophetId = 1;
    } else if ((company == "efcltd")) {
      prophetId = 3;
    } else if (company == "fpp-ltd") {
      prophetId = 4;
    }
    const fetchData = async () => {
      const serverAddress = apiConfig.serverAddress;

      try {
        const allDropDowns = [
          "reasonForRequest",
          "masterProductCode",
          "markVariety",
          "productType",
          "organicCertification",
          "temperatureGrade",
          "intrastatCommodityCode",
          "classifiedAllergicTypes",
          "countryOfOrigin",
          "brand",
          "caliberSize",
          "endCustomer",
          "variety",
          "subProductCode",
          // "sort_group"
        ];

        const productType = 0;
        const dropdownsRequest = await fetch(
          `${serverAddress}products/get-products-dropdowns-list?prophetId=${prophetId}&productType=${productType}`,
          {
            method: "POST",
            headers: {
              Accept: "application/json",
              "Content-Type": "application/json",
              Authorization: `Bearer ${userData.token}`,
            },
            body: JSON.stringify(allDropDowns),
          }
        );
        // console.log("dropdownsRequest",dropdownsRequest);
        if (dropdownsRequest.status === 401) {
          console.error("Your session has expired. Please log in again.");
          toast.error("Your session has expired. Please log in again.");
          setTimeout(() => {
            const redirectUrl = `/login?redirect=${encodeURIComponent(
              window.location.pathname
            )}`;
            logoutHandler(instance, redirectUrl);
          }, 3000);
          return null;
        } 

        const rawMaterialRequest = fetch(
          `${serverAddress}products/get-raw-materials-by-id/${productId}`,
          {
            method: "GET",
            headers: {
              Accept: "application/json",
              "Content-Type": "application/json",
              Authorization: `Bearer ${userData.token}`,
            },
          }
        );

        const [dropdownsResponse, rawMaterialResponse] = await Promise.all([
          dropdownsRequest,
          rawMaterialRequest,
        ]);

        const allDropdownsList = await dropdownsResponse.json();
        const rawMaterialData = await rawMaterialResponse.json();

        // console.log("dropdowns", allDropdownsList);
        // console.log("raw material data", rawMaterialData);

        setDropdowns(allDropdownsList);
        setRawMaterialData(rawMaterialData);
      } catch (error) {
        console.error("Error fetching data", error);
      }
    };

    if (productId) {
      fetchData();
    }
  }, [productId]);

  return (
    <Layout userData={userData}>
      <ToastContainer limit={1} />
      {(!dropdowns && !rawMaterialData) ? (
        <div
          style={{
            display: "flex",
            alignItems: "center",
            justifyContent: "center",
            height: "100vh",
          }}
        >
          <ThreeCircles
            color="#002D73"
            height={50}
            width={50}
            visible={true}
            ariaLabel="oval-loading"
            secondaryColor="#0066FF"
            strokeWidth={2}
            strokeWidthSecondary={2}
          />
        </div>
      ) : (
        <RawMaterialRequest
          dropdowns={dropdowns}
          userData={userData}
          rawMaterialData={rawMaterialData}
          pageType={"update"}
        />
      )}
    </Layout>
  );
};

export default index;

export const getServerSideProps = async (context) => {
  const serverAddress = apiConfig.serverAddress;
  const userData = context.req.cookies.user; // Access the "user" cookie

  try {
    return {
      props: {
        userData: JSON.parse(userData),
      },
    };
  } catch (error) {
    console.error("Error fetching data", error);
    return {
      props: {
        userData: null,
      },
    };
  }
};
