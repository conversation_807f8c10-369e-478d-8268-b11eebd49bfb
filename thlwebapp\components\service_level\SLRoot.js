import React from 'react';
import SLLayout from '@/components/service_level/SLLayout';
// import { replaceCustomersDataWithTheNew, replaceCustomersListWithTheNew, replaceProductsWithTheNew } from '@/utils/service-level/utils/indexedSLDB';

const SLRoot = ({
  productList,
  userData,
  customerList,
  customerSLDataOg,
  currentCustomer,
  initalDate,
  endDate,setCustomerList,allReasonsSubreasons,setAllReasonsSubreasons,initialDataExists
}) => {
  return (
    <div>
      <SLLayout
        productList={productList}
        customerList={customerList}
        customerSLDataOg={customerSLDataOg}
        initialDataExists={initialDataExists}
        userData={userData}
        currentCustomer={currentCustomer}
        initalDate = {initalDate}
        endDate = {endDate}
        setCustomerList = {setCustomerList}
        allReasonsSubreasons = {allReasonsSubreasons}
        setAllReasonsSubreasons = {setAllReasonsSubreasons}
      />
    </div>
  )
}



export default SLRoot;