"use strict";

const utils = require("../utils");
const config = require("../../config");
const sql = require("mssql");
const logger = require("../../utils/logger");
const socket = require("../../utils/socket");
const fs = require("fs");
const formatWhatifData = (list, calendarData, financialYear) => {
  const transformedData = {};

  const weekMap = {};
  let currentWeek = calendarData[0].currentWeek;
  console.log("current week",currentWeek)
  calendarData.forEach((week) => {
    const quarter = `Q${
      week.fiscalyear <= financialYear ? week.fiscalquarter : "5"
    }`;
    if (!weekMap[quarter]) {
      weekMap[quarter] = [];
    }
    weekMap[quarter].push({
      week: week.fiscalweek,
      startWeek: week.startweek,
      endWeek: week.endweek,
      fiscalYear: week.fiscalyear,
      NoDataNextFY_Q1: 0,
      data: [
        { name: "volume", value: 0 },
        { name: "Value", value: 0 },
        { name: "be", value: 0 },
        { name: "price", value: 0 },
        { name: "units", value: 0 },
        { name: "gross_profit", value: 0 },
        { name: "gross_profit_percentage", value: 0 },
        { name: "sales", value: 0 },
      ],
    });
  });

  list.recordset.forEach((item) => {
    const productDesc = item.product_desc;
    const pkey = item.pkey;
    const quarter = `Q${
      item.fiscalyear <= financialYear ? item.fiscalquarter : "5"
    }`;
    const weekNumber = item.fiscalweek;

    if (!transformedData[pkey]) {
      transformedData[pkey] = {
        product_desc: productDesc,
        NoDataNextFY_Q1: item.NoDataNextFY_Q1,
        fiscalyear: item.fiscalyear,
        altfilid: item.altfilid,
        prodnum: item.prodnum,
        pkey: item.pkey,
        startWeek: item.startWeek,
        isLockedBy: item.locked_by,
        customer_code: item.hocustcode_customer,
        Master_code: item.Master_Code,
        Business_unit: item.Business_unit,
        case_size: item.Count_Size,
        budget_date: item.budget_date,
        quarters: {
          Q1: JSON.parse(JSON.stringify(weekMap.Q1 || [])),
          Q2: JSON.parse(JSON.stringify(weekMap.Q2 || [])),
          Q3: JSON.parse(JSON.stringify(weekMap.Q3 || [])),
          Q4: JSON.parse(JSON.stringify(weekMap.Q4 || [])),
          Q5: JSON.parse(JSON.stringify(weekMap.Q5 || [])),
        },
        promotions: [],
        total_product_volume: 0,
        total_product_value: 0,
        total_product_be: 0,
        total_product_avg_unit_price: 0,
        total_product_gross_profit: 0,
      };
    }

    const weekData = transformedData[pkey].quarters[quarter].find(
      (weekObj) => weekObj?.week === weekNumber
    );

    const currentDate = new Date();
    const currentMonth = currentDate.getMonth() + 1;
    const startWeekYear = currentDate.getFullYear();

    const currentFinanceYear =
      currentMonth >= 10 ? startWeekYear + 1 : startWeekYear;

    if (weekData) {
      weekData.NoDataNextFY_Q1 = item.NoDataNextFY_Q1;
      weekData.hasData = item.HasData;
      weekData.data = [
        { name: "volume", value: item.Weekly_Volume },
        { name: "Value", value: item.Weekly_Value },
        { name: "be", value: item.BE },
        { name: "price", value: item.Weekly_Avg_Unit_Price },
        { name: "units", value: item.Weekly_Units },
        { name: "gross_profit", value: item.Weekly_Gross_Profit },
        {
          name: "gross_profit_percentage",
          value: item.Weekly_Gross_Profit_Percentage,
        },
        {
          name: "sales",
          value: item.sales,
        },
      ];
    }

    if (quarter != "Q5") {
      // const isCurrentWeek = item.fiscalyear === currentFinanceYear && item.fiscalweek === currentWeek;
      // const isPreviousWeek = item.fiscalyear === currentFinanceYear && item.fiscalweek === currentWeek-1;

      // if (isCurrentWeek && item.new_volume != null) {
      //   const dailyPromoVolume = item.new_volume / 7;
      //   const dailyPromoSales = item.new_sales / 7;
      //   const dailyPromoGP = item.new_gp / 7;
      //   const currentDay = (currentDate.getDay() - 1 + 7) % 7;
      //   const daysToSum = 7 - currentDay;
      //   // const daysToSum =3;
      //   console.log("days to sum",daysToSum)

      //   item.new_volume= dailyPromoVolume * daysToSum;
      //   item.new_sales= dailyPromoSales * daysToSum;
      //   item.new_gp= dailyPromoGP * daysToSum;
      // }else if(isPreviousWeek && item.new_volume != null){
      //   const dailyPromoVolume = item.new_volume / 7;
      //   const dailyPromoSales = item.new_sales / 7;
      //   const dailyPromoGP = item.new_gp / 7;

      //   item.new_volume= dailyPromoVolume;
      //   item.new_sales= dailyPromoSales;
      //   item.new_gp= dailyPromoGP;
      // }

      transformedData[pkey].total_product_volume +=
        item.fiscalyear === currentFinanceYear &&
        item.fiscalweek >= currentWeek &&
        item.what_if_id != null &&
        item.new_volume != null
          ? item.new_volume
          : item.Weekly_Volume || 0;

      transformedData[pkey].total_product_value +=
        item.fiscalyear === currentFinanceYear &&
        item.fiscalweek >= currentWeek &&
        item.what_if_id != null &&
        item.new_sales != null
          ? item.new_sales
          : item.Weekly_Value || 0;

      transformedData[pkey].total_product_be +=
        item.fiscalyear === currentFinanceYear &&
        item.fiscalweek >= currentWeek &&
        item.what_if_id != null &&
        item.new_be != null
          ? item.new_be
          : item.BE || 0;

      transformedData[pkey].total_product_avg_unit_price +=
        item.fiscalyear === currentFinanceYear &&
        item.fiscalweek >= currentWeek &&
        item.what_if_id != null &&
        item.new_price != null
          ? item.new_price
          : item.Weekly_Avg_Unit_Price || 0;

      transformedData[pkey].total_product_gross_profit +=
        item.fiscalyear === currentFinanceYear &&
        item.fiscalweek >= currentWeek &&
        item.what_if_id != null &&
        item.new_gp != null
          ? item.new_gp
          : item.Weekly_Gross_Profit || 0;
    }

    if (item.task_type_id !== null) {
      let promotion = transformedData[pkey].promotions.find(
        (promo) => promo.what_if_id === item.what_if_id
      );
      if (!promotion) {
        promotion = {
          what_if_id: item.what_if_id,
          type_id: item.task_type_id,
          breakeven_status_id: item.breakeven_status_id,
          promo_fiscalYear: item.fiscal_year,
          promo_start_week_no: item.start_week_no,
          promo_end_week_no: item.end_week_no,
          promo_start_week: item.start_week,
          description: item.description,
          is_confirmed: item.is_confirmed,
          created_by: item?.email?.split("@")[0]?.split(".")?.join(" "),
          weekData: {},
        };
        transformedData[pkey].promotions.push(promotion);
      }

      promotion.weekData[weekNumber] = {
        current: {
          volume: item.new_volume,
          price: item.new_price,
          sales: item.new_sales,
          be: item.new_be,
          gp: item.new_gp,
          gp_percent: item.new_gp_percentage,
        },
      };
    }
  });

  const transformedArray = Object.values(transformedData);
  const overallTotalProductVolume = transformedArray.reduce(
    (acc, product) => acc + product.total_product_volume,
    0
  );
  transformedArray.push({
    overall_total_product_volume: overallTotalProductVolume,
  });

  return JSON.stringify(transformedArray);
};

const formatWhatifDataNextWeek = (list, calendarData) => {
  const transformedData = {};

  const weekMap = {};
  calendarData.forEach((week) => {
    const quarter = `Q${week.fiscalquarter}`;
    if (!weekMap[quarter]) {
      weekMap[quarter] = [];
    }
    weekMap[quarter].push({
      week: week.fiscalweek,
      startWeek: week.startweek,
      data: [
        { name: "volume", value: "" },
        { name: "Value", value: "" },
        { name: "be", value: "" },
        { name: "price", value: "" },
        { name: "units", value: "" },
        { name: "gross_profit", value: "" },
        { name: "gross_profit_percentage", value: "" },
        { name: "sales", value: "" },
      ],
    });
  });

  list.recordset.forEach((item) => {
    const quarter = `Q${item.fiscalquarter}`;

    const weekNumber = item.fiscalweek;
    const weekData = weekMap[quarter].find(
      (weekObj) => weekObj?.week === weekNumber
    );
    if (weekData) {
      weekData.data = [
        { name: "volume", value: item.Weekly_Volume },
        { name: "Value", value: item.Weekly_Value },
        { name: "be", value: item.BE },
        { name: "price", value: item.Weekly_Avg_Unit_Price },
        { name: "units", value: item.Weekly_Units },
        { name: "gross_profit", value: item.Weekly_Gross_Profit },
        {
          name: "gross_profit_percentage",
          value: item.Weekly_Gross_Profit_Percentage,
        },
        {
          name: "sales",
          value: item.sales,
        },
      ];
    }
  });

  const firstKey = Object.keys(weekMap)[0];
  const firstObject = weekMap[firstKey];
  return firstObject;
};

const calculateTotals = (list, financeYear, currentWeek,bugdetDataByCustomer) => {
// console.log("budget data",bugdetDataByCustomer)
  const weeklyAllProductsTotals = {};
  const quarterlyBudgetTotals = {};
  const quarterlyAllProductsTotals = {};
  const currentDate = new Date();
  const currentMonth = currentDate.getMonth() + 1;
  const startWeekYear = currentDate.getFullYear();

  const currentFinanceYear =
    currentMonth >= 10 ? startWeekYear + 1 : startWeekYear;

  list.recordset.forEach((item) => {
    const pkey = item.pkey;
    const quarter = `Q${
      item.fiscalyear <= financeYear ? item.fiscalquarter : "5"
    }`;
    const financialYear = item.fiscalyear;
    const weekNumber = item.fiscalweek;

    if (!weeklyAllProductsTotals[financialYear]) {
      weeklyAllProductsTotals[financialYear] = {};
    }

    if (!weeklyAllProductsTotals[financialYear][weekNumber]) {
      weeklyAllProductsTotals[financialYear][weekNumber] = {
        quarter_no: item.fiscalquarter,
        total_all_products_value: 0,
        total_all_products_volume: 0,
        total_all_products_gross_profit: 0,
        count: 0,
      };
            
    }

    if (
      item.what_if_id != null &&
      item.new_volume != null && item.new_sales != null && item.new_gp != null &&
      item.start_week_no <= item.end_week_no &&
      item.fiscalyear === currentFinanceYear &&
      item.fiscalweek >= currentWeek
    ) {
      weeklyAllProductsTotals[financialYear][
        weekNumber
      ].total_all_products_volume += item.new_volume;
      weeklyAllProductsTotals[financialYear][
        weekNumber
      ].total_all_products_value += item.new_sales;
      weeklyAllProductsTotals[financialYear][
        weekNumber
      ].total_all_products_gross_profit += item.new_gp;
    } else if (
      item.what_if_id != null &&
      item.new_volume != null && item.new_sales != null && item.new_gp != null &&
      item.start_week_no > item.end_week_no &&
      financialYear != item.fiscal_year &&
      item.fiscalyear === currentFinanceYear &&
      item.fiscalweek >= currentWeek
    ) {
      weeklyAllProductsTotals[financialYear][
        weekNumber
      ].total_all_products_volume += item.new_volume;
      weeklyAllProductsTotals[financialYear][
        weekNumber
      ].total_all_products_value += item.new_sales;
      weeklyAllProductsTotals[financialYear][
        weekNumber
      ].total_all_products_gross_profit += item.new_gp;
    } else {
      weeklyAllProductsTotals[financialYear][
        weekNumber
      ].total_all_products_volume += item.Weekly_Volume;
      weeklyAllProductsTotals[financialYear][
        weekNumber
      ].total_all_products_value += item.Weekly_Value;
      weeklyAllProductsTotals[financialYear][
        weekNumber
      ].total_all_products_gross_profit += item.Weekly_Gross_Profit;
    }

    weeklyAllProductsTotals[financialYear][weekNumber].count++;


    if (!quarterlyAllProductsTotals[quarter]) {
      quarterlyAllProductsTotals[quarter] = {
        total_all_products_value: 0,
        total_all_products_volume: 0,
        total_all_products_gross_profit: 0,
        count: 0,
      };
    }

    if (
      item.what_if_id != null &&
      item.new_volume != null &&
      item.start_week_no <= item.end_week_no &&
      item.fiscalyear === currentFinanceYear &&
      item.fiscalweek >= currentWeek
    ) {
      quarterlyAllProductsTotals[quarter].total_all_products_value +=
        item.new_sales;

      quarterlyAllProductsTotals[quarter].total_all_products_volume +=
        item.new_volume;

      quarterlyAllProductsTotals[quarter].total_all_products_gross_profit +=
        item.new_gp;
    } else if (
      item.what_if_id != null &&
      item.new_volume != null &&
      item.start_week_no > item.end_week_no &&
      financialYear != item.fiscal_year &&
      item.fiscalyear === currentFinanceYear &&
      item.fiscalweek >= currentWeek
    ) {
      quarterlyAllProductsTotals[quarter].total_all_products_value +=
        item.new_sales;

      quarterlyAllProductsTotals[quarter].total_all_products_volume +=
        item.new_volume;

      quarterlyAllProductsTotals[quarter].total_all_products_gross_profit +=
        item.new_gp;
    } else {
      quarterlyAllProductsTotals[quarter].total_all_products_value +=
        item.Weekly_Value;

      quarterlyAllProductsTotals[quarter].total_all_products_volume +=
        item.Weekly_Volume;

      quarterlyAllProductsTotals[quarter].total_all_products_gross_profit +=
        item.Weekly_Gross_Profit;
    }
    quarterlyAllProductsTotals[quarter].count++;
  });

  bugdetDataByCustomer.recordset.forEach((item)=>{
    const quarter = `Q${
      item.fiscalyear <= financeYear ? item.fiscalquarter : "5"
    }`;
    if (!quarterlyBudgetTotals[quarter]) {
      quarterlyBudgetTotals[quarter] = {
        total_budget_value: 0,
        total_budget_volume: 0,
        total_budget_gross_profit: 0,
        count: 0,
      };
    }
    
    quarterlyBudgetTotals[quarter].total_budget_value +=
      item.Weekly_Budget_Value || 0;
    quarterlyBudgetTotals[quarter].total_budget_volume +=
      item.Weekly_Budget_Volume || 0;
    quarterlyBudgetTotals[quarter].total_budget_gross_profit +=
      item.Weekly_Budget_Gross_Profit || 0;
    quarterlyBudgetTotals[quarter].count++;
  });

  Object.keys(quarterlyBudgetTotals).forEach((quarter) => {
    const total = quarterlyBudgetTotals[quarter];

    total.total_budget_gp_percent =
      total.total_budget_value !== 0
        ? (total.total_budget_gross_profit / total.total_budget_value) * 100
        : 0;
  });

  Object.keys(quarterlyAllProductsTotals).forEach((quarter) => {
    const total = quarterlyAllProductsTotals[quarter];
    total.total_all_gp_percent =
      total.total_all_products_value !== 0
        ? (total.total_all_products_gross_profit /
            total.total_all_products_value) *
          100
        : 0;
  });

  return {
    weeklyAllProductsTotals,
    quarterlyBudgetTotals,
    quarterlyAllProductsTotals,
  };
};

const getWhatifByCustomer = async (
  cust_code,
  financialYear,
  quartersSelected,
  name,
  email
) => {
  let transaction;
  try {
    let pool = await sql.connect(config.sql);
    transaction = new sql.Transaction(pool);
    await transaction.begin();
    const sqlQueries = await utils.loadSqlQueries("whatif");
    const whatifByCustomer = await pool
      .request(transaction)
      .input("cust_code", sql.VarChar, cust_code)
      .input("financialYear", sql.VarChar, financialYear.toString())
      .query(sqlQueries.getWhatifByCustomer);

      
    let whatifCalender;
    if (quartersSelected == "current") {
      whatifCalender = await pool
        .request(transaction)
        .input("financialYear", sql.VarChar, financialYear.toString())
        .query(sqlQueries.getCurrentQuarterCalendarData);
    } else {
      whatifCalender = await pool
        .request(transaction)
        .input("calName", sql.VarChar, "OFF Financial Calendar")
        .input("quartersSelected", sql.VarChar, quartersSelected)
        .input("financialYear", sql.VarChar, financialYear.toString())
        .query(sqlQueries.getCalenderData);
    }
    const formatedWhatif = formatWhatifData(
      whatifByCustomer,
      whatifCalender.recordset,
      financialYear
    );
    await transaction.commit();
    return formatedWhatif;
  } catch (error) {
    logger.error({
      username: name,
      type: "error",
      description: `User with email ${email} failed to fetch whatif by customer: "${error.message}"`,
      item_id: null,
      module_id: 3,
    });
    if (transaction) {
      await transaction.rollback();
    }
    console.log("error", error);
    return error.message;
  }
};

const getWhatifTotalsByCustomer = async (
  cust_code,
  name,
  email,
  financialYear
) => {
  let transaction;
  try {
    let pool = await sql.connect(config.sql);
    transaction = new sql.Transaction(pool);
    await transaction.begin();
    const sqlQueries = await utils.loadSqlQueries("whatif");

    const whatifByCustomer = await pool
      .request(transaction)
      .input("cust_code", sql.VarChar, cust_code)
      .input("financialYear", sql.VarChar, financialYear.toString())
      .query(sqlQueries.getWhatifByCustomer);

      const bugdetDataByCustomer = await pool
      .request(transaction)
      .input("cust_code", sql.VarChar, cust_code)
      .input("financialYear", sql.VarChar, financialYear.toString())
      .query(sqlQueries.getBudgetDataByCustomer);

    let currentWeek = await pool
      .request(transaction)
      .input("financialYear", sql.VarChar, financialYear.toString())
      .query(sqlQueries.getCurrentWeek);
    const totals = calculateTotals(
      whatifByCustomer,
      financialYear,
      currentWeek.recordset[0].currentWeek,bugdetDataByCustomer
    );

    await transaction.commit();
    return totals;
  } catch (error) {
    logger.error({
      username: name,
      type: "error",
      description: `User with email ${email} failed to fetch whatif totals by customer: "${error.message}"`,
      item_id: null,
      module_id: 3,
    });
    if (transaction) {
      await transaction.rollback();
    }
    console.log("error", error);
    return error.message;
  }
};

const getDefaultCalendarData = async (
  quartersSelected,
  name,
  email,
  financialYear
) => {
  let transaction;
  try {
    let pool = await sql.connect(config.sql);
    transaction = new sql.Transaction(pool);
    await transaction.begin();
    const sqlQueries = await utils.loadSqlQueries("whatif");
    const whatifByCustomer = await pool
      .request(transaction)
      .input("quartersSelected", sql.VarChar, quartersSelected)
      .input("financialYear", sql.VarChar, financialYear.toString())
      .query(sqlQueries.getCalenderData);

    await transaction.commit();
    return whatifByCustomer.recordset;
  } catch (error) {
    logger.error({
      username: name,
      type: "error",
      description: `User with email ${email} failed to fetch default calendar data: "${error.message}"`,
      item_id: null,
      module_id: 3,
    });
    if (transaction) {
      await transaction.rollback();
    }
    return error.message;
  }
};
const getstdCalendarData = async (
  quartersSelected,
  name,
  email,
  financialYear
) => {
  let transaction;
  try {
    let pool = await sql.connect(config.sql);
    transaction = new sql.Transaction(pool);
    await transaction.begin();
    const sqlQueries = await utils.loadSqlQueries("whatif");
    const whatifByCustomer = await pool
      .request(transaction)
      .input("quartersSelected", sql.VarChar, quartersSelected)
      .input("financialYear", sql.VarChar, financialYear)
      .query(sqlQueries.getStdCalendarData);

    await transaction.commit();
    return whatifByCustomer.recordset;
  } catch (error) {
    logger.error({
      username: name,
      type: "error",
      description: `User with email ${email} failed to fetch std calendar data: "${error.message}"`,
      item_id: null,
      module_id: 3,
    });
    if (transaction) {
      await transaction.rollback();
    }
    return error.message;
  }
};

const getWhatifProductByCustomer = async (cust_code, name, email) => {
  let transaction;
  try {
    let pool = await sql.connect(config.sql);
    transaction = new sql.Transaction(pool);
    await transaction.begin();
    const sqlQueries = await utils.loadSqlQueries("whatif");
    const getWhatifProductByCustomer = await pool
      .request(transaction)
      .input("cust_code", sql.VarChar, cust_code)
      .query(sqlQueries.getWhatifProductByCustomer);

    await transaction.commit();
    return getWhatifProductByCustomer.recordset;
  } catch (error) {
    logger.error({
      username: name,
      type: "error",
      description: `User with email ${email} failed to fetch what if products by customer: "${error.message}"`,
      item_id: null,
      module_id: 3,
    });
    if (transaction) {
      await transaction.rollback();
    }
    return error.message;
  }
};

const getWhatifCustomers = async (name, email) => {
  let transaction;
  try {
    let pool = await sql.connect(config.sql);
    transaction = new sql.Transaction(pool);
    await transaction.begin();
    const sqlQueries = await utils.loadSqlQueries("whatif");
    const whatifCustomers = await pool
      .request(transaction)
      //.input("prd_number", sql.Int, prd_number)
      .query(sqlQueries.getCustomers);

    await transaction.commit();
    return whatifCustomers.recordset;
  } catch (error) {
    logger.error({
      username: name,
      type: "error",
      description: `User with email ${email} failed to fetch what if customer: "${error.message}"`,
      item_id: null,
      module_id: 3,
    });
    if (transaction) {
      await transaction.rollback();
    }
    return error.message;
  }
};

const getCurrentQuarterCalendarData = async (name, email, financialYear) => {
  let transaction;
  try {
    let pool = await sql.connect(config.sql);
    transaction = new sql.Transaction(pool);
    await transaction.begin();
    const sqlQueries = await utils.loadSqlQueries("whatif");
    const getCurrentQuarterCalendarData = await pool
      .request(transaction)
      .input("financialYear", sql.VarChar, financialYear.toString())
      .query(sqlQueries.getCurrentQuarterCalendarData);
    await transaction.commit();
    return getCurrentQuarterCalendarData.recordset;
  } catch (error) {
    logger.error({
      username: name,
      type: "error",
      description: `User with email ${email} failed to fetch current quarter calendar data: "${error.message}"`,
      item_id: null,
      module_id: 3,
    });
    console.error(error);
    if (transaction) {
      await transaction.rollback();
    }
    return error.message;
  }
};

const getCurrentQuarterStdCalendarData = async (name, email, financialYear) => {
  let transaction;
  try {
    let pool = await sql.connect(config.sql);
    transaction = new sql.Transaction(pool);
    await transaction.begin();
    const sqlQueries = await utils.loadSqlQueries("whatif");
    const getCurrentQuarterStdCalendarData = await pool
      .request(transaction)
      .input("financialYear", sql.VarChar, financialYear.toString())
      .query(sqlQueries.getCurrentQuarterStdCalendarData);
    await transaction.commit();
    return getCurrentQuarterStdCalendarData.recordset;
  } catch (error) {
    logger.error({
      username: name,
      type: "error",
      description: `User with email ${email} failed to fetch current quarter calendar data: "${error.message}"`,
      item_id: null,
      module_id: 3,
    });
    console.error(error);
    if (transaction) {
      await transaction.rollback();
    }
    return error.message;
  }
};

const getWhatifProducts = async (name, email) => {
  let transaction;
  try {
    let pool = await sql.connect(config.sql);
    transaction = new sql.Transaction(pool);
    await transaction.begin();
    const sqlQueries = await utils.loadSqlQueries("whatif");
    const whatifProducts = await pool
      .request(transaction)
      //.input("prd_number", sql.Int, prd_number)
      .query(sqlQueries.getProducts);

    await transaction.commit();
    return whatifProducts.recordset;
  } catch (error) {
    logger.error({
      username: name,
      type: "error",
      description: `User with email ${email} failed to fetch whatif products: "${error.message}"`,
      item_id: null,
      module_id: 3,
    });
    if (transaction) {
      await transaction.rollback();
    }
    return error.message;
  }
};

const getWhatifByQuarter = async (qrt_number, name, email) => {
  let transaction;
  try {
    let pool = await sql.connect(config.sql);
    transaction = new sql.Transaction(pool);
    await transaction.begin();
    const sqlQueries = await utils.loadSqlQueries("whatif");
    const whatifByQuarter = await pool
      .request(transaction)
      .input("qrt_number", sql.Int, qrt_number)
      .query(sqlQueries.getWhatifData);

    await transaction.commit();
    return whatifByQuarter.recordset;
  } catch (error) {
    logger.error({
      username: name,
      type: "error",
      description: `User with email ${email} failed to fetch whatif by quarter: "${error.message}"`,
      item_id: null,
      module_id: 3,
    });
    if (transaction) {
      await transaction.rollback();
    }
    return error.message;
  }
};

const addWhatif = async (whatif, name, email) => {
  let transaction;
  try {
    const io = socket.getIO();
    let pool = await sql.connect(config.sql);
    transaction = new sql.Transaction(pool);
    await transaction.begin();
    const sqlQueries = await utils.loadSqlQueries("whatif");

    let promotionData = {
      task_type_id: whatif.taskTypeId,
      start_week_no: whatif.startWeekNo,
      start_week: whatif.startWeekDate,
      end_week_no: whatif.endWeekNo,
      pkey: whatif.pkey,
      product_name: whatif.productName,
      breakeven_status_id: whatif.breakEvenStatusId,
      fiscalyear: whatif.fiscalYear,
      description: whatif.description,
      is_confirmed: whatif.isConfirmed,
      is_deleted: 0,
      timestamp: whatif.timestamp,
      weeksData: whatif.apiWeeksData,
      user_id: whatif.user_id,
      created_by: whatif.created_by,
    };

    let whatIfId = whatif.whatIfId;

    // io.emit('message', whatif);

    let isNewPromo;

    let taskTypeName;
    let breakEvenTaskName;

    if (promotionData?.task_type_id) {
      const taskType = await pool
        .request(transaction)
        .input("task_type_id", sql.Int, promotionData?.task_type_id)
        .query(sqlQueries.getTaskTypeByName);
      taskTypeName = taskType.recordset[0].type;

      if (
        promotionData?.task_type_id == 2 &&
        promotionData?.breakeven_status_id
      ) {
        const breakevenTaskType = await pool
          .request(transaction)
          .input(
            "breakEvenStatusId",
            sql.Int,
            promotionData?.breakeven_status_id
          )
          .query(sqlQueries.getBreakevenStatusName);

        breakEvenTaskName = breakevenTaskType.recordset[0].name;
      }
    }

    let previousData;
    if (whatIfId) {
      const previousWhatIfData = await pool
        .request(transaction)
        .input("what_if_id", sql.Int, whatIfId)
        .query(sqlQueries.getPreviousWhatIfData);
      previousData = previousWhatIfData.recordset;
    }

    const comparePromotionData = (newPromotion, previousPromotions) => {
      const differences = [];

      const addDifference = (field, oldValue, newValue) => {
        differences.push(`${field} changed from ${oldValue} to ${newValue}`);
      };

      const previousPromotion = previousPromotions.find(
        (promo) => promo.pkey === newPromotion.pkey
      );

      if (previousPromotion) {
        if (previousPromotion.end_week_no !== newPromotion.end_week_no) {
          addDifference(
            "Updated end week number",
            previousPromotion.end_week_no,
            newPromotion.end_week_no
          );
        }
        if (previousPromotion.description !== newPromotion.description) {
          addDifference(
            "Description",
            previousPromotion.description,
            newPromotion.description
          );
        }
        if (previousPromotion.is_confirmed !== newPromotion.is_confirmed) {
          addDifference(
            "Confirmation status",
            previousPromotion.is_confirmed,
            newPromotion.is_confirmed
          );
        }
        if (newPromotion.task_type_id == 2) {
          if (
            previousPromotion.breakeven_status_id !=
            newPromotion.breakeven_status_id
          ) {
            addDifference(
              `Breakeven status`,
              previousPromotion.breakeven_status_name,
              breakEvenTaskName
            );
          }
        }
        newPromotion.weeksData.forEach((newWeek) => {
          const previousWeek = previousPromotions.find(
            (promo) => promo.week_no === newWeek.weekNumber
          );

          if (previousWeek) {
            if (previousPromotion.task_type_id !== newPromotion.task_type_id) {
              addDifference(
                "Task type",
                previousPromotion.task_type_name,
                taskTypeName
              );

              newPromotion.weeksData.forEach((newWeek) => {
                differences.push(
                  `Week ${newWeek.weekNumber}, volume: ${newWeek.newVolume}, BE: ${newWeek.newBe}, price: ${newWeek.newPrice}, GP: ${newWeek.newGp}, sales: ${newWeek.newSales}, GP percentage: ${newWeek.newGpPer}`
                );
              });
            } else {
              if (previousWeek.new_volume !== newWeek.newVolume) {
                addDifference(
                  `Week ${newWeek.weekNumber} volume`,
                  previousWeek.new_volume,
                  newWeek.newVolume
                );
              }
              if (previousWeek.new_be !== newWeek.newBe) {
                addDifference(
                  `Week ${newWeek.weekNumber} BE`,
                  previousWeek.new_be,
                  newWeek.newBe
                );
              }
              if (previousWeek.new_price !== newWeek.newPrice) {
                addDifference(
                  `Week ${newWeek.weekNumber} price`,
                  previousWeek.new_price,
                  newWeek.newPrice
                );
              }
              if (previousWeek.new_gp !== newWeek.newGp) {
                addDifference(
                  `Week ${newWeek.weekNumber} GP`,
                  previousWeek.new_gp,
                  newWeek.newGp
                );
              }
              if (previousWeek.new_sales !== newWeek.newSales) {
                addDifference(
                  `Week ${newWeek.weekNumber} sales`,
                  previousWeek.new_sales,
                  newWeek.newSales
                );
              }
              if (previousWeek.new_gp_percentage !== newWeek.newGpPer) {
                addDifference(
                  `Week ${newWeek.weekNumber} GP percentage`,
                  previousWeek.new_gp_percentage,
                  newWeek.newGpPer
                );
              }
            }
          } else {
            differences.push(
              `New week added: ${newWeek.weekNumber}, volume: ${newWeek.newVolume}, BE: ${newWeek.newBe}, price: ${newWeek.newPrice}, GP: ${newWeek.newGp}, sales: ${newWeek.newSales}, GP percentage: ${newWeek.newGpPer}`
            );
          }
        });

        previousPromotions.forEach((prevWeek) => {
          if (
            !newPromotion.weeksData.find(
              (newWeek) => newWeek.weekNumber === prevWeek.week_no
            )
          ) {
            differences.push(`Week deleted: ${prevWeek.week_no}`);
          }
        });
      } else {
        differences.push("New promotion added");
      }

      // Generate the final description
      const description = differences.join("; ");
      return description;
    };
    let updatedPromoDescription;
    if (whatif.whatIfId) {
      updatedPromoDescription = comparePromotionData(
        promotionData,
        previousData
      );
    }

    if (!whatif.whatIfId) {
      isNewPromo = true;
      const whatIfPromo = await pool
        .request(transaction)
        .input("task_type_id", sql.Int, promotionData.task_type_id)
        .input("start_week_no", sql.Int, promotionData.start_week_no)
        .input("start_week", sql.DateTime, promotionData.start_week)
        .input("end_week_no", sql.Int, promotionData.end_week_no)
        .input("pkey", sql.VarChar, promotionData.pkey)
        .input("product_name", sql.VarChar, promotionData.product_name)
        .input(
          "breakeven_status_id",
          sql.Int,
          promotionData.breakeven_status_id
        )
        .input("fiscalyear", sql.Int, promotionData.fiscalyear)
        .input("description", sql.VarChar, promotionData.description)
        .input("is_confirmed", sql.Bit, promotionData.is_confirmed)
        .input("created_by", sql.Int, promotionData.created_by)
        .query(sqlQueries.addSinglePromotion);

      const promotion_id = whatIfPromo.recordset[0].id;
      whatIfId = promotion_id;
      if (promotion_id) {
        promotionData.weeksData.map((res) => {
          pool
            .request(transaction)
            .input("whatif_id", sql.Int, promotion_id)
            .input("week_no", sql.Int, res.weekNumber)
            .input("new_volume", sql.Float, res.newVolume)
            .input("new_be", sql.Float, res.newBe)
            .input("new_price", sql.Float, res.newPrice)
            .input("new_gp", sql.Float, res.newGp)
            .input("new_sales", sql.Float, res.newSales)
            .input("new_gp_percentage", sql.Float, res.newGpPer)
            .input("updated_by", sql.Int, 1)
            .query(sqlQueries.addPromotionsById);
        });
      }
    } else {
      isNewPromo = false;
      const whatIfPromo = await pool
        .request(transaction)
        .input("whatif_id", sql.Int, whatif.whatIfId)
        .input("task_type_id", sql.Int, whatif.taskTypeId)
        .input("end_week_no", sql.Int, whatif.endWeekNo)
        .input("breakeven_status_id", sql.Int, whatif.breakEvenStatusId)
        .input("description", sql.VarChar, whatif.description)
        .input("is_confirmed", sql.Bit, whatif.isConfirmed)
        .input("updated_by", sql.Int, whatif.created_by)
        .query(sqlQueries.updateSinglePromotion);

      if (whatIfPromo.recordset.length > 0) {
        const checkExisting = await pool
          .request(transaction)
          .input("whatif_id", whatif.whatIfId)
          .query(sqlQueries.checkExistingPromotions);

        const existingWhatifs = checkExisting.recordset;

        promotionData.weeksData.map((item) => {
          if (!item.isNew) {
            pool
              .request(transaction)
              .input("whatif_id", sql.Int, whatif.whatIfId)
              .input("week_no", sql.Int, item.weekNumber)
              .input("new_volume", sql.Float, item.newVolume)
              .input("new_be", sql.Float, item.newBe)
              .input("new_price", sql.Float, item.newPrice)
              .input("new_gp", sql.Float, item.newGp)
              .input("new_sales", sql.Float, item.newSales)
              .input("new_gp_percentage", sql.Float, item.newGpPer)
              .input("updated_by", sql.Int, 1)
              .query(sqlQueries.updatePromotionDetailsById);
          } else {
            const doesWeekExists = existingWhatifs.filter(
              (wif) => wif.week_no === item.weekNumber
            );

            if (doesWeekExists.length > 0) {
              pool
                .request(transaction)
                .input("whatif_id", sql.Int, whatif.whatIfId)
                .input("week_no", sql.Int, item.weekNumber)
                .input("new_volume", sql.Float, item.newVolume)
                .input("new_be", sql.Float, item.newBe)
                .input("new_price", sql.Float, item.newPrice)
                .input("new_gp", sql.Float, item.newGp)
                .input("new_sales", sql.Float, item.newSales)
                .input("new_gp_percentage", sql.Float, item.newGpPer)
                .input("is_deleted", sql.Bit, 0)
                .input("updated_by", sql.Int, 1)
                .query(sqlQueries.updateExistPromo);
            } else {
              pool
                .request(transaction)
                .input("whatif_id", sql.Int, whatif.whatIfId)
                .input("week_no", sql.Int, item.weekNumber)
                .input("new_volume", sql.Float, item.newVolume)
                .input("new_be", sql.Float, item.newBe)
                .input("new_price", sql.Float, item.newPrice)
                .input("new_gp", sql.Float, item.newGp)
                .input("new_sales", sql.Float, item.newSales)
                .input("new_gp_percentage", sql.Float, item.newGpPer)
                .input("updated_by", sql.Int, 1)
                .query(sqlQueries.addPromotionsById);
            }
          }
        });

        const missingWhatifs = existingWhatifs.filter(
          (existing) =>
            !promotionData.weeksData.some(
              (newW) => newW.weekNumber === existing.week_no
            )
        );

        missingWhatifs.map((mWi) => {
          pool
            .request(transaction)
            .input("whatif_id", sql.Int, whatif.whatIfId)
            .input("week_no", sql.Int, mWi.week_no)
            .input("is_deleted", sql.Bit, 1)
            .input("updated_by", sql.Int, 1)
            .query(sqlQueries.deleteExistingPromo);
        });
      }
    }
    if (isNewPromo) {
      const weeksDataDescription = promotionData.weeksData
        .map(
          (week) =>
            `Week ${week.weekNumber}: Volume ${week.newVolume}, BE ${week.newBe}, Price ${week.newPrice}, GP ${week.newGp}, Sales ${week.newSales}, GP% ${week.newGpPer}`
        )
        .join("; ");

      let description;

      if (promotionData.task_type_id == 2) {
        description = `User with email ${email} created a new promo of type ${taskTypeName}, Breakeven status type ${breakEvenTaskName} for product ${
          promotionData.product_name
        }, fiscal year ${promotionData.fiscalyear} starting from week ${
          promotionData.start_week_no
        } till week ${promotionData.end_week_no}${
          promotionData.description
            ? `, description: ${promotionData.description}`
            : ""
        }, isConfirmed: ${
          promotionData.is_confirmed
        }. Weeks Data: ${weeksDataDescription}`;
      } else {
        description = `User with email ${email} created a new promo of type ${taskTypeName}
   for product ${promotionData.product_name}, fiscal year ${
          promotionData.fiscalyear
        } starting from week ${promotionData.start_week_no} till week ${
          promotionData.end_week_no
        }${
          promotionData.description
            ? `, description: ${promotionData.description}`
            : ""
        }, isConfirmed: ${
          promotionData.is_confirmed
        }. Weeks Data: ${weeksDataDescription}`;
      }

      logger.info({
        username: name,
        type: "success",
        description: description,
        item_id: whatIfId,
        module_id: 3,
      });
    } else {
      logger.info({
        username: name,
        type: "success",
        description: `User with email ${email} updated data for product ${promotionData.product_name}, fiscal year ${promotionData.fiscalyear}. ${updatedPromoDescription}`,
        item_id: whatIfId,
        module_id: 3,
      });
    }
    await transaction.commit();
    return { response: whatIfId };
  } catch (error) {
    logger.error({
      username: name,
      type: "error",
      description: `User with email ${email} failed to add/update whatif: "${error.message}"`,
      item_id: whatif.whatIfId ? whatif.whatifId : null,
      module_id: 3,
    });
    if (transaction) {
      await transaction.rollback();
    }
    console.error("error", error);
    return { response: null };
  } finally {
    if (transaction) {
      transaction = null;
    }
  }
};

const removeWhatif = async (whatifId, productName, fiscalYear, name, email) => {
  let transaction;
  try {
    let pool = await sql.connect(config.sql);
    transaction = new sql.Transaction(pool);
    await transaction.begin();
    const sqlQueries = await utils.loadSqlQueries("whatif");
    await pool
      .request(transaction)
      .input("whatif_id", sql.Int, whatifId)
      .input("is_deleted", sql.Bit, 1)
      .query(sqlQueries.removeAllPromotions);

    await transaction.commit();
    logger.info({
      username: name,
      type: "success",
      description: `User with email ${email} removed whatif with id ${whatifId} for product ${productName}, fiscal year ${fiscalYear}`,
      item_id: whatifId,
      module_id: 3,
    });
    return { response: true };
  } catch (error) {
    if (transaction) {
      await transaction.rollback();
    }
    console.error("error", error);
    logger.error({
      username: name,
      type: "error",
      description: `User with email ${email} failed to remove whatif for product ${productName}: "${error.message}"`,
      item_id: whatifId ? whatifId : null,
      module_id: 3,
    });
    return { response: false };
  }
};

const getBreakevenStatuses = async (name, email) => {
  let transaction;
  try {
    let pool = await sql.connect(config.sql);
    transaction = new sql.Transaction(pool);
    await transaction.begin();
    const sqlQueries = await utils.loadSqlQueries("whatif");
    const breakevenStatuses = await pool
      .request(transaction)
      .query(sqlQueries.getBreakEvenStatuses);

    await transaction.commit();
    return breakevenStatuses.recordset;
  } catch (error) {
    logger.error({
      username: name,
      type: "error",
      description: `User with email ${email} failed to fetch breakeven status "${error.message}"`,
      item_id: null,
      module_id: 3,
    });
    if (transaction) {
      await transaction.rollback();
    }
    console.error("error", error);
  }
};

const getTaskTypes = async (name, email) => {
  let transaction;
  try {
    let pool = await sql.connect(config.sql);
    transaction = new sql.Transaction(pool);
    await transaction.begin();
    const sqlQueries = await utils.loadSqlQueries("whatif");
    const taskTypes = await pool
      .request(transaction)
      .query(sqlQueries.getTaskTypes);

    await transaction.commit();
    return taskTypes.recordset;
  } catch (error) {
    logger.error({
      username: name,
      type: "error",
      description: `User with email ${email} failed to fetch task types: "${error.message}"`,
      item_id: null,
      module_id: 3,
    });
    if (transaction) {
      await transaction.rollback();
    }
    console.error("error", error);
  }
};

const removeLocks = async (name, email) => {
  let transaction;
  try {
    const io = socket.getIO();
    const payload = { name, email };
    io.emit("locksRemoved", payload);

    let pool = await sql.connect(config.sql);
    transaction = new sql.Transaction(pool);
    await transaction.begin();
    const sqlQueries = await utils.loadSqlQueries("whatif");
    const removedLock = await pool
      .request(transaction)
      .input("user_id", sql.VarChar, email)
      .query(sqlQueries.deleteWhatifLocks);

    await transaction.commit();
    return { response: true };
  } catch (error) {
    logger.error({
      username: name,
      type: "error",
      description: `User with email ${email} failed to fetch whatif locks: "${error.message}"`,
      item_id: null,
      module_id: 3,
    });
    if (transaction) {
      await transaction.rollback();
    }
    console.error("error", error);
  }
};

const addLock = async (payload, name, email) => {
  let transaction;
  try {
    const io = socket.getIO();

    payload.email = email;
    payload.name = name;

    io.emit("lockAdded", payload);

    let pool = await sql.connect(config.sql);
    transaction = new sql.Transaction(pool);
    await transaction.begin();
    const sqlQueries = await utils.loadSqlQueries("whatif");
    await pool
      .request(transaction)
      .input("user_id", sql.VarChar, email)
      .query(sqlQueries.deleteWhatifLocks);

    const addedLock = await pool
      .request(transaction)
      .input("customer_id", sql.VarChar, payload.customer_id)
      .input("pkey", sql.VarChar, payload.pkey)
      .input("user_id", sql.VarChar, email)
      .input("user_name", sql.VarChar, name)
      .query(sqlQueries.addWhatifLock);

    await transaction.commit();
    return { response: true };
  } catch (error) {
    logger.error({
      username: name,
      type: "error",
      description: `User with email ${email} failed to add a lock: "${error.message}"`,
      item_id: null,
      module_id: 3,
    });
    if (transaction) {
      await transaction.rollback();
    }
    console.error("error", error);
    return { response: null };
  } finally {
    if (transaction) {
      transaction = null;
    }
  }
};

const getIntialCustomerData = async (
  whatifCustomers,
  financialYear,
  name,
  email
) => {
  let transaction;
  try {
    let pool = await sql.connect(config.sql);
    transaction = new sql.Transaction(pool);
    await transaction.begin();
    const sqlQueries = await utils.loadSqlQueries("whatif");
    const whatifDataByCustomers = await getWhatifByCustomer(
      whatifCustomers,
      financialYear,
      "current"
    );
    await transaction.commit();
    return whatifDataByCustomers;
  } catch (error) {
    logger.error({
      username: name,
      type: "error",
      description: `User with email ${email} failed to fetch initial customer data: "${error.message}"`,
      item_id: null,
      module_id: 3,
    });
    if (transaction) {
      await transaction.rollback();
    }
    console.error("error", error);
  }
};

//TODO function to convert it to excel and save 
// const exportWhatIfData = async (
//   whatifCustomers,
//   financialYear,
//   name,
//   email,
// ) => {
//   console.log("thsi is tshe fucntions in thre index.kjs",financialYear);
//   const whatifDataByCustomers = await getWhatifByCustomer(
//     whatifCustomers,
//     financialYear,
//     "1,2,3,4"
//   );

//   try {
//     // Ensure data is in JSON format
//     const jsonData = typeof whatifDataByCustomers === 'string'
//       ? JSON.parse(whatifDataByCustomers) // Parse string to JSON if necessary
//       : whatifDataByCustomers; // Use as-is if already a valid object/array
  
//     // Write the data to a file in JSON format
//     fs.writeFile(
//       'Output.json', // Save with a .json extension
//       JSON.stringify(jsonData, null, 2), // Format the JSON with indentation
//       (err) => {
//         if (err) {
//           console.error('Error writing to file:', err);
//           throw err;
//         }
//         console.log('Filtered data successfully written to Output.json');
//       }
//     );
//     return jsonData;
//   } catch (error) {
//     console.error('Error processing data:', error.message);
//     return;
//   }
// };

const getIntialData = async (name, email, cust_code, financialYear) => {
  let transaction;
  try {
    let pool = await sql.connect(config.sql);
    transaction = new sql.Transaction(pool);
    await transaction.begin();
    const sqlQueries = await utils.loadSqlQueries("whatif");
    let allInitialData = {};

    const currentQuarterCalendarData = await getCurrentQuarterCalendarData(
      name,
      email,
      financialYear
    );
    const currentQuarterStdCalendarData =
      await getCurrentQuarterStdCalendarData(name, email, financialYear);
    const whatifCustomers = await getWhatifCustomers(name, email);
    const whatifTotalsByCustomers = await getWhatifTotalsByCustomer(
      cust_code == "null" ? whatifCustomers[0]?.value : cust_code,
      name,
      email,
      financialYear
    );
    allInitialData.currentQuarterCalendarData = currentQuarterCalendarData;
    allInitialData.currentQuarterStdCalendarData =
      currentQuarterStdCalendarData;
    allInitialData.whatifCustomers = whatifCustomers;
    allInitialData.whatifTotalsByCustomers = whatifTotalsByCustomers;

    await transaction.commit();

    return allInitialData;
  } catch (error) {
    logger.error({
      username: name,
      type: "error",
      description: `User with email ${email} failed to fetch whatif initial data: "${error.message}"`,
      item_id: null,
      module_id: 3,
    });
    if (transaction) {
      await transaction.rollback();
    }
    console.error("error", error);
  }
};
const getNextQuarterProductDataByWeek = async (
  pkey,
  weekNo,
  name,
  email,
  financialYear
) => {
  let transaction;
  try {
    let pool = await sql.connect(config.sql);
    transaction = new sql.Transaction(pool);
    await transaction.begin();
    const sqlQueries = await utils.loadSqlQueries("whatif");
    const nextQuarterData = await pool
      .request(transaction)
      .input("pkey", sql.VarChar, pkey)
      .input("nextWeek", sql.Int, weekNo)
      .input("financialYear", sql.VarChar, financialYear)
      .query(sqlQueries.getQuarterDataByWeekNo);
    const calendarData = await pool
      .request(transaction)
      .input("WeekNO", sql.Int, weekNo)
      .query(sqlQueries.getCalendarDataByWeek);
    const formatedWhatif = formatWhatifDataNextWeek(
      nextQuarterData,
      calendarData.recordset
    );

    await transaction.commit();
    return formatedWhatif;
  } catch (error) {
    logger.error({
      username: name,
      type: "error",
      description: `User with email ${email} failed to fetch whatif next quarter product data by week: "${error.message}"`,
      item_id: null,
      module_id: 3,
    });
    console.error(error);
    if (transaction) {
      await transaction.rollback();
    }
    console.error("error", error);
  }
};
const getNextPromosByPkeyWhatifId = async (
  pkey,
  whatif_id,
  weekNo,
  name,
  email
) => {
  let transaction;
  try {

    let pool = await sql.connect(config.sql);
    transaction = new sql.Transaction(pool);
    await transaction.begin();
    const sqlQueries = await utils.loadSqlQueries("whatif");
    const nextPromosByPkeyWhatifId = await pool
      .request(transaction)
      .input("pkey", sql.VarChar, pkey)
      .input(
        "whatif_id",
        whatif_id !== "undefined" &&
          whatif_id !== "null" &&
          whatif_id !== undefined &&
          whatif_id !== null
          ? sql.Int
          : sql.NVarChar,
        whatif_id !== "undefined" &&
          whatif_id !== "null" &&
          whatif_id !== undefined &&
          whatif_id !== null
          ? whatif_id
          : null
      )
      .input("weekNo", sql.Int, weekNo)
      .query(sqlQueries.getNextPromosByPkeyWhatifId);

    await transaction.commit();

    return nextPromosByPkeyWhatifId.recordset;
  } catch (error) {
    logger.error({
      username: name,
      type: "error",
      description: `User with email ${email} failed to fetch whatif next quarter product data by week: "${error.message}"`,
      item_id: null,
      module_id: 3,
    });
    console.error(error);
    if (transaction) {
      await transaction.rollback();
    }
    console.error("error", error);
  }
};

module.exports = {
  getWhatifByCustomer,
  getWhatifTotalsByCustomer,
  getWhatifProductByCustomer,
  getWhatifByQuarter,
  getDefaultCalendarData,
  getstdCalendarData,
  getWhatifCustomers,
  getNextQuarterProductDataByWeek,
  getWhatifProducts,
  addWhatif,
  removeWhatif,
  getBreakevenStatuses,
  getTaskTypes,
  removeLocks,
  addLock,
  getCurrentQuarterCalendarData,
  getCurrentQuarterStdCalendarData,
  getIntialData,
  getIntialCustomerData,
  // exportWhatIfData,
  getNextPromosByPkeyWhatifId,
};
