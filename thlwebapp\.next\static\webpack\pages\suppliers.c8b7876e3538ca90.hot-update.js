"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/suppliers",{

/***/ "./pages/suppliers.js":
/*!****************************!*\
  !*** ./pages/suppliers.js ***!
  \****************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __N_SSP: function() { return /* binding */ __N_SSP; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _components_Layout__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/Layout */ \"./components/Layout.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _fortawesome_react_fontawesome__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @fortawesome/react-fontawesome */ \"./node_modules/@fortawesome/react-fontawesome/index.es.js\");\n/* harmony import */ var _fortawesome_free_solid_svg_icons__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! @fortawesome/free-solid-svg-icons */ \"./node_modules/@fortawesome/free-solid-svg-icons/index.mjs\");\n/* harmony import */ var ag_grid_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ag-grid-react */ \"./node_modules/ag-grid-react/lib/main.js\");\n/* harmony import */ var ag_grid_react__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(ag_grid_react__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var ag_grid_community_styles_ag_grid_css__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ag-grid-community/styles//ag-grid.css */ \"./node_modules/ag-grid-community/styles/ag-grid.css\");\n/* harmony import */ var ag_grid_community_styles_ag_grid_css__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(ag_grid_community_styles_ag_grid_css__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var ag_grid_community_styles_ag_theme_alpine_css__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ag-grid-community/styles//ag-theme-alpine.css */ \"./node_modules/ag-grid-community/styles/ag-theme-alpine.css\");\n/* harmony import */ var ag_grid_community_styles_ag_theme_alpine_css__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(ag_grid_community_styles_ag_theme_alpine_css__WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var _utils_renderer_actionRenderer__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../utils/renderer/actionRenderer */ \"./utils/renderer/actionRenderer.js\");\n/* harmony import */ var _utils_renderer_nameRenderer__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ../utils/renderer/nameRenderer */ \"./utils/renderer/nameRenderer.js\");\n/* harmony import */ var _utils_renderer_statusRenderer__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ../utils/renderer/statusRenderer */ \"./utils/renderer/statusRenderer.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! next/link */ \"./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_10___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_10__);\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_11___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_11__);\n/* harmony import */ var _services_apiConfig__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/services/apiConfig */ \"./services/apiConfig.js\");\n/* harmony import */ var js_cookie__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! js-cookie */ \"./node_modules/js-cookie/dist/js.cookie.mjs\");\n/* harmony import */ var _utils_auth_auth__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/utils/auth/auth */ \"./utils/auth/auth.js\");\n/* harmony import */ var react_toastify__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! react-toastify */ \"./node_modules/react-toastify/dist/react-toastify.esm.mjs\");\n/* harmony import */ var react_toastify_dist_ReactToastify_css__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! react-toastify/dist/ReactToastify.css */ \"./node_modules/react-toastify/dist/ReactToastify.css\");\n/* harmony import */ var react_toastify_dist_ReactToastify_css__WEBPACK_IMPORTED_MODULE_16___default = /*#__PURE__*/__webpack_require__.n(react_toastify_dist_ReactToastify_css__WEBPACK_IMPORTED_MODULE_16__);\n/* harmony import */ var _utils_loaders_loadingContext__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! @/utils/loaders/loadingContext */ \"./utils/loaders/loadingContext.js\");\n/* harmony import */ var _utils_getCookieData__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! @/utils/getCookieData */ \"./utils/getCookieData.js\");\n/* harmony import */ var _azure_msal_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! @azure/msal-react */ \"./node_modules/@azure/msal-react/dist/index.js\");\n/* harmony import */ var _utils_userContext__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! @/utils/userContext */ \"./utils/userContext.js\");\n/* harmony import */ var _utils_rolePermissionsContext__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! @/utils/rolePermissionsContext */ \"./utils/rolePermissionsContext.js\");\n/* harmony import */ var _barrel_optimize_names_Dialog_Transition_headlessui_react__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=Dialog,Transition!=!@headlessui/react */ \"__barrel_optimize__?names=Dialog,Transition!=!./node_modules/@headlessui/react/dist/headlessui.esm.js\");\n/* harmony import */ var _utils_exportExcel__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! ../utils/exportExcel */ \"./utils/exportExcel.js\");\n/* harmony import */ var _components_supplierCodeRenderer__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! @/components/supplierCodeRenderer */ \"./components/supplierCodeRenderer.js\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n//import 'ag-grid-enterprise';\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst suppliers = (param)=>{\n    let { userData, token } = param;\n    _s();\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_11__.useRouter)();\n    const { permissions, updatePermissions } = (0,_utils_rolePermissionsContext__WEBPACK_IMPORTED_MODULE_21__.usePermissions)();\n    const [pageSize, setPageSize] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(15);\n    const [allRowData, setAllRowData] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [rowData, setRowData] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [isFiltered, setIsFiltered] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [isFilteredName, setIsFilteredName] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const gridRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)();\n    const { setIsLoading } = (0,_utils_loaders_loadingContext__WEBPACK_IMPORTED_MODULE_17__.useLoading)();\n    const [supplierRoles, setSupplierRoles] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [multipleFilterInternalData, setMultipleFilterInternalData] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [containsCancelledSupplier, setContainsCancelledSupplier] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [exportDisabled, setExportDisabled] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(true);\n    const [supplierCheckedValue, setSupplierCheckedValue] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const { instance, accounts } = (0,_azure_msal_react__WEBPACK_IMPORTED_MODULE_19__.useMsal)();\n    const { userDetails } = (0,_utils_userContext__WEBPACK_IMPORTED_MODULE_20__.useUser)();\n    const [isCommonError, setCommonError] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"\");\n    const [isOpenOption, setIsOpenOption] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [isUnExportable, setIsUnExportable] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [unExportableSuppliernames, setUnExportableSupplierNames] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"\");\n    const [supplierUniqueCodeToast, setSupplierUniqueCodeToast] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [supplierCodeValid, setSupplierCodeValid] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(true);\n    const [selectedExportType, setSelectedExportType] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"\");\n    const [prophetId, setProphetId] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"\");\n    const [emailStatusPopup, setEmailStatusPopup] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [popupMessage, setPopUpMessage] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"\");\n    const [internalExportSuccess, setInternalExportSuccess] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [ISSExportSuccess, setISSExportSuccess] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [blockScreen, setBlockScreen] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const user = (0,_utils_getCookieData__WEBPACK_IMPORTED_MODULE_18__.getCookieData)(\"user\");\n    const [selectedStatus, setSelectedStatus] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"default\"); // State to track selected status\n    const closeOptionModal = ()=>{\n        setIsOpenOption(false);\n    };\n    const handleExportType = (e)=>{\n        setSelectedExportType(e.target.value);\n    };\n    const closeEmailPopup = ()=>{\n        setEmailStatusPopup(false);\n        if (internalExportSuccess && ISSExportSuccess) {\n            setStatusChange();\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        if (containsCancelledSupplier) {\n            react_toastify__WEBPACK_IMPORTED_MODULE_15__.toast.error(//todo: change this message to make it better\n            \"Cannot export cancelled supplier.\");\n            setExportDisabled(true);\n        }\n    }, [\n        containsCancelledSupplier\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        if (supplierUniqueCodeToast && !supplierCodeValid) {\n            react_toastify__WEBPACK_IMPORTED_MODULE_15__.toast.error(//todo: change this message to make it better\n            \"Supplier code is not unique and not valid, kindly make sure the supplier code is unique and valid.\");\n        } else if (supplierUniqueCodeToast) {\n            react_toastify__WEBPACK_IMPORTED_MODULE_15__.toast.error(//todo: change this message to make it better\n            \"Supplier code is not unique, kindly make sure the supplier code is unique.\");\n        } else if (!supplierCodeValid) {\n            react_toastify__WEBPACK_IMPORTED_MODULE_15__.toast.error(\"Supplier code is not valid\");\n        }\n    }, [\n        supplierUniqueCodeToast,\n        supplierCodeValid\n    ]);\n    const [company, setCompany] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"\");\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        if (typeof document !== \"undefined\") {\n            document.title = \"Suppliers\";\n        }\n        if (true) {\n            const company = js_cookie__WEBPACK_IMPORTED_MODULE_13__[\"default\"].get(\"company\");\n            if (company) {\n                setCompany(company);\n            }\n            localStorage.removeItem(\"current\");\n            localStorage.removeItem(\"allowedSections\");\n        }\n        js_cookie__WEBPACK_IMPORTED_MODULE_13__[\"default\"].remove(\"prophet\");\n        js_cookie__WEBPACK_IMPORTED_MODULE_13__[\"default\"].remove(\"prophets\");\n        setIsLoading(false);\n        getData(userData).then((data)=>{\n            if (data === null) {\n                return;\n            }\n            const formattedData = data === null || data === void 0 ? void 0 : data.map((row)=>{\n                const roleIds = (row === null || row === void 0 ? void 0 : row.role_ids) ? JSON.parse(row === null || row === void 0 ? void 0 : row.role_ids).map((ele)=>ele.role_id) : [];\n                const roleJson = JSON.parse(row === null || row === void 0 ? void 0 : row.role_json);\n                const formattedRow = {\n                    isActive: row === null || row === void 0 ? void 0 : row.is_active,\n                    prophets: (row === null || row === void 0 ? void 0 : row.prophet_ids) ? JSON.parse(row === null || row === void 0 ? void 0 : row.prophet_ids) : [],\n                    supplier_code: (row === null || row === void 0 ? void 0 : row.prophet_ids) ? JSON.parse(row === null || row === void 0 ? void 0 : row.prophet_ids)[0].prophet_code : [],\n                    company_name: (row === null || row === void 0 ? void 0 : row.name) ? row === null || row === void 0 ? void 0 : row.name : \"Not Entered\",\n                    country_code: row === null || row === void 0 ? void 0 : row.country_code,\n                    payment_type: row === null || row === void 0 ? void 0 : row.payment_type,\n                    payment_type_name: row === null || row === void 0 ? void 0 : row.payment_type_name,\n                    currency_name: row === null || row === void 0 ? void 0 : row.currency_name,\n                    currency_id: row === null || row === void 0 ? void 0 : row.iss_currency_id,\n                    global_gap_number: row === null || row === void 0 ? void 0 : row.global_gap_number,\n                    chile_certificate_number: row === null || row === void 0 ? void 0 : row.chile_certificate_number,\n                    red_tractor: row === null || row === void 0 ? void 0 : row.red_tractor,\n                    organic_certificate_number: row === null || row === void 0 ? void 0 : row.organic_certificate_number,\n                    puc_code: row === null || row === void 0 ? void 0 : row.puc_code,\n                    address_line_1: row === null || row === void 0 ? void 0 : row.address_line_1,\n                    address_line_2: row === null || row === void 0 ? void 0 : row.address_line_2,\n                    address_line_3: row === null || row === void 0 ? void 0 : row.address_line_3,\n                    address_line_4: row === null || row === void 0 ? void 0 : row.address_line_4,\n                    postal_code: row === null || row === void 0 ? void 0 : row.postal_code,\n                    id: row === null || row === void 0 ? void 0 : row.id,\n                    currency: (row === null || row === void 0 ? void 0 : row.currency) ? row === null || row === void 0 ? void 0 : row.currency : \"Not Entered\",\n                    currency_code: (row === null || row === void 0 ? void 0 : row.currency_code) ? row === null || row === void 0 ? void 0 : row.currency_code : \"\",\n                    Compliance: row === null || row === void 0 ? void 0 : row.compliance,\n                    Financials: row === null || row === void 0 ? void 0 : row.financial,\n                    General: row === null || row === void 0 ? void 0 : row.technical,\n                    Procurement: row === null || row === void 0 ? void 0 : row.procurement,\n                    requestor: row === null || row === void 0 ? void 0 : row.requestor_name,\n                    requestor_email: row === null || row === void 0 ? void 0 : row.requestor_email,\n                    companies: (row === null || row === void 0 ? void 0 : row.prophet_names) ? row === null || row === void 0 ? void 0 : row.prophet_names : \"Not Entered\",\n                    role: (row === null || row === void 0 ? void 0 : row.role_names) ? row === null || row === void 0 ? void 0 : row.role_names : \"Not Entered\",\n                    roleJson: (row === null || row === void 0 ? void 0 : row.role_json) ? JSON.parse(row === null || row === void 0 ? void 0 : row.role_json) : [],\n                    roleId: (row === null || row === void 0 ? void 0 : row.role_ids) ? JSON.parse(row === null || row === void 0 ? void 0 : row.role_ids) : [],\n                    roleIds: (row === null || row === void 0 ? void 0 : row.role_ids) ? JSON.parse(row === null || row === void 0 ? void 0 : row.role_ids).map((ele)=>{\n                        return ele.role_id;\n                    }) : [],\n                    supplier_type: row === null || row === void 0 ? void 0 : row.supplier_type_label,\n                    contacts_json: row === null || row === void 0 ? void 0 : row.contacts_json,\n                    distribution_points_json: (row === null || row === void 0 ? void 0 : row.distribution_points_json) ? JSON.parse(row === null || row === void 0 ? void 0 : row.distribution_points_json) : [],\n                    vat_number: row === null || row === void 0 ? void 0 : row.vat_number,\n                    payment_terms: row === null || row === void 0 ? void 0 : row.payment_terms,\n                    sort_bic: row === null || row === void 0 ? void 0 : row.decryptedSort_Bic,\n                    name_branch: row === null || row === void 0 ? void 0 : row.decryptedName_branch,\n                    account_number: row === null || row === void 0 ? void 0 : row.decryptedAccountNumber,\n                    vatable: row === null || row === void 0 ? void 0 : row.vatable,\n                    iss_ledger_code: row === null || row === void 0 ? void 0 : row.iss_ledger_code,\n                    internal_ledger_code: row === null || row === void 0 ? void 0 : row.internal_ledger_code,\n                    intermediatery_account_number: row === null || row === void 0 ? void 0 : row.decryptedIntermediatery_account_number,\n                    bacs_currency_code: row === null || row === void 0 ? void 0 : row.bacs_currency_code,\n                    has_iban: row === null || row === void 0 ? void 0 : row.has_iban,\n                    isProducerSupplier: row === null || row === void 0 ? void 0 : row.product_supplier,\n                    isEmergencyRequest: row === null || row === void 0 ? void 0 : row.emergency_request,\n                    supplier_group: row === null || row === void 0 ? void 0 : row.sendac_groups_json,\n                    supplierLinks: row === null || row === void 0 ? void 0 : row.supplier_links_json,\n                    status: row === null || row === void 0 ? void 0 : row.label,\n                    role_num: row === null || row === void 0 ? void 0 : row.role_nums,\n                    edi: row === null || row === void 0 ? void 0 : row.edi\n                };\n                if ((roleIds.includes(1) || (roleIds === null || roleIds === void 0 ? void 0 : roleIds.includes(6))) && (row === null || row === void 0 ? void 0 : row.emergency_request) && (row === null || row === void 0 ? void 0 : row.financial) !== \"Completed\") {\n                    formattedRow.isEmergencyAndFinanceNotComplete = true;\n                } else {\n                    formattedRow.isEmergencyAndFinanceNotComplete = false;\n                }\n                return formattedRow;\n            });\n            setAllRowData(formattedData);\n            const filteredData = formattedData.length > 0 ? formattedData === null || formattedData === void 0 ? void 0 : formattedData.filter((row)=>row.status !== \"Cancelled\" && row.status !== \"Exported\") : [];\n            setRowData(filteredData);\n            const fetchRolePermissions = async ()=>{\n                try {\n                    let serverAddress = _services_apiConfig__WEBPACK_IMPORTED_MODULE_12__.apiConfig.serverAddress;\n                    const response = await fetch(\"\".concat(serverAddress, \"suppliers/get-role-permissions\"), {\n                        method: \"GET\",\n                        headers: {\n                            Authorization: \"Bearer \".concat(token)\n                        }\n                    });\n                    if (!response.ok) {\n                        throw new Error(\"Request failed with status \".concat(response.status));\n                    }\n                    const result = await response.json();\n                    const rolePermissions = {};\n                    for (const row of result){\n                        var _row_sections_split, _row_sections;\n                        const sectionsArray = row === null || row === void 0 ? void 0 : (_row_sections = row.sections) === null || _row_sections === void 0 ? void 0 : (_row_sections_split = _row_sections.split(\",\")) === null || _row_sections_split === void 0 ? void 0 : _row_sections_split.filter((section)=>(section === null || section === void 0 ? void 0 : section.trim()) !== \"\"); // Split sections string into an array and remove empty values\n                        rolePermissions[row === null || row === void 0 ? void 0 : row.role_id] = sectionsArray;\n                    }\n                    updatePermissions(rolePermissions);\n                } catch (error) {\n                    console.error(error);\n                // setCommonError(error.message);\n                }\n            };\n            fetchRolePermissions();\n        }).catch((error)=>{\n            console.log(error);\n        });\n    }, []);\n    function getData() {\n        const company = js_cookie__WEBPACK_IMPORTED_MODULE_13__[\"default\"].get(\"company\");\n        let serverAddress = _services_apiConfig__WEBPACK_IMPORTED_MODULE_12__.apiConfig.serverAddress;\n        const ADCompanyName = js_cookie__WEBPACK_IMPORTED_MODULE_13__[\"default\"].get(\"ADCompanyName\");\n        let prophetId = 0;\n        if (ADCompanyName == \"FPP\") {\n            prophetId = 4;\n        } else if (ADCompanyName == \"EFC\") {\n            prophetId = 3;\n        } else if (ADCompanyName == \"DPS\") {\n            prophetId = 1;\n        } else if (ADCompanyName == \"DPS MS\") {\n            prophetId = 2;\n        }\n        console.log(\"prophetId\", prophetId);\n        return fetch(\"\".concat(serverAddress, \"suppliers/get-suppliers/\").concat(company, \"/\").concat(prophetId), {\n            method: \"GET\",\n            headers: {\n                Authorization: \"Bearer \".concat(token)\n            }\n        }).then(async (res)=>{\n            if (res.status == 502) {\n                setBlockScreen(true);\n                return;\n            }\n            setBlockScreen(false);\n            if (res.status === 400) {\n                react_toastify__WEBPACK_IMPORTED_MODULE_15__.toast.error(\"There was an error with your request. Please check your data and try again.\");\n                return null;\n            } else if (res.status === 401) {\n                react_toastify__WEBPACK_IMPORTED_MODULE_15__.toast.error(\"Your session has expired. Please log in again.\");\n                setTimeout(()=>{\n                    localStorage.removeItem(\"superUser\");\n                    js_cookie__WEBPACK_IMPORTED_MODULE_13__[\"default\"].remove(\"company\");\n                    js_cookie__WEBPACK_IMPORTED_MODULE_13__[\"default\"].remove(\"ADCompanyName\");\n                    localStorage.removeItem(\"id\");\n                    localStorage.removeItem(\"name\");\n                    localStorage.removeItem(\"role\");\n                    localStorage.removeItem(\"email\");\n                    js_cookie__WEBPACK_IMPORTED_MODULE_13__[\"default\"].remove(\"user\");\n                    js_cookie__WEBPACK_IMPORTED_MODULE_13__[\"default\"].remove(\"theme\");\n                    js_cookie__WEBPACK_IMPORTED_MODULE_13__[\"default\"].remove(\"token\");\n                    const redirectUrl = \"/login?redirect=\".concat(encodeURIComponent(window.location.pathname));\n                    (0,_utils_auth_auth__WEBPACK_IMPORTED_MODULE_14__.logoutHandler)(instance, redirectUrl);\n                }, 3000);\n                return null;\n            }\n            if (res.status === 200) {\n                return res.json();\n            }\n            throw new Error(\"Failed to fetch data\");\n        }).catch((error)=>{\n            setCommonError(error.message);\n        });\n    }\n    function deleteAll() {\n        setIsLoading(true);\n        let serverAddress = _services_apiConfig__WEBPACK_IMPORTED_MODULE_12__.apiConfig.serverAddress;\n        return fetch(\"\".concat(serverAddress, \"suppliers/delete-all\"), {\n            method: \"DELETE\",\n            headers: {\n                Authorization: \"Bearer \".concat(token)\n            }\n        }).then(async (res)=>{\n            if (res.status === 400) {\n                setIsLoading(false);\n                react_toastify__WEBPACK_IMPORTED_MODULE_15__.toast.error(\"There was an error with your request. Please check your data and try again.\");\n            } else if (res.status === 401) {\n                react_toastify__WEBPACK_IMPORTED_MODULE_15__.toast.error(\"Your session has expired. Please log in again.\");\n                setTimeout(()=>{\n                    setIsLoading(false);\n                    localStorage.removeItem(\"superUser\");\n                    js_cookie__WEBPACK_IMPORTED_MODULE_13__[\"default\"].remove(\"company\");\n                    js_cookie__WEBPACK_IMPORTED_MODULE_13__[\"default\"].remove(\"ADCompanyName\");\n                    localStorage.removeItem(\"id\");\n                    localStorage.removeItem(\"name\");\n                    localStorage.removeItem(\"role\");\n                    localStorage.removeItem(\"email\");\n                    js_cookie__WEBPACK_IMPORTED_MODULE_13__[\"default\"].remove(\"user\");\n                    js_cookie__WEBPACK_IMPORTED_MODULE_13__[\"default\"].remove(\"theme\");\n                    js_cookie__WEBPACK_IMPORTED_MODULE_13__[\"default\"].remove(\"token\");\n                    const redirectUrl = \"/login?redirect=\".concat(encodeURIComponent(window.location.pathname));\n                    (0,_utils_auth_auth__WEBPACK_IMPORTED_MODULE_14__.logoutHandler)(instance, redirectUrl);\n                }, 3000);\n            }\n            if (res.status === 200) {\n                react_toastify__WEBPACK_IMPORTED_MODULE_15__.toast.info(\"Delete successfull\");\n                setIsLoading(false);\n                router.reload();\n            }\n            throw new Error(\"Failed to delete\");\n        }).catch((error)=>{\n            setIsLoading(false);\n            console.error(error);\n        });\n    }\n    const handlePageSizeChange = (event)=>{\n        const newPageSize = parseInt(event.target.value, 15);\n        setPageSize(newPageSize);\n        gridRef.current.api.paginationSetPageSize(newPageSize);\n    };\n    const defaultColDef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useMemo)(()=>({\n            sortable: true,\n            filter: false,\n            resizable: true,\n            flex: 1\n        }));\n    const gridOptions = {\n        responsive: true\n    };\n    const CustomCellRenderer = (params)=>{\n        const truncatedText = params === null || params === void 0 ? void 0 : params.value;\n        // params?.value && params?.value?.length > 12\n        //   ? params?.value?.substring(0, 12) + \"...\"\n        //   : params?.value;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n            title: params === null || params === void 0 ? void 0 : params.value,\n            children: truncatedText\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\suppliers.js\",\n            lineNumber: 403,\n            columnNumber: 12\n        }, undefined);\n    };\n    const setStatusChange = ()=>{\n        setIsLoading(true);\n        setTimeout(function() {\n            getData(userData).then((data)=>{\n                if (data === null) {\n                    return;\n                }\n                const formattedData = data === null || data === void 0 ? void 0 : data.map((row)=>{\n                    const roleIds = (row === null || row === void 0 ? void 0 : row.role_ids) ? JSON.parse(row === null || row === void 0 ? void 0 : row.role_ids).map((ele)=>ele.role_id) : [];\n                    const roleJson = JSON.parse(row === null || row === void 0 ? void 0 : row.role_json);\n                    const formattedRow = {\n                        isActive: row === null || row === void 0 ? void 0 : row.is_active,\n                        prophets: (row === null || row === void 0 ? void 0 : row.prophet_ids) ? JSON.parse(row === null || row === void 0 ? void 0 : row.prophet_ids) : [],\n                        supplier_code: (row === null || row === void 0 ? void 0 : row.prophet_ids) ? JSON.parse(row === null || row === void 0 ? void 0 : row.prophet_ids)[0].prophet_code : [],\n                        company_name: (row === null || row === void 0 ? void 0 : row.name) ? row === null || row === void 0 ? void 0 : row.name : \"Not Entered\",\n                        id: row === null || row === void 0 ? void 0 : row.id,\n                        currency: (row === null || row === void 0 ? void 0 : row.currency) ? row === null || row === void 0 ? void 0 : row.currency : \"Not Entered\",\n                        Compliance: row === null || row === void 0 ? void 0 : row.compliance,\n                        Financials: row === null || row === void 0 ? void 0 : row.financial,\n                        General: row === null || row === void 0 ? void 0 : row.technical,\n                        Procurement: row === null || row === void 0 ? void 0 : row.procurement,\n                        requestor: row === null || row === void 0 ? void 0 : row.requestor_name,\n                        companies: (row === null || row === void 0 ? void 0 : row.prophet_names) ? row === null || row === void 0 ? void 0 : row.prophet_names : \"Not Entered\",\n                        role: (row === null || row === void 0 ? void 0 : row.role_names) ? row === null || row === void 0 ? void 0 : row.role_names : \"Not Entered\",\n                        roleId: (row === null || row === void 0 ? void 0 : row.role_ids) ? JSON.parse(row === null || row === void 0 ? void 0 : row.role_ids) : [],\n                        roleIds: (row === null || row === void 0 ? void 0 : row.role_ids) ? JSON.parse(row === null || row === void 0 ? void 0 : row.role_ids).map((ele)=>{\n                            return ele.role_id;\n                        }) : [],\n                        country_code: row === null || row === void 0 ? void 0 : row.country_code,\n                        payment_type: row === null || row === void 0 ? void 0 : row.payment_type,\n                        payment_type_name: row === null || row === void 0 ? void 0 : row.payment_type_name,\n                        currency_name: row === null || row === void 0 ? void 0 : row.currency_name,\n                        currency_id: row === null || row === void 0 ? void 0 : row.iss_currency_id,\n                        global_gap_number: row === null || row === void 0 ? void 0 : row.global_gap_number,\n                        chile_certificate_number: row === null || row === void 0 ? void 0 : row.chile_certificate_number,\n                        red_tractor: row === null || row === void 0 ? void 0 : row.red_tractor,\n                        organic_certificate_number: row === null || row === void 0 ? void 0 : row.organic_certificate_number,\n                        puc_code: row === null || row === void 0 ? void 0 : row.puc_code,\n                        address_line_1: row === null || row === void 0 ? void 0 : row.address_line_1,\n                        address_line_2: row === null || row === void 0 ? void 0 : row.address_line_2,\n                        address_line_3: row === null || row === void 0 ? void 0 : row.address_line_3,\n                        address_line_4: row === null || row === void 0 ? void 0 : row.address_line_4,\n                        postal_code: row === null || row === void 0 ? void 0 : row.postal_code,\n                        currency_code: (row === null || row === void 0 ? void 0 : row.currency_code) ? row === null || row === void 0 ? void 0 : row.currency_code : \"\",\n                        requestor_email: row === null || row === void 0 ? void 0 : row.requestor_email,\n                        companies: (row === null || row === void 0 ? void 0 : row.prophet_names) ? row === null || row === void 0 ? void 0 : row.prophet_names : \"Not Entered\",\n                        vatable: row === null || row === void 0 ? void 0 : row.vatable,\n                        roleJson: (row === null || row === void 0 ? void 0 : row.role_json) ? JSON.parse(row === null || row === void 0 ? void 0 : row.role_json) : [],\n                        supplier_type: row === null || row === void 0 ? void 0 : row.supplier_type_label,\n                        isProducerSupplier: row === null || row === void 0 ? void 0 : row.product_supplier,\n                        isEmergencyRequest: row === null || row === void 0 ? void 0 : row.emergency_request,\n                        supplier_group: row === null || row === void 0 ? void 0 : row.sendac_groups_json,\n                        supplierLinks: row === null || row === void 0 ? void 0 : row.supplier_links_json,\n                        status: row === null || row === void 0 ? void 0 : row.label,\n                        role_num: row === null || row === void 0 ? void 0 : row.role_nums,\n                        contacts_json: row === null || row === void 0 ? void 0 : row.contacts_json,\n                        distribution_points_json: (row === null || row === void 0 ? void 0 : row.distribution_points_json) ? JSON.parse(row === null || row === void 0 ? void 0 : row.distribution_points_json) : [],\n                        vat_number: row === null || row === void 0 ? void 0 : row.vat_number,\n                        payment_terms: row === null || row === void 0 ? void 0 : row.payment_terms,\n                        sort_bic: row === null || row === void 0 ? void 0 : row.decryptedSort_Bic,\n                        name_branch: row === null || row === void 0 ? void 0 : row.decryptedName_branch,\n                        account_number: row === null || row === void 0 ? void 0 : row.decryptedAccountNumber,\n                        iss_ledger_code: row === null || row === void 0 ? void 0 : row.iss_ledger_code,\n                        internal_ledger_code: row === null || row === void 0 ? void 0 : row.internal_ledger_code,\n                        intermediatery_account_number: row === null || row === void 0 ? void 0 : row.decryptedIntermediatery_account_number,\n                        bacs_currency_code: row === null || row === void 0 ? void 0 : row.bacs_currency_code,\n                        has_iban: row === null || row === void 0 ? void 0 : row.has_iban,\n                        edi: row === null || row === void 0 ? void 0 : row.edi\n                    };\n                    if (((roleIds === null || roleIds === void 0 ? void 0 : roleIds.includes(1)) || (roleIds === null || roleIds === void 0 ? void 0 : roleIds.includes(6))) && (row === null || row === void 0 ? void 0 : row.emergency_request) && (row === null || row === void 0 ? void 0 : row.financial) !== \"Completed\") {\n                        formattedRow.isEmergencyAndFinanceNotComplete = true;\n                    } else {\n                        formattedRow.isEmergencyAndFinanceNotComplete = false;\n                    }\n                    return formattedRow;\n                });\n                setAllRowData(formattedData);\n                const filteredData = formattedData.length > 0 ? formattedData === null || formattedData === void 0 ? void 0 : formattedData.filter((row)=>row.status !== \"Completed\" && row.status !== \"Cancelled\" && row.status !== \"Exported\") : [];\n                setRowData(filteredData);\n                setIsLoading(false);\n            }).catch((error)=>{\n                console.log(error);\n                setIsLoading(false);\n            });\n        }, 3000);\n    // console.log('updated id', id)\n    // console.log('updated status', status)\n    // console.log(rowData)\n    // const find_supplier = rowData.filter((item) => item.id === id)\n    // const filterWithId = rowData.filter((item) => item.id !== id)\n    // console.log(find_supplier)\n    // //const rowNode = gridRef.current.api.getRowNode(id.toString());\n    // //console.log(rowNode);\n    // //rowNode.setRowData(...rowData, find_supplier);\n    // setRowData([...filterWithId, ...find_supplier]);\n    };\n    const CustomTooltipComponent = (param)=>/*#__PURE__*/ {\n        let { value } = param;\n        return (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            title: value,\n            children: value\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\suppliers.js\",\n            lineNumber: 538,\n            columnNumber: 5\n        }, undefined);\n    };\n    const columnDefs = [\n        {\n            headerName: \"Supplier Name\",\n            field: \"company_name\",\n            // checkboxSelection: true,\n            checkboxSelection: (params)=>{\n                return params.data.status === \"Cancelled\" ? {\n                    checked: false,\n                    disabled: true\n                } : true;\n            },\n            cellRenderer: _utils_renderer_nameRenderer__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n            headerCheckboxSelection: true,\n            //suppressMenu: true,\n            //suppressSizeToFit: true,\n            //suppressSizeToFit: false,\n            flex: \"8%\",\n            filter: true,\n            cellRendererParams: {\n                setSuppliers: setRowData,\n                setIsFiltered: setIsFiltered,\n                setIsFilteredName: setIsFilteredName\n            }\n        },\n        {\n            headerName: \"Supplier Code\",\n            cellRenderer: _components_supplierCodeRenderer__WEBPACK_IMPORTED_MODULE_23__[\"default\"],\n            field: \"supplier_code\",\n            flex: \"3%\"\n        },\n        {\n            headerName: \"Roles\",\n            field: \"role\",\n            cellRenderer: CustomCellRenderer,\n            tooltipComponent: CustomTooltipComponent,\n            flex: \"3%\"\n        },\n        {\n            headerName: \"Companies\",\n            field: \"companies\",\n            cellRenderer: CustomCellRenderer,\n            tooltipComponent: CustomTooltipComponent,\n            flex: \"4%\"\n        },\n        {\n            headerName: \"Currency\",\n            field: \"currency\",\n            flex: \"3%\",\n            cellRenderer: CustomCellRenderer,\n            tooltipComponent: CustomTooltipComponent,\n            cellStyle: (params)=>{\n                if (params.value == \"Not Entered\") {\n                    return {\n                        color: \"#B31312\"\n                    };\n                }\n                return null;\n            }\n        },\n        {\n            headerName: \"Requestor\",\n            cellRenderer: CustomCellRenderer,\n            tooltipComponent: CustomTooltipComponent,\n            field: \"requestor\",\n            flex: \"4%\"\n        },\n        {\n            headerName: \"General\",\n            cellRenderer: _utils_renderer_statusRenderer__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n            field: \"General\",\n            flex: \"3%\"\n        },\n        {\n            headerName: \"Financial\",\n            cellRenderer: _utils_renderer_statusRenderer__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n            field: \"Financials\",\n            flex: \"3%\"\n        },\n        {\n            headerName: \"Compliance\",\n            field: \"Compliance\",\n            cellRenderer: _utils_renderer_statusRenderer__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n            flex: \"4%\"\n        },\n        {\n            headerName: \"Procurement\",\n            field: \"Procurement\",\n            cellRenderer: _utils_renderer_statusRenderer__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n            flex: \"4%\"\n        },\n        {\n            headerName: \"Status\",\n            field: \"status\",\n            cellRenderer: _utils_renderer_statusRenderer__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n            cellStyle: ()=>({\n                    justifyContent: \"center\"\n                }),\n            flex: \"4%\",\n            hide: false\n        },\n        {\n            field: \"\",\n            cellRenderer: (params)=>(0,_utils_renderer_actionRenderer__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(params, userData, token, company),\n            flex: \"2%\",\n            cellStyle: ()=>({}),\n            sortable: false,\n            cellRendererParams: {\n                setUpdateStatusChange: setStatusChange\n            }\n        },\n        {\n            field: \"role_num\",\n            hide: true\n        }\n    ];\n    const onFilterTextBoxChanged = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(()=>{\n        gridRef.current.api.setQuickFilter(document.getElementById(\"filter-text-box\").value);\n    }, []);\n    const handleFilterToggle = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)((e)=>{\n        const { value } = e.target;\n        setSelectedStatus(value);\n        let filteredData = [];\n        if (value == \"default\") {\n            filteredData = allRowData.filter((row)=>row.status !== \"Cancelled\" && row.status !== \"Exported\");\n        } else {\n            filteredData = allRowData.filter((row)=>row.status === value);\n        }\n        setRowData(filteredData);\n        setExportDisabled(filteredData.length === 0);\n    }, [\n        allRowData\n    ]);\n    const exportFilteredData = async ()=>{\n        let export_ISSresponse = false;\n        let exportInternal_response = false;\n        // setIsOpenOption(false);\n        const gridApi = gridRef.current.api;\n        let filteredData = [];\n        const isInternal = selectedExportType === \"internalExport\";\n        if (isInternal) {\n            gridApi.forEachNodeAfterFilter((node)=>{\n                var _node_data, _node_data1, _node_data2, _node_data3;\n                if (node.data !== undefined && ((_node_data = node.data) === null || _node_data === void 0 ? void 0 : _node_data.status) === \"Completed\" || ((_node_data1 = node.data) === null || _node_data1 === void 0 ? void 0 : _node_data1.status) === \"Exported\" || ((_node_data2 = node.data) === null || _node_data2 === void 0 ? void 0 : _node_data2.isEmergencyRequest) && ((_node_data3 = node.data) === null || _node_data3 === void 0 ? void 0 : _node_data3.General) === \"Complete\") {\n                    filteredData = [\n                        [\n                            {\n                                \"Supplier Active\": \"test\",\n                                \"Haulage cube local\": \"test\",\n                                \"Haulage cube name\": \"test\",\n                                \"update guesstimates type\": \"test\",\n                                \"Organization ID\": \"\",\n                                \"Enforce department\": \"\",\n                                \"Sendac Group\": \"\",\n                                \"Supplier name\": \"\",\n                                \"Supplier type\": \"\",\n                                \"User Lookup 2\": \"\",\n                                \"Address Line 1\": \"\",\n                                \"Address Line 2\": \"\",\n                                \"Address Line 3\": \"\",\n                                \"Address Line 4\": \"\",\n                                \"Post code\": \"\",\n                                \"Country code\": \"\",\n                                \"Payee supplier code\": \"\",\n                                \"Invoice supplier\": \"\",\n                                \"Head office\": \"\",\n                                \"Settlement days\": \"\",\n                                \"Bank General Ledger Code\": \"\",\n                                \"Currency number\": \"\",\n                                \"Currency number / name\": \"\",\n                                \"Bank general ledger code\": \"\",\n                                \"Payment type\": \"\",\n                                \"Country code\": \"\",\n                                Vatable: \"\",\n                                vatable: \"\",\n                                \"Update guesstimates type\": \"\",\n                                \"Area Number\": \"\",\n                                Buyer: \"\",\n                                \"Multiple lot indicator\": \"\",\n                                \"multiple lot indicator\": \"\",\n                                \"Generate Pallet Loading Plan\": \"\",\n                                \"Distribution point for supplier\": \"\",\n                                \"Payment terms\": \"\",\n                                \"Department Number\": \"\"\n                            }\n                        ],\n                        [\n                            {\n                                \"Supplier code\": \"test\",\n                                Sendacroleid: \"test\",\n                                Description: \"test\",\n                                \"Supplier Code Name\": \"\",\n                                Type: \"\",\n                                \"Type Description\": \"\",\n                                GGN: \"\"\n                            }\n                        ],\n                        [\n                            {\n                                \"Contact ID\": \"test\",\n                                \"Supplier Name\": \"test\",\n                                \"Email Address\": \"\",\n                                Telephone: \"\",\n                                Cell: \"\"\n                            }\n                        ],\n                        [\n                            {\n                                \"Organization ID\": \"test\",\n                                \"Organization Name\": \"\"\n                            }\n                        ],\n                        [\n                            {\n                                \"Organization ID\": \"test\",\n                                \"Organization Code\": \"\",\n                                \"Role Type ID\": \"\",\n                                Selected: \"\",\n                                \"Organisation ID\": \"\",\n                                \"Role Type ID\": \"\",\n                                \"Contact ID\": \"\",\n                                \"Contact ID Email Address\": \"\",\n                                \"Contact ID Telephone\": \"\"\n                            }\n                        ]\n                    ];\n                }\n            });\n        } else {\n            gridApi.forEachNodeAfterFilter((node)=>{\n                var _node_data, _node_data1, _node_data2, _node_data3;\n                let rolesArray = node.data.roleId.map((ele)=>{\n                    return ele.role_id;\n                });\n                if (node.data !== undefined && ((_node_data = node.data) === null || _node_data === void 0 ? void 0 : _node_data.status) === \"Completed\" || ((_node_data1 = node.data) === null || _node_data1 === void 0 ? void 0 : _node_data1.status) === \"Exported\" || ((_node_data2 = node.data) === null || _node_data2 === void 0 ? void 0 : _node_data2.isEmergencyRequest) && ((_node_data3 = node.data) === null || _node_data3 === void 0 ? void 0 : _node_data3.General) === \"Complete\") {\n                    var _node_data4, _node_data_prophets__prophet_code, _node_data_prophets_, _node_data5, _node_data_company_name, _node_data6, _node_data7, _node_data8, _node_data9, _node_data10, _node_data11, _node_data12, _node_data13, _node_data_prophets__prophet_code1, _node_data_prophets_1, _node_data14, _node_data15, _node_data16, _node_data17, _node_data18, _node_data19, _node_data20, _node_data21;\n                    const filteredExportData = {\n                        \"Supplier Active\": (node === null || node === void 0 ? void 0 : (_node_data4 = node.data) === null || _node_data4 === void 0 ? void 0 : _node_data4.isActive) ? 1 : 0,\n                        \"Supplier code\": node === null || node === void 0 ? void 0 : (_node_data5 = node.data) === null || _node_data5 === void 0 ? void 0 : (_node_data_prophets_ = _node_data5.prophets[0]) === null || _node_data_prophets_ === void 0 ? void 0 : (_node_data_prophets__prophet_code = _node_data_prophets_.prophet_code) === null || _node_data_prophets__prophet_code === void 0 ? void 0 : _node_data_prophets__prophet_code.trim(),\n                        \"EDI Partner\": \"\",\n                        \"Supplier name\": node === null || node === void 0 ? void 0 : (_node_data6 = node.data) === null || _node_data6 === void 0 ? void 0 : (_node_data_company_name = _node_data6.company_name) === null || _node_data_company_name === void 0 ? void 0 : _node_data_company_name.trim(),\n                        \"Country Code\": node === null || node === void 0 ? void 0 : (_node_data7 = node.data) === null || _node_data7 === void 0 ? void 0 : _node_data7.country_code,\n                        \"Distribution Point for Supplier\": 6,\n                        \"Bank Ledger Code\": node === null || node === void 0 ? void 0 : (_node_data8 = node.data) === null || _node_data8 === void 0 ? void 0 : _node_data8.currency_id,\n                        \"Area Number\": 170,\n                        Vatable: 0,\n                        Buyer: 1,\n                        \"Billing type\": 0,\n                        \"Payment type\": node === null || node === void 0 ? void 0 : (_node_data9 = node.data) === null || _node_data9 === void 0 ? void 0 : _node_data9.payment_type,\n                        \"Currency number\": node === null || node === void 0 ? void 0 : (_node_data10 = node.data) === null || _node_data10 === void 0 ? void 0 : _node_data10.currency_id,\n                        GGN: node === null || node === void 0 ? void 0 : (_node_data11 = node.data) === null || _node_data11 === void 0 ? void 0 : _node_data11.global_gap_number,\n                        \"Organic cert\": node === null || node === void 0 ? void 0 : (_node_data12 = node.data) === null || _node_data12 === void 0 ? void 0 : _node_data12.organic_certificate_number,\n                        \"Regional cert\": node === null || node === void 0 ? void 0 : (_node_data13 = node.data) === null || _node_data13 === void 0 ? void 0 : _node_data13.chile_certificate_number,\n                        \"Head office\": node === null || node === void 0 ? void 0 : (_node_data14 = node.data) === null || _node_data14 === void 0 ? void 0 : (_node_data_prophets_1 = _node_data14.prophets[0]) === null || _node_data_prophets_1 === void 0 ? void 0 : (_node_data_prophets__prophet_code1 = _node_data_prophets_1.prophet_code) === null || _node_data_prophets__prophet_code1 === void 0 ? void 0 : _node_data_prophets__prophet_code1.trim(),\n                        \"Address line 1\": node === null || node === void 0 ? void 0 : (_node_data15 = node.data) === null || _node_data15 === void 0 ? void 0 : _node_data15.address_line_1,\n                        \"Address line 2\": node === null || node === void 0 ? void 0 : (_node_data16 = node.data) === null || _node_data16 === void 0 ? void 0 : _node_data16.address_line_2,\n                        \"Address line 3\": node === null || node === void 0 ? void 0 : (_node_data17 = node.data) === null || _node_data17 === void 0 ? void 0 : _node_data17.address_line_3,\n                        \"Address line 4\": node === null || node === void 0 ? void 0 : (_node_data18 = node.data) === null || _node_data18 === void 0 ? void 0 : _node_data18.address_line_4,\n                        \"Postal code\": node === null || node === void 0 ? void 0 : (_node_data19 = node.data) === null || _node_data19 === void 0 ? void 0 : _node_data19.postal_code,\n                        status: node === null || node === void 0 ? void 0 : (_node_data20 = node.data) === null || _node_data20 === void 0 ? void 0 : _node_data20.status,\n                        id: node === null || node === void 0 ? void 0 : (_node_data21 = node.data) === null || _node_data21 === void 0 ? void 0 : _node_data21.id\n                    };\n                    filteredData.push(filteredExportData);\n                }\n            });\n        }\n        if (filteredData.length === 0) {\n            react_toastify__WEBPACK_IMPORTED_MODULE_15__.toast.error(\"No filtered data to export.\", {\n                position: \"top-right\",\n                autoClose: 5000,\n                hideProgressBar: false,\n                closeOnClick: true,\n                pauseOnHover: false,\n                draggable: true,\n                progress: undefined,\n                theme: \"light\"\n            });\n        } else {\n            if (supplierCheckedValue) {\n                if (true) {\n                    const allStatesData = [\n                        ulpFilData,\n                        supplierActiveData,\n                        roleData,\n                        sendacGroupData,\n                        bankAc,\n                        senBnk,\n                        contactData,\n                        organizationData,\n                        organizationRoleData,\n                        sheetSupplierId\n                    ];\n                    exportInternal_response = await (0,_utils_exportExcel__WEBPACK_IMPORTED_MODULE_22__[\"default\"])(allStatesData, true, token, company, userData, prophetId, userData === null || userData === void 0 ? void 0 : userData.email, \"\");\n                }\n                if (!isUnExportable) {\n                    const allStatesData = [\n                        multipleFilterISSData,\n                        roleData\n                    ];\n                    export_ISSresponse = await (0,_utils_exportExcel__WEBPACK_IMPORTED_MODULE_22__[\"default\"])(allStatesData, false, token, company, userData, prophetId, userData === null || userData === void 0 ? void 0 : userData.email, \"\");\n                }\n                setEmailStatusPopup(true);\n                if (export_ISSresponse && exportInternal_response) {\n                    setPopUpMessage(\"Email sent to Finance Department and ISS admin\");\n                    setISSExportSuccess(true);\n                    setInternalExportSuccess(true);\n                } else if (exportInternal_response && isUnExportable) {\n                    setPopUpMessage(\"Email sent to Finance Department, but suppliers \".concat(unExportableSuppliernames, \" not exported as Hauliers and Expense roles not allowed to be exported to ISS\"));\n                    setInternalExportSuccess(true);\n                } else if (export_ISSresponse) {\n                    setISSExportSuccess(true);\n                    setPopUpMessage(\"Email sent to ISS Admin Team, but not to Finance Department\");\n                } else {\n                    setPopUpMessage(\"Email not sent to either Finance Department or ISS Admin Team\");\n                }\n            }\n        }\n        setSelectedExportType(\"\");\n        gridRef.current.api.deselectAll();\n    };\n    const [supplierActiveData, setSupplierActiveData] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([\n        [\n            \"sendac (Supplier file)\"\n        ]\n    ]);\n    const [roleData, setRoleData] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([\n        [\n            \"sendacrole (Supplier role file)\"\n        ]\n    ]);\n    const [contactData, setContactData] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([\n        [\n            \"contactdet (Supplier personnel contact details)\"\n        ]\n    ]);\n    const [organizationData, setOrganizationData] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([\n        [\n            \"organization (Organization)\"\n        ]\n    ]);\n    const [organizationRoleData, setOrganizationRoleData] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([\n        [\n            \"orgroles (Organization Roles)\"\n        ]\n    ]);\n    const [sendacGroupData, setSendacGroupData] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([\n        [\n            \"sendacgroup (Sendac group file)\"\n        ]\n    ]);\n    const [bankAc, setBankAc] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([\n        [\n            \"bankac (Bank account details table)\"\n        ]\n    ]);\n    const [multipleFilterISSData, setMultipleFilterISSData] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([\n        [\n            \"Supplier Data\"\n        ]\n    ]);\n    const [senBnk, setSenBnk] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([\n        [\n            \"senbnk (Supplier bank link table)\"\n        ]\n    ]);\n    const [ulpFilData, setUlpFilData] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([\n        [\n            \"UlpFil\"\n        ]\n    ]);\n    const [sheetSupplierId, setSheetSupplierId] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([\n        [\n            \"Supplier Id\"\n        ]\n    ]);\n    const handleGridReady = (params)=>{\n        params.api.setColumnDefs(columnDefs);\n    };\n    const extractContacts = (supplierCode, contactsJsonStr, supplierName)=>{\n        try {\n            const contacts = contactsJsonStr ? JSON.parse(contactsJsonStr) : [];\n            if (Array.isArray(contacts)) {\n                return contacts.map((contact)=>({\n                        \"Supplier code\": supplierCode ? supplierCode : \"\",\n                        \"Contact ID\": \"\",\n                        Name: supplierName || \"\",\n                        \"Email Address\": contact.email_id || \"\",\n                        \"Telephone number\": contact.telephone || \"\",\n                        \"Cell phone number\": \"\",\n                        \"Fax number\": \"\",\n                        \"Instant Message\": \"\",\n                        \"Physical Address\": \"\",\n                        \"Postal Address\": \"\",\n                        \"Row verision\": \"\",\n                        \"Created timestamp\": \"\"\n                    }));\n            }\n        } catch (error) {\n            console.error(\"Error parsing contacts_json:\", error);\n        }\n        return [\n            {\n                \"Contact ID\": \"\",\n                Name: supplierName || \"\",\n                \"Email Address\": \"\",\n                \"Telephone number\": \"\",\n                \"Cell phone number\": \"\",\n                \"Fax number\": \"\",\n                \"Instant Message\": \"\",\n                \"Physical Address\": \"\",\n                \"Postal Address\": \"\",\n                \"Row verision\": \"\",\n                \"Created timestamp\": \"\"\n            }\n        ];\n    };\n    const extractSendacGroup = (sendacGroupJson)=>{\n        try {\n            const sendacGroups = sendacGroupJson ? JSON.parse(sendacGroupJson) : [];\n            if (Array.isArray(sendacGroups)) {\n                return sendacGroups === null || sendacGroups === void 0 ? void 0 : sendacGroups.map((group)=>({\n                        \"Supplier group\": \"\",\n                        Description: (group === null || group === void 0 ? void 0 : group.created_by) ? group === null || group === void 0 ? void 0 : group.label : \"\"\n                    }));\n            }\n        } catch (error) {\n            console.error(\"Error parsing contacts_json:\", error);\n        }\n    };\n    const multipleSendRoleOnRoleNums = (data, role_num)=>{\n        var _data_role;\n        const roleNums = role_num === null || role_num === void 0 ? void 0 : role_num.split(\",\").map((num)=>num.trim());\n        const roleNames = data === null || data === void 0 ? void 0 : (_data_role = data.role) === null || _data_role === void 0 ? void 0 : _data_role.split(\",\").map((name)=>name.trim());\n        return roleNums.map((num, index)=>{\n            var _data_prophets_, _data_prophets_1;\n            return {\n                Sendacroleid: \"\",\n                \"Supplier code\": (data === null || data === void 0 ? void 0 : (_data_prophets_ = data.prophets[0]) === null || _data_prophets_ === void 0 ? void 0 : _data_prophets_.prophet_code) ? data.prophets[0].prophet_code.trim() : \"\",\n                Description: (data === null || data === void 0 ? void 0 : (_data_prophets_1 = data.prophets[0]) === null || _data_prophets_1 === void 0 ? void 0 : _data_prophets_1.prophet_code) ? data.prophets[0].prophet_code.trim() : \"\",\n                \"Supplier Code Supplier Name\": data.company_name,\n                Type: num,\n                \"Type Description\": roleNames[index],\n                \"Supplier code Global gap number\": data === null || data === void 0 ? void 0 : data.global_gap_number,\n                \"Created timestamp\": \"\",\n                Active: 1\n            };\n        });\n    };\n    function getGLCode(internal_ledger_code) {\n        if (internal_ledger_code) {\n            return internal_ledger_code;\n        } else return \"\";\n    }\n    const [incompleteToast, setIncompleteToast] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        if (incompleteToast) {\n            react_toastify__WEBPACK_IMPORTED_MODULE_15__.toast.error(\"Supplier details are incomplete.\", {\n                position: \"top-right\",\n                autoClose: 1000,\n                hideProgressBar: false,\n                closeOnClick: true,\n                pauseOnHover: false,\n                draggable: true,\n                progress: undefined,\n                theme: \"light\"\n            });\n        }\n    }, [\n        incompleteToast\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        if (containsCancelledSupplier) {\n            react_toastify__WEBPACK_IMPORTED_MODULE_15__.toast.error(\"Cannot export cancelled supplier.\", {\n                position: \"top-right\",\n                autoClose: 1000,\n                hideProgressBar: false,\n                closeOnClick: true,\n                pauseOnHover: false,\n                draggable: true,\n                progress: undefined,\n                theme: \"light\"\n            });\n        }\n    }, [\n        containsCancelledSupplier\n    ]);\n    const handleCheckboxEvent = (event)=>{\n        const getRowData = event.data;\n        const isSelected = event.node.selected;\n        const selectedRows = gridRef.current.api.getSelectedRows();\n        const prophet_id = getRowData.prophets[0].prophet_id;\n        setProphetId(prophet_id);\n        const extractedValues = selectedRows.map((param)=>{\n            let { status, isEmergencyRequest, General } = param;\n            return {\n                status,\n                isEmergencyRequest,\n                General\n            };\n        });\n        const exportDisabled = extractedValues.some((param)=>{\n            let { status, isEmergencyRequest, General } = param;\n            return !(status === \"Completed\" || status === \"Exported\" || isEmergencyRequest && General === \"Complete\");\n        });\n        const canExport = extractedValues.every((param)=>{\n            let { isEmergencyRequest } = param;\n            return !(!isEmergencyRequest && ((userData === null || userData === void 0 ? void 0 : userData.role_id) != 1 || (userData === null || userData === void 0 ? void 0 : userData.role_id) != 2 || (userData === null || userData === void 0 ? void 0 : userData.role_id) != 5 || (userData === null || userData === void 0 ? void 0 : userData.role_id) != 6));\n        });\n        const isExportableBasedOnCodeUnique = selectedRows.every((row)=>{\n            var _row_prophets_, _row_prophets_1;\n            const codeCount = row === null || row === void 0 ? void 0 : (_row_prophets_ = row.prophets[0]) === null || _row_prophets_ === void 0 ? void 0 : _row_prophets_.code_count;\n            const prophetCode = row === null || row === void 0 ? void 0 : (_row_prophets_1 = row.prophets[0]) === null || _row_prophets_1 === void 0 ? void 0 : _row_prophets_1.prophet_code;\n            if (codeCount && codeCount > 1 && prophetCode && prophetCode !== \"\") {\n                return false;\n            } else if (codeCount && codeCount == 1 && prophetCode && prophetCode !== \"\") {\n                return true;\n            }\n            return false;\n        });\n        const doesContainCancelledSupplier = selectedRows.some((row)=>row.status === \"Cancelled\");\n        const isExportValid = selectedRows.every((row)=>{\n            var _row_prophets_, _row_prophets_1, _row_roleIds, _row_roleIds1;\n            const supCode = row === null || row === void 0 ? void 0 : (_row_prophets_ = row.prophets[0]) === null || _row_prophets_ === void 0 ? void 0 : _row_prophets_.prophet_code;\n            const prophet_id = row === null || row === void 0 ? void 0 : (_row_prophets_1 = row.prophets[0]) === null || _row_prophets_1 === void 0 ? void 0 : _row_prophets_1.prophet_id;\n            const isSupplierAccount = (row === null || row === void 0 ? void 0 : (_row_roleIds = row.roleIds) === null || _row_roleIds === void 0 ? void 0 : _row_roleIds.includes(1)) || (row === null || row === void 0 ? void 0 : (_row_roleIds1 = row.roleIds) === null || _row_roleIds1 === void 0 ? void 0 : _row_roleIds1.includes(6));\n            let currency = (row === null || row === void 0 ? void 0 : row.currency) == \"$\" ? \"\\\\\".concat(row === null || row === void 0 ? void 0 : row.currency) : row === null || row === void 0 ? void 0 : row.currency;\n            let actualCurr;\n            if (currency && currency == \"Not Entered\") {\n                actualCurr = \"\";\n            } else {\n                actualCurr = currency;\n            }\n            let isValid = true;\n            if (isSupplierAccount) {\n                if (prophet_id == 1) {\n                    let regexPattern;\n                    regexPattern = new RegExp(\"^[A-Z0-9]{4}[A-Z0145678]\".concat(actualCurr, \"$\"));\n                    // let regexPattern = new RegExp(`^([A-Z0]{5})${actualCurr}$`);\n                    isValid = regexPattern.test(supCode);\n                } else if (prophet_id == 2) {\n                    let regexPattern;\n                    regexPattern = new RegExp(\"^([A-Z0]{4})9\".concat(actualCurr, \"$\"));\n                    // let regexPattern = new RegExp(`^([A-Z0]{4})9${actualCurr}$`);\n                    isValid = regexPattern.test(supCode);\n                } else if (prophet_id == 3) {\n                    let regexPattern = new RegExp(\"^[A-Z0-9]{4}[A-Z01345678][A-Z0-9]*$\");\n                    isValid = regexPattern.test(supCode) && supCode.length == 6;\n                } else if (prophet_id == 4) {\n                    let regexPattern;\n                    regexPattern = new RegExp(\"^([A-Z0]{4})2\".concat(actualCurr, \"$\"));\n                    isValid = regexPattern.test(supCode);\n                }\n            }\n            return isValid;\n        });\n        if (selectedRows.length > 0) {\n            if (!canExport && (userData === null || userData === void 0 ? void 0 : userData.role_id) != 1 && (userData === null || userData === void 0 ? void 0 : userData.role_id) != 2 && (userData === null || userData === void 0 ? void 0 : userData.role_id) != 5 && (userData === null || userData === void 0 ? void 0 : userData.role_id) != 6) {\n                setExportDisabled(true);\n            } else if (doesContainCancelledSupplier) {\n                setContainsCancelledSupplier(true);\n                setExportDisabled(true);\n            } else if (!isExportableBasedOnCodeUnique) {\n                setSupplierUniqueCodeToast(true);\n                setExportDisabled(true);\n            } else if (!isExportValid) {\n                setSupplierCodeValid(false);\n                setExportDisabled(true);\n            } else {\n                setExportDisabled(exportDisabled);\n            }\n        } else {\n            setExportDisabled(true);\n        }\n        let isUnExportableToISS = false;\n        let supplierNames = [];\n        selectedRows.forEach((row)=>{\n            var _row_roleIds, _row_roleIds1, _row_roleIds2, _row_roleIds3;\n            if (!(row === null || row === void 0 ? void 0 : (_row_roleIds = row.roleIds) === null || _row_roleIds === void 0 ? void 0 : _row_roleIds.includes(1)) && !(row === null || row === void 0 ? void 0 : (_row_roleIds1 = row.roleIds) === null || _row_roleIds1 === void 0 ? void 0 : _row_roleIds1.includes(2)) && !(row === null || row === void 0 ? void 0 : (_row_roleIds2 = row.roleIds) === null || _row_roleIds2 === void 0 ? void 0 : _row_roleIds2.includes(3)) && !(row === null || row === void 0 ? void 0 : (_row_roleIds3 = row.roleIds) === null || _row_roleIds3 === void 0 ? void 0 : _row_roleIds3.includes(4))) {\n                isUnExportableToISS = true;\n                supplierNames.push(row.company_name);\n            }\n        });\n        const supplierNamesString = supplierNames.join(\", \");\n        setIsUnExportable(isUnExportableToISS);\n        setUnExportableSupplierNames(supplierNamesString);\n        if ((getRowData === null || getRowData === void 0 ? void 0 : getRowData.status) == \"Completed\" || (getRowData === null || getRowData === void 0 ? void 0 : getRowData.status) == \"Exported\" || (getRowData === null || getRowData === void 0 ? void 0 : getRowData.isEmergencyRequest) && getRowData.status != \"Cancelled\" && (getRowData === null || getRowData === void 0 ? void 0 : getRowData.General) === \"Complete\") {\n            var _getRowData_roleIds, _getRowData_roleIds1, _getRowData_roleIds2, _getRowData_roleIds3, _getRowData_prophets__prophet_code, _getRowData_prophets_, _getRowData_company_name, _getRowData_prophets__prophet_code1, _getRowData_prophets_1, _getRowData_distribution_points_json, _getRowData_prophets_2, _getRowData_prophets_3, _getRowData_prophets__prophet_code2, _getRowData_prophets_4, _getRowData_prophets_5, _getRowData_prophets_6, _getRowData_distribution_points_json1, _getRowData_distribution_points_json2, _getRowData_prophets__prophet_code3, _getRowData_prophets_7, _getRowData_prophets__prophet_code4, _getRowData_prophets_8, _getRowData_prophets_9, _getRowData_prophets__prophet_code5, _getRowData_prophets_10, _getRowData_prophets_11, _getRowData_prophets__prophet_code6, _getRowData_prophets_12;\n            let regional_cert = \"\";\n            if ((getRowData === null || getRowData === void 0 ? void 0 : (_getRowData_roleIds = getRowData.roleIds) === null || _getRowData_roleIds === void 0 ? void 0 : _getRowData_roleIds.includes(2)) || (getRowData === null || getRowData === void 0 ? void 0 : (_getRowData_roleIds1 = getRowData.roleIds) === null || _getRowData_roleIds1 === void 0 ? void 0 : _getRowData_roleIds1.includes(3))) {\n                if (getRowData.country_code == \"UK\") {\n                    regional_cert = getRowData === null || getRowData === void 0 ? void 0 : getRowData.red_tractor;\n                } else if (getRowData.country_code == \"ZA\") {\n                    regional_cert = getRowData === null || getRowData === void 0 ? void 0 : getRowData.puc_code;\n                } else if (getRowData.country_code == \"CL\") {\n                    regional_cert = getRowData === null || getRowData === void 0 ? void 0 : getRowData.chile_certificate_number;\n                }\n            }\n            let currencyId = \"\";\n            let currencyName = \"\";\n            if ((getRowData === null || getRowData === void 0 ? void 0 : (_getRowData_roleIds2 = getRowData.roleIds) === null || _getRowData_roleIds2 === void 0 ? void 0 : _getRowData_roleIds2.includes(1)) || (getRowData === null || getRowData === void 0 ? void 0 : (_getRowData_roleIds3 = getRowData.roleIds) === null || _getRowData_roleIds3 === void 0 ? void 0 : _getRowData_roleIds3.includes(6))) {\n                currencyId = getRowData === null || getRowData === void 0 ? void 0 : getRowData.currency_id;\n                currencyName = getRowData === null || getRowData === void 0 ? void 0 : getRowData.currency_name;\n            } else {\n                currencyId = 1;\n                currencyName = \"Sterling\";\n            }\n            function getCorrespondingUserLookup(curr) {\n                if (curr == \"GBP\") {\n                    return \"GBPBACS\";\n                } else if (curr == \"EUR\") {\n                    return \"EUROSEPA\";\n                } else if (curr == \"USD\") {\n                    return \"USDPRIORITY\";\n                } else {\n                    return \"\";\n                }\n            }\n            console.log(\"get row data\", getRowData);\n            var _getRowData_edi;\n            const filteredISSExportData = {\n                \"Supplier Active\": \"N/A\",\n                \"Supplier code\": getRowData === null || getRowData === void 0 ? void 0 : (_getRowData_prophets_ = getRowData.prophets[0]) === null || _getRowData_prophets_ === void 0 ? void 0 : (_getRowData_prophets__prophet_code = _getRowData_prophets_.prophet_code) === null || _getRowData_prophets__prophet_code === void 0 ? void 0 : _getRowData_prophets__prophet_code.trim(),\n                \"EDI Partner\": \"N/A\",\n                \"Supplier name\": getRowData === null || getRowData === void 0 ? void 0 : (_getRowData_company_name = getRowData.company_name) === null || _getRowData_company_name === void 0 ? void 0 : _getRowData_company_name.trim(),\n                \"EDI ANA number\": (_getRowData_edi = getRowData === null || getRowData === void 0 ? void 0 : getRowData.edi) !== null && _getRowData_edi !== void 0 ? _getRowData_edi : \"N/A\",\n                \"Producer (supplier)\": \"N/A\",\n                \"Department number\": \"N/A\",\n                \"Currency number\": (getRowData === null || getRowData === void 0 ? void 0 : getRowData.currency_id) ? getRowData === null || getRowData === void 0 ? void 0 : getRowData.currency_id : 1,\n                \"Global gap number\": getRowData === null || getRowData === void 0 ? void 0 : getRowData.global_gap_number,\n                \"Grower group\": \"N/A\",\n                \"Defra county number\": \"N/A\",\n                \"Date start\": \"N/A\",\n                \"Date end\": \"N/A\",\n                \"Organic Cert\": getRowData === null || getRowData === void 0 ? void 0 : getRowData.organic_certificate_number,\n                \"Regional Cert\": regional_cert,\n                \"Head office\": getRowData === null || getRowData === void 0 ? void 0 : (_getRowData_prophets_1 = getRowData.prophets[0]) === null || _getRowData_prophets_1 === void 0 ? void 0 : (_getRowData_prophets__prophet_code1 = _getRowData_prophets_1.prophet_code) === null || _getRowData_prophets__prophet_code1 === void 0 ? void 0 : _getRowData_prophets__prophet_code1.trim(),\n                \"Country Code\": getRowData === null || getRowData === void 0 ? void 0 : getRowData.country_code,\n                \"Distribution point for supplier\": (getRowData === null || getRowData === void 0 ? void 0 : (_getRowData_distribution_points_json = getRowData.distribution_points_json) === null || _getRowData_distribution_points_json === void 0 ? void 0 : _getRowData_distribution_points_json.length) > 0 ? getRowData === null || getRowData === void 0 ? void 0 : getRowData.distribution_points_json[0].from_dp : \"N/A\",\n                \"Bool 2\": \"N/A\",\n                \"Bool 3\": \"N/A\",\n                \"Address line 1\": getRowData === null || getRowData === void 0 ? void 0 : getRowData.address_line_1,\n                \"Address line 2\": getRowData === null || getRowData === void 0 ? void 0 : getRowData.address_line_2,\n                \"Address line 3\": getRowData === null || getRowData === void 0 ? void 0 : getRowData.address_line_3,\n                \"Address line 4\": getRowData === null || getRowData === void 0 ? void 0 : getRowData.address_line_4,\n                \"Post code\": getRowData === null || getRowData === void 0 ? void 0 : getRowData.postal_code,\n                \"Currency Number\": (getRowData === null || getRowData === void 0 ? void 0 : getRowData.currency_id) ? getRowData === null || getRowData === void 0 ? void 0 : getRowData.currency_id : 1,\n                \"Bank general ledger code\": (getRowData === null || getRowData === void 0 ? void 0 : getRowData.iss_ledger_code) ? getRowData === null || getRowData === void 0 ? void 0 : getRowData.iss_ledger_code : \"12200\",\n                \"Bank general ledger code Currency number if bank\": (getRowData === null || getRowData === void 0 ? void 0 : getRowData.currency_id) ? getRowData === null || getRowData === void 0 ? void 0 : getRowData.currency_id : 1,\n                \"Settlement days\": getRowData === null || getRowData === void 0 ? void 0 : getRowData.payment_terms,\n                \"Department Number\": (getRowData === null || getRowData === void 0 ? void 0 : (_getRowData_prophets_2 = getRowData.prophets[0]) === null || _getRowData_prophets_2 === void 0 ? void 0 : _getRowData_prophets_2.prophet_id) == 1 ? 1 : (getRowData === null || getRowData === void 0 ? void 0 : (_getRowData_prophets_3 = getRowData.prophets[0]) === null || _getRowData_prophets_3 === void 0 ? void 0 : _getRowData_prophets_3.prophet_id) == 2 ? 9 : (getRowData === null || getRowData === void 0 ? void 0 : getRowData.prophets[0].prophet_id) == 3 ? 3 : (getRowData === null || getRowData === void 0 ? void 0 : getRowData.prophets[0].prophet_id) == 4 ? 2 : \"N/A\",\n                \"Area Number\": \"1\",\n                Vatable: (getRowData === null || getRowData === void 0 ? void 0 : getRowData.vatable) != null ? (getRowData === null || getRowData === void 0 ? void 0 : getRowData.vatable) ? \"1\" : \"0\" : \"0\",\n                Buyer: \"1\",\n                \"Billing type\": \"0\",\n                \"Payment type\": (getRowData === null || getRowData === void 0 ? void 0 : getRowData.payment_type) ? getRowData === null || getRowData === void 0 ? void 0 : getRowData.payment_type : 2,\n                \"Expense general ledger code\": \"N/A\",\n                \"Authorise on register\": \"N/A\",\n                \"Use % authorise rule\": 5,\n                \"User text 1\": \"N/A\",\n                \"Mandatory altfil on service jobs\": \"N/A\",\n                \"Organization ID\": \"N/A\",\n                id: getRowData === null || getRowData === void 0 ? void 0 : getRowData.id,\n                isEmergencyAndFinanceNotComplete: getRowData === null || getRowData === void 0 ? void 0 : getRowData.isEmergencyAndFinanceNotComplete\n            };\n            const newSupplierActiveData = {\n                \"Supplier Active\": (getRowData === null || getRowData === void 0 ? void 0 : getRowData.isActive) ? 1 : 0,\n                \"Haulage cube local\": \"\",\n                \"Haulage cube name\": \"\",\n                \"update guesstimates type\": 1,\n                \"Organization ID\": \"\",\n                \"Vat number 1\": getRowData === null || getRowData === void 0 ? void 0 : getRowData.vat_number,\n                \"Organic cert\": getRowData === null || getRowData === void 0 ? void 0 : getRowData.organic_certificate_number,\n                \"Regional cert\": regional_cert,\n                \"Global gap number\": getRowData === null || getRowData === void 0 ? void 0 : getRowData.global_gap_number,\n                \"Enforce department\": \"\",\n                \"Sendac Group\": (getRowData === null || getRowData === void 0 ? void 0 : getRowData.supplier_group) ? JSON.parse(getRowData === null || getRowData === void 0 ? void 0 : getRowData.supplier_group)[0].value : \"\",\n                \"Supplier code\": getRowData === null || getRowData === void 0 ? void 0 : (_getRowData_prophets_4 = getRowData.prophets[0]) === null || _getRowData_prophets_4 === void 0 ? void 0 : (_getRowData_prophets__prophet_code2 = _getRowData_prophets_4.prophet_code) === null || _getRowData_prophets__prophet_code2 === void 0 ? void 0 : _getRowData_prophets__prophet_code2.trim(),\n                \"Supplier name\": getRowData.company_name,\n                \"Supplier type\": getRowData === null || getRowData === void 0 ? void 0 : getRowData.supplier_type,\n                \"User Lookup 2\": \"\",\n                \"Address Line 1\": getRowData === null || getRowData === void 0 ? void 0 : getRowData.address_line_1,\n                \"Address Line 2\": getRowData === null || getRowData === void 0 ? void 0 : getRowData.address_line_2,\n                \"Address Line 3\": getRowData === null || getRowData === void 0 ? void 0 : getRowData.address_line_3,\n                \"Address Line 4\": getRowData === null || getRowData === void 0 ? void 0 : getRowData.address_line_4,\n                \"Post code\": getRowData === null || getRowData === void 0 ? void 0 : getRowData.postal_code,\n                \"Country code\": getRowData === null || getRowData === void 0 ? void 0 : getRowData.country_code,\n                \"Payee supplier code\": \"\",\n                \"Invoice supplier\": \"\",\n                \"Head office\": \"\",\n                \"Settlement days\": getRowData === null || getRowData === void 0 ? void 0 : getRowData.payment_terms,\n                \"Bank general ledger code Currency number if bank\": currencyId,\n                \"Currency number\": currencyId,\n                \"Currency number Currency name\": currencyName,\n                \"Bank general ledger code\": getGLCode(getRowData === null || getRowData === void 0 ? void 0 : getRowData.internal_ledger_code),\n                \"payment type\": getRowData === null || getRowData === void 0 ? void 0 : getRowData.payment_type,\n                \"Payment type name\": getRowData === null || getRowData === void 0 ? void 0 : getRowData.payment_type_name,\n                \"country code\": getRowData === null || getRowData === void 0 ? void 0 : getRowData.country_code,\n                Vatable: (getRowData === null || getRowData === void 0 ? void 0 : getRowData.vatable) != null ? (getRowData === null || getRowData === void 0 ? void 0 : getRowData.vatable) ? \"1\" : \"0\" : \"0\",\n                \"vatable desc\": (getRowData === null || getRowData === void 0 ? void 0 : getRowData.vatable) != null ? (getRowData === null || getRowData === void 0 ? void 0 : getRowData.vatable) ? \"Vatable\" : \"None vatable\" : \"None vatable\",\n                \"Area Number\": (getRowData === null || getRowData === void 0 ? void 0 : getRowData.prophets[0].prophet_id) == 3 || (getRowData === null || getRowData === void 0 ? void 0 : getRowData.prophets[0].prophet_id) == 4 ? 1 : 7,\n                Buyer: 1,\n                \"Multiple lot indicator\": \"0\",\n                \"multiple lot indicator desc\": \"By Lot\",\n                \"Generate Pallet Loading Plan\": \"\",\n                \"Distribution point for supplier\": 6,\n                \"Payment terms\": \"\",\n                \"Department Number\": (getRowData === null || getRowData === void 0 ? void 0 : (_getRowData_prophets_5 = getRowData.prophets[0]) === null || _getRowData_prophets_5 === void 0 ? void 0 : _getRowData_prophets_5.prophet_id) == 1 ? 1 : (getRowData === null || getRowData === void 0 ? void 0 : (_getRowData_prophets_6 = getRowData.prophets[0]) === null || _getRowData_prophets_6 === void 0 ? void 0 : _getRowData_prophets_6.prophet_id) == 2 ? 9 : (getRowData === null || getRowData === void 0 ? void 0 : getRowData.prophets[0].prophet_id) == 3 ? 3 : (getRowData === null || getRowData === void 0 ? void 0 : getRowData.prophets[0].prophet_id) == 4 ? 2 : \"\",\n                \"Allow credit rebates\": \"\",\n                \"Alternative DP for supplier\": 1,\n                \"Actual posting stops purchase charges\": \"\",\n                \"Authorise on register\": \"\",\n                \"User text 1\": \"\",\n                \"User lookup 1\": (getRowData === null || getRowData === void 0 ? void 0 : getRowData.prophets[0].prophet_id) == 3 || (getRowData === null || getRowData === void 0 ? void 0 : getRowData.prophets[0].prophet_id) == 4 ? getCorrespondingUserLookup(getRowData === null || getRowData === void 0 ? void 0 : getRowData.currency_code) : \"\",\n                \"Receive orders from edi\": \"\",\n                \"Send invoices from edi\": \"\",\n                \"Send orders from edi\": \"\",\n                \"EDI partner\": (getRowData === null || getRowData === void 0 ? void 0 : getRowData.prophets[0].prophet_id) == 3 || (getRowData === null || getRowData === void 0 ? void 0 : getRowData.prophets[0].prophet_id) == 4 ? 2000 : \"\",\n                \"Generic code\": (getRowData === null || getRowData === void 0 ? void 0 : getRowData.prophets[0].prophet_id) == 3 || (getRowData === null || getRowData === void 0 ? void 0 : getRowData.prophets[0].prophet_id) == 4 ? \"STOCK\" : \"\",\n                \"EDI ANA number\": \"\",\n                \"User % authorize rule\": 5,\n                FromDP: (getRowData === null || getRowData === void 0 ? void 0 : (_getRowData_distribution_points_json1 = getRowData.distribution_points_json) === null || _getRowData_distribution_points_json1 === void 0 ? void 0 : _getRowData_distribution_points_json1.length) > 0 ? getRowData.distribution_points_json[0].from_dp || \"\" : \"\"\n            };\n            let UlpFil = {};\n            if ((getRowData === null || getRowData === void 0 ? void 0 : (_getRowData_distribution_points_json2 = getRowData.distribution_points_json) === null || _getRowData_distribution_points_json2 === void 0 ? void 0 : _getRowData_distribution_points_json2.length) > 0 && ((getRowData === null || getRowData === void 0 ? void 0 : getRowData.distribution_points_json[0].from_dp) === null || (getRowData === null || getRowData === void 0 ? void 0 : getRowData.distribution_points_json[0].from_dp) === undefined)) {\n                var _getRowData_distribution_points_json3, _getRowData_prophets__prophet_code7, _getRowData_prophets_13, _getRowData_distribution_points_json4, _getRowData_distribution_points_json_;\n                UlpFil = {\n                    \"Distribution point\": \"\",\n                    Description: (getRowData === null || getRowData === void 0 ? void 0 : (_getRowData_distribution_points_json3 = getRowData.distribution_points_json) === null || _getRowData_distribution_points_json3 === void 0 ? void 0 : _getRowData_distribution_points_json3.length) > 0 && ((getRowData === null || getRowData === void 0 ? void 0 : getRowData.distribution_points_json[0].from_dp) === null || (getRowData === null || getRowData === void 0 ? void 0 : getRowData.distribution_points_json[0].from_dp) === undefined) ? getRowData === null || getRowData === void 0 ? void 0 : getRowData.distribution_points_json[0].name : \"\",\n                    \"Service Supplier Code\": ((getRowData === null || getRowData === void 0 ? void 0 : getRowData.distribution_points_json[0].from_dp) === null || (getRowData === null || getRowData === void 0 ? void 0 : getRowData.distribution_points_json[0].from_dp) === undefined) && (getRowData === null || getRowData === void 0 ? void 0 : (_getRowData_prophets_13 = getRowData.prophets[0]) === null || _getRowData_prophets_13 === void 0 ? void 0 : (_getRowData_prophets__prophet_code7 = _getRowData_prophets_13.prophet_code) === null || _getRowData_prophets__prophet_code7 === void 0 ? void 0 : _getRowData_prophets__prophet_code7.trim()),\n                    \"Default expected stock status\": ((getRowData === null || getRowData === void 0 ? void 0 : getRowData.distribution_points_json[0].from_dp) === null || (getRowData === null || getRowData === void 0 ? void 0 : getRowData.distribution_points_json[0].from_dp) === undefined) && \"0\",\n                    \"Default received stock status\": ((getRowData === null || getRowData === void 0 ? void 0 : getRowData.distribution_points_json[0].from_dp) === null || (getRowData === null || getRowData === void 0 ? void 0 : getRowData.distribution_points_json[0].from_dp) === undefined) && \"0\",\n                    \"Pallets in packhouse\": ((getRowData === null || getRowData === void 0 ? void 0 : getRowData.distribution_points_json[0].from_dp) === null || (getRowData === null || getRowData === void 0 ? void 0 : getRowData.distribution_points_json[0].from_dp) === undefined) && \"0\",\n                    \"Default haulier\": ((getRowData === null || getRowData === void 0 ? void 0 : getRowData.distribution_points_json[0].from_dp) === null || (getRowData === null || getRowData === void 0 ? void 0 : getRowData.distribution_points_json[0].from_dp) === undefined) && \"ZZZZZ\",\n                    \"Default expected location id\": ((getRowData === null || getRowData === void 0 ? void 0 : getRowData.distribution_points_json[0].from_dp) === null || (getRowData === null || getRowData === void 0 ? void 0 : getRowData.distribution_points_json[0].from_dp) === undefined) && 21,\n                    \"Default receiving location id\": ((getRowData === null || getRowData === void 0 ? void 0 : getRowData.distribution_points_json[0].from_dp) === null || (getRowData === null || getRowData === void 0 ? void 0 : getRowData.distribution_points_json[0].from_dp) === undefined) && 21,\n                    \"Packhouse location id\": ((getRowData === null || getRowData === void 0 ? void 0 : getRowData.distribution_points_json[0].from_dp) === null || (getRowData === null || getRowData === void 0 ? void 0 : getRowData.distribution_points_json[0].from_dp) === undefined) && 21,\n                    \"Default pick location id\": ((getRowData === null || getRowData === void 0 ? void 0 : getRowData.distribution_points_json[0].from_dp) === null || (getRowData === null || getRowData === void 0 ? void 0 : getRowData.distribution_points_json[0].from_dp) === undefined) && 21,\n                    \"Despatch location id\": ((getRowData === null || getRowData === void 0 ? void 0 : getRowData.distribution_points_json[0].from_dp) === null || (getRowData === null || getRowData === void 0 ? void 0 : getRowData.distribution_points_json[0].from_dp) === undefined) && 21,\n                    \"Default waste location id\": ((getRowData === null || getRowData === void 0 ? void 0 : getRowData.distribution_points_json[0].from_dp) === null || (getRowData === null || getRowData === void 0 ? void 0 : getRowData.distribution_points_json[0].from_dp) === undefined) && 21,\n                    \"Default pre-pick location id\": ((getRowData === null || getRowData === void 0 ? void 0 : getRowData.distribution_points_json[0].from_dp) === null || (getRowData === null || getRowData === void 0 ? void 0 : getRowData.distribution_points_json[0].from_dp) === undefined) && 21,\n                    \"Default returns location id\": ((getRowData === null || getRowData === void 0 ? void 0 : getRowData.distribution_points_json[0].from_dp) === null || (getRowData === null || getRowData === void 0 ? void 0 : getRowData.distribution_points_json[0].from_dp) === undefined) && 21,\n                    Address: \"\",\n                    \"Service supplier code\": \"\",\n                    \"EDI Reference Code\": \"\",\n                    \"EDI ANA Code\": \"\",\n                    \"User Integer 1\": ((getRowData === null || getRowData === void 0 ? void 0 : getRowData.distribution_points_json[0].from_dp) === null || (getRowData === null || getRowData === void 0 ? void 0 : getRowData.distribution_points_json[0].from_dp) === undefined) && 1,\n                    \"Movement resource group\": \"\",\n                    \"Handheld application used\": ((getRowData === null || getRowData === void 0 ? void 0 : getRowData.distribution_points_json[0].from_dp) === null || (getRowData === null || getRowData === void 0 ? void 0 : getRowData.distribution_points_json[0].from_dp) === undefined) && \"0\",\n                    \"Pallets in procure/receiving\": ((getRowData === null || getRowData === void 0 ? void 0 : getRowData.distribution_points_json[0].from_dp) === null || (getRowData === null || getRowData === void 0 ? void 0 : getRowData.distribution_points_json[0].from_dp) === undefined) && \"0\",\n                    \"Operational depo\": \"\",\n                    \"Enabled for masterfile sending\": \"\",\n                    \"Connected registed depot\": \"\",\n                    \"EDI Transmission type of depo\": \"\",\n                    \"Container loading depo\": ((getRowData === null || getRowData === void 0 ? void 0 : getRowData.distribution_points_json[0].from_dp) === null || (getRowData === null || getRowData === void 0 ? void 0 : getRowData.distribution_points_json[0].from_dp) === undefined) && \"0\",\n                    \"Airport depot\": \"\",\n                    \"Sms notification\": \"\",\n                    Port: \"\",\n                    Dormant: \"\",\n                    Active: ((getRowData === null || getRowData === void 0 ? void 0 : getRowData.distribution_points_json[0].from_dp) === null || (getRowData === null || getRowData === void 0 ? void 0 : getRowData.distribution_points_json[0].from_dp) === undefined) && 1,\n                    \"Ingredient distribution point\": \"\",\n                    \"Show in CE\": ((getRowData === null || getRowData === void 0 ? void 0 : getRowData.distribution_points_json[0].from_dp) === null || (getRowData === null || getRowData === void 0 ? void 0 : getRowData.distribution_points_json[0].from_dp) === undefined) && 1,\n                    \"Charge direction\": \"\",\n                    \"Pallet receive time\": \"\",\n                    \"User string 3\": \"\",\n                    \"Direct DP\": (getRowData === null || getRowData === void 0 ? void 0 : (_getRowData_distribution_points_json4 = getRowData.distribution_points_json) === null || _getRowData_distribution_points_json4 === void 0 ? void 0 : _getRowData_distribution_points_json4.length) > 0 && ((getRowData === null || getRowData === void 0 ? void 0 : getRowData.distribution_points_json[0].from_dp) === null || (getRowData === null || getRowData === void 0 ? void 0 : getRowData.distribution_points_json[0].from_dp) === undefined) ? (getRowData === null || getRowData === void 0 ? void 0 : (_getRowData_distribution_points_json_ = getRowData.distribution_points_json[0]) === null || _getRowData_distribution_points_json_ === void 0 ? void 0 : _getRowData_distribution_points_json_.direct_dp) ? 1 : \"0\" : \"\",\n                    \"Include on XML\": ((getRowData === null || getRowData === void 0 ? void 0 : getRowData.distribution_points_json[0].from_dp) === null || (getRowData === null || getRowData === void 0 ? void 0 : getRowData.distribution_points_json[0].from_dp) === undefined) && \"0\"\n                };\n            }\n            // const newRoleData = {\n            //   Sendacroleid: \"\",\n            //   \"Supplier code\": getRowData?.prophets[0]?.prophet_code?.trim(),\n            //   Description: \"\",\n            //   \"Supplier Code Supplier Name\": getRowData.company_name,\n            //   Type: \"\",\n            //   \"Type Description\": getRowData?.[\"role names\"],\n            //   \"Supplier code Global gap number\": getRowData?.global_gap_number,\n            //   \"Created timestamp\": \"\",\n            //   Active: \"\",\n            // };\n            const newRoleData = multipleSendRoleOnRoleNums(getRowData, getRowData === null || getRowData === void 0 ? void 0 : getRowData.role_num);\n            const extractedSendacGroup = extractSendacGroup(getRowData === null || getRowData === void 0 ? void 0 : getRowData.supplier_group);\n            let sort_code = \"\";\n            let account_number = \"\";\n            let swiftBicCode = \"\";\n            let iban = \"\";\n            const swiftBicRegex = /^([A-Z0-9]{4}[A-Z0-9]{2}[A-Z0-9]{2}|[A-Z0-9]{4}[A-Z0-9]{2}[A-Z0-9]{2}[A-Z0-9]{3})$/;\n            if (swiftBicRegex.test(getRowData === null || getRowData === void 0 ? void 0 : getRowData.sort_bic) && (getRowData === null || getRowData === void 0 ? void 0 : getRowData.has_iban)) {\n                var _getRowData_account_number;\n                sort_code = \"000000\";\n                account_number = getRowData === null || getRowData === void 0 ? void 0 : (_getRowData_account_number = getRowData.account_number) === null || _getRowData_account_number === void 0 ? void 0 : _getRowData_account_number.slice(-8);\n                swiftBicCode = getRowData === null || getRowData === void 0 ? void 0 : getRowData.sort_bic;\n                iban = getRowData === null || getRowData === void 0 ? void 0 : getRowData.account_number;\n            } else if (!(getRowData === null || getRowData === void 0 ? void 0 : getRowData.has_iban) && swiftBicRegex.test(getRowData === null || getRowData === void 0 ? void 0 : getRowData.sort_bic)) {\n                sort_code = \"000000\";\n                account_number = getRowData === null || getRowData === void 0 ? void 0 : getRowData.account_number;\n                swiftBicCode = getRowData === null || getRowData === void 0 ? void 0 : getRowData.sort_bic;\n            } else {\n                sort_code = getRowData === null || getRowData === void 0 ? void 0 : getRowData.sort_bic;\n                account_number = getRowData === null || getRowData === void 0 ? void 0 : getRowData.account_number;\n            }\n            const bankac = {\n                \"Supplier code\": getRowData === null || getRowData === void 0 ? void 0 : (_getRowData_prophets_7 = getRowData.prophets[0]) === null || _getRowData_prophets_7 === void 0 ? void 0 : (_getRowData_prophets__prophet_code3 = _getRowData_prophets_7.prophet_code) === null || _getRowData_prophets__prophet_code3 === void 0 ? void 0 : _getRowData_prophets__prophet_code3.trim(),\n                \"Record id\": \"\",\n                \"Bank sort code\": sort_code,\n                \"Account number\": account_number,\n                \"Country code\": (getRowData === null || getRowData === void 0 ? void 0 : getRowData.country_code) == \"UK\" ? \"GB\" : getRowData === null || getRowData === void 0 ? void 0 : getRowData.country_code,\n                \"Account holder\": getRowData.company_name,\n                \"Currency number\": currencyId,\n                \"BACS currency\": getRowData === null || getRowData === void 0 ? void 0 : getRowData.bacs_currency_code,\n                \"Address Line 1\": \"\",\n                \"Address Line 2\": \"\",\n                \"BIC/Swift address\": swiftBicCode,\n                \"Internation bank reference code\": iban,\n                \"Account user id\": \"\",\n                \"Post code\": \"\"\n            };\n            const senbnk = {\n                \"Supplier code\": \"\",\n                Bankacid: \"\",\n                \"Header bank record id\": \"\",\n                \"Intermediary bank account id\": \"\",\n                \"Intermediary bank account id Internation bank reference code\": \"\"\n            };\n            // Parse and map contacts\n            const contacts = extractContacts(getRowData === null || getRowData === void 0 ? void 0 : (_getRowData_prophets_8 = getRowData.prophets[0]) === null || _getRowData_prophets_8 === void 0 ? void 0 : (_getRowData_prophets__prophet_code4 = _getRowData_prophets_8.prophet_code) === null || _getRowData_prophets__prophet_code4 === void 0 ? void 0 : _getRowData_prophets__prophet_code4.trim(), getRowData.contacts_json, getRowData === null || getRowData === void 0 ? void 0 : getRowData.company_name);\n            const newOrganizationData = {\n                \"Organization ID\": \"\",\n                \"Organization Name\": (getRowData === null || getRowData === void 0 ? void 0 : (_getRowData_prophets_9 = getRowData.prophets[0]) === null || _getRowData_prophets_9 === void 0 ? void 0 : _getRowData_prophets_9.prophet_code) ? getRowData === null || getRowData === void 0 ? void 0 : (_getRowData_prophets_10 = getRowData.prophets[0]) === null || _getRowData_prophets_10 === void 0 ? void 0 : (_getRowData_prophets__prophet_code5 = _getRowData_prophets_10.prophet_code) === null || _getRowData_prophets__prophet_code5 === void 0 ? void 0 : _getRowData_prophets__prophet_code5.trim() : \"\"\n            };\n            const newOrganizationRoleData = {\n                \"Organization ID\": \"\",\n                \"Organization Code\": \"\",\n                \"Role Type ID\": \"\",\n                Selected: \"\",\n                \"Organisation ID\": \"\",\n                \"Role Type ID\": \"\",\n                \"Contact ID\": \"\",\n                \"Contact ID Email Address\": \"\",\n                \"Contact ID Telephone\": \"\",\n                \"Contact ID Fax\": \"\"\n            };\n            const sheetSuppliersId = {\n                id: getRowData === null || getRowData === void 0 ? void 0 : getRowData.id,\n                supplierName: getRowData === null || getRowData === void 0 ? void 0 : getRowData.company_name,\n                isEmergencyAndFinanceNotComplete: getRowData === null || getRowData === void 0 ? void 0 : getRowData.isEmergencyAndFinanceNotComplete,\n                supplierCode: (getRowData === null || getRowData === void 0 ? void 0 : (_getRowData_prophets_11 = getRowData.prophets[0]) === null || _getRowData_prophets_11 === void 0 ? void 0 : _getRowData_prophets_11.prophet_code) ? getRowData === null || getRowData === void 0 ? void 0 : (_getRowData_prophets_12 = getRowData.prophets[0]) === null || _getRowData_prophets_12 === void 0 ? void 0 : (_getRowData_prophets__prophet_code6 = _getRowData_prophets_12.prophet_code) === null || _getRowData_prophets__prophet_code6 === void 0 ? void 0 : _getRowData_prophets__prophet_code6.trim() : \"\"\n            };\n            if (isSelected) {\n                setSupplierActiveData((prev)=>[\n                        ...prev,\n                        newSupplierActiveData\n                    ]);\n                setRoleData((prev)=>[\n                        ...prev,\n                        ...newRoleData\n                    ]);\n                setContactData((prev)=>[\n                        ...prev,\n                        ...contacts\n                    ]);\n                setOrganizationData((prev)=>[\n                        ...prev,\n                        newOrganizationData\n                    ]);\n                setOrganizationRoleData((prev)=>[\n                        ...prev,\n                        newOrganizationRoleData\n                    ]);\n                setSheetSupplierId((prev)=>[\n                        ...prev,\n                        sheetSuppliersId\n                    ]);\n                setSendacGroupData((prev)=>[\n                        ...prev,\n                        ...extractedSendacGroup\n                    ]);\n                setBankAc((prev)=>[\n                        ...prev,\n                        bankac\n                    ]);\n                setSenBnk((prev)=>[\n                        ...prev,\n                        senbnk\n                    ]);\n                if (Object.keys(UlpFil).length > 0) {\n                    setUlpFilData((prev)=>[\n                            ...prev,\n                            UlpFil\n                        ]);\n                }\n                setMultipleFilterISSData((prev)=>[\n                        ...prev,\n                        filteredISSExportData\n                    ]);\n            } else {\n                setMultipleFilterISSData((prev)=>prev.filter((item)=>item.id !== getRowData.id));\n                setSupplierActiveData((prev)=>prev.filter((item, index)=>{\n                        var _getRowData_prophets__prophet_code, _getRowData_prophets_;\n                        return index === 0 || item[\"Supplier code\"] !== (getRowData === null || getRowData === void 0 ? void 0 : (_getRowData_prophets_ = getRowData.prophets[0]) === null || _getRowData_prophets_ === void 0 ? void 0 : (_getRowData_prophets__prophet_code = _getRowData_prophets_.prophet_code) === null || _getRowData_prophets__prophet_code === void 0 ? void 0 : _getRowData_prophets__prophet_code.trim());\n                    }));\n                setUlpFilData((prev)=>prev.filter((item, index)=>{\n                        var _getRowData_prophets__prophet_code, _getRowData_prophets_;\n                        return index === 0 || item[\"Service Supplier Code\"] !== (getRowData === null || getRowData === void 0 ? void 0 : (_getRowData_prophets_ = getRowData.prophets[0]) === null || _getRowData_prophets_ === void 0 ? void 0 : (_getRowData_prophets__prophet_code = _getRowData_prophets_.prophet_code) === null || _getRowData_prophets__prophet_code === void 0 ? void 0 : _getRowData_prophets__prophet_code.trim());\n                    }));\n                setRoleData((prev)=>prev.filter((item, index)=>{\n                        var _getRowData_prophets__prophet_code, _getRowData_prophets_;\n                        return index === 0 || item[\"Supplier code\"] !== (getRowData === null || getRowData === void 0 ? void 0 : (_getRowData_prophets_ = getRowData.prophets[0]) === null || _getRowData_prophets_ === void 0 ? void 0 : (_getRowData_prophets__prophet_code = _getRowData_prophets_.prophet_code) === null || _getRowData_prophets__prophet_code === void 0 ? void 0 : _getRowData_prophets__prophet_code.trim());\n                    }));\n                // setContactData((prev) =>\n                // prev.filter(\n                //   (item, index) => index === 0 || item[\"Supplier code\"] !== contacts[0][\"Supplier code\"]\n                // )\n                // );\n                setContactData((prev)=>prev.filter((item, index)=>{\n                        if (contacts.length > 0) {\n                            return index === 0 || item[\"Supplier code\"] !== contacts[0][\"Supplier code\"];\n                        } else {\n                            // Handle the case when contacts array is empty\n                            return true; // or any other logic based on your requirements\n                        }\n                    }));\n                setOrganizationData((prev)=>prev.filter((item, index)=>{\n                        var _getRowData_prophets__prophet_code, _getRowData_prophets_;\n                        return index === 0 || item[\"Organization Name\"] !== (getRowData === null || getRowData === void 0 ? void 0 : (_getRowData_prophets_ = getRowData.prophets[0]) === null || _getRowData_prophets_ === void 0 ? void 0 : (_getRowData_prophets__prophet_code = _getRowData_prophets_.prophet_code) === null || _getRowData_prophets__prophet_code === void 0 ? void 0 : _getRowData_prophets__prophet_code.trim());\n                    }));\n                setOrganizationRoleData((prev)=>prev.filter((item, index)=>index === 0 || item[\"Organization ID\"] !== \"\"));\n                setSendacGroupData((prev)=>prev.filter((item, index)=>{\n                        var _extractedSendacGroup_;\n                        return index === 0 || item[\"Description\"] !== ((_extractedSendacGroup_ = extractedSendacGroup[0]) === null || _extractedSendacGroup_ === void 0 ? void 0 : _extractedSendacGroup_.Description);\n                    }));\n                setBankAc((prev)=>prev.filter((item, index)=>{\n                        var _getRowData_prophets__prophet_code, _getRowData_prophets_;\n                        return index === 0 || item[\"Supplier code\"] !== (getRowData === null || getRowData === void 0 ? void 0 : (_getRowData_prophets_ = getRowData.prophets[0]) === null || _getRowData_prophets_ === void 0 ? void 0 : (_getRowData_prophets__prophet_code = _getRowData_prophets_.prophet_code) === null || _getRowData_prophets__prophet_code === void 0 ? void 0 : _getRowData_prophets__prophet_code.trim());\n                    }));\n                setSenBnk((prev)=>prev.filter((item, index)=>index === 0 || item[\"Supplier code\"] !== \"\"));\n                setSheetSupplierId((prev)=>prev.filter((item, index)=>index === 0 || item[\"id\"] !== (getRowData === null || getRowData === void 0 ? void 0 : getRowData.id)));\n            }\n            setSupplierCheckedValue(supplierActiveData.length > 0 || roleData.length > 0 || contactData.length > 0 || organizationData.length > 0 || organizationRoleData.length > 0 || bankAc.length > 0 || senBnk.length > 0 || sendacGroupData.length > 0 || ulpFilData.length > 0 || multipleFilterISSData.length > 0);\n        } else {\n            if (event.node.selected) {\n                if (doesContainCancelledSupplier) {\n                    setContainsCancelledSupplier(true);\n                    return;\n                }\n                setIncompleteToast(true);\n                setTimeout(()=>{\n                    setIncompleteToast(false);\n                }, 3000);\n            }\n        }\n    };\n    const clearFiltersHandler = ()=>{\n        setRowData(allRowData);\n        setIsFiltered(false);\n        setIsFilteredName(\"\");\n        setProphetId(\"\");\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_toastify__WEBPACK_IMPORTED_MODULE_15__.ToastContainer, {\n                limit: 1\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\suppliers.js\",\n                lineNumber: 1770,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Layout__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                userData: userData,\n                blockScreen: blockScreen,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mr-20 md:mr-12 lg:mr-14\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-row md:flex-col lg:flex-row justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center border border-light-gray rounded-md px-3 w-auto md:w-fit lg:w-auto md:py-2 lg:py-1 md:mb-3 lg:mb-0 mr-2\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        id: \"default-checkbox\",\n                                                        type: \"radio\",\n                                                        value: \"default\",\n                                                        checked: selectedStatus === \"default\",\n                                                        className: \"w-5 h-5 text-blue border-theme-blue2 rounded\",\n                                                        onChange: handleFilterToggle\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\suppliers.js\",\n                                                        lineNumber: 1777,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        htmlFor: \"default-checkbox\",\n                                                        className: \"p-0 ml-3 labels\",\n                                                        children: \"Default\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\suppliers.js\",\n                                                        lineNumber: 1785,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\suppliers.js\",\n                                                lineNumber: 1776,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\suppliers.js\",\n                                            lineNumber: 1775,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center border border-light-gray rounded-md px-3 w-auto md:w-fit lg:w-auto md:py-2 lg:py-1 md:mb-3 lg:mb-0 mr-2\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        id: \"export-checkbox\",\n                                                        type: \"radio\",\n                                                        value: \"Exported\",\n                                                        checked: selectedStatus === \"Exported\",\n                                                        className: \"w-5 h-5 text-blue border-theme-blue2 rounded\",\n                                                        onChange: handleFilterToggle\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\suppliers.js\",\n                                                        lineNumber: 1792,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        htmlFor: \"export-checkbox\",\n                                                        className: \"p-0 ml-3 labels\",\n                                                        children: \"Exported\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\suppliers.js\",\n                                                        lineNumber: 1800,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\suppliers.js\",\n                                                lineNumber: 1791,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\suppliers.js\",\n                                            lineNumber: 1790,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center border border-light-gray rounded-md px-3 w-auto md:w-fit lg:w-auto md:py-2 lg:py-1 md:mb-3 lg:mb-0 mr-2\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        id: \"completed-radio\",\n                                                        type: \"radio\",\n                                                        value: \"Completed\",\n                                                        checked: selectedStatus === \"Completed\",\n                                                        className: \"w-5 h-5 text-blue border-theme-blue2 rounded\",\n                                                        onChange: handleFilterToggle\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\suppliers.js\",\n                                                        lineNumber: 1807,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        htmlFor: \"completed-radio\",\n                                                        className: \"p-0 ml-3 labels\",\n                                                        children: \"Completed\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\suppliers.js\",\n                                                        lineNumber: 1815,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\suppliers.js\",\n                                                lineNumber: 1806,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\suppliers.js\",\n                                            lineNumber: 1805,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center border border-light-gray rounded-md px-3 w-auto md:w-fit lg:w-auto md:py-2 lg:py-1 md:mb-3 lg:mb-0 mr-2\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        id: \"cancelled-radio\",\n                                                        type: \"radio\",\n                                                        value: \"Cancelled\",\n                                                        checked: selectedStatus === \"Cancelled\",\n                                                        className: \"w-5 h-5 text-blue border-theme-blue2 rounded\",\n                                                        onChange: handleFilterToggle\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\suppliers.js\",\n                                                        lineNumber: 1822,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        htmlFor: \"cancelled-radio\",\n                                                        className: \"p-0 ml-3 labels\",\n                                                        children: [\n                                                            \" \",\n                                                            \"Cancelled\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\suppliers.js\",\n                                                        lineNumber: 1830,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\suppliers.js\",\n                                                lineNumber: 1821,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\suppliers.js\",\n                                            lineNumber: 1820,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        isFiltered && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"p-3 py-1 flex items-center capitalize ml-5\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"b\", {\n                                                            className: \"mr-3\",\n                                                            children: \"Filtered On: \"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\suppliers.js\",\n                                                            lineNumber: 1840,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        \" \",\n                                                        isFilteredName\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\suppliers.js\",\n                                                    lineNumber: 1839,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    type: \"button\",\n                                                    onClick: clearFiltersHandler,\n                                                    className: \"flex h-[20px] border bg-red-500 text-white border-red-500 button rounded-md items-center !px-1 !py-1 ml-1\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fortawesome_react_fontawesome__WEBPACK_IMPORTED_MODULE_3__.FontAwesomeIcon, {\n                                                        icon: _fortawesome_free_solid_svg_icons__WEBPACK_IMPORTED_MODULE_24__.faClose,\n                                                        className: \"fw-bold\",\n                                                        size: \"lg\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\suppliers.js\",\n                                                        lineNumber: 1847,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\suppliers.js\",\n                                                    lineNumber: 1842,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\suppliers.js\",\n                                    lineNumber: 1774,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"relative block w-[47vh] text-gray-400 mt-0 pt-0\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"absolute px-4 py-1 pt-[0.4rem] 2xl:pt-1.5 text-black\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fortawesome_react_fontawesome__WEBPACK_IMPORTED_MODULE_3__.FontAwesomeIcon, {\n                                                        icon: _fortawesome_free_solid_svg_icons__WEBPACK_IMPORTED_MODULE_24__.faSearch,\n                                                        className: \"fw-bold\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\suppliers.js\",\n                                                        lineNumber: 1859,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\suppliers.js\",\n                                                    lineNumber: 1858,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"text\",\n                                                    id: \"filter-text-box\",\n                                                    placeholder: \"Search\",\n                                                    onInput: onFilterTextBoxChanged,\n                                                    className: \"block w-full px-4 pl-10 text-gray-500 placeholder-gray-400 searchbar border rounded-lg appearance-none form-input focus:outline-none\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\suppliers.js\",\n                                                    lineNumber: 1861,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\suppliers.js\",\n                                            lineNumber: 1857,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>exportFilteredData(),\n                                            className: \" border text-skin-primary border-skin-primary button rounded-md items-center !py-1\",\n                                            disabled: exportDisabled ? true : false,\n                                            children: \"Export\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\suppliers.js\",\n                                            lineNumber: 1869,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        userData.email == \"<EMAIL>\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: deleteAll,\n                                            className: \"border text-skin-primary border-skin-primary button rounded-md items-center !py-1\",\n                                            children: \"Delete\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\suppliers.js\",\n                                            lineNumber: 1877,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_10___default()), {\n                                                href: \"/supplier/add\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    className: \"button border border-skin-primary bg-skin-primary text-white rounded-md whitespace-nowrap\",\n                                                    children: \"Add Supplier\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\suppliers.js\",\n                                                    lineNumber: 1886,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\suppliers.js\",\n                                                lineNumber: 1885,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\suppliers.js\",\n                                            lineNumber: 1884,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\suppliers.js\",\n                                    lineNumber: 1856,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\suppliers.js\",\n                            lineNumber: 1773,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"my-5\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative ag-theme-alpine !rounded-md\",\n                                style: {\n                                    height: \"calc(100vh - 151px)\"\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ag_grid_react__WEBPACK_IMPORTED_MODULE_4__.AgGridReact, {\n                                        rowData: rowData,\n                                        ref: gridRef,\n                                        columnDefs: columnDefs,\n                                        defaultColDef: defaultColDef,\n                                        suppressRowClickSelection: true,\n                                        rowSelection: \"multiple\",\n                                        pagination: true,\n                                        paginationPageSize: pageSize,\n                                        onPageSizeChanged: handlePageSizeChange,\n                                        tooltipShowDelay: 0,\n                                        tooltipHideDelay: 1000,\n                                        onGridReady: handleGridReady,\n                                        onRowSelected: handleCheckboxEvent,\n                                        gridOptions: gridOptions\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\suppliers.js\",\n                                        lineNumber: 1898,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-start mt-2 pagination-style\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            htmlFor: \"page-size-select pagination\",\n                                            className: \"inputs\",\n                                            children: [\n                                                \"Show\",\n                                                \" \",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                    id: \"page-size-select\",\n                                                    onChange: handlePageSizeChange,\n                                                    value: pageSize,\n                                                    className: \"focus:outline-none\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: 10,\n                                                            children: \"10\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\suppliers.js\",\n                                                            lineNumber: 1923,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: 15,\n                                                            children: \"15\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\suppliers.js\",\n                                                            lineNumber: 1924,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: 25,\n                                                            children: \"25\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\suppliers.js\",\n                                                            lineNumber: 1925,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: 50,\n                                                            children: \"50\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\suppliers.js\",\n                                                            lineNumber: 1926,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: 100,\n                                                            children: \"100\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\suppliers.js\",\n                                                            lineNumber: 1927,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\suppliers.js\",\n                                                    lineNumber: 1917,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                \" \",\n                                                \"Entries\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\suppliers.js\",\n                                            lineNumber: 1915,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\suppliers.js\",\n                                        lineNumber: 1914,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\suppliers.js\",\n                                lineNumber: 1894,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\suppliers.js\",\n                            lineNumber: 1893,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\suppliers.js\",\n                    lineNumber: 1772,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\suppliers.js\",\n                lineNumber: 1771,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Dialog_Transition_headlessui_react__WEBPACK_IMPORTED_MODULE_25__.Transition, {\n                appear: true,\n                show: isOpenOption,\n                as: react__WEBPACK_IMPORTED_MODULE_2__.Fragment,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Dialog_Transition_headlessui_react__WEBPACK_IMPORTED_MODULE_25__.Dialog, {\n                    as: \"div\",\n                    className: \"relative z-10\",\n                    onClose: setIsOpenOption,\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Dialog_Transition_headlessui_react__WEBPACK_IMPORTED_MODULE_25__.Transition.Child, {\n                            as: react__WEBPACK_IMPORTED_MODULE_2__.Fragment,\n                            enter: \"ease-out duration-300\",\n                            enterFrom: \"opacity-0\",\n                            enterTo: \"opacity-100\",\n                            leave: \"ease-in duration-200\",\n                            leaveFrom: \"opacity-100\",\n                            leaveTo: \"opacity-0\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"fixed inset-0 bg-white bg-opacity-25 backdrop-blur-sm\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\suppliers.js\",\n                                lineNumber: 1947,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\suppliers.js\",\n                            lineNumber: 1938,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"fixed inset-0 overflow-y-auto\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-center min-h-full p-4 text-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Dialog_Transition_headlessui_react__WEBPACK_IMPORTED_MODULE_25__.Transition.Child, {\n                                    as: react__WEBPACK_IMPORTED_MODULE_2__.Fragment,\n                                    enter: \"ease-out duration-300\",\n                                    enterFrom: \"opacity-0 scale-95\",\n                                    enterTo: \"opacity-100 scale-100\",\n                                    leave: \"ease-in duration-200\",\n                                    leaveFrom: \"opacity-100 scale-100\",\n                                    leaveTo: \"opacity-0 scale-95\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Dialog_Transition_headlessui_react__WEBPACK_IMPORTED_MODULE_25__.Dialog.Panel, {\n                                        className: \"transform overflow-hidden rounded-xl bg-white  text-left align-middle shadow-xl transition-all\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-white w-[500px]\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex w-full bg-skin-primary h-[40px] items-center justify-between\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                                className: \"text-white items-center font-poppinsemibold pl-4 text-[20px]\",\n                                                                children: \"Select the export type\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\suppliers.js\",\n                                                                lineNumber: 1964,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fortawesome_react_fontawesome__WEBPACK_IMPORTED_MODULE_3__.FontAwesomeIcon, {\n                                                                icon: _fortawesome_free_solid_svg_icons__WEBPACK_IMPORTED_MODULE_24__.faClose,\n                                                                className: \"pr-4 text-white cursor-pointer\",\n                                                                onClick: closeOptionModal\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\suppliers.js\",\n                                                                lineNumber: 1967,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\suppliers.js\",\n                                                        lineNumber: 1963,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex justify-around items-center px-5\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center pl-0 pt-5\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                        name: \"exportType\",\n                                                                        id: \"internalExport\",\n                                                                        type: \"radio\",\n                                                                        className: \"mr-4\",\n                                                                        value: \"internalExport\",\n                                                                        checked: selectedExportType === \"internalExport\",\n                                                                        onChange: handleExportType\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\suppliers.js\",\n                                                                        lineNumber: 1975,\n                                                                        columnNumber: 25\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        className: \"font-poppinsregular text-[16px] text-charcoal-gray\",\n                                                                        htmlFor: \"internalExport\",\n                                                                        children: \"Internal\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\suppliers.js\",\n                                                                        lineNumber: 1984,\n                                                                        columnNumber: 25\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\suppliers.js\",\n                                                                lineNumber: 1974,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center pl-4 pt-5\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                        type: \"radio\",\n                                                                        name: \"exportType\",\n                                                                        id: \"ISS\",\n                                                                        className: \"mr-4\",\n                                                                        value: \"ISS\",\n                                                                        checked: selectedExportType === \"ISS\",\n                                                                        onChange: handleExportType\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\suppliers.js\",\n                                                                        lineNumber: 1992,\n                                                                        columnNumber: 25\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        htmlFor: \"ISS\",\n                                                                        className: \"font-poppinsregular text-[16px] text-charcoal-gray\",\n                                                                        children: \"ISS\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\suppliers.js\",\n                                                                        lineNumber: 2001,\n                                                                        columnNumber: 25\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\suppliers.js\",\n                                                                lineNumber: 1991,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\suppliers.js\",\n                                                        lineNumber: 1973,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\suppliers.js\",\n                                                lineNumber: 1962,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-center pb-4 pr-4 mt-2\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: exportFilteredData,\n                                                    disabled: !selectedExportType,\n                                                    className: \"font-circularstdbook rounded-md w-[100px] p-1 leading-5 mt-1 py-2 text-center hover:opacity-80 bg-skin-primary text-white\",\n                                                    children: \"Select\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\suppliers.js\",\n                                                    lineNumber: 2011,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\suppliers.js\",\n                                                lineNumber: 2010,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\suppliers.js\",\n                                        lineNumber: 1961,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\suppliers.js\",\n                                    lineNumber: 1952,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\suppliers.js\",\n                                lineNumber: 1951,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\suppliers.js\",\n                            lineNumber: 1950,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\suppliers.js\",\n                    lineNumber: 1937,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\suppliers.js\",\n                lineNumber: 1936,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Dialog_Transition_headlessui_react__WEBPACK_IMPORTED_MODULE_25__.Transition, {\n                appear: true,\n                show: emailStatusPopup,\n                as: react__WEBPACK_IMPORTED_MODULE_2__.Fragment,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Dialog_Transition_headlessui_react__WEBPACK_IMPORTED_MODULE_25__.Dialog, {\n                    as: \"div\",\n                    className: \"relative z-10\",\n                    onClose: closeEmailPopup,\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Dialog_Transition_headlessui_react__WEBPACK_IMPORTED_MODULE_25__.Transition.Child, {\n                            as: react__WEBPACK_IMPORTED_MODULE_2__.Fragment,\n                            enter: \"ease-out duration-300\",\n                            enterFrom: \"opacity-0\",\n                            enterTo: \"opacity-100\",\n                            leave: \"ease-in duration-200\",\n                            leaveFrom: \"opacity-100\",\n                            leaveTo: \"opacity-0\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"fixed inset-0 bg-black bg-opacity-25 backdrop-blur-sm\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\suppliers.js\",\n                                lineNumber: 2036,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\suppliers.js\",\n                            lineNumber: 2027,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"fixed inset-0 overflow-y-auto\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-center min-h-full p-4 text-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Dialog_Transition_headlessui_react__WEBPACK_IMPORTED_MODULE_25__.Transition.Child, {\n                                    as: react__WEBPACK_IMPORTED_MODULE_2__.Fragment,\n                                    enter: \"ease-out duration-300\",\n                                    enterFrom: \"opacity-0 scale-95\",\n                                    enterTo: \"opacity-100 scale-100\",\n                                    leave: \"ease-in duration-200\",\n                                    leaveFrom: \"opacity-100 scale-100\",\n                                    leaveTo: \"opacity-0 scale-95\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Dialog_Transition_headlessui_react__WEBPACK_IMPORTED_MODULE_25__.Dialog.Panel, {\n                                        className: \" w-[45%] transform overflow-hidden rounded-xl bg-white  text-left align-middle shadow-xl transition-all\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative bg-white rounded-lg shadow\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-start justify-between p-8 rounded-t\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"flex flex-row text-xl font-semibold text-gray-900  items-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"flex justify-center items-center border border-skin-primary text-skin-primary w-[25px] h-[25px] text-center leading-5 rounded-full text-base me-4\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fortawesome_react_fontawesome__WEBPACK_IMPORTED_MODULE_3__.FontAwesomeIcon, {\n                                                                            icon: _fortawesome_free_solid_svg_icons__WEBPACK_IMPORTED_MODULE_24__.faInfo\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\suppliers.js\",\n                                                                            lineNumber: 2057,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        \" \"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\suppliers.js\",\n                                                                    lineNumber: 2056,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                \" \",\n                                                                \"Status Message\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\suppliers.js\",\n                                                            lineNumber: 2055,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: closeEmailPopup,\n                                                            type: \"button\",\n                                                            className: \"text-black bg-transparent hover:bg-gray-200 hover:text-gray-900 rounded-lg text-xl w-8 h-8 ml-auto inline-flex justify-center items-center \",\n                                                            \"data-modal-hide\": \"default-modal\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fortawesome_react_fontawesome__WEBPACK_IMPORTED_MODULE_3__.FontAwesomeIcon, {\n                                                                    icon: _fortawesome_free_solid_svg_icons__WEBPACK_IMPORTED_MODULE_24__.faXmark,\n                                                                    className: \"text-skin-primary\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\suppliers.js\",\n                                                                    lineNumber: 2067,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                \" \"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\suppliers.js\",\n                                                            lineNumber: 2061,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\suppliers.js\",\n                                                    lineNumber: 2054,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"p-8 py-0 space-y-6\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-base xl:text-md 2xl:text-lg leading-relaxed mt-2\",\n                                                        children: popupMessage\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\suppliers.js\",\n                                                        lineNumber: 2075,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\suppliers.js\",\n                                                    lineNumber: 2074,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-end p-6 space-x-2 justify-end\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: closeEmailPopup,\n                                                        \"data-modal-hide\": \"default-modal\",\n                                                        type: \"button\",\n                                                        className: \"border text-dark-gray focus:ring-4 focus:outline-none focus:ring-blue-300 font-medium rounded-md text-md px-6 py-2 text-center \",\n                                                        children: \"Ok\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\suppliers.js\",\n                                                        lineNumber: 2081,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\suppliers.js\",\n                                                    lineNumber: 2080,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\suppliers.js\",\n                                            lineNumber: 2052,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\suppliers.js\",\n                                        lineNumber: 2050,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\suppliers.js\",\n                                    lineNumber: 2041,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\suppliers.js\",\n                                lineNumber: 2040,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\suppliers.js\",\n                            lineNumber: 2039,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\suppliers.js\",\n                    lineNumber: 2026,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\suppliers.js\",\n                lineNumber: 2025,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true);\n};\n_s(suppliers, \"H9gtB91Zay9/ScaLTy1kYxLBRMI=\", false, function() {\n    return [\n        next_router__WEBPACK_IMPORTED_MODULE_11__.useRouter,\n        _utils_rolePermissionsContext__WEBPACK_IMPORTED_MODULE_21__.usePermissions,\n        _utils_loaders_loadingContext__WEBPACK_IMPORTED_MODULE_17__.useLoading,\n        _azure_msal_react__WEBPACK_IMPORTED_MODULE_19__.useMsal,\n        _utils_userContext__WEBPACK_IMPORTED_MODULE_20__.useUser\n    ];\n});\nvar __N_SSP = true;\n/* harmony default export */ __webpack_exports__[\"default\"] = (suppliers);\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevExports = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevExports) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports on update so we can compare the boundary\n                // signatures.\n                module.hot.dispose(function (data) {\n                    data.prevExports = currentExports;\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevExports !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevExports, currentExports)) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevExports !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./pages/suppliers.js\n"));

/***/ })

});