"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/login",{

/***/ "./public/images/dps-logo.png":
/*!************************************!*\
  !*** ./public/images/dps-logo.png ***!
  \************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony default export */ __webpack_exports__[\"default\"] = ({\"src\":\"/_next/static/media/dps-logo.b393787d.png\",\"height\":70,\"width\":69,\"blurDataURL\":\"/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Fdps-logo.b393787d.png&w=8&q=70\",\"blurWidth\":8,\"blurHeight\":8});//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9wdWJsaWMvaW1hZ2VzL2Rwcy1sb2dvLnBuZyIsIm1hcHBpbmdzIjoiO0FBQUEsK0RBQWUsQ0FBQyxrTUFBa00iLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vcHVibGljL2ltYWdlcy9kcHMtbG9nby5wbmc/MWFhZCJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCB7XCJzcmNcIjpcIi9fbmV4dC9zdGF0aWMvbWVkaWEvZHBzLWxvZ28uYjM5Mzc4N2QucG5nXCIsXCJoZWlnaHRcIjo3MCxcIndpZHRoXCI6NjksXCJibHVyRGF0YVVSTFwiOlwiL19uZXh0L2ltYWdlP3VybD0lMkZfbmV4dCUyRnN0YXRpYyUyRm1lZGlhJTJGZHBzLWxvZ28uYjM5Mzc4N2QucG5nJnc9OCZxPTcwXCIsXCJibHVyV2lkdGhcIjo4LFwiYmx1ckhlaWdodFwiOjh9OyJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///./public/images/dps-logo.png\n"));

/***/ }),

/***/ "./public/images/microsoft-icon.png":
/*!******************************************!*\
  !*** ./public/images/microsoft-icon.png ***!
  \******************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony default export */ __webpack_exports__[\"default\"] = ({\"src\":\"/_next/static/media/microsoft-icon.92d4f7a6.png\",\"height\":42,\"width\":42,\"blurDataURL\":\"/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Fmicrosoft-icon.92d4f7a6.png&w=8&q=70\",\"blurWidth\":8,\"blurHeight\":8});//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9wdWJsaWMvaW1hZ2VzL21pY3Jvc29mdC1pY29uLnBuZyIsIm1hcHBpbmdzIjoiO0FBQUEsK0RBQWUsQ0FBQyw4TUFBOE0iLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vcHVibGljL2ltYWdlcy9taWNyb3NvZnQtaWNvbi5wbmc/YmRiZCJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCB7XCJzcmNcIjpcIi9fbmV4dC9zdGF0aWMvbWVkaWEvbWljcm9zb2Z0LWljb24uOTJkNGY3YTYucG5nXCIsXCJoZWlnaHRcIjo0MixcIndpZHRoXCI6NDIsXCJibHVyRGF0YVVSTFwiOlwiL19uZXh0L2ltYWdlP3VybD0lMkZfbmV4dCUyRnN0YXRpYyUyRm1lZGlhJTJGbWljcm9zb2Z0LWljb24uOTJkNGY3YTYucG5nJnc9OCZxPTcwXCIsXCJibHVyV2lkdGhcIjo4LFwiYmx1ckhlaWdodFwiOjh9OyJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///./public/images/microsoft-icon.png\n"));

/***/ }),

/***/ "./public/images/user-account.png":
/*!****************************************!*\
  !*** ./public/images/user-account.png ***!
  \****************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony default export */ __webpack_exports__[\"default\"] = ({\"src\":\"/_next/static/media/user-account.e319c333.png\",\"height\":61,\"width\":61,\"blurDataURL\":\"/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Fuser-account.e319c333.png&w=8&q=70\",\"blurWidth\":8,\"blurHeight\":8});//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9wdWJsaWMvaW1hZ2VzL3VzZXItYWNjb3VudC5wbmciLCJtYXBwaW5ncyI6IjtBQUFBLCtEQUFlLENBQUMsME1BQTBNIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL3B1YmxpYy9pbWFnZXMvdXNlci1hY2NvdW50LnBuZz9mM2FiIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IHtcInNyY1wiOlwiL19uZXh0L3N0YXRpYy9tZWRpYS91c2VyLWFjY291bnQuZTMxOWMzMzMucG5nXCIsXCJoZWlnaHRcIjo2MSxcIndpZHRoXCI6NjEsXCJibHVyRGF0YVVSTFwiOlwiL19uZXh0L2ltYWdlP3VybD0lMkZfbmV4dCUyRnN0YXRpYyUyRm1lZGlhJTJGdXNlci1hY2NvdW50LmUzMTljMzMzLnBuZyZ3PTgmcT03MFwiLFwiYmx1cldpZHRoXCI6OCxcImJsdXJIZWlnaHRcIjo4fTsiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///./public/images/user-account.png\n"));

/***/ }),

/***/ "./components/LoginSection.js":
/*!************************************!*\
  !*** ./components/LoginSection.js ***!
  \************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/image */ \"./node_modules/next/image.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_image__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _public_images_microsoft_icon_png__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../public/images/microsoft-icon.png */ \"./public/images/microsoft-icon.png\");\n/* harmony import */ var _public_images_dps_logo_png__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../public/images/dps-logo.png */ \"./public/images/dps-logo.png\");\n/* harmony import */ var _public_images_user_account_png__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../public/images/user-account.png */ \"./public/images/user-account.png\");\n/* harmony import */ var _azure_msal_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @azure/msal-react */ \"./node_modules/@azure/msal-react/dist/index.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_7__);\n/* harmony import */ var _utils_loaders_loadingContext__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/utils/loaders/loadingContext */ \"./utils/loaders/loadingContext.js\");\n/* harmony import */ var js_cookie__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! js-cookie */ \"./node_modules/js-cookie/dist/js.cookie.mjs\");\n/* harmony import */ var _utils_auth_authConfig__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/utils/auth/authConfig */ \"./utils/auth/authConfig.js\");\n/* harmony import */ var _utils_auth_auth__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/utils/auth/auth */ \"./utils/auth/auth.js\");\n/* harmony import */ var _utils_userContext__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/utils/userContext */ \"./utils/userContext.js\");\n/* harmony import */ var _services_apiConfig__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/services/apiConfig */ \"./services/apiConfig.js\");\n/* harmony import */ var react_toastify__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! react-toastify */ \"./node_modules/react-toastify/dist/react-toastify.esm.mjs\");\n/* harmony import */ var react_toastify_dist_ReactToastify_css__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! react-toastify/dist/ReactToastify.css */ \"./node_modules/react-toastify/dist/ReactToastify.css\");\n/* harmony import */ var react_toastify_dist_ReactToastify_css__WEBPACK_IMPORTED_MODULE_15___default = /*#__PURE__*/__webpack_require__.n(react_toastify_dist_ReactToastify_css__WEBPACK_IMPORTED_MODULE_15__);\n/* harmony import */ var _utils_themeContext__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @/utils/themeContext */ \"./utils/themeContext.js\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst LoginSection = ()=>{\n    _s();\n    const { userDetails, updateToken } = (0,_utils_userContext__WEBPACK_IMPORTED_MODULE_12__.useUser)();\n    const { instance, accounts } = (0,_azure_msal_react__WEBPACK_IMPORTED_MODULE_6__.useMsal)();\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_7__.useRouter)();\n    const { redirect } = router.query;\n    const { setIsLoading } = (0,_utils_loaders_loadingContext__WEBPACK_IMPORTED_MODULE_8__.useLoading)();\n    const [accountExist, setAccountExist] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const { setThemeColor } = (0,_utils_themeContext__WEBPACK_IMPORTED_MODULE_16__.useTheme)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        setAccountExist((accounts === null || accounts === void 0 ? void 0 : accounts.length) > 0);\n    }, [\n        accounts\n    ]);\n    function extractCompanyName(email) {\n        const domain = email === null || email === void 0 ? void 0 : email.split(\"@\")[1];\n        const companyName = domain === null || domain === void 0 ? void 0 : domain.split(\".\")[0];\n        return companyName;\n    }\n    const getUserDetails = async (accessToken)=>{\n        try {\n            const response = await fetch(\"https://graph.microsoft.com/v1.0/me?$select=displayName,mail,companyName\", {\n                headers: {\n                    Authorization: \"Bearer \".concat(accessToken),\n                    \"Content-Type\": \"application/json\"\n                }\n            });\n            const data = await response.json();\n            return data;\n        } catch (error) {\n            console.error(\"Error fetching user data:\", error);\n            return null;\n        }\n    };\n    const signInHandler = async ()=>{\n        setIsLoading(true);\n        try {\n            const request = {\n                prompt: \"select_account\",\n                scopes: [\n                    \"User.Read\",\n                    \"Directory.Read.All\"\n                ]\n            };\n            const loginObj = await instance.loginPopup(request);\n            instance.setActiveAccount(loginObj.account);\n            scheduleTokenRenewal(loginObj);\n            js_cookie__WEBPACK_IMPORTED_MODULE_9__[\"default\"].set(\"token\", loginObj === null || loginObj === void 0 ? void 0 : loginObj.accessToken, {\n                expires: 365\n            });\n            let result = await (0,_utils_auth_auth__WEBPACK_IMPORTED_MODULE_11__.setUserCookie)(loginObj, instance, accounts[0], updateToken);\n            if (loginObj) {\n                var _loginObj_account;\n                const userData = await getUserDetails(loginObj.accessToken);\n                if (userData) {\n                    js_cookie__WEBPACK_IMPORTED_MODULE_9__[\"default\"].set(\"ADCompanyName\", userData.companyName);\n                } else {\n                    console.warn(\"Company name not found in AD.\");\n                }\n                const company = extractCompanyName(loginObj === null || loginObj === void 0 ? void 0 : (_loginObj_account = loginObj.account) === null || _loginObj_account === void 0 ? void 0 : _loginObj_account.username);\n                // const company = extractCompanyName(\"<EMAIL>\");\n                // const company = extractCompanyName(\"<EMAIL>\");\n                // const company = extractCompanyName(\"<EMAIL>\");\n                // localStorage.setItem(\"domain\", company);\n                js_cookie__WEBPACK_IMPORTED_MODULE_9__[\"default\"].set(\"company\", company);\n                const serverAddress = _services_apiConfig__WEBPACK_IMPORTED_MODULE_13__.apiConfig.serverAddress;\n                await fetch(\"\".concat(serverAddress, \"users/getTheme\"), {\n                    method: \"POST\",\n                    headers: {\n                        Accept: \"application/json\",\n                        \"Content-Type\": \"application/json\"\n                    },\n                    body: JSON.stringify({\n                        company: company,\n                        ADCompany: userData.companyName\n                    })\n                }).then((res)=>{\n                    // if(res.status === 400 || res.status === 401){\n                    //   setTimeout(function(){\n                    //     logoutHandler(instance);\n                    //     router.push('/login');\n                    //   }, 1000);\n                    // }\n                    if (res.status === 200) {\n                        return res.json();\n                    }\n                    return Promise.reject(res);\n                }).then((json)=>{\n                    let res = json;\n                    if (res) {\n                        js_cookie__WEBPACK_IMPORTED_MODULE_9__[\"default\"].set(\"theme\", JSON.stringify(res === null || res === void 0 ? void 0 : res.themeColour), {\n                            expires: 365\n                        });\n                        // localStorage.setItem(\"company\", company);\n                        js_cookie__WEBPACK_IMPORTED_MODULE_9__[\"default\"].set(\"company\", company);\n                        setThemeColor(res === null || res === void 0 ? void 0 : res.themeColour);\n                    } else {\n                        react_toastify__WEBPACK_IMPORTED_MODULE_14__.toast.error(\"Unauthorized User. Please try again.\", {\n                            position: \"top-right\"\n                        });\n                    }\n                }).catch((error)=>{\n                    react_toastify__WEBPACK_IMPORTED_MODULE_14__.toast.error(error.statusText, {\n                        position: \"top-right\"\n                    });\n                });\n                await fetch(\"\".concat(serverAddress, \"logs/logLogin\"), {\n                    method: \"POST\",\n                    headers: {\n                        \"Content-Type\": \"application/json\"\n                    },\n                    body: JSON.stringify({\n                        userData: loginObj === null || loginObj === void 0 ? void 0 : loginObj.account,\n                        company: userData.companyName\n                    })\n                });\n            }\n            if (redirect) {\n                // setIsLoading(false);\n                router.replace(redirect);\n            } else {\n                router.replace(\"/suppliers\");\n            }\n        } catch (error) {\n            //console.error(\"Error during login:\", error);\n            //toast.error(error.message, {\n            //  position: \"top-right\"\n            //});\n            setIsLoading(false);\n            return;\n        }\n    };\n    const useThisAccountHandler = async ()=>{\n        setIsLoading(true);\n        // Ensure active account is set or pass account in request\n        const activeAccount = instance.getActiveAccount();\n        if (!activeAccount) {\n            console.error(\"No active account found!\");\n            return;\n        }\n        const loginObj = await instance.acquireTokenSilent({\n            scopes: [\n                \"User.Read\"\n            ],\n            account: activeAccount,\n            forceRefresh: true\n        });\n        (0,_utils_auth_auth__WEBPACK_IMPORTED_MODULE_11__.setUserCookie)(loginObj, instance, accounts[0]);\n        instance.setActiveAccount(loginObj.account);\n        scheduleTokenRenewal(loginObj);\n        js_cookie__WEBPACK_IMPORTED_MODULE_9__[\"default\"].set(\"token\", loginObj === null || loginObj === void 0 ? void 0 : loginObj.accessToken, {\n            expires: 365\n        });\n        let result = await (0,_utils_auth_auth__WEBPACK_IMPORTED_MODULE_11__.setUserCookie)(loginObj, instance, accounts[0], updateToken);\n        if (loginObj) {\n            var _loginObj_account;\n            const userData = await getUserDetails(loginObj.accessToken);\n            if (userData) {\n                // Cookies.set(\"company\", userData.companyName);\n                js_cookie__WEBPACK_IMPORTED_MODULE_9__[\"default\"].set(\"ADCompanyName\", userData.companyName);\n            } else {\n                console.warn(\"Company name not found in AD.\");\n            }\n            const company = extractCompanyName(loginObj === null || loginObj === void 0 ? void 0 : (_loginObj_account = loginObj.account) === null || _loginObj_account === void 0 ? void 0 : _loginObj_account.username);\n            // const company = extractCompanyName(\"<EMAIL>\");\n            // const company = extractCompanyName(\"<EMAIL>\");\n            // const company = extractCompanyName(\"<EMAIL>\");\n            // localStorage.setItem(\"domain\", company);\n            js_cookie__WEBPACK_IMPORTED_MODULE_9__[\"default\"].set(\"company\", company);\n            const serverAddress = _services_apiConfig__WEBPACK_IMPORTED_MODULE_13__.apiConfig.serverAddress;\n            fetch(\"\".concat(serverAddress, \"users/getTheme\"), {\n                method: \"POST\",\n                headers: {\n                    Accept: \"application/json\",\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    company: company,\n                    ADCompany: userData.companyName\n                })\n            }).then((res)=>{\n                // if(res.status === 400 || res.status === 401){\n                //   setTimeout(function(){\n                //     logoutHandler(instance);\n                //     router.push('/login');\n                //   }, 1000);\n                // }\n                if (res.status === 200) {\n                    return res.json();\n                }\n                return Promise.reject(res);\n            }).then((json)=>{\n                let res = json;\n                if (res) {\n                    js_cookie__WEBPACK_IMPORTED_MODULE_9__[\"default\"].set(\"theme\", JSON.stringify(res === null || res === void 0 ? void 0 : res.themeColour), {\n                        expires: 365\n                    });\n                    // localStorage.setItem(\"company\", company);\n                    js_cookie__WEBPACK_IMPORTED_MODULE_9__[\"default\"].set(\"company\", company);\n                    setThemeColor(res === null || res === void 0 ? void 0 : res.themeColour);\n                } else {\n                    react_toastify__WEBPACK_IMPORTED_MODULE_14__.toast.error(\"Unauthorized User. Please try again.\", {\n                        position: \"top-right\"\n                    });\n                }\n            }).catch((error)=>{\n                react_toastify__WEBPACK_IMPORTED_MODULE_14__.toast.error(error.statusText, {\n                    position: \"top-right\"\n                });\n            });\n            fetch(\"\".concat(serverAddress, \"logs/logLogin\"), {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    userData: loginObj.account,\n                    company: userData.companyName\n                })\n            });\n        }\n        if (redirect) {\n            router.push(redirect);\n        } else {\n            router.push(\"/suppliers\");\n        }\n    };\n    function scheduleTokenRenewal(loginObj) {\n        setTimeout(async ()=>{\n            try {\n                // Ensure active account is set or pass account in request\n                const activeAccount = instance.getActiveAccount();\n                if (!activeAccount) {\n                    console.error(\"No active account found!\");\n                    return;\n                }\n                const tokenResponse = await instance.acquireTokenSilent({\n                    scopes: [\n                        \"User.Read\"\n                    ],\n                    account: activeAccount,\n                    forceRefresh: true\n                });\n                // Refresh the access token in the cookie\n                js_cookie__WEBPACK_IMPORTED_MODULE_9__[\"default\"].set(\"token\", tokenResponse.accessToken, {\n                    expires: 365\n                });\n                // ReSchedule the next token renewal\n                scheduleTokenRenewal(tokenResponse);\n            } catch (error) {\n                console.error(\"Silent token acquisition failed: \", error);\n                signInHandler();\n            }\n        }, loginObj.expiresOn.getTime() - Date.now() - 60000);\n    }\n    // const router = useRouter();\n    // const [ userdata, setUserdata ] = useState(\"\");\n    // const [errorMessage, setErrorMessage] = useState(\"\");\n    // useEffect(() => {\n    //   const userdetails = Cookies.get('user');\n    //   const sessionEmail = sessionStorage.getItem('useremail');\n    //   if(userdetails && sessionEmail){\n    //     router.push('/suppliers');\n    //   }\n    //   if(userdetails){\n    //     const cookieParse = JSON.parse(userdetails);\n    //     setUserdata(cookieParse);\n    //   } else {\n    //     setUserdata('');\n    //   }\n    //   if(router.query?.message == 'text')\n    //   {\n    //     setErrorMessage(\"Authentication is not allowed\");\n    //   }\n    // }, []);\n    // const { login } = useAuth();\n    // msalInstance.handleRedirectPromise();\n    // const handleLogin = async () => {\n    //   await login();\n    // };\n    // Cookies.remove('userdatails');\n    // const setUserDetails = () => {\n    //   sessionStorage.setItem('useremail', userdata.email_id);\n    //   router.push('suppliers');\n    // }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-end\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\LoginSection.js\",\n                    lineNumber: 334,\n                    columnNumber: 11\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-col items-center justify-center h-full\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"font-semibold text-2xl xl:text-4xl 2xl:text-5xl py-2\",\n                            children: \"Get Started Now\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\LoginSection.js\",\n                            lineNumber: 336,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-base xl:text-lg 2xl:text-xl my-1\",\n                            children: \"Please login to access your account\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\LoginSection.js\",\n                            lineNumber: 339,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-row items-center rounded-md cursor-pointer\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                className: \"flex items-center rounded-md border border-gray-300 p-4 gap-5 lg:gap-9 mt-4\",\n                                onClick: signInHandler,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                        src: _public_images_microsoft_icon_png__WEBPACK_IMPORTED_MODULE_3__[\"default\"],\n                                        className: \"\",\n                                        width: 20,\n                                        height: 20,\n                                        alt: \"Microsoft Logo\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\LoginSection.js\",\n                                        lineNumber: 347,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-lg text-center lg:text-left\",\n                                        children: \"Sign in with Microsoft\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\LoginSection.js\",\n                                        lineNumber: 354,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\LoginSection.js\",\n                                lineNumber: 343,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\LoginSection.js\",\n                            lineNumber: 342,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\LoginSection.js\",\n                    lineNumber: 335,\n                    columnNumber: 11\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\LoginSection.js\",\n            lineNumber: 330,\n            columnNumber: 9\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\LoginSection.js\",\n        lineNumber: 329,\n        columnNumber: 5\n    }, undefined);\n};\n_s(LoginSection, \"Yi+toZE7kHZ33aw2v8QxPSUp7vE=\", false, function() {\n    return [\n        _utils_userContext__WEBPACK_IMPORTED_MODULE_12__.useUser,\n        _azure_msal_react__WEBPACK_IMPORTED_MODULE_6__.useMsal,\n        next_router__WEBPACK_IMPORTED_MODULE_7__.useRouter,\n        _utils_loaders_loadingContext__WEBPACK_IMPORTED_MODULE_8__.useLoading,\n        _utils_themeContext__WEBPACK_IMPORTED_MODULE_16__.useTheme\n    ];\n});\n_c = LoginSection;\n/* harmony default export */ __webpack_exports__[\"default\"] = (LoginSection);\nvar _c;\n$RefreshReg$(_c, \"LoginSection\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevExports = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevExports) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports on update so we can compare the boundary\n                // signatures.\n                module.hot.dispose(function (data) {\n                    data.prevExports = currentExports;\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevExports !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevExports, currentExports)) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevExports !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/LoginSection.js\n"));

/***/ }),

/***/ "./pages/login.js":
/*!************************!*\
  !*** ./pages/login.js ***!
  \************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ Login; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_LoginBanner__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/LoginBanner */ \"./components/LoginBanner.js\");\n/* harmony import */ var _fortawesome_react_fontawesome__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @fortawesome/react-fontawesome */ \"./node_modules/@fortawesome/react-fontawesome/index.es.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/image */ \"./node_modules/next/image.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_image__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _public_images_microsoft_icon_png__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../public/images/microsoft-icon.png */ \"./public/images/microsoft-icon.png\");\n/* harmony import */ var _public_images_dps_logo_png__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../public/images/dps-logo.png */ \"./public/images/dps-logo.png\");\n/* harmony import */ var _services_apiConfig__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/services/apiConfig */ \"./services/apiConfig.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_8___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_8__);\n/* harmony import */ var _components_LoginSection__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/LoginSection */ \"./components/LoginSection.js\");\n/* harmony import */ var js_cookie__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! js-cookie */ \"./node_modules/js-cookie/dist/js.cookie.mjs\");\n/* harmony import */ var _utils_userContext__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/utils/userContext */ \"./utils/userContext.js\");\n/* harmony import */ var _utils_getCookieData__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/utils/getCookieData */ \"./utils/getCookieData.js\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction Login() {\n    _s();\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_8__.useRouter)();\n    const { userDetails, updateToken } = (0,_utils_userContext__WEBPACK_IMPORTED_MODULE_11__.useUser)();\n    let serverAddress = _services_apiConfig__WEBPACK_IMPORTED_MODULE_7__.apiConfig.serverAddress;\n    const [email, setEmail] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [emailId, setEmailId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [name, setName] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [role, setRole] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [id, setId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [password, setPassword] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [validationMessageVisible, setValidationMessageVisible] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [validationMessage, setValidationMessage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [roleId, setRoleId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const user = (0,_utils_getCookieData__WEBPACK_IMPORTED_MODULE_12__.getCookieData)(\"user\");\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        localStorage.removeItem(\"superUser\");\n        localStorage.removeItem(\"domain\");\n    }, []);\n    const handleLogin = ()=>{\n        setValidationMessageVisible(false);\n        setIsLoading(true);\n        fetch(\"\".concat(serverAddress, \"user/authenticateUser\"), {\n            method: \"POST\",\n            headers: {\n                Accept: \"application/json\",\n                \"Content-Type\": \"application/json\",\n                Authorization: \"Bearer \".concat(user === null || user === void 0 ? void 0 : user.token)\n            },\n            body: JSON.stringify({\n                email: email === null || email === void 0 ? void 0 : email.toLowerCase(),\n                password: password\n            })\n        }).then((res)=>{\n            if (res.status === 200 || res.status == 409) {\n                return res.json();\n            }\n            return Promise.reject(res);\n        }).then((json)=>{\n            if (json.status != 409) {\n                var _json_user_, _json_user_1, _json_user_2, _json_user_3, _json_user_4, _json_user_5;\n                setRoleId((_json_user_ = json.user[0]) === null || _json_user_ === void 0 ? void 0 : _json_user_.role_id);\n                setEmailId((_json_user_1 = json.user[0]) === null || _json_user_1 === void 0 ? void 0 : _json_user_1.email);\n                setName(((_json_user_2 = json.user[0]) === null || _json_user_2 === void 0 ? void 0 : _json_user_2.first_name) + \" \" + ((_json_user_3 = json.user[0]) === null || _json_user_3 === void 0 ? void 0 : _json_user_3.last_name));\n                setRole((_json_user_4 = json.user[0]) === null || _json_user_4 === void 0 ? void 0 : _json_user_4.role_id);\n                setId((_json_user_5 = json.user[0]) === null || _json_user_5 === void 0 ? void 0 : _json_user_5.id);\n                setValidationMessageVisible(true);\n                setValidationMessage(json.message);\n                if (json.message == \"Login Successful!\") {\n                    setValidationMessageVisible(false);\n                    localStorage.setItem(\"userDetails\", JSON.stringify(json.user[0]));\n                    router.replace(\"/suppliers\");\n                }\n            } else {\n                setValidationMessageVisible(true);\n                setValidationMessage(json.message);\n            }\n        }).catch((err)=>{\n            setValidationMessageVisible(true);\n            setValidationMessage(err);\n        }).finally(()=>{\n            setIsLoading(false);\n        });\n    };\n    const checkKeyPress = (e)=>{\n        const { key, keyCode } = e;\n        if (keyCode === 13) {\n            handleLogin();\n        }\n    };\n    if (true) {\n        localStorage.setItem(\"email\", emailId);\n        localStorage.setItem(\"name\", name);\n        localStorage.setItem(\"role\", role);\n        localStorage.setItem(\"id\", id);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative flex flex-col items-center justify-center w-full p-4 xl:w-1/2 font-Inter bg-white\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_LoginSection__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\login.js\",\n                    lineNumber: 103,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\login.js\",\n                lineNumber: 102,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_LoginBanner__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\login.js\",\n                lineNumber: 105,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\login.js\",\n        lineNumber: 101,\n        columnNumber: 5\n    }, this);\n} // export const getServerSideProps = async (context) => {\n //   const token = context.req.cookies.token;\n //   if (token) {\n //     return {\n //       redirect: {\n //         destination: \"/suppliers\",\n //         permanent: false,\n //       },\n //     };\n //   }\n // };\n_s(Login, \"KwpL7+XEayHIs4AGNH0dchqX89Y=\", false, function() {\n    return [\n        next_router__WEBPACK_IMPORTED_MODULE_8__.useRouter,\n        _utils_userContext__WEBPACK_IMPORTED_MODULE_11__.useUser\n    ];\n});\n_c = Login;\nvar _c;\n$RefreshReg$(_c, \"Login\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevExports = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevExports) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports on update so we can compare the boundary\n                // signatures.\n                module.hot.dispose(function (data) {\n                    data.prevExports = currentExports;\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevExports !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevExports, currentExports)) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevExports !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./pages/login.js\n"));

/***/ }),

/***/ "./services/apiConfig.js":
/*!*******************************!*\
  !*** ./services/apiConfig.js ***!
  \*******************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   apiConfig: function() { return /* binding */ apiConfig; }\n/* harmony export */ });\nlet apiConfig = {\n    // serverAddress: `http://************:9000/api/`,\n    //serverAddress: `http://**************:9000/api/`,\n    serverAddress: \"\".concat(\"http://localhost:8081\", \"/api/\"),\n    socketAddress: \"\".concat(\"http://localhost:8081\")\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevExports = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevExports) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports on update so we can compare the boundary\n                // signatures.\n                module.hot.dispose(function (data) {\n                    data.prevExports = currentExports;\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevExports !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevExports, currentExports)) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevExports !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************\n//# sourceURL=webpack-internal:///./services/apiConfig.js\n"));

/***/ }),

/***/ "./utils/auth/auth.js":
/*!****************************!*\
  !*** ./utils/auth/auth.js ***!
  \****************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getIdTokeSilently: function() { return /* binding */ getIdTokeSilently; },\n/* harmony export */   getToken: function() { return /* binding */ getToken; },\n/* harmony export */   logoutHandler: function() { return /* binding */ logoutHandler; },\n/* harmony export */   setUserCookie: function() { return /* binding */ setUserCookie; },\n/* harmony export */   useAuth: function() { return /* binding */ useAuth; },\n/* harmony export */   useUser: function() { return /* binding */ useUser; }\n/* harmony export */ });\n/* harmony import */ var _authConfig__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./authConfig */ \"./utils/auth/authConfig.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _services_apiConfig__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/services/apiConfig */ \"./services/apiConfig.js\");\n/* harmony import */ var js_cookie__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! js-cookie */ \"./node_modules/js-cookie/dist/js.cookie.mjs\");\n/* harmony import */ var react_toastify__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react-toastify */ \"./node_modules/react-toastify/dist/react-toastify.esm.mjs\");\n/* harmony import */ var react_toastify_dist_ReactToastify_css__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! react-toastify/dist/ReactToastify.css */ \"./node_modules/react-toastify/dist/ReactToastify.css\");\n/* harmony import */ var react_toastify_dist_ReactToastify_css__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(react_toastify_dist_ReactToastify_css__WEBPACK_IMPORTED_MODULE_5__);\n// auth.js\nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\n\n\n\nconst getToken = async ()=>{\n    try {\n        const accounts = msalInstance.getAllAccounts();\n        if (accounts.length === 0) {\n            throw new Error(\"No account found\");\n        }\n        const tokenResponse = await msalInstance.acquireTokenSilent({\n            scopes: _authConfig__WEBPACK_IMPORTED_MODULE_0__.loginRequest.scopes,\n            account: accounts[0]\n        });\n        return tokenResponse.accessToken;\n    } catch (error) {\n        console.error(\"Failed to get token:\", error);\n        throw error;\n    }\n};\nconst logoutHandler = async (instance, redirectUrl)=>{\n    instance.clearCache();\n    localStorage.removeItem(\"superUser\");\n    localStorage.removeItem(\"company\");\n    localStorage.removeItem(\"id\");\n    localStorage.removeItem(\"name\");\n    localStorage.removeItem(\"role\");\n    localStorage.removeItem(\"email\");\n    js_cookie__WEBPACK_IMPORTED_MODULE_3__[\"default\"].remove(\"user\");\n    js_cookie__WEBPACK_IMPORTED_MODULE_3__[\"default\"].remove(\"theme\");\n    js_cookie__WEBPACK_IMPORTED_MODULE_3__[\"default\"].remove(\"token\");\n    js_cookie__WEBPACK_IMPORTED_MODULE_3__[\"default\"].remove(\"company\");\n    js_cookie__WEBPACK_IMPORTED_MODULE_3__[\"default\"].remove(\"ADCompanyName\");\n    instance.logout({\n        postLogoutRedirectUri: redirectUrl || _authConfig__WEBPACK_IMPORTED_MODULE_0__.BASE_URL\n    }).catch((error)=>{\n        console.error(\"Logout error\", error);\n    });\n};\nconst getIdTokeSilently = async (instance, account)=>{\n    if (account) {\n        try {\n            // Provide the scopes you need for your application\n            const request = {\n                account: account,\n                scopes: [\n                    \"User.Read\"\n                ]\n            };\n            const response = await instance.acquireTokenSilent(request);\n            return response.accessToken;\n        } catch (error) {\n            console.error(\"Silent login error\", error);\n            return null;\n        }\n    }\n    return null;\n};\nconst setUserCookie = async (data, instance, account, updateToken)=>{\n    let serverAddress = _services_apiConfig__WEBPACK_IMPORTED_MODULE_2__.apiConfig.serverAddress;\n    fetch(\"\".concat(serverAddress, \"users/getUserByEmail\"), {\n        method: \"POST\",\n        headers: {\n            Authorization: \"Bearer \".concat(data.accessToken),\n            Accept: \"application/json\",\n            \"Content-Type\": \"application/json\"\n        },\n        body: JSON.stringify({\n            email: data.account.username,\n            name: data.account.name\n        })\n    }).then((res)=>{\n        // if(res.status === 400 || res.status === 401){\n        //   setTimeout(function(){\n        //     logoutHandler(instance);\n        //     router.push('/login');\n        //   }, 1000);\n        // }\n        if (res.status === 200) {\n            return res.json();\n        }\n        return Promise.reject(res);\n    }).then((json)=>{\n        let res = json;\n        if (res.length > 0) {\n            const userdetails = {\n                user_id: res[0].user_id,\n                name: data.account.name,\n                email: data.account.username,\n                role: res[0].role_id,\n                token: data.accessToken,\n                role_id: res[0].role_id,\n                department_id: res[0].department_id\n            };\n            localStorage.setItem(\"superUser\", res[0].role_id);\n            js_cookie__WEBPACK_IMPORTED_MODULE_3__[\"default\"].set(\"user\", JSON.stringify(userdetails), {\n                expires: 365\n            });\n            updateToken(userdetails);\n        // //const tokenStatus = updateToken(res, data.accessToken);\n        // if (tokenStatus) {\n        //   // sessionStorage.setItem(\"useremail\", res[0].email);\n        //   // router.push(\"/suppliers\");\n        // } else {\n        //   sessionStorage.clear();\n        //   router.push({ pathname: \"/login\", query: { message: \"text\" } });\n        // }\n        } else {\n            js_cookie__WEBPACK_IMPORTED_MODULE_3__[\"default\"].set(\"user\", JSON.stringify({\n                email: data.account.username,\n                name: data.account.name,\n                token: data.accessToken,\n                role: null\n            }));\n            updateToken({\n                email: data.account.username,\n                name: data.account.name\n            });\n        }\n    }).catch((error)=>{\n        react_toastify__WEBPACK_IMPORTED_MODULE_4__.toast.error(error.statusText, {\n            position: \"top-right\"\n        });\n    });\n    return true;\n};\nconst useAuth = ()=>{\n    _s();\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_1__.useRouter)();\n    const login = async ()=>{\n        try {\n            let msalResponse = await msalInstance.loginPopup(_authConfig__WEBPACK_IMPORTED_MODULE_0__.loginRequest);\n            getAccountDetails(msalResponse);\n        //router.push(\"/suppliers\");\n        } catch (error) {\n            console.error(\"Failed to log in:\", error);\n        }\n    };\n    const updateToken = (resData, token)=>{\n        let res = resData;\n        let serverAddress = _services_apiConfig__WEBPACK_IMPORTED_MODULE_2__.apiConfig.serverAddress;\n        let userData = {\n            id: res[0].user_id,\n            role_id: res[0].role_id,\n            first_name: res[0].first_name,\n            last_name: res[0].last_name,\n            email: res[0].email,\n            mobile_no: res[0].mobile_no,\n            password: res[0].password,\n            status: res[0].status,\n            created_date: res[0].created_date,\n            token: token\n        };\n        fetch(\"\".concat(serverAddress, \"users/update-user/\").concat(res[0].user_id), {\n            method: \"PUT\",\n            headers: {\n                //Authorization: `Bearer ${token}`,\n                Accept: \"application/json\",\n                \"Content-Type\": \"application/json\"\n            },\n            body: JSON.stringify(userData)\n        }).then((res)=>{\n            if (res.status === 200) {\n                return res.json();\n            }\n            return Promise.reject(res);\n        }).then((json)=>{\n            let res = json;\n            if (res.length > 0) {\n                return true;\n            } else {\n                return false;\n            }\n        });\n    };\n    const isLoggedIn = ()=>{\n        return !!msalInstance.getAllAccounts().length;\n    };\n    return {\n        login,\n        logout,\n        isLoggedIn\n    };\n};\n_s(useAuth, \"fN7XvhJ+p5oE6+Xlo0NJmXpxjC8=\", false, function() {\n    return [\n        next_router__WEBPACK_IMPORTED_MODULE_1__.useRouter\n    ];\n});\nconst useUser = ()=>{\n    _s1();\n    const [user, setUser] = useState(null);\n    const { instance } = useMsal();\n    const activeAccount = instance.getActiveAccount();\n    useEffect(()=>{\n        const getUser = async ()=>{\n            try {\n                if (activeAccount) {\n                    const accessToken = await instance.acquireTokenSilent({\n                        scopes: [\n                            \"User.Read\",\n                            \"Directory.Read.All\",\n                            \"User.ReadBasic.All\"\n                        ]\n                    });\n                    const userResponse = await fetch(\"https://graph.microsoft.com/v1.0/me\", {\n                        headers: {\n                            Authorization: \"Bearer \".concat(accessToken.accessToken)\n                        }\n                    });\n                    if (!userResponse.ok) {\n                        throw new Error(\"Request failed with status \".concat(userResponse.status));\n                    }\n                    const userData = await userResponse.json();\n                    setUser(userData);\n                }\n            } catch (error) {\n                console.error(\"Error fetching user data:\", error);\n            }\n        };\n        const intervalId = setInterval(getUser, 1000); // Refresh user data every second\n        return ()=>{\n            clearInterval(intervalId); // Clean up interval on component unmount\n        };\n    }, [\n        activeAccount,\n        instance\n    ]);\n    return user;\n};\n_s1(useUser, \"j5U2A8YVrGIZ6m01RV+TAEjV0cQ=\", true);\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevExports = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevExports) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports on update so we can compare the boundary\n                // signatures.\n                module.hot.dispose(function (data) {\n                    data.prevExports = currentExports;\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevExports !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevExports, currentExports)) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevExports !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./utils/auth/auth.js\n"));

/***/ }),

/***/ "./utils/getCookieData.js":
/*!********************************!*\
  !*** ./utils/getCookieData.js ***!
  \********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getCookieData: function() { return /* binding */ getCookieData; }\n/* harmony export */ });\n/* harmony import */ var js_cookie__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! js-cookie */ \"./node_modules/js-cookie/dist/js.cookie.mjs\");\n\nconst getCookieData = (cookieName)=>{\n    const cookieData = js_cookie__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(cookieName);\n    if (cookieData) {\n        return JSON.parse(cookieData);\n    }\n    return null;\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevExports = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevExports) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports on update so we can compare the boundary\n                // signatures.\n                module.hot.dispose(function (data) {\n                    data.prevExports = currentExports;\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevExports !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevExports, currentExports)) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevExports !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi91dGlscy9nZXRDb29raWVEYXRhLmpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQWdDO0FBRXpCLE1BQU1DLGdCQUFnQixDQUFDQztJQUM1QixNQUFNQyxhQUFhSCxxREFBVyxDQUFDRTtJQUUvQixJQUFJQyxZQUFZO1FBQ2QsT0FBT0UsS0FBS0MsS0FBSyxDQUFDSDtJQUNwQjtJQUVBLE9BQU87QUFDVCxFQUFFIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL3V0aWxzL2dldENvb2tpZURhdGEuanM/YjUwNyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgQ29va2llcyBmcm9tIFwianMtY29va2llXCI7XHJcblxyXG5leHBvcnQgY29uc3QgZ2V0Q29va2llRGF0YSA9IChjb29raWVOYW1lKSA9PiB7XHJcbiAgY29uc3QgY29va2llRGF0YSA9IENvb2tpZXMuZ2V0KGNvb2tpZU5hbWUpO1xyXG5cclxuICBpZiAoY29va2llRGF0YSkge1xyXG4gICAgcmV0dXJuIEpTT04ucGFyc2UoY29va2llRGF0YSk7XHJcbiAgfVxyXG5cclxuICByZXR1cm4gbnVsbDtcclxufTtcclxuIl0sIm5hbWVzIjpbIkNvb2tpZXMiLCJnZXRDb29raWVEYXRhIiwiY29va2llTmFtZSIsImNvb2tpZURhdGEiLCJnZXQiLCJKU09OIiwicGFyc2UiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///./utils/getCookieData.js\n"));

/***/ })

});