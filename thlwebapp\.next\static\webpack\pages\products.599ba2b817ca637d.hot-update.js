"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/products",{

/***/ "./public/images/iss_logo.jpg":
/*!************************************!*\
  !*** ./public/images/iss_logo.jpg ***!
  \************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony default export */ __webpack_exports__[\"default\"] = ({\"src\":\"/_next/static/media/iss_logo.2dbf60f2.jpg\",\"height\":551,\"width\":1021,\"blurDataURL\":\"/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Fiss_logo.2dbf60f2.jpg&w=8&q=70\",\"blurWidth\":8,\"blurHeight\":4});//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9wdWJsaWMvaW1hZ2VzL2lzc19sb2dvLmpwZyIsIm1hcHBpbmdzIjoiO0FBQUEsK0RBQWUsQ0FBQyxxTUFBcU0iLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vcHVibGljL2ltYWdlcy9pc3NfbG9nby5qcGc/YmI5YSJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCB7XCJzcmNcIjpcIi9fbmV4dC9zdGF0aWMvbWVkaWEvaXNzX2xvZ28uMmRiZjYwZjIuanBnXCIsXCJoZWlnaHRcIjo1NTEsXCJ3aWR0aFwiOjEwMjEsXCJibHVyRGF0YVVSTFwiOlwiL19uZXh0L2ltYWdlP3VybD0lMkZfbmV4dCUyRnN0YXRpYyUyRm1lZGlhJTJGaXNzX2xvZ28uMmRiZjYwZjIuanBnJnc9OCZxPTcwXCIsXCJibHVyV2lkdGhcIjo4LFwiYmx1ckhlaWdodFwiOjR9OyJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///./public/images/iss_logo.jpg\n"));

/***/ }),

/***/ "./components/Layout.js":
/*!******************************!*\
  !*** ./components/Layout.js ***!
  \******************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getServerSideProps: function() { return /* binding */ getServerSideProps; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _Navbar__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./Navbar */ \"./components/Navbar.js\");\n/* harmony import */ var _Sidebar__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./Sidebar */ \"./components/Sidebar.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/navigation */ \"./node_modules/next/navigation.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(next_navigation__WEBPACK_IMPORTED_MODULE_5__);\n\nvar _s = $RefreshSig$();\n\n\n\n\n\nconst Layout = (param)=>{\n    let { children, userData, company, blockScreen } = param;\n    _s();\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_4__.useRouter)();\n    const currentPath = router.pathname;\n    const [showDesktopViewMessage, setShowDesktopViewMessage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_5__.usePathname)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const handleResize = ()=>{\n            if (window.innerWidth <= 767) {\n                setShowDesktopViewMessage(true);\n            } else {\n                setShowDesktopViewMessage(false);\n            }\n        };\n        window.addEventListener(\"resize\", handleResize);\n        handleResize();\n        return ()=>window.removeEventListener(\"resize\", handleResize);\n    }, []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        id: \"wrapper\",\n        children: showDesktopViewMessage ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"desktop-view-message\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"message-content\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        children: \"Please Open in Desktop View\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\Layout.js\",\n                        lineNumber: 32,\n                        columnNumber: 13\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        children: \"This website is best viewed on a desktop or laptop.\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\Layout.js\",\n                        lineNumber: 33,\n                        columnNumber: 13\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\Layout.js\",\n                lineNumber: 31,\n                columnNumber: 11\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\Layout.js\",\n            lineNumber: 30,\n            columnNumber: 9\n        }, undefined) : blockScreen ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"block-view-message\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"message-content\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            children: \"Service Unavailable\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\Layout.js\",\n                            lineNumber: 40,\n                            columnNumber: 15\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            children: \"We are currently experiencing issues and are working to resolve them as quickly as possible. Please check back later.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\Layout.js\",\n                            lineNumber: 41,\n                            columnNumber: 15\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\Layout.js\",\n                    lineNumber: 39,\n                    columnNumber: 13\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\Layout.js\",\n                lineNumber: 38,\n                columnNumber: 11\n            }, undefined)\n        }, void 0, false) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Sidebar__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                    userData: userData,\n                    companyName: company\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\Layout.js\",\n                    lineNumber: 50,\n                    columnNumber: 11\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"page-container\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Navbar__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                            userData: userData\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\Layout.js\",\n                            lineNumber: 52,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"main-root\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative md:ml-[55px] lg:ml-[60px] py-2 2xl:h-[calc(100%-60px)] \".concat(pathname == \"/whatif\" || pathname == \"/service_level\" ? \"w-[100%-70px] px-0 pl-3 mt-[45px]\" : \"w-full px-8 mt-[60px]\"),\n                                children: children\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\Layout.js\",\n                                lineNumber: 54,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\Layout.js\",\n                            lineNumber: 53,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\Layout.js\",\n                    lineNumber: 51,\n                    columnNumber: 11\n                }, undefined)\n            ]\n        }, void 0, true)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\Layout.js\",\n        lineNumber: 28,\n        columnNumber: 5\n    }, undefined);\n};\n_s(Layout, \"E7wJCME/M0kEq0xEwa8ayz4R7Ag=\", false, function() {\n    return [\n        next_router__WEBPACK_IMPORTED_MODULE_4__.useRouter,\n        next_navigation__WEBPACK_IMPORTED_MODULE_5__.usePathname\n    ];\n});\n_c = Layout;\n/* harmony default export */ __webpack_exports__[\"default\"] = (Layout);\nconst getServerSideProps = async (context)=>{\n    let userData = {};\n    let company = \"\";\n    try {\n        userData = JSON.parse(context.req.cookies.user || \"{}\");\n        company = JSON.parse(context.req.localStorage.company || \"\");\n    } catch (error) {\n        console.error(\"Error parsing userData:\", error);\n    }\n    return {\n        props: {\n            userData,\n            company\n        }\n    };\n};\nvar _c;\n$RefreshReg$(_c, \"Layout\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevExports = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevExports) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports on update so we can compare the boundary\n                // signatures.\n                module.hot.dispose(function (data) {\n                    data.prevExports = currentExports;\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevExports !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevExports, currentExports)) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevExports !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9jb21wb25lbnRzL0xheW91dC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7O0FBQW1EO0FBQ3JCO0FBQ0U7QUFDUTtBQUNNO0FBRTlDLE1BQU1PLFNBQVM7UUFBQyxFQUFFQyxRQUFRLEVBQUVDLFFBQVEsRUFBRUMsT0FBTyxFQUFFQyxXQUFXLEVBQUU7O0lBQzFELE1BQU1DLFNBQVNQLHNEQUFTQTtJQUN4QixNQUFNUSxjQUFjRCxPQUFPRSxRQUFRO0lBQ25DLE1BQU0sQ0FBQ0Msd0JBQXdCQywwQkFBMEIsR0FBR2QsK0NBQVFBLENBQUM7SUFDckUsTUFBTVksV0FBV1IsNERBQVdBO0lBRTVCTCxnREFBU0EsQ0FBQztRQUNSLE1BQU1nQixlQUFlO1lBQ25CLElBQUlDLE9BQU9DLFVBQVUsSUFBSSxLQUFLO2dCQUM1QkgsMEJBQTBCO1lBQzVCLE9BQU87Z0JBQ0xBLDBCQUEwQjtZQUM1QjtRQUNGO1FBRUFFLE9BQU9FLGdCQUFnQixDQUFDLFVBQVVIO1FBRWxDQTtRQUNBLE9BQU8sSUFBTUMsT0FBT0csbUJBQW1CLENBQUMsVUFBVUo7SUFDcEQsR0FBRyxFQUFFO0lBQ0wscUJBQ0UsOERBQUNLO1FBQUlDLElBQUc7a0JBQ0xSLHVDQUNDLDhEQUFDTztZQUFJRSxXQUFVO3NCQUNiLDRFQUFDRjtnQkFBSUUsV0FBVTs7a0NBQ2IsOERBQUNDO2tDQUFHOzs7Ozs7a0NBQ0osOERBQUNDO2tDQUFFOzs7Ozs7Ozs7Ozs7Ozs7O3dCQUdMZiw0QkFDRjtzQkFDRSw0RUFBQ1c7Z0JBQUlFLFdBQVU7MEJBQ2IsNEVBQUNGO29CQUFJRSxXQUFVOztzQ0FDYiw4REFBQ0M7c0NBQUc7Ozs7OztzQ0FDSiw4REFBQ0M7c0NBQUU7Ozs7Ozs7Ozs7Ozs7Ozs7OzBDQVFUOzs4QkFDRSw4REFBQ3RCLGdEQUFPQTtvQkFBQ0ssVUFBVUE7b0JBQVVrQixhQUFhakI7Ozs7Ozs4QkFDMUMsOERBQUNZO29CQUFJRSxXQUFVOztzQ0FDYiw4REFBQ3JCLCtDQUFNQTs0QkFBQ00sVUFBVUE7Ozs7OztzQ0FDbEIsOERBQUNhOzRCQUFJRSxXQUFVO3NDQUNiLDRFQUFDRjtnQ0FDQ0UsV0FBVyxtRUFJVixPQUhDVixZQUFZLGFBQWFBLFlBQVksbUJBQ2pDLHNDQUNBOzBDQUdMTjs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBUWpCO0dBOURNRDs7UUFDV0Ysa0RBQVNBO1FBR1BDLHdEQUFXQTs7O0tBSnhCQztBQWdFTiwrREFBZUEsTUFBTUEsRUFBQztBQUVmLE1BQU1xQixxQkFBcUIsT0FBT0M7SUFDdkMsSUFBSXBCLFdBQVcsQ0FBQztJQUNoQixJQUFJQyxVQUFVO0lBQ2QsSUFBSTtRQUNGRCxXQUFXcUIsS0FBS0MsS0FBSyxDQUFDRixRQUFRRyxHQUFHLENBQUNDLE9BQU8sQ0FBQ0MsSUFBSSxJQUFJO1FBQ2xEeEIsVUFBVW9CLEtBQUtDLEtBQUssQ0FBQ0YsUUFBUUcsR0FBRyxDQUFDRyxZQUFZLENBQUN6QixPQUFPLElBQUk7SUFDM0QsRUFBRSxPQUFPMEIsT0FBTztRQUNkQyxRQUFRRCxLQUFLLENBQUMsMkJBQTJCQTtJQUMzQztJQUNBLE9BQU87UUFDTEUsT0FBTztZQUNMN0I7WUFDQUM7UUFDRjtJQUNGO0FBQ0YsRUFBRSIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9jb21wb25lbnRzL0xheW91dC5qcz81MTVjIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBSZWFjdCwgeyB1c2VFZmZlY3QsIHVzZVN0YXRlIH0gZnJvbSBcInJlYWN0XCI7XHJcbmltcG9ydCBOYXZiYXIgZnJvbSBcIi4vTmF2YmFyXCI7XHJcbmltcG9ydCBTaWRlYmFyIGZyb20gXCIuL1NpZGViYXJcIjtcclxuaW1wb3J0IHsgdXNlUm91dGVyIH0gZnJvbSBcIm5leHQvcm91dGVyXCI7XHJcbmltcG9ydCB7IHVzZVBhdGhuYW1lIH0gZnJvbSBcIm5leHQvbmF2aWdhdGlvblwiO1xyXG5cclxuY29uc3QgTGF5b3V0ID0gKHsgY2hpbGRyZW4sIHVzZXJEYXRhLCBjb21wYW55LCBibG9ja1NjcmVlbiB9KSA9PiB7XHJcbiAgY29uc3Qgcm91dGVyID0gdXNlUm91dGVyKCk7XHJcbiAgY29uc3QgY3VycmVudFBhdGggPSByb3V0ZXIucGF0aG5hbWU7XHJcbiAgY29uc3QgW3Nob3dEZXNrdG9wVmlld01lc3NhZ2UsIHNldFNob3dEZXNrdG9wVmlld01lc3NhZ2VdID0gdXNlU3RhdGUoZmFsc2UpO1xyXG4gIGNvbnN0IHBhdGhuYW1lID0gdXNlUGF0aG5hbWUoKTtcclxuXHJcbiAgdXNlRWZmZWN0KCgpID0+IHtcclxuICAgIGNvbnN0IGhhbmRsZVJlc2l6ZSA9ICgpID0+IHtcclxuICAgICAgaWYgKHdpbmRvdy5pbm5lcldpZHRoIDw9IDc2Nykge1xyXG4gICAgICAgIHNldFNob3dEZXNrdG9wVmlld01lc3NhZ2UodHJ1ZSk7XHJcbiAgICAgIH0gZWxzZSB7XHJcbiAgICAgICAgc2V0U2hvd0Rlc2t0b3BWaWV3TWVzc2FnZShmYWxzZSk7XHJcbiAgICAgIH1cclxuICAgIH07XHJcblxyXG4gICAgd2luZG93LmFkZEV2ZW50TGlzdGVuZXIoXCJyZXNpemVcIiwgaGFuZGxlUmVzaXplKTtcclxuXHJcbiAgICBoYW5kbGVSZXNpemUoKTtcclxuICAgIHJldHVybiAoKSA9PiB3aW5kb3cucmVtb3ZlRXZlbnRMaXN0ZW5lcihcInJlc2l6ZVwiLCBoYW5kbGVSZXNpemUpO1xyXG4gIH0sIFtdKTtcclxuICByZXR1cm4gKFxyXG4gICAgPGRpdiBpZD1cIndyYXBwZXJcIj5cclxuICAgICAge3Nob3dEZXNrdG9wVmlld01lc3NhZ2UgPyAoXHJcbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJkZXNrdG9wLXZpZXctbWVzc2FnZVwiPlxyXG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJtZXNzYWdlLWNvbnRlbnRcIj5cclxuICAgICAgICAgICAgPGgyPlBsZWFzZSBPcGVuIGluIERlc2t0b3AgVmlldzwvaDI+XHJcbiAgICAgICAgICAgIDxwPlRoaXMgd2Vic2l0ZSBpcyBiZXN0IHZpZXdlZCBvbiBhIGRlc2t0b3Agb3IgbGFwdG9wLjwvcD5cclxuICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgIDwvZGl2PlxyXG4gICAgICApIDogYmxvY2tTY3JlZW4gPyAoXHJcbiAgICAgICAgPD5cclxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYmxvY2stdmlldy1tZXNzYWdlXCI+XHJcbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibWVzc2FnZS1jb250ZW50XCI+XHJcbiAgICAgICAgICAgICAgPGgyPlNlcnZpY2UgVW5hdmFpbGFibGU8L2gyPlxyXG4gICAgICAgICAgICAgIDxwPlxyXG4gICAgICAgICAgICAgICAgV2UgYXJlIGN1cnJlbnRseSBleHBlcmllbmNpbmcgaXNzdWVzIGFuZCBhcmUgd29ya2luZyB0byByZXNvbHZlXHJcbiAgICAgICAgICAgICAgICB0aGVtIGFzIHF1aWNrbHkgYXMgcG9zc2libGUuIFBsZWFzZSBjaGVjayBiYWNrIGxhdGVyLlxyXG4gICAgICAgICAgICAgIDwvcD5cclxuICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICA8Lz5cclxuICAgICAgKSA6IChcclxuICAgICAgICA8PlxyXG4gICAgICAgICAgPFNpZGViYXIgdXNlckRhdGE9e3VzZXJEYXRhfSBjb21wYW55TmFtZT17Y29tcGFueX0gLz5cclxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwicGFnZS1jb250YWluZXJcIj5cclxuICAgICAgICAgICAgPE5hdmJhciB1c2VyRGF0YT17dXNlckRhdGF9IC8+XHJcbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibWFpbi1yb290XCI+XHJcbiAgICAgICAgICAgICAgPGRpdlxyXG4gICAgICAgICAgICAgICAgY2xhc3NOYW1lPXtgcmVsYXRpdmUgbWQ6bWwtWzU1cHhdIGxnOm1sLVs2MHB4XSBweS0yIDJ4bDpoLVtjYWxjKDEwMCUtNjBweCldICR7XHJcbiAgICAgICAgICAgICAgICAgIHBhdGhuYW1lID09IFwiL3doYXRpZlwiIHx8IHBhdGhuYW1lID09IFwiL3NlcnZpY2VfbGV2ZWxcIlxyXG4gICAgICAgICAgICAgICAgICAgID8gXCJ3LVsxMDAlLTcwcHhdIHB4LTAgcGwtMyBtdC1bNDVweF1cIlxyXG4gICAgICAgICAgICAgICAgICAgIDogXCJ3LWZ1bGwgcHgtOCBtdC1bNjBweF1cIlxyXG4gICAgICAgICAgICAgICAgfWB9XHJcbiAgICAgICAgICAgICAgPlxyXG4gICAgICAgICAgICAgICAge2NoaWxkcmVufVxyXG4gICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgIDwvPlxyXG4gICAgICApfVxyXG4gICAgPC9kaXY+XHJcbiAgKTtcclxufTtcclxuXHJcbmV4cG9ydCBkZWZhdWx0IExheW91dDtcclxuXHJcbmV4cG9ydCBjb25zdCBnZXRTZXJ2ZXJTaWRlUHJvcHMgPSBhc3luYyAoY29udGV4dCkgPT4ge1xyXG4gIGxldCB1c2VyRGF0YSA9IHt9O1xyXG4gIGxldCBjb21wYW55ID0gXCJcIjtcclxuICB0cnkge1xyXG4gICAgdXNlckRhdGEgPSBKU09OLnBhcnNlKGNvbnRleHQucmVxLmNvb2tpZXMudXNlciB8fCBcInt9XCIpO1xyXG4gICAgY29tcGFueSA9IEpTT04ucGFyc2UoY29udGV4dC5yZXEubG9jYWxTdG9yYWdlLmNvbXBhbnkgfHwgXCJcIik7XHJcbiAgfSBjYXRjaCAoZXJyb3IpIHtcclxuICAgIGNvbnNvbGUuZXJyb3IoXCJFcnJvciBwYXJzaW5nIHVzZXJEYXRhOlwiLCBlcnJvcik7XHJcbiAgfVxyXG4gIHJldHVybiB7XHJcbiAgICBwcm9wczoge1xyXG4gICAgICB1c2VyRGF0YSxcclxuICAgICAgY29tcGFueSxcclxuICAgIH0sXHJcbiAgfTtcclxufTtcclxuIl0sIm5hbWVzIjpbIlJlYWN0IiwidXNlRWZmZWN0IiwidXNlU3RhdGUiLCJOYXZiYXIiLCJTaWRlYmFyIiwidXNlUm91dGVyIiwidXNlUGF0aG5hbWUiLCJMYXlvdXQiLCJjaGlsZHJlbiIsInVzZXJEYXRhIiwiY29tcGFueSIsImJsb2NrU2NyZWVuIiwicm91dGVyIiwiY3VycmVudFBhdGgiLCJwYXRobmFtZSIsInNob3dEZXNrdG9wVmlld01lc3NhZ2UiLCJzZXRTaG93RGVza3RvcFZpZXdNZXNzYWdlIiwiaGFuZGxlUmVzaXplIiwid2luZG93IiwiaW5uZXJXaWR0aCIsImFkZEV2ZW50TGlzdGVuZXIiLCJyZW1vdmVFdmVudExpc3RlbmVyIiwiZGl2IiwiaWQiLCJjbGFzc05hbWUiLCJoMiIsInAiLCJjb21wYW55TmFtZSIsImdldFNlcnZlclNpZGVQcm9wcyIsImNvbnRleHQiLCJKU09OIiwicGFyc2UiLCJyZXEiLCJjb29raWVzIiwidXNlciIsImxvY2FsU3RvcmFnZSIsImVycm9yIiwiY29uc29sZSIsInByb3BzIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///./components/Layout.js\n"));

/***/ }),

/***/ "./components/Navbar.js":
/*!******************************!*\
  !*** ./components/Navbar.js ***!
  \******************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   themes: function() { return /* binding */ themes; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _fortawesome_react_fontawesome__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @fortawesome/react-fontawesome */ \"./node_modules/@fortawesome/react-fontawesome/index.es.js\");\n/* harmony import */ var _fortawesome_free_solid_svg_icons__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @fortawesome/free-solid-svg-icons */ \"./node_modules/@fortawesome/free-solid-svg-icons/index.mjs\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/link */ \"./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _azure_msal_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @azure/msal-react */ \"./node_modules/@azure/msal-react/dist/index.js\");\n/* harmony import */ var _utils_theme_theme_switcher__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/utils/theme/theme-switcher */ \"./utils/theme/theme-switcher.js\");\n/* harmony import */ var js_cookie__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! js-cookie */ \"./node_modules/js-cookie/dist/js.cookie.mjs\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\nconst themes = [\n    {\n        bg: \"#022d71\",\n        text: \"#022d71\",\n        name: \"DPS\"\n    },\n    {\n        bg: \"#2e9b28\",\n        text: \"#2e9b28\",\n        name: \"EFC\"\n    },\n    {\n        bg: \"#a91e23\",\n        text: \"#a91e23\",\n        name: \"M&S\"\n    },\n    {\n        bg: \"#3d6546\",\n        text: \"#3d6546\",\n        name: \"FPP\"\n    }\n];\nconst Navbar = (param)=>{\n    let { userData } = param;\n    _s();\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_4__.useRouter)();\n    const [pageName, setPageName] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [currentRoute, setCurrentRoute] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const currentPath = router.pathname;\n        setCurrentRoute(currentPath);\n        if (currentPath === \"/finished-product-request/add\") {\n            js_cookie__WEBPACK_IMPORTED_MODULE_7__[\"default\"].set(\"PreviousPage\", \"FG\");\n            setPageName(\"Finished Goods Request\");\n        } else if (currentPath === \"/raw-material-request/add\") {\n            setPageName(\"Raw Material Request\");\n            js_cookie__WEBPACK_IMPORTED_MODULE_7__[\"default\"].set(\"PreviousPage\", \"RM\");\n        } else if (currentPath === \"/packaging-form/add\") {\n            setPageName(\"Packaging Request\");\n        } else if (currentPath === \"/variety/add\") {\n            js_cookie__WEBPACK_IMPORTED_MODULE_7__[\"default\"].set(\"PreviousPage\", \"NV\");\n            setPageName(\"New Variety Request\");\n        } else if (currentPath === \"/variety/[productId]/edit\") {\n            js_cookie__WEBPACK_IMPORTED_MODULE_7__[\"default\"].set(\"PreviousPage\", \"NV\");\n            setPageName(\"Edit New Variety Request\");\n        } else if (currentPath === \"/packaging-form/[productId]/edit\") {\n            js_cookie__WEBPACK_IMPORTED_MODULE_7__[\"default\"].set(\"PreviousPage\", \"PK\");\n            setPageName(\"Edit Packaging Request\");\n        } else if (currentPath === \"/raw-material-request/[productId]/edit\") {\n            js_cookie__WEBPACK_IMPORTED_MODULE_7__[\"default\"].set(\"PreviousPage\", \"RM\");\n            setPageName(\"Edit Raw Material Request\");\n        } else if (currentPath === \"/finished-product-request/[productId]/edit\") {\n            js_cookie__WEBPACK_IMPORTED_MODULE_7__[\"default\"].set(\"PreviousPage\", \"FG\");\n            setPageName(\"Edit Finished Goods Request\");\n        } else if (currentPath === null || currentPath === void 0 ? void 0 : currentPath.endsWith(\"/edit\")) {\n            setPageName(\"Edit Supplier\");\n        } else if (currentPath === null || currentPath === void 0 ? void 0 : currentPath.endsWith(\"/add\")) {\n            setPageName(\"Add Supplier\");\n        } else if (currentPath === null || currentPath === void 0 ? void 0 : currentPath.endsWith(\"/edit/forms\")) {\n            setPageName(\"Supplier Form\");\n        } else if (currentPath === null || currentPath === void 0 ? void 0 : currentPath.endsWith(\"/confirm\")) {\n            setPageName(\"Confirm Details for Supplier\");\n        } else if (currentPath === \"/suppliers\") {\n            setPageName(\"Suppliers\");\n        } else if (currentPath === \"/users\") {\n            setPageName(\"User Management\");\n        } else if (currentPath === \"/viewlogs\") {\n            setPageName(\"View Logs\");\n        } else if (currentPath === \"/products\") {\n            js_cookie__WEBPACK_IMPORTED_MODULE_7__[\"default\"].set(\"PreviousPage\", \"PK\");\n            setPageName(\"Products\");\n        } else if (currentPath === \"/variety\") {\n            setPageName(\"Variety\");\n            js_cookie__WEBPACK_IMPORTED_MODULE_7__[\"default\"].set(\"PreviousPage\", \"NV\");\n        } else if (currentPath === \"/finishedProductRequest\") {\n            js_cookie__WEBPACK_IMPORTED_MODULE_7__[\"default\"].set(\"PreviousPage\", \"FG\");\n            setPageName(\"Finished Product Request\");\n        } else if (currentPath === \"/rawMaterialRequest\") {\n            js_cookie__WEBPACK_IMPORTED_MODULE_7__[\"default\"].set(\"PreviousPage\", \"RM\");\n            setPageName(\"Raw Material Request\");\n        } else if (currentPath === \"/whatif\") {\n            setPageName(\"Whatif\");\n        } else if (currentPath === \"/service_level\" || currentPath === \"/service_level/reports/masterForcast\") {\n            setPageName(\"Service Level\");\n        }\n    }, [\n        router.pathname\n    ]);\n    const baseCompanyOptions = [\n        {\n            value: \"dpsltd\",\n            label: \"DPS\"\n        },\n        {\n            value: \"dpsltdms\",\n            label: \"DPS M&S\"\n        },\n        {\n            value: \"efcltd\",\n            label: \"EFC\"\n        },\n        {\n            value: \"fpp-ltd\",\n            label: \"FPP\"\n        }\n    ];\n    const companyOptions = [\n        ...baseCompanyOptions,\n        ...(userData === null || userData === void 0 ? void 0 : userData.role_id) === 6 ? [\n            {\n                value: \"issproduce\",\n                label: \"ISS\"\n            },\n            {\n                value: \"flrs\",\n                label: \"FLRS\",\n                disabled: true\n            },\n            {\n                value: \"thl\",\n                label: \"THL\",\n                disabled: true\n            }\n        ] : []\n    ];\n    const [selectedCompany, setSelectedCompany] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(js_cookie__WEBPACK_IMPORTED_MODULE_7__[\"default\"].get(\"ADCompanyName\") == \"DPS MS\" ? \"dpsltdms\" : js_cookie__WEBPACK_IMPORTED_MODULE_7__[\"default\"].get(\"company\") || \"\");\n    const handleCompanyChange = (event)=>{\n        console.log(\"e.target.value\", event.target.value);\n        const company = event.target.value;\n        if (company == \"dpsltd\") {\n            js_cookie__WEBPACK_IMPORTED_MODULE_7__[\"default\"].set(\"theme\", \"#022D71\");\n            js_cookie__WEBPACK_IMPORTED_MODULE_7__[\"default\"].set(\"ADCompanyName\", \"DPS\");\n            setSelectedCompany(company);\n            js_cookie__WEBPACK_IMPORTED_MODULE_7__[\"default\"].set(\"company\", company);\n        } else if (company == \"dpsltdms\") {\n            js_cookie__WEBPACK_IMPORTED_MODULE_7__[\"default\"].set(\"theme\", \"#0d6bfc\");\n            js_cookie__WEBPACK_IMPORTED_MODULE_7__[\"default\"].set(\"ADCompanyName\", \"DPS MS\");\n            setSelectedCompany(\"dpsltd\");\n            js_cookie__WEBPACK_IMPORTED_MODULE_7__[\"default\"].set(\"company\", \"dpsltd\");\n        } else if (company == \"efcltd\") {\n            js_cookie__WEBPACK_IMPORTED_MODULE_7__[\"default\"].set(\"theme\", \"#3eab58\");\n            js_cookie__WEBPACK_IMPORTED_MODULE_7__[\"default\"].set(\"ADCompanyName\", \"EFC\");\n            setSelectedCompany(company);\n            js_cookie__WEBPACK_IMPORTED_MODULE_7__[\"default\"].set(\"company\", company);\n        } else if (company == \"fpp-ltd\") {\n            js_cookie__WEBPACK_IMPORTED_MODULE_7__[\"default\"].set(\"theme\", \"#3d6546\");\n            js_cookie__WEBPACK_IMPORTED_MODULE_7__[\"default\"].set(\"ADCompanyName\", \"Fresh Produce Partners\");\n            setSelectedCompany(company);\n            js_cookie__WEBPACK_IMPORTED_MODULE_7__[\"default\"].set(\"company\", company);\n        } else if (company == \"issproduce\") {\n            js_cookie__WEBPACK_IMPORTED_MODULE_7__[\"default\"].set(\"theme\", \"#ABC400\");\n            js_cookie__WEBPACK_IMPORTED_MODULE_7__[\"default\"].set(\"ADCompanyName\", \"Integrated Service Solutions Ltd\");\n            setSelectedCompany(company);\n            js_cookie__WEBPACK_IMPORTED_MODULE_7__[\"default\"].set(\"company\", company);\n        }\n        router.reload();\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"titlebar\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex flex-row justify-between w-full bg-skin-primary h-14\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-row w-full items-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"page-heading cursor-pointer\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fortawesome_react_fontawesome__WEBPACK_IMPORTED_MODULE_2__.FontAwesomeIcon, {\n                                        icon: _fortawesome_free_solid_svg_icons__WEBPACK_IMPORTED_MODULE_8__.faChevronLeft,\n                                        className: \"pageName text-white\",\n                                        onClick: ()=>router.back()\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\Navbar.js\",\n                                        lineNumber: 146,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                        href: currentRoute,\n                                        className: \"ml-4 2xl:text-lg font-poppinssemibold pageName text-white tracking-wide\",\n                                        children: pageName\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\Navbar.js\",\n                                        lineNumber: 151,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\Navbar.js\",\n                                lineNumber: 145,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_utils_theme_theme_switcher__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                className: \"ml-5\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\Navbar.js\",\n                                lineNumber: 158,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\Navbar.js\",\n                        lineNumber: 144,\n                        columnNumber: 11\n                    }, undefined),\n                    ((userData === null || userData === void 0 ? void 0 : userData.role_id) == 5 || (userData === null || userData === void 0 ? void 0 : userData.role_id) == 6) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-row justify-end w-1/2 items-center mr-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                            value: selectedCompany,\n                            onChange: handleCompanyChange,\n                            className: \"bg-white text-black rounded\",\n                            children: companyOptions.map((opt)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                    value: opt.value,\n                                    disabled: opt.disabled,\n                                    children: opt.label\n                                }, opt.value, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\Navbar.js\",\n                                    lineNumber: 168,\n                                    columnNumber: 19\n                                }, undefined))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\Navbar.js\",\n                            lineNumber: 162,\n                            columnNumber: 15\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\Navbar.js\",\n                        lineNumber: 161,\n                        columnNumber: 13\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\Navbar.js\",\n                lineNumber: 143,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\Navbar.js\",\n            lineNumber: 142,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\Navbar.js\",\n        lineNumber: 141,\n        columnNumber: 5\n    }, undefined);\n};\n_s(Navbar, \"8bgZbm+HleyJfidZ9YLsifOPi/U=\", false, function() {\n    return [\n        next_router__WEBPACK_IMPORTED_MODULE_4__.useRouter\n    ];\n});\n_c = Navbar;\n/* harmony default export */ __webpack_exports__[\"default\"] = (Navbar);\nvar _c;\n$RefreshReg$(_c, \"Navbar\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevExports = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevExports) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports on update so we can compare the boundary\n                // signatures.\n                module.hot.dispose(function (data) {\n                    data.prevExports = currentExports;\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevExports !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevExports, currentExports)) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevExports !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/Navbar.js\n"));

/***/ }),

/***/ "./components/SideBarLinks.js":
/*!************************************!*\
  !*** ./components/SideBarLinks.js ***!
  \************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ SideBarLinks; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _fortawesome_free_solid_svg_icons__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @fortawesome/free-solid-svg-icons */ \"./node_modules/@fortawesome/free-solid-svg-icons/index.mjs\");\n/* harmony import */ var _fortawesome_react_fontawesome__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @fortawesome/react-fontawesome */ \"./node_modules/@fortawesome/react-fontawesome/index.es.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _utils_loaders_loadingContext__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/utils/loaders/loadingContext */ \"./utils/loaders/loadingContext.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\nfunction SideBarLinks(param) {\n    let { isSuppliersActive, userData, company, isUsersActive, isLogsActive, isWhatifActive, isServiceLevelActive, isProductsActive, ADCompany, currentPathname } = param;\n    var _process_env_NEXT_PUBLIC_AVAILABLE_MODULES;\n    _s();\n    const availableModules = (_process_env_NEXT_PUBLIC_AVAILABLE_MODULES = \"supplier,products,whatif,serviceLevel,users,logs\") === null || _process_env_NEXT_PUBLIC_AVAILABLE_MODULES === void 0 ? void 0 : _process_env_NEXT_PUBLIC_AVAILABLE_MODULES.split(\",\");\n    const { setIsLoading } = (0,_utils_loaders_loadingContext__WEBPACK_IMPORTED_MODULE_4__.useLoading)();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n        className: \"nav navbar-nav\",\n        children: availableModules && availableModules.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n            children: [\n                availableModules.includes(\"supplier\") && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                    className: \"flex justify-center mb-3\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                        href: \"/suppliers\",\n                        title: \"Supplier\",\n                        className: \"!px-4 !py-3 bg-white rounded-md text-center \".concat(isSuppliersActive ? \"opacity-100\" : \"opacity-70\"),\n                        onClick: (e)=>{\n                            if (isSuppliersActive && (currentPathname === null || currentPathname === void 0 ? void 0 : currentPathname.startsWith(\"/suppliers\"))) {\n                                e.preventDefault();\n                                window.location.reload();\n                            } else {\n                                setIsLoading(true);\n                            }\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fortawesome_react_fontawesome__WEBPACK_IMPORTED_MODULE_1__.FontAwesomeIcon, {\n                            icon: _fortawesome_free_solid_svg_icons__WEBPACK_IMPORTED_MODULE_5__.faTruck,\n                            size: \"lg\",\n                            className: \"text-skin-primary\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\SideBarLinks.js\",\n                            lineNumber: 63,\n                            columnNumber: 17\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\SideBarLinks.js\",\n                        lineNumber: 45,\n                        columnNumber: 15\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\SideBarLinks.js\",\n                    lineNumber: 44,\n                    columnNumber: 13\n                }, this),\n                availableModules.includes(\"products\") && !(userData === null || userData === void 0 ? void 0 : userData.department_id) == 5 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                    className: \"flex justify-center mb-3\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                        href: \"/products\",\n                        title: \"Products\",\n                        className: \"!px-4 !py-3 bg-white rounded-md text-center \".concat(isProductsActive ? \"opacity-100\" : \"opacity-70\"),\n                        onClick: (e)=>{\n                            if (isProductsActive) {\n                                e.preventDefault();\n                                window.location.reload();\n                            } else {\n                                setIsLoading(true);\n                            }\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fortawesome_react_fontawesome__WEBPACK_IMPORTED_MODULE_1__.FontAwesomeIcon, {\n                            icon: _fortawesome_free_solid_svg_icons__WEBPACK_IMPORTED_MODULE_5__.faBoxArchive,\n                            size: \"lg\",\n                            className: \"text-skin-primary\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\SideBarLinks.js\",\n                            lineNumber: 88,\n                            columnNumber: 17\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\SideBarLinks.js\",\n                        lineNumber: 73,\n                        columnNumber: 15\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\SideBarLinks.js\",\n                    lineNumber: 72,\n                    columnNumber: 13\n                }, this),\n                (company == \"efcltd\" || company == \"flrs\" || company == \"thl\") && ((userData === null || userData === void 0 ? void 0 : userData.department_id) == 1 || (userData === null || userData === void 0 ? void 0 : userData.department_id) == 2) && availableModules.includes(\"whatif\") && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                    className: \"flex justify-center mb-3\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                        href: \"/whatif\",\n                        title: \"What if\",\n                        className: \"!px-4 !py-3 bg-white rounded-md text-center \".concat(isWhatifActive ? \"opacity-100\" : \"opacity-70\"),\n                        onClick: (e)=>{\n                            if (isWhatifActive) {\n                                e.preventDefault();\n                                window.location.reload();\n                            } else {\n                                setIsLoading(true);\n                            }\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fortawesome_react_fontawesome__WEBPACK_IMPORTED_MODULE_1__.FontAwesomeIcon, {\n                            icon: _fortawesome_free_solid_svg_icons__WEBPACK_IMPORTED_MODULE_5__.faChartLine,\n                            size: \"lg\",\n                            className: \"text-skin-primary\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\SideBarLinks.js\",\n                            lineNumber: 115,\n                            columnNumber: 19\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\SideBarLinks.js\",\n                        lineNumber: 100,\n                        columnNumber: 17\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\SideBarLinks.js\",\n                    lineNumber: 99,\n                    columnNumber: 15\n                }, this),\n                ((userData === null || userData === void 0 ? void 0 : userData.department_id) == 1 || (userData === null || userData === void 0 ? void 0 : userData.department_id) == 2) && availableModules.includes(\"serviceLevel\") && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                    className: \"flex justify-center mb-3\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                        href: \"/service_level\",\n                        title: \"Service Level\",\n                        className: \"!px-4 !py-3 bg-white rounded-md text-center \".concat(isServiceLevelActive ? \"opacity-100\" : \"opacity-70\"),\n                        onClick: (e)=>{\n                            if (isServiceLevelActive) {\n                                e.preventDefault();\n                                window.location.reload();\n                            } else {\n                                setIsLoading(true);\n                            }\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fortawesome_react_fontawesome__WEBPACK_IMPORTED_MODULE_1__.FontAwesomeIcon, {\n                            icon: _fortawesome_free_solid_svg_icons__WEBPACK_IMPORTED_MODULE_5__.faFileCircleQuestion,\n                            size: \"lg\",\n                            className: \"text-skin-primary\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\SideBarLinks.js\",\n                            lineNumber: 142,\n                            columnNumber: 19\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\SideBarLinks.js\",\n                        lineNumber: 127,\n                        columnNumber: 17\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\SideBarLinks.js\",\n                    lineNumber: 126,\n                    columnNumber: 15\n                }, this),\n                ((userData === null || userData === void 0 ? void 0 : userData.role_id) === 1 || (userData === null || userData === void 0 ? void 0 : userData.role_id) === 5 || (userData === null || userData === void 0 ? void 0 : userData.role_id) === 6) && availableModules.includes(\"users\") && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                    className: \"flex justify-center mb-3\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                        href: \"/users\",\n                        title: \"Users\",\n                        className: \"!px-4 !py-3 bg-white rounded-md text-center \".concat(isUsersActive ? \"opacity-100\" : \"opacity-70\"),\n                        onClick: (e)=>{\n                            if (isUsersActive) {\n                                e.preventDefault();\n                                window.location.reload();\n                            } else {\n                                setIsLoading(true);\n                            }\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fortawesome_react_fontawesome__WEBPACK_IMPORTED_MODULE_1__.FontAwesomeIcon, {\n                            icon: _fortawesome_free_solid_svg_icons__WEBPACK_IMPORTED_MODULE_5__.faUsersLine,\n                            size: \"lg\",\n                            className: \"text-skin-primary\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\SideBarLinks.js\",\n                            lineNumber: 172,\n                            columnNumber: 19\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\SideBarLinks.js\",\n                        lineNumber: 156,\n                        columnNumber: 17\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\SideBarLinks.js\",\n                    lineNumber: 155,\n                    columnNumber: 15\n                }, this),\n                ((userData === null || userData === void 0 ? void 0 : userData.role_id) === 1 || (userData === null || userData === void 0 ? void 0 : userData.role_id) === 5 || (userData === null || userData === void 0 ? void 0 : userData.role_id) === 6) && availableModules.includes(\"logs\") && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                    className: \"flex justify-center mb-3\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                        href: \"/viewlogs\",\n                        title: \"Logs\",\n                        className: \"!px-4 !py-3 bg-white rounded-md text-center \".concat(isLogsActive ? \"opacity-100\" : \"opacity-70\"),\n                        onClick: (e)=>{\n                            if (isLogsActive) {\n                                e.preventDefault();\n                                window.location.reload();\n                            } else {\n                                setIsLoading(true);\n                            }\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fortawesome_react_fontawesome__WEBPACK_IMPORTED_MODULE_1__.FontAwesomeIcon, {\n                            icon: _fortawesome_free_solid_svg_icons__WEBPACK_IMPORTED_MODULE_5__.faList,\n                            size: \"lg\",\n                            className: \"text-skin-primary\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\SideBarLinks.js\",\n                            lineNumber: 201,\n                            columnNumber: 19\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\SideBarLinks.js\",\n                        lineNumber: 185,\n                        columnNumber: 17\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\SideBarLinks.js\",\n                    lineNumber: 184,\n                    columnNumber: 15\n                }, this)\n            ]\n        }, void 0, true)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\SideBarLinks.js\",\n        lineNumber: 40,\n        columnNumber: 5\n    }, this);\n}\n_s(SideBarLinks, \"HE5iXj3OUhzAOBpDJUVF6nKZb8k=\", false, function() {\n    return [\n        _utils_loaders_loadingContext__WEBPACK_IMPORTED_MODULE_4__.useLoading\n    ];\n});\n_c = SideBarLinks;\nvar _c;\n$RefreshReg$(_c, \"SideBarLinks\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevExports = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevExports) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports on update so we can compare the boundary\n                // signatures.\n                module.hot.dispose(function (data) {\n                    data.prevExports = currentExports;\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevExports !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevExports, currentExports)) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevExports !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/SideBarLinks.js\n"));

/***/ }),

/***/ "./components/Sidebar.js":
/*!*******************************!*\
  !*** ./components/Sidebar.js ***!
  \*******************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ Sidebar; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _public_images_dps_logo_png__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../public/images/dps-logo.png */ \"./public/images/dps-logo.png\");\n/* harmony import */ var _public_images_efc_logo_jpg__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../public/images/efc_logo.jpg */ \"./public/images/efc_logo.jpg\");\n/* harmony import */ var _public_images_dps_ms_logo_png__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../public/images/dps_ms_logo.png */ \"./public/images/dps_ms_logo.png\");\n/* harmony import */ var _public_images_fpp_logo_png__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../public/images/fpp_logo.png */ \"./public/images/fpp_logo.png\");\n/* harmony import */ var _public_images_iss_logo_jpg__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../public/images/iss_logo.jpg */ \"./public/images/iss_logo.jpg\");\n/* harmony import */ var _public_images_nav_icon_png__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ../public/images/nav-icon.png */ \"./public/images/nav-icon.png\");\n/* harmony import */ var _public_images_user_png__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ../public/images/user.png */ \"./public/images/user.png\");\n/* harmony import */ var _public_images_loading_img_png__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ../public/images/loading_img.png */ \"./public/images/loading_img.png\");\n/* harmony import */ var _public_images_logout_icon_png__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ../public/images/logout-icon.png */ \"./public/images/logout-icon.png\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! next/link */ \"./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_12___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_12__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! next/image */ \"./node_modules/next/image.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_13___default = /*#__PURE__*/__webpack_require__.n(next_image__WEBPACK_IMPORTED_MODULE_13__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! next/navigation */ \"./node_modules/next/navigation.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_14___default = /*#__PURE__*/__webpack_require__.n(next_navigation__WEBPACK_IMPORTED_MODULE_14__);\n/* harmony import */ var _utils_auth_auth__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! ../utils/auth/auth */ \"./utils/auth/auth.js\");\n/* harmony import */ var _azure_msal_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @azure/msal-react */ \"./node_modules/@azure/msal-react/dist/index.js\");\n/* harmony import */ var js_cookie__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! js-cookie */ \"./node_modules/js-cookie/dist/js.cookie.mjs\");\n/* harmony import */ var react_loader_spinner__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! react-loader-spinner */ \"./node_modules/react-loader-spinner/dist/module.js\");\n/* harmony import */ var _SideBarLinks__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! ./SideBarLinks */ \"./components/SideBarLinks.js\");\n/* provided dependency */ var process = __webpack_require__(/*! process */ \"./node_modules/next/dist/build/polyfills/process.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction Sidebar() {\n    _s();\n    const [userData, setUserData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [company, setCompany] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [ADCompany, setADCompany] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const userCookie = js_cookie__WEBPACK_IMPORTED_MODULE_17__[\"default\"].get(\"user\");\n        const companyCookie = js_cookie__WEBPACK_IMPORTED_MODULE_17__[\"default\"].get(\"company\");\n        const ADcompanyCookie = js_cookie__WEBPACK_IMPORTED_MODULE_17__[\"default\"].get(\"ADCompanyName\");\n        if (userCookie) {\n            setUserData(JSON.parse(userCookie));\n        }\n        if (companyCookie) {\n            setCompany(companyCookie);\n        }\n        if (ADcompanyCookie) {\n            setADCompany(ADcompanyCookie);\n        }\n    }, []);\n    const { instance } = (0,_azure_msal_react__WEBPACK_IMPORTED_MODULE_16__.useMsal)();\n    // const userRoleData = getCookieData(\"user\");\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const currentPathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_14__.usePathname)();\n    const [isSuppliersActive, setIsSuppliersActive] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)((currentPathname === null || currentPathname === void 0 ? void 0 : currentPathname.startsWith(\"/suppliers\")) || (currentPathname === null || currentPathname === void 0 ? void 0 : currentPathname.startsWith(\"/supplier\")));\n    const [isUsersActive, setIsUsersActive] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(currentPathname === \"/users\");\n    const [isLogsActive, setIsLogsActive] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(currentPathname === \"/viewlogs\");\n    const [isProductsActive, setIsProductsActive] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(currentPathname === \"/products\");\n    const [isWhatifActive, setIsWhatifActive] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(currentPathname === \"/whatif\");\n    const [isServiceLevelActive, setIsServiceLevelActive] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(currentPathname === \"/service_level\");\n    const [isRawMaterialRequestActive, setIsRawMaterialRequestActive] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(currentPathname === \"/raw-material-request/add\");\n    const getLogo = (company)=>{\n        if (!company) return;\n        switch(company){\n            case \"dpsltd\":\n                return _public_images_dps_logo_png__WEBPACK_IMPORTED_MODULE_3__[\"default\"];\n            case \"DPS MS\":\n                return _public_images_dps_ms_logo_png__WEBPACK_IMPORTED_MODULE_5__[\"default\"];\n            case \"efcltd\":\n                return _public_images_efc_logo_jpg__WEBPACK_IMPORTED_MODULE_4__[\"default\"];\n            case \"fpp-ltd\":\n                return _public_images_fpp_logo_png__WEBPACK_IMPORTED_MODULE_6__[\"default\"];\n            case \"thl\":\n                return _public_images_efc_logo_jpg__WEBPACK_IMPORTED_MODULE_4__[\"default\"];\n            case \"issproduce\":\n                return _public_images_iss_logo_jpg__WEBPACK_IMPORTED_MODULE_7__[\"default\"];\n            case \"flrs\":\n                return _public_images_efc_logo_jpg__WEBPACK_IMPORTED_MODULE_4__[\"default\"];\n            default:\n                return;\n        }\n    };\n    const getLogoSizeClass = (company)=>{\n        if (!company) return \"h-14 w-100\";\n        switch(company){\n            case \"dpsltd\":\n                return \"!h-16 !w-auto\";\n            default:\n                return \"h-14 w-100\";\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n            id: \"sidemenu\",\n            className: \"navbar navbar-default sidebar bg-skin-primary\",\n            role: \"navigation\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"container-fluid h-full\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_12___default()), {\n                        href: \"/suppliers\",\n                        title: \"Home\",\n                        className: \"z-50\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"brand\",\n                            children: company ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_13___default()), {\n                                src: getLogo(company == \"dpsltd\" && ADCompany == \"DPS MS\" ? \"DPS MS\" : company),\n                                alt: \"logo\",\n                                className: getLogoSizeClass(company == \"dpsltd\" && ADCompany == \"DPS MS\" ? \"DPS MS\" : company)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\Sidebar.js\",\n                                lineNumber: 117,\n                                columnNumber: 17\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_loader_spinner__WEBPACK_IMPORTED_MODULE_18__.Oval, {\n                                    color: \"#002D73\",\n                                    height: 20,\n                                    width: 20,\n                                    visible: true,\n                                    ariaLabel: \"oval-loading\",\n                                    secondaryColor: \"#0066FF\",\n                                    strokeWidth: 2,\n                                    strokeWidthSecondary: 2\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\Sidebar.js\",\n                                    lineNumber: 132,\n                                    columnNumber: 19\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\Sidebar.js\",\n                                lineNumber: 131,\n                                columnNumber: 17\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\Sidebar.js\",\n                            lineNumber: 115,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\Sidebar.js\",\n                        lineNumber: 114,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        id: \"bs-sidebar-navbar-collapse-1\",\n                        className: \"pt-10 w-100 text-center flex flex-col justify-between flex-end items-stretch h-full\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col w-full\",\n                                children: userData && company && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_SideBarLinks__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                    isSuppliersActive: isSuppliersActive,\n                                    userData: userData,\n                                    currentPathname: currentPathname,\n                                    company: company,\n                                    isUsersActive: isUsersActive,\n                                    isLogsActive: isLogsActive,\n                                    isProductsActive: isProductsActive,\n                                    isWhatifActive: isWhatifActive,\n                                    isServiceLevelActive: isServiceLevelActive,\n                                    ADCompany: ADCompany\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\Sidebar.js\",\n                                    lineNumber: 152,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\Sidebar.js\",\n                                lineNumber: 150,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-center flex-col items-center gap-4 my-4 mb-20 cursor-pointer\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        title: \"IT Training Material\",\n                                        href: \"\".concat(process.env.NEXT_PUBLIC_TRAINING_MATERIAL),\n                                        target: \"_blank\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            xmlns: \"http://www.w3.org/2000/svg\",\n                                            viewBox: \"0 0 448 512\",\n                                            fill: \"#FFFF\",\n                                            className: \"w-6\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                d: \"M0 88C0 39.4 39.4 0 88 0L392 0c30.9 0 56 25.1 56 56l0 288c0 22.3-13.1 41.6-32 50.6l0 69.4 8 0c13.3 0 24 10.7 24 24s-10.7 24-24 24L80 512c-44.2 0-80-35.8-80-80c0-2.7 .1-5.4 .4-8L0 424 0 88zM80 400c-17.7 0-32 14.3-32 32s14.3 32 32 32l288 0 0-64L80 400zM48 358.7c9.8-4.3 20.6-6.7 32-6.7l312 0c4.4 0 8-3.6 8-8l0-288c0-4.4-3.6-8-8-8L88 48C65.9 48 48 65.9 48 88l0 270.7zM160 112l8.8-17.7c2.9-5.9 11.4-5.9 14.3 0L192 112l17.7 8.8c5.9 2.9 5.9 11.4 0 14.3L192 144l-8.8 17.7c-2.9 5.9-11.4 5.9-14.3 0L160 144l-17.7-8.8c-5.9-2.9-5.9-11.4 0-14.3L160 112zM264 216l16.6-38.8c2.8-6.5 11.9-6.5 14.7 0L312 216l38.8 16.6c6.5 2.8 6.5 11.9 0 14.7L312 264l-16.6 38.8c-2.8 6.5-11.9 6.5-14.7 0L264 264l-38.8-16.6c-6.5-2.8-6.5-11.9 0-14.7L264 216z\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\Sidebar.js\",\n                                                lineNumber: 178,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\Sidebar.js\",\n                                            lineNumber: 172,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\Sidebar.js\",\n                                        lineNumber: 167,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        title: \"Logout\",\n                                        onClick: ()=>{\n                                            localStorage.removeItem(\"superUser\");\n                                            js_cookie__WEBPACK_IMPORTED_MODULE_17__[\"default\"].remove(\"company\");\n                                            js_cookie__WEBPACK_IMPORTED_MODULE_17__[\"default\"].remove(\"ADCompanyName\");\n                                            localStorage.removeItem(\"id\");\n                                            localStorage.removeItem(\"name\");\n                                            localStorage.removeItem(\"role\");\n                                            localStorage.removeItem(\"email\");\n                                            js_cookie__WEBPACK_IMPORTED_MODULE_17__[\"default\"].remove(\"user\");\n                                            js_cookie__WEBPACK_IMPORTED_MODULE_17__[\"default\"].remove(\"theme\");\n                                            js_cookie__WEBPACK_IMPORTED_MODULE_17__[\"default\"].remove(\"token\");\n                                            const redirectUrl = \"/login?redirect=\".concat(encodeURIComponent(window.location.pathname));\n                                            (0,_utils_auth_auth__WEBPACK_IMPORTED_MODULE_15__.logoutHandler)(instance, redirectUrl);\n                                        },\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_13___default()), {\n                                            src: _public_images_logout_icon_png__WEBPACK_IMPORTED_MODULE_11__[\"default\"],\n                                            alt: \"logout\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\Sidebar.js\",\n                                            lineNumber: 200,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\Sidebar.js\",\n                                        lineNumber: 181,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\Sidebar.js\",\n                                lineNumber: 166,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\Sidebar.js\",\n                        lineNumber: 146,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\Sidebar.js\",\n                lineNumber: 113,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\Sidebar.js\",\n            lineNumber: 108,\n            columnNumber: 7\n        }, this)\n    }, void 0, false);\n}\n_s(Sidebar, \"CqfPHC0NzGwmyxMjqjmKLbgxaBY=\", false, function() {\n    return [\n        _azure_msal_react__WEBPACK_IMPORTED_MODULE_16__.useMsal,\n        next_router__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        next_navigation__WEBPACK_IMPORTED_MODULE_14__.usePathname\n    ];\n});\n_c = Sidebar;\nvar _c;\n$RefreshReg$(_c, \"Sidebar\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevExports = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevExports) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports on update so we can compare the boundary\n                // signatures.\n                module.hot.dispose(function (data) {\n                    data.prevExports = currentExports;\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevExports !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevExports, currentExports)) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevExports !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/Sidebar.js\n"));

/***/ }),

/***/ "./utils/exportExcel.js":
/*!******************************!*\
  !*** ./utils/exportExcel.js ***!
  \******************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var exceljs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! exceljs */ \"./node_modules/exceljs/dist/exceljs.min.js\");\n/* harmony import */ var exceljs__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(exceljs__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react_toastify__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react-toastify */ \"./node_modules/react-toastify/dist/react-toastify.esm.mjs\");\n/* harmony import */ var react_toastify_dist_ReactToastify_css__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-toastify/dist/ReactToastify.css */ \"./node_modules/react-toastify/dist/ReactToastify.css\");\n/* harmony import */ var react_toastify_dist_ReactToastify_css__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react_toastify_dist_ReactToastify_css__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _services_apiConfig__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/services/apiConfig */ \"./services/apiConfig.js\");\n/* harmony import */ var _auth_auth__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./auth/auth */ \"./utils/auth/auth.js\");\n\n\n\n\n\n// todo:move the supplierCode column from the excel file to first column\nconst exportExcel = async function(data, isInternal, token, company, userData, prophet_id, requestor_email, isMultiple) {\n    let isProductExtract = arguments.length > 8 && arguments[8] !== void 0 ? arguments[8] : false, onProductSubmit = arguments.length > 9 && arguments[9] !== void 0 ? arguments[9] : false, productEmailParagraph = arguments.length > 10 && arguments[10] !== void 0 ? arguments[10] : \"\", productEmailCommentPlaceholder = arguments.length > 11 && arguments[11] !== void 0 ? arguments[11] : \"\", request_no = arguments.length > 12 && arguments[12] !== void 0 ? arguments[12] : \"\", varietyRequest = arguments.length > 13 && arguments[13] !== void 0 ? arguments[13] : false;\n    let serverAddress = _services_apiConfig__WEBPACK_IMPORTED_MODULE_3__.apiConfig.serverAddress;\n    let isEmergencyAndFinanceNotCompleteObj = [];\n    let supplierNames = [];\n    const workbook = new (exceljs__WEBPACK_IMPORTED_MODULE_0___default().Workbook)();\n    if (isInternal) {\n        data.forEach((sheetData, index1)=>{\n            if (sheetData.length === 0) {\n                console.error(\"sheetData is empty for index:\", index1);\n                return;\n            }\n            const sheetName = sheetData[0] || \"Sheet\".concat(index1 + 1);\n            let worksheet;\n            if (index1 < 9) {\n                if (Array.isArray(sheetName)) {\n                    const actualSheetName = sheetName[0];\n                    worksheet = workbook.addWorksheet(actualSheetName);\n                } else {\n                    worksheet = workbook.addWorksheet(sheetName);\n                }\n            }\n            if (sheetData.length > 1) {\n                let headers;\n                if (index1 < 9) {\n                    headers = Object.keys(sheetData[1]);\n                    worksheet.addRow(headers);\n                }\n                sheetData.slice(1).forEach((row, internalIndex)=>{\n                    if (index1 < 9) {\n                        const rowData = headers.map((header)=>row[header] || \"\");\n                        if (index1 != 4 && index1 != 3) {\n                            worksheet.addRow(rowData);\n                        } else if (index1 == 4) {\n                            worksheet.addRow(rowData);\n                        } else if (index1 == 3 && rowData[1] != \"\") {\n                            worksheet.addRow(rowData);\n                        }\n                    }\n                    if (index1 === 9) {\n                        supplierNames.push({\n                            supplierName: row === null || row === void 0 ? void 0 : row.supplierName,\n                            supplierCode: row === null || row === void 0 ? void 0 : row.supplierCode\n                        });\n                        if (row.isEmergencyAndFinanceNotComplete) {\n                            let isEmergencyAndFinanceNotCompleteSupplier = {\n                                isEmergencyAndFinanceNotComplete: row.isEmergencyAndFinanceNotComplete,\n                                supplierName: row.supplierName\n                            };\n                            isEmergencyAndFinanceNotCompleteObj.push(isEmergencyAndFinanceNotCompleteSupplier);\n                        }\n                        fetch(\"\".concat(serverAddress, \"suppliers/update-supplier/\").concat(row.id), {\n                            method: \"PUT\",\n                            headers: {\n                                Authorization: \"Bearer \".concat(token),\n                                Accept: \"application/json\",\n                                \"Content-Type\": \"application/json\"\n                            },\n                            body: JSON.stringify({\n                                sectionName: \"updateStatus\",\n                                type: \"exportExcel\",\n                                status: 5,\n                                exported: true,\n                                updated_date: new Date().toISOString(),\n                                company_name: row.supplierName,\n                                to: \"Internal\"\n                            })\n                        }).then((res)=>{\n                            if (res.status === 200) {\n                                return res.json();\n                            }\n                            // if (res.status === 401){\n                            //   toast.error(\"Your session has expired. Please log in again.\");\n                            //   setTimeout(() => {\n                            //     const redirectUrl = `/login?redirect=${encodeURIComponent(\n                            //       window.location.pathname\n                            //     )}`;\n                            //     logoutHandler(instance, redirectUrl);\n                            //   }, 3000);\n                            //   return null;\n                            // }\n                            return Promise.reject(res);\n                        }).then((json)=>{\n                            if (json.status == 200) {\n                                return true;\n                            }\n                        }).catch((error)=>{\n                            console.log(error);\n                        });\n                    }\n                });\n            }\n        });\n    } else if (isProductExtract) {\n        if (data.length === 0) {\n            console.error(\"sheetData is empty for index:\");\n            return;\n        }\n        data.forEach((sheetData)=>{\n            var _Object;\n            const sheetName = sheetData[0] || \"Sheet\".concat(index + 1);\n            let worksheet;\n            if (Array.isArray(sheetName)) {\n                const actualSheetName = sheetName[0];\n                worksheet = workbook.addWorksheet(actualSheetName);\n            } else {\n                worksheet = workbook.addWorksheet(sheetName);\n            }\n            // console.log(\"Object.keys(sheetData[1])\",Object?.keys(sheetData[1])? Object?.keys(sheetData[1]) : 'not there');\n            let headers = ((_Object = Object) === null || _Object === void 0 ? void 0 : _Object.keys(sheetData[1])) ? Object.keys(sheetData[1]) : \"null\";\n            if (isMultiple) {\n                headers = headers.slice(0, -1);\n            }\n            worksheet.addRow(headers);\n            sheetData.slice(1).forEach((row, internalIndex)=>{\n                let rowData;\n                rowData = headers.map((header)=>row[header] || \"\");\n                worksheet.addRow(rowData);\n            });\n        });\n    } else {\n        if (data.length === 0) {\n            console.error(\"sheetData is empty for index:\");\n            return;\n        }\n        data.forEach((row, index1)=>{\n            const sheetName = row[0] || \"Sheet\".concat(index1 + 1);\n            let worksheet;\n            if (Array.isArray(sheetName)) {\n                const actualSheetName = sheetName[0];\n                worksheet = workbook.addWorksheet(actualSheetName);\n            } else {\n                worksheet = workbook.addWorksheet(sheetName);\n            }\n            let headers;\n            if (index1 == 1) {\n                headers = Object.keys(row[1]).slice(0, -2);\n            } else {\n                headers = Object.keys(row[1]);\n            }\n            worksheet.addRow(headers);\n            for(let i = 1; i < row.length; i++){\n                if (index1 == 0 && i > 0) {\n                    supplierNames.push({\n                        supplierName: row[i][\"Supplier name\"],\n                        supplierCode: row[i][\"Supplier code\"]\n                    });\n                }\n                let rowData;\n                if (index1 == 1) {\n                    rowData = headers.map((header)=>row[i][header] || \"\").slice(0, -2);\n                } else {\n                    rowData = headers.map((header)=>row[i][header] || \"\");\n                }\n                worksheet.addRow(rowData);\n                if (row[i].isEmergencyAndFinanceNotComplete) {\n                    let isEmergencyAndFinanceNotCompleteSupplier = {\n                        isEmergencyAndFinanceNotComplete: row[i].isEmergencyAndFinanceNotComplete,\n                        supplierName: row[i][\"Supplier name\"]\n                    };\n                    isEmergencyAndFinanceNotCompleteObj.push(isEmergencyAndFinanceNotCompleteSupplier);\n                }\n                if (index1 == 1) {\n                    fetch(\"\".concat(serverAddress, \"suppliers/update-supplier/\").concat(row[i].id), {\n                        method: \"PUT\",\n                        headers: {\n                            Authorization: \"Bearer \".concat(token),\n                            Accept: \"application/json\",\n                            \"Content-Type\": \"application/json\"\n                        },\n                        body: JSON.stringify({\n                            sectionName: \"updateStatus\",\n                            type: \"exportExcel\",\n                            status: 5,\n                            exported: true,\n                            updated_date: new Date().toISOString(),\n                            company_name: row[\"Supplier name\"],\n                            to: \"ISS\"\n                        })\n                    }).then((res)=>{\n                        if (res.status === 200) {\n                            return res.json();\n                        }\n                        // if (res.status === 401){\n                        //   toast.error(\"Your session has expired. Please log in again.\");\n                        //   setTimeout(() => {\n                        //     const redirectUrl = `/login?redirect=${encodeURIComponent(\n                        //       window.location.pathname\n                        //     )}`;\n                        //     logoutHandler(instance, redirectUrl);\n                        //   }, 3000);\n                        //   return null;\n                        // }\n                        return Promise.reject(res);\n                    }).then((json)=>{\n                        if (json.status == 200) {\n                            return true;\n                        }\n                    }).catch((error)=>{\n                        console.log(error);\n                    });\n                }\n            }\n        });\n    }\n    const buffer = await workbook.xlsx.writeBuffer();\n    const blob = new Blob([\n        buffer\n    ], {\n        type: \"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet\"\n    });\n    const blobUrl = URL.createObjectURL(blob);\n    const a = document.createElement(\"a\");\n    a.style.display = \"none\";\n    a.href = blobUrl;\n    const now = new Date();\n    const timestamp = \"\".concat(now.getFullYear(), \"-\").concat((now.getMonth() + 1).toString().padStart(2, \"0\"), \"-\").concat(now.getDate().toString().padStart(2, \"0\"), \"_\").concat(now.getHours().toString().padStart(2, \"0\"), \"-\").concat(now.getMinutes().toString().padStart(2, \"0\"), \"-\").concat(now.getSeconds().toString().padStart(2, \"0\"));\n    if (isInternal) {\n        a.download = \"internal_export_\".concat(timestamp, \"_\").concat(data[0].length - 1 === 1 ? \"S\" : \"G\", \".xlsx\");\n    } else if (isProductExtract) {\n        if (request_no && !varietyRequest) {\n            a.download = \"\".concat(request_no, \"_product_export.xlsx\");\n        } else if (request_no && varietyRequest) {\n            a.download = \"\".concat(request_no, \"_export.xlsx\");\n        } else {\n            a.download = \"product_export.xlsx\";\n        }\n    } else {\n        a.download = \"iss_export_\".concat(timestamp, \"_\").concat(data.length === 1 ? \"S\" : \"G\", \".xlsx\");\n    }\n    let fileName;\n    console.log(\"fle name\", fileName);\n    if (isInternal) {\n        fileName = \"internal_export_\".concat(timestamp, \"_\").concat(data[0].length - 1 === 1 ? \"S\" : \"G\", \".xlsx\");\n    } else if (isProductExtract && !varietyRequest) {\n        fileName = request_no ? \"\".concat(request_no, \"_product_export.xlsx\") : \"product_export.xlsx\";\n    } else if (varietyRequest) {\n        fileName = request_no ? \"\".concat(request_no, \"_NV_export.xlsx\") : \"NV_export.xlsx\";\n    } else {\n        fileName = \"iss_export_\".concat(timestamp, \"_\").concat(data.length === 1 ? \"S\" : \"G\", \".xlsx\");\n    }\n    console.log(\"fle name --- \\n\", fileName);\n    document.body.appendChild(a);\n    a.click();\n    // Clean up\n    URL.revokeObjectURL(blobUrl);\n    document.body.removeChild(a);\n    react_toastify__WEBPACK_IMPORTED_MODULE_1__.toast.success(\"Request details extracted successfully.\", {\n        toastId: 22,\n        position: \"top-right\",\n        autoClose: 3000,\n        pauseOnHover: false\n    });\n    if (!varietyRequest) {\n        const formData = new FormData();\n        formData.append(\"file\", blob, fileName);\n        formData.append(\"company\", company);\n        formData.append(\"prophet_id\", prophet_id);\n        formData.append(\"name\", userData === null || userData === void 0 ? void 0 : userData.name);\n        formData.append(\"isInternal\", isInternal);\n        formData.append(\"exporterEmail\", userData === null || userData === void 0 ? void 0 : userData.email);\n        formData.append(\"requestorEmail\", requestor_email);\n        formData.append(\"isProductRequest\", isProductExtract);\n        formData.append(\"supplierNames\", JSON.stringify(supplierNames));\n        formData.append(\"onProductSubmit\", onProductSubmit);\n        formData.append(\"request_no\", request_no);\n        formData.append(\"productEmailCommentPlaceholder\", productEmailCommentPlaceholder);\n        formData.append(\"productEmailParagraph\", productEmailParagraph);\n        const serializedData = JSON.stringify(isEmergencyAndFinanceNotCompleteObj);\n        formData.append(\"isEmergencyAndFinanceNotCompleteObj\", serializedData);\n        const response = await fetch(\"\".concat(serverAddress, \"email/send-email\"), {\n            method: \"POST\",\n            body: formData,\n            headers: {\n                Authorization: \"Bearer \".concat(token)\n            }\n        });\n        if (response.ok) {\n            react_toastify__WEBPACK_IMPORTED_MODULE_1__.toast.success(\"Email sent\", {\n                toastId: 22,\n                position: \"top-right\",\n                autoClose: 3000,\n                pauseOnHover: false\n            });\n        } else if (response.status === 401) {\n            // console.log(\"error YES 401\");\n            react_toastify__WEBPACK_IMPORTED_MODULE_1__.toast.error(\"Failed to send email\", {\n                toastId: 22,\n                position: \"top-right\",\n                autoClose: 3000,\n                pauseOnHover: false\n            });\n            return response.status;\n        } else {\n            react_toastify__WEBPACK_IMPORTED_MODULE_1__.toast.error(\"Failed to send email\", {\n                toastId: 22,\n                position: \"top-right\",\n                autoClose: 3000,\n                pauseOnHover: false\n            });\n        }\n    }\n    return true;\n};\n/* harmony default export */ __webpack_exports__[\"default\"] = (exportExcel);\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevExports = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevExports) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports on update so we can compare the boundary\n                // signatures.\n                module.hot.dispose(function (data) {\n                    data.prevExports = currentExports;\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevExports !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevExports, currentExports)) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevExports !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./utils/exportExcel.js\n"));

/***/ }),

/***/ "./utils/renderer/actionRenderer.js":
/*!******************************************!*\
  !*** ./utils/renderer/actionRenderer.js ***!
  \******************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _fortawesome_react_fontawesome__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @fortawesome/react-fontawesome */ \"./node_modules/@fortawesome/react-fontawesome/index.es.js\");\n/* harmony import */ var _fortawesome_free_solid_svg_icons__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @fortawesome/free-solid-svg-icons */ \"./node_modules/@fortawesome/free-solid-svg-icons/index.mjs\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/link */ \"./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _services_apiConfig__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/services/apiConfig */ \"./services/apiConfig.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var js_cookie__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! js-cookie */ \"./node_modules/js-cookie/dist/js.cookie.mjs\");\n/* harmony import */ var _exportExcel__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../exportExcel */ \"./utils/exportExcel.js\");\n/* harmony import */ var react_toastify__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! react-toastify */ \"./node_modules/react-toastify/dist/react-toastify.esm.mjs\");\n/* harmony import */ var react_toastify_dist_ReactToastify_css__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! react-toastify/dist/ReactToastify.css */ \"./node_modules/react-toastify/dist/ReactToastify.css\");\n/* harmony import */ var react_toastify_dist_ReactToastify_css__WEBPACK_IMPORTED_MODULE_9___default = /*#__PURE__*/__webpack_require__.n(react_toastify_dist_ReactToastify_css__WEBPACK_IMPORTED_MODULE_9__);\n/* harmony import */ var _utils_userContext__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/utils/userContext */ \"./utils/userContext.js\");\n/* harmony import */ var _utils_getCookieData__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/utils/getCookieData */ \"./utils/getCookieData.js\");\n/* harmony import */ var _components_CopySupplier__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/components/CopySupplier */ \"./components/CopySupplier.js\");\n/* harmony import */ var _barrel_optimize_names_Dialog_Transition_headlessui_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Dialog,Transition!=!@headlessui/react */ \"__barrel_optimize__?names=Dialog,Transition!=!./node_modules/@headlessui/react/dist/headlessui.esm.js\");\n/* harmony import */ var _utils_rolePermissionsContext__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/utils/rolePermissionsContext */ \"./utils/rolePermissionsContext.js\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst actionRenderer = (params, userData, token, company)=>{\n    var _supplierData_prophets_;\n    _s();\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const canExport = (userData === null || userData === void 0 ? void 0 : userData.role_id) === 1 || (userData === null || userData === void 0 ? void 0 : userData.role_id) === 2 || (userData === null || userData === void 0 ? void 0 : userData.role_id) === 5 || (userData === null || userData === void 0 ? void 0 : userData.role_id) === 6 || params.data.isEmergencyRequest;\n    const { permissions, updatePermissions } = (0,_utils_rolePermissionsContext__WEBPACK_IMPORTED_MODULE_13__.usePermissions)();\n    const serverAddress = _services_apiConfig__WEBPACK_IMPORTED_MODULE_4__.apiConfig.serverAddress;\n    const { userDetails, updateToken } = (0,_utils_userContext__WEBPACK_IMPORTED_MODULE_10__.useUser)();\n    const supplierData = params.data;\n    let prophet_id = (supplierData === null || supplierData === void 0 ? void 0 : supplierData.prophets.length) > 0 && (supplierData === null || supplierData === void 0 ? void 0 : (_supplierData_prophets_ = supplierData.prophets[0]) === null || _supplierData_prophets_ === void 0 ? void 0 : _supplierData_prophets_.prophet_id);\n    const role_ids = params.data.roleId.map((ele)=>ele.role_id);\n    const supplier_id = params.data.id;\n    const supplier_status = params.data.status;\n    const [status, setStatus] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(\"\");\n    const [data, setData] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)([]);\n    const user = (0,_utils_getCookieData__WEBPACK_IMPORTED_MODULE_11__.getCookieData)(\"user\");\n    const [isOpen, setIsOpen] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(false);\n    const [isOpenOption, setIsOpenOption] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(false);\n    const [selectedExportType, setSelectedExportType] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(\"\");\n    const [emailStatusPopup, setEmailStatusPopup] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(false);\n    const [popupMessage, setPopUpMessage] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(\"\");\n    const [internalExportSuccess, setInternalExportSuccess] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(false);\n    const [ISSExportSuccess, setISSExportSuccess] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(false);\n    const closeOptionModal = ()=>{\n        setIsOpenOption(false);\n    };\n    const [isCancelOpen, setIsCancelOpen] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(false);\n    const cancelProduct = ()=>{\n        setIsCancelOpen(true);\n    };\n    const closeCancelModal = ()=>{\n        setIsCancelOpen(false);\n    };\n    const closeEmailPopup = ()=>{\n        setEmailStatusPopup(false);\n        if (internalExportSuccess && ISSExportSuccess) {\n            params.setUpdateStatusChange(data === null || data === void 0 ? void 0 : data.id, data === null || data === void 0 ? void 0 : data.status);\n        }\n    };\n    function saveModalData() {\n        var _params_data, _params_data1;\n        let serverAddress = _services_apiConfig__WEBPACK_IMPORTED_MODULE_4__.apiConfig.serverAddress;\n        // setLoading(true);\n        fetch(\"\".concat(serverAddress, \"suppliers/update-supplier/\").concat(supplier_id), {\n            method: \"PUT\",\n            headers: {\n                Accept: \"application/json\",\n                \"Content-Type\": \"application/json\",\n                Authorization: \"Bearer \".concat(userData.token)\n            },\n            body: JSON.stringify({\n                sectionName: \"updateStatus\",\n                type: \"cancelProduct\",\n                status: 6,\n                updated_date: new Date().toISOString(),\n                company_name: params === null || params === void 0 ? void 0 : (_params_data = params.data) === null || _params_data === void 0 ? void 0 : _params_data.company_name,\n                requestor_name: params.data.requestor,\n                requestor_email: (_params_data1 = params.data) === null || _params_data1 === void 0 ? void 0 : _params_data1.requestor_email\n            })\n        }).then((res)=>{\n            if (res.status === 400) {\n                react_toastify__WEBPACK_IMPORTED_MODULE_8__.toast.error(\"There was an error with your request. Please check your data and try again.\");\n            } else if (res.status === 401) {\n                react_toastify__WEBPACK_IMPORTED_MODULE_8__.toast.error(\"Your session has expired. Please log in again.\");\n            // setTimeout(() => {\n            //   localStorage.removeItem(\"superUser\");\n            //   Cookies.remove(\"company\")\n            //   localStorage.removeItem(\"id\");\n            //   localStorage.removeItem(\"name\");\n            //   localStorage.removeItem(\"role\");\n            //   localStorage.removeItem(\"email\");\n            //   Cookies.remove(\"user\")\n            //   Cookies.remove(\"theme\")\n            //   Cookies.remove(\"token\")\n            //   // logoutHandler(instance);\n            // }, 3000);\n            }\n            if (res.status === 200) {\n                return res.json();\n            }\n            return Promise.reject(res);\n        }).then((json)=>{\n            react_toastify__WEBPACK_IMPORTED_MODULE_8__.toast.success(\"Supplier cancelled successfully\", {\n                position: \"top-right\"\n            });\n            params.setUpdateStatusChange(data === null || data === void 0 ? void 0 : data.id, data === null || data === void 0 ? void 0 : data.status);\n            closeCancelModal();\n        }).catch((err)=>{\n            // setLoading(false);\n            react_toastify__WEBPACK_IMPORTED_MODULE_8__.toast.error(\"Error cancelling product:\", err.statusText, {\n                position: \"top-right\"\n            });\n            return err;\n        });\n    }\n    const openOptionModal = (supplier_id)=>{\n        if (supplierData.status == \"Completed\" || supplierData.status == \"Exported\" || params.data.isEmergencyRequest && params.data.General === \"Complete\") {\n            var _params_data_prophets_, _params_data, _params_data_prophets_1, _params_data1, _params_data2, _params_data3, _params_data_roleIds, _params_data4, _params_data_roleIds1, _params_data5, _params_data6, _params_data7, _params_data8;\n            let isExportableBasedOnCodeUnique = false;\n            const codeCount = (_params_data = params.data) === null || _params_data === void 0 ? void 0 : (_params_data_prophets_ = _params_data.prophets[0]) === null || _params_data_prophets_ === void 0 ? void 0 : _params_data_prophets_.code_count;\n            const prophetCode = (_params_data1 = params.data) === null || _params_data1 === void 0 ? void 0 : (_params_data_prophets_1 = _params_data1.prophets[0]) === null || _params_data_prophets_1 === void 0 ? void 0 : _params_data_prophets_1.prophet_code;\n            const prophet_id = ((_params_data2 = params.data) === null || _params_data2 === void 0 ? void 0 : _params_data2.prophets.length) > 0 && ((_params_data3 = params.data) === null || _params_data3 === void 0 ? void 0 : _params_data3.prophets[0].prophet_id);\n            const isSupplierAccount = ((_params_data4 = params.data) === null || _params_data4 === void 0 ? void 0 : (_params_data_roleIds = _params_data4.roleIds) === null || _params_data_roleIds === void 0 ? void 0 : _params_data_roleIds.includes(1)) || ((_params_data5 = params.data) === null || _params_data5 === void 0 ? void 0 : (_params_data_roleIds1 = _params_data5.roleIds) === null || _params_data_roleIds1 === void 0 ? void 0 : _params_data_roleIds1.includes(6));\n            let currency = ((_params_data6 = params.data) === null || _params_data6 === void 0 ? void 0 : _params_data6.currency) == \"$\" ? \"\\\\\".concat((_params_data7 = params.data) === null || _params_data7 === void 0 ? void 0 : _params_data7.currency) : (_params_data8 = params.data) === null || _params_data8 === void 0 ? void 0 : _params_data8.currency;\n            let actualCurr;\n            if (currency && currency == \"Not Entered\") {\n                actualCurr = \"\";\n            } else {\n                actualCurr = currency;\n            }\n            let isValid = true;\n            if (isSupplierAccount) {\n                if (prophet_id == 1) {\n                    let regexPattern;\n                    regexPattern = new RegExp(\"^[A-Z0-9]{4}[A-Z0145678]\".concat(actualCurr, \"$\"));\n                    // let regexPattern = new RegExp(`^([A-Z0]{5})${actualCurr}$`);\n                    isValid = regexPattern.test(prophetCode);\n                } else if (prophet_id == 2) {\n                    let regexPattern;\n                    regexPattern = new RegExp(\"^([A-Z0]{4})9\".concat(actualCurr, \"$\"));\n                    // let regexPattern = new RegExp(`^([A-Z0]{4})9${actualCurr}$`);\n                    isValid = regexPattern.test(prophetCode);\n                } else if (prophet_id == 3) {\n                    let regexPattern = new RegExp(\"^[A-Z0-9]{4}[A-Z01345678][A-Z0-9]*$\");\n                    isValid = regexPattern.test(prophetCode) && prophetCode.length == 6;\n                } else if (prophet_id == 4) {\n                    let regexPattern;\n                    regexPattern = new RegExp(\"^([A-Z0]{4})2\".concat(actualCurr, \"$\"));\n                    isValid = regexPattern.test(prophetCode);\n                }\n            }\n            if (codeCount && codeCount > 1 && prophetCode && prophetCode !== \"\") {\n                isExportableBasedOnCodeUnique = false;\n            } else if (codeCount && codeCount == 1 && prophetCode && prophetCode !== \"\") {\n                isExportableBasedOnCodeUnique = true;\n            }\n            if (!isExportableBasedOnCodeUnique && !isValid) {\n                react_toastify__WEBPACK_IMPORTED_MODULE_8__.toast.error(\"Supplier code is not unique and valid, kindly make sure the supplier code is unique and is valid.\");\n                return;\n            } else if (!isExportableBasedOnCodeUnique) {\n                react_toastify__WEBPACK_IMPORTED_MODULE_8__.toast.error(\"Supplier code is not unique, kindly make sure the supplier code is unique.\");\n                return;\n            } else if (!isValid) {\n                react_toastify__WEBPACK_IMPORTED_MODULE_8__.toast.error(\"Supplier code is not vaild, kindly make sure the supplier code is valid.\");\n                return;\n            }\n            handleSingleExportSupplier(supplier_id);\n        } else {\n            handleSingleExportSupplier(supplier_id);\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_5__.useEffect)(()=>{\n        if (supplier_id) {\n            setData(supplierData);\n            setStatus(supplier_status);\n        }\n    }, [\n        supplier_id\n    ]);\n    const editSupplier = ()=>{\n        if (true) {\n            router.push({\n                pathname: \"/supplier/\".concat(supplier_id, \"/edit\")\n            });\n        }\n    };\n    const confirmPage = ()=>{\n        const mappedPermissions = role_ids.map((roleId)=>({\n                roleId: roleId,\n                permissions: permissions[roleId]\n            }));\n        const uniqueSections = [\n            ...new Set(mappedPermissions.flatMap((item)=>item.permissions))\n        ];\n        localStorage.setItem(\"allowedSections\", uniqueSections);\n        if (true) {\n            router.push({\n                pathname: \"/supplier/\".concat(supplier_id, \"/confirm\")\n            });\n        }\n    };\n    function getGLCode(internal_ledger_code, department, currency, roleIds) {\n        if (internal_ledger_code) {\n            return internal_ledger_code;\n        } else {\n            return \"\";\n        }\n    }\n    const extractContacts = (contactsJsonStr)=>{\n        try {\n            const contacts = contactsJsonStr ? JSON.parse(contactsJsonStr) : [];\n            if (Array.isArray(contacts)) {\n                return contacts.map((contact)=>{\n                    var _data_prophets_, _data_prophets__prophet_code, _data_prophets_1;\n                    return {\n                        \"Supplier code\": (data === null || data === void 0 ? void 0 : (_data_prophets_ = data.prophets[0]) === null || _data_prophets_ === void 0 ? void 0 : _data_prophets_.prophet_code) ? data === null || data === void 0 ? void 0 : (_data_prophets_1 = data.prophets[0]) === null || _data_prophets_1 === void 0 ? void 0 : (_data_prophets__prophet_code = _data_prophets_1.prophet_code) === null || _data_prophets__prophet_code === void 0 ? void 0 : _data_prophets__prophet_code.trim() : \"\",\n                        \"Contact ID\": \"\",\n                        Name: data.company_name || \"\",\n                        \"Email Address\": contact.email_id || \"\",\n                        \"Telephone number\": contact.telephone || \"\",\n                        \"Cell phone number\": \"\",\n                        \"Fax number\": \"\",\n                        \"Instant Message\": \"\",\n                        \"Physical Address\": \"\",\n                        \"Postal Address\": \"\",\n                        \"Row verision\": \"\",\n                        \"Created timestamp\": \"\"\n                    };\n                });\n            }\n        } catch (error) {\n            console.error(\"Error parsing contacts_json:\", error);\n        }\n        return [\n            {\n                \"Contact ID\": \"\",\n                Name: data.company_name || \"\",\n                \"Email Address\": \"\",\n                \"Telephone number\": \"\",\n                \"Cell phone number\": \"\",\n                \"Fax number\": \"\",\n                \"Instant Message\": \"\",\n                \"Physical Address\": \"\",\n                \"Postal Address\": \"\",\n                \"Row verision\": \"\",\n                \"Created timestamp\": \"\"\n            }\n        ];\n    };\n    const extractSendacGroup = (sendacGroupJson, id)=>{\n        try {\n            const sendacGroups = sendacGroupJson ? JSON.parse(sendacGroupJson) : [];\n            if (Array.isArray(sendacGroups)) {\n                const filteredGroups = sendacGroups.filter((group)=>(group === null || group === void 0 ? void 0 : group.created_by) === id);\n                if (filteredGroups.length > 0) {\n                    return filteredGroups.map((group)=>({\n                            \"Supplier group\": \"\",\n                            Description: (group === null || group === void 0 ? void 0 : group.created_by) ? group === null || group === void 0 ? void 0 : group.label : \"\"\n                        }));\n                } else {\n                    // Handle the case when no matching group is found\n                    return []; // or any other default value or action\n                }\n            }\n        } catch (error) {\n            console.error(\"Error parsing contacts_json:\", error);\n        }\n    };\n    const multipleSendRoleOnRoleNums = ()=>{\n        var _data_role_num;\n        const roleNums = data === null || data === void 0 ? void 0 : (_data_role_num = data.role_num) === null || _data_role_num === void 0 ? void 0 : _data_role_num.split(\",\").map((num)=>num.trim());\n        return roleNums.map((num)=>{\n            var _data_prophets_, _data_prophets_1;\n            return {\n                Sendacroleid: \"\",\n                \"Supplier code\": (data === null || data === void 0 ? void 0 : (_data_prophets_ = data.prophets[0]) === null || _data_prophets_ === void 0 ? void 0 : _data_prophets_.prophet_code) ? data.prophets[0].prophet_code.trim() : \"\",\n                Description: (data === null || data === void 0 ? void 0 : (_data_prophets_1 = data.prophets[0]) === null || _data_prophets_1 === void 0 ? void 0 : _data_prophets_1.prophet_code) ? data.prophets[0].prophet_code.trim() : \"\",\n                \"Supplier Code Supplier Name\": data.company_name,\n                Type: num,\n                \"Type Description\": data === null || data === void 0 ? void 0 : data[\"role names\"],\n                \"Supplier code Global gap number\": data === null || data === void 0 ? void 0 : data.global_gap_number,\n                \"Created timestamp\": \"\",\n                Active: 1\n            };\n        });\n    };\n    const handleSingleExportSupplier = async (id)=>{\n        var _data_distribution_points_json, _data_roleIds;\n        const company = js_cookie__WEBPACK_IMPORTED_MODULE_6__[\"default\"].get(\"company\");\n        // setIsOpenOption(false);\n        let rolesArray = params.data.roleId.map((ele)=>{\n            return ele.role_id;\n        });\n        const formattedDistributionData = data === null || data === void 0 ? void 0 : (_data_distribution_points_json = data.distribution_points_json) === null || _data_distribution_points_json === void 0 ? void 0 : _data_distribution_points_json.map((row)=>({\n                distributionPoint: row === null || row === void 0 ? void 0 : row.name,\n                directDPvalue: row.direct_dp ? \"True\" : \"False\",\n                directDP: row.direct_dp,\n                from_dp: row.from_dp\n            }));\n        let filteredInternalExportData = [];\n        let filteredISSExportData = [];\n        // const isInternal = selectedExportType === \"internalExport\";\n        if (supplier_status === \"Completed\" || supplier_status === \"Exported\" || params.data.isEmergencyRequest && params.data.status != \"Cancelled\" && params.data.General === \"Complete\" && ((data === null || data === void 0 ? void 0 : data.roleIds.includes(1)) || (data === null || data === void 0 ? void 0 : (_data_roleIds = data.roleIds) === null || _data_roleIds === void 0 ? void 0 : _data_roleIds.includes(6))) && params.data.currency_id) {\n            var _params_data;\n            if (true) {\n                var _data_roleIds1, _data_roleIds2, _data_roleIds3, _data_prophets__prophet_code, _data_prophets_, _formattedDistributionData_, _data_prophets__prophet_code1, _data_prophets_1, _data_prophets_2, _data_prophets_3, _data_prophets_4, _data_prophets__prophet_code2, _data_prophets_5, _data_prophets_6, _data_prophets__prophet_code3, _data_prophets_7, _data_prophets_8, _data_prophets__prophet_code4, _data_prophets_9, _data_prophets_10, _data_prophets__prophet_code5, _data_prophets_11, _data_prophets_12, _data_prophets__prophet_code6, _data_prophets_13;\n                let sort_code = \"\";\n                let account_number = \"\";\n                let swiftBicCode = \"\";\n                let iban = \"\";\n                const swiftBicRegex = /^([A-Z0-9]{4}[A-Z0-9]{2}[A-Z0-9]{2}|[A-Z0-9]{4}[A-Z0-9]{2}[A-Z0-9]{2}[A-Z0-9]{3})$/;\n                if (swiftBicRegex.test(data === null || data === void 0 ? void 0 : data.decryptedSort_Bic) && (data === null || data === void 0 ? void 0 : data.has_iban)) {\n                    var _data_decryptedAccountNumber;\n                    sort_code = \"000000\";\n                    account_number = data === null || data === void 0 ? void 0 : (_data_decryptedAccountNumber = data.decryptedAccountNumber) === null || _data_decryptedAccountNumber === void 0 ? void 0 : _data_decryptedAccountNumber.slice(-8);\n                    swiftBicCode = data === null || data === void 0 ? void 0 : data.decryptedSort_Bic;\n                    iban = data === null || data === void 0 ? void 0 : data.decryptedAccountNumber;\n                } else if (!(data === null || data === void 0 ? void 0 : data.has_iban) && swiftBicRegex.test(data === null || data === void 0 ? void 0 : data.decryptedSort_Bic)) {\n                    sort_code = \"000000\";\n                    account_number = data === null || data === void 0 ? void 0 : data.decryptedAccountNumber;\n                    swiftBicCode = data === null || data === void 0 ? void 0 : data.decryptedSort_Bic;\n                } else {\n                    sort_code = data === null || data === void 0 ? void 0 : data.decryptedSort_Bic;\n                    account_number = data === null || data === void 0 ? void 0 : data.decryptedAccountNumber;\n                }\n                let regional_cert = \"\";\n                if ((data === null || data === void 0 ? void 0 : (_data_roleIds1 = data.roleIds) === null || _data_roleIds1 === void 0 ? void 0 : _data_roleIds1.includes(2)) || (data === null || data === void 0 ? void 0 : (_data_roleIds2 = data.roleIds) === null || _data_roleIds2 === void 0 ? void 0 : _data_roleIds2.includes(3))) {\n                    if ((data === null || data === void 0 ? void 0 : data.country_code) == \"UK\") {\n                        regional_cert = data === null || data === void 0 ? void 0 : data.red_tractor;\n                    } else if ((data === null || data === void 0 ? void 0 : data.country_code) == \"ZA\") {\n                        regional_cert = data === null || data === void 0 ? void 0 : data.puc_code;\n                    } else if ((data === null || data === void 0 ? void 0 : data.country_code) == \"CL\") {\n                        regional_cert = data === null || data === void 0 ? void 0 : data.chile_certificate_number;\n                    }\n                }\n                let currencyId = \"\";\n                let currencyName = \"\";\n                if ((data === null || data === void 0 ? void 0 : data.roleIds.includes(1)) || (data === null || data === void 0 ? void 0 : data.roleIds.includes(5)) || (data === null || data === void 0 ? void 0 : (_data_roleIds3 = data.roleIds) === null || _data_roleIds3 === void 0 ? void 0 : _data_roleIds3.includes(6))) {\n                    currencyId = (data === null || data === void 0 ? void 0 : data.currency_id) || 1;\n                    currencyName = (data === null || data === void 0 ? void 0 : data.currency_name) || \"Sterling\";\n                } else {\n                    currencyId = 1;\n                    currencyName = \"Sterling\";\n                }\n                function getCorrespondingUserLookup(curr) {\n                    if (curr == \"GBP\") {\n                        return \"GBPBACS\";\n                    } else if (curr == \"EUR\") {\n                        return \"EUROSEPA\";\n                    } else if (curr == \"USD\") {\n                        return \"USDPRIORITY\";\n                    } else {\n                        return \"\";\n                    }\n                }\n                console.log(\"supplier type\", data === null || data === void 0 ? void 0 : data.supplier_type);\n                var _data_edi;\n                filteredInternalExportData = [\n                    [\n                        \"UlpFil\",\n                        {\n                            \"Distribution point\": \"\",\n                            Description: (formattedDistributionData === null || formattedDistributionData === void 0 ? void 0 : formattedDistributionData.length) > 0 && (formattedDistributionData[0].from_dp === null || formattedDistributionData[0].from_dp === undefined) ? formattedDistributionData[0].distributionPoint : \"\",\n                            \"Service Supplier Code\": (formattedDistributionData === null || formattedDistributionData === void 0 ? void 0 : formattedDistributionData.length) > 0 && (formattedDistributionData[0].from_dp === null || formattedDistributionData[0].from_dp === undefined) && (data === null || data === void 0 ? void 0 : (_data_prophets_ = data.prophets[0]) === null || _data_prophets_ === void 0 ? void 0 : (_data_prophets__prophet_code = _data_prophets_.prophet_code) === null || _data_prophets__prophet_code === void 0 ? void 0 : _data_prophets__prophet_code.trim()),\n                            \"Default expected stock status\": (formattedDistributionData === null || formattedDistributionData === void 0 ? void 0 : formattedDistributionData.length) > 0 && (formattedDistributionData[0].from_dp === null || formattedDistributionData[0].from_dp === undefined) && \"0\",\n                            \"Default received stock status\": (formattedDistributionData === null || formattedDistributionData === void 0 ? void 0 : formattedDistributionData.length) > 0 && (formattedDistributionData[0].from_dp === null || formattedDistributionData[0].from_dp === undefined) && \"0\",\n                            \"Pallets in packhouse\": (formattedDistributionData === null || formattedDistributionData === void 0 ? void 0 : formattedDistributionData.length) > 0 && (formattedDistributionData[0].from_dp === null || formattedDistributionData[0].from_dp === undefined) && \"0\",\n                            \"Default haulier\": (formattedDistributionData === null || formattedDistributionData === void 0 ? void 0 : formattedDistributionData.length) > 0 && (formattedDistributionData[0].from_dp === null || formattedDistributionData[0].from_dp === undefined) && \"ZZZZZ\",\n                            \"Default expected location id\": (formattedDistributionData === null || formattedDistributionData === void 0 ? void 0 : formattedDistributionData.length) > 0 && (formattedDistributionData[0].from_dp === null || formattedDistributionData[0].from_dp === undefined) && 21,\n                            \"Default receiving location id\": (formattedDistributionData === null || formattedDistributionData === void 0 ? void 0 : formattedDistributionData.length) > 0 && (formattedDistributionData[0].from_dp === null || formattedDistributionData[0].from_dp === undefined) && 21,\n                            \"Packhouse location id\": (formattedDistributionData === null || formattedDistributionData === void 0 ? void 0 : formattedDistributionData.length) > 0 && (formattedDistributionData[0].from_dp === null || formattedDistributionData[0].from_dp === undefined) && 21,\n                            \"Default pick location id\": (formattedDistributionData === null || formattedDistributionData === void 0 ? void 0 : formattedDistributionData.length) > 0 && (formattedDistributionData[0].from_dp === null || formattedDistributionData[0].from_dp === undefined) && 21,\n                            \"Despatch location id\": (formattedDistributionData === null || formattedDistributionData === void 0 ? void 0 : formattedDistributionData.length) > 0 && (formattedDistributionData[0].from_dp === null || formattedDistributionData[0].from_dp === undefined) && 21,\n                            \"Default waste location id\": (formattedDistributionData === null || formattedDistributionData === void 0 ? void 0 : formattedDistributionData.length) > 0 && (formattedDistributionData[0].from_dp === null || formattedDistributionData[0].from_dp === undefined) && 21,\n                            \"Default pre-pick location id\": (formattedDistributionData === null || formattedDistributionData === void 0 ? void 0 : formattedDistributionData.length) > 0 && (formattedDistributionData[0].from_dp === null || formattedDistributionData[0].from_dp === undefined) && 21,\n                            \"Default returns location id\": (formattedDistributionData === null || formattedDistributionData === void 0 ? void 0 : formattedDistributionData.length) > 0 && (formattedDistributionData[0].from_dp === null || formattedDistributionData[0].from_dp === undefined) && 21,\n                            Address: \"\",\n                            \"Service supplier code\": \"\",\n                            \"EDI Reference Code\": \"\",\n                            \"EDI ANA Code\": \"\",\n                            \"User Integer 1\": (formattedDistributionData === null || formattedDistributionData === void 0 ? void 0 : formattedDistributionData.length) > 0 && (formattedDistributionData[0].from_dp === null || formattedDistributionData[0].from_dp === undefined) && 1,\n                            \"Movement resource group\": \"\",\n                            \"Handheld application used\": (formattedDistributionData === null || formattedDistributionData === void 0 ? void 0 : formattedDistributionData.length) > 0 && (formattedDistributionData[0].from_dp === null || formattedDistributionData[0].from_dp === undefined) && \"0\",\n                            \"Pallets in procure/receiving\": (formattedDistributionData === null || formattedDistributionData === void 0 ? void 0 : formattedDistributionData.length) > 0 && (formattedDistributionData[0].from_dp === null || formattedDistributionData[0].from_dp === undefined) && \"0\",\n                            \"Operational depo\": \"\",\n                            \"Enabled for masterfile sending\": \"\",\n                            \"Connected registed depot\": \"\",\n                            \"EDI Transmission type of depo\": \"\",\n                            \"Container loading depo\": (formattedDistributionData === null || formattedDistributionData === void 0 ? void 0 : formattedDistributionData.length) > 0 && (formattedDistributionData[0].from_dp === null || formattedDistributionData[0].from_dp === undefined) && \"0\",\n                            \"Airport depot\": \"\",\n                            \"Sms notification\": \"\",\n                            Port: \"\",\n                            Dormant: \"\",\n                            Active: (formattedDistributionData === null || formattedDistributionData === void 0 ? void 0 : formattedDistributionData.length) > 0 && (formattedDistributionData[0].from_dp === null || formattedDistributionData[0].from_dp === undefined) && 1,\n                            \"Ingredient distribution point\": \"\",\n                            \"Show in CE\": (formattedDistributionData === null || formattedDistributionData === void 0 ? void 0 : formattedDistributionData.length) > 0 && (formattedDistributionData[0].from_dp === null || formattedDistributionData[0].from_dp === undefined) && 1,\n                            \"Charge direction\": \"\",\n                            \"Pallet receive time\": \"\",\n                            \"User string 3\": \"\",\n                            \"Direct DP\": (formattedDistributionData === null || formattedDistributionData === void 0 ? void 0 : formattedDistributionData.length) > 0 && (formattedDistributionData[0].from_dp === null || formattedDistributionData[0].from_dp === undefined) ? ((_formattedDistributionData_ = formattedDistributionData[0]) === null || _formattedDistributionData_ === void 0 ? void 0 : _formattedDistributionData_.directDP) ? 1 : \"0\" : \"\",\n                            \"Include on XML\": (formattedDistributionData === null || formattedDistributionData === void 0 ? void 0 : formattedDistributionData.length) > 0 && (formattedDistributionData[0].from_dp === null || formattedDistributionData[0].from_dp === undefined) && \"0\"\n                        }\n                    ],\n                    [\n                        \"sendac (Supplier file)\",\n                        {\n                            \"Supplier Active\": (data === null || data === void 0 ? void 0 : data.isActive) ? 1 : 0,\n                            \"Haulage cube local\": \"\",\n                            \"Haulage cube name\": \"\",\n                            \"update guesstimates type\": 1,\n                            \"Organization ID\": \"\",\n                            \"Vat number 1\": data === null || data === void 0 ? void 0 : data.vat_number,\n                            \"Organic cert\": data === null || data === void 0 ? void 0 : data.organic_certificate_number,\n                            \"Regional cert\": regional_cert,\n                            \"Global gap number\": data === null || data === void 0 ? void 0 : data.global_gap_number,\n                            \"Enforce department\": \"\",\n                            \"Sendac Group\": (data === null || data === void 0 ? void 0 : data.supplier_group) ? JSON.parse(data.supplier_group)[0].value : \"\",\n                            \"Supplier code\": data === null || data === void 0 ? void 0 : (_data_prophets_1 = data.prophets[0]) === null || _data_prophets_1 === void 0 ? void 0 : (_data_prophets__prophet_code1 = _data_prophets_1.prophet_code) === null || _data_prophets__prophet_code1 === void 0 ? void 0 : _data_prophets__prophet_code1.trim(),\n                            \"Supplier name\": data.company_name,\n                            \"Supplier type\": data === null || data === void 0 ? void 0 : data.supplier_type,\n                            \"User Lookup 2\": \"\",\n                            \"Address Line 1\": data === null || data === void 0 ? void 0 : data.address_line_1,\n                            \"Address Line 2\": data === null || data === void 0 ? void 0 : data.address_line_2,\n                            \"Address Line 3\": data === null || data === void 0 ? void 0 : data.address_line_3,\n                            \"Address Line 4\": data === null || data === void 0 ? void 0 : data.address_line_4,\n                            \"Post code\": data === null || data === void 0 ? void 0 : data.postal_code,\n                            \"Country Code\": data === null || data === void 0 ? void 0 : data.country_code,\n                            \"Payee supplier code\": \"\",\n                            \"Invoice supplier\": \"\",\n                            \"Head office\": \"\",\n                            \"Settlement days\": data === null || data === void 0 ? void 0 : data.payment_terms,\n                            \"Bank general ledger code Currency number if bank\": currencyId,\n                            \"Currency number\": currencyId,\n                            \"Currency number Currency name\": currencyName,\n                            \"Bank general ledger code\": getGLCode(data === null || data === void 0 ? void 0 : data.internal_ledger_code, data === null || data === void 0 ? void 0 : (_data_prophets_2 = data.prophets[0]) === null || _data_prophets_2 === void 0 ? void 0 : _data_prophets_2.prophet_id, data === null || data === void 0 ? void 0 : data.currency_code, data === null || data === void 0 ? void 0 : data.roleIds),\n                            \"payment Type\": data === null || data === void 0 ? void 0 : data.payment_type,\n                            \"payment type name\": data === null || data === void 0 ? void 0 : data.payment_type_name,\n                            \"Country code\": data === null || data === void 0 ? void 0 : data.country_code,\n                            Vatable: (data === null || data === void 0 ? void 0 : data.vatable) != null ? (data === null || data === void 0 ? void 0 : data.vatable) ? \"1\" : \"0\" : \"0\",\n                            \"vatable desc\": (data === null || data === void 0 ? void 0 : data.vatable) != null ? (data === null || data === void 0 ? void 0 : data.vatable) ? \"Vatable\" : \"None vatable\" : \"None vatable\",\n                            \"Area Number\": (data === null || data === void 0 ? void 0 : data.prophets[0].prophet_id) == 3 || (data === null || data === void 0 ? void 0 : data.prophets[0].prophet_id) == 4 ? 1 : 7,\n                            Buyer: 1,\n                            \"Multiple Lot Indicator\": \"0\",\n                            \"multiple lot indicator desc\": \"By Lot\",\n                            \"Generate Pallet Loading Plan\": \"\",\n                            \"Distribution point for supplier\": 6,\n                            \"Payment terms\": \"\",\n                            \"Department Number\": (data === null || data === void 0 ? void 0 : (_data_prophets_3 = data.prophets[0]) === null || _data_prophets_3 === void 0 ? void 0 : _data_prophets_3.prophet_id) == 1 ? 1 : (data === null || data === void 0 ? void 0 : (_data_prophets_4 = data.prophets[0]) === null || _data_prophets_4 === void 0 ? void 0 : _data_prophets_4.prophet_id) == 2 ? 9 : (data === null || data === void 0 ? void 0 : data.prophets[0].prophet_id) == 3 ? 3 : (data === null || data === void 0 ? void 0 : data.prophets[0].prophet_id) == 4 ? 2 : \"\",\n                            \"Allow credit rebates\": \"\",\n                            \"Alternative DP for supplier\": 1,\n                            \"Actual posting stops purchase charges\": \"\",\n                            \"Authorise on register\": \"\",\n                            \"User text 1\": \"\",\n                            \"User lookup 1\": (data === null || data === void 0 ? void 0 : data.prophets[0].prophet_id) == 3 || (data === null || data === void 0 ? void 0 : data.prophets[0].prophet_id) == 4 ? getCorrespondingUserLookup(data === null || data === void 0 ? void 0 : data.currency_code) : \"\",\n                            \"Receive orders from edi\": \"\",\n                            \"Send invoices from edi\": \"\",\n                            \"send orders from edi\": \"\",\n                            \"EDI partner\": (data === null || data === void 0 ? void 0 : data.prophets[0].prophet_id) == 3 || (data === null || data === void 0 ? void 0 : data.prophets[0].prophet_id) == 4 ? 2000 : \"\",\n                            \"Generic code\": (data === null || data === void 0 ? void 0 : data.prophets[0].prophet_id) == 3 || (data === null || data === void 0 ? void 0 : data.prophets[0].prophet_id) == 4 ? \"STOCK\" : \"\",\n                            \"EDI ANA number\": (_data_edi = data === null || data === void 0 ? void 0 : data.edi) !== null && _data_edi !== void 0 ? _data_edi : \"N/A\",\n                            \"User % authorize rule\": 5,\n                            FromDP: (formattedDistributionData === null || formattedDistributionData === void 0 ? void 0 : formattedDistributionData.length) > 0 ? formattedDistributionData[0].from_dp || \"\" : \"\"\n                        }\n                    ],\n                    [\n                        \"sendacrole (Supplier role file)\"\n                    ],\n                    [\n                        \"sendacgroup (Sendac group file)\"\n                    ],\n                    [\n                        \"bankac (Bank account details table)\",\n                        {\n                            \"Supplier code\": data === null || data === void 0 ? void 0 : (_data_prophets_5 = data.prophets[0]) === null || _data_prophets_5 === void 0 ? void 0 : (_data_prophets__prophet_code2 = _data_prophets_5.prophet_code) === null || _data_prophets__prophet_code2 === void 0 ? void 0 : _data_prophets__prophet_code2.trim(),\n                            \"Record id\": \"\",\n                            \"Bank sort code\": sort_code,\n                            \"Account number\": account_number,\n                            \"Country code\": (data === null || data === void 0 ? void 0 : data.country_code) == \"UK\" ? \"GB\" : data === null || data === void 0 ? void 0 : data.country_code,\n                            \"Account holder\": data.company_name,\n                            \"Currency number\": currencyId,\n                            \"BACS currency\": data === null || data === void 0 ? void 0 : data.bacs_currency_code,\n                            \"Address Line 1\": \"\",\n                            \"Address Line 2\": \"\",\n                            \"BIC/Swift address\": swiftBicCode,\n                            \"Internation bank reference code\": iban,\n                            \"Account user id\": \"\",\n                            \"Post code\": \"\"\n                        }\n                    ],\n                    [\n                        \"senbnk (Supplier bank link table)\",\n                        {\n                            \"Supplier code\": (data === null || data === void 0 ? void 0 : (_data_prophets_6 = data.prophets[0]) === null || _data_prophets_6 === void 0 ? void 0 : _data_prophets_6.prophet_code) ? data === null || data === void 0 ? void 0 : (_data_prophets_7 = data.prophets[0]) === null || _data_prophets_7 === void 0 ? void 0 : (_data_prophets__prophet_code3 = _data_prophets_7.prophet_code) === null || _data_prophets__prophet_code3 === void 0 ? void 0 : _data_prophets__prophet_code3.trim() : \"\",\n                            Bankacid: \"\",\n                            \"Header bank record id\": \"\",\n                            \"Intermediary bank account id\": \"\",\n                            \"Intermediary bank account id Internation bank reference code\": \"\"\n                        }\n                    ],\n                    [\n                        \"contactdet (Supplier personnel contact details)\"\n                    ],\n                    [\n                        \"organization (Organization)\",\n                        {\n                            \"Organization ID\": \"\",\n                            \"Organization Name\": (data === null || data === void 0 ? void 0 : (_data_prophets_8 = data.prophets[0]) === null || _data_prophets_8 === void 0 ? void 0 : _data_prophets_8.prophet_code) ? data === null || data === void 0 ? void 0 : (_data_prophets_9 = data.prophets[0]) === null || _data_prophets_9 === void 0 ? void 0 : (_data_prophets__prophet_code4 = _data_prophets_9.prophet_code) === null || _data_prophets__prophet_code4 === void 0 ? void 0 : _data_prophets__prophet_code4.trim() : \"\"\n                        }\n                    ],\n                    [\n                        \"orgroles (Organization Roles)\",\n                        {\n                            \"Organization ID\": \"\",\n                            \"Organization Code\": (data === null || data === void 0 ? void 0 : (_data_prophets_10 = data.prophets[0]) === null || _data_prophets_10 === void 0 ? void 0 : _data_prophets_10.prophet_code) ? data === null || data === void 0 ? void 0 : (_data_prophets_11 = data.prophets[0]) === null || _data_prophets_11 === void 0 ? void 0 : (_data_prophets__prophet_code5 = _data_prophets_11.prophet_code) === null || _data_prophets__prophet_code5 === void 0 ? void 0 : _data_prophets__prophet_code5.trim() : \"\",\n                            \"Role Type ID\": \"\",\n                            Selected: \"\",\n                            \"Organisation ID\": \"\",\n                            \"role Type ID\": \"\",\n                            \"Contact ID\": \"\",\n                            \"Contact ID Email Address\": \"\",\n                            \"Contact ID Telephone\": \"\",\n                            \"Contact ID Fax\": \"\"\n                        }\n                    ],\n                    [\n                        \"sheetSuppliersId\",\n                        {\n                            id: data === null || data === void 0 ? void 0 : data.id,\n                            supplierName: formattedDistributionData === null || formattedDistributionData === void 0 ? void 0 : formattedDistributionData.company_name,\n                            isEmergencyAndFinanceNotComplete: data === null || data === void 0 ? void 0 : data.isEmergencyAndFinanceNotComplete,\n                            supplierCode: (data === null || data === void 0 ? void 0 : (_data_prophets_12 = data.prophets[0]) === null || _data_prophets_12 === void 0 ? void 0 : _data_prophets_12.prophet_code) ? data === null || data === void 0 ? void 0 : (_data_prophets_13 = data.prophets[0]) === null || _data_prophets_13 === void 0 ? void 0 : (_data_prophets__prophet_code6 = _data_prophets_13.prophet_code) === null || _data_prophets__prophet_code6 === void 0 ? void 0 : _data_prophets__prophet_code6.trim() : \"\"\n                        }\n                    ]\n                ];\n                const addDataToSheet = (sheetIndex, dataToAdd)=>{\n                    if (filteredInternalExportData[sheetIndex].length === 2) {\n                        filteredInternalExportData[sheetIndex].push(...dataToAdd);\n                    } else {\n                        filteredInternalExportData[sheetIndex] = [\n                            filteredInternalExportData[sheetIndex][0],\n                            ...dataToAdd\n                        ];\n                    }\n                };\n                // Extract contacts and add to the contacts sheet\n                const contacts = extractContacts(data === null || data === void 0 ? void 0 : data.contacts_json);\n                const extractedSendacGroup = extractSendacGroup(data.supplier_group, data === null || data === void 0 ? void 0 : data.id);\n                let sendacRoleOnRoleNums = multipleSendRoleOnRoleNums(data);\n                addDataToSheet(6, contacts);\n                addDataToSheet(2, sendacRoleOnRoleNums);\n                addDataToSheet(3, extractedSendacGroup);\n            }\n            let export_ISS_response;\n            if (rolesArray.includes(1) || rolesArray.includes(2) || rolesArray.includes(3) || rolesArray.includes(4)) {\n                var _data_prophets__prophet_code7, _data_prophets_14, _formattedDistributionData_1, _data_prophets__prophet_code8, _data_prophets_15, _data_prophets__prophet_code9, _data_prophets_16, _data_distribution_points_json1, _data_prophets_17, _data_prophets_18, _params_data1;\n                let sendacRoleOnRoleNums = multipleSendRoleOnRoleNums(data === null || data === void 0 ? void 0 : data.role_num);\n                var _data_edi1;\n                filteredISSExportData = [\n                    [\n                        \"UlpFil\",\n                        {\n                            \"Distribution point\": \"\",\n                            Description: (formattedDistributionData === null || formattedDistributionData === void 0 ? void 0 : formattedDistributionData.length) > 0 && (formattedDistributionData[0].from_dp === null || formattedDistributionData[0].from_dp === undefined) ? formattedDistributionData[0].distributionPoint : \"\",\n                            \"Service Supplier Code\": (formattedDistributionData === null || formattedDistributionData === void 0 ? void 0 : formattedDistributionData.length) > 0 && (formattedDistributionData[0].from_dp === null || formattedDistributionData[0].from_dp === undefined) && (data === null || data === void 0 ? void 0 : (_data_prophets_14 = data.prophets[0]) === null || _data_prophets_14 === void 0 ? void 0 : (_data_prophets__prophet_code7 = _data_prophets_14.prophet_code) === null || _data_prophets__prophet_code7 === void 0 ? void 0 : _data_prophets__prophet_code7.trim()),\n                            \"Default expected stock status\": (formattedDistributionData === null || formattedDistributionData === void 0 ? void 0 : formattedDistributionData.length) > 0 && (formattedDistributionData[0].from_dp === null || formattedDistributionData[0].from_dp === undefined) && \"0\",\n                            \"Default received stock status\": (formattedDistributionData === null || formattedDistributionData === void 0 ? void 0 : formattedDistributionData.length) > 0 && (formattedDistributionData[0].from_dp === null || formattedDistributionData[0].from_dp === undefined) && \"0\",\n                            \"Pallets in packhouse\": (formattedDistributionData === null || formattedDistributionData === void 0 ? void 0 : formattedDistributionData.length) > 0 && (formattedDistributionData[0].from_dp === null || formattedDistributionData[0].from_dp === undefined) && \"0\",\n                            \"Default haulier\": (formattedDistributionData === null || formattedDistributionData === void 0 ? void 0 : formattedDistributionData.length) > 0 && (formattedDistributionData[0].from_dp === null || formattedDistributionData[0].from_dp === undefined) && \"ZZZZZ\",\n                            \"Default expected location id\": (formattedDistributionData === null || formattedDistributionData === void 0 ? void 0 : formattedDistributionData.length) > 0 && (formattedDistributionData[0].from_dp === null || formattedDistributionData[0].from_dp === undefined) && 21,\n                            \"Default receiving location id\": (formattedDistributionData === null || formattedDistributionData === void 0 ? void 0 : formattedDistributionData.length) > 0 && (formattedDistributionData[0].from_dp === null || formattedDistributionData[0].from_dp === undefined) && 21,\n                            \"Packhouse location id\": (formattedDistributionData === null || formattedDistributionData === void 0 ? void 0 : formattedDistributionData.length) > 0 && (formattedDistributionData[0].from_dp === null || formattedDistributionData[0].from_dp === undefined) && 21,\n                            \"Default pick location id\": (formattedDistributionData === null || formattedDistributionData === void 0 ? void 0 : formattedDistributionData.length) > 0 && (formattedDistributionData[0].from_dp === null || formattedDistributionData[0].from_dp === undefined) && 21,\n                            \"Despatch location id\": (formattedDistributionData === null || formattedDistributionData === void 0 ? void 0 : formattedDistributionData.length) > 0 && (formattedDistributionData[0].from_dp === null || formattedDistributionData[0].from_dp === undefined) && 21,\n                            \"Default waste location id\": (formattedDistributionData === null || formattedDistributionData === void 0 ? void 0 : formattedDistributionData.length) > 0 && (formattedDistributionData[0].from_dp === null || formattedDistributionData[0].from_dp === undefined) && 21,\n                            \"Default pre-pick location id\": (formattedDistributionData === null || formattedDistributionData === void 0 ? void 0 : formattedDistributionData.length) > 0 && (formattedDistributionData[0].from_dp === null || formattedDistributionData[0].from_dp === undefined) && 21,\n                            \"Default returns location id\": (formattedDistributionData === null || formattedDistributionData === void 0 ? void 0 : formattedDistributionData.length) > 0 && (formattedDistributionData[0].from_dp === null || formattedDistributionData[0].from_dp === undefined) && 21,\n                            Address: \"\",\n                            \"Service supplier code\": \"\",\n                            \"EDI Reference Code\": \"\",\n                            \"EDI ANA Code\": \"\",\n                            \"User Integer 1\": (formattedDistributionData === null || formattedDistributionData === void 0 ? void 0 : formattedDistributionData.length) > 0 && (formattedDistributionData[0].from_dp === null || formattedDistributionData[0].from_dp === undefined) && 1,\n                            \"Movement resource group\": \"\",\n                            \"Handheld application used\": (formattedDistributionData === null || formattedDistributionData === void 0 ? void 0 : formattedDistributionData.length) > 0 && (formattedDistributionData[0].from_dp === null || formattedDistributionData[0].from_dp === undefined) && \"0\",\n                            \"Pallets in procure/receiving\": (formattedDistributionData === null || formattedDistributionData === void 0 ? void 0 : formattedDistributionData.length) > 0 && (formattedDistributionData[0].from_dp === null || formattedDistributionData[0].from_dp === undefined) && \"0\",\n                            \"Operational depo\": \"\",\n                            \"Enabled for masterfile sending\": \"\",\n                            \"Connected registed depot\": \"\",\n                            \"EDI Transmission type of depo\": \"\",\n                            \"Container loading depo\": (formattedDistributionData === null || formattedDistributionData === void 0 ? void 0 : formattedDistributionData.length) > 0 && (formattedDistributionData[0].from_dp === null || formattedDistributionData[0].from_dp === undefined) && \"0\",\n                            \"Airport depot\": \"\",\n                            \"Sms notification\": \"\",\n                            Port: \"\",\n                            Dormant: \"\",\n                            Active: (formattedDistributionData === null || formattedDistributionData === void 0 ? void 0 : formattedDistributionData.length) > 0 && (formattedDistributionData[0].from_dp === null || formattedDistributionData[0].from_dp === undefined) && 1,\n                            \"Ingredient distribution point\": \"\",\n                            \"Show in CE\": (formattedDistributionData === null || formattedDistributionData === void 0 ? void 0 : formattedDistributionData.length) > 0 && (formattedDistributionData[0].from_dp === null || formattedDistributionData[0].from_dp === undefined) && 1,\n                            \"Charge direction\": \"\",\n                            \"Pallet receive time\": \"\",\n                            \"User string 3\": \"\",\n                            \"Direct DP\": (formattedDistributionData === null || formattedDistributionData === void 0 ? void 0 : formattedDistributionData.length) > 0 && (formattedDistributionData[0].from_dp === null || formattedDistributionData[0].from_dp === undefined) ? ((_formattedDistributionData_1 = formattedDistributionData[0]) === null || _formattedDistributionData_1 === void 0 ? void 0 : _formattedDistributionData_1.directDP) ? 1 : \"0\" : \"\",\n                            \"Include on XML\": (formattedDistributionData === null || formattedDistributionData === void 0 ? void 0 : formattedDistributionData.length) > 0 && (formattedDistributionData[0].from_dp === null || formattedDistributionData[0].from_dp === undefined) && \"0\"\n                        }\n                    ],\n                    [\n                        \"Supplier data\",\n                        {\n                            \"Supplier Active\": \"N/A\",\n                            \"Supplier code\": data === null || data === void 0 ? void 0 : (_data_prophets_15 = data.prophets[0]) === null || _data_prophets_15 === void 0 ? void 0 : (_data_prophets__prophet_code8 = _data_prophets_15.prophet_code) === null || _data_prophets__prophet_code8 === void 0 ? void 0 : _data_prophets__prophet_code8.trim(),\n                            \"EDI Partner\": \"N/A\",\n                            \"Supplier name\": data.company_name,\n                            \"EDI ANA number\": (_data_edi1 = data === null || data === void 0 ? void 0 : data.edi) !== null && _data_edi1 !== void 0 ? _data_edi1 : \"N/A\",\n                            \"Producer (supplier)\": \"N/A\",\n                            \"Department number\": \"N/A\",\n                            \"Currency number\": (data === null || data === void 0 ? void 0 : data.currency_id) ? data === null || data === void 0 ? void 0 : data.currency_id : 1,\n                            \"Global gap number\": data === null || data === void 0 ? void 0 : data.global_gap_number,\n                            \"Grower group\": \"N/A\",\n                            \"Defra county number\": \"N/A\",\n                            \"Date start\": \"N/A\",\n                            \"Date end\": \"N/A\",\n                            \"Organic cert\": data === null || data === void 0 ? void 0 : data.organic_certificate_number,\n                            \"Regional cert\": data === null || data === void 0 ? void 0 : data.chile_certificate_number,\n                            \"Head office\": data === null || data === void 0 ? void 0 : (_data_prophets_16 = data.prophets[0]) === null || _data_prophets_16 === void 0 ? void 0 : (_data_prophets__prophet_code9 = _data_prophets_16.prophet_code) === null || _data_prophets__prophet_code9 === void 0 ? void 0 : _data_prophets__prophet_code9.trim(),\n                            \"Country Code\": data === null || data === void 0 ? void 0 : data.country_code,\n                            \"Distribution point for supplier\": (data === null || data === void 0 ? void 0 : (_data_distribution_points_json1 = data.distribution_points_json) === null || _data_distribution_points_json1 === void 0 ? void 0 : _data_distribution_points_json1.length) > 0 ? data === null || data === void 0 ? void 0 : data.distribution_points_json[0].from_dp : \"N/A\",\n                            \"Bool 2\": \"N/A\",\n                            \"Bool 3\": \"N/A\",\n                            \"Address line 1\": data === null || data === void 0 ? void 0 : data.address_line_1,\n                            \"Address line 2\": data === null || data === void 0 ? void 0 : data.address_line_2,\n                            \"Address line 3\": data === null || data === void 0 ? void 0 : data.address_line_3,\n                            \"Address line 4\": data === null || data === void 0 ? void 0 : data.address_line_4,\n                            \"Post code\": data === null || data === void 0 ? void 0 : data.postal_code,\n                            \"Currency Number\": (data === null || data === void 0 ? void 0 : data.currency_id) ? data === null || data === void 0 ? void 0 : data.currency_id : 1,\n                            \"Bank general ledger code\": (data === null || data === void 0 ? void 0 : data.iss_ledger_code) ? data === null || data === void 0 ? void 0 : data.iss_ledger_code : \"12200\",\n                            \"Bank general ledger code Currency number if bank\": (data === null || data === void 0 ? void 0 : data.currency_id) ? data === null || data === void 0 ? void 0 : data.currency_id : 1,\n                            \"Settlement days\": data === null || data === void 0 ? void 0 : data.payment_terms,\n                            \"Department Number\": (data === null || data === void 0 ? void 0 : (_data_prophets_17 = data.prophets[0]) === null || _data_prophets_17 === void 0 ? void 0 : _data_prophets_17.prophet_id) == 1 ? 1 : (data === null || data === void 0 ? void 0 : (_data_prophets_18 = data.prophets[0]) === null || _data_prophets_18 === void 0 ? void 0 : _data_prophets_18.prophet_id) == 2 ? 9 : (data === null || data === void 0 ? void 0 : data.prophets[0].prophet_id) == 3 ? 3 : (data === null || data === void 0 ? void 0 : data.prophets[0].prophet_id) == 4 ? 2 : \"N/A\",\n                            \"Area Number\": \"1\",\n                            Vatable: (data === null || data === void 0 ? void 0 : data.vatable) != null ? (data === null || data === void 0 ? void 0 : data.vatable) ? \"1\" : \"0\" : \"0\",\n                            Buyer: \"1\",\n                            \"Billing type\": \"0\",\n                            \"Payment type\": (data === null || data === void 0 ? void 0 : data.payment_type) ? data === null || data === void 0 ? void 0 : data.payment_type : 2,\n                            \"Expense general ledger code\": \"N/A\",\n                            \"Authorise on register\": \"N/A\",\n                            \"Use % authorise rule\": 5,\n                            \"User text 1\": \"N/A\",\n                            \"Mandatory altfil on service jobs\": \"N/A\",\n                            \"Organization ID\": \"N/A\",\n                            FromDP: (formattedDistributionData === null || formattedDistributionData === void 0 ? void 0 : formattedDistributionData.length) > 0 ? formattedDistributionData[0].from_dp || \"\" : \"\",\n                            id: data === null || data === void 0 ? void 0 : data.id,\n                            isEmergencyAndFinanceNotComplete: data === null || data === void 0 ? void 0 : data.isEmergencyAndFinanceNotComplete\n                        }\n                    ],\n                    [\n                        \"sendacrole (Supplier role file)\"\n                    ]\n                ];\n                const addSendacRoleDataToSheet = (sheetIndex, dataToAdd)=>{\n                    if (filteredISSExportData[sheetIndex].length === 1) {\n                        filteredISSExportData[sheetIndex].push(...dataToAdd);\n                    } else {\n                        filteredISSExportData[sheetIndex] = [\n                            filteredISSExportData[sheetIndex][0],\n                            ...dataToAdd\n                        ];\n                    }\n                };\n                addSendacRoleDataToSheet(2, sendacRoleOnRoleNums);\n                export_ISS_response = await (0,_exportExcel__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(filteredISSExportData, false, token, company, userData, prophet_id, (_params_data1 = params.data) === null || _params_data1 === void 0 ? void 0 : _params_data1.requestor_email, \"\");\n            } else {\n                export_ISS_response = \"Not sent\";\n            }\n            const exportInternal_response = await (0,_exportExcel__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(filteredInternalExportData, true, token, company, userData, prophet_id, (_params_data = params.data) === null || _params_data === void 0 ? void 0 : _params_data.requestor_email, \"\");\n            setEmailStatusPopup(true);\n            if (export_ISS_response && exportInternal_response && export_ISS_response != \"Not sent\") {\n                setPopUpMessage(\"Email successfully sent to both Finance Department and ISS Admin Team\");\n                setISSExportSuccess(true);\n                setInternalExportSuccess(true);\n            } else if (export_ISS_response && export_ISS_response != \"Not sent\") {\n                setPopUpMessage(\"Email sent to ISS Admin Team , but not to Finance Department\");\n                setInternalExportSuccess(true);\n            } else if (exportInternal_response && export_ISS_response != \"Not sent\") {\n                setISSExportSuccess(true);\n                setPopUpMessage(\"Email sent to ISS Admin Team, but not to Finance Department\");\n            } else if (exportInternal_response && export_ISS_response == \"Not sent\") {\n                setPopUpMessage(\"Email sent to Finance Department , but not to ISS Admin as only Haulier or Expense role not allowed to export on ISS\");\n                setInternalExportSuccess(true);\n            } else {\n                setPopUpMessage(\"Email not sent to either Finance Department or ISS Admin Team\");\n            }\n        } else {\n            var _data_roleIds4;\n            if (params.data.isEmergencyRequest && (params.data.General === \"Incomplete\" || params.data.General == \"Not Entered\")) {\n                react_toastify__WEBPACK_IMPORTED_MODULE_8__.toast.error(\"General section needs to complete\", {\n                    position: \"top-right\",\n                    autoClose: 3000,\n                    hideProgressBar: false,\n                    closeOnClick: true,\n                    pauseOnHover: false,\n                    draggable: true,\n                    progress: undefined,\n                    theme: \"light\"\n                });\n                return;\n            } else if (params.data.isEmergencyRequest && ((data === null || data === void 0 ? void 0 : data.roleIds.includes(1)) || (data === null || data === void 0 ? void 0 : (_data_roleIds4 = data.roleIds) === null || _data_roleIds4 === void 0 ? void 0 : _data_roleIds4.includes(6))) && !params.data.currency_id) {\n                react_toastify__WEBPACK_IMPORTED_MODULE_8__.toast.error(\"Please select a currency and a valid supplier code to export\", {\n                    position: \"top-right\",\n                    autoClose: 3000,\n                    hideProgressBar: false,\n                    closeOnClick: true,\n                    pauseOnHover: false,\n                    draggable: true,\n                    progress: undefined,\n                    theme: \"light\"\n                });\n                return;\n            } else {\n                react_toastify__WEBPACK_IMPORTED_MODULE_8__.toast.error(\"Supplier details are incomplete or not confirmed.\", {\n                    position: \"top-right\",\n                    autoClose: 3000,\n                    hideProgressBar: false,\n                    closeOnClick: true,\n                    pauseOnHover: false,\n                    draggable: true,\n                    progress: undefined,\n                    theme: \"light\"\n                });\n                return;\n            }\n        }\n        setSelectedExportType(\"\");\n    };\n    const handleCopySupplier = ()=>{\n        setIsOpen(true);\n    };\n    if (isOpen) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_CopySupplier__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n            isOpen: true,\n            setIsOpen: setIsOpen,\n            supplierData: data,\n            userData: userData\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\utils\\\\renderer\\\\actionRenderer.js\",\n            lineNumber: 1122,\n            columnNumber: 7\n        }, undefined);\n    }\n    const disabledClass = \"text-gray-500 cursor-not-allowed\";\n    const handleExportType = (e)=>{\n        setSelectedExportType(e.target.value);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex flex-row gap-4 justify-center text-blue-500\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: ()=>status != \"Exported\" && status != \"Cancelled\" ? editSupplier() : confirmPage(),\n                        children: status == \"Exported\" || status == \"Cancelled\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fortawesome_react_fontawesome__WEBPACK_IMPORTED_MODULE_1__.FontAwesomeIcon, {\n                            icon: _fortawesome_free_solid_svg_icons__WEBPACK_IMPORTED_MODULE_14__.faEye,\n                            size: \"lg\",\n                            title: \"View Supplier\",\n                            className: \"text-skin-primary\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\utils\\\\renderer\\\\actionRenderer.js\",\n                            lineNumber: 1151,\n                            columnNumber: 13\n                        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fortawesome_react_fontawesome__WEBPACK_IMPORTED_MODULE_1__.FontAwesomeIcon, {\n                            icon: _fortawesome_free_solid_svg_icons__WEBPACK_IMPORTED_MODULE_14__.faPenToSquare,\n                            size: \"lg\",\n                            title: \"Edit Supplier\",\n                            className: \"text-skin-primary\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\utils\\\\renderer\\\\actionRenderer.js\",\n                            lineNumber: 1158,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\utils\\\\renderer\\\\actionRenderer.js\",\n                        lineNumber: 1143,\n                        columnNumber: 9\n                    }, undefined),\n                    status != \"Cancelled\" && canExport && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: ()=>openOptionModal(supplier_id),\n                        title: \"Export Supplier\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fortawesome_react_fontawesome__WEBPACK_IMPORTED_MODULE_1__.FontAwesomeIcon, {\n                            icon: _fortawesome_free_solid_svg_icons__WEBPACK_IMPORTED_MODULE_14__.faFileExport,\n                            size: \"lg\",\n                            className: \"text-skin-primary\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\utils\\\\renderer\\\\actionRenderer.js\",\n                            lineNumber: 1171,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\utils\\\\renderer\\\\actionRenderer.js\",\n                        lineNumber: 1167,\n                        columnNumber: 11\n                    }, undefined),\n                    status != \"Cancelled\" && status != \"Exported\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: cancelProduct,\n                        title: \"Cancel Product\",\n                        className: \"flex items-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fortawesome_react_fontawesome__WEBPACK_IMPORTED_MODULE_1__.FontAwesomeIcon, {\n                            icon: _fortawesome_free_solid_svg_icons__WEBPACK_IMPORTED_MODULE_14__.faXmark,\n                            size: \"sm\",\n                            className: \"border rounded-sm border-skin-primary text-skin-primary m-0 w-[15px] h-[15px]\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\utils\\\\renderer\\\\actionRenderer.js\",\n                            lineNumber: 1184,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\utils\\\\renderer\\\\actionRenderer.js\",\n                        lineNumber: 1179,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\utils\\\\renderer\\\\actionRenderer.js\",\n                lineNumber: 1139,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Dialog_Transition_headlessui_react__WEBPACK_IMPORTED_MODULE_15__.Transition, {\n                appear: true,\n                show: isCancelOpen,\n                as: react__WEBPACK_IMPORTED_MODULE_5__.Fragment,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Dialog_Transition_headlessui_react__WEBPACK_IMPORTED_MODULE_15__.Dialog, {\n                    as: \"div\",\n                    className: \"relative z-10\",\n                    onClose: closeCancelModal,\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Dialog_Transition_headlessui_react__WEBPACK_IMPORTED_MODULE_15__.Transition.Child, {\n                            as: react__WEBPACK_IMPORTED_MODULE_5__.Fragment,\n                            enter: \"ease-out duration-300\",\n                            enterFrom: \"opacity-0\",\n                            enterTo: \"opacity-100\",\n                            leave: \"ease-in duration-200\",\n                            leaveFrom: \"opacity-100\",\n                            leaveTo: \"opacity-0\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"fixed inset-0 bg-black bg-opacity-25 backdrop-blur-sm\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\utils\\\\renderer\\\\actionRenderer.js\",\n                                lineNumber: 1204,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\utils\\\\renderer\\\\actionRenderer.js\",\n                            lineNumber: 1195,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"fixed inset-0 overflow-y-auto\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-center min-h-full p-4 text-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Dialog_Transition_headlessui_react__WEBPACK_IMPORTED_MODULE_15__.Transition.Child, {\n                                    as: react__WEBPACK_IMPORTED_MODULE_5__.Fragment,\n                                    enter: \"ease-out duration-300\",\n                                    enterFrom: \"opacity-0 scale-95\",\n                                    enterTo: \"opacity-100 scale-100\",\n                                    leave: \"ease-in duration-200\",\n                                    leaveFrom: \"opacity-100 scale-100\",\n                                    leaveTo: \"opacity-0 scale-95\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Dialog_Transition_headlessui_react__WEBPACK_IMPORTED_MODULE_15__.Dialog.Panel, {\n                                        className: \" w-[45%] transform overflow-hidden rounded-xl bg-white  text-left align-middle shadow-xl transition-all\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative bg-white rounded-lg shadow\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-start justify-between p-8 rounded-t\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"flex flex-row text-xl font-semibold text-gray-900  items-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"flex justify-center items-center border border-skin-primary text-skin-primary w-[25px] h-[25px] text-center leading-5 rounded-full text-base me-4\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fortawesome_react_fontawesome__WEBPACK_IMPORTED_MODULE_1__.FontAwesomeIcon, {\n                                                                            icon: _fortawesome_free_solid_svg_icons__WEBPACK_IMPORTED_MODULE_14__.faInfo\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\utils\\\\renderer\\\\actionRenderer.js\",\n                                                                            lineNumber: 1225,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        \" \"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\utils\\\\renderer\\\\actionRenderer.js\",\n                                                                    lineNumber: 1224,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                \" \",\n                                                                \"Warning\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\utils\\\\renderer\\\\actionRenderer.js\",\n                                                            lineNumber: 1223,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: closeCancelModal,\n                                                            type: \"button\",\n                                                            className: \"text-black bg-transparent hover:bg-gray-200 hover:text-gray-900 rounded-lg text-xl w-8 h-8 ml-auto inline-flex justify-center items-center \",\n                                                            \"data-modal-hide\": \"default-modal\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fortawesome_react_fontawesome__WEBPACK_IMPORTED_MODULE_1__.FontAwesomeIcon, {\n                                                                    icon: _fortawesome_free_solid_svg_icons__WEBPACK_IMPORTED_MODULE_14__.faXmark,\n                                                                    className: \"text-skin-primary\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\utils\\\\renderer\\\\actionRenderer.js\",\n                                                                    lineNumber: 1235,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                \" \"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\utils\\\\renderer\\\\actionRenderer.js\",\n                                                            lineNumber: 1229,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\utils\\\\renderer\\\\actionRenderer.js\",\n                                                    lineNumber: 1222,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"p-8 py-0 space-y-6\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-base xl:text-md 2xl:text-lg leading-relaxed mt-2\",\n                                                        children: \"Are you sure you want to cancel supplier?\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\utils\\\\renderer\\\\actionRenderer.js\",\n                                                        lineNumber: 1243,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\utils\\\\renderer\\\\actionRenderer.js\",\n                                                    lineNumber: 1242,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-end p-6 space-x-2 justify-end\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: closeCancelModal,\n                                                            \"data-modal-hide\": \"default-modal\",\n                                                            type: \"button\",\n                                                            className: \"border border-skin-primary text-skin-primary focus:ring-4 focus:outline-none focus:ring-blue-300 font-medium rounded-md text-md px-6 py-2 text-center \",\n                                                            children: \"No\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\utils\\\\renderer\\\\actionRenderer.js\",\n                                                            lineNumber: 1249,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: saveModalData,\n                                                            \"data-modal-hide\": \"default-modal\",\n                                                            type: \"button\",\n                                                            className: \"text-white bg-red-500 hover:bg-red-500 focus:ring-4 focus:outline-none focus:ring-blue-300 font-medium rounded-md text-md px-6 py-2 text-center \",\n                                                            children: \"Yes\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\utils\\\\renderer\\\\actionRenderer.js\",\n                                                            lineNumber: 1257,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\utils\\\\renderer\\\\actionRenderer.js\",\n                                                    lineNumber: 1248,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\utils\\\\renderer\\\\actionRenderer.js\",\n                                            lineNumber: 1220,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\utils\\\\renderer\\\\actionRenderer.js\",\n                                        lineNumber: 1218,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\utils\\\\renderer\\\\actionRenderer.js\",\n                                    lineNumber: 1209,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\utils\\\\renderer\\\\actionRenderer.js\",\n                                lineNumber: 1208,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\utils\\\\renderer\\\\actionRenderer.js\",\n                            lineNumber: 1207,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\utils\\\\renderer\\\\actionRenderer.js\",\n                    lineNumber: 1194,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\utils\\\\renderer\\\\actionRenderer.js\",\n                lineNumber: 1193,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Dialog_Transition_headlessui_react__WEBPACK_IMPORTED_MODULE_15__.Transition, {\n                appear: true,\n                show: emailStatusPopup,\n                as: react__WEBPACK_IMPORTED_MODULE_5__.Fragment,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Dialog_Transition_headlessui_react__WEBPACK_IMPORTED_MODULE_15__.Dialog, {\n                    as: \"div\",\n                    className: \"relative z-10\",\n                    onClose: closeEmailPopup,\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Dialog_Transition_headlessui_react__WEBPACK_IMPORTED_MODULE_15__.Transition.Child, {\n                            as: react__WEBPACK_IMPORTED_MODULE_5__.Fragment,\n                            enter: \"ease-out duration-300\",\n                            enterFrom: \"opacity-0\",\n                            enterTo: \"opacity-100\",\n                            leave: \"ease-in duration-200\",\n                            leaveFrom: \"opacity-100\",\n                            leaveTo: \"opacity-0\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"fixed inset-0 bg-black bg-opacity-25 backdrop-blur-sm\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\utils\\\\renderer\\\\actionRenderer.js\",\n                                lineNumber: 1284,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\utils\\\\renderer\\\\actionRenderer.js\",\n                            lineNumber: 1275,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"fixed inset-0 overflow-y-auto\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-center min-h-full p-4 text-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Dialog_Transition_headlessui_react__WEBPACK_IMPORTED_MODULE_15__.Transition.Child, {\n                                    as: react__WEBPACK_IMPORTED_MODULE_5__.Fragment,\n                                    enter: \"ease-out duration-300\",\n                                    enterFrom: \"opacity-0 scale-95\",\n                                    enterTo: \"opacity-100 scale-100\",\n                                    leave: \"ease-in duration-200\",\n                                    leaveFrom: \"opacity-100 scale-100\",\n                                    leaveTo: \"opacity-0 scale-95\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Dialog_Transition_headlessui_react__WEBPACK_IMPORTED_MODULE_15__.Dialog.Panel, {\n                                        className: \" w-[45%] transform overflow-hidden rounded-xl bg-white  text-left align-middle shadow-xl transition-all\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative bg-white rounded-lg shadow\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-start justify-between p-8 rounded-t\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"flex flex-row text-xl font-semibold text-gray-900  items-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"flex justify-center items-center border border-skin-primary text-skin-primary w-[25px] h-[25px] text-center leading-5 rounded-full text-base me-4\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fortawesome_react_fontawesome__WEBPACK_IMPORTED_MODULE_1__.FontAwesomeIcon, {\n                                                                            icon: _fortawesome_free_solid_svg_icons__WEBPACK_IMPORTED_MODULE_14__.faInfo\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\utils\\\\renderer\\\\actionRenderer.js\",\n                                                                            lineNumber: 1305,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        \" \"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\utils\\\\renderer\\\\actionRenderer.js\",\n                                                                    lineNumber: 1304,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                \" \",\n                                                                \"Status Message\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\utils\\\\renderer\\\\actionRenderer.js\",\n                                                            lineNumber: 1303,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: closeEmailPopup,\n                                                            type: \"button\",\n                                                            className: \"text-black bg-transparent hover:bg-gray-200 hover:text-gray-900 rounded-lg text-xl w-8 h-8 ml-auto inline-flex justify-center items-center \",\n                                                            \"data-modal-hide\": \"default-modal\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fortawesome_react_fontawesome__WEBPACK_IMPORTED_MODULE_1__.FontAwesomeIcon, {\n                                                                    icon: _fortawesome_free_solid_svg_icons__WEBPACK_IMPORTED_MODULE_14__.faXmark,\n                                                                    className: \"text-skin-primary\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\utils\\\\renderer\\\\actionRenderer.js\",\n                                                                    lineNumber: 1315,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                \" \"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\utils\\\\renderer\\\\actionRenderer.js\",\n                                                            lineNumber: 1309,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\utils\\\\renderer\\\\actionRenderer.js\",\n                                                    lineNumber: 1302,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"p-8 py-0 space-y-6\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-base xl:text-md 2xl:text-lg leading-relaxed mt-2\",\n                                                        children: popupMessage\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\utils\\\\renderer\\\\actionRenderer.js\",\n                                                        lineNumber: 1323,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\utils\\\\renderer\\\\actionRenderer.js\",\n                                                    lineNumber: 1322,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-end p-6 space-x-2 justify-end\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: closeEmailPopup,\n                                                        \"data-modal-hide\": \"default-modal\",\n                                                        type: \"button\",\n                                                        className: \"border text-dark-gray focus:ring-4 focus:outline-none focus:ring-blue-300 font-medium rounded-md text-md px-6 py-2 text-center \",\n                                                        children: \"Ok\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\utils\\\\renderer\\\\actionRenderer.js\",\n                                                        lineNumber: 1329,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\utils\\\\renderer\\\\actionRenderer.js\",\n                                                    lineNumber: 1328,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\utils\\\\renderer\\\\actionRenderer.js\",\n                                            lineNumber: 1300,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\utils\\\\renderer\\\\actionRenderer.js\",\n                                        lineNumber: 1298,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\utils\\\\renderer\\\\actionRenderer.js\",\n                                    lineNumber: 1289,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\utils\\\\renderer\\\\actionRenderer.js\",\n                                lineNumber: 1288,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\utils\\\\renderer\\\\actionRenderer.js\",\n                            lineNumber: 1287,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\utils\\\\renderer\\\\actionRenderer.js\",\n                    lineNumber: 1274,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\utils\\\\renderer\\\\actionRenderer.js\",\n                lineNumber: 1273,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true);\n};\n_s(actionRenderer, \"oYr84Owku3ZZ/XUqh43OyVW+Os0=\", false, function() {\n    return [\n        next_router__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        _utils_rolePermissionsContext__WEBPACK_IMPORTED_MODULE_13__.usePermissions,\n        _utils_userContext__WEBPACK_IMPORTED_MODULE_10__.useUser\n    ];\n});\n/* harmony default export */ __webpack_exports__[\"default\"] = (actionRenderer);\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevExports = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevExports) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports on update so we can compare the boundary\n                // signatures.\n                module.hot.dispose(function (data) {\n                    data.prevExports = currentExports;\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevExports !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevExports, currentExports)) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevExports !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./utils/renderer/actionRenderer.js\n"));

/***/ })

});