"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/service_level",{

/***/ "./components/service_level/ViewDetails.jsx":
/*!**************************************************!*\
  !*** ./components/service_level/ViewDetails.jsx ***!
  \**************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _fluentui_react_components__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @fluentui/react-components */ \"./node_modules/@fluentui/react-components/lib/index.js\");\n/* harmony import */ var _AuditDetails__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./AuditDetails */ \"./components/service_level/AuditDetails.jsx\");\n/* harmony import */ var _services_apiConfig__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/services/apiConfig */ \"./services/apiConfig.js\");\n/* harmony import */ var _DeleteReasonPopover__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./DeleteReasonPopover */ \"./components/service_level/DeleteReasonPopover.jsx\");\n/* harmony import */ var _utils_whatif_utils_getFormattedDate__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/utils/whatif/utils/getFormattedDate */ \"./utils/whatif/utils/getFormattedDate.js\");\n/* harmony import */ var react_toastify__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! react-toastify */ \"./node_modules/react-toastify/dist/react-toastify.esm.mjs\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_7__);\n/* harmony import */ var _utils_secureStorage__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/utils/secureStorage */ \"./utils/secureStorage.js\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\nconst ViewDetails = (param)=>{\n    let { data, setData, setMapForReasonsParentsAndTheirCorrespondingChildren, reasonsMasterList, parentReasonList, reasonsData, fetchReasonData, userData, isOpen, setIsOpen, isBulkUpdate, bulkUpdateData, setReasonsData, setAllSelectedProducts, setBulkUpdateData, setIsHeaderChecked, setSelectedRows } = param;\n    _s();\n    const [lockedBy, setLockedBy] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isBeingEdited, setIsBeingEdited] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [selectedReasonDropdownValue, setSelectedReasonDropdownValue] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [subReasonsList, setSubReasonsList] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedSubReasonDropdownValue, setSelectedSubReasonDropdownValue] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [quantity, setQuantity] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [comment, setComment] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [onEditQuantityValue, setOnEditQuantityValue] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [onEditSelectedReason, setOnEditSelectedReason] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [onEditSelectedSubReason, setOnEditSelectedSubReason] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [onEditComment, setOnEditComment] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [deleteId, setDeleteId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [deleteReason, setDeleteReason] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [isDeleteTrue, setIsDeleteTrue] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isValidQuantity, setIsValidQuantity] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [isValidReason, setIsValidReasons] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [isValidSubReason, setIsValidSubReasons] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [isValidEditQuantity, setIsValidEditQuantity] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [isValidEditReason, setIsValidEditReasons] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [isValidEditSubReason, setIsValidEditSubReasons] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [invalidEditQuantityId, setInvalidEditQuantityId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [invalidEditReasonsId, setInvalidEditReasonsId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [invalidEditSubReasonsId, setInvalidEditSubReasonsId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [originalEditQuantity, setOriginalEditQuantity] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [isAuditDetailsOpen, setIsAuditDetailsOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isValidComment, setIsValidComment] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [isOtherSelected, setIsOtherSelected] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [currentData, setCurrentData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(data);\n    const [currentCustomers, setCurrentCustomers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [orderId, setOrderId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isSaveButtonDisabled, setIsSaveButtonDisabled] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_7__.useRouter)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        setMapForReasonsParentsAndTheirCorrespondingChildren();\n    }, [\n        reasonsMasterList\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const ids = data.map((item)=>item.ORD_ID);\n        const customers = data.map((item)=>item.CUSTOMER);\n        setOrderId(ids);\n        setCurrentData(data);\n        setCurrentCustomers(customers);\n        return ()=>{\n            setOrderId([]);\n        };\n    }, []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (orderId.length > 0) {\n            fetchReasonData(orderId, currentCustomers);\n        }\n    }, [\n        orderId[0],\n        currentCustomers[0]\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (currentData.length > 0) {\n            setLockedBy(currentData[0].LOCKED_BY);\n        }\n    }, [\n        currentData\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        setLockedBy(data[0].LOCKED_BY);\n    }, [\n        data[0].LOCKED_BY\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (isDeleteTrue) {\n            handleDeleteReason(orderId);\n            setIsDeleteTrue(false);\n        }\n    }, [\n        isDeleteTrue\n    ]);\n    const saveReasons = ()=>{\n        setIsSaveButtonDisabled(true);\n        if (isBulkUpdate) {\n            let totalAddedReasons = bulkUpdateData.totalCasesDifferent;\n            setBulkUpdateData((prev)=>{\n                return {\n                    ...prev,\n                    totalCasesDifferent: 0,\n                    totalCasesAddedReasons: totalAddedReasons\n                };\n            });\n        }\n        const saveData = currentData.map((item)=>{\n            var _reasonsMasterList_filter_, _reasonsMasterList_filter_1;\n            return {\n                quantity: isBulkUpdate ? item.CASES_DIFFERENCE : typeof quantity === \"string\" ? quantity.trim() : quantity,\n                reasons: selectedReasonDropdownValue,\n                reasonsLabel: (_reasonsMasterList_filter_ = reasonsMasterList.filter((r)=>r.id == +selectedReasonDropdownValue)[0]) === null || _reasonsMasterList_filter_ === void 0 ? void 0 : _reasonsMasterList_filter_.reason,\n                subReason: selectedSubReasonDropdownValue,\n                subReasonLabel: (_reasonsMasterList_filter_1 = reasonsMasterList.filter((r)=>r.id === selectedSubReasonDropdownValue)[0]) === null || _reasonsMasterList_filter_1 === void 0 ? void 0 : _reasonsMasterList_filter_1.reason,\n                comment: comment.trim(),\n                orderId: item.ORD_ID,\n                addedBy: userData.email,\n                addedByName: userData.name,\n                custCode: item.CUSTOMER\n            };\n        });\n        const isValid = saveData.every((item)=>{\n            const isQuantityValid = item.quantity && item.quantity !== \"\" && item.quantiy !== 0;\n            const isReasonValid = item.reasons && item !== \"\";\n            const isSubReasonValid = item.subReason && item.subReason !== \"\";\n            if (selectedReasonDropdownValue === \"30\" && !item.comment) {\n                setIsValidComment(false);\n                return;\n            } else {\n                setIsValidComment(true);\n            }\n            // Set individual validation states\n            if (!isQuantityValid) {\n                alert(\"not valid\");\n                setIsValidQuantity(false);\n            }\n            if (!isReasonValid) {\n                setIsValidReasons(false);\n            }\n            if (!isSubReasonValid) {\n                setIsValidSubReasons(false);\n            }\n            return isQuantityValid && isReasonValid && isSubReasonValid;\n        });\n        // If any of the items are invalid, set the overall validation states\n        if (!isValid) {\n            return; // Exit if any validation fails\n        }\n        setIsValidQuantity(true);\n        setIsValidReasons(true);\n        setIsValidSubReasons(true);\n        try {\n            const serverAddress = _services_apiConfig__WEBPACK_IMPORTED_MODULE_3__.apiConfig.serverAddress;\n            fetch(\"\".concat(serverAddress, \"servicelevel/add-new-reason\"), {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                credentials: \"include\",\n                body: JSON.stringify(saveData)\n            }).then((res)=>{\n                if (res.status == 401) {\n                    react_toastify__WEBPACK_IMPORTED_MODULE_6__.toast.error(\"Your session has expired. Please log in again.\");\n                    setTimeout(async ()=>{\n                        await (0,_utils_secureStorage__WEBPACK_IMPORTED_MODULE_8__.logout)();\n                        const redirectUrl = \"/login?redirect=\".concat(encodeURIComponent(window.location.pathname));\n                        router.push(redirectUrl);\n                    }, 3000);\n                    return null;\n                } else if (res.status == 400) {\n                    react_toastify__WEBPACK_IMPORTED_MODULE_6__.toast.error(\"There was an error with your request. Please check your data and try again.\");\n                    return null;\n                } else {\n                    return res.json(); // Ensure you parse the JSON\n                }\n            }).then((json)=>{\n                if (!isBulkUpdate) {\n                    fetchReasonData(orderId[0], currentCustomers[0]);\n                } else {\n                    const reasonsDataArr = json.reduce((acc, item)=>{\n                        const existingEntry = acc.find((entry)=>entry.reason_id === item.reason_id);\n                        if (existingEntry) {\n                            existingEntry.quantity += item.quantity;\n                            existingEntry.id.push(item.id);\n                            existingEntry.order_id.push(item.order_id);\n                            existingEntry.cust_codes.push(item.cust_code);\n                        } else {\n                            acc.push({\n                                added_by: item.added_by,\n                                comment: item.comment,\n                                delete_reason: null,\n                                deleted_by: null,\n                                id: [\n                                    item.id\n                                ],\n                                is_deleted: false,\n                                order_id: [\n                                    item.order_id\n                                ],\n                                quantity: item.quantity,\n                                reason: item.reasonsLabel,\n                                sub_reason: item.subReasonLabel,\n                                reason_id: item.reason_id,\n                                subreason_id: item.subreason_id,\n                                cust_codes: [\n                                    item.cust_code\n                                ]\n                            });\n                        }\n                        return acc;\n                    }, []);\n                    // Set the aggregated reasons data\n                    setReasonsData(reasonsDataArr);\n                }\n                setQuantity(\"\");\n                setComment(\"\");\n                setSelectedReasonDropdownValue(\"\");\n                setSelectedSubReasonDropdownValue(\"\");\n                setIsSaveButtonDisabled(false);\n            });\n        } catch (error) {\n            setIsSaveButtonDisabled(false);\n            console.error(\"Failed to save new reason.\", error);\n        }\n    };\n    const handleEdit = (id, orderId)=>{\n        setIsSaveButtonDisabled(false);\n        // console.log(\"save reasons data\",reasonsData);\n        if (!isValidEditQuantity) {\n            return;\n        }\n        const editData = currentData.map((item, index)=>{\n            var _reasonsMasterList_filter_, _reasonsMasterList_filter_1;\n            return {\n                quantity: isBulkUpdate ? item.CASES_DIFFERENCE : onEditQuantityValue,\n                reasons: onEditSelectedReason,\n                reasonsLabel: (_reasonsMasterList_filter_ = reasonsMasterList.filter((r)=>r.id == +onEditSelectedReason)[0]) === null || _reasonsMasterList_filter_ === void 0 ? void 0 : _reasonsMasterList_filter_.reason,\n                subReason: onEditSelectedSubReason,\n                subReasonLabel: (_reasonsMasterList_filter_1 = reasonsMasterList.filter((r)=>r.id == +onEditSelectedSubReason)[0]) === null || _reasonsMasterList_filter_1 === void 0 ? void 0 : _reasonsMasterList_filter_1.reason,\n                comment: onEditComment.trim(),\n                orderId: item.ORD_ID,\n                id: Array.isArray(id) ? id[index] : id,\n                updatedBy: userData.email,\n                originalEditQuantity: originalEditQuantity,\n                cust_code: item.CUSTOMER\n            };\n        });\n        const isValid = editData.every((item)=>{\n            const isQuantityValid = item.quantity && item.quantity !== \"\";\n            const isReasonValid = item.reasons && item !== \"\";\n            const isSubReasonValid = item.subReason && item.subReason !== \"\";\n            // Set individual validation states\n            if (!isQuantityValid) {\n                setIsValidEditQuantity(false);\n                setInvalidEditQuantityId(item.id);\n            }\n            if (!isReasonValid) {\n                setIsValidEditReasons(false);\n                setInvalidEditReasonsId(item.id);\n            }\n            if (!isSubReasonValid) {\n                setIsValidEditSubReasons(false);\n                setInvalidEditSubReasonsId(item.id);\n            }\n            return isQuantityValid && isReasonValid && isSubReasonValid;\n        });\n        if (!isValid) {\n            return;\n        }\n        setIsValidEditQuantity(true);\n        setIsValidEditReasons(true);\n        setInvalidEditQuantityId(\"\");\n        setInvalidEditReasonsId(\"\");\n        setIsBeingEdited(null);\n        setOriginalEditQuantity(null);\n        setIsValidEditSubReasons(true);\n        setInvalidEditSubReasonsId(\"\");\n        try {\n            const serverAddress = _services_apiConfig__WEBPACK_IMPORTED_MODULE_3__.apiConfig.serverAddress;\n            fetch(\"\".concat(serverAddress, \"servicelevel/edit-reason\"), {\n                method: \"PUT\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                credentials: \"include\",\n                body: JSON.stringify(editData)\n            }).then((res)=>{\n                if (res.status == 401) {\n                    react_toastify__WEBPACK_IMPORTED_MODULE_6__.toast.error(\"Your session has expired. Please log in again.\");\n                    setTimeout(async ()=>{\n                        await (0,_utils_secureStorage__WEBPACK_IMPORTED_MODULE_8__.logout)();\n                        const redirectUrl = \"/login?redirect=\".concat(encodeURIComponent(window.location.pathname));\n                        router.push(redirectUrl);\n                    }, 3000);\n                    return null;\n                } else if (res.status == 400) {\n                    react_toastify__WEBPACK_IMPORTED_MODULE_6__.toast.error(\"There was an error with your request. Please check your data and try again.\");\n                    return null;\n                } else {\n                    return res.json(); // Ensure you parse the JSON\n                }\n            }).then((json)=>{\n                if (!isBulkUpdate) {\n                    fetchReasonData(data[0].ORD_ID, currentCustomers[0]);\n                } else {\n                    const reasonsDataArr = editData.reduce((acc, item)=>{\n                        const existingEntry = acc.find((entry)=>parseInt(entry.reason_id) === parseInt(item.reasons));\n                        if (existingEntry) {\n                            existingEntry.quantity += item.quantity;\n                            existingEntry.id.push(item.id);\n                            existingEntry.order_id.push(item.orderId.toString());\n                            existingEntry.cust_codes.push(item.cust_code);\n                        } else {\n                            acc.push({\n                                added_by: item.updatedBy,\n                                comment: item.comment,\n                                delete_reason: null,\n                                deleted_by: null,\n                                id: [\n                                    item.id\n                                ],\n                                is_deleted: false,\n                                order_id: [\n                                    item.orderId.toString()\n                                ],\n                                quantity: item.quantity,\n                                reason: item.reasonsLabel,\n                                sub_reason: item.subReasonLabel,\n                                reason_id: parseInt(item.reasons),\n                                subreason_id: parseInt(item.subReason),\n                                cust_codes: [\n                                    item.cust_code\n                                ]\n                            });\n                        }\n                        return acc;\n                    }, []);\n                    // Set the aggregated reasons data\n                    setReasonsData(reasonsDataArr);\n                }\n            });\n            setIsSaveButtonDisabled(false);\n        } catch (error) {\n            setIsSaveButtonDisabled(false);\n            console.error(\"Failed to save new reason.\", error);\n        }\n    };\n    const handleDeleteReason = (orderId)=>{\n        // console.log(\"save reasons data\",reasonsData);\n        const deleteData = {\n            orderId,\n            deleteReason: deleteReason,\n            id: Array.isArray(deleteId) ? deleteId : [\n                deleteId\n            ],\n            deletedBy: userData.email,\n            deletedByName: userData.name\n        };\n        let totalCasesDifferent = bulkUpdateData.totalCasesAddedReasons;\n        setBulkUpdateData((prev)=>{\n            return {\n                ...prev,\n                totalCasesDifferent: totalCasesDifferent\n            };\n        });\n        try {\n            const serverAddress = _services_apiConfig__WEBPACK_IMPORTED_MODULE_3__.apiConfig.serverAddress;\n            fetch(\"\".concat(serverAddress, \"servicelevel/delete-reason\"), {\n                method: \"PUT\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                credentials: \"include\",\n                body: JSON.stringify(deleteData)\n            }).then((res)=>{\n                if (res.status == 401) {\n                    react_toastify__WEBPACK_IMPORTED_MODULE_6__.toast.error(\"Your session has expired. Please log in again.\");\n                    setTimeout(async ()=>{\n                        await (0,_utils_secureStorage__WEBPACK_IMPORTED_MODULE_8__.logout)();\n                        const redirectUrl = \"/login?redirect=\".concat(encodeURIComponent(window.location.pathname));\n                        router.push(redirectUrl);\n                    }, 3000);\n                    return null;\n                } else if (res.status == 400) {\n                    react_toastify__WEBPACK_IMPORTED_MODULE_6__.toast.error(\"There was an error with your request. Please check your data and try again.\");\n                    return null;\n                } else {\n                    return res.json(); // Ensure you parse the JSON\n                }\n            }).then((json)=>{\n                fetchReasonData(orderId, currentCustomers[0]);\n            });\n        } catch (error) {\n            console.error(\"Failed to save new reason.\", error);\n        }\n    };\n    const handleParentDropdownReasonChange = (e, type)=>{\n        let parentId;\n        if (typeof e === \"object\") {\n            parentId = parseInt(e.target.value);\n        } else {\n            parentId = e;\n        }\n        if (type == \"add\") {\n            setSelectedReasonDropdownValue(e.target.value);\n            setIsValidReasons(true);\n            if (e.target.value === \"30\") {\n                setSelectedSubReasonDropdownValue(31);\n                setIsValidComment(true);\n                setIsOtherSelected(true);\n            } else {\n                setSelectedSubReasonDropdownValue(\"\");\n                setIsOtherSelected(false);\n            }\n        }\n        setSubReasonsList(reasonsMasterList.filter((child)=>child.parent_id == parentId));\n    };\n    const handleChildDropdownSubReasonChange = (e)=>{\n        setSelectedSubReasonDropdownValue(parseInt(e.target.value));\n        setIsValidSubReasons(true);\n    };\n    const handleQuantityChange = (e)=>{\n        const value = e.target.value;\n        setQuantity(value);\n        const quantityValue = parseInt(value, 10) || 0;\n        22;\n        if (quantityValue <= 0 || quantityValue > data[0].CASES_DIFFERENCE - data[0].CASES_ADDED_REASONS) {\n            setIsValidQuantity(false);\n        } else {\n            setIsValidQuantity(true);\n        }\n    };\n    const handleCommentChange = (e)=>{\n        const value = e.target.value;\n        setComment(value);\n    };\n    const handleEditCommentChange = (e)=>{\n        const value = e.target.value;\n        setOnEditComment(value);\n    };\n    const handleEditQuantity = (e, reasonId)=>{\n        const value = e.target.value;\n        const quantityValue = parseInt(value, 10) || 0;\n        let totalExistingQuantity = reasonsData.reduce((total, reason)=>{\n            return reason.id === reasonId ? total : total + reason.quantity;\n        }, 0);\n        const maxAllowedQuantity = data[0].CASES_DIFFERENCE - data[0].CASES_ADDED_REASONS + originalEditQuantity;\n        if (quantityValue <= 0 || quantityValue > maxAllowedQuantity) {\n            setInvalidEditQuantityId(reasonId);\n            setIsValidEditQuantity(false);\n        } else {\n            setInvalidEditQuantityId(\"\");\n            setIsValidEditQuantity(true);\n        }\n        setOnEditQuantityValue(value);\n    };\n    const handleOpenChange = async (event, data)=>{\n        setIsOpen(data.open);\n        if (!data.open) {\n            setIsHeaderChecked(false);\n            setLockedBy(false);\n            setIsBeingEdited(null);\n            setOriginalEditQuantity(null);\n            setQuantity(\"\");\n            setComment(\"\");\n            setOnEditQuantityValue(\"\");\n            setOnEditSelectedReason(\"\");\n            setOnEditSelectedSubReason(\"\");\n            setDeleteId(\"\");\n            setDeleteReason(\"\");\n            setInvalidEditQuantityId(\"\");\n            setInvalidEditReasonsId(\"\");\n            setOriginalEditQuantity(\"\");\n            setIsValidQuantity(true);\n            setIsValidReasons(true);\n            setIsValidSubReasons(true);\n            setReasonsData([]);\n            setData([]);\n            setSelectedRows([]);\n            const serverAddress = _services_apiConfig__WEBPACK_IMPORTED_MODULE_3__.apiConfig.serverAddress;\n            await fetch(\"\".concat(serverAddress, \"servicelevel/remove-locks\"), {\n                method: \"POST\",\n                headers: {\n                    Accept: \"application/json\",\n                    \"Content-Type\": \"application/json\"\n                },\n                credentials: \"include\",\n                body: JSON.stringify({\n                    email: userData.email,\n                    custCode: currentCustomers,\n                    orderId: orderId,\n                    isPayloadRequired: true\n                })\n            }).catch((error)=>{\n                console.log(\"error\", error);\n            });\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!isBulkUpdate && data[0] && data[0].CASES_DIFFERENCE - data[0].CASES_ADDED_REASONS != 0) {\n            setQuantity(String(data[0].CASES_DIFFERENCE - data[0].CASES_ADDED_REASONS));\n        } else {\n            setQuantity(bulkUpdateData.totalCasesDifferent);\n        }\n        if (quantity <= data[0].CASES_DIFFERENCE - data[0].CASES_ADDED_REASONS && quantity != 0) {\n            setIsValidQuantity(true);\n        }\n    }, [\n        data,\n        isBulkUpdate\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fluentui_react_components__WEBPACK_IMPORTED_MODULE_9__.Dialog, {\n                modalType: \"non-modal\",\n                style: {\n                    fontFamily: \"poppinsregular\"\n                },\n                open: isOpen,\n                onOpenChange: handleOpenChange,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fluentui_react_components__WEBPACK_IMPORTED_MODULE_9__.DialogSurface, {\n                    className: \"!max-w-[60%]\",\n                    style: {\n                        fontFamily: \"poppinsregular\"\n                    },\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fluentui_react_components__WEBPACK_IMPORTED_MODULE_9__.DialogBody, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fluentui_react_components__WEBPACK_IMPORTED_MODULE_9__.DialogTitle, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-xl\",\n                                                children: \"View Details\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                                lineNumber: 607,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            !isBulkUpdate && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fluentui_react_components__WEBPACK_IMPORTED_MODULE_9__.Tooltip, {\n                                                content: \"Audit details for order\",\n                                                relationship: \"label\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>setIsAuditDetailsOpen(true),\n                                                    className: \"tooltip-button\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        fill: \"currentcolor\",\n                                                        xmlns: \"http://www.w3.org/2000/svg\",\n                                                        viewBox: \"0 0 512 512\",\n                                                        className: \"w-4 h-5 !text-skin-primary\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            d: \"M16 96a48 48 0 1 1 96 0A48 48 0 1 1 16 96zM64 208a48 48 0 1 1 0 96 48 48 0 1 1 0-96zm0 160a48 48 0 1 1 0 96 48 48 0 1 1 0-96zM191.5 54.4c5.5-4.2 12.3-6.4 19.2-6.4L424 48c13.3 0 24 10.7 24 24l0 48c0 13.3-10.7 24-24 24l-213.3 0c-6.9 0-13.7-2.2-19.2-6.4l-38.4-28.8c-8.5-6.4-8.5-19.2 0-25.6l38.4-28.8zM153.1 243.2l38.4-28.8c5.5-4.2 12.3-6.4 19.2-6.4L488 208c13.3 0 24 10.7 24 24l0 48c0 13.3-10.7 24-24 24l-277.3 0c-6.9 0-13.7-2.2-19.2-6.4l-38.4-28.8c-8.5-6.4-8.5-19.2 0-25.6zm0 160l38.4-28.8c5.5-4.2 12.3-6.4 19.2-6.4L424 368c13.3 0 24 10.7 24 24l0 48c0 13.3-10.7 24-24 24l-213.3 0c-6.9 0-13.7-2.2-19.2-6.4l-38.4-28.8c-8.5-6.4-8.5-19.2 0-25.6z\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                                            lineNumber: 623,\n                                                            columnNumber: 25\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                                        lineNumber: 617,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                                    lineNumber: 613,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                                lineNumber: 609,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                        lineNumber: 606,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"hr\", {\n                                        className: \"border-b border-gray-200\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                        lineNumber: 629,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                lineNumber: 605,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fluentui_react_components__WEBPACK_IMPORTED_MODULE_9__.DialogContent, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-col gap-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex gap-5 justify-between w-full\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-1/4 flex flex-col \",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            htmlFor: \"depotdate\",\n                                                            className: \"text-gray-500\",\n                                                            children: \"Sales Order / Order Det Id\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                                            lineNumber: 637,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"text\",\n                                                            className: \"px-2 2xl:px-3 border rounded-md w-full\",\n                                                            id: \"depotdate\",\n                                                            value: isBulkUpdate ? \"Multiple\" : \"\".concat(currentData[0].ORD_NUMBER, \" / \").concat(currentData[0].ORD_ID),\n                                                            readOnly: true,\n                                                            disabled: true\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                                            lineNumber: 640,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                                    lineNumber: 636,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \" flex w-1/2 flex-col\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            htmlFor: \"depotdate\",\n                                                            className: \"text-gray-500\",\n                                                            children: \"Product\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                                            lineNumber: 654,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"text\",\n                                                            className: \"px-2 2xl:px-3 border rounded-md w-full\",\n                                                            id: \"depotdate\",\n                                                            value: data[0].PRODUCT_DESCRIPTION,\n                                                            readOnly: true,\n                                                            disabled: true\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                                            lineNumber: 657,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                                    lineNumber: 653,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \" flex w-1/4 flex-col justify-end \",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-center rounded-lg w-28 capitalize h-[30px] px-2 py-1 !text-center \".concat(isBulkUpdate ? \"bg-theme-blue2 text-white\" : data[0].ORD_STATUS === \"Cancelled\" ? \"bg-[#ff2929] text-white\" : data[0].ORD_STATUS === \"Open\" ? \"bg-[#54C5ED] text-white\" : data[0].ORD_STATUS === \"Invoiced\" ? \"bg-[#FFAE00] text-white\" : data[0].ORD_STATUS === \"Delivered\" ? \"bg-[#3EAB58] text-white\" : data[0].ORD_STATUS === \"Picked\" ? \"bg-[#FF6C09] text-white\" : data[0].ORD_STATUS === \"CANCELLED-Invoiced\" ? \"bg-cancelled-status text-white\" : \"bg-qtydiff-status !text-gray-700\" // Default style for any other status\n                                                        ),\n                                                        children: isBulkUpdate ? \"Multiple\" : \"\".concat(data[0].ORD_STATUS.toLowerCase())\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                                        lineNumber: 668,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                                    lineNumber: 667,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                            lineNumber: 633,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex gap-5 justify-between w-full\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-1/4 flex flex-col\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            htmlFor: \"depotdate\",\n                                                            className: \"text-gray-500\",\n                                                            children: \"Depot Date\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                                            lineNumber: 695,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"text\",\n                                                            className: \"px-2 2xl:px-3 border rounded-md w-full\",\n                                                            id: \"depotdate\",\n                                                            value: isBulkUpdate ? \"Multiple\" : \"\".concat((0,_utils_whatif_utils_getFormattedDate__WEBPACK_IMPORTED_MODULE_5__.formatDisplay)(data[0].DEPOT_DATE)),\n                                                            readOnly: true,\n                                                            disabled: true\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                                            lineNumber: 698,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                                    lineNumber: 694,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-1/4 flex flex-col\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            htmlFor: \"altfill\",\n                                                            className: \"text-gray-500\",\n                                                            children: \"Altfill\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                                            lineNumber: 712,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"text\",\n                                                            className: \"px-2 2xl:px-3 border rounded-md w-full\",\n                                                            id: \"altfill\",\n                                                            value: data[0].ALTFILID,\n                                                            readOnly: true,\n                                                            disabled: true\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                                            lineNumber: 715,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                                    lineNumber: 711,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-1/4 flex flex-col\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            htmlFor: \"customer\",\n                                                            className: \"text-gray-500\",\n                                                            children: \"Customer\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                                            lineNumber: 725,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"text\",\n                                                            className: \"px-2 2xl:px-3 border rounded-md w-full\",\n                                                            id: \"customer\",\n                                                            value: isBulkUpdate ? \"Multiple\" : \"\".concat(data[0].CUSTOMER),\n                                                            readOnly: true,\n                                                            disabled: true\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                                            lineNumber: 728,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                                    lineNumber: 724,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-1/4 flex flex-col\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            htmlFor: \"category\",\n                                                            className: \"text-gray-500\",\n                                                            children: \"Master Product Code\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                                            lineNumber: 738,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"text\",\n                                                            className: \"px-2 2xl:px-3 border rounded-md w-full\",\n                                                            id: \"category\",\n                                                            value: data[0].MASTER_PRODUCT_CODE,\n                                                            readOnly: true,\n                                                            disabled: true\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                                            lineNumber: 741,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                                    lineNumber: 737,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                            lineNumber: 693,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex gap-5 justify-between w-full\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-1/4 flex flex-col\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            htmlFor: \"casesize\",\n                                                            className: \"text-gray-500\",\n                                                            children: \"Case Size\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                                            lineNumber: 754,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"text\",\n                                                            className: \"px-2 2xl:px-3 border rounded-md w-full\",\n                                                            id: \"casesize\",\n                                                            value: data[0].CASE_SIZE,\n                                                            readOnly: true,\n                                                            disabled: true\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                                            lineNumber: 757,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                                    lineNumber: 753,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-1/4 flex flex-col\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            htmlFor: \"casesord\",\n                                                            className: \"text-gray-500\",\n                                                            children: \"Cases Ordered\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                                            lineNumber: 767,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"text\",\n                                                            className: \"px-2 2xl:px-3 border rounded-md w-full\",\n                                                            id: \"casesord\",\n                                                            value: isBulkUpdate ? bulkUpdateData.totalCasesOrdered : \"\".concat(data[0].CASES_ORIGINAL),\n                                                            readOnly: true,\n                                                            disabled: true\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                                            lineNumber: 770,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                                    lineNumber: 766,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-1/4 flex flex-col\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            htmlFor: \"casedel\",\n                                                            className: \"text-gray-500\",\n                                                            children: \"Cases Delivered\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                                            lineNumber: 784,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"text\",\n                                                            className: \"px-2 2xl:px-3 border rounded-md w-full\",\n                                                            id: \"casedel\",\n                                                            value: isBulkUpdate ? bulkUpdateData.totalCasesDelivered : \"\".concat(data[0].CASES_DELIVERED),\n                                                            readOnly: true,\n                                                            disabled: true\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                                            lineNumber: 787,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                                    lineNumber: 783,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-1/4 flex flex-col\",\n                                                    title: \"The following case differences are the absolute sum of all differences\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            htmlFor: \"casesdiff\",\n                                                            className: \"text-gray-500 font-bold\",\n                                                            children: \"Cases Different\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                                            lineNumber: 804,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"text\",\n                                                            className: \"px-2 2xl:px-3 border rounded-md w-full font-bold\",\n                                                            id: \"casesdiff\",\n                                                            value: isBulkUpdate ? bulkUpdateData.totalCasesDifferent : \"\".concat(data[0].CASES_DIFFERENCE - data[0].CASES_ADDED_REASONS),\n                                                            readOnly: true,\n                                                            disabled: true\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                                            lineNumber: 810,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                                    lineNumber: 800,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                            lineNumber: 752,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        (!isBulkUpdate || isBulkUpdate && reasonsData.length == 0) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"border border-gray-200 p-4 rounded-md\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    style: {\n                                                        fontFamily: \"poppinsregular\"\n                                                    },\n                                                    children: \"Add the reason(s)\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                                    lineNumber: 833,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"hr\", {\n                                                    className: \"border-b border-gray-200\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                                    lineNumber: 836,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex gap-4 justify-between pt-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-[10%] flex flex-col\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    htmlFor: \"quantity\",\n                                                                    className: \"text-gray-500\",\n                                                                    children: \"Quantity\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                                                    lineNumber: 839,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                    type: \"number\",\n                                                                    onChange: handleQuantityChange,\n                                                                    className: \"px-2 2xl:px-3 border rounded-md \".concat(!isValidQuantity && \"!border-red-500\"),\n                                                                    value: quantity,\n                                                                    max: data[0].CASES_DIFFERENCE - data[0].CASES_ADDED_REASONS,\n                                                                    id: \"quantity\",\n                                                                    disabled: isBeingEdited || isBulkUpdate\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                                                    lineNumber: 842,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                                            lineNumber: 838,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-[30%] flex flex-col\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    htmlFor: \"reason\",\n                                                                    className: \"text-gray-500\",\n                                                                    children: \"Reason\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                                                    lineNumber: 858,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                                    onChange: (e)=>handleParentDropdownReasonChange(e, \"add\"),\n                                                                    className: \"px-2 2xl:px-3 border \".concat(!isValidReason && \"!border-red-500\", \" rounded-md w-full h-[31px]\"),\n                                                                    value: selectedReasonDropdownValue,\n                                                                    id: \"reason\",\n                                                                    disabled: isBeingEdited,\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                            value: \"\",\n                                                                            children: \"Select...\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                                                            lineNumber: 873,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        parentReasonList === null || parentReasonList === void 0 ? void 0 : parentReasonList.map((parentReason, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                value: parentReason.id,\n                                                                                children: parentReason.reason\n                                                                            }, \"\".concat(parentReason.id, \"-\").concat(index), false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                                                                lineNumber: 875,\n                                                                                columnNumber: 29\n                                                                            }, undefined))\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                                                    lineNumber: 862,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                                            lineNumber: 857,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-[30%] flex flex-col\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    htmlFor: \"subreasons\",\n                                                                    className: \"text-gray-500\",\n                                                                    children: \"Sub Reason\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                                                    lineNumber: 886,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                                    onChange: handleChildDropdownSubReasonChange,\n                                                                    disabled: !selectedReasonDropdownValue || isBeingEdited || isOtherSelected,\n                                                                    className: \"px-2 2xl:px-3 border rounded-md w-full h-[31px] \".concat(!isValidSubReason && \"!border-red-500\"),\n                                                                    value: selectedSubReasonDropdownValue,\n                                                                    id: \"subreasons\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                            value: \"\",\n                                                                            children: \"Select...\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                                                            lineNumber: 903,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        subReasonsList === null || subReasonsList === void 0 ? void 0 : subReasonsList.map((subReason, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                value: subReason.id,\n                                                                                children: subReason.reason\n                                                                            }, \"\".concat(subReason.id, \"-\").concat(index), false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                                                                lineNumber: 905,\n                                                                                columnNumber: 29\n                                                                            }, undefined))\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                                                    lineNumber: 890,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                                            lineNumber: 885,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-[25%] flex flex-col\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    htmlFor: \"comment\",\n                                                                    className: \"text-gray-500\",\n                                                                    children: \"Comment\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                                                    lineNumber: 915,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                    type: \"text\",\n                                                                    onChange: handleCommentChange,\n                                                                    maxLength: 200,\n                                                                    className: \"px-2 2xl:px-3 border rounded-md w-full \".concat(!isValidComment && \"!border-red-500\"),\n                                                                    id: \"comment\",\n                                                                    value: comment,\n                                                                    disabled: isBeingEdited\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                                                    lineNumber: 918,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                                            lineNumber: 914,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-[5%] flex flex-col\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                className: \"mt-8\",\n                                                                onClick: ()=>saveReasons(),\n                                                                disabled: isSaveButtonDisabled || isBeingEdited || !isValidQuantity,\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                    fill: \"currentcolor\",\n                                                                    xmlns: \"http://www.w3.org/2000/svg\",\n                                                                    viewBox: \"0 0 512 512\",\n                                                                    className: \"w-5 h-5 fill !text-skin-primary\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                        d: \"M57.6 35.8C23.1 20.6-11.3 57.4 6.1 90.9l63 121.2c4.4 8.4 12.6 14.1 22 15.3c0 0 0 0 0 0L266 249.3c3.4 .4 6 3.3 6 6.7s-2.6 6.3-6 6.7L91.1 284.6s0 0 0 0c-9.4 1.2-17.6 6.9-22 15.3L6.1 421.1c-17.4 33.5 17 70.2 51.6 55.1L492.9 285.3c25.5-11.2 25.5-47.4 0-58.6L57.6 35.8z\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                                                        lineNumber: 946,\n                                                                        columnNumber: 29\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                                                    lineNumber: 940,\n                                                                    columnNumber: 27\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                                                lineNumber: 931,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                                            lineNumber: 930,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                                    lineNumber: 837,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                            lineNumber: 832,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"previousreasons flex flex-col gap-3\",\n                                            children: reasonsData === null || reasonsData === void 0 ? void 0 : reasonsData.map((reason, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex flex-col gap-3 bg-gray-100 rounded-md p-4 my-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex gap-4 justify-between pt-3 \",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"w-[10%] flex flex-col\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                htmlFor: \"quantity\",\n                                                                                className: \"text-gray-500\",\n                                                                                children: \"Quantity\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                                                                lineNumber: 966,\n                                                                                columnNumber: 29\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                type: \"number\",\n                                                                                onChange: (e)=>handleEditQuantity(e, reason.id),\n                                                                                className: \"px-2 2xl:px-3 border \".concat(invalidEditQuantityId == reason.id && !isValidEditQuantity && \"!border-red-500\", \" rounded-md w-full\"),\n                                                                                id: \"quantity\",\n                                                                                disabled: isBeingEdited != reason.id || isBulkUpdate,\n                                                                                defaultValue: reason.quantity\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                                                                lineNumber: 969,\n                                                                                columnNumber: 29\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                                                        lineNumber: 965,\n                                                                        columnNumber: 27\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"w-[30%] flex flex-col\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                htmlFor: \"reason1\",\n                                                                                className: \"text-gray-500\",\n                                                                                children: \"Reason\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                                                                lineNumber: 985,\n                                                                                columnNumber: 29\n                                                                            }, undefined),\n                                                                            isBeingEdited != reason.id ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                type: \"text\",\n                                                                                className: \"px-2 2xl:px-3 border \".concat(invalidEditReasonsId == reason.id && !isValidEditReason && \"!border-red-500\", \" rounded-md w-full\"),\n                                                                                id: \"reason1\",\n                                                                                disabled: isBeingEdited != reason.id,\n                                                                                defaultValue: reason.reason\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                                                                lineNumber: 989,\n                                                                                columnNumber: 31\n                                                                            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                                                onChange: (e)=>{\n                                                                                    const selectedValue = e.target.value;\n                                                                                    setOnEditSelectedReason(selectedValue);\n                                                                                    setIsValidEditReasons(true);\n                                                                                    setInvalidEditReasonsId(\"\");\n                                                                                    handleParentDropdownReasonChange(e, \"edit\");\n                                                                                },\n                                                                                className: \"px-2 2xl:px-3 border \".concat(invalidEditReasonsId == reason.id && !isValidEditReason && \"!border-red-500\", \" rounded-md w-full h-[31px]\"),\n                                                                                id: \"reason1\",\n                                                                                value: onEditSelectedReason,\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                        value: \"\",\n                                                                                        children: \"Select...\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                                                                        lineNumber: 1017,\n                                                                                        columnNumber: 33\n                                                                                    }, undefined),\n                                                                                    parentReasonList === null || parentReasonList === void 0 ? void 0 : parentReasonList.map((parentReason, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                            value: parentReason.id,\n                                                                                            children: parentReason.reason\n                                                                                        }, \"\".concat(parentReason.id, \"-\").concat(index), false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                                                                            lineNumber: 1019,\n                                                                                            columnNumber: 35\n                                                                                        }, undefined))\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                                                                lineNumber: 1001,\n                                                                                columnNumber: 31\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                                                        lineNumber: 984,\n                                                                        columnNumber: 27\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"w-[30%] flex flex-col\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                htmlFor: \"subreasons1\",\n                                                                                className: \"text-gray-500\",\n                                                                                children: \"Sub Reason\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                                                                lineNumber: 1030,\n                                                                                columnNumber: 29\n                                                                            }, undefined),\n                                                                            isBeingEdited != reason.id ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                type: \"text\",\n                                                                                onChange: (e)=>setOnEditSelectedSubReason(e.target.value),\n                                                                                className: \"px-2 2xl:px-3 border rounded-md w-full \".concat(invalidEditSubReasonsId == reason.id && !isValidEditSubReason && \"!border-red-500\"),\n                                                                                id: \"subreasons1\",\n                                                                                disabled: isBeingEdited != reason.id,\n                                                                                defaultValue: reason.sub_reason\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                                                                lineNumber: 1037,\n                                                                                columnNumber: 31\n                                                                            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                                                onChange: (e)=>setOnEditSelectedSubReason(e.target.value),\n                                                                                className: \"px-2 2xl:px-3 border rounded-md w-full h-[31px]\",\n                                                                                id: \"subreasons1\",\n                                                                                value: onEditSelectedSubReason,\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                        value: \"\",\n                                                                                        children: \"Select...\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                                                                        lineNumber: 1060,\n                                                                                        columnNumber: 33\n                                                                                    }, undefined),\n                                                                                    subReasonsList === null || subReasonsList === void 0 ? void 0 : subReasonsList.map((subReason)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                            value: subReason.id,\n                                                                                            children: subReason.reason\n                                                                                        }, \"\".concat(subReason.id, \"-\").concat(index), false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                                                                            lineNumber: 1062,\n                                                                                            columnNumber: 35\n                                                                                        }, undefined))\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                                                                lineNumber: 1052,\n                                                                                columnNumber: 31\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                                                        lineNumber: 1029,\n                                                                        columnNumber: 27\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"w-[25%] flex flex-col\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                htmlFor: \"comment\",\n                                                                                className: \"text-gray-500\",\n                                                                                children: \"Comment\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                                                                lineNumber: 1073,\n                                                                                columnNumber: 29\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                type: \"text\",\n                                                                                onChange: handleEditCommentChange,\n                                                                                className: \"px-2 2xl:px-3 border rounded-md w-full\",\n                                                                                id: \"comment\",\n                                                                                disabled: isBeingEdited != reason.id,\n                                                                                defaultValue: reason.comment\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                                                                lineNumber: 1076,\n                                                                                columnNumber: 29\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                                                        lineNumber: 1072,\n                                                                        columnNumber: 27\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"w-[5%] flex flex-col\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                                                        lineNumber: 1085,\n                                                                        columnNumber: 27\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                                                lineNumber: 964,\n                                                                columnNumber: 25\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex justify-between items-center\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex gap-2 items-center\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fluentui_react_components__WEBPACK_IMPORTED_MODULE_9__.Avatar, {\n                                                                                // initials=\"LT\"\n                                                                                color: \"light-teal\",\n                                                                                name: reason.added_by\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                                                                lineNumber: 1089,\n                                                                                columnNumber: 29\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                children: [\n                                                                                    reason.added_by,\n                                                                                    \" \"\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                                                                lineNumber: 1094,\n                                                                                columnNumber: 29\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-gray-400\",\n                                                                                children: reason.date\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                                                                lineNumber: 1095,\n                                                                                columnNumber: 29\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                                                        lineNumber: 1088,\n                                                                        columnNumber: 27\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex justify-end gap-4 pt-2\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_DeleteReasonPopover__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                                setDeleteId: setDeleteId,\n                                                                                setDeleteReason: setDeleteReason,\n                                                                                setIsDeleteTrue: setIsDeleteTrue,\n                                                                                id: reason.id\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                                                                lineNumber: 1098,\n                                                                                columnNumber: 29\n                                                                            }, undefined),\n                                                                            isBeingEdited && isBeingEdited == reason.id ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                                    fill: \"currentcolor\",\n                                                                                    xmlns: \"http://www.w3.org/2000/svg\",\n                                                                                    viewBox: \"0 0 512 512\",\n                                                                                    className: \"w-5 h-5 !text-skin-primary\",\n                                                                                    onClick: ()=>handleEdit(reason.id, data[0].ORD_ID),\n                                                                                    disabled: !isValidEditQuantity || isSaveButtonDisabled,\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                        d: \"M57.6 35.8C23.1 20.6-11.3 57.4 6.1 90.9l63 121.2c4.4 8.4 12.6 14.1 22 15.3c0 0 0 0 0 0L266 249.3c3.4 .4 6 3.3 6 6.7s-2.6 6.3-6 6.7L91.1 284.6s0 0 0 0c-9.4 1.2-17.6 6.9-22 15.3L6.1 421.1c-17.4 33.5 17 70.2 51.6 55.1L492.9 285.3c25.5-11.2 25.5-47.4 0-58.6L57.6 35.8z\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                                                                        lineNumber: 1118,\n                                                                                        columnNumber: 35\n                                                                                    }, undefined)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                                                                    lineNumber: 1106,\n                                                                                    columnNumber: 33\n                                                                                }, undefined)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                                                                lineNumber: 1105,\n                                                                                columnNumber: 31\n                                                                            }, undefined) : !isBulkUpdate ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                className: \"b\",\n                                                                                href: \"\",\n                                                                                onClick: ()=>{\n                                                                                    setIsBeingEdited(reason.id);\n                                                                                    setOriginalEditQuantity(reason.quantity);\n                                                                                    setMapForReasonsParentsAndTheirCorrespondingChildren();\n                                                                                    handleParentDropdownReasonChange(reason.reason_id, \"edit\");\n                                                                                    setOnEditComment(reason.comment);\n                                                                                    setOnEditQuantityValue(reason.quantity);\n                                                                                    setOnEditSelectedReason(reason.reason_id);\n                                                                                    setOnEditSelectedSubReason(reason.subreason_id);\n                                                                                },\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                                    fill: \"currentcolor\",\n                                                                                    xmlns: \"http://www.w3.org/2000/svg\",\n                                                                                    viewBox: \"0 0 512 512\",\n                                                                                    className: \"w-5 h-5 !text-skin-primary\",\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                        d: \"M362.7 19.3L314.3 67.7 444.3 197.7l48.4-48.4c25-25 25-65.5 0-90.5L453.3 19.3c-25-25-65.5-25-90.5 0zm-71 71L58.6 323.5c-10.4 10.4-18 23.3-22.2 37.4L1 481.2C-1.5 489.7 .8 498.8 7 505s15.3 8.5 23.7 6.1l120.3-35.4c14.1-4.2 27-11.8 37.4-22.2L421.7 220.3 291.7 90.3z\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                                                                        lineNumber: 1147,\n                                                                                        columnNumber: 35\n                                                                                    }, undefined)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                                                                    lineNumber: 1141,\n                                                                                    columnNumber: 33\n                                                                                }, undefined)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                                                                lineNumber: 1122,\n                                                                                columnNumber: 31\n                                                                            }, undefined) : \"\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                                                        lineNumber: 1097,\n                                                                        columnNumber: 27\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                                                lineNumber: 1087,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                                        lineNumber: 963,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                }, \"\".concat(reason.id, \"-\").concat(index), false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                                    lineNumber: 962,\n                                                    columnNumber: 21\n                                                }, undefined))\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                            lineNumber: 959,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                    lineNumber: 632,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                lineNumber: 631,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fluentui_react_components__WEBPACK_IMPORTED_MODULE_9__.DialogActions, {\n                                className: \"!mt-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"hr\", {\n                                        className: \"border-b border-gray-200\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                        lineNumber: 1162,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fluentui_react_components__WEBPACK_IMPORTED_MODULE_9__.DialogTrigger, {\n                                        disableButtonEnhancement: true,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            className: \"border rounded-md border-skin-primary text-skin-primary px-5 py-1\",\n                                            children: \"Close\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                            lineNumber: 1164,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                        lineNumber: 1163,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                lineNumber: 1161,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                        lineNumber: 604,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                    lineNumber: 600,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                lineNumber: 594,\n                columnNumber: 7\n            }, undefined),\n            isAuditDetailsOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_AuditDetails__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                orderId: data[0].ORD_ID,\n                isAuditDetailsOpen: isAuditDetailsOpen,\n                setIsAuditDetailsOpen: setIsAuditDetailsOpen\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                lineNumber: 1173,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true);\n};\n_s(ViewDetails, \"ZpEicqoPiKnnlcki/uC6TfmdFX4=\", false, function() {\n    return [\n        next_router__WEBPACK_IMPORTED_MODULE_7__.useRouter\n    ];\n});\n_c = ViewDetails;\n/* harmony default export */ __webpack_exports__[\"default\"] = (ViewDetails);\nvar _c;\n$RefreshReg$(_c, \"ViewDetails\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevExports = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevExports) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports on update so we can compare the boundary\n                // signatures.\n                module.hot.dispose(function (data) {\n                    data.prevExports = currentExports;\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevExports !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevExports, currentExports)) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevExports !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/service_level/ViewDetails.jsx\n"));

/***/ })

});