import { useEffect, useState } from "react";
import LoginBanner from "@/components/LoginBanner";
import { useRouter } from "next/router";
import LoginSectionSecure from "@/components/LoginSectionSecure";

export default function Login() {
  const router = useRouter();
  const [isMounted, setIsMounted] = useState(false);

  useEffect(() => {
    setIsMounted(true);
    
    // Clear any old authentication data
    if (typeof window !== 'undefined') {
      localStorage.removeItem("superUser");
      localStorage.removeItem("domain");
      localStorage.removeItem("userDetails");
    }
  }, []);

  // Check if user is already authenticated
  useEffect(() => {
    const checkAuth = async () => {
      if (!isMounted) return;
      
      try {
        const apiBase = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:8081';
        const response = await fetch(`${apiBase}/api/auth/me`, {
          method: 'GET',
          credentials: 'include',
        });

        if (response.ok) {
          // User is already authenticated, redirect
          const { redirect } = router.query;
          router.replace(redirect || '/suppliers');
        }
      } catch (error) {
        // User not authenticated, stay on login page
      }
    };

    checkAuth();
  }, [isMounted, router]);

  if (!isMounted) {
    return null; // Prevent hydration mismatch
  }

  return (
    <div className="flex flex-col lg:flex-row min-h-screen">
      <LoginBanner />
      <div className="flex-1 flex items-center justify-center p-8">
        <LoginSectionSecure />
      </div>
    </div>
  );
}
