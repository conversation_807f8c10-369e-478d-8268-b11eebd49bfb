import Layout from "@/components/Layout";
import React, {
  useMemo,
  useState,
  useRef,
  useCallback,
  useEffect,
  Fragment,
} from "react";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faSearch, faClose } from "@fortawesome/free-solid-svg-icons";
import { AgGridReact } from "ag-grid-react";
import "ag-grid-community/styles//ag-grid.css";
import "ag-grid-community/styles//ag-theme-alpine.css";
import { useRouter } from "next/router";
import { ToastContainer, toast } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import { useLoading } from "@/utils/loaders/loadingContext";
import Select from "react-select";
import { usePermissions } from "@/utils/rolePermissionsContext";
import productStatusRenderer from "@/utils/renderer/productStatusRenderer";
import productActionRenderer from "@/utils/renderer/productActionRenderer";
import { apiConfig } from "@/services/apiConfig";
import Cookies from "js-cookie";
import { useMsal } from "@azure/msal-react";
import RequestDialoge from "@/components/ProductDialog";
import { logout } from "@/utils/secureStorage";
import { extractCompanyFromEmail } from "@/utils/extractCompanyFromEmail";

const customSelectStyles = {
  // Default style
  control: (base) => ({
    ...base,
    height: "28px",
    minHeight: "28px",
    // border: 0,
  }),
  // Style when the condition is true
  option: (base, { data }) => ({
    ...base,
    color: data.is_new == true ? "red" : "",
  }),

  valueContainer: (provided, state) => ({
    ...provided,
    height: "26px",
    width: "300px",
    padding: "0 6px",
  }),

  input: (provided, state) => ({
    ...provided,
    margin: "0px",
  }),
  indicatorSeparator: (state) => ({
    display: "none",
  }),
  indicatorsContainer: (provided, state) => ({
    ...provided,
    height: "28px",
  }),
};

const Products = ({ userData, PreviousPage, pageTypeId }) => {
  console.log("userData, PreviousPage, pageTypeId",userData, PreviousPage, pageTypeId)
  const router = useRouter();
  const [pageSize, setPageSize] = useState(15);
  const gridRef = useRef();
  const [isRMChecked, setIsRMChecked] = useState(false);
  const [isFGChecked, setIsFGChecked] = useState(false);
  const [isNVChecked, setIsNVChecked] = useState(false);
  const { setIsLoading } = useLoading();
  const [rowData, setRowData] = useState([]);
  const [company, setCompany] = useState("");
  const [blockScreen, setBlockScreen] = useState(false);
  const [typeId, setTypeId] = useState(pageTypeId);
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [isIssUser, setIsIssUser] = useState(false);
  const [isIssProcurementTeamUser, setIsIssProcurementTeamUser] =
    useState(false);
  const [isIssAdmin, setisIssAdmin] = useState(false);
  const [nvStatus, setNVStatus] = useState([]);
  const [statusOptions] = useState([
    { value: "Draft", label: "Draft" },
    { value: "Pending Review", label: "Pending Review" },
    { value: "Rejected", label: "Rejected" },
    { value: "ISS to Setup", label: "ISS to Setup" },
    { value: "Setup Completed", label: "Setup Completed" },
    { value: "Cancelled", label: "Cancelled" },
  ]);

  const [filteredRowData, setFilteredRowData] = useState([]);
  const [checkedValue, setCheckedValue] = useState(false);
  const [incompleteToast, setIncompleteToast] = useState(false);
  const [searchInput, setSearchInput] = useState("");
  useEffect(() => {
    // const userCompany = Cookies.get("company");
    const userCompany = userData?.company || extractCompanyFromEmail(userData?.email);
    if (userCompany === "issproduce") {
      setIsIssUser(true);
    }

    console.log(userData);
    if (userData.department_id === 2) {
      setIsIssProcurementTeamUser(true);
      console.log("checking if it is an admin", userData.role);
      if (userData.role_id === 1) {
        console.log("is admin");
        setisIssAdmin(true);
      }
    }
    console.log("isIssProcurementTeamUser", isIssProcurementTeamUser);
  }, []);

  const openDialog = () => {
    setIsDialogOpen(true);
  };

  const closeDialog = () => {
    setIsDialogOpen(false);
  };

  // const [isOpenOption, setIsOpenOption] = useState(false);
  const [selectedRequestType, setSelectedRequestType] = useState(PreviousPage);

  const handleTypeIdChange = (e) => {
    setSearchInput("");
    const filterTextBox = document.getElementById("filter-text-box");
    filterTextBox.value = "";
    gridRef.current.api.setQuickFilter("");
    setNVStatus([]);
    setTypeId(parseInt(e.target.value));
    if (e.target.value === "1") {
      setSelectedRequestType("rawMaterialRequest");
    } else if (e.target.value === "3") {
      setSelectedRequestType("newVarietyRequest");
    } else if (e.target.value === "4") {
      setSelectedRequestType("packagingform");
    }
  };

  // const closeOptionModal = () => {
  //   setIsOpenOption(false);
  // };

  const handleRequestType = (type) => {
    setSelectedRequestType(type);
  };

  const handleFormType = () => {
    if (selectedRequestType) {
      localStorage.setItem("formType", selectedRequestType);
      console.log(selectedRequestType);
      if (selectedRequestType === "rawMaterialRequest") {
        router.push({
          pathname: `/raw-material-request/add`,
        });
        // } else if (selectedRequestType == "finishedProductRequest") {
        //   router.push({
        //     pathname: `/finished-product-request/add` });
      } else if (selectedRequestType == "newVarietyRequest") {
        router.push({ pathname: `/variety/add` });
      } else if (selectedRequestType == "packagingform") {
        // router.push({ pathname: `/packaging/add` });
        router.push({ pathname: `/packaging-form/add` }); //TODO
      }
    }
  };

  useEffect(() => {
    if (incompleteToast) {
      toast.error("Kindly Submit the Request to Export it.", {
        position: "top-right",
        autoClose: 1000,
        hideProgressBar: false,
        closeOnClick: true,
        pauseOnHover: false,
        draggable: true,
        progress: undefined,
        theme: "light",
      });
    }
  }, [incompleteToast]);

  const [productsExtractData, setProductsExtractData] = useState([
    ["Product Extract Data"],
  ]);

  useEffect(() => {
    if (productsExtractData.length > 0) {
      setCheckedValue(true);
    } else {
      setCheckedValue(false);
    }
  }, [productsExtractData]);

  useEffect(() => {
    if (typeof document !== "undefined") {
      document.title = "Products";
    }

    setIsLoading(false);
    getData()
      .then((data) => {
        console.log(data);
        const formattedData = data?.map((row) => ({
          id: row.id,
          code: row.code,
          action_id: row.action_id,
          request_no: row.request_no,
          type: row.product_type,
          product_type_name: row.product_type_label,
          product_type_id: row.product_type_id,
          reason: row?.reason_description
            ? row?.reason_description
            : "Not Entered",
          delivery_date: row.delivery_date
            ? new Date(row.delivery_date).toISOString().split("T")[0]
            : "Not Entered",
          // delivery_date: new Date(row.delivery_date),
          product_code: row.master_product_code
            ? row.master_product_code
            : "Not Entered",
          product_description: row.product_description
            ? row.product_description
            : "Not Entered",
          originator: row.originator ? row.originator : row.originator_name,
          originator_email: row.originator_email,
          coo: row.coo ? row.coo : "Not Entered",
          status: row.status_label,
          master_product_code: row?.master_product_code,
          count_or_size: row?.count_or_size,
          units_in_outer: row?.units_in_outer,
          cases_per_pallet: row?.cases_per_pallet,
          outer_net_weight: row?.outer_net_weight,
          outer_gross_weight: row?.outer_gross_weight,
          sub_product_code: row?.sub_product_code,
          temperature_grade: row?.temperature_grade,
          intrastat_commodity_code: row?.intrastat_commodity_code,
          temperature_grade_name: row?.temperature_grade_name,
          intrastat_commodity_code_name: row?.intrastat_commodity_code_name,
          mark_variety_name: row?.mark_variety_name,
          intrastat_commodity_code_id: row?.intrastat_commodity_code_id,
          sort_group_id: row?.group_id,
          company: row?.company_name,
          temperature_grade_id: row?.temperature_grade_id,
          userText4: row?.userText4,
          userText5: row?.userText5,
          userText6: row?.userText6,
        }));
        setCompany(formattedData[0].company);
        setRowData(formattedData);
        setFilteredRowData(formattedData);
      })
      .catch((error) => {
        return error;
      });
  }, [typeId]);

  function getData() {
    setRowData([]);
    setFilteredRowData([]);
    let serverAddress = apiConfig.serverAddress;
    const company_name =
      userData?.company || extractCompanyFromEmail(userData?.email);
    const AdCompany = userData?.companyName || userData?.ADCompanyName;
    return fetch(
      `${serverAddress}products/get-products/${
        company_name == "dpsltd" && AdCompany == "DPS MS"
          ? "DPS MS"
          : company_name
      }
      /${typeId}`,
      {
        method: "GET",
        headers: {
          "Content-Type": "application/json",
        },
        credentials: "include", // Use session authentication
      }
    )
      .then(async (res) => {
        if (res.status == 502) {
          setBlockScreen(true);
          return;
        }
        setBlockScreen(false);
        if (res.status === 200) {
          return res.json();
        }
        if (res.status === 400) {
          toast.error(
            "There was an error with your request. Please check your data and try again."
          );
        } else if (res.status === 401) {
          toast.error("Your session has expired. Please log in again.");
          setTimeout(async () => {
            await logout();
            router.push("/login");
          }, 3000);
        }
        throw new Error("Failed to fetch data");
      })
      .catch((error) => {
        console.error(error);
      });
  }

  const handlePageSizeChange = (event) => {
    const newPageSize = parseInt(event.target.value, 15);
    setPageSize(newPageSize);
    gridRef.current.api.paginationSetPageSize(newPageSize);
  };

  const handleStatusChange = (selectedOptions) => {
    setNVStatus(selectedOptions);
    filterData(selectedOptions);
  };

  const filterData = (statuses) => {
    if (statuses.length == 0) {
      setFilteredRowData(rowData);
      return;
    }
    const filteredData = rowData.filter((row) =>
      statuses.some((status) => status.value === row.status)
    );

    setFilteredRowData(filteredData.length > 0 ? filteredData : []);
  };

  // const clearFilters = () => {
  //   setSelectedStatuses([]);
  //   setFilteredRowData(rowData);
  // };

  const defaultColDef = useMemo(() => ({
    sortable: true,
    filter: false,
    resizable: true,
    flex: 1,
    suppressMenu: false,
  }));

  const gridOptions = {
    responsive: true,
  };

  const columnDefs = [
    {
      headerName: "Request No.",
      field: "request_no",
      // cellRenderer: nameRenderer,
      suppressMenu: true,
      suppressSizeToFit: true,
      suppressSizeToFit: false,
      cellClass: "ag-grid-checkbox-cell",
      flex: "2%",
      filter: true,
    },
    {
      headerName: "Reason",
      field: "reason",
      flex: "2%",
      cellRenderer: productStatusRenderer,
      hide: typeId == 1 || 4 ? false : true,
      cellStyle: (params) => {
        if (params.value == "Not Entered") {
          return { color: "#B31312" };
        }
        return null;
      },
    },
    {
      headerName: "Delivery Date",
      field: "delivery_date",
      flex: "2%",
      hide: typeId == 1 || 4 ? false : true,
      cellRenderer: (params) => {
        if (params.value === "Not Entered") {
          return <div className="text-[#B31312]">Not Entered</div>;
        } else {
          // params.value
          // Format the date from yyyy-mm-dd to dd/mm/yyyy
          const dateParts = params.value.split("-");
          const formattedDate = dateParts.reverse().join("/");

          return <div>{formattedDate}</div>;
        }
      },
    },
    {
      headerName: "Product Code",
      field: "product_code",
      flex: "2%",
      // cellClass: (params) => {
      //   return params.value === "Not Entered" ? 'not-entered' : '';
      // }
      cellStyle: (params) => {
        if (params.value == "Not Entered") {
          return { color: "#B31312" };
        }
        return null;
      },
    },
    {
      // headerName: "Product description",
      // field: "product_description",
      headerName: "Product Description",
      field: "product_description",
      // cellRenderer: statusRenderer,
      flex: "3%",
      cellStyle: (params) => {
        if (params.value == "Not Entered") {
          return { color: "#B31312" };
        }
        return null;
      },
    },
    {
      headerName: "Originator",
      field: "originator",
      headerName: "Originator",
      field: "originator",
      // cellRenderer: statusRenderer,
      flex: "2%",
      flex: "2%",
    },
    // {
    //   headerName: "COO",
    //   field: "coo",
    //   headerName: "COO",
    //   field: "coo",
    //   // cellRenderer: statusRenderer,
    //   flex: "2%",
    //   cellStyle: (params) => {
    //     if (params.value == "Not Entered") {
    //       return { color: "#B31312" };
    //     }
    //     return null;
    //   },
    // },

    {
      headerName: "Status",
      field: "status",
      cellRenderer: productStatusRenderer,
      cellStyle: () => ({ justifyContent: "center" }),
      flex: "2%",
      hide: false,
    },
    {
      // field: "Action(s)",
      field: typeId == 3 ? "Action(s)" : "Action(s)",
      cellRenderer: (params) =>
        productActionRenderer(
          params,
          userData,
          company,
          typeId,
          setIsLoading,
          isIssUser
        ),
      flex: "2%",
      cellStyle: () => ({}),
      cellStyle: { justifyContent: "end", paddingRight: "10px" },
      sortable: false,
    },
    {
      field: "role_num",
      hide: true,
    },
  ];

  const handleGridReady = (params) => {
    params.api.setColumnDefs(columnDefs);
  };

  const onFilterTextBoxChanged = useCallback(() => {
    gridRef.current.api.setQuickFilter(
      document.getElementById("filter-text-box").value
    );
    setSearchInput(document.getElementById("filter-text-box").value);
  }, []);

  const handleFilterProductType = useCallback((e) => {
    if (e.target.value == "RM") {
      if (e.target.checked) {
        setIsRMChecked(true);
        setIsFGChecked(false);
        setIsNVChecked(false);
        gridRef.current.api.setQuickFilter("RM");
      } else {
        setIsRMChecked(false);
        gridRef.current.api.setQuickFilter("");
      }
    } else if (e.target.value == "FG") {
      if (e.target.checked) {
        setIsFGChecked(true);
        setIsRMChecked(false);
        setIsNVChecked(false);
        gridRef.current.api.setQuickFilter("FG");
      } else {
        setIsFGChecked(false);
        gridRef.current.api.setQuickFilter("");
      }
    } else if (e.target.value == "NV") {
      if (e.target.checked) {
        setIsNVChecked(true);
        setIsFGChecked(false);
        setIsRMChecked(false);
        gridRef.current.api.setQuickFilter("NV");
      } else {
        setIsNVChecked(true);
        gridRef.current.api.setQuickFilter("");
      }
    } else {
      setIsRMChecked(false);
      setIsFGChecked(false);
      setIsNVChecked(false);
      gridRef.current.api.setQuickFilter("");
    }
  }, []);

  return (
    <>
      <ToastContainer limit={1} />
      <Layout userData={userData} blockScreen={blockScreen}>
        <div className="mr-20 md:mr-12 lg:mr-14">
          <div className="flex flex-row md:flex-col lg:flex-row justify-between">
            <div className="flex items-center">
              <div className="flex items-center gap-4">
                <div className="border border-light-gray rounded-md px-3 py-1">
                  <label className="flex items-center gap-2 cursor-pointer">
                    <input
                      type="radio"
                      name="requestType"
                      value="1"
                      className="form-radio w-5 h-5 cursor-pointer"
                      checked={typeId === 1}
                      onChange={handleTypeIdChange}
                    />
                    <span className="labels">Raw Materials</span>
                  </label>
                </div>
                {/* <label className="flex items-center gap-2">
                  <input
                   type="radio" 
                   name="requestType" 
                   value="2" 
                   className="form-radio" 
                   checked={typeId === 2}
                   onChange={handleTypeIdChange} 
                   />
                  <span>Finish Goods</span>
                </label> */}
                <div className="border border-light-gray rounded-md px-3 py-1">
                  <label className="flex items-center gap-2 cursor-pointer">
                    <input
                      type="radio"
                      name="requestType"
                      value="3"
                      className="form-radio w-5 h-5 cursor-pointer"
                      checked={typeId === 3}
                      onChange={handleTypeIdChange}
                    />
                    <span className="labels">New Variety</span>
                  </label>
                </div>
                {isIssUser && (
                  <div className="border border-light-gray rounded-md px-3 py-1">
                    <label className="flex items-center gap-2 cursor-pointer">
                      <input
                        type="radio"
                        name="requestType"
                        value="4"
                        className="form-radio w-5 h-5 cursor-pointer"
                        checked={typeId === 4}
                        onChange={handleTypeIdChange}
                      />
                      <span className="labels">Packaging</span>
                    </label>
                  </div>
                )}
              </div>
              <div className="labels" style={{ marginLeft: "10px" }}>
                {typeId === 3 && (
                  <Select
                    isMulti
                    options={statusOptions}
                    onChange={handleStatusChange}
                    placeholder="Select Status..."
                    className="basic-multi-select"
                    classNamePrefix="select"
                    value={nvStatus}
                    isSearchable={false}
                    styles={customSelectStyles}
                  />
                )}
              </div>
              {/* <button onClick={clearFilters} className="ml-4">
                Clear Filters
              </button> */}
              <div className="flex items-center px-3 w-auto md:w-fit lg:w-auto md:py-2 lg:py-1 md:mb-3 lg:mb-0">
                {/* <div className="flex">
                  <p className="p-0 ml-3 labels mr-4">Request type</p>
                  <input
                    id="RM"
                    type="checkbox"
                    value="RM"
                    className="w-5 h-5 text-blue border-theme-blue2 rounded mr-1"
                    style={{ borderColor: "#C8C6C6" }}
                    onChange={(e) => handleFilterProductType(e)}
                    checked={isRMChecked}
                  />
                  <label htmlFor="RM" className="mr-2">
                    RM
                  </label>
                  <input
                    id="FG"
                    type="checkbox"
                    value="FG"
                    className="w-5 h-5 text-blue border-theme-blue2 rounded mr-1"
                    style={{ borderColor: "#C8C6C6" }}
                    onChange={(e) => handleFilterProductType(e)}
                    checked={isFGChecked}
                  />
                  <label htmlFor="FG">FG</label>
                </div> */}
              </div>
            </div>
            <div className="flex items-center gap-6">
              <label className="relative block w-[47vh] text-gray-400 mt-0 pt-0">
                <span className="absolute px-4 py-1 pt-[0.4rem] 2xl:pt-1.5 text-black">
                  <FontAwesomeIcon icon={faSearch} className="fw-bold" />
                </span>
                <input
                  type="text"
                  id="filter-text-box"
                  placeholder="Search..."
                  onInput={onFilterTextBoxChanged}
                  value={searchInput}
                  className="block w-full px-4 pl-10 text-gray-500 placeholder-gray-400 searchbar border rounded-lg appearance-none form-input focus:outline-none"
                />
              </label>

              {/* <div>
                <button
                  className="button border border-skin-primary bg-skin-primary text-white rounded-md whitespace-nowrap"
                  onClick={handleFormType}
                >
                  Add Request
                </button>
                
            //   </div> */}
              <RequestDialoge
                isOpen={openDialog}
                onClose={closeDialog}
                handleFormType={handleFormType}
                selectedRequestType={selectedRequestType}
                isIssUser={isIssUser}
                isIssProcurmentUser={isIssProcurementTeamUser}
                handleRequestType={handleRequestType}
                admin={isIssAdmin}
              />
            </div>
          </div>
          <div className="my-5">
            <div
              className="relative ag-theme-alpine !rounded-md"
              style={{ height: "calc(100vh - 151px)" }}
            >
              <AgGridReact
                rowData={filteredRowData}
                ref={gridRef}
                columnDefs={columnDefs}
                defaultColDef={defaultColDef}
                suppressRowClickSelection
                pagination={true}
                paginationPageSize={pageSize}
                onPageSizeChanged={handlePageSizeChange}
                tooltipShowDelay={0}
                tooltipHideDelay={1000}
                onGridReady={handleGridReady}
                gridOptions={gridOptions}
              />
              <div className="flex justify-start mt-2 pagination-style">
                <label htmlFor="page-size-select pagination" className="inputs">
                  Show{" "}
                  <select
                    id="page-size-select"
                    onChange={handlePageSizeChange}
                    value={pageSize}
                    className="focus:outline-none"
                  >
                    <option value={10}>10</option>
                    <option value={15}>15</option>
                    <option value={25}>25</option>
                    <option value={50}>50</option>
                    <option value={100}>100</option>
                  </select>{" "}
                  Entries
                </label>
              </div>
            </div>
          </div>
        </div>
      </Layout>
    </>
  );
};

export default Products;

export const getServerSideProps = async (context) => {
  try {
    const { req, resolvedUrl } = context;
    const sessionId = req.cookies.thl_session;
    const previousPage = req.cookies.PreviousPage || "RM";

    if (!sessionId) {
      return {
        redirect: {
          destination: `/login?redirect=${encodeURIComponent(resolvedUrl)}`,
          permanent: false,
        },
      };
    }

    const apiBase =
      process.env.NEXT_PUBLIC_API_BASE_URL || "http://localhost:8081";

    const response = await fetch(`${apiBase}/api/auth/me`, {
      method: "GET",
      headers: {
        Cookie: `thl_session=${sessionId}`,
        "Content-Type": "application/json",
      },
    });

    if (!response.ok) {
      return {
        redirect: {
          destination: `/login?redirect=${encodeURIComponent(resolvedUrl)}`,
          permanent: false,
        },
      };
    }

    const { user } = await response.json();

    // Determine pageTypeId and PreviousPage value
    const pageTypeId =
      previousPage === "PK" ? 4 : previousPage === "NV" ? 3 : 1;

    return {
      props: {
        userData: user,
        PreviousPage: pageTypeId,
        pageTypeId,
      },
    };
  } catch (error) {
    console.error("Error in getServerSideProps:", error);
    return {
      redirect: {
        destination: "/login",
        permanent: false,
      },
    };
  }
};
