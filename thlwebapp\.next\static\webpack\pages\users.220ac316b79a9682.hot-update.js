"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/users",{

/***/ "./public/images/iss_logo.jpg":
/*!************************************!*\
  !*** ./public/images/iss_logo.jpg ***!
  \************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony default export */ __webpack_exports__[\"default\"] = ({\"src\":\"/_next/static/media/iss_logo.2dbf60f2.jpg\",\"height\":551,\"width\":1021,\"blurDataURL\":\"/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Fiss_logo.2dbf60f2.jpg&w=8&q=70\",\"blurWidth\":8,\"blurHeight\":4});//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9wdWJsaWMvaW1hZ2VzL2lzc19sb2dvLmpwZyIsIm1hcHBpbmdzIjoiO0FBQUEsK0RBQWUsQ0FBQyxxTUFBcU0iLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vcHVibGljL2ltYWdlcy9pc3NfbG9nby5qcGc/YmI5YSJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCB7XCJzcmNcIjpcIi9fbmV4dC9zdGF0aWMvbWVkaWEvaXNzX2xvZ28uMmRiZjYwZjIuanBnXCIsXCJoZWlnaHRcIjo1NTEsXCJ3aWR0aFwiOjEwMjEsXCJibHVyRGF0YVVSTFwiOlwiL19uZXh0L2ltYWdlP3VybD0lMkZfbmV4dCUyRnN0YXRpYyUyRm1lZGlhJTJGaXNzX2xvZ28uMmRiZjYwZjIuanBnJnc9OCZxPTcwXCIsXCJibHVyV2lkdGhcIjo4LFwiYmx1ckhlaWdodFwiOjR9OyJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///./public/images/iss_logo.jpg\n"));

/***/ }),

/***/ "./components/Layout.js":
/*!******************************!*\
  !*** ./components/Layout.js ***!
  \******************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getServerSideProps: function() { return /* binding */ getServerSideProps; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _Navbar__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./Navbar */ \"./components/Navbar.js\");\n/* harmony import */ var _Sidebar__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./Sidebar */ \"./components/Sidebar.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/navigation */ \"./node_modules/next/navigation.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(next_navigation__WEBPACK_IMPORTED_MODULE_5__);\n\nvar _s = $RefreshSig$();\n\n\n\n\n\nconst Layout = (param)=>{\n    let { children, userData, company, blockScreen } = param;\n    _s();\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_4__.useRouter)();\n    const currentPath = router.pathname;\n    const [showDesktopViewMessage, setShowDesktopViewMessage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_5__.usePathname)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const handleResize = ()=>{\n            if (window.innerWidth <= 767) {\n                setShowDesktopViewMessage(true);\n            } else {\n                setShowDesktopViewMessage(false);\n            }\n        };\n        window.addEventListener(\"resize\", handleResize);\n        handleResize();\n        return ()=>window.removeEventListener(\"resize\", handleResize);\n    }, []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        id: \"wrapper\",\n        children: showDesktopViewMessage ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"desktop-view-message\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"message-content\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        children: \"Please Open in Desktop View\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\Layout.js\",\n                        lineNumber: 32,\n                        columnNumber: 13\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        children: \"This website is best viewed on a desktop or laptop.\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\Layout.js\",\n                        lineNumber: 33,\n                        columnNumber: 13\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\Layout.js\",\n                lineNumber: 31,\n                columnNumber: 11\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\Layout.js\",\n            lineNumber: 30,\n            columnNumber: 9\n        }, undefined) : blockScreen ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"block-view-message\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"message-content\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            children: \"Service Unavailable\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\Layout.js\",\n                            lineNumber: 40,\n                            columnNumber: 15\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            children: \"We are currently experiencing issues and are working to resolve them as quickly as possible. Please check back later.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\Layout.js\",\n                            lineNumber: 41,\n                            columnNumber: 15\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\Layout.js\",\n                    lineNumber: 39,\n                    columnNumber: 13\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\Layout.js\",\n                lineNumber: 38,\n                columnNumber: 11\n            }, undefined)\n        }, void 0, false) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Sidebar__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                    userData: userData,\n                    companyName: company\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\Layout.js\",\n                    lineNumber: 50,\n                    columnNumber: 11\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"page-container\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Navbar__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                            userData: userData\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\Layout.js\",\n                            lineNumber: 52,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"main-root\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative md:ml-[55px] lg:ml-[60px] py-2 2xl:h-[calc(100%-60px)] \".concat(pathname == \"/whatif\" || pathname == \"/service_level\" ? \"w-[100%-70px] px-0 pl-3 mt-[45px]\" : \"w-full px-8 mt-[60px]\"),\n                                children: children\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\Layout.js\",\n                                lineNumber: 54,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\Layout.js\",\n                            lineNumber: 53,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\Layout.js\",\n                    lineNumber: 51,\n                    columnNumber: 11\n                }, undefined)\n            ]\n        }, void 0, true)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\Layout.js\",\n        lineNumber: 28,\n        columnNumber: 5\n    }, undefined);\n};\n_s(Layout, \"E7wJCME/M0kEq0xEwa8ayz4R7Ag=\", false, function() {\n    return [\n        next_router__WEBPACK_IMPORTED_MODULE_4__.useRouter,\n        next_navigation__WEBPACK_IMPORTED_MODULE_5__.usePathname\n    ];\n});\n_c = Layout;\n/* harmony default export */ __webpack_exports__[\"default\"] = (Layout);\nconst getServerSideProps = async (context)=>{\n    let userData = {};\n    let company = \"\";\n    try {\n        userData = JSON.parse(context.req.cookies.user || \"{}\");\n        company = JSON.parse(context.req.localStorage.company || \"\");\n    } catch (error) {\n        console.error(\"Error parsing userData:\", error);\n    }\n    return {\n        props: {\n            userData,\n            company\n        }\n    };\n};\nvar _c;\n$RefreshReg$(_c, \"Layout\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevExports = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevExports) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports on update so we can compare the boundary\n                // signatures.\n                module.hot.dispose(function (data) {\n                    data.prevExports = currentExports;\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevExports !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevExports, currentExports)) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevExports !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/Layout.js\n"));

/***/ }),

/***/ "./components/Navbar.js":
/*!******************************!*\
  !*** ./components/Navbar.js ***!
  \******************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   themes: function() { return /* binding */ themes; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _fortawesome_react_fontawesome__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @fortawesome/react-fontawesome */ \"./node_modules/@fortawesome/react-fontawesome/index.es.js\");\n/* harmony import */ var _fortawesome_free_solid_svg_icons__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @fortawesome/free-solid-svg-icons */ \"./node_modules/@fortawesome/free-solid-svg-icons/index.mjs\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/link */ \"./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _azure_msal_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @azure/msal-react */ \"./node_modules/@azure/msal-react/dist/index.js\");\n/* harmony import */ var _utils_theme_theme_switcher__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/utils/theme/theme-switcher */ \"./utils/theme/theme-switcher.js\");\n/* harmony import */ var js_cookie__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! js-cookie */ \"./node_modules/js-cookie/dist/js.cookie.mjs\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\nconst themes = [\n    {\n        bg: \"#022d71\",\n        text: \"#022d71\",\n        name: \"DPS\"\n    },\n    {\n        bg: \"#2e9b28\",\n        text: \"#2e9b28\",\n        name: \"EFC\"\n    },\n    {\n        bg: \"#a91e23\",\n        text: \"#a91e23\",\n        name: \"M&S\"\n    },\n    {\n        bg: \"#3d6546\",\n        text: \"#3d6546\",\n        name: \"FPP\"\n    }\n];\nconst Navbar = (param)=>{\n    let { userData } = param;\n    _s();\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_4__.useRouter)();\n    const [pageName, setPageName] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [currentRoute, setCurrentRoute] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const currentPath = router.pathname;\n        setCurrentRoute(currentPath);\n        if (currentPath === \"/finished-product-request/add\") {\n            js_cookie__WEBPACK_IMPORTED_MODULE_7__[\"default\"].set(\"PreviousPage\", \"FG\");\n            setPageName(\"Finished Goods Request\");\n        } else if (currentPath === \"/raw-material-request/add\") {\n            setPageName(\"Raw Material Request\");\n            js_cookie__WEBPACK_IMPORTED_MODULE_7__[\"default\"].set(\"PreviousPage\", \"RM\");\n        } else if (currentPath === \"/packaging-form/add\") {\n            setPageName(\"Packaging Request\");\n        } else if (currentPath === \"/variety/add\") {\n            js_cookie__WEBPACK_IMPORTED_MODULE_7__[\"default\"].set(\"PreviousPage\", \"NV\");\n            setPageName(\"New Variety Request\");\n        } else if (currentPath === \"/variety/[productId]/edit\") {\n            js_cookie__WEBPACK_IMPORTED_MODULE_7__[\"default\"].set(\"PreviousPage\", \"NV\");\n            setPageName(\"Edit New Variety Request\");\n        } else if (currentPath === \"/packaging-form/[productId]/edit\") {\n            js_cookie__WEBPACK_IMPORTED_MODULE_7__[\"default\"].set(\"PreviousPage\", \"PK\");\n            setPageName(\"Edit Packaging Request\");\n        } else if (currentPath === \"/raw-material-request/[productId]/edit\") {\n            js_cookie__WEBPACK_IMPORTED_MODULE_7__[\"default\"].set(\"PreviousPage\", \"RM\");\n            setPageName(\"Edit Raw Material Request\");\n        } else if (currentPath === \"/finished-product-request/[productId]/edit\") {\n            js_cookie__WEBPACK_IMPORTED_MODULE_7__[\"default\"].set(\"PreviousPage\", \"FG\");\n            setPageName(\"Edit Finished Goods Request\");\n        } else if (currentPath === null || currentPath === void 0 ? void 0 : currentPath.endsWith(\"/edit\")) {\n            setPageName(\"Edit Supplier\");\n        } else if (currentPath === null || currentPath === void 0 ? void 0 : currentPath.endsWith(\"/add\")) {\n            setPageName(\"Add Supplier\");\n        } else if (currentPath === null || currentPath === void 0 ? void 0 : currentPath.endsWith(\"/edit/forms\")) {\n            setPageName(\"Supplier Form\");\n        } else if (currentPath === null || currentPath === void 0 ? void 0 : currentPath.endsWith(\"/confirm\")) {\n            setPageName(\"Confirm Details for Supplier\");\n        } else if (currentPath === \"/suppliers\") {\n            setPageName(\"Suppliers\");\n        } else if (currentPath === \"/users\") {\n            setPageName(\"User Management\");\n        } else if (currentPath === \"/viewlogs\") {\n            setPageName(\"View Logs\");\n        } else if (currentPath === \"/products\") {\n            js_cookie__WEBPACK_IMPORTED_MODULE_7__[\"default\"].set(\"PreviousPage\", \"PK\");\n            setPageName(\"Products\");\n        } else if (currentPath === \"/variety\") {\n            setPageName(\"Variety\");\n            js_cookie__WEBPACK_IMPORTED_MODULE_7__[\"default\"].set(\"PreviousPage\", \"NV\");\n        } else if (currentPath === \"/finishedProductRequest\") {\n            js_cookie__WEBPACK_IMPORTED_MODULE_7__[\"default\"].set(\"PreviousPage\", \"FG\");\n            setPageName(\"Finished Product Request\");\n        } else if (currentPath === \"/rawMaterialRequest\") {\n            js_cookie__WEBPACK_IMPORTED_MODULE_7__[\"default\"].set(\"PreviousPage\", \"RM\");\n            setPageName(\"Raw Material Request\");\n        } else if (currentPath === \"/whatif\") {\n            setPageName(\"Whatif\");\n        } else if (currentPath === \"/service_level\" || currentPath === \"/service_level/reports/masterForcast\") {\n            setPageName(\"Service Level\");\n        }\n    }, [\n        router.pathname\n    ]);\n    const baseCompanyOptions = [\n        {\n            value: \"dpsltd\",\n            label: \"DPS\"\n        },\n        {\n            value: \"dpsltdms\",\n            label: \"DPS M&S\"\n        },\n        {\n            value: \"efcltd\",\n            label: \"EFC\"\n        },\n        {\n            value: \"fpp-ltd\",\n            label: \"FPP\"\n        }\n    ];\n    const companyOptions = [\n        ...baseCompanyOptions,\n        ...(userData === null || userData === void 0 ? void 0 : userData.role_id) === 6 ? [\n            {\n                value: \"issproduce\",\n                label: \"ISS\"\n            },\n            {\n                value: \"flrs\",\n                label: \"FLRS\",\n                disabled: true\n            },\n            {\n                value: \"thl\",\n                label: \"THL\",\n                disabled: true\n            }\n        ] : []\n    ];\n    const [selectedCompany, setSelectedCompany] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(js_cookie__WEBPACK_IMPORTED_MODULE_7__[\"default\"].get(\"ADCompanyName\") == \"DPS MS\" ? \"dpsltdms\" : js_cookie__WEBPACK_IMPORTED_MODULE_7__[\"default\"].get(\"company\") || \"\");\n    const handleCompanyChange = (event)=>{\n        console.log(\"e.target.value\", event.target.value);\n        const company = event.target.value;\n        if (company == \"dpsltd\") {\n            js_cookie__WEBPACK_IMPORTED_MODULE_7__[\"default\"].set(\"theme\", \"#022D71\");\n            js_cookie__WEBPACK_IMPORTED_MODULE_7__[\"default\"].set(\"ADCompanyName\", \"DPS\");\n            setSelectedCompany(company);\n            js_cookie__WEBPACK_IMPORTED_MODULE_7__[\"default\"].set(\"company\", company);\n        } else if (company == \"dpsltdms\") {\n            js_cookie__WEBPACK_IMPORTED_MODULE_7__[\"default\"].set(\"theme\", \"#0d6bfc\");\n            js_cookie__WEBPACK_IMPORTED_MODULE_7__[\"default\"].set(\"ADCompanyName\", \"DPS MS\");\n            setSelectedCompany(\"dpsltd\");\n            js_cookie__WEBPACK_IMPORTED_MODULE_7__[\"default\"].set(\"company\", \"dpsltd\");\n        } else if (company == \"efcltd\") {\n            js_cookie__WEBPACK_IMPORTED_MODULE_7__[\"default\"].set(\"theme\", \"#3eab58\");\n            js_cookie__WEBPACK_IMPORTED_MODULE_7__[\"default\"].set(\"ADCompanyName\", \"EFC\");\n            setSelectedCompany(company);\n            js_cookie__WEBPACK_IMPORTED_MODULE_7__[\"default\"].set(\"company\", company);\n        } else if (company == \"fpp-ltd\") {\n            js_cookie__WEBPACK_IMPORTED_MODULE_7__[\"default\"].set(\"theme\", \"#3d6546\");\n            js_cookie__WEBPACK_IMPORTED_MODULE_7__[\"default\"].set(\"ADCompanyName\", \"Fresh Produce Partners\");\n            setSelectedCompany(company);\n            js_cookie__WEBPACK_IMPORTED_MODULE_7__[\"default\"].set(\"company\", company);\n        } else if (company == \"issproduce\") {\n            js_cookie__WEBPACK_IMPORTED_MODULE_7__[\"default\"].set(\"theme\", \"#ABC400\");\n            js_cookie__WEBPACK_IMPORTED_MODULE_7__[\"default\"].set(\"ADCompanyName\", \"Integrated Service Solutions Ltd\");\n            setSelectedCompany(company);\n            js_cookie__WEBPACK_IMPORTED_MODULE_7__[\"default\"].set(\"company\", company);\n        }\n        router.reload();\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"titlebar\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex flex-row justify-between w-full bg-skin-primary h-14\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-row w-full items-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"page-heading cursor-pointer\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fortawesome_react_fontawesome__WEBPACK_IMPORTED_MODULE_2__.FontAwesomeIcon, {\n                                        icon: _fortawesome_free_solid_svg_icons__WEBPACK_IMPORTED_MODULE_8__.faChevronLeft,\n                                        className: \"pageName text-white\",\n                                        onClick: ()=>router.back()\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\Navbar.js\",\n                                        lineNumber: 146,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                        href: currentRoute,\n                                        className: \"ml-4 2xl:text-lg font-poppinssemibold pageName text-white tracking-wide\",\n                                        children: pageName\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\Navbar.js\",\n                                        lineNumber: 151,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\Navbar.js\",\n                                lineNumber: 145,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_utils_theme_theme_switcher__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                className: \"ml-5\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\Navbar.js\",\n                                lineNumber: 158,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\Navbar.js\",\n                        lineNumber: 144,\n                        columnNumber: 11\n                    }, undefined),\n                    ((userData === null || userData === void 0 ? void 0 : userData.role_id) == 5 || (userData === null || userData === void 0 ? void 0 : userData.role_id) == 6) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-row justify-end w-1/2 items-center mr-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                            value: selectedCompany,\n                            onChange: handleCompanyChange,\n                            className: \"bg-white text-black rounded\",\n                            children: companyOptions.map((opt)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                    value: opt.value,\n                                    disabled: opt.disabled,\n                                    children: opt.label\n                                }, opt.value, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\Navbar.js\",\n                                    lineNumber: 168,\n                                    columnNumber: 19\n                                }, undefined))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\Navbar.js\",\n                            lineNumber: 162,\n                            columnNumber: 15\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\Navbar.js\",\n                        lineNumber: 161,\n                        columnNumber: 13\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\Navbar.js\",\n                lineNumber: 143,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\Navbar.js\",\n            lineNumber: 142,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\Navbar.js\",\n        lineNumber: 141,\n        columnNumber: 5\n    }, undefined);\n};\n_s(Navbar, \"8bgZbm+HleyJfidZ9YLsifOPi/U=\", false, function() {\n    return [\n        next_router__WEBPACK_IMPORTED_MODULE_4__.useRouter\n    ];\n});\n_c = Navbar;\n/* harmony default export */ __webpack_exports__[\"default\"] = (Navbar);\nvar _c;\n$RefreshReg$(_c, \"Navbar\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevExports = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevExports) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports on update so we can compare the boundary\n                // signatures.\n                module.hot.dispose(function (data) {\n                    data.prevExports = currentExports;\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevExports !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevExports, currentExports)) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevExports !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9jb21wb25lbnRzL05hdmJhci5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBQTRDO0FBQ3FCO0FBQ0M7QUFDckM7QUFFVztBQUNJO0FBQ2E7QUFDekI7QUFDekIsTUFBTVMsU0FBUztJQUNwQjtRQUFFQyxJQUFJO1FBQVdDLE1BQU07UUFBV0MsTUFBTTtJQUFNO0lBQzlDO1FBQUVGLElBQUk7UUFBV0MsTUFBTTtRQUFXQyxNQUFNO0lBQU07SUFDOUM7UUFBRUYsSUFBSTtRQUFXQyxNQUFNO1FBQVdDLE1BQU07SUFBTTtJQUM5QztRQUFFRixJQUFJO1FBQVdDLE1BQU07UUFBV0MsTUFBTTtJQUFNO0NBQy9DLENBQUM7QUFFRixNQUFNQyxTQUFTO1FBQUMsRUFBRUMsUUFBUSxFQUFFOztJQUMxQixNQUFNQyxTQUFTVixzREFBU0E7SUFDeEIsTUFBTSxDQUFDVyxVQUFVQyxZQUFZLEdBQUdqQiwrQ0FBUUEsQ0FBQztJQUN6QyxNQUFNLENBQUNrQixjQUFjQyxnQkFBZ0IsR0FBR25CLCtDQUFRQSxDQUFDO0lBRWpEQyxnREFBU0EsQ0FBQztRQUNSLE1BQU1tQixjQUFjTCxPQUFPTSxRQUFRO1FBQ25DRixnQkFBZ0JDO1FBQ2hCLElBQUlBLGdCQUFnQixpQ0FBaUM7WUFDbkRaLHFEQUFXLENBQUMsZ0JBQWdCO1lBQzVCUyxZQUFZO1FBQ2QsT0FBTyxJQUFJRyxnQkFBZ0IsNkJBQTZCO1lBQ3RESCxZQUFZO1lBQ1pULHFEQUFXLENBQUMsZ0JBQWdCO1FBQzlCLE9BQU8sSUFBSVksZ0JBQWdCLHVCQUF1QjtZQUNoREgsWUFBWTtRQUNkLE9BQU8sSUFBSUcsZ0JBQWdCLGdCQUFnQjtZQUN6Q1oscURBQVcsQ0FBQyxnQkFBZ0I7WUFDNUJTLFlBQVk7UUFDZCxPQUFPLElBQUlHLGdCQUFnQiw2QkFBNkI7WUFDdERaLHFEQUFXLENBQUMsZ0JBQWdCO1lBQzVCUyxZQUFZO1FBQ2QsT0FBTyxJQUFJRyxnQkFBZ0Isb0NBQW9DO1lBQzdEWixxREFBVyxDQUFDLGdCQUFnQjtZQUM1QlMsWUFBWTtRQUNkLE9BQU8sSUFBSUcsZ0JBQWdCLDBDQUEwQztZQUNuRVoscURBQVcsQ0FBQyxnQkFBZ0I7WUFDNUJTLFlBQVk7UUFDZCxPQUFPLElBQUlHLGdCQUFnQiw4Q0FBOEM7WUFDdkVaLHFEQUFXLENBQUMsZ0JBQWdCO1lBQzVCUyxZQUFZO1FBQ2QsT0FBTyxJQUFJRyx3QkFBQUEsa0NBQUFBLFlBQWFHLFFBQVEsQ0FBQyxVQUFVO1lBQ3pDTixZQUFhO1FBQ2YsT0FBTyxJQUFJRyx3QkFBQUEsa0NBQUFBLFlBQWFHLFFBQVEsQ0FBQyxTQUFTO1lBQ3hDTixZQUFZO1FBQ2QsT0FBTyxJQUFJRyx3QkFBQUEsa0NBQUFBLFlBQWFHLFFBQVEsQ0FBQyxnQkFBZ0I7WUFDL0NOLFlBQWE7UUFDZixPQUFPLElBQUlHLHdCQUFBQSxrQ0FBQUEsWUFBYUcsUUFBUSxDQUFDLGFBQWE7WUFDNUNOLFlBQWE7UUFDZixPQUFPLElBQUlHLGdCQUFnQixjQUFjO1lBQ3ZDSCxZQUFZO1FBQ2QsT0FBTyxJQUFJRyxnQkFBZ0IsVUFBVTtZQUNuQ0gsWUFBWTtRQUNkLE9BQU8sSUFBSUcsZ0JBQWdCLGFBQWE7WUFDdENILFlBQVk7UUFDZCxPQUFPLElBQUlHLGdCQUFnQixhQUFhO1lBQ3RDWixxREFBVyxDQUFDLGdCQUFnQjtZQUM1QlMsWUFBWTtRQUNkLE9BQU8sSUFBSUcsZ0JBQWdCLFlBQVk7WUFDckNILFlBQVk7WUFDWlQscURBQVcsQ0FBQyxnQkFBZ0I7UUFDOUIsT0FBTyxJQUFJWSxnQkFBZ0IsMkJBQTJCO1lBQ3BEWixxREFBVyxDQUFDLGdCQUFnQjtZQUM1QlMsWUFBWTtRQUNkLE9BQU8sSUFBSUcsZ0JBQWdCLHVCQUF1QjtZQUNoRFoscURBQVcsQ0FBQyxnQkFBZ0I7WUFDNUJTLFlBQVk7UUFDZCxPQUFPLElBQUlHLGdCQUFnQixXQUFXO1lBQ3BDSCxZQUFZO1FBQ2QsT0FBTyxJQUNMRyxnQkFBZ0Isb0JBQ2hCQSxnQkFBZ0Isd0NBQ2hCO1lBQ0FILFlBQVk7UUFDZDtJQUNGLEdBQUc7UUFBQ0YsT0FBT00sUUFBUTtLQUFDO0lBRXBCLE1BQU1HLHFCQUFxQjtRQUN6QjtZQUFFQyxPQUFPO1lBQVVDLE9BQU87UUFBTTtRQUNoQztZQUFFRCxPQUFPO1lBQVlDLE9BQU87UUFBVTtRQUN0QztZQUFFRCxPQUFPO1lBQVVDLE9BQU87UUFBTTtRQUNoQztZQUFFRCxPQUFPO1lBQVdDLE9BQU87UUFBTTtLQUNsQztJQUVELE1BQU1DLGlCQUFpQjtXQUNsQkg7V0FDQ1YsQ0FBQUEscUJBQUFBLCtCQUFBQSxTQUFVYyxPQUFPLE1BQUssSUFDdEI7WUFDRTtnQkFBRUgsT0FBTztnQkFBY0MsT0FBTztZQUFNO1lBQ3BDO2dCQUFFRCxPQUFPO2dCQUFRQyxPQUFPO2dCQUFRRyxVQUFVO1lBQUs7WUFDL0M7Z0JBQUVKLE9BQU87Z0JBQU9DLE9BQU87Z0JBQU9HLFVBQVU7WUFBSztTQUM5QyxHQUNELEVBQUU7S0FDUDtJQUVELE1BQU0sQ0FBQ0MsaUJBQWlCQyxtQkFBbUIsR0FBRy9CLCtDQUFRQSxDQUNwRFEscURBQVcsQ0FBQyxvQkFBb0IsV0FDNUIsYUFDQUEscURBQVcsQ0FBQyxjQUFjO0lBR2hDLE1BQU15QixzQkFBc0IsQ0FBQ0M7UUFDM0JDLFFBQVFDLEdBQUcsQ0FBQyxrQkFBa0JGLE1BQU1HLE1BQU0sQ0FBQ1osS0FBSztRQUNoRCxNQUFNYSxVQUFVSixNQUFNRyxNQUFNLENBQUNaLEtBQUs7UUFDbEMsSUFBSWEsV0FBVyxVQUFVO1lBQ3ZCOUIscURBQVcsQ0FBQyxTQUFTO1lBQ3JCQSxxREFBVyxDQUFDLGlCQUFpQjtZQUM3QnVCLG1CQUFtQk87WUFDbkI5QixxREFBVyxDQUFDLFdBQVc4QjtRQUN6QixPQUFPLElBQUlBLFdBQVcsWUFBWTtZQUNoQzlCLHFEQUFXLENBQUMsU0FBUztZQUNyQkEscURBQVcsQ0FBQyxpQkFBaUI7WUFDN0J1QixtQkFBbUI7WUFDbkJ2QixxREFBVyxDQUFDLFdBQVc7UUFDekIsT0FBTyxJQUFJOEIsV0FBVyxVQUFVO1lBQzlCOUIscURBQVcsQ0FBQyxTQUFTO1lBQ3JCQSxxREFBVyxDQUFDLGlCQUFpQjtZQUM3QnVCLG1CQUFtQk87WUFDbkI5QixxREFBVyxDQUFDLFdBQVc4QjtRQUN6QixPQUFPLElBQUlBLFdBQVcsV0FBVztZQUMvQjlCLHFEQUFXLENBQUMsU0FBUztZQUNyQkEscURBQVcsQ0FBQyxpQkFBaUI7WUFDN0J1QixtQkFBbUJPO1lBQ25COUIscURBQVcsQ0FBQyxXQUFXOEI7UUFDekIsT0FBTyxJQUFJQSxXQUFXLGNBQWM7WUFDbEM5QixxREFBVyxDQUFDLFNBQVM7WUFDckJBLHFEQUFXLENBQUMsaUJBQWlCO1lBQzdCdUIsbUJBQW1CTztZQUNuQjlCLHFEQUFXLENBQUMsV0FBVzhCO1FBQ3pCO1FBQ0F2QixPQUFPd0IsTUFBTTtJQUNmO0lBRUEscUJBQ0UsOERBQUNDO2tCQUNDLDRFQUFDQztZQUFJQyxXQUFVO3NCQUNiLDRFQUFDRDtnQkFBSUMsV0FBVTs7a0NBQ2IsOERBQUNEO3dCQUFJQyxXQUFVOzswQ0FDYiw4REFBQ0Q7Z0NBQUlDLFdBQVU7O2tEQUNiLDhEQUFDeEMsMkVBQWVBO3dDQUNkeUMsTUFBTXhDLDRFQUFhQTt3Q0FDbkJ1QyxXQUFVO3dDQUNWRSxTQUFTLElBQU03QixPQUFPOEIsSUFBSTs7Ozs7O2tEQUU1Qiw4REFBQ3pDLGtEQUFJQTt3Q0FDSDBDLE1BQU01Qjt3Q0FDTndCLFdBQVU7a0RBRVQxQjs7Ozs7Ozs7Ozs7OzBDQUdMLDhEQUFDVCxtRUFBYUE7Z0NBQUNtQyxXQUFVOzs7Ozs7Ozs7Ozs7b0JBRXpCNUIsQ0FBQUEsQ0FBQUEscUJBQUFBLCtCQUFBQSxTQUFVYyxPQUFPLEtBQUksS0FBS2QsQ0FBQUEscUJBQUFBLCtCQUFBQSxTQUFVYyxPQUFPLEtBQUksb0JBQy9DLDhEQUFDYTt3QkFBSUMsV0FBVTtrQ0FDYiw0RUFBQ0s7NEJBQ0N0QixPQUFPSzs0QkFDUGtCLFVBQVVmOzRCQUNWUyxXQUFVO3NDQUVUZixlQUFlc0IsR0FBRyxDQUFDLENBQUNDLG9CQUNuQiw4REFBQ0M7b0NBRUMxQixPQUFPeUIsSUFBSXpCLEtBQUs7b0NBQ2hCSSxVQUFVcUIsSUFBSXJCLFFBQVE7OENBRXJCcUIsSUFBSXhCLEtBQUs7bUNBSkx3QixJQUFJekIsS0FBSzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQWNsQztHQXRLTVo7O1FBQ1dSLGtEQUFTQTs7O0tBRHBCUTtBQXdLTiwrREFBZUEsTUFBTUEsRUFBQyIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9jb21wb25lbnRzL05hdmJhci5qcz9mYmNhIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IHVzZVN0YXRlLCB1c2VFZmZlY3QgfSBmcm9tIFwicmVhY3RcIjtcclxuaW1wb3J0IHsgRm9udEF3ZXNvbWVJY29uIH0gZnJvbSBcIkBmb3J0YXdlc29tZS9yZWFjdC1mb250YXdlc29tZVwiO1xyXG5pbXBvcnQgeyBmYUNoZXZyb25MZWZ0IH0gZnJvbSBcIkBmb3J0YXdlc29tZS9mcmVlLXNvbGlkLXN2Zy1pY29uc1wiO1xyXG5pbXBvcnQgTGluayBmcm9tIFwibmV4dC9saW5rXCI7XHJcblxyXG5pbXBvcnQgeyB1c2VSb3V0ZXIgfSBmcm9tIFwibmV4dC9yb3V0ZXJcIjtcclxuaW1wb3J0IHsgdXNlTXNhbCB9IGZyb20gXCJAYXp1cmUvbXNhbC1yZWFjdFwiO1xyXG5pbXBvcnQgVGhlbWVTd2l0Y2hlciBmcm9tIFwiQC91dGlscy90aGVtZS90aGVtZS1zd2l0Y2hlclwiO1xyXG5pbXBvcnQgQ29va2llcyBmcm9tIFwianMtY29va2llXCI7XHJcbmV4cG9ydCBjb25zdCB0aGVtZXMgPSBbXHJcbiAgeyBiZzogXCIjMDIyZDcxXCIsIHRleHQ6IFwiIzAyMmQ3MVwiLCBuYW1lOiBcIkRQU1wiIH0sXHJcbiAgeyBiZzogXCIjMmU5YjI4XCIsIHRleHQ6IFwiIzJlOWIyOFwiLCBuYW1lOiBcIkVGQ1wiIH0sXHJcbiAgeyBiZzogXCIjYTkxZTIzXCIsIHRleHQ6IFwiI2E5MWUyM1wiLCBuYW1lOiBcIk0mU1wiIH0sXHJcbiAgeyBiZzogXCIjM2Q2NTQ2XCIsIHRleHQ6IFwiIzNkNjU0NlwiLCBuYW1lOiBcIkZQUFwiIH0sXHJcbl07XHJcblxyXG5jb25zdCBOYXZiYXIgPSAoeyB1c2VyRGF0YSB9KSA9PiB7XHJcbiAgY29uc3Qgcm91dGVyID0gdXNlUm91dGVyKCk7XHJcbiAgY29uc3QgW3BhZ2VOYW1lLCBzZXRQYWdlTmFtZV0gPSB1c2VTdGF0ZShcIlwiKTtcclxuICBjb25zdCBbY3VycmVudFJvdXRlLCBzZXRDdXJyZW50Um91dGVdID0gdXNlU3RhdGUoXCJcIik7XHJcblxyXG4gIHVzZUVmZmVjdCgoKSA9PiB7XHJcbiAgICBjb25zdCBjdXJyZW50UGF0aCA9IHJvdXRlci5wYXRobmFtZTtcclxuICAgIHNldEN1cnJlbnRSb3V0ZShjdXJyZW50UGF0aCk7XHJcbiAgICBpZiAoY3VycmVudFBhdGggPT09IFwiL2ZpbmlzaGVkLXByb2R1Y3QtcmVxdWVzdC9hZGRcIikge1xyXG4gICAgICBDb29raWVzLnNldChcIlByZXZpb3VzUGFnZVwiLCBcIkZHXCIpO1xyXG4gICAgICBzZXRQYWdlTmFtZShcIkZpbmlzaGVkIEdvb2RzIFJlcXVlc3RcIik7XHJcbiAgICB9IGVsc2UgaWYgKGN1cnJlbnRQYXRoID09PSBcIi9yYXctbWF0ZXJpYWwtcmVxdWVzdC9hZGRcIikge1xyXG4gICAgICBzZXRQYWdlTmFtZShcIlJhdyBNYXRlcmlhbCBSZXF1ZXN0XCIpO1xyXG4gICAgICBDb29raWVzLnNldChcIlByZXZpb3VzUGFnZVwiLCBcIlJNXCIpO1xyXG4gICAgfSBlbHNlIGlmIChjdXJyZW50UGF0aCA9PT0gXCIvcGFja2FnaW5nLWZvcm0vYWRkXCIpIHtcclxuICAgICAgc2V0UGFnZU5hbWUoXCJQYWNrYWdpbmcgUmVxdWVzdFwiKTtcclxuICAgIH0gZWxzZSBpZiAoY3VycmVudFBhdGggPT09IFwiL3ZhcmlldHkvYWRkXCIpIHtcclxuICAgICAgQ29va2llcy5zZXQoXCJQcmV2aW91c1BhZ2VcIiwgXCJOVlwiKTtcclxuICAgICAgc2V0UGFnZU5hbWUoXCJOZXcgVmFyaWV0eSBSZXF1ZXN0XCIpO1xyXG4gICAgfSBlbHNlIGlmIChjdXJyZW50UGF0aCA9PT0gXCIvdmFyaWV0eS9bcHJvZHVjdElkXS9lZGl0XCIpIHtcclxuICAgICAgQ29va2llcy5zZXQoXCJQcmV2aW91c1BhZ2VcIiwgXCJOVlwiKTtcclxuICAgICAgc2V0UGFnZU5hbWUoXCJFZGl0IE5ldyBWYXJpZXR5IFJlcXVlc3RcIik7XHJcbiAgICB9IGVsc2UgaWYgKGN1cnJlbnRQYXRoID09PSBcIi9wYWNrYWdpbmctZm9ybS9bcHJvZHVjdElkXS9lZGl0XCIpIHtcclxuICAgICAgQ29va2llcy5zZXQoXCJQcmV2aW91c1BhZ2VcIiwgXCJQS1wiKTtcclxuICAgICAgc2V0UGFnZU5hbWUoXCJFZGl0IFBhY2thZ2luZyBSZXF1ZXN0XCIpO1xyXG4gICAgfSBlbHNlIGlmIChjdXJyZW50UGF0aCA9PT0gXCIvcmF3LW1hdGVyaWFsLXJlcXVlc3QvW3Byb2R1Y3RJZF0vZWRpdFwiKSB7XHJcbiAgICAgIENvb2tpZXMuc2V0KFwiUHJldmlvdXNQYWdlXCIsIFwiUk1cIik7XHJcbiAgICAgIHNldFBhZ2VOYW1lKFwiRWRpdCBSYXcgTWF0ZXJpYWwgUmVxdWVzdFwiKTtcclxuICAgIH0gZWxzZSBpZiAoY3VycmVudFBhdGggPT09IFwiL2ZpbmlzaGVkLXByb2R1Y3QtcmVxdWVzdC9bcHJvZHVjdElkXS9lZGl0XCIpIHtcclxuICAgICAgQ29va2llcy5zZXQoXCJQcmV2aW91c1BhZ2VcIiwgXCJGR1wiKTtcclxuICAgICAgc2V0UGFnZU5hbWUoXCJFZGl0IEZpbmlzaGVkIEdvb2RzIFJlcXVlc3RcIik7XHJcbiAgICB9IGVsc2UgaWYgKGN1cnJlbnRQYXRoPy5lbmRzV2l0aChcIi9lZGl0XCIpKSB7XHJcbiAgICAgIHNldFBhZ2VOYW1lKGBFZGl0IFN1cHBsaWVyYCk7XHJcbiAgICB9IGVsc2UgaWYgKGN1cnJlbnRQYXRoPy5lbmRzV2l0aChcIi9hZGRcIikpIHtcclxuICAgICAgc2V0UGFnZU5hbWUoXCJBZGQgU3VwcGxpZXJcIik7XHJcbiAgICB9IGVsc2UgaWYgKGN1cnJlbnRQYXRoPy5lbmRzV2l0aChcIi9lZGl0L2Zvcm1zXCIpKSB7XHJcbiAgICAgIHNldFBhZ2VOYW1lKGBTdXBwbGllciBGb3JtYCk7XHJcbiAgICB9IGVsc2UgaWYgKGN1cnJlbnRQYXRoPy5lbmRzV2l0aChcIi9jb25maXJtXCIpKSB7XHJcbiAgICAgIHNldFBhZ2VOYW1lKGBDb25maXJtIERldGFpbHMgZm9yIFN1cHBsaWVyYCk7XHJcbiAgICB9IGVsc2UgaWYgKGN1cnJlbnRQYXRoID09PSBcIi9zdXBwbGllcnNcIikge1xyXG4gICAgICBzZXRQYWdlTmFtZShcIlN1cHBsaWVyc1wiKTtcclxuICAgIH0gZWxzZSBpZiAoY3VycmVudFBhdGggPT09IFwiL3VzZXJzXCIpIHtcclxuICAgICAgc2V0UGFnZU5hbWUoXCJVc2VyIE1hbmFnZW1lbnRcIik7XHJcbiAgICB9IGVsc2UgaWYgKGN1cnJlbnRQYXRoID09PSBcIi92aWV3bG9nc1wiKSB7XHJcbiAgICAgIHNldFBhZ2VOYW1lKFwiVmlldyBMb2dzXCIpO1xyXG4gICAgfSBlbHNlIGlmIChjdXJyZW50UGF0aCA9PT0gXCIvcHJvZHVjdHNcIikge1xyXG4gICAgICBDb29raWVzLnNldChcIlByZXZpb3VzUGFnZVwiLCBcIlBLXCIpO1xyXG4gICAgICBzZXRQYWdlTmFtZShcIlByb2R1Y3RzXCIpO1xyXG4gICAgfSBlbHNlIGlmIChjdXJyZW50UGF0aCA9PT0gXCIvdmFyaWV0eVwiKSB7XHJcbiAgICAgIHNldFBhZ2VOYW1lKFwiVmFyaWV0eVwiKTtcclxuICAgICAgQ29va2llcy5zZXQoXCJQcmV2aW91c1BhZ2VcIiwgXCJOVlwiKTtcclxuICAgIH0gZWxzZSBpZiAoY3VycmVudFBhdGggPT09IFwiL2ZpbmlzaGVkUHJvZHVjdFJlcXVlc3RcIikge1xyXG4gICAgICBDb29raWVzLnNldChcIlByZXZpb3VzUGFnZVwiLCBcIkZHXCIpO1xyXG4gICAgICBzZXRQYWdlTmFtZShcIkZpbmlzaGVkIFByb2R1Y3QgUmVxdWVzdFwiKTtcclxuICAgIH0gZWxzZSBpZiAoY3VycmVudFBhdGggPT09IFwiL3Jhd01hdGVyaWFsUmVxdWVzdFwiKSB7XHJcbiAgICAgIENvb2tpZXMuc2V0KFwiUHJldmlvdXNQYWdlXCIsIFwiUk1cIik7XHJcbiAgICAgIHNldFBhZ2VOYW1lKFwiUmF3IE1hdGVyaWFsIFJlcXVlc3RcIik7XHJcbiAgICB9IGVsc2UgaWYgKGN1cnJlbnRQYXRoID09PSBcIi93aGF0aWZcIikge1xyXG4gICAgICBzZXRQYWdlTmFtZShcIldoYXRpZlwiKTtcclxuICAgIH0gZWxzZSBpZiAoXHJcbiAgICAgIGN1cnJlbnRQYXRoID09PSBcIi9zZXJ2aWNlX2xldmVsXCIgfHxcclxuICAgICAgY3VycmVudFBhdGggPT09IFwiL3NlcnZpY2VfbGV2ZWwvcmVwb3J0cy9tYXN0ZXJGb3JjYXN0XCJcclxuICAgICkge1xyXG4gICAgICBzZXRQYWdlTmFtZShcIlNlcnZpY2UgTGV2ZWxcIik7XHJcbiAgICB9XHJcbiAgfSwgW3JvdXRlci5wYXRobmFtZV0pO1xyXG5cclxuICBjb25zdCBiYXNlQ29tcGFueU9wdGlvbnMgPSBbXHJcbiAgICB7IHZhbHVlOiBcImRwc2x0ZFwiLCBsYWJlbDogXCJEUFNcIiB9LFxyXG4gICAgeyB2YWx1ZTogXCJkcHNsdGRtc1wiLCBsYWJlbDogXCJEUFMgTSZTXCIgfSxcclxuICAgIHsgdmFsdWU6IFwiZWZjbHRkXCIsIGxhYmVsOiBcIkVGQ1wiIH0sXHJcbiAgICB7IHZhbHVlOiBcImZwcC1sdGRcIiwgbGFiZWw6IFwiRlBQXCIgfSxcclxuICBdO1xyXG5cclxuICBjb25zdCBjb21wYW55T3B0aW9ucyA9IFtcclxuICAgIC4uLmJhc2VDb21wYW55T3B0aW9ucyxcclxuICAgIC4uLih1c2VyRGF0YT8ucm9sZV9pZCA9PT0gNlxyXG4gICAgICA/IFtcclxuICAgICAgICAgIHsgdmFsdWU6IFwiaXNzcHJvZHVjZVwiLCBsYWJlbDogXCJJU1NcIiB9LFxyXG4gICAgICAgICAgeyB2YWx1ZTogXCJmbHJzXCIsIGxhYmVsOiBcIkZMUlNcIiwgZGlzYWJsZWQ6IHRydWUgfSxcclxuICAgICAgICAgIHsgdmFsdWU6IFwidGhsXCIsIGxhYmVsOiBcIlRITFwiLCBkaXNhYmxlZDogdHJ1ZSB9LFxyXG4gICAgICAgIF1cclxuICAgICAgOiBbXSksXHJcbiAgXTtcclxuXHJcbiAgY29uc3QgW3NlbGVjdGVkQ29tcGFueSwgc2V0U2VsZWN0ZWRDb21wYW55XSA9IHVzZVN0YXRlKFxyXG4gICAgQ29va2llcy5nZXQoXCJBRENvbXBhbnlOYW1lXCIpID09IFwiRFBTIE1TXCJcclxuICAgICAgPyBcImRwc2x0ZG1zXCJcclxuICAgICAgOiBDb29raWVzLmdldChcImNvbXBhbnlcIikgfHwgXCJcIlxyXG4gICk7XHJcblxyXG4gIGNvbnN0IGhhbmRsZUNvbXBhbnlDaGFuZ2UgPSAoZXZlbnQpID0+IHtcclxuICAgIGNvbnNvbGUubG9nKFwiZS50YXJnZXQudmFsdWVcIiwgZXZlbnQudGFyZ2V0LnZhbHVlKTtcclxuICAgIGNvbnN0IGNvbXBhbnkgPSBldmVudC50YXJnZXQudmFsdWU7XHJcbiAgICBpZiAoY29tcGFueSA9PSBcImRwc2x0ZFwiKSB7XHJcbiAgICAgIENvb2tpZXMuc2V0KFwidGhlbWVcIiwgXCIjMDIyRDcxXCIpO1xyXG4gICAgICBDb29raWVzLnNldChcIkFEQ29tcGFueU5hbWVcIiwgXCJEUFNcIik7XHJcbiAgICAgIHNldFNlbGVjdGVkQ29tcGFueShjb21wYW55KTtcclxuICAgICAgQ29va2llcy5zZXQoXCJjb21wYW55XCIsIGNvbXBhbnkpO1xyXG4gICAgfSBlbHNlIGlmIChjb21wYW55ID09IFwiZHBzbHRkbXNcIikge1xyXG4gICAgICBDb29raWVzLnNldChcInRoZW1lXCIsIFwiIzBkNmJmY1wiKTtcclxuICAgICAgQ29va2llcy5zZXQoXCJBRENvbXBhbnlOYW1lXCIsIFwiRFBTIE1TXCIpO1xyXG4gICAgICBzZXRTZWxlY3RlZENvbXBhbnkoXCJkcHNsdGRcIik7XHJcbiAgICAgIENvb2tpZXMuc2V0KFwiY29tcGFueVwiLCBcImRwc2x0ZFwiKTtcclxuICAgIH0gZWxzZSBpZiAoY29tcGFueSA9PSBcImVmY2x0ZFwiKSB7XHJcbiAgICAgIENvb2tpZXMuc2V0KFwidGhlbWVcIiwgXCIjM2VhYjU4XCIpO1xyXG4gICAgICBDb29raWVzLnNldChcIkFEQ29tcGFueU5hbWVcIiwgXCJFRkNcIik7XHJcbiAgICAgIHNldFNlbGVjdGVkQ29tcGFueShjb21wYW55KTtcclxuICAgICAgQ29va2llcy5zZXQoXCJjb21wYW55XCIsIGNvbXBhbnkpO1xyXG4gICAgfSBlbHNlIGlmIChjb21wYW55ID09IFwiZnBwLWx0ZFwiKSB7XHJcbiAgICAgIENvb2tpZXMuc2V0KFwidGhlbWVcIiwgXCIjM2Q2NTQ2XCIpO1xyXG4gICAgICBDb29raWVzLnNldChcIkFEQ29tcGFueU5hbWVcIiwgXCJGcmVzaCBQcm9kdWNlIFBhcnRuZXJzXCIpO1xyXG4gICAgICBzZXRTZWxlY3RlZENvbXBhbnkoY29tcGFueSk7XHJcbiAgICAgIENvb2tpZXMuc2V0KFwiY29tcGFueVwiLCBjb21wYW55KTtcclxuICAgIH0gZWxzZSBpZiAoY29tcGFueSA9PSBcImlzc3Byb2R1Y2VcIikge1xyXG4gICAgICBDb29raWVzLnNldChcInRoZW1lXCIsIFwiI0FCQzQwMFwiKTtcclxuICAgICAgQ29va2llcy5zZXQoXCJBRENvbXBhbnlOYW1lXCIsIFwiSW50ZWdyYXRlZCBTZXJ2aWNlIFNvbHV0aW9ucyBMdGRcIik7XHJcbiAgICAgIHNldFNlbGVjdGVkQ29tcGFueShjb21wYW55KTtcclxuICAgICAgQ29va2llcy5zZXQoXCJjb21wYW55XCIsIGNvbXBhbnkpO1xyXG4gICAgfVxyXG4gICAgcm91dGVyLnJlbG9hZCgpO1xyXG4gIH07XHJcblxyXG4gIHJldHVybiAoXHJcbiAgICA8aGVhZGVyPlxyXG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cInRpdGxlYmFyXCI+XHJcbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGZsZXgtcm93IGp1c3RpZnktYmV0d2VlbiB3LWZ1bGwgYmctc2tpbi1wcmltYXJ5IGgtMTRcIj5cclxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBmbGV4LXJvdyB3LWZ1bGwgaXRlbXMtY2VudGVyXCI+XHJcbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwicGFnZS1oZWFkaW5nIGN1cnNvci1wb2ludGVyXCI+XHJcbiAgICAgICAgICAgICAgPEZvbnRBd2Vzb21lSWNvblxyXG4gICAgICAgICAgICAgICAgaWNvbj17ZmFDaGV2cm9uTGVmdH1cclxuICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInBhZ2VOYW1lIHRleHQtd2hpdGVcIlxyXG4gICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gcm91dGVyLmJhY2soKX1cclxuICAgICAgICAgICAgICAvPlxyXG4gICAgICAgICAgICAgIDxMaW5rXHJcbiAgICAgICAgICAgICAgICBocmVmPXtjdXJyZW50Um91dGV9XHJcbiAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJtbC00IDJ4bDp0ZXh0LWxnIGZvbnQtcG9wcGluc3NlbWlib2xkIHBhZ2VOYW1lIHRleHQtd2hpdGUgdHJhY2tpbmctd2lkZVwiXHJcbiAgICAgICAgICAgICAgPlxyXG4gICAgICAgICAgICAgICAge3BhZ2VOYW1lfVxyXG4gICAgICAgICAgICAgIDwvTGluaz5cclxuICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgIDxUaGVtZVN3aXRjaGVyIGNsYXNzTmFtZT1cIm1sLTVcIiAvPlxyXG4gICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICB7KHVzZXJEYXRhPy5yb2xlX2lkID09IDUgfHwgdXNlckRhdGE/LnJvbGVfaWQgPT0gNikgJiYgKFxyXG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggZmxleC1yb3cganVzdGlmeS1lbmQgdy0xLzIgaXRlbXMtY2VudGVyIG1yLTRcIj5cclxuICAgICAgICAgICAgICA8c2VsZWN0XHJcbiAgICAgICAgICAgICAgICB2YWx1ZT17c2VsZWN0ZWRDb21wYW55fVxyXG4gICAgICAgICAgICAgICAgb25DaGFuZ2U9e2hhbmRsZUNvbXBhbnlDaGFuZ2V9XHJcbiAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJiZy13aGl0ZSB0ZXh0LWJsYWNrIHJvdW5kZWRcIlxyXG4gICAgICAgICAgICAgID5cclxuICAgICAgICAgICAgICAgIHtjb21wYW55T3B0aW9ucy5tYXAoKG9wdCkgPT4gKFxyXG4gICAgICAgICAgICAgICAgICA8b3B0aW9uXHJcbiAgICAgICAgICAgICAgICAgICAga2V5PXtvcHQudmFsdWV9XHJcbiAgICAgICAgICAgICAgICAgICAgdmFsdWU9e29wdC52YWx1ZX1cclxuICAgICAgICAgICAgICAgICAgICBkaXNhYmxlZD17b3B0LmRpc2FibGVkfVxyXG4gICAgICAgICAgICAgICAgICA+XHJcbiAgICAgICAgICAgICAgICAgICAge29wdC5sYWJlbH1cclxuICAgICAgICAgICAgICAgICAgPC9vcHRpb24+XHJcbiAgICAgICAgICAgICAgICApKX1cclxuICAgICAgICAgICAgICA8L3NlbGVjdD5cclxuICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICApfVxyXG4gICAgICAgIDwvZGl2PlxyXG4gICAgICA8L2Rpdj5cclxuICAgIDwvaGVhZGVyPlxyXG4gICk7XHJcbn07XHJcblxyXG5leHBvcnQgZGVmYXVsdCBOYXZiYXI7XHJcbiJdLCJuYW1lcyI6WyJ1c2VTdGF0ZSIsInVzZUVmZmVjdCIsIkZvbnRBd2Vzb21lSWNvbiIsImZhQ2hldnJvbkxlZnQiLCJMaW5rIiwidXNlUm91dGVyIiwidXNlTXNhbCIsIlRoZW1lU3dpdGNoZXIiLCJDb29raWVzIiwidGhlbWVzIiwiYmciLCJ0ZXh0IiwibmFtZSIsIk5hdmJhciIsInVzZXJEYXRhIiwicm91dGVyIiwicGFnZU5hbWUiLCJzZXRQYWdlTmFtZSIsImN1cnJlbnRSb3V0ZSIsInNldEN1cnJlbnRSb3V0ZSIsImN1cnJlbnRQYXRoIiwicGF0aG5hbWUiLCJzZXQiLCJlbmRzV2l0aCIsImJhc2VDb21wYW55T3B0aW9ucyIsInZhbHVlIiwibGFiZWwiLCJjb21wYW55T3B0aW9ucyIsInJvbGVfaWQiLCJkaXNhYmxlZCIsInNlbGVjdGVkQ29tcGFueSIsInNldFNlbGVjdGVkQ29tcGFueSIsImdldCIsImhhbmRsZUNvbXBhbnlDaGFuZ2UiLCJldmVudCIsImNvbnNvbGUiLCJsb2ciLCJ0YXJnZXQiLCJjb21wYW55IiwicmVsb2FkIiwiaGVhZGVyIiwiZGl2IiwiY2xhc3NOYW1lIiwiaWNvbiIsIm9uQ2xpY2siLCJiYWNrIiwiaHJlZiIsInNlbGVjdCIsIm9uQ2hhbmdlIiwibWFwIiwib3B0Iiwib3B0aW9uIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///./components/Navbar.js\n"));

/***/ }),

/***/ "./components/SideBarLinks.js":
/*!************************************!*\
  !*** ./components/SideBarLinks.js ***!
  \************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ SideBarLinks; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _fortawesome_free_solid_svg_icons__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @fortawesome/free-solid-svg-icons */ \"./node_modules/@fortawesome/free-solid-svg-icons/index.mjs\");\n/* harmony import */ var _fortawesome_react_fontawesome__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @fortawesome/react-fontawesome */ \"./node_modules/@fortawesome/react-fontawesome/index.es.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _utils_loaders_loadingContext__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/utils/loaders/loadingContext */ \"./utils/loaders/loadingContext.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\nfunction SideBarLinks(param) {\n    let { isSuppliersActive, userData, company, isUsersActive, isLogsActive, isWhatifActive, isServiceLevelActive, isProductsActive, ADCompany, currentPathname } = param;\n    var _process_env_NEXT_PUBLIC_AVAILABLE_MODULES;\n    _s();\n    const availableModules = (_process_env_NEXT_PUBLIC_AVAILABLE_MODULES = \"supplier,products,whatif,serviceLevel,users,logs\") === null || _process_env_NEXT_PUBLIC_AVAILABLE_MODULES === void 0 ? void 0 : _process_env_NEXT_PUBLIC_AVAILABLE_MODULES.split(\",\");\n    const { setIsLoading } = (0,_utils_loaders_loadingContext__WEBPACK_IMPORTED_MODULE_4__.useLoading)();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n        className: \"nav navbar-nav\",\n        children: availableModules && availableModules.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n            children: [\n                availableModules.includes(\"supplier\") && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                    className: \"flex justify-center mb-3\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                        href: \"/suppliers\",\n                        title: \"Supplier\",\n                        className: \"!px-4 !py-3 bg-white rounded-md text-center \".concat(isSuppliersActive ? \"opacity-100\" : \"opacity-70\"),\n                        onClick: (e)=>{\n                            if (isSuppliersActive && (currentPathname === null || currentPathname === void 0 ? void 0 : currentPathname.startsWith(\"/suppliers\"))) {\n                                e.preventDefault();\n                                window.location.reload();\n                            } else {\n                                setIsLoading(true);\n                            }\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fortawesome_react_fontawesome__WEBPACK_IMPORTED_MODULE_1__.FontAwesomeIcon, {\n                            icon: _fortawesome_free_solid_svg_icons__WEBPACK_IMPORTED_MODULE_5__.faTruck,\n                            size: \"lg\",\n                            className: \"text-skin-primary\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\SideBarLinks.js\",\n                            lineNumber: 63,\n                            columnNumber: 17\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\SideBarLinks.js\",\n                        lineNumber: 45,\n                        columnNumber: 15\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\SideBarLinks.js\",\n                    lineNumber: 44,\n                    columnNumber: 13\n                }, this),\n                availableModules.includes(\"products\") && !(userData === null || userData === void 0 ? void 0 : userData.department_id) == 5 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                    className: \"flex justify-center mb-3\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                        href: \"/products\",\n                        title: \"Products\",\n                        className: \"!px-4 !py-3 bg-white rounded-md text-center \".concat(isProductsActive ? \"opacity-100\" : \"opacity-70\"),\n                        onClick: (e)=>{\n                            if (isProductsActive) {\n                                e.preventDefault();\n                                window.location.reload();\n                            } else {\n                                setIsLoading(true);\n                            }\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fortawesome_react_fontawesome__WEBPACK_IMPORTED_MODULE_1__.FontAwesomeIcon, {\n                            icon: _fortawesome_free_solid_svg_icons__WEBPACK_IMPORTED_MODULE_5__.faBoxArchive,\n                            size: \"lg\",\n                            className: \"text-skin-primary\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\SideBarLinks.js\",\n                            lineNumber: 88,\n                            columnNumber: 17\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\SideBarLinks.js\",\n                        lineNumber: 73,\n                        columnNumber: 15\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\SideBarLinks.js\",\n                    lineNumber: 72,\n                    columnNumber: 13\n                }, this),\n                (company == \"efcltd\" || company == \"flrs\" || company == \"thl\") && ((userData === null || userData === void 0 ? void 0 : userData.department_id) == 1 || (userData === null || userData === void 0 ? void 0 : userData.department_id) == 2) && availableModules.includes(\"whatif\") && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                    className: \"flex justify-center mb-3\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                        href: \"/whatif\",\n                        title: \"What if\",\n                        className: \"!px-4 !py-3 bg-white rounded-md text-center \".concat(isWhatifActive ? \"opacity-100\" : \"opacity-70\"),\n                        onClick: (e)=>{\n                            if (isWhatifActive) {\n                                e.preventDefault();\n                                window.location.reload();\n                            } else {\n                                setIsLoading(true);\n                            }\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fortawesome_react_fontawesome__WEBPACK_IMPORTED_MODULE_1__.FontAwesomeIcon, {\n                            icon: _fortawesome_free_solid_svg_icons__WEBPACK_IMPORTED_MODULE_5__.faChartLine,\n                            size: \"lg\",\n                            className: \"text-skin-primary\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\SideBarLinks.js\",\n                            lineNumber: 115,\n                            columnNumber: 19\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\SideBarLinks.js\",\n                        lineNumber: 100,\n                        columnNumber: 17\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\SideBarLinks.js\",\n                    lineNumber: 99,\n                    columnNumber: 15\n                }, this),\n                ((userData === null || userData === void 0 ? void 0 : userData.department_id) == 1 || (userData === null || userData === void 0 ? void 0 : userData.department_id) == 2) && availableModules.includes(\"serviceLevel\") && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                    className: \"flex justify-center mb-3\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                        href: \"/service_level\",\n                        title: \"Service Level\",\n                        className: \"!px-4 !py-3 bg-white rounded-md text-center \".concat(isServiceLevelActive ? \"opacity-100\" : \"opacity-70\"),\n                        onClick: (e)=>{\n                            if (isServiceLevelActive) {\n                                e.preventDefault();\n                                window.location.reload();\n                            } else {\n                                setIsLoading(true);\n                            }\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fortawesome_react_fontawesome__WEBPACK_IMPORTED_MODULE_1__.FontAwesomeIcon, {\n                            icon: _fortawesome_free_solid_svg_icons__WEBPACK_IMPORTED_MODULE_5__.faFileCircleQuestion,\n                            size: \"lg\",\n                            className: \"text-skin-primary\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\SideBarLinks.js\",\n                            lineNumber: 142,\n                            columnNumber: 19\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\SideBarLinks.js\",\n                        lineNumber: 127,\n                        columnNumber: 17\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\SideBarLinks.js\",\n                    lineNumber: 126,\n                    columnNumber: 15\n                }, this),\n                ((userData === null || userData === void 0 ? void 0 : userData.role_id) === 1 || (userData === null || userData === void 0 ? void 0 : userData.role_id) === 5 || (userData === null || userData === void 0 ? void 0 : userData.role_id) === 6) && availableModules.includes(\"users\") && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                    className: \"flex justify-center mb-3\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                        href: \"/users\",\n                        title: \"Users\",\n                        className: \"!px-4 !py-3 bg-white rounded-md text-center \".concat(isUsersActive ? \"opacity-100\" : \"opacity-70\"),\n                        onClick: (e)=>{\n                            if (isUsersActive) {\n                                e.preventDefault();\n                                window.location.reload();\n                            } else {\n                                setIsLoading(true);\n                            }\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fortawesome_react_fontawesome__WEBPACK_IMPORTED_MODULE_1__.FontAwesomeIcon, {\n                            icon: _fortawesome_free_solid_svg_icons__WEBPACK_IMPORTED_MODULE_5__.faUsersLine,\n                            size: \"lg\",\n                            className: \"text-skin-primary\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\SideBarLinks.js\",\n                            lineNumber: 172,\n                            columnNumber: 19\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\SideBarLinks.js\",\n                        lineNumber: 156,\n                        columnNumber: 17\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\SideBarLinks.js\",\n                    lineNumber: 155,\n                    columnNumber: 15\n                }, this),\n                ((userData === null || userData === void 0 ? void 0 : userData.role_id) === 1 || (userData === null || userData === void 0 ? void 0 : userData.role_id) === 5 || (userData === null || userData === void 0 ? void 0 : userData.role_id) === 6) && availableModules.includes(\"logs\") && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                    className: \"flex justify-center mb-3\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                        href: \"/viewlogs\",\n                        title: \"Logs\",\n                        className: \"!px-4 !py-3 bg-white rounded-md text-center \".concat(isLogsActive ? \"opacity-100\" : \"opacity-70\"),\n                        onClick: (e)=>{\n                            if (isLogsActive) {\n                                e.preventDefault();\n                                window.location.reload();\n                            } else {\n                                setIsLoading(true);\n                            }\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fortawesome_react_fontawesome__WEBPACK_IMPORTED_MODULE_1__.FontAwesomeIcon, {\n                            icon: _fortawesome_free_solid_svg_icons__WEBPACK_IMPORTED_MODULE_5__.faList,\n                            size: \"lg\",\n                            className: \"text-skin-primary\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\SideBarLinks.js\",\n                            lineNumber: 201,\n                            columnNumber: 19\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\SideBarLinks.js\",\n                        lineNumber: 185,\n                        columnNumber: 17\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\SideBarLinks.js\",\n                    lineNumber: 184,\n                    columnNumber: 15\n                }, this)\n            ]\n        }, void 0, true)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\SideBarLinks.js\",\n        lineNumber: 40,\n        columnNumber: 5\n    }, this);\n}\n_s(SideBarLinks, \"HE5iXj3OUhzAOBpDJUVF6nKZb8k=\", false, function() {\n    return [\n        _utils_loaders_loadingContext__WEBPACK_IMPORTED_MODULE_4__.useLoading\n    ];\n});\n_c = SideBarLinks;\nvar _c;\n$RefreshReg$(_c, \"SideBarLinks\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevExports = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevExports) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports on update so we can compare the boundary\n                // signatures.\n                module.hot.dispose(function (data) {\n                    data.prevExports = currentExports;\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevExports !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevExports, currentExports)) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevExports !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/SideBarLinks.js\n"));

/***/ }),

/***/ "./components/Sidebar.js":
/*!*******************************!*\
  !*** ./components/Sidebar.js ***!
  \*******************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ Sidebar; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _public_images_dps_logo_png__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../public/images/dps-logo.png */ \"./public/images/dps-logo.png\");\n/* harmony import */ var _public_images_efc_logo_jpg__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../public/images/efc_logo.jpg */ \"./public/images/efc_logo.jpg\");\n/* harmony import */ var _public_images_dps_ms_logo_png__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../public/images/dps_ms_logo.png */ \"./public/images/dps_ms_logo.png\");\n/* harmony import */ var _public_images_fpp_logo_png__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../public/images/fpp_logo.png */ \"./public/images/fpp_logo.png\");\n/* harmony import */ var _public_images_iss_logo_jpg__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../public/images/iss_logo.jpg */ \"./public/images/iss_logo.jpg\");\n/* harmony import */ var _public_images_nav_icon_png__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ../public/images/nav-icon.png */ \"./public/images/nav-icon.png\");\n/* harmony import */ var _public_images_user_png__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ../public/images/user.png */ \"./public/images/user.png\");\n/* harmony import */ var _public_images_loading_img_png__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ../public/images/loading_img.png */ \"./public/images/loading_img.png\");\n/* harmony import */ var _public_images_logout_icon_png__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ../public/images/logout-icon.png */ \"./public/images/logout-icon.png\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! next/link */ \"./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_12___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_12__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! next/image */ \"./node_modules/next/image.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_13___default = /*#__PURE__*/__webpack_require__.n(next_image__WEBPACK_IMPORTED_MODULE_13__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! next/navigation */ \"./node_modules/next/navigation.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_14___default = /*#__PURE__*/__webpack_require__.n(next_navigation__WEBPACK_IMPORTED_MODULE_14__);\n/* harmony import */ var _utils_auth_auth__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! ../utils/auth/auth */ \"./utils/auth/auth.js\");\n/* harmony import */ var _azure_msal_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @azure/msal-react */ \"./node_modules/@azure/msal-react/dist/index.js\");\n/* harmony import */ var js_cookie__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! js-cookie */ \"./node_modules/js-cookie/dist/js.cookie.mjs\");\n/* harmony import */ var react_loader_spinner__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! react-loader-spinner */ \"./node_modules/react-loader-spinner/dist/module.js\");\n/* harmony import */ var _SideBarLinks__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! ./SideBarLinks */ \"./components/SideBarLinks.js\");\n/* provided dependency */ var process = __webpack_require__(/*! process */ \"./node_modules/next/dist/build/polyfills/process.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction Sidebar() {\n    _s();\n    const [userData, setUserData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [company, setCompany] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [ADCompany, setADCompany] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const userCookie = js_cookie__WEBPACK_IMPORTED_MODULE_17__[\"default\"].get(\"user\");\n        const companyCookie = js_cookie__WEBPACK_IMPORTED_MODULE_17__[\"default\"].get(\"company\");\n        const ADcompanyCookie = js_cookie__WEBPACK_IMPORTED_MODULE_17__[\"default\"].get(\"ADCompanyName\");\n        if (userCookie) {\n            setUserData(JSON.parse(userCookie));\n        }\n        if (companyCookie) {\n            setCompany(companyCookie);\n        }\n        if (ADcompanyCookie) {\n            setADCompany(ADcompanyCookie);\n        }\n    }, []);\n    const { instance } = (0,_azure_msal_react__WEBPACK_IMPORTED_MODULE_16__.useMsal)();\n    // const userRoleData = getCookieData(\"user\");\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const currentPathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_14__.usePathname)();\n    const [isSuppliersActive, setIsSuppliersActive] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)((currentPathname === null || currentPathname === void 0 ? void 0 : currentPathname.startsWith(\"/suppliers\")) || (currentPathname === null || currentPathname === void 0 ? void 0 : currentPathname.startsWith(\"/supplier\")));\n    const [isUsersActive, setIsUsersActive] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(currentPathname === \"/users\");\n    const [isLogsActive, setIsLogsActive] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(currentPathname === \"/viewlogs\");\n    const [isProductsActive, setIsProductsActive] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(currentPathname === \"/products\");\n    const [isWhatifActive, setIsWhatifActive] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(currentPathname === \"/whatif\");\n    const [isServiceLevelActive, setIsServiceLevelActive] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(currentPathname === \"/service_level\");\n    const [isRawMaterialRequestActive, setIsRawMaterialRequestActive] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(currentPathname === \"/raw-material-request/add\");\n    const getLogo = (company)=>{\n        if (!company) return;\n        switch(company){\n            case \"dpsltd\":\n                return _public_images_dps_logo_png__WEBPACK_IMPORTED_MODULE_3__[\"default\"];\n            case \"DPS MS\":\n                return _public_images_dps_ms_logo_png__WEBPACK_IMPORTED_MODULE_5__[\"default\"];\n            case \"efcltd\":\n                return _public_images_efc_logo_jpg__WEBPACK_IMPORTED_MODULE_4__[\"default\"];\n            case \"fpp-ltd\":\n                return _public_images_fpp_logo_png__WEBPACK_IMPORTED_MODULE_6__[\"default\"];\n            case \"thl\":\n                return _public_images_efc_logo_jpg__WEBPACK_IMPORTED_MODULE_4__[\"default\"];\n            case \"issproduce\":\n                return _public_images_iss_logo_jpg__WEBPACK_IMPORTED_MODULE_7__[\"default\"];\n            case \"flrs\":\n                return _public_images_efc_logo_jpg__WEBPACK_IMPORTED_MODULE_4__[\"default\"];\n            default:\n                return;\n        }\n    };\n    const getLogoSizeClass = (company)=>{\n        if (!company) return \"h-14 w-100\";\n        switch(company){\n            case \"dpsltd\":\n                return \"!h-16 !w-auto\";\n            default:\n                return \"h-14 w-100\";\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n            id: \"sidemenu\",\n            className: \"navbar navbar-default sidebar bg-skin-primary\",\n            role: \"navigation\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"container-fluid h-full\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_12___default()), {\n                        href: \"/suppliers\",\n                        title: \"Home\",\n                        className: \"z-50\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"brand\",\n                            children: company ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_13___default()), {\n                                src: getLogo(company == \"dpsltd\" && ADCompany == \"DPS MS\" ? \"DPS MS\" : company),\n                                alt: \"logo\",\n                                className: getLogoSizeClass(company == \"dpsltd\" && ADCompany == \"DPS MS\" ? \"DPS MS\" : company)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\Sidebar.js\",\n                                lineNumber: 117,\n                                columnNumber: 17\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_loader_spinner__WEBPACK_IMPORTED_MODULE_18__.Oval, {\n                                    color: \"#002D73\",\n                                    height: 20,\n                                    width: 20,\n                                    visible: true,\n                                    ariaLabel: \"oval-loading\",\n                                    secondaryColor: \"#0066FF\",\n                                    strokeWidth: 2,\n                                    strokeWidthSecondary: 2\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\Sidebar.js\",\n                                    lineNumber: 132,\n                                    columnNumber: 19\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\Sidebar.js\",\n                                lineNumber: 131,\n                                columnNumber: 17\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\Sidebar.js\",\n                            lineNumber: 115,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\Sidebar.js\",\n                        lineNumber: 114,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        id: \"bs-sidebar-navbar-collapse-1\",\n                        className: \"pt-10 w-100 text-center flex flex-col justify-between flex-end items-stretch h-full\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col w-full\",\n                                children: userData && company && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_SideBarLinks__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                    isSuppliersActive: isSuppliersActive,\n                                    userData: userData,\n                                    currentPathname: currentPathname,\n                                    company: company,\n                                    isUsersActive: isUsersActive,\n                                    isLogsActive: isLogsActive,\n                                    isProductsActive: isProductsActive,\n                                    isWhatifActive: isWhatifActive,\n                                    isServiceLevelActive: isServiceLevelActive,\n                                    ADCompany: ADCompany\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\Sidebar.js\",\n                                    lineNumber: 152,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\Sidebar.js\",\n                                lineNumber: 150,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-center flex-col items-center gap-4 my-4 mb-20 cursor-pointer\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        title: \"IT Training Material\",\n                                        href: \"\".concat(process.env.NEXT_PUBLIC_TRAINING_MATERIAL),\n                                        target: \"_blank\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            xmlns: \"http://www.w3.org/2000/svg\",\n                                            viewBox: \"0 0 448 512\",\n                                            fill: \"#FFFF\",\n                                            className: \"w-6\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                d: \"M0 88C0 39.4 39.4 0 88 0L392 0c30.9 0 56 25.1 56 56l0 288c0 22.3-13.1 41.6-32 50.6l0 69.4 8 0c13.3 0 24 10.7 24 24s-10.7 24-24 24L80 512c-44.2 0-80-35.8-80-80c0-2.7 .1-5.4 .4-8L0 424 0 88zM80 400c-17.7 0-32 14.3-32 32s14.3 32 32 32l288 0 0-64L80 400zM48 358.7c9.8-4.3 20.6-6.7 32-6.7l312 0c4.4 0 8-3.6 8-8l0-288c0-4.4-3.6-8-8-8L88 48C65.9 48 48 65.9 48 88l0 270.7zM160 112l8.8-17.7c2.9-5.9 11.4-5.9 14.3 0L192 112l17.7 8.8c5.9 2.9 5.9 11.4 0 14.3L192 144l-8.8 17.7c-2.9 5.9-11.4 5.9-14.3 0L160 144l-17.7-8.8c-5.9-2.9-5.9-11.4 0-14.3L160 112zM264 216l16.6-38.8c2.8-6.5 11.9-6.5 14.7 0L312 216l38.8 16.6c6.5 2.8 6.5 11.9 0 14.7L312 264l-16.6 38.8c-2.8 6.5-11.9 6.5-14.7 0L264 264l-38.8-16.6c-6.5-2.8-6.5-11.9 0-14.7L264 216z\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\Sidebar.js\",\n                                                lineNumber: 178,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\Sidebar.js\",\n                                            lineNumber: 172,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\Sidebar.js\",\n                                        lineNumber: 167,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        title: \"Logout\",\n                                        onClick: ()=>{\n                                            localStorage.removeItem(\"superUser\");\n                                            js_cookie__WEBPACK_IMPORTED_MODULE_17__[\"default\"].remove(\"company\");\n                                            js_cookie__WEBPACK_IMPORTED_MODULE_17__[\"default\"].remove(\"ADCompanyName\");\n                                            localStorage.removeItem(\"id\");\n                                            localStorage.removeItem(\"name\");\n                                            localStorage.removeItem(\"role\");\n                                            localStorage.removeItem(\"email\");\n                                            js_cookie__WEBPACK_IMPORTED_MODULE_17__[\"default\"].remove(\"user\");\n                                            js_cookie__WEBPACK_IMPORTED_MODULE_17__[\"default\"].remove(\"theme\");\n                                            js_cookie__WEBPACK_IMPORTED_MODULE_17__[\"default\"].remove(\"token\");\n                                            const redirectUrl = \"/login?redirect=\".concat(encodeURIComponent(window.location.pathname));\n                                            (0,_utils_auth_auth__WEBPACK_IMPORTED_MODULE_15__.logoutHandler)(instance, redirectUrl);\n                                        },\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_13___default()), {\n                                            src: _public_images_logout_icon_png__WEBPACK_IMPORTED_MODULE_11__[\"default\"],\n                                            alt: \"logout\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\Sidebar.js\",\n                                            lineNumber: 200,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\Sidebar.js\",\n                                        lineNumber: 181,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\Sidebar.js\",\n                                lineNumber: 166,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\Sidebar.js\",\n                        lineNumber: 146,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\Sidebar.js\",\n                lineNumber: 113,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\Sidebar.js\",\n            lineNumber: 108,\n            columnNumber: 7\n        }, this)\n    }, void 0, false);\n}\n_s(Sidebar, \"CqfPHC0NzGwmyxMjqjmKLbgxaBY=\", false, function() {\n    return [\n        _azure_msal_react__WEBPACK_IMPORTED_MODULE_16__.useMsal,\n        next_router__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        next_navigation__WEBPACK_IMPORTED_MODULE_14__.usePathname\n    ];\n});\n_c = Sidebar;\nvar _c;\n$RefreshReg$(_c, \"Sidebar\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevExports = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevExports) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports on update so we can compare the boundary\n                // signatures.\n                module.hot.dispose(function (data) {\n                    data.prevExports = currentExports;\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevExports !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevExports, currentExports)) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevExports !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/Sidebar.js\n"));

/***/ }),

/***/ "./pages/users.js":
/*!************************!*\
  !*** ./pages/users.js ***!
  \************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __N_SSP: function() { return /* binding */ __N_SSP; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _components_Layout__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/Layout */ \"./components/Layout.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _fortawesome_react_fontawesome__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @fortawesome/react-fontawesome */ \"./node_modules/@fortawesome/react-fontawesome/index.es.js\");\n/* harmony import */ var _fortawesome_free_solid_svg_icons__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! @fortawesome/free-solid-svg-icons */ \"./node_modules/@fortawesome/free-solid-svg-icons/index.mjs\");\n/* harmony import */ var ag_grid_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ag-grid-react */ \"./node_modules/ag-grid-react/lib/main.js\");\n/* harmony import */ var ag_grid_react__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(ag_grid_react__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var ag_grid_community_styles_ag_grid_css__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ag-grid-community/styles//ag-grid.css */ \"./node_modules/ag-grid-community/styles/ag-grid.css\");\n/* harmony import */ var ag_grid_community_styles_ag_grid_css__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(ag_grid_community_styles_ag_grid_css__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var ag_grid_community_styles_ag_theme_alpine_css__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ag-grid-community/styles//ag-theme-alpine.css */ \"./node_modules/ag-grid-community/styles/ag-theme-alpine.css\");\n/* harmony import */ var ag_grid_community_styles_ag_theme_alpine_css__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(ag_grid_community_styles_ag_theme_alpine_css__WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_7__);\n/* harmony import */ var _services_apiConfig__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/services/apiConfig */ \"./services/apiConfig.js\");\n/* harmony import */ var _utils_renderer_roleRenderer__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/utils/renderer/roleRenderer */ \"./utils/renderer/roleRenderer.js\");\n/* harmony import */ var _utils_renderer_departmentRenderer__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/utils/renderer/departmentRenderer */ \"./utils/renderer/departmentRenderer.js\");\n/* harmony import */ var js_cookie__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! js-cookie */ \"./node_modules/js-cookie/dist/js.cookie.mjs\");\n/* harmony import */ var react_toastify__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! react-toastify */ \"./node_modules/react-toastify/dist/react-toastify.esm.mjs\");\n/* harmony import */ var react_toastify_dist_ReactToastify_css__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! react-toastify/dist/ReactToastify.css */ \"./node_modules/react-toastify/dist/ReactToastify.css\");\n/* harmony import */ var react_toastify_dist_ReactToastify_css__WEBPACK_IMPORTED_MODULE_13___default = /*#__PURE__*/__webpack_require__.n(react_toastify_dist_ReactToastify_css__WEBPACK_IMPORTED_MODULE_13__);\n/* harmony import */ var _utils_userContext__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/utils/userContext */ \"./utils/userContext.js\");\n/* harmony import */ var _utils_getCookieData__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @/utils/getCookieData */ \"./utils/getCookieData.js\");\n/* harmony import */ var _utils_loaders_loadingContext__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @/utils/loaders/loadingContext */ \"./utils/loaders/loadingContext.js\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst users = (param)=>{\n    let { userData } = param;\n    _s();\n    const { setIsLoading } = (0,_utils_loaders_loadingContext__WEBPACK_IMPORTED_MODULE_16__.useLoading)();\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_7__.useRouter)();\n    const { userDetails, updateToken } = (0,_utils_userContext__WEBPACK_IMPORTED_MODULE_14__.useUser)();\n    const [pageSize, setPageSize] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(15);\n    const [rowData, setRowData] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [email, setEmail] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"\");\n    const [role, setRole] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"\");\n    const [department, setDepartment] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"\");\n    const [editData, setEditData] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)({});\n    const [validEmail, setValidEmail] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [validRole, setValidRole] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [validDepartment, setValidDepartment] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [isToken, setToken] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"\");\n    const [isEdit, setIsEdit] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [userId, setUserId] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(0);\n    const [salesDepartment, setSalesDepartment] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [procurementDepartment, setProcurementDepartment] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [financialDepartment, setFinancialDepartment] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [technicalDepartment, setTechnicalDepartment] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [isCommonError, setCommonError] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"\");\n    const gridRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)();\n    const emailRegex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$/;\n    const user = (0,_utils_getCookieData__WEBPACK_IMPORTED_MODULE_15__.getCookieData)(\"user\");\n    const IconsRenderer = (props)=>{\n        let updatedData;\n        const handleDelete = (event)=>{\n            const allData = props.data;\n            if (allData.userId == user.user_id) {\n                react_toastify__WEBPACK_IMPORTED_MODULE_12__.toast.error(\"Loggedin user can't delete their own email\", {\n                    position: \"top-right\"\n                });\n                return false;\n            }\n            let serverAddress = _services_apiConfig__WEBPACK_IMPORTED_MODULE_8__.apiConfig.serverAddress;\n            fetch(\"\".concat(serverAddress, \"users/delete-user/\").concat(allData.userId), {\n                method: \"DELETE\",\n                headers: {\n                    Authorization: \"Bearer \".concat(user.token),\n                    Accept: \"application/json\",\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    username: user.name,\n                    useremail: user.email\n                })\n            }).then((res)=>{\n                if (res.status === 400) {\n                    react_toastify__WEBPACK_IMPORTED_MODULE_12__.toast.error(\"There was an error with your request. Please check your data and try again.\");\n                } else if (res.status === 401) {\n                    react_toastify__WEBPACK_IMPORTED_MODULE_12__.toast.error(\"Your session has expired. Please log in again.\");\n                    setTimeout(()=>{\n                        localStorage.removeItem(\"superUser\");\n                        js_cookie__WEBPACK_IMPORTED_MODULE_11__[\"default\"].remove(\"company\");\n                        js_cookie__WEBPACK_IMPORTED_MODULE_11__[\"default\"].remove(\"ADCompanyName\");\n                        localStorage.removeItem(\"id\");\n                        localStorage.removeItem(\"name\");\n                        localStorage.removeItem(\"role\");\n                        localStorage.removeItem(\"email\");\n                        js_cookie__WEBPACK_IMPORTED_MODULE_11__[\"default\"].remove(\"user\");\n                        js_cookie__WEBPACK_IMPORTED_MODULE_11__[\"default\"].remove(\"theme\");\n                        js_cookie__WEBPACK_IMPORTED_MODULE_11__[\"default\"].remove(\"token\");\n                        const redirectUrl = \"/login?redirect=\".concat(encodeURIComponent(window.location.pathname));\n                        logoutHandler(instance, redirectUrl);\n                    }, 3000);\n                }\n                if (res.status === 200 || res.status == 409) {\n                    return res.json();\n                }\n                throw new Error(\"Failed to fetch data\");\n            }).then((json)=>{\n                if (json.status == 200) {\n                    updatedData = [\n                        ...rowData\n                    ];\n                    const index = updatedData.indexOf(allData);\n                    updatedData.splice(index, 1);\n                    props.api.applyTransaction({\n                        remove: updatedData\n                    });\n                    setRowData(updatedData);\n                    react_toastify__WEBPACK_IMPORTED_MODULE_12__.toast.success(json.message, {\n                        position: \"top-right\"\n                    });\n                }\n            }).catch((error)=>{\n            // toast.error(error.statusText, {\n            //   position: \"top-right\",\n            // });\n            });\n        };\n        const handleEdit = ()=>{\n            const editedData = props.data;\n            setEditData(editedData);\n            updatedData = [\n                ...rowData\n            ];\n            const index = updatedData.indexOf(editedData);\n            updatedData.splice(index, 1);\n            props.api.applyTransaction({\n                remove: updatedData\n            });\n            setRowData(updatedData);\n            setEmail(editedData.email);\n            setRole(editedData.role);\n            setDepartment(editedData.department);\n            setUserId(editedData.userId);\n            setIsEdit(true);\n        };\n        const isEditDisabled = isEdit || (props.data.role === 6 || props.data.role === 5) && props.data.userId !== user.user_id;\n        const isDeleteDisabled = isEdit || props.data.role === 6 || props.data.role === 5 || props.data.userId === user.user_id;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex flex-row gap-4 justify-center text-skin-primary\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    onClick: handleEdit,\n                    disabled: isEdit || user.role_id != 6 && isEditDisabled,\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fortawesome_react_fontawesome__WEBPACK_IMPORTED_MODULE_3__.FontAwesomeIcon, {\n                        icon: _fortawesome_free_solid_svg_icons__WEBPACK_IMPORTED_MODULE_17__.faPenToSquare\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\users.js\",\n                        lineNumber: 166,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\users.js\",\n                    lineNumber: 162,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    onClick: handleDelete,\n                    disabled: user.role_id != 6 && isDeleteDisabled,\n                    className: \"text-red-500\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fortawesome_react_fontawesome__WEBPACK_IMPORTED_MODULE_3__.FontAwesomeIcon, {\n                        icon: _fortawesome_free_solid_svg_icons__WEBPACK_IMPORTED_MODULE_17__.faTrash\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\users.js\",\n                        lineNumber: 173,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\users.js\",\n                    lineNumber: 168,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\users.js\",\n            lineNumber: 161,\n            columnNumber: 7\n        }, undefined);\n    };\n    // useEffect(() => {\n    //   const roleIdFromCookies = cookies.role_id;\n    //   if (roleIdFromCookies !== 1) {\n    //     router.push('/unauthorized'); // Assuming you're using Next.js router\n    //   }\n    // }, []);\n    const handleSelectDepartment = (e)=>{\n        if (e.target.value == 1) {\n            if (e.target.checked) {\n                setSalesDepartment(true);\n                setProcurementDepartment(false);\n                setFinancialDepartment(false);\n                setTechnicalDepartment(false);\n            } else {\n                setSalesDepartment(false);\n                setProcurementDepartment(false);\n                setFinancialDepartment(false);\n                setTechnicalDepartment(false);\n            }\n        } else if (e.target.value == 2) {\n            if (e.target.checked) {\n                setSalesDepartment(false);\n                setProcurementDepartment(true);\n                setFinancialDepartment(false);\n                setTechnicalDepartment(false);\n            } else {\n                setSalesDepartment(false);\n                setProcurementDepartment(false);\n                setFinancialDepartment(false);\n                setTechnicalDepartment(false);\n            }\n        } else if (e.target.value == 3) {\n            if (e.target.checked) {\n                setSalesDepartment(false);\n                setProcurementDepartment(false);\n                setFinancialDepartment(true);\n                setTechnicalDepartment(false);\n            } else {\n                setSalesDepartment(false);\n                setProcurementDepartment(false);\n                setFinancialDepartment(false);\n                setTechnicalDepartment(false);\n            }\n        } else if (e.target.value == 5) {\n            if (e.target.checked) {\n                setSalesDepartment(false);\n                setProcurementDepartment(false);\n                setFinancialDepartment(false);\n                setTechnicalDepartment(true);\n            } else {\n                setSalesDepartment(false);\n                setProcurementDepartment(false);\n                setFinancialDepartment(false);\n                setTechnicalDepartment(true);\n            }\n        } else {\n            setSalesDepartment(false);\n            setProcurementDepartment(false);\n            setFinancialDepartment(false);\n            setTechnicalDepartment(false);\n        }\n    };\n    function handleSubmit() {\n        setIsEdit(false);\n        if (email && role) {\n            let serverAddress = _services_apiConfig__WEBPACK_IMPORTED_MODULE_8__.apiConfig.serverAddress;\n            if (isEdit) {\n                fetch(\"\".concat(serverAddress, \"users/update-user/\").concat(userId), {\n                    method: \"PUT\",\n                    headers: {\n                        Authorization: \"Bearer \".concat(user.token),\n                        Accept: \"application/json\",\n                        \"Content-Type\": \"application/json\"\n                    },\n                    body: JSON.stringify({\n                        role_id: role,\n                        email: email === null || email === void 0 ? void 0 : email.trim(),\n                        user: {\n                            username: user.name,\n                            useremail: user.email\n                        },\n                        department_id: department\n                    })\n                }).then((res)=>{\n                    if (res.status === 400 || res.status === 401) {\n                        setCommonError(\"Invalid Token\");\n                    }\n                    if (res.status === 200 || res.status == 409) {\n                        return res.json();\n                    }\n                    throw new Error(\"Failed to fetch data\");\n                }).then((json)=>{\n                    const newItem = {\n                        email: json[0].email,\n                        role: json[0].role_id,\n                        userId: json[0].user_id,\n                        department: json[0].department_id\n                    };\n                    setRowData([\n                        ...rowData,\n                        newItem\n                    ]);\n                    setEmail(\"\");\n                    setRole(\"\");\n                    setDepartment(\"\");\n                    react_toastify__WEBPACK_IMPORTED_MODULE_12__.toast.success(\"User successfully updated\", {\n                        position: \"top-right\"\n                    });\n                }).catch((error)=>{\n                // toast.error(error.statusText, {\n                //   position: \"top-right\",\n                // });\n                });\n            } else {\n                fetch(\"\".concat(serverAddress, \"users/add-user\"), {\n                    method: \"POST\",\n                    headers: {\n                        Authorization: \"Bearer \".concat(user.token),\n                        Accept: \"application/json\",\n                        \"Content-Type\": \"application/json\"\n                    },\n                    body: JSON.stringify({\n                        role_id: role,\n                        department_id: department,\n                        email: email === null || email === void 0 ? void 0 : email.trim(),\n                        user: {\n                            username: user.name,\n                            useremail: user.email\n                        }\n                    })\n                }).then((res)=>{\n                    if (res.status === 400 || res.status === 401) {\n                        setCommonError(\"Invalid Token\");\n                    }\n                    if (res.status === 200 || res.status == 409) {\n                        return res.json();\n                    }\n                    throw new Error(\"Failed to fetch data\");\n                }).then((json)=>{\n                    if (json.data == \"exists\") {\n                        react_toastify__WEBPACK_IMPORTED_MODULE_12__.toast.error(\"User is already exists.\", {\n                            position: \"top-right\"\n                        });\n                        return;\n                    }\n                    if (json.length > 0) {\n                        const newItem = {\n                            email: email,\n                            role: role,\n                            userId: json[0].id,\n                            department: department\n                        };\n                        setRowData([\n                            ...rowData,\n                            newItem\n                        ]);\n                        setEmail(\"\");\n                        react_toastify__WEBPACK_IMPORTED_MODULE_12__.toast.success(\"User successfully created\", {\n                            position: \"top-right\"\n                        });\n                    }\n                }).catch((error)=>{\n                // console.log(error);\n                // toast.error(error.statusText, {\n                //   position: \"top-right\",\n                // });\n                });\n            }\n        } else {\n            setValidEmail(true);\n            setValidRole(true);\n            setValidDepartment(true);\n        }\n    }\n    function handleCancel() {\n        setIsEdit(false);\n        if (isEdit) {\n            const newItem = {\n                email: editData.email,\n                role: editData.role,\n                userId: editData.userId,\n                department: editData.department\n            };\n            setRowData([\n                ...rowData,\n                newItem\n            ]);\n        }\n        setEmail(\"\");\n        setRole(\"\");\n        setDepartment(\"\");\n        setEditData({});\n        setValidDepartment(false);\n        setValidRole(false);\n        setValidEmail(false);\n    }\n    const handleUserValidation = ()=>{\n        if (email && emailRegex.test(email)) {\n            setValidEmail(false);\n        } else {\n            setValidEmail(true);\n        }\n        if (validRole) {\n            setValidRole(true);\n        } else {\n            setValidRole(false);\n        }\n        if (validDepartment) {\n            setValidDepartment(true);\n        } else {\n            setValidDepartment(false);\n        }\n        return true;\n    };\n    function getData() {\n        let serverAddress = _services_apiConfig__WEBPACK_IMPORTED_MODULE_8__.apiConfig.serverAddress;\n        return fetch(\"\".concat(serverAddress, \"users/get-users\"), {\n            method: \"GET\",\n            headers: {\n                Accept: \"application/json\",\n                \"Content-Type\": \"application/json\",\n                Authorization: \"Bearer \".concat(user.token)\n            }\n        }).then((res)=>{\n            if (res.status === 400 || res.status === 401) {\n                setCommonError(\"Invalid Token\");\n            }\n            if (res.status === 200) {\n                return res.json();\n            }\n            throw new Error(\"Failed to fetch data\");\n        }).catch((error)=>{\n            react_toastify__WEBPACK_IMPORTED_MODULE_12__.toast.error(\"Failed to fetch data\", error.message, {\n                position: \"top-right\"\n            });\n        });\n    }\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        setIsLoading(false);\n        if (typeof document !== \"undefined\") {\n            document.title = \"Users\";\n        }\n        if (true) {\n            localStorage.removeItem(\"supplier_id\");\n        }\n        getData().then((data)=>{\n            const formattedData = data === null || data === void 0 ? void 0 : data.map((row)=>({\n                    email: row.email,\n                    role: row.role_id,\n                    userId: row.user_id,\n                    department: row.department_id\n                }));\n            setRowData(formattedData);\n        });\n    }, []);\n    const handlePageSizeChange = (event)=>{\n        const newPageSize = parseInt(event.target.value, 15);\n        setPageSize(newPageSize);\n        gridRef.current.api.paginationSetPageSize(newPageSize);\n    };\n    const defaultColDef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useMemo)(()=>({\n            sortable: true,\n            filter: false,\n            resizable: true,\n            flex: 1,\n            suppressMenu: false\n        }));\n    const columnDefs = [\n        {\n            headerName: \"Email\",\n            field: \"email\",\n            suppressMenu: true,\n            suppressSizeToFit: true,\n            flex: \"6%\"\n        },\n        {\n            headerName: \"Role\",\n            field: \"role\",\n            cellRenderer: _utils_renderer_roleRenderer__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n            flex: \"4%\",\n            hide: false\n        },\n        {\n            headerName: \"Department\",\n            field: \"department\",\n            cellRenderer: _utils_renderer_departmentRenderer__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n            flex: \"4%\",\n            hide: false\n        },\n        {\n            field: \"\",\n            cellRenderer: IconsRenderer,\n            flex: \"4%\",\n            cellStyle: ()=>({})\n        },\n        {\n            field: \"userId\",\n            //cellRenderer: IconsRenderer,\n            flex: \"4%\",\n            cellStyle: ()=>({}),\n            hide: true,\n            suppressFiltersToolPanel: true\n        }\n    ];\n    const onFilterTextBoxChanged = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(()=>{\n        gridRef.current.api.setQuickFilter(document.getElementById(\"filter-text-box\").value);\n    }, []);\n    const handleGridReady = (params)=>{\n        params.api.setColumnDefs(columnDefs);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_toastify__WEBPACK_IMPORTED_MODULE_12__.ToastContainer, {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\users.js\",\n                lineNumber: 508,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Layout__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                userData: userData,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-row justify-between w-[95%] gap-8 pe-1\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-[50%]\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-row md:flex-col lg:flex-row items-end\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex gap-6 w-full justify-end\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"relative block w-[47vh] text-gray-400 focus-within:text-gray-600 mt-0 pt-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"absolute px-4 py-1 text-black\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fortawesome_react_fontawesome__WEBPACK_IMPORTED_MODULE_3__.FontAwesomeIcon, {\n                                                        icon: _fortawesome_free_solid_svg_icons__WEBPACK_IMPORTED_MODULE_17__.faSearch,\n                                                        className: \"fw-bold\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\users.js\",\n                                                        lineNumber: 516,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\users.js\",\n                                                    lineNumber: 515,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"text\",\n                                                    id: \"filter-text-box\",\n                                                    placeholder: \"Search\",\n                                                    onInput: onFilterTextBoxChanged,\n                                                    className: \"block w-full px-4 pl-10 text-gray-500 placeholder-gray-400 searchbar border rounded-lg appearance-none form-input focus:outline-none\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\users.js\",\n                                                    lineNumber: 518,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\users.js\",\n                                            lineNumber: 514,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\users.js\",\n                                        lineNumber: 513,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\users.js\",\n                                    lineNumber: 512,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"my-5\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative ag-theme-alpine\",\n                                        style: {\n                                            height: \"calc(100vh - 150px)\"\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ag_grid_react__WEBPACK_IMPORTED_MODULE_4__.AgGridReact, {\n                                                rowData: rowData,\n                                                ref: gridRef,\n                                                columnDefs: columnDefs,\n                                                defaultColDef: defaultColDef,\n                                                suppressRowClickSelection: true,\n                                                rowSelection: \"multiple\",\n                                                pagination: true,\n                                                paginationPageSize: pageSize,\n                                                onPageSizeChanged: handlePageSizeChange,\n                                                tooltipShowDelay: 0,\n                                                tooltipHideDelay: 1000,\n                                                onGridReady: handleGridReady\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\users.js\",\n                                                lineNumber: 533,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-start mt-2 pagination-style\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    htmlFor: \"page-size-select pagination\",\n                                                    className: \"inputs\",\n                                                    children: [\n                                                        \"Show\",\n                                                        \" \",\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                            id: \"page-size-select\",\n                                                            onChange: handlePageSizeChange,\n                                                            value: pageSize,\n                                                            className: \"focus:outline-none\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                    value: 10,\n                                                                    children: \"10\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\users.js\",\n                                                                    lineNumber: 559,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                    value: 15,\n                                                                    children: \"15\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\users.js\",\n                                                                    lineNumber: 560,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                    value: 25,\n                                                                    children: \"25\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\users.js\",\n                                                                    lineNumber: 561,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                    value: 50,\n                                                                    children: \"50\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\users.js\",\n                                                                    lineNumber: 562,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                    value: 100,\n                                                                    children: \"100\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\users.js\",\n                                                                    lineNumber: 563,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\users.js\",\n                                                            lineNumber: 553,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        \" \",\n                                                        \"entries\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\users.js\",\n                                                    lineNumber: 548,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\users.js\",\n                                                lineNumber: 547,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\users.js\",\n                                        lineNumber: 529,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\users.js\",\n                                    lineNumber: 528,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\users.js\",\n                            lineNumber: 511,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-[50%] mt-14\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative panel-container contentsectionbg rounded-lg w-full 2xl:w-[calc(100%-70px)] p-4 pb-0 shadow-lg\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"m-3 mb-0\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mb-3\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                className: \"formtitle pb-1\",\n                                                children: \"Assign user role \"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\users.js\",\n                                                lineNumber: 575,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\users.js\",\n                                            lineNumber: 574,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex flex-col mb-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"labels mb-1\",\n                                                    children: [\n                                                        \"Email \",\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-red-500\",\n                                                            children: \"*\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\users.js\",\n                                                            lineNumber: 579,\n                                                            columnNumber: 27\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\users.js\",\n                                                    lineNumber: 578,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"email\",\n                                                    maxLength: 80,\n                                                    name: \"Email\",\n                                                    value: email,\n                                                    onChange: (e)=>{\n                                                        setEmail(e.target.value), handleUserValidation;\n                                                    },\n                                                    className: \"w-full px-2 2xl:px-3 border border-light-gray rounded-md searchbar\",\n                                                    onBlur: handleUserValidation\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\users.js\",\n                                                    lineNumber: 581,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                validEmail && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-red-500\",\n                                                    children: \"Please enter valid email address\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\users.js\",\n                                                    lineNumber: 593,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\users.js\",\n                                            lineNumber: 577,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex flex-col mb-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"me-5 labels\",\n                                                    children: [\n                                                        \"Role \",\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-red-500\",\n                                                            children: \"*\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\users.js\",\n                                                            lineNumber: 600,\n                                                            columnNumber: 26\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\users.js\",\n                                                    lineNumber: 599,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex mt-1\",\n                                                    children: [\n                                                        (userData === null || userData === void 0 ? void 0 : userData.role_id) == 6 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                            id: \"superadmin-radio\",\n                                                                            type: \"radio\",\n                                                                            value: \"1\",\n                                                                            checked: role == 6,\n                                                                            onChange: ()=>{\n                                                                                setRole(6), handleUserValidation;\n                                                                            },\n                                                                            onBlur: handleUserValidation,\n                                                                            className: \"ml-0 w-4 h-4 text-blue-600 bg-gray-100 border-light-gray focus:ring-blue-500 focus:ring-2\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\users.js\",\n                                                                            lineNumber: 607,\n                                                                            columnNumber: 29\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                            htmlFor: \"superadmin-radio\",\n                                                                            className: \"p-0 ml-2 me-5 text-blackcolor\",\n                                                                            children: \"Super Admin\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\users.js\",\n                                                                            lineNumber: 618,\n                                                                            columnNumber: 29\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\users.js\",\n                                                                    lineNumber: 606,\n                                                                    columnNumber: 27\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\users.js\",\n                                                                lineNumber: 605,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        }, void 0, false),\n                                                        ((userData === null || userData === void 0 ? void 0 : userData.role_id) == 5 || (userData === null || userData === void 0 ? void 0 : userData.role_id) == 6) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                        id: \"thladmin-radio\",\n                                                                        type: \"radio\",\n                                                                        value: \"1\",\n                                                                        checked: role == 5,\n                                                                        onChange: ()=>{\n                                                                            setRole(5), handleUserValidation;\n                                                                        },\n                                                                        disabled: (userData === null || userData === void 0 ? void 0 : userData.role_id) == 5 ? true : false,\n                                                                        onBlur: handleUserValidation,\n                                                                        className: \"ml-0 w-4 h-4 text-blue-600 bg-gray-100 border-light-gray focus:ring-blue-500 focus:ring-2\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\users.js\",\n                                                                        lineNumber: 631,\n                                                                        columnNumber: 27\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        htmlFor: \"thladmin-radio\",\n                                                                        className: \"p-0 ml-2 me-5 text-blackcolor\",\n                                                                        children: \"THL Admin\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\users.js\",\n                                                                        lineNumber: 643,\n                                                                        columnNumber: 27\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\users.js\",\n                                                                lineNumber: 630,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\users.js\",\n                                                            lineNumber: 629,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                        id: \"admin-radio\",\n                                                                        type: \"radio\",\n                                                                        value: \"1\",\n                                                                        checked: role == 1,\n                                                                        onChange: ()=>{\n                                                                            setRole(1), handleUserValidation;\n                                                                        },\n                                                                        onBlur: handleUserValidation,\n                                                                        className: \"ml-0 w-4 h-4 text-blue-600 bg-gray-100 border-light-gray focus:ring-blue-500 focus:ring-2 hover:cursor-pointer\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\users.js\",\n                                                                        lineNumber: 654,\n                                                                        columnNumber: 25\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        htmlFor: \"admin-radio\",\n                                                                        className: \"p-0 ml-2 me-5 text-blackcolor hover:cursor-pointer\",\n                                                                        children: \"Administrator\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\users.js\",\n                                                                        lineNumber: 665,\n                                                                        columnNumber: 25\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\users.js\",\n                                                                lineNumber: 653,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\users.js\",\n                                                            lineNumber: 652,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                    id: \"approver-radio\",\n                                                                    type: \"radio\",\n                                                                    value: \"2\",\n                                                                    checked: role == 2,\n                                                                    onChange: ()=>{\n                                                                        setRole(2), handleUserValidation;\n                                                                    },\n                                                                    onBlur: handleUserValidation,\n                                                                    className: \"ml-0 w-4 h-4 text-blue-600 bg-gray-100 border-light-gray focus:ring-blue-500 focus:ring-2\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\users.js\",\n                                                                    lineNumber: 674,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    htmlFor: \"approver-radio\",\n                                                                    className: \"p-0 ml-2 me-5 text-blackcolor\",\n                                                                    children: \"Approver\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\users.js\",\n                                                                    lineNumber: 685,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\users.js\",\n                                                            lineNumber: 673,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                        id: \"user-radio\",\n                                                                        type: \"radio\",\n                                                                        value: \"4\",\n                                                                        checked: role == 4,\n                                                                        onChange: ()=>{\n                                                                            setRole(4), handleUserValidation;\n                                                                        },\n                                                                        onBlur: handleUserValidation,\n                                                                        className: \"ml-0 w-4 h-4 text-blue-600 bg-gray-100 border-light-gray focus:ring-blue-500 focus:ring-2 hover:cursor-pointer\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\users.js\",\n                                                                        lineNumber: 694,\n                                                                        columnNumber: 25\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        htmlFor: \"user-radio\",\n                                                                        className: \"p-0 ml-2 me-5 text-blackcolor hover:cursor-pointer\",\n                                                                        children: \"User\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\users.js\",\n                                                                        lineNumber: 705,\n                                                                        columnNumber: 25\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\users.js\",\n                                                                lineNumber: 693,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\users.js\",\n                                                            lineNumber: 692,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\users.js\",\n                                                    lineNumber: 602,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                validRole && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-red-500\",\n                                                    children: \"Select role\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\users.js\",\n                                                    lineNumber: 715,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\users.js\",\n                                            lineNumber: 598,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex flex-col mb-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"me-5 labels\",\n                                                    children: [\n                                                        \"Department \",\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-red-500\",\n                                                            children: \"*\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\users.js\",\n                                                            lineNumber: 720,\n                                                            columnNumber: 32\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\users.js\",\n                                                    lineNumber: 719,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex mt-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                    id: \"department-sales\",\n                                                                    type: \"radio\",\n                                                                    value: 1,\n                                                                    checked: department == 1,\n                                                                    onChange: (e)=>{\n                                                                        setDepartment(1);\n                                                                        handleSelectDepartment(e);\n                                                                    },\n                                                                    onBlur: handleUserValidation,\n                                                                    className: \"ml-0 w-4 h-4 text-blue-600 bg-gray-100 border-light-gray focus:ring-blue-500 focus:ring-2 hover:cursor-pointer\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\users.js\",\n                                                                    lineNumber: 724,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    htmlFor: \"department-sales\",\n                                                                    className: \"p-0 ml-2 me-5 text-blackcolor hover:cursor-pointer\",\n                                                                    children: \"Sales\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\users.js\",\n                                                                    lineNumber: 736,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\users.js\",\n                                                            lineNumber: 723,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                    id: \"department-procurement\",\n                                                                    type: \"radio\",\n                                                                    value: 2,\n                                                                    checked: department == 2,\n                                                                    onChange: (e)=>{\n                                                                        setDepartment(2);\n                                                                        handleSelectDepartment(e);\n                                                                    },\n                                                                    onBlur: handleUserValidation,\n                                                                    className: \"ml-0 w-4 h-4 text-blue-600 bg-gray-100 border-light-gray focus:ring-blue-500 focus:ring-2 hover:cursor-pointer\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\users.js\",\n                                                                    lineNumber: 744,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    htmlFor: \"department-procurement\",\n                                                                    className: \"p-0 ml-2 me-5 text-blackcolor hover:cursor-pointer\",\n                                                                    children: \"Procurement\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\users.js\",\n                                                                    lineNumber: 756,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\users.js\",\n                                                            lineNumber: 743,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                    id: \"department-financial\",\n                                                                    type: \"radio\",\n                                                                    value: 3,\n                                                                    checked: department == 3,\n                                                                    onChange: (e)=>{\n                                                                        setDepartment(3);\n                                                                        handleSelectDepartment(e);\n                                                                    },\n                                                                    onBlur: handleUserValidation,\n                                                                    className: \"ml-0 w-4 h-4 text-blue-600 bg-gray-100 border-light-gray focus:ring-blue-500 focus:ring-2 hover:cursor-pointer\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\users.js\",\n                                                                    lineNumber: 764,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    htmlFor: \"department-financial\",\n                                                                    className: \"p-0 ml-2 me-5 text-blackcolor hover:cursor-pointer\",\n                                                                    children: \"Financial\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\users.js\",\n                                                                    lineNumber: 776,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\users.js\",\n                                                            lineNumber: 763,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                    id: \"department-technical\",\n                                                                    type: \"radio\",\n                                                                    value: 5,\n                                                                    checked: department == 5,\n                                                                    onChange: (e)=>{\n                                                                        setDepartment(5);\n                                                                        handleSelectDepartment(e);\n                                                                    },\n                                                                    onBlur: handleUserValidation,\n                                                                    className: \"ml-0 w-4 h-4 text-blue-600 bg-gray-100 border-light-gray focus:ring-blue-500 focus:ring-2 hover:cursor-pointer\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\users.js\",\n                                                                    lineNumber: 784,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    htmlFor: \"department-technical\",\n                                                                    className: \"p-0 ml-2 me-5 text-blackcolor hover:cursor-pointer\",\n                                                                    children: \"Technical\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\users.js\",\n                                                                    lineNumber: 796,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\users.js\",\n                                                            lineNumber: 783,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                    id: \"department-logistics\",\n                                                                    type: \"radio\",\n                                                                    value: 8,\n                                                                    checked: department == 8,\n                                                                    onChange: (e)=>{\n                                                                        setDepartment(8);\n                                                                        handleSelectDepartment(e);\n                                                                    },\n                                                                    onBlur: handleUserValidation,\n                                                                    className: \"ml-0 w-4 h-4 text-blue-600 bg-gray-100 border-light-gray focus:ring-blue-500 focus:ring-2 hover:cursor-pointer\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\users.js\",\n                                                                    lineNumber: 804,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    htmlFor: \"department-logistics\",\n                                                                    className: \"p-0 ml-2 me-5 text-blackcolor hover:cursor-pointer\",\n                                                                    children: \"Logistics\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\users.js\",\n                                                                    lineNumber: 816,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\users.js\",\n                                                            lineNumber: 803,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\users.js\",\n                                                    lineNumber: 722,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                validDepartment && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-red-500\",\n                                                    children: \"Select Departement\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\users.js\",\n                                                    lineNumber: 845,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\users.js\",\n                                            lineNumber: 718,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex justify-end pb-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    className: \"border border-skin-primary text-skin-primary me-10 py-1 px-8 font-medium rounded-md\",\n                                                    onClick: handleCancel,\n                                                    children: \"Cancel\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\users.js\",\n                                                    lineNumber: 849,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    className: \"border border-skin-primary bg-skin-primary text-white rounded-md py-1 px-8 font-medium\",\n                                                    onClick: handleSubmit,\n                                                    disabled: email && emailRegex.test(email) && role && department ? false : true,\n                                                    children: \"Save\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\users.js\",\n                                                    lineNumber: 855,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\users.js\",\n                                            lineNumber: 848,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\users.js\",\n                                    lineNumber: 573,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\users.js\",\n                                lineNumber: 572,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\users.js\",\n                            lineNumber: 571,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\users.js\",\n                    lineNumber: 510,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\users.js\",\n                lineNumber: 509,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true);\n};\n_s(users, \"qTSUkOLZJZMjOQIrw9YH5FyNi2g=\", false, function() {\n    return [\n        _utils_loaders_loadingContext__WEBPACK_IMPORTED_MODULE_16__.useLoading,\n        next_router__WEBPACK_IMPORTED_MODULE_7__.useRouter,\n        _utils_userContext__WEBPACK_IMPORTED_MODULE_14__.useUser\n    ];\n});\nvar __N_SSP = true;\n/* harmony default export */ __webpack_exports__[\"default\"] = (users);\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevExports = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevExports) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports on update so we can compare the boundary\n                // signatures.\n                module.hot.dispose(function (data) {\n                    data.prevExports = currentExports;\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevExports !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevExports, currentExports)) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevExports !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./pages/users.js\n"));

/***/ }),

/***/ "./utils/renderer/departmentRenderer.js":
/*!**********************************************!*\
  !*** ./utils/renderer/departmentRenderer.js ***!
  \**********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n\nvar _s = $RefreshSig$();\n\nconst colorMap = {\n    1: {\n        background: \"rgba(62, 171, 88, 0.2)\",\n        text: \"#3EAB58\"\n    },\n    2: {\n        background: \"rgba(0, 102, 255, 0.2)\",\n        text: \"#0066FF\"\n    },\n    3: {\n        background: \"rgb(211, 211, 211, 0.7)\",\n        text: \"#000000\"\n    },\n    8: {\n        background: \"rgba(102, 100, 227, 0.2)\",\n        text: \"#592cf2\"\n    },\n    5: {\n        background: \"rgba(255, 212, 169, 0.5)\",\n        text: \"#b37858\"\n    },\n    default: {\n        background: \"transparent\",\n        text: \"#9A9A9A\"\n    }\n};\nconst departmentRenderer = (params)=>{\n    _s();\n    const department = params.value;\n    const { background, text } = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>colorMap[department] || colorMap.default, [\n        department\n    ]);\n    const spanStyle = {\n        backgroundColor: background,\n        width: \"95px\",\n        textAlign: \"center\",\n        display: \"inline-block\",\n        verticalAlign: \"middle\",\n        lineHeight: \"24px\",\n        height: \"32px\",\n        color: text,\n        padding: \"6px\",\n        borderRadius: \"10px\"\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n        style: spanStyle,\n        children: department == 1 ? \"Sales\" : department == 2 ? \"Procurement\" : department == 3 ? \"Financial\" : department == 5 ? \"Technical\" : department == 8 ? \"Logistics\" : \"\"\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\utils\\\\renderer\\\\departmentRenderer.js\",\n        lineNumber: 51,\n        columnNumber: 5\n    }, undefined);\n};\n_s(departmentRenderer, \"oG5WjEbAEsX/uyBWjLiCJK7fJI8=\");\n/* harmony default export */ __webpack_exports__[\"default\"] = (departmentRenderer);\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevExports = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevExports) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports on update so we can compare the boundary\n                // signatures.\n                module.hot.dispose(function (data) {\n                    data.prevExports = currentExports;\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevExports !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevExports, currentExports)) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevExports !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./utils/renderer/departmentRenderer.js\n"));

/***/ }),

/***/ "./utils/renderer/roleRenderer.js":
/*!****************************************!*\
  !*** ./utils/renderer/roleRenderer.js ***!
  \****************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n\nvar _s = $RefreshSig$();\n\nconst colorMap = {\n    1: {\n        background: \"rgba(62, 171, 88, 0.2)\",\n        text: \"#3EAB58\"\n    },\n    2: {\n        background: \"rgba(0, 102, 255, 0.2)\",\n        text: \"#0066FF\"\n    },\n    4: {\n        background: \"rgb(211, 211, 211, 0.7)\",\n        text: \"#000000\"\n    },\n    5: {\n        background: \"rgba(62, 171, 88, 0.2)\",\n        text: \"#3EAB58\"\n    },\n    6: {\n        background: \"rgba(62, 171, 88, 0.2)\",\n        text: \"#3EAB58\"\n    },\n    default: {\n        background: \"rgba(154, 154, 154, 0.2)\",\n        text: \"#9A9A9A\"\n    }\n};\nconst roleRenderer = (params)=>{\n    _s();\n    const role = params.value;\n    const { background, text } = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>colorMap[role] || colorMap.default, [\n        role\n    ]);\n    // if (role === 4) {\n    //   return null; // Return null to hide the component\n    // }\n    const spanStyle = {\n        backgroundColor: background,\n        width: \"95px\",\n        textAlign: \"center\",\n        display: \"inline-block\",\n        verticalAlign: \"middle\",\n        lineHeight: \"24px\",\n        height: \"32px\",\n        color: text,\n        padding: \"6px\",\n        borderRadius: \"10px\"\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n        style: spanStyle,\n        children: role == 1 ? \"Administrator\" : role === 2 ? \"Approver\" : role === 5 ? \"THL Admin\" : role === 6 ? \"Super Admin\" : \"User\"\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\utils\\\\renderer\\\\roleRenderer.js\",\n        lineNumber: 54,\n        columnNumber: 5\n    }, undefined);\n};\n_s(roleRenderer, \"oG5WjEbAEsX/uyBWjLiCJK7fJI8=\");\n/* harmony default export */ __webpack_exports__[\"default\"] = (roleRenderer);\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevExports = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevExports) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports on update so we can compare the boundary\n                // signatures.\n                module.hot.dispose(function (data) {\n                    data.prevExports = currentExports;\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevExports !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevExports, currentExports)) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevExports !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./utils/renderer/roleRenderer.js\n"));

/***/ })

});