"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/_app",{

/***/ "./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[8].oneOf[13].use[1]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[8].oneOf[13].use[2]!./styles/globals.css":
/*!**********************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[8].oneOf[13].use[1]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[8].oneOf[13].use[2]!./styles/globals.css ***!
  \**********************************************************************************************************************************************************************************************************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var _node_modules_next_dist_build_webpack_loaders_css_loader_src_runtime_api_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../node_modules/next/dist/build/webpack/loaders/css-loader/src/runtime/api.js */ \"./node_modules/next/dist/build/webpack/loaders/css-loader/src/runtime/api.js\");\n/* harmony import */ var _node_modules_next_dist_build_webpack_loaders_css_loader_src_runtime_api_js__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_node_modules_next_dist_build_webpack_loaders_css_loader_src_runtime_api_js__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _node_modules_next_dist_build_webpack_loaders_css_loader_src_runtime_getUrl_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../node_modules/next/dist/build/webpack/loaders/css-loader/src/runtime/getUrl.js */ \"./node_modules/next/dist/build/webpack/loaders/css-loader/src/runtime/getUrl.js\");\n/* harmony import */ var _node_modules_next_dist_build_webpack_loaders_css_loader_src_runtime_getUrl_js__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_node_modules_next_dist_build_webpack_loaders_css_loader_src_runtime_getUrl_js__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _assets_fonts_Poppins_Black_ttf__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../assets/fonts/Poppins-Black.ttf */ \"./assets/fonts/Poppins-Black.ttf\");\n/* harmony import */ var _assets_fonts_Poppins_BlackItalic_ttf__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../assets/fonts/Poppins-BlackItalic.ttf */ \"./assets/fonts/Poppins-BlackItalic.ttf\");\n/* harmony import */ var _assets_fonts_Poppins_Bold_ttf__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../assets/fonts/Poppins-Bold.ttf */ \"./assets/fonts/Poppins-Bold.ttf\");\n/* harmony import */ var _assets_fonts_Poppins_BoldItalic_ttf__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../assets/fonts/Poppins-BoldItalic.ttf */ \"./assets/fonts/Poppins-BoldItalic.ttf\");\n/* harmony import */ var _assets_fonts_Poppins_ExtraBold_ttf__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../assets/fonts/Poppins-ExtraBold.ttf */ \"./assets/fonts/Poppins-ExtraBold.ttf\");\n/* harmony import */ var _assets_fonts_Poppins_ExtraBoldItalic_ttf__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../assets/fonts/Poppins-ExtraBoldItalic.ttf */ \"./assets/fonts/Poppins-ExtraBoldItalic.ttf\");\n/* harmony import */ var _assets_fonts_Poppins_ExtraLight_ttf__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ../assets/fonts/Poppins-ExtraLight.ttf */ \"./assets/fonts/Poppins-ExtraLight.ttf\");\n/* harmony import */ var _assets_fonts_Poppins_ExtraLightItalic_ttf__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ../assets/fonts/Poppins-ExtraLightItalic.ttf */ \"./assets/fonts/Poppins-ExtraLightItalic.ttf\");\n/* harmony import */ var _assets_fonts_Poppins_Italic_ttf__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ../assets/fonts/Poppins-Italic.ttf */ \"./assets/fonts/Poppins-Italic.ttf\");\n/* harmony import */ var _assets_fonts_Poppins_Light_ttf__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ../assets/fonts/Poppins-Light.ttf */ \"./assets/fonts/Poppins-Light.ttf\");\n/* harmony import */ var _assets_fonts_Poppins_LightItalic_ttf__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ../assets/fonts/Poppins-LightItalic.ttf */ \"./assets/fonts/Poppins-LightItalic.ttf\");\n/* harmony import */ var _assets_fonts_Poppins_Medium_ttf__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! ../assets/fonts/Poppins-Medium.ttf */ \"./assets/fonts/Poppins-Medium.ttf\");\n/* harmony import */ var _assets_fonts_Poppins_MediumItalic_ttf__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! ../assets/fonts/Poppins-MediumItalic.ttf */ \"./assets/fonts/Poppins-MediumItalic.ttf\");\n/* harmony import */ var _assets_fonts_Poppins_Regular_ttf__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! ../assets/fonts/Poppins-Regular.ttf */ \"./assets/fonts/Poppins-Regular.ttf\");\n/* harmony import */ var _assets_fonts_Poppins_SemiBold_ttf__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! ../assets/fonts/Poppins-SemiBold.ttf */ \"./assets/fonts/Poppins-SemiBold.ttf\");\n/* harmony import */ var _assets_fonts_Poppins_SemiBoldItalic_ttf__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! ../assets/fonts/Poppins-SemiBoldItalic.ttf */ \"./assets/fonts/Poppins-SemiBoldItalic.ttf\");\n// Imports\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nvar ___CSS_LOADER_EXPORT___ = _node_modules_next_dist_build_webpack_loaders_css_loader_src_runtime_api_js__WEBPACK_IMPORTED_MODULE_0___default()(true);\nvar ___CSS_LOADER_URL_REPLACEMENT_0___ = _node_modules_next_dist_build_webpack_loaders_css_loader_src_runtime_getUrl_js__WEBPACK_IMPORTED_MODULE_1___default()(_assets_fonts_Poppins_Black_ttf__WEBPACK_IMPORTED_MODULE_2__);\nvar ___CSS_LOADER_URL_REPLACEMENT_1___ = _node_modules_next_dist_build_webpack_loaders_css_loader_src_runtime_getUrl_js__WEBPACK_IMPORTED_MODULE_1___default()(_assets_fonts_Poppins_BlackItalic_ttf__WEBPACK_IMPORTED_MODULE_3__);\nvar ___CSS_LOADER_URL_REPLACEMENT_2___ = _node_modules_next_dist_build_webpack_loaders_css_loader_src_runtime_getUrl_js__WEBPACK_IMPORTED_MODULE_1___default()(_assets_fonts_Poppins_Bold_ttf__WEBPACK_IMPORTED_MODULE_4__);\nvar ___CSS_LOADER_URL_REPLACEMENT_3___ = _node_modules_next_dist_build_webpack_loaders_css_loader_src_runtime_getUrl_js__WEBPACK_IMPORTED_MODULE_1___default()(_assets_fonts_Poppins_BoldItalic_ttf__WEBPACK_IMPORTED_MODULE_5__);\nvar ___CSS_LOADER_URL_REPLACEMENT_4___ = _node_modules_next_dist_build_webpack_loaders_css_loader_src_runtime_getUrl_js__WEBPACK_IMPORTED_MODULE_1___default()(_assets_fonts_Poppins_ExtraBold_ttf__WEBPACK_IMPORTED_MODULE_6__);\nvar ___CSS_LOADER_URL_REPLACEMENT_5___ = _node_modules_next_dist_build_webpack_loaders_css_loader_src_runtime_getUrl_js__WEBPACK_IMPORTED_MODULE_1___default()(_assets_fonts_Poppins_ExtraBoldItalic_ttf__WEBPACK_IMPORTED_MODULE_7__);\nvar ___CSS_LOADER_URL_REPLACEMENT_6___ = _node_modules_next_dist_build_webpack_loaders_css_loader_src_runtime_getUrl_js__WEBPACK_IMPORTED_MODULE_1___default()(_assets_fonts_Poppins_ExtraLight_ttf__WEBPACK_IMPORTED_MODULE_8__);\nvar ___CSS_LOADER_URL_REPLACEMENT_7___ = _node_modules_next_dist_build_webpack_loaders_css_loader_src_runtime_getUrl_js__WEBPACK_IMPORTED_MODULE_1___default()(_assets_fonts_Poppins_ExtraLightItalic_ttf__WEBPACK_IMPORTED_MODULE_9__);\nvar ___CSS_LOADER_URL_REPLACEMENT_8___ = _node_modules_next_dist_build_webpack_loaders_css_loader_src_runtime_getUrl_js__WEBPACK_IMPORTED_MODULE_1___default()(_assets_fonts_Poppins_Italic_ttf__WEBPACK_IMPORTED_MODULE_10__);\nvar ___CSS_LOADER_URL_REPLACEMENT_9___ = _node_modules_next_dist_build_webpack_loaders_css_loader_src_runtime_getUrl_js__WEBPACK_IMPORTED_MODULE_1___default()(_assets_fonts_Poppins_Light_ttf__WEBPACK_IMPORTED_MODULE_11__);\nvar ___CSS_LOADER_URL_REPLACEMENT_10___ = _node_modules_next_dist_build_webpack_loaders_css_loader_src_runtime_getUrl_js__WEBPACK_IMPORTED_MODULE_1___default()(_assets_fonts_Poppins_LightItalic_ttf__WEBPACK_IMPORTED_MODULE_12__);\nvar ___CSS_LOADER_URL_REPLACEMENT_11___ = _node_modules_next_dist_build_webpack_loaders_css_loader_src_runtime_getUrl_js__WEBPACK_IMPORTED_MODULE_1___default()(_assets_fonts_Poppins_Medium_ttf__WEBPACK_IMPORTED_MODULE_13__);\nvar ___CSS_LOADER_URL_REPLACEMENT_12___ = _node_modules_next_dist_build_webpack_loaders_css_loader_src_runtime_getUrl_js__WEBPACK_IMPORTED_MODULE_1___default()(_assets_fonts_Poppins_MediumItalic_ttf__WEBPACK_IMPORTED_MODULE_14__);\nvar ___CSS_LOADER_URL_REPLACEMENT_13___ = _node_modules_next_dist_build_webpack_loaders_css_loader_src_runtime_getUrl_js__WEBPACK_IMPORTED_MODULE_1___default()(_assets_fonts_Poppins_Regular_ttf__WEBPACK_IMPORTED_MODULE_15__);\nvar ___CSS_LOADER_URL_REPLACEMENT_14___ = _node_modules_next_dist_build_webpack_loaders_css_loader_src_runtime_getUrl_js__WEBPACK_IMPORTED_MODULE_1___default()(_assets_fonts_Poppins_SemiBold_ttf__WEBPACK_IMPORTED_MODULE_16__);\nvar ___CSS_LOADER_URL_REPLACEMENT_15___ = _node_modules_next_dist_build_webpack_loaders_css_loader_src_runtime_getUrl_js__WEBPACK_IMPORTED_MODULE_1___default()(_assets_fonts_Poppins_SemiBoldItalic_ttf__WEBPACK_IMPORTED_MODULE_17__);\n// Module\n___CSS_LOADER_EXPORT___.push([module.id, \"/*\\n! tailwindcss v3.3.3 | MIT License | https://tailwindcss.com\\n*//*\\n1. Prevent padding and border from affecting element width. (https://github.com/mozdevs/cssremedy/issues/4)\\n2. Allow adding a border to an element by just adding a border-width. (https://github.com/tailwindcss/tailwindcss/pull/116)\\n*/\\n\\n*,\\n::before,\\n::after {\\n  box-sizing: border-box; /* 1 */\\n  border-width: 0; /* 2 */\\n  border-style: solid; /* 2 */\\n  border-color: #e5e7eb; /* 2 */\\n}\\n\\n::before,\\n::after {\\n  --tw-content: '';\\n}\\n\\n/*\\n1. Use a consistent sensible line-height in all browsers.\\n2. Prevent adjustments of font size after orientation changes in iOS.\\n3. Use a more readable tab size.\\n4. Use the user's configured `sans` font-family by default.\\n5. Use the user's configured `sans` font-feature-settings by default.\\n6. Use the user's configured `sans` font-variation-settings by default.\\n*/\\n\\nhtml {\\n  line-height: 1.5; /* 1 */\\n  -webkit-text-size-adjust: 100%; /* 2 */\\n  -moz-tab-size: 4; /* 3 */\\n  -o-tab-size: 4;\\n     tab-size: 4; /* 3 */\\n  font-family: ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont, \\\"Segoe UI\\\", Roboto, \\\"Helvetica Neue\\\", Arial, \\\"Noto Sans\\\", sans-serif, \\\"Apple Color Emoji\\\", \\\"Segoe UI Emoji\\\", \\\"Segoe UI Symbol\\\", \\\"Noto Color Emoji\\\"; /* 4 */\\n  font-feature-settings: normal; /* 5 */\\n  font-variation-settings: normal; /* 6 */\\n}\\n\\n/*\\n1. Remove the margin in all browsers.\\n2. Inherit line-height from `html` so users can set them as a class directly on the `html` element.\\n*/\\n\\nbody {\\n  margin: 0; /* 1 */\\n  line-height: inherit; /* 2 */\\n}\\n\\n/*\\n1. Add the correct height in Firefox.\\n2. Correct the inheritance of border color in Firefox. (https://bugzilla.mozilla.org/show_bug.cgi?id=190655)\\n3. Ensure horizontal rules are visible by default.\\n*/\\n\\nhr {\\n  height: 0; /* 1 */\\n  color: inherit; /* 2 */\\n  border-top-width: 1px; /* 3 */\\n}\\n\\n/*\\nAdd the correct text decoration in Chrome, Edge, and Safari.\\n*/\\n\\nabbr:where([title]) {\\n  -webkit-text-decoration: underline dotted;\\n          text-decoration: underline dotted;\\n}\\n\\n/*\\nRemove the default font size and weight for headings.\\n*/\\n\\nh1,\\nh2,\\nh3,\\nh4,\\nh5,\\nh6 {\\n  font-size: inherit;\\n  font-weight: inherit;\\n}\\n\\n/*\\nReset links to optimize for opt-in styling instead of opt-out.\\n*/\\n\\na {\\n  color: inherit;\\n  text-decoration: inherit;\\n}\\n\\n/*\\nAdd the correct font weight in Edge and Safari.\\n*/\\n\\nb,\\nstrong {\\n  font-weight: bolder;\\n}\\n\\n/*\\n1. Use the user's configured `mono` font family by default.\\n2. Correct the odd `em` font sizing in all browsers.\\n*/\\n\\ncode,\\nkbd,\\nsamp,\\npre {\\n  font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, \\\"Liberation Mono\\\", \\\"Courier New\\\", monospace; /* 1 */\\n  font-size: 1em; /* 2 */\\n}\\n\\n/*\\nAdd the correct font size in all browsers.\\n*/\\n\\nsmall {\\n  font-size: 80%;\\n}\\n\\n/*\\nPrevent `sub` and `sup` elements from affecting the line height in all browsers.\\n*/\\n\\nsub,\\nsup {\\n  font-size: 75%;\\n  line-height: 0;\\n  position: relative;\\n  vertical-align: baseline;\\n}\\n\\nsub {\\n  bottom: -0.25em;\\n}\\n\\nsup {\\n  top: -0.5em;\\n}\\n\\n/*\\n1. Remove text indentation from table contents in Chrome and Safari. (https://bugs.chromium.org/p/chromium/issues/detail?id=999088, https://bugs.webkit.org/show_bug.cgi?id=201297)\\n2. Correct table border color inheritance in all Chrome and Safari. (https://bugs.chromium.org/p/chromium/issues/detail?id=935729, https://bugs.webkit.org/show_bug.cgi?id=195016)\\n3. Remove gaps between table borders by default.\\n*/\\n\\ntable {\\n  text-indent: 0; /* 1 */\\n  border-color: inherit; /* 2 */\\n  border-collapse: collapse; /* 3 */\\n}\\n\\n/*\\n1. Change the font styles in all browsers.\\n2. Remove the margin in Firefox and Safari.\\n3. Remove default padding in all browsers.\\n*/\\n\\nbutton,\\ninput,\\noptgroup,\\nselect,\\ntextarea {\\n  font-family: inherit; /* 1 */\\n  font-feature-settings: inherit; /* 1 */\\n  font-variation-settings: inherit; /* 1 */\\n  font-size: 100%; /* 1 */\\n  font-weight: inherit; /* 1 */\\n  line-height: inherit; /* 1 */\\n  color: inherit; /* 1 */\\n  margin: 0; /* 2 */\\n  padding: 0; /* 3 */\\n}\\n\\n/*\\nRemove the inheritance of text transform in Edge and Firefox.\\n*/\\n\\nbutton,\\nselect {\\n  text-transform: none;\\n}\\n\\n/*\\n1. Correct the inability to style clickable types in iOS and Safari.\\n2. Remove default button styles.\\n*/\\n\\nbutton,\\n[type='button'],\\n[type='reset'],\\n[type='submit'] {\\n  -webkit-appearance: button; /* 1 */\\n  background-color: transparent; /* 2 */\\n  background-image: none; /* 2 */\\n}\\n\\n/*\\nUse the modern Firefox focus style for all focusable elements.\\n*/\\n\\n:-moz-focusring {\\n  outline: auto;\\n}\\n\\n/*\\nRemove the additional `:invalid` styles in Firefox. (https://github.com/mozilla/gecko-dev/blob/2f9eacd9d3d995c937b4251a5557d95d494c9be1/layout/style/res/forms.css#L728-L737)\\n*/\\n\\n:-moz-ui-invalid {\\n  box-shadow: none;\\n}\\n\\n/*\\nAdd the correct vertical alignment in Chrome and Firefox.\\n*/\\n\\nprogress {\\n  vertical-align: baseline;\\n}\\n\\n/*\\nCorrect the cursor style of increment and decrement buttons in Safari.\\n*/\\n\\n::-webkit-inner-spin-button,\\n::-webkit-outer-spin-button {\\n  height: auto;\\n}\\n\\n/*\\n1. Correct the odd appearance in Chrome and Safari.\\n2. Correct the outline style in Safari.\\n*/\\n\\n[type='search'] {\\n  -webkit-appearance: textfield; /* 1 */\\n  outline-offset: -2px; /* 2 */\\n}\\n\\n/*\\nRemove the inner padding in Chrome and Safari on macOS.\\n*/\\n\\n::-webkit-search-decoration {\\n  -webkit-appearance: none;\\n}\\n\\n/*\\n1. Correct the inability to style clickable types in iOS and Safari.\\n2. Change font properties to `inherit` in Safari.\\n*/\\n\\n::-webkit-file-upload-button {\\n  -webkit-appearance: button; /* 1 */\\n  font: inherit; /* 2 */\\n}\\n\\n/*\\nAdd the correct display in Chrome and Safari.\\n*/\\n\\nsummary {\\n  display: list-item;\\n}\\n\\n/*\\nRemoves the default spacing and border for appropriate elements.\\n*/\\n\\nblockquote,\\ndl,\\ndd,\\nh1,\\nh2,\\nh3,\\nh4,\\nh5,\\nh6,\\nhr,\\nfigure,\\np,\\npre {\\n  margin: 0;\\n}\\n\\nfieldset {\\n  margin: 0;\\n  padding: 0;\\n}\\n\\nlegend {\\n  padding: 0;\\n}\\n\\nol,\\nul,\\nmenu {\\n  list-style: none;\\n  margin: 0;\\n  padding: 0;\\n}\\n\\n/*\\nReset default styling for dialogs.\\n*/\\ndialog {\\n  padding: 0;\\n}\\n\\n/*\\nPrevent resizing textareas horizontally by default.\\n*/\\n\\ntextarea {\\n  resize: vertical;\\n}\\n\\n/*\\n1. Reset the default placeholder opacity in Firefox. (https://github.com/tailwindlabs/tailwindcss/issues/3300)\\n2. Set the default placeholder color to the user's configured gray 400 color.\\n*/\\n\\ninput::-moz-placeholder, textarea::-moz-placeholder {\\n  opacity: 1; /* 1 */\\n  color: #9ca3af; /* 2 */\\n}\\n\\ninput::placeholder,\\ntextarea::placeholder {\\n  opacity: 1; /* 1 */\\n  color: #9ca3af; /* 2 */\\n}\\n\\n/*\\nSet the default cursor for buttons.\\n*/\\n\\nbutton,\\n[role=\\\"button\\\"] {\\n  cursor: pointer;\\n}\\n\\n/*\\nMake sure disabled buttons don't get the pointer cursor.\\n*/\\n:disabled {\\n  cursor: default;\\n}\\n\\n/*\\n1. Make replaced elements `display: block` by default. (https://github.com/mozdevs/cssremedy/issues/14)\\n2. Add `vertical-align: middle` to align replaced elements more sensibly by default. (https://github.com/jensimmons/cssremedy/issues/14#issuecomment-634934210)\\n   This can trigger a poorly considered lint error in some tools but is included by design.\\n*/\\n\\nimg,\\nsvg,\\nvideo,\\ncanvas,\\naudio,\\niframe,\\nembed,\\nobject {\\n  display: block; /* 1 */\\n  vertical-align: middle; /* 2 */\\n}\\n\\n/*\\nConstrain images and videos to the parent width and preserve their intrinsic aspect ratio. (https://github.com/mozdevs/cssremedy/issues/14)\\n*/\\n\\nimg,\\nvideo {\\n  max-width: 100%;\\n  height: auto;\\n}\\n\\n/* Make elements with the HTML hidden attribute stay hidden by default */\\n[hidden] {\\n  display: none;\\n}\\n\\n*, ::before, ::after {\\n  --tw-border-spacing-x: 0;\\n  --tw-border-spacing-y: 0;\\n  --tw-translate-x: 0;\\n  --tw-translate-y: 0;\\n  --tw-rotate: 0;\\n  --tw-skew-x: 0;\\n  --tw-skew-y: 0;\\n  --tw-scale-x: 1;\\n  --tw-scale-y: 1;\\n  --tw-pan-x:  ;\\n  --tw-pan-y:  ;\\n  --tw-pinch-zoom:  ;\\n  --tw-scroll-snap-strictness: proximity;\\n  --tw-gradient-from-position:  ;\\n  --tw-gradient-via-position:  ;\\n  --tw-gradient-to-position:  ;\\n  --tw-ordinal:  ;\\n  --tw-slashed-zero:  ;\\n  --tw-numeric-figure:  ;\\n  --tw-numeric-spacing:  ;\\n  --tw-numeric-fraction:  ;\\n  --tw-ring-inset:  ;\\n  --tw-ring-offset-width: 0px;\\n  --tw-ring-offset-color: #fff;\\n  --tw-ring-color: rgb(59 130 246 / 0.5);\\n  --tw-ring-offset-shadow: 0 0 #0000;\\n  --tw-ring-shadow: 0 0 #0000;\\n  --tw-shadow: 0 0 #0000;\\n  --tw-shadow-colored: 0 0 #0000;\\n  --tw-blur:  ;\\n  --tw-brightness:  ;\\n  --tw-contrast:  ;\\n  --tw-grayscale:  ;\\n  --tw-hue-rotate:  ;\\n  --tw-invert:  ;\\n  --tw-saturate:  ;\\n  --tw-sepia:  ;\\n  --tw-drop-shadow:  ;\\n  --tw-backdrop-blur:  ;\\n  --tw-backdrop-brightness:  ;\\n  --tw-backdrop-contrast:  ;\\n  --tw-backdrop-grayscale:  ;\\n  --tw-backdrop-hue-rotate:  ;\\n  --tw-backdrop-invert:  ;\\n  --tw-backdrop-opacity:  ;\\n  --tw-backdrop-saturate:  ;\\n  --tw-backdrop-sepia:  ;\\n}\\n\\n::backdrop {\\n  --tw-border-spacing-x: 0;\\n  --tw-border-spacing-y: 0;\\n  --tw-translate-x: 0;\\n  --tw-translate-y: 0;\\n  --tw-rotate: 0;\\n  --tw-skew-x: 0;\\n  --tw-skew-y: 0;\\n  --tw-scale-x: 1;\\n  --tw-scale-y: 1;\\n  --tw-pan-x:  ;\\n  --tw-pan-y:  ;\\n  --tw-pinch-zoom:  ;\\n  --tw-scroll-snap-strictness: proximity;\\n  --tw-gradient-from-position:  ;\\n  --tw-gradient-via-position:  ;\\n  --tw-gradient-to-position:  ;\\n  --tw-ordinal:  ;\\n  --tw-slashed-zero:  ;\\n  --tw-numeric-figure:  ;\\n  --tw-numeric-spacing:  ;\\n  --tw-numeric-fraction:  ;\\n  --tw-ring-inset:  ;\\n  --tw-ring-offset-width: 0px;\\n  --tw-ring-offset-color: #fff;\\n  --tw-ring-color: rgb(59 130 246 / 0.5);\\n  --tw-ring-offset-shadow: 0 0 #0000;\\n  --tw-ring-shadow: 0 0 #0000;\\n  --tw-shadow: 0 0 #0000;\\n  --tw-shadow-colored: 0 0 #0000;\\n  --tw-blur:  ;\\n  --tw-brightness:  ;\\n  --tw-contrast:  ;\\n  --tw-grayscale:  ;\\n  --tw-hue-rotate:  ;\\n  --tw-invert:  ;\\n  --tw-saturate:  ;\\n  --tw-sepia:  ;\\n  --tw-drop-shadow:  ;\\n  --tw-backdrop-blur:  ;\\n  --tw-backdrop-brightness:  ;\\n  --tw-backdrop-contrast:  ;\\n  --tw-backdrop-grayscale:  ;\\n  --tw-backdrop-hue-rotate:  ;\\n  --tw-backdrop-invert:  ;\\n  --tw-backdrop-opacity:  ;\\n  --tw-backdrop-saturate:  ;\\n  --tw-backdrop-sepia:  ;\\n}\\r\\n.container {\\n  width: 100%;\\n}\\r\\n@media (min-width: 640px) {\\n\\n  .container {\\n    max-width: 640px;\\n  }\\n}\\r\\n@media (min-width: 765px) {\\n\\n  .container {\\n    max-width: 765px;\\n  }\\n}\\r\\n@media (min-width: 1024px) {\\n\\n  .container {\\n    max-width: 1024px;\\n  }\\n}\\r\\n@media (min-width: 1280px) {\\n\\n  .container {\\n    max-width: 1280px;\\n  }\\n}\\r\\n@media (min-width: 1536px) {\\n\\n  .container {\\n    max-width: 1536px;\\n  }\\n}\\r\\n@media (min-width: 1920px) {\\n\\n  .container {\\n    max-width: 1920px;\\n  }\\n}\\r\\n.sr-only {\\n  position: absolute;\\n  width: 1px;\\n  height: 1px;\\n  padding: 0;\\n  margin: -1px;\\n  overflow: hidden;\\n  clip: rect(0, 0, 0, 0);\\n  white-space: nowrap;\\n  border-width: 0;\\n}\\r\\n.pointer-events-none {\\n  pointer-events: none;\\n}\\r\\n.visible {\\n  visibility: visible;\\n}\\r\\n.fixed {\\n  position: fixed;\\n}\\r\\n.\\\\!absolute {\\n  position: absolute !important;\\n}\\r\\n.absolute {\\n  position: absolute;\\n}\\r\\n.relative {\\n  position: relative;\\n}\\r\\n.sticky {\\n  position: sticky;\\n}\\r\\n.inset-0 {\\n  inset: 0px;\\n}\\r\\n.\\\\!right-1 {\\n  right: 0.25rem !important;\\n}\\r\\n.\\\\!right-2 {\\n  right: 0.5rem !important;\\n}\\r\\n.\\\\!top-1 {\\n  top: 0.25rem !important;\\n}\\r\\n.-top-0 {\\n  top: -0px;\\n}\\r\\n.-top-2 {\\n  top: -0.5rem;\\n}\\r\\n.-top-3 {\\n  top: -0.75rem;\\n}\\r\\n.-top-4 {\\n  top: -1rem;\\n}\\r\\n.-top-\\\\[0\\\\.9\\\\] {\\n  top: -0.9;\\n}\\r\\n.-top-\\\\[2px\\\\] {\\n  top: -2px;\\n}\\r\\n.bottom-0 {\\n  bottom: 0px;\\n}\\r\\n.bottom-\\\\[-20px\\\\] {\\n  bottom: -20px;\\n}\\r\\n.left-0 {\\n  left: 0px;\\n}\\r\\n.left-1\\\\/2 {\\n  left: 50%;\\n}\\r\\n.left-2 {\\n  left: 0.5rem;\\n}\\r\\n.left-5 {\\n  left: 1.25rem;\\n}\\r\\n.left-\\\\[42\\\\%\\\\] {\\n  left: 42%;\\n}\\r\\n.right-0 {\\n  right: 0px;\\n}\\r\\n.right-1 {\\n  right: 0.25rem;\\n}\\r\\n.right-10 {\\n  right: 2.5rem;\\n}\\r\\n.right-2 {\\n  right: 0.5rem;\\n}\\r\\n.right-5 {\\n  right: 1.25rem;\\n}\\r\\n.top-0 {\\n  top: 0px;\\n}\\r\\n.top-1 {\\n  top: 0.25rem;\\n}\\r\\n.top-1\\\\/2 {\\n  top: 50%;\\n}\\r\\n.top-2 {\\n  top: 0.5rem;\\n}\\r\\n.top-\\\\[52px\\\\] {\\n  top: 52px;\\n}\\r\\n.top-full {\\n  top: 100%;\\n}\\r\\n.\\\\!z-\\\\[10\\\\] {\\n  z-index: 10 !important;\\n}\\r\\n.\\\\!z-\\\\[9999999\\\\] {\\n  z-index: 9999999 !important;\\n}\\r\\n.\\\\!z-\\\\[9\\\\] {\\n  z-index: 9 !important;\\n}\\r\\n.z-10 {\\n  z-index: 10;\\n}\\r\\n.z-20 {\\n  z-index: 20;\\n}\\r\\n.z-50 {\\n  z-index: 50;\\n}\\r\\n.z-\\\\[5\\\\] {\\n  z-index: 5;\\n}\\r\\n.z-\\\\[999\\\\] {\\n  z-index: 999;\\n}\\r\\n.col-span-2 {\\n  grid-column: span 2 / span 2;\\n}\\r\\n.col-start-6 {\\n  grid-column-start: 6;\\n}\\r\\n.m-1 {\\n  margin: 0.25rem;\\n}\\r\\n.m-3 {\\n  margin: 0.75rem;\\n}\\r\\n.m-4 {\\n  margin: 1rem;\\n}\\r\\n.m-5 {\\n  margin: 1.25rem;\\n}\\r\\n.\\\\!my-3 {\\n  margin-top: 0.75rem !important;\\n  margin-bottom: 0.75rem !important;\\n}\\r\\n.mx-2 {\\n  margin-left: 0.5rem;\\n  margin-right: 0.5rem;\\n}\\r\\n.mx-4 {\\n  margin-left: 1rem;\\n  margin-right: 1rem;\\n}\\r\\n.mx-auto {\\n  margin-left: auto;\\n  margin-right: auto;\\n}\\r\\n.my-1 {\\n  margin-top: 0.25rem;\\n  margin-bottom: 0.25rem;\\n}\\r\\n.my-2 {\\n  margin-top: 0.5rem;\\n  margin-bottom: 0.5rem;\\n}\\r\\n.my-32 {\\n  margin-top: 8rem;\\n  margin-bottom: 8rem;\\n}\\r\\n.my-4 {\\n  margin-top: 1rem;\\n  margin-bottom: 1rem;\\n}\\r\\n.my-5 {\\n  margin-top: 1.25rem;\\n  margin-bottom: 1.25rem;\\n}\\r\\n.my-6 {\\n  margin-top: 1.5rem;\\n  margin-bottom: 1.5rem;\\n}\\r\\n.my-8 {\\n  margin-top: 2rem;\\n  margin-bottom: 2rem;\\n}\\r\\n.\\\\!mt-2 {\\n  margin-top: 0.5rem !important;\\n}\\r\\n.\\\\!mt-3 {\\n  margin-top: 0.75rem !important;\\n}\\r\\n.-mt-1 {\\n  margin-top: -0.25rem;\\n}\\r\\n.-mt-\\\\[1px\\\\] {\\n  margin-top: -1px;\\n}\\r\\n.-mt-\\\\[2px\\\\] {\\n  margin-top: -2px;\\n}\\r\\n.mb-0 {\\n  margin-bottom: 0px;\\n}\\r\\n.mb-1 {\\n  margin-bottom: 0.25rem;\\n}\\r\\n.mb-10 {\\n  margin-bottom: 2.5rem;\\n}\\r\\n.mb-2 {\\n  margin-bottom: 0.5rem;\\n}\\r\\n.mb-20 {\\n  margin-bottom: 5rem;\\n}\\r\\n.mb-3 {\\n  margin-bottom: 0.75rem;\\n}\\r\\n.mb-4 {\\n  margin-bottom: 1rem;\\n}\\r\\n.mb-5 {\\n  margin-bottom: 1.25rem;\\n}\\r\\n.mb-6 {\\n  margin-bottom: 1.5rem;\\n}\\r\\n.mb-8 {\\n  margin-bottom: 2rem;\\n}\\r\\n.mb-\\\\[18px\\\\] {\\n  margin-bottom: 18px;\\n}\\r\\n.mb-\\\\[2px\\\\] {\\n  margin-bottom: 2px;\\n}\\r\\n.me-10 {\\n  margin-inline-end: 2.5rem;\\n}\\r\\n.me-3 {\\n  margin-inline-end: 0.75rem;\\n}\\r\\n.me-4 {\\n  margin-inline-end: 1rem;\\n}\\r\\n.me-5 {\\n  margin-inline-end: 1.25rem;\\n}\\r\\n.ml-0 {\\n  margin-left: 0px;\\n}\\r\\n.ml-1 {\\n  margin-left: 0.25rem;\\n}\\r\\n.ml-2 {\\n  margin-left: 0.5rem;\\n}\\r\\n.ml-3 {\\n  margin-left: 0.75rem;\\n}\\r\\n.ml-4 {\\n  margin-left: 1rem;\\n}\\r\\n.ml-5 {\\n  margin-left: 1.25rem;\\n}\\r\\n.ml-8 {\\n  margin-left: 2rem;\\n}\\r\\n.ml-\\\\[18\\\\%\\\\] {\\n  margin-left: 18%;\\n}\\r\\n.ml-\\\\[28px\\\\] {\\n  margin-left: 28px;\\n}\\r\\n.ml-\\\\[46px\\\\] {\\n  margin-left: 46px;\\n}\\r\\n.ml-auto {\\n  margin-left: auto;\\n}\\r\\n.mr-1 {\\n  margin-right: 0.25rem;\\n}\\r\\n.mr-12 {\\n  margin-right: 3rem;\\n}\\r\\n.mr-2 {\\n  margin-right: 0.5rem;\\n}\\r\\n.mr-20 {\\n  margin-right: 5rem;\\n}\\r\\n.mr-3 {\\n  margin-right: 0.75rem;\\n}\\r\\n.mr-4 {\\n  margin-right: 1rem;\\n}\\r\\n.mr-\\\\[23px\\\\] {\\n  margin-right: 23px;\\n}\\r\\n.mr-\\\\[25px\\\\] {\\n  margin-right: 25px;\\n}\\r\\n.mr-\\\\[55px\\\\] {\\n  margin-right: 55px;\\n}\\r\\n.mr-\\\\[5px\\\\] {\\n  margin-right: 5px;\\n}\\r\\n.mt-0 {\\n  margin-top: 0px;\\n}\\r\\n.mt-1 {\\n  margin-top: 0.25rem;\\n}\\r\\n.mt-14 {\\n  margin-top: 3.5rem;\\n}\\r\\n.mt-2 {\\n  margin-top: 0.5rem;\\n}\\r\\n.mt-3 {\\n  margin-top: 0.75rem;\\n}\\r\\n.mt-4 {\\n  margin-top: 1rem;\\n}\\r\\n.mt-5 {\\n  margin-top: 1.25rem;\\n}\\r\\n.mt-6 {\\n  margin-top: 1.5rem;\\n}\\r\\n.mt-7 {\\n  margin-top: 1.75rem;\\n}\\r\\n.mt-8 {\\n  margin-top: 2rem;\\n}\\r\\n.mt-9 {\\n  margin-top: 2.25rem;\\n}\\r\\n.mt-\\\\[17px\\\\] {\\n  margin-top: 17px;\\n}\\r\\n.mt-\\\\[2px\\\\] {\\n  margin-top: 2px;\\n}\\r\\n.mt-\\\\[45px\\\\] {\\n  margin-top: 45px;\\n}\\r\\n.mt-\\\\[60px\\\\] {\\n  margin-top: 60px;\\n}\\r\\n.block {\\n  display: block;\\n}\\r\\n.inline-block {\\n  display: inline-block;\\n}\\r\\n.inline {\\n  display: inline;\\n}\\r\\n.\\\\!flex {\\n  display: flex !important;\\n}\\r\\n.flex {\\n  display: flex;\\n}\\r\\n.inline-flex {\\n  display: inline-flex;\\n}\\r\\n.table {\\n  display: table;\\n}\\r\\n.grid {\\n  display: grid;\\n}\\r\\n.hidden {\\n  display: none;\\n}\\r\\n.\\\\!h-0 {\\n  height: 0px !important;\\n}\\r\\n.h-10 {\\n  height: 2.5rem;\\n}\\r\\n.h-12 {\\n  height: 3rem;\\n}\\r\\n.h-14 {\\n  height: 3.5rem;\\n}\\r\\n.h-4 {\\n  height: 1rem;\\n}\\r\\n.h-5 {\\n  height: 1.25rem;\\n}\\r\\n.h-52 {\\n  height: 13rem;\\n}\\r\\n.h-6 {\\n  height: 1.5rem;\\n}\\r\\n.h-8 {\\n  height: 2rem;\\n}\\r\\n.h-9 {\\n  height: 2.25rem;\\n}\\r\\n.h-\\\\[100vh\\\\] {\\n  height: 100vh;\\n}\\r\\n.h-\\\\[15px\\\\] {\\n  height: 15px;\\n}\\r\\n.h-\\\\[20px\\\\] {\\n  height: 20px;\\n}\\r\\n.h-\\\\[22px\\\\] {\\n  height: 22px;\\n}\\r\\n.h-\\\\[23px\\\\] {\\n  height: 23px;\\n}\\r\\n.h-\\\\[25px\\\\] {\\n  height: 25px;\\n}\\r\\n.h-\\\\[28px\\\\] {\\n  height: 28px;\\n}\\r\\n.h-\\\\[30px\\\\] {\\n  height: 30px;\\n}\\r\\n.h-\\\\[31px\\\\] {\\n  height: 31px;\\n}\\r\\n.h-\\\\[36px\\\\] {\\n  height: 36px;\\n}\\r\\n.h-\\\\[38px\\\\] {\\n  height: 38px;\\n}\\r\\n.h-\\\\[40px\\\\] {\\n  height: 40px;\\n}\\r\\n.h-\\\\[calc\\\\(100vh-100px\\\\)\\\\] {\\n  height: calc(100vh - 100px);\\n}\\r\\n.h-full {\\n  height: 100%;\\n}\\r\\n.\\\\!h-16 {\\n  height: 4rem !important;\\n}\\r\\n.h-24 {\\n  height: 6rem;\\n}\\r\\n.h-\\\\[85vh\\\\] {\\n  height: 85vh;\\n}\\r\\n.\\\\!max-h-\\\\[28rem\\\\] {\\n  max-height: 28rem !important;\\n}\\r\\n.\\\\!max-h-\\\\[70vh\\\\] {\\n  max-height: 70vh !important;\\n}\\r\\n.\\\\!max-h-full {\\n  max-height: 100% !important;\\n}\\r\\n.min-h-full {\\n  min-height: 100%;\\n}\\r\\n.\\\\!w-10 {\\n  width: 2.5rem !important;\\n}\\r\\n.\\\\!w-2\\\\/5 {\\n  width: 40% !important;\\n}\\r\\n.\\\\!w-28 {\\n  width: 7rem !important;\\n}\\r\\n.\\\\!w-32 {\\n  width: 8rem !important;\\n}\\r\\n.\\\\!w-44 {\\n  width: 11rem !important;\\n}\\r\\n.\\\\!w-48 {\\n  width: 12rem !important;\\n}\\r\\n.\\\\!w-52 {\\n  width: 13rem !important;\\n}\\r\\n.\\\\!w-60 {\\n  width: 15rem !important;\\n}\\r\\n.\\\\!w-72 {\\n  width: 18rem !important;\\n}\\r\\n.\\\\!w-80 {\\n  width: 20rem !important;\\n}\\r\\n.\\\\!w-\\\\[20\\\\%\\\\] {\\n  width: 20% !important;\\n}\\r\\n.\\\\!w-\\\\[450px\\\\] {\\n  width: 450px !important;\\n}\\r\\n.\\\\!w-full {\\n  width: 100% !important;\\n}\\r\\n.w-1\\\\/2 {\\n  width: 50%;\\n}\\r\\n.w-1\\\\/3 {\\n  width: 33.333333%;\\n}\\r\\n.w-1\\\\/4 {\\n  width: 25%;\\n}\\r\\n.w-1\\\\/5 {\\n  width: 20%;\\n}\\r\\n.w-11 {\\n  width: 2.75rem;\\n}\\r\\n.w-16 {\\n  width: 4rem;\\n}\\r\\n.w-2\\\\/3 {\\n  width: 66.666667%;\\n}\\r\\n.w-2\\\\/5 {\\n  width: 40%;\\n}\\r\\n.w-2\\\\/6 {\\n  width: 33.333333%;\\n}\\r\\n.w-28 {\\n  width: 7rem;\\n}\\r\\n.w-3\\\\/4 {\\n  width: 75%;\\n}\\r\\n.w-4 {\\n  width: 1rem;\\n}\\r\\n.w-5 {\\n  width: 1.25rem;\\n}\\r\\n.w-6 {\\n  width: 1.5rem;\\n}\\r\\n.w-8 {\\n  width: 2rem;\\n}\\r\\n.w-\\\\[10\\\\%\\\\] {\\n  width: 10%;\\n}\\r\\n.w-\\\\[100\\\\%-70px\\\\] {\\n  width: 100%-70px;\\n}\\r\\n.w-\\\\[100px\\\\] {\\n  width: 100px;\\n}\\r\\n.w-\\\\[115px\\\\] {\\n  width: 115px;\\n}\\r\\n.w-\\\\[130px\\\\] {\\n  width: 130px;\\n}\\r\\n.w-\\\\[150px\\\\] {\\n  width: 150px;\\n}\\r\\n.w-\\\\[15px\\\\] {\\n  width: 15px;\\n}\\r\\n.w-\\\\[160px\\\\] {\\n  width: 160px;\\n}\\r\\n.w-\\\\[20\\\\%\\\\] {\\n  width: 20%;\\n}\\r\\n.w-\\\\[22px\\\\] {\\n  width: 22px;\\n}\\r\\n.w-\\\\[25\\\\%\\\\] {\\n  width: 25%;\\n}\\r\\n.w-\\\\[25px\\\\] {\\n  width: 25px;\\n}\\r\\n.w-\\\\[30\\\\%\\\\] {\\n  width: 30%;\\n}\\r\\n.w-\\\\[30px\\\\] {\\n  width: 30px;\\n}\\r\\n.w-\\\\[40\\\\%\\\\] {\\n  width: 40%;\\n}\\r\\n.w-\\\\[44\\\\%\\\\] {\\n  width: 44%;\\n}\\r\\n.w-\\\\[45\\\\%\\\\] {\\n  width: 45%;\\n}\\r\\n.w-\\\\[47vh\\\\] {\\n  width: 47vh;\\n}\\r\\n.w-\\\\[48\\\\%\\\\] {\\n  width: 48%;\\n}\\r\\n.w-\\\\[5\\\\%\\\\] {\\n  width: 5%;\\n}\\r\\n.w-\\\\[50\\\\%\\\\] {\\n  width: 50%;\\n}\\r\\n.w-\\\\[500px\\\\] {\\n  width: 500px;\\n}\\r\\n.w-\\\\[60\\\\%\\\\] {\\n  width: 60%;\\n}\\r\\n.w-\\\\[75\\\\%\\\\] {\\n  width: 75%;\\n}\\r\\n.w-\\\\[8\\\\%\\\\] {\\n  width: 8%;\\n}\\r\\n.w-\\\\[93\\\\%\\\\] {\\n  width: 93%;\\n}\\r\\n.w-\\\\[94\\\\%\\\\] {\\n  width: 94%;\\n}\\r\\n.w-\\\\[95\\\\%\\\\] {\\n  width: 95%;\\n}\\r\\n.w-\\\\[96\\\\%\\\\] {\\n  width: 96%;\\n}\\r\\n.w-\\\\[calc\\\\(100\\\\%-20px\\\\)\\\\] {\\n  width: calc(100% - 20px);\\n}\\r\\n.w-auto {\\n  width: auto;\\n}\\r\\n.w-full {\\n  width: 100%;\\n}\\r\\n.w-screen {\\n  width: 100vw;\\n}\\r\\n.\\\\!w-auto {\\n  width: auto !important;\\n}\\r\\n.w-80 {\\n  width: 20rem;\\n}\\r\\n.w-\\\\[15\\\\%\\\\] {\\n  width: 15%;\\n}\\r\\n.\\\\!min-w-0 {\\n  min-width: 0px !important;\\n}\\r\\n.\\\\!min-w-fit {\\n  min-width: -moz-fit-content !important;\\n  min-width: fit-content !important;\\n}\\r\\n.\\\\!max-w-\\\\[60\\\\%\\\\] {\\n  max-width: 60% !important;\\n}\\r\\n.\\\\!max-w-\\\\[600px\\\\] {\\n  max-width: 600px !important;\\n}\\r\\n.\\\\!max-w-\\\\[90\\\\%\\\\] {\\n  max-width: 90% !important;\\n}\\r\\n.max-w-\\\\[288px\\\\] {\\n  max-width: 288px;\\n}\\r\\n.flex-1 {\\n  flex: 1 1 0%;\\n}\\r\\n.table-fixed {\\n  table-layout: fixed;\\n}\\r\\n.border-collapse {\\n  border-collapse: collapse;\\n}\\r\\n.-translate-x-1\\\\/2 {\\n  --tw-translate-x: -50%;\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\r\\n.-translate-x-\\\\[58\\\\.5\\\\%\\\\] {\\n  --tw-translate-x: -58.5%;\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\r\\n.-translate-y-1\\\\/2 {\\n  --tw-translate-y: -50%;\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\r\\n.translate-y-0 {\\n  --tw-translate-y: 0px;\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\r\\n.translate-y-1 {\\n  --tw-translate-y: 0.25rem;\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\r\\n.-rotate-90 {\\n  --tw-rotate: -90deg;\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\r\\n.rotate-180 {\\n  --tw-rotate: 180deg;\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\r\\n.rotate-45 {\\n  --tw-rotate: 45deg;\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\r\\n.scale-100 {\\n  --tw-scale-x: 1;\\n  --tw-scale-y: 1;\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\r\\n.scale-95 {\\n  --tw-scale-x: .95;\\n  --tw-scale-y: .95;\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\r\\n.scale-x-100 {\\n  --tw-scale-x: 1;\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\r\\n.transform {\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\r\\n.cursor-default {\\n  cursor: default;\\n}\\r\\n.cursor-not-allowed {\\n  cursor: not-allowed;\\n}\\r\\n.cursor-pointer {\\n  cursor: pointer;\\n}\\r\\n.resize-none {\\n  resize: none;\\n}\\r\\n.resize {\\n  resize: both;\\n}\\r\\n.list-inside {\\n  list-style-position: inside;\\n}\\r\\n.list-disc {\\n  list-style-type: disc;\\n}\\r\\n.appearance-none {\\n  -webkit-appearance: none;\\n     -moz-appearance: none;\\n          appearance: none;\\n}\\r\\n.grid-cols-1 {\\n  grid-template-columns: repeat(1, minmax(0, 1fr));\\n}\\r\\n.grid-cols-2 {\\n  grid-template-columns: repeat(2, minmax(0, 1fr));\\n}\\r\\n.grid-cols-3 {\\n  grid-template-columns: repeat(3, minmax(0, 1fr));\\n}\\r\\n.grid-cols-4 {\\n  grid-template-columns: repeat(4, minmax(0, 1fr));\\n}\\r\\n.grid-cols-8 {\\n  grid-template-columns: repeat(8, minmax(0, 1fr));\\n}\\r\\n.flex-row {\\n  flex-direction: row;\\n}\\r\\n.flex-col {\\n  flex-direction: column;\\n}\\r\\n.flex-wrap {\\n  flex-wrap: wrap;\\n}\\r\\n.items-start {\\n  align-items: flex-start;\\n}\\r\\n.items-end {\\n  align-items: flex-end;\\n}\\r\\n.items-center {\\n  align-items: center;\\n}\\r\\n.items-baseline {\\n  align-items: baseline;\\n}\\r\\n.items-stretch {\\n  align-items: stretch;\\n}\\r\\n.justify-start {\\n  justify-content: flex-start;\\n}\\r\\n.justify-end {\\n  justify-content: flex-end;\\n}\\r\\n.justify-center {\\n  justify-content: center;\\n}\\r\\n.\\\\!justify-between {\\n  justify-content: space-between !important;\\n}\\r\\n.justify-between {\\n  justify-content: space-between;\\n}\\r\\n.justify-around {\\n  justify-content: space-around;\\n}\\r\\n.\\\\!gap-0 {\\n  gap: 0px !important;\\n}\\r\\n.gap-1 {\\n  gap: 0.25rem;\\n}\\r\\n.gap-10 {\\n  gap: 2.5rem;\\n}\\r\\n.gap-2 {\\n  gap: 0.5rem;\\n}\\r\\n.gap-3 {\\n  gap: 0.75rem;\\n}\\r\\n.gap-4 {\\n  gap: 1rem;\\n}\\r\\n.gap-5 {\\n  gap: 1.25rem;\\n}\\r\\n.gap-6 {\\n  gap: 1.5rem;\\n}\\r\\n.gap-8 {\\n  gap: 2rem;\\n}\\r\\n.gap-12 {\\n  gap: 3rem;\\n}\\r\\n.space-x-1 > :not([hidden]) ~ :not([hidden]) {\\n  --tw-space-x-reverse: 0;\\n  margin-right: calc(0.25rem * var(--tw-space-x-reverse));\\n  margin-left: calc(0.25rem * calc(1 - var(--tw-space-x-reverse)));\\n}\\r\\n.space-x-2 > :not([hidden]) ~ :not([hidden]) {\\n  --tw-space-x-reverse: 0;\\n  margin-right: calc(0.5rem * var(--tw-space-x-reverse));\\n  margin-left: calc(0.5rem * calc(1 - var(--tw-space-x-reverse)));\\n}\\r\\n.space-y-6 > :not([hidden]) ~ :not([hidden]) {\\n  --tw-space-y-reverse: 0;\\n  margin-top: calc(1.5rem * calc(1 - var(--tw-space-y-reverse)));\\n  margin-bottom: calc(1.5rem * var(--tw-space-y-reverse));\\n}\\r\\n.overflow-auto {\\n  overflow: auto;\\n}\\r\\n.\\\\!overflow-hidden {\\n  overflow: hidden !important;\\n}\\r\\n.overflow-hidden {\\n  overflow: hidden;\\n}\\r\\n.overflow-x-auto {\\n  overflow-x: auto;\\n}\\r\\n.overflow-y-auto {\\n  overflow-y: auto;\\n}\\r\\n.whitespace-nowrap {\\n  white-space: nowrap;\\n}\\r\\n.\\\\!rounded-md {\\n  border-radius: 0.375rem !important;\\n}\\r\\n.\\\\!rounded-xl {\\n  border-radius: 0.75rem !important;\\n}\\r\\n.rounded {\\n  border-radius: 0.25rem;\\n}\\r\\n.rounded-\\\\[4px\\\\] {\\n  border-radius: 4px;\\n}\\r\\n.rounded-full {\\n  border-radius: 9999px;\\n}\\r\\n.rounded-lg {\\n  border-radius: 0.5rem;\\n}\\r\\n.rounded-md {\\n  border-radius: 0.375rem;\\n}\\r\\n.rounded-sm {\\n  border-radius: 0.125rem;\\n}\\r\\n.rounded-xl {\\n  border-radius: 0.75rem;\\n}\\r\\n.rounded-t {\\n  border-top-left-radius: 0.25rem;\\n  border-top-right-radius: 0.25rem;\\n}\\r\\n.rounded-b-lg {\\n  border-bottom-right-radius: 0.5rem;\\n  border-bottom-left-radius: 0.5rem;\\n}\\r\\n.rounded-t-lg {\\n  border-top-left-radius: 0.5rem;\\n  border-top-right-radius: 0.5rem;\\n}\\r\\n.rounded-bl-lg {\\n  border-bottom-left-radius: 0.5rem;\\n}\\r\\n.rounded-bl-md {\\n  border-bottom-left-radius: 0.375rem;\\n}\\r\\n.rounded-br-lg {\\n  border-bottom-right-radius: 0.5rem;\\n}\\r\\n.rounded-br-md {\\n  border-bottom-right-radius: 0.375rem;\\n}\\r\\n.rounded-tl-lg {\\n  border-top-left-radius: 0.5rem;\\n}\\r\\n.rounded-tl-md {\\n  border-top-left-radius: 0.375rem;\\n}\\r\\n.rounded-tl-xl {\\n  border-top-left-radius: 0.75rem;\\n}\\r\\n.rounded-tr-lg {\\n  border-top-right-radius: 0.5rem;\\n}\\r\\n.rounded-tr-md {\\n  border-top-right-radius: 0.375rem;\\n}\\r\\n.rounded-tr-xl {\\n  border-top-right-radius: 0.75rem;\\n}\\r\\n.\\\\!border {\\n  border-width: 1px !important;\\n}\\r\\n.\\\\!border-0 {\\n  border-width: 0px !important;\\n}\\r\\n.border {\\n  border-width: 1px;\\n}\\r\\n.border-0 {\\n  border-width: 0px;\\n}\\r\\n.border-b {\\n  border-bottom-width: 1px;\\n}\\r\\n.border-b-2 {\\n  border-bottom-width: 2px;\\n}\\r\\n.border-e-\\\\[1px\\\\] {\\n  border-inline-end-width: 1px;\\n}\\r\\n.border-l-4 {\\n  border-left-width: 4px;\\n}\\r\\n.border-r {\\n  border-right-width: 1px;\\n}\\r\\n.border-t {\\n  border-top-width: 1px;\\n}\\r\\n.\\\\!border-bright-red {\\n  --tw-border-opacity: 1 !important;\\n  border-color: rgb(249 54 71 / var(--tw-border-opacity)) !important;\\n}\\r\\n.\\\\!border-red-500 {\\n  --tw-border-opacity: 1 !important;\\n  border-color: rgb(239 68 68 / var(--tw-border-opacity)) !important;\\n}\\r\\n.\\\\!border-seablue {\\n  --tw-border-opacity: 1 !important;\\n  border-color: rgb(0 224 213 / var(--tw-border-opacity)) !important;\\n}\\r\\n.\\\\!border-skin-primary {\\n  --tw-border-opacity: 1 !important;\\n  border-color: rgba(var(--color-primary), var(--tw-border-opacity)) !important;\\n}\\r\\n.border-\\\\[\\\\#0066ff\\\\] {\\n  --tw-border-opacity: 1;\\n  border-color: rgb(0 102 255 / var(--tw-border-opacity));\\n}\\r\\n.border-\\\\[\\\\#FBB522\\\\] {\\n  --tw-border-opacity: 1;\\n  border-color: rgb(251 181 34 / var(--tw-border-opacity));\\n}\\r\\n.border-\\\\[\\\\#cccccc\\\\] {\\n  --tw-border-opacity: 1;\\n  border-color: rgb(204 204 204 / var(--tw-border-opacity));\\n}\\r\\n.border-\\\\[\\\\#ddd\\\\] {\\n  --tw-border-opacity: 1;\\n  border-color: rgb(221 221 221 / var(--tw-border-opacity));\\n}\\r\\n.border-blue-300 {\\n  --tw-border-opacity: 1;\\n  border-color: rgb(147 197 253 / var(--tw-border-opacity));\\n}\\r\\n.border-blue-500 {\\n  --tw-border-opacity: 1;\\n  border-color: rgb(59 130 246 / var(--tw-border-opacity));\\n}\\r\\n.border-bright-green {\\n  --tw-border-opacity: 1;\\n  border-color: rgb(62 171 88 / var(--tw-border-opacity));\\n}\\r\\n.border-bright-red {\\n  --tw-border-opacity: 1;\\n  border-color: rgb(249 54 71 / var(--tw-border-opacity));\\n}\\r\\n.border-gray-100 {\\n  --tw-border-opacity: 1;\\n  border-color: rgb(243 244 246 / var(--tw-border-opacity));\\n}\\r\\n.border-gray-200 {\\n  --tw-border-opacity: 1;\\n  border-color: rgb(229 231 235 / var(--tw-border-opacity));\\n}\\r\\n.border-gray-300 {\\n  --tw-border-opacity: 1;\\n  border-color: rgb(209 213 219 / var(--tw-border-opacity));\\n}\\r\\n.border-gray-400 {\\n  --tw-border-opacity: 1;\\n  border-color: rgb(156 163 175 / var(--tw-border-opacity));\\n}\\r\\n.border-light-gray {\\n  --tw-border-opacity: 1;\\n  border-color: rgb(214 214 214 / var(--tw-border-opacity));\\n}\\r\\n.border-light-gray2 {\\n  --tw-border-opacity: 1;\\n  border-color: rgb(176 176 176 / var(--tw-border-opacity));\\n}\\r\\n.border-light-gray3 {\\n  --tw-border-opacity: 1;\\n  border-color: rgb(211 211 211 / var(--tw-border-opacity));\\n}\\r\\n.border-lighter-gray {\\n  --tw-border-opacity: 1;\\n  border-color: rgb(175 175 175 / var(--tw-border-opacity));\\n}\\r\\n.border-red-500 {\\n  --tw-border-opacity: 1;\\n  border-color: rgb(239 68 68 / var(--tw-border-opacity));\\n}\\r\\n.border-save-green {\\n  --tw-border-opacity: 1;\\n  border-color: rgb(37 174 101 / var(--tw-border-opacity));\\n}\\r\\n.border-skin-primary {\\n  --tw-border-opacity: 1;\\n  border-color: rgba(var(--color-primary), var(--tw-border-opacity));\\n}\\r\\n.border-theme-blue2 {\\n  --tw-border-opacity: 1;\\n  border-color: rgb(0 102 255 / var(--tw-border-opacity));\\n}\\r\\n.border-white {\\n  --tw-border-opacity: 1;\\n  border-color: rgb(255 255 255 / var(--tw-border-opacity));\\n}\\r\\n.border-\\\\[\\\\#d3d3d3\\\\] {\\n  --tw-border-opacity: 1;\\n  border-color: rgb(211 211 211 / var(--tw-border-opacity));\\n}\\r\\n.\\\\!bg-\\\\[\\\\#f3f8ff\\\\] {\\n  --tw-bg-opacity: 1 !important;\\n  background-color: rgb(243 248 255 / var(--tw-bg-opacity)) !important;\\n}\\r\\n.\\\\!bg-be-status {\\n  --tw-bg-opacity: 1 !important;\\n  background-color: rgb(255 154 3 / var(--tw-bg-opacity)) !important;\\n}\\r\\n.\\\\!bg-confirm-status {\\n  --tw-bg-opacity: 1 !important;\\n  background-color: rgb(51 202 127 / var(--tw-bg-opacity)) !important;\\n}\\r\\n.\\\\!bg-gray-100 {\\n  --tw-bg-opacity: 1 !important;\\n  background-color: rgb(243 244 246 / var(--tw-bg-opacity)) !important;\\n}\\r\\n.\\\\!bg-issue-status {\\n  --tw-bg-opacity: 1 !important;\\n  background-color: rgb(251 70 70 / var(--tw-bg-opacity)) !important;\\n}\\r\\n.\\\\!bg-locked-products {\\n  --tw-bg-opacity: 1 !important;\\n  background-color: rgb(211 234 255 / var(--tw-bg-opacity)) !important;\\n}\\r\\n.\\\\!bg-needsupdate-status {\\n  --tw-bg-opacity: 1 !important;\\n  background-color: rgb(255 245 208 / var(--tw-bg-opacity)) !important;\\n}\\r\\n.\\\\!bg-pricechange-status {\\n  --tw-bg-opacity: 1 !important;\\n  background-color: rgb(44 176 250 / var(--tw-bg-opacity)) !important;\\n}\\r\\n.\\\\!bg-seablue {\\n  --tw-bg-opacity: 1 !important;\\n  background-color: rgb(0 224 213 / var(--tw-bg-opacity)) !important;\\n}\\r\\n.\\\\!bg-transparent {\\n  background-color: transparent !important;\\n}\\r\\n.\\\\!bg-volumechange-status {\\n  --tw-bg-opacity: 1 !important;\\n  background-color: rgb(179 187 221 / var(--tw-bg-opacity)) !important;\\n}\\r\\n.\\\\!bg-white {\\n  --tw-bg-opacity: 1 !important;\\n  background-color: rgb(255 255 255 / var(--tw-bg-opacity)) !important;\\n}\\r\\n.\\\\!bg-wip-status {\\n  --tw-bg-opacity: 1 !important;\\n  background-color: rgb(255 223 55 / var(--tw-bg-opacity)) !important;\\n}\\r\\n.bg-\\\\[\\\\#00E0D5\\\\] {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(0 224 213 / var(--tw-bg-opacity));\\n}\\r\\n.bg-\\\\[\\\\#3EAB58\\\\] {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(62 171 88 / var(--tw-bg-opacity));\\n}\\r\\n.bg-\\\\[\\\\#54C5ED\\\\] {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(84 197 237 / var(--tw-bg-opacity));\\n}\\r\\n.bg-\\\\[\\\\#F3F8FF\\\\] {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(243 248 255 / var(--tw-bg-opacity));\\n}\\r\\n.bg-\\\\[\\\\#FF6C09\\\\] {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(255 108 9 / var(--tw-bg-opacity));\\n}\\r\\n.bg-\\\\[\\\\#FFAE00\\\\] {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(255 174 0 / var(--tw-bg-opacity));\\n}\\r\\n.bg-\\\\[\\\\#f3f8ff\\\\] {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(243 248 255 / var(--tw-bg-opacity));\\n}\\r\\n.bg-\\\\[\\\\#ff2929\\\\] {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(255 41 41 / var(--tw-bg-opacity));\\n}\\r\\n.bg-be-status {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(255 154 3 / var(--tw-bg-opacity));\\n}\\r\\n.bg-black {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(0 0 0 / var(--tw-bg-opacity));\\n}\\r\\n.bg-black\\\\/25 {\\n  background-color: rgb(0 0 0 / 0.25);\\n}\\r\\n.bg-blue-50 {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(239 246 255 / var(--tw-bg-opacity));\\n}\\r\\n.bg-blue-600 {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(37 99 235 / var(--tw-bg-opacity));\\n}\\r\\n.bg-cancelled-status {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(255 108 9 / var(--tw-bg-opacity));\\n}\\r\\n.bg-complete-status {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(62 171 88 / var(--tw-bg-opacity));\\n}\\r\\n.bg-confirm-status {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(51 202 127 / var(--tw-bg-opacity));\\n}\\r\\n.bg-gray-100 {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(243 244 246 / var(--tw-bg-opacity));\\n}\\r\\n.bg-gray-200 {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(229 231 235 / var(--tw-bg-opacity));\\n}\\r\\n.bg-green-300 {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(134 239 172 / var(--tw-bg-opacity));\\n}\\r\\n.bg-green-500 {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(34 197 94 / var(--tw-bg-opacity));\\n}\\r\\n.bg-issue-status {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(251 70 70 / var(--tw-bg-opacity));\\n}\\r\\n.bg-locked-products {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(211 234 255 / var(--tw-bg-opacity));\\n}\\r\\n.bg-needsupdate-status {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(255 245 208 / var(--tw-bg-opacity));\\n}\\r\\n.bg-pricechange-status {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(44 176 250 / var(--tw-bg-opacity));\\n}\\r\\n.bg-qtydiff-status {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(255 203 71 / var(--tw-bg-opacity));\\n}\\r\\n.bg-red-500 {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(239 68 68 / var(--tw-bg-opacity));\\n}\\r\\n.bg-save-green {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(37 174 101 / var(--tw-bg-opacity));\\n}\\r\\n.bg-skin-primary {\\n  --tw-bg-opacity: 1;\\n  background-color: rgba(var(--color-primary), var(--tw-bg-opacity));\\n}\\r\\n.bg-theme-blue {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(0 45 115 / var(--tw-bg-opacity));\\n}\\r\\n.bg-theme-blue2 {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(0 102 255 / var(--tw-bg-opacity));\\n}\\r\\n.bg-transparent {\\n  background-color: transparent;\\n}\\r\\n.bg-volumechange-status {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(179 187 221 / var(--tw-bg-opacity));\\n}\\r\\n.bg-white {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(255 255 255 / var(--tw-bg-opacity));\\n}\\r\\n.bg-wip-status {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(255 223 55 / var(--tw-bg-opacity));\\n}\\r\\n.bg-opacity-25 {\\n  --tw-bg-opacity: 0.25;\\n}\\r\\n.bg-cover {\\n  background-size: cover;\\n}\\r\\n.fill-current {\\n  fill: currentColor;\\n}\\r\\n.fill-gray-300 {\\n  fill: #d1d5db;\\n}\\r\\n.fill-skin-primary {\\n  fill: rgb(var(--color-primary));\\n}\\r\\n.fill-white {\\n  fill: #FFFFFF;\\n}\\r\\n.\\\\!p-0 {\\n  padding: 0px !important;\\n}\\r\\n.\\\\!p-1 {\\n  padding: 0.25rem !important;\\n}\\r\\n.\\\\!p-3 {\\n  padding: 0.75rem !important;\\n}\\r\\n.\\\\!p-5 {\\n  padding: 1.25rem !important;\\n}\\r\\n.p-0 {\\n  padding: 0px;\\n}\\r\\n.p-1 {\\n  padding: 0.25rem;\\n}\\r\\n.p-2 {\\n  padding: 0.5rem;\\n}\\r\\n.p-3 {\\n  padding: 0.75rem;\\n}\\r\\n.p-4 {\\n  padding: 1rem;\\n}\\r\\n.p-6 {\\n  padding: 1.5rem;\\n}\\r\\n.p-8 {\\n  padding: 2rem;\\n}\\r\\n.p-\\\\[6px\\\\] {\\n  padding: 6px;\\n}\\r\\n.\\\\!px-1 {\\n  padding-left: 0.25rem !important;\\n  padding-right: 0.25rem !important;\\n}\\r\\n.\\\\!px-3 {\\n  padding-left: 0.75rem !important;\\n  padding-right: 0.75rem !important;\\n}\\r\\n.\\\\!px-4 {\\n  padding-left: 1rem !important;\\n  padding-right: 1rem !important;\\n}\\r\\n.\\\\!py-1 {\\n  padding-top: 0.25rem !important;\\n  padding-bottom: 0.25rem !important;\\n}\\r\\n.\\\\!py-3 {\\n  padding-top: 0.75rem !important;\\n  padding-bottom: 0.75rem !important;\\n}\\r\\n.px-0 {\\n  padding-left: 0px;\\n  padding-right: 0px;\\n}\\r\\n.px-1 {\\n  padding-left: 0.25rem;\\n  padding-right: 0.25rem;\\n}\\r\\n.px-14 {\\n  padding-left: 3.5rem;\\n  padding-right: 3.5rem;\\n}\\r\\n.px-2 {\\n  padding-left: 0.5rem;\\n  padding-right: 0.5rem;\\n}\\r\\n.px-2\\\\.5 {\\n  padding-left: 0.625rem;\\n  padding-right: 0.625rem;\\n}\\r\\n.px-3 {\\n  padding-left: 0.75rem;\\n  padding-right: 0.75rem;\\n}\\r\\n.px-4 {\\n  padding-left: 1rem;\\n  padding-right: 1rem;\\n}\\r\\n.px-5 {\\n  padding-left: 1.25rem;\\n  padding-right: 1.25rem;\\n}\\r\\n.px-6 {\\n  padding-left: 1.5rem;\\n  padding-right: 1.5rem;\\n}\\r\\n.px-8 {\\n  padding-left: 2rem;\\n  padding-right: 2rem;\\n}\\r\\n.px-9 {\\n  padding-left: 2.25rem;\\n  padding-right: 2.25rem;\\n}\\r\\n.py-0 {\\n  padding-top: 0px;\\n  padding-bottom: 0px;\\n}\\r\\n.py-1 {\\n  padding-top: 0.25rem;\\n  padding-bottom: 0.25rem;\\n}\\r\\n.py-2 {\\n  padding-top: 0.5rem;\\n  padding-bottom: 0.5rem;\\n}\\r\\n.py-3 {\\n  padding-top: 0.75rem;\\n  padding-bottom: 0.75rem;\\n}\\r\\n.py-4 {\\n  padding-top: 1rem;\\n  padding-bottom: 1rem;\\n}\\r\\n.py-5 {\\n  padding-top: 1.25rem;\\n  padding-bottom: 1.25rem;\\n}\\r\\n.py-\\\\[5px\\\\] {\\n  padding-top: 5px;\\n  padding-bottom: 5px;\\n}\\r\\n.px-\\\\[9px\\\\] {\\n  padding-left: 9px;\\n  padding-right: 9px;\\n}\\r\\n.\\\\!pb-3 {\\n  padding-bottom: 0.75rem !important;\\n}\\r\\n.pb-0 {\\n  padding-bottom: 0px;\\n}\\r\\n.pb-1 {\\n  padding-bottom: 0.25rem;\\n}\\r\\n.pb-2 {\\n  padding-bottom: 0.5rem;\\n}\\r\\n.pb-20 {\\n  padding-bottom: 5rem;\\n}\\r\\n.pb-3 {\\n  padding-bottom: 0.75rem;\\n}\\r\\n.pb-4 {\\n  padding-bottom: 1rem;\\n}\\r\\n.pb-8 {\\n  padding-bottom: 2rem;\\n}\\r\\n.pe-1 {\\n  padding-inline-end: 0.25rem;\\n}\\r\\n.pe-8 {\\n  padding-inline-end: 2rem;\\n}\\r\\n.pl-0 {\\n  padding-left: 0px;\\n}\\r\\n.pl-1 {\\n  padding-left: 0.25rem;\\n}\\r\\n.pl-10 {\\n  padding-left: 2.5rem;\\n}\\r\\n.pl-2 {\\n  padding-left: 0.5rem;\\n}\\r\\n.pl-3 {\\n  padding-left: 0.75rem;\\n}\\r\\n.pl-4 {\\n  padding-left: 1rem;\\n}\\r\\n.pl-\\\\[2px\\\\] {\\n  padding-left: 2px;\\n}\\r\\n.pl-\\\\[88px\\\\] {\\n  padding-left: 88px;\\n}\\r\\n.pr-2 {\\n  padding-right: 0.5rem;\\n}\\r\\n.pr-3 {\\n  padding-right: 0.75rem;\\n}\\r\\n.pr-4 {\\n  padding-right: 1rem;\\n}\\r\\n.pr-6 {\\n  padding-right: 1.5rem;\\n}\\r\\n.pr-\\\\[10px\\\\] {\\n  padding-right: 10px;\\n}\\r\\n.pr-\\\\[12px\\\\] {\\n  padding-right: 12px;\\n}\\r\\n.pr-\\\\[18px\\\\] {\\n  padding-right: 18px;\\n}\\r\\n.pr-\\\\[45px\\\\] {\\n  padding-right: 45px;\\n}\\r\\n.pr-\\\\[5px\\\\] {\\n  padding-right: 5px;\\n}\\r\\n.pr-\\\\[70px\\\\] {\\n  padding-right: 70px;\\n}\\r\\n.ps-8 {\\n  padding-inline-start: 2rem;\\n}\\r\\n.pt-0 {\\n  padding-top: 0px;\\n}\\r\\n.pt-1 {\\n  padding-top: 0.25rem;\\n}\\r\\n.pt-10 {\\n  padding-top: 2.5rem;\\n}\\r\\n.pt-2 {\\n  padding-top: 0.5rem;\\n}\\r\\n.pt-3 {\\n  padding-top: 0.75rem;\\n}\\r\\n.pt-4 {\\n  padding-top: 1rem;\\n}\\r\\n.pt-5 {\\n  padding-top: 1.25rem;\\n}\\r\\n.pt-6 {\\n  padding-top: 1.5rem;\\n}\\r\\n.pt-\\\\[0\\\\.4rem\\\\] {\\n  padding-top: 0.4rem;\\n}\\r\\n.pt-\\\\[80px\\\\] {\\n  padding-top: 80px;\\n}\\r\\n.pl-12 {\\n  padding-left: 3rem;\\n}\\r\\n.pr-12 {\\n  padding-right: 3rem;\\n}\\r\\n.\\\\!text-left {\\n  text-align: left !important;\\n}\\r\\n.text-left {\\n  text-align: left;\\n}\\r\\n.\\\\!text-center {\\n  text-align: center !important;\\n}\\r\\n.text-center {\\n  text-align: center;\\n}\\r\\n.text-right {\\n  text-align: right;\\n}\\r\\n.align-top {\\n  vertical-align: top;\\n}\\r\\n.align-middle {\\n  vertical-align: middle;\\n}\\r\\n.font-poppinsregular {\\n  font-family: poppinsregular;\\n}\\r\\n.font-poppinssemibold {\\n  font-family: poppinssemibold;\\n}\\r\\n.\\\\!text-base {\\n  font-size: 1rem !important;\\n  line-height: 1.5rem !important;\\n}\\r\\n.text-2xl {\\n  font-size: 1.5rem;\\n  line-height: 2rem;\\n}\\r\\n.text-4xl {\\n  font-size: 2.25rem;\\n  line-height: 2.5rem;\\n}\\r\\n.text-\\\\[10px\\\\] {\\n  font-size: 10px;\\n}\\r\\n.text-\\\\[16px\\\\] {\\n  font-size: 16px;\\n}\\r\\n.text-\\\\[20px\\\\] {\\n  font-size: 20px;\\n}\\r\\n.text-base {\\n  font-size: 1rem;\\n  line-height: 1.5rem;\\n}\\r\\n.text-lg {\\n  font-size: 1.125rem;\\n  line-height: 1.75rem;\\n}\\r\\n.text-sm {\\n  font-size: 0.875rem;\\n  line-height: 1.25rem;\\n}\\r\\n.text-xl {\\n  font-size: 1.25rem;\\n  line-height: 1.75rem;\\n}\\r\\n.text-xs {\\n  font-size: 0.75rem;\\n  line-height: 1rem;\\n}\\r\\n.\\\\!font-medium {\\n  font-weight: 500 !important;\\n}\\r\\n.\\\\!font-normal {\\n  font-weight: 400 !important;\\n}\\r\\n.font-bold {\\n  font-weight: 700;\\n}\\r\\n.font-medium {\\n  font-weight: 500;\\n}\\r\\n.font-normal {\\n  font-weight: 400;\\n}\\r\\n.font-semibold {\\n  font-weight: 600;\\n}\\r\\n.uppercase {\\n  text-transform: uppercase;\\n}\\r\\n.capitalize {\\n  text-transform: capitalize;\\n}\\r\\n.leading-5 {\\n  line-height: 1.25rem;\\n}\\r\\n.leading-8 {\\n  line-height: 2rem;\\n}\\r\\n.leading-\\\\[30px\\\\] {\\n  line-height: 30px;\\n}\\r\\n.leading-relaxed {\\n  line-height: 1.625;\\n}\\r\\n.tracking-tight {\\n  letter-spacing: -0.025em;\\n}\\r\\n.tracking-wider {\\n  letter-spacing: 0.05em;\\n}\\r\\n.tracking-wide {\\n  letter-spacing: 0.025em;\\n}\\r\\n.\\\\!text-\\\\[\\\\#333333\\\\] {\\n  --tw-text-opacity: 1 !important;\\n  color: rgb(51 51 51 / var(--tw-text-opacity)) !important;\\n}\\r\\n.\\\\!text-gray-300 {\\n  --tw-text-opacity: 1 !important;\\n  color: rgb(209 213 219 / var(--tw-text-opacity)) !important;\\n}\\r\\n.\\\\!text-gray-500 {\\n  --tw-text-opacity: 1 !important;\\n  color: rgb(107 114 128 / var(--tw-text-opacity)) !important;\\n}\\r\\n.\\\\!text-gray-700 {\\n  --tw-text-opacity: 1 !important;\\n  color: rgb(55 65 81 / var(--tw-text-opacity)) !important;\\n}\\r\\n.\\\\!text-red-500 {\\n  --tw-text-opacity: 1 !important;\\n  color: rgb(239 68 68 / var(--tw-text-opacity)) !important;\\n}\\r\\n.\\\\!text-skin-primary {\\n  --tw-text-opacity: 1 !important;\\n  color: rgba(var(--color-primary), var(--tw-text-opacity)) !important;\\n}\\r\\n.text-\\\\[\\\\#B31312\\\\] {\\n  --tw-text-opacity: 1;\\n  color: rgb(179 19 18 / var(--tw-text-opacity));\\n}\\r\\n.text-\\\\[\\\\#FBB522\\\\] {\\n  --tw-text-opacity: 1;\\n  color: rgb(251 181 34 / var(--tw-text-opacity));\\n}\\r\\n.text-\\\\[\\\\#ffffff\\\\] {\\n  --tw-text-opacity: 1;\\n  color: rgb(255 255 255 / var(--tw-text-opacity));\\n}\\r\\n.text-black {\\n  --tw-text-opacity: 1;\\n  color: rgb(0 0 0 / var(--tw-text-opacity));\\n}\\r\\n.text-blue-500 {\\n  --tw-text-opacity: 1;\\n  color: rgb(59 130 246 / var(--tw-text-opacity));\\n}\\r\\n.text-blue-600 {\\n  --tw-text-opacity: 1;\\n  color: rgb(37 99 235 / var(--tw-text-opacity));\\n}\\r\\n.text-bright-green {\\n  --tw-text-opacity: 1;\\n  color: rgb(62 171 88 / var(--tw-text-opacity));\\n}\\r\\n.text-bright-red {\\n  --tw-text-opacity: 1;\\n  color: rgb(249 54 71 / var(--tw-text-opacity));\\n}\\r\\n.text-bright-yellow {\\n  --tw-text-opacity: 1;\\n  color: rgb(251 181 34 / var(--tw-text-opacity));\\n}\\r\\n.text-dark-gray {\\n  --tw-text-opacity: 1;\\n  color: rgb(114 114 114 / var(--tw-text-opacity));\\n}\\r\\n.text-gray-300 {\\n  --tw-text-opacity: 1;\\n  color: rgb(209 213 219 / var(--tw-text-opacity));\\n}\\r\\n.text-gray-400 {\\n  --tw-text-opacity: 1;\\n  color: rgb(156 163 175 / var(--tw-text-opacity));\\n}\\r\\n.text-gray-500 {\\n  --tw-text-opacity: 1;\\n  color: rgb(107 114 128 / var(--tw-text-opacity));\\n}\\r\\n.text-gray-600 {\\n  --tw-text-opacity: 1;\\n  color: rgb(75 85 99 / var(--tw-text-opacity));\\n}\\r\\n.text-gray-800 {\\n  --tw-text-opacity: 1;\\n  color: rgb(31 41 55 / var(--tw-text-opacity));\\n}\\r\\n.text-gray-900 {\\n  --tw-text-opacity: 1;\\n  color: rgb(17 24 39 / var(--tw-text-opacity));\\n}\\r\\n.text-issue-status {\\n  --tw-text-opacity: 1;\\n  color: rgb(251 70 70 / var(--tw-text-opacity));\\n}\\r\\n.text-light-gray2 {\\n  --tw-text-opacity: 1;\\n  color: rgb(176 176 176 / var(--tw-text-opacity));\\n}\\r\\n.text-lighter-gray {\\n  --tw-text-opacity: 1;\\n  color: rgb(175 175 175 / var(--tw-text-opacity));\\n}\\r\\n.text-red-500 {\\n  --tw-text-opacity: 1;\\n  color: rgb(239 68 68 / var(--tw-text-opacity));\\n}\\r\\n.text-red-600 {\\n  --tw-text-opacity: 1;\\n  color: rgb(220 38 38 / var(--tw-text-opacity));\\n}\\r\\n.text-skin-a11y {\\n  --tw-text-opacity: 1;\\n  color: rgba(var(--color-a11y), var(--tw-text-opacity));\\n}\\r\\n.text-skin-primary {\\n  --tw-text-opacity: 1;\\n  color: rgba(var(--color-primary), var(--tw-text-opacity));\\n}\\r\\n.text-theme-blue2 {\\n  --tw-text-opacity: 1;\\n  color: rgb(0 102 255 / var(--tw-text-opacity));\\n}\\r\\n.text-white {\\n  --tw-text-opacity: 1;\\n  color: rgb(255 255 255 / var(--tw-text-opacity));\\n}\\r\\n.text-gray-700 {\\n  --tw-text-opacity: 1;\\n  color: rgb(55 65 81 / var(--tw-text-opacity));\\n}\\r\\n.underline {\\n  text-decoration-line: underline;\\n}\\r\\n.placeholder-gray-400::-moz-placeholder {\\n  --tw-placeholder-opacity: 1;\\n  color: rgb(156 163 175 / var(--tw-placeholder-opacity));\\n}\\r\\n.placeholder-gray-400::placeholder {\\n  --tw-placeholder-opacity: 1;\\n  color: rgb(156 163 175 / var(--tw-placeholder-opacity));\\n}\\r\\n.accent-skin-primary {\\n  accent-color: rgb(var(--color-primary));\\n}\\r\\n.opacity-0 {\\n  opacity: 0;\\n}\\r\\n.opacity-100 {\\n  opacity: 1;\\n}\\r\\n.opacity-50 {\\n  opacity: 0.5;\\n}\\r\\n.opacity-70 {\\n  opacity: 0.7;\\n}\\r\\n.\\\\!shadow-lg {\\n  --tw-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1) !important;\\n  --tw-shadow-colored: 0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -4px var(--tw-shadow-color) !important;\\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow) !important;\\n}\\r\\n.shadow {\\n  --tw-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);\\n  --tw-shadow-colored: 0 1px 3px 0 var(--tw-shadow-color), 0 1px 2px -1px var(--tw-shadow-color);\\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\\n}\\r\\n.shadow-lg {\\n  --tw-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);\\n  --tw-shadow-colored: 0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -4px var(--tw-shadow-color);\\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\\n}\\r\\n.shadow-sm {\\n  --tw-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05);\\n  --tw-shadow-colored: 0 1px 2px 0 var(--tw-shadow-color);\\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\\n}\\r\\n.shadow-xl {\\n  --tw-shadow: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);\\n  --tw-shadow-colored: 0 20px 25px -5px var(--tw-shadow-color), 0 8px 10px -6px var(--tw-shadow-color);\\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\\n}\\r\\n.shadow-md {\\n  --tw-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);\\n  --tw-shadow-colored: 0 4px 6px -1px var(--tw-shadow-color), 0 2px 4px -2px var(--tw-shadow-color);\\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\\n}\\r\\n.outline {\\n  outline-style: solid;\\n}\\r\\n.blur {\\n  --tw-blur: blur(8px);\\n  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);\\n}\\r\\n.filter {\\n  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);\\n}\\r\\n.backdrop-blur-none {\\n  --tw-backdrop-blur: blur(0);\\n  -webkit-backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);\\n          backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);\\n}\\r\\n.backdrop-blur-sm {\\n  --tw-backdrop-blur: blur(4px);\\n  -webkit-backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);\\n          backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);\\n}\\r\\n.transition {\\n  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, -webkit-backdrop-filter;\\n  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter;\\n  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter, -webkit-backdrop-filter;\\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\\n  transition-duration: 150ms;\\n}\\r\\n.transition-all {\\n  transition-property: all;\\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\\n  transition-duration: 150ms;\\n}\\r\\n.duration-150 {\\n  transition-duration: 150ms;\\n}\\r\\n.duration-200 {\\n  transition-duration: 200ms;\\n}\\r\\n.duration-300 {\\n  transition-duration: 300ms;\\n}\\r\\n.ease-in {\\n  transition-timing-function: cubic-bezier(0.4, 0, 1, 1);\\n}\\r\\n.ease-in-out {\\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\\n}\\r\\n.ease-out {\\n  transition-timing-function: cubic-bezier(0, 0, 0.2, 1);\\n}\\r\\n/*@import \\\"bootstrap/bootstrap\\\";*/\\r\\n\\r\\n@font-face {\\r\\n  font-family: poppinsblack;\\r\\n  src: url(\" + ___CSS_LOADER_URL_REPLACEMENT_0___ + \");\\r\\n}\\r\\n@font-face {\\r\\n  font-family: poppinsblackitalic;\\r\\n  src: url(\" + ___CSS_LOADER_URL_REPLACEMENT_1___ + \");\\r\\n}\\r\\n@font-face {\\r\\n  font-family: poppinsbold;\\r\\n  src: url(\" + ___CSS_LOADER_URL_REPLACEMENT_2___ + \");\\r\\n}\\r\\n@font-face {\\r\\n  font-family: poppinsbolditalic;\\r\\n  src: url(\" + ___CSS_LOADER_URL_REPLACEMENT_3___ + \");\\r\\n}\\r\\n@font-face {\\r\\n  font-family: poppinsextrabold;\\r\\n  src: url(\" + ___CSS_LOADER_URL_REPLACEMENT_4___ + \");\\r\\n}\\r\\n@font-face {\\r\\n  font-family: poppinsextrabolditalic;\\r\\n  src: url(\" + ___CSS_LOADER_URL_REPLACEMENT_5___ + \");\\r\\n}\\r\\n@font-face {\\r\\n  font-family: poppinsextralight;\\r\\n  src: url(\" + ___CSS_LOADER_URL_REPLACEMENT_6___ + \");\\r\\n}\\r\\n@font-face {\\r\\n  font-family: poppinsextralightitalic;\\r\\n  src: url(\" + ___CSS_LOADER_URL_REPLACEMENT_7___ + \");\\r\\n}\\r\\n@font-face {\\r\\n  font-family: poppinsitalic;\\r\\n  src: url(\" + ___CSS_LOADER_URL_REPLACEMENT_8___ + \");\\r\\n}\\r\\n@font-face {\\r\\n  font-family: poppinslight;\\r\\n  src: url(\" + ___CSS_LOADER_URL_REPLACEMENT_9___ + \");\\r\\n}\\r\\n@font-face {\\r\\n  font-family: poppinslightitalic;\\r\\n  src: url(\" + ___CSS_LOADER_URL_REPLACEMENT_10___ + \");\\r\\n}\\r\\n@font-face {\\r\\n  font-family: poppinsmedium;\\r\\n  src: url(\" + ___CSS_LOADER_URL_REPLACEMENT_11___ + \");\\r\\n}\\r\\n@font-face {\\r\\n  font-family: poppinsmediumitalic;\\r\\n  src: url(\" + ___CSS_LOADER_URL_REPLACEMENT_12___ + \");\\r\\n}\\r\\n@font-face {\\r\\n  font-family: poppinsregular;\\r\\n  src: url(\" + ___CSS_LOADER_URL_REPLACEMENT_13___ + \");\\r\\n}\\r\\n@font-face {\\r\\n  font-family: poppinssemibold;\\r\\n  src: url(\" + ___CSS_LOADER_URL_REPLACEMENT_14___ + \");\\r\\n}\\r\\n@font-face {\\r\\n  font-family: poppinssemibolditalic;\\r\\n  src: url(\" + ___CSS_LOADER_URL_REPLACEMENT_15___ + \");\\r\\n}\\r\\n@font-face {\\r\\n  font-family: poppinsitalic;\\r\\n  src: url(\" + ___CSS_LOADER_URL_REPLACEMENT_8___ + \");\\r\\n}\\r\\n@font-face {\\r\\n  font-family: poppinsitalic;\\r\\n  src: url(\" + ___CSS_LOADER_URL_REPLACEMENT_8___ + \");\\r\\n}\\r\\n\\r\\nbody,\\r\\nhtml {\\r\\n  height: 100%;\\r\\n  background-repeat: repeat;\\r\\n  font-family: \\\"poppinsregular\\\";\\r\\n  overflow-x: hidden;\\r\\n  background-color: #f3f8ff;\\r\\n  font-size: 12px;\\r\\n}\\r\\n\\r\\n.dark body,\\r\\nhtml {\\r\\n  height: 100%;\\r\\n  background-repeat: repeat;\\r\\n  font-family: \\\"poppinsregular\\\" !important;\\r\\n  overflow-x: hidden;\\r\\n  background-color: #0e0e10;\\r\\n  font-size: 14px;\\r\\n}\\r\\n\\r\\n.ag-header .ag-header-cell.header-with-border  {\\r\\n  border-bottom: 1px solid #ccc;\\r\\n}\\r\\n\\r\\n.ag-header-cell-text {\\r\\n  font-size: 12px;\\r\\n  color: #333333;\\r\\n  font-family: \\\"poppinsmedium\\\";\\r\\n}\\r\\n\\r\\nbutton[disabled] {\\r\\n  opacity: 0.5;\\r\\n  cursor: not-allowed;\\r\\n}\\r\\n\\r\\n.dark .ag-header-cell-text {\\r\\n  font-size: 12px;\\r\\n  color: #fff;\\r\\n  font-family: \\\"poppinsmedium\\\";\\r\\n}\\r\\n\\r\\n.ag-ltr .ag-cell {\\r\\n  color: #333333;\\r\\n  font-family: \\\"poppinsregular\\\";\\r\\n  font-size: 12px;\\r\\n}\\r\\n\\r\\n.dark .ag-ltr .ag-cell {\\r\\n  color: white;\\r\\n  font-family: \\\"poppinsregular\\\";\\r\\n  font-size: 12px;\\r\\n  background-color: #1d212d;\\r\\n}\\r\\n\\r\\n.ag-paging-row-summary-panel-number {\\r\\n  font-family: \\\"poppinsregular\\\";\\r\\n  font-size: 12px;\\r\\n}\\r\\n.dark .ag-paging-row-summary-panel-number {\\r\\n  font-family: \\\"poppinsregular\\\";\\r\\n  font-size: 12px;\\r\\n  color: white;\\r\\n}\\r\\n\\r\\n.ag-paging-page-summary-panel {\\r\\n  font-family: \\\"poppinsregular\\\";\\r\\n  font-size: 12px;\\r\\n}\\r\\n.dark .ag-paging-page-summary-panel {\\r\\n  font-family: \\\"poppinsregular\\\";\\r\\n  font-size: 12px;\\r\\n  color: white;\\r\\n}\\r\\n\\r\\n.pagination {\\r\\n  font-family: \\\"poppinsregular\\\";\\r\\n  font-size: 12px;\\r\\n}\\r\\n.dark .pagination {\\r\\n  font-family: \\\"poppinsregular\\\";\\r\\n  font-size: 12px;\\r\\n  color: white;\\r\\n}\\r\\n\\r\\n.ag-paging-panel {\\r\\n  justify-content: between;\\r\\n}\\r\\n.dark .ag-paging-panel {\\r\\n  justify-content: between;\\r\\n  color: white;\\r\\n  background-color: #1d212d;\\r\\n}\\r\\n\\r\\n.dark .ag-center-cols-viewport {\\r\\n  background-color: #1d212d;\\r\\n}\\r\\n\\r\\n.contentsectionbg {\\r\\n  background-color: white;\\r\\n}\\r\\n\\r\\n.dark .contentsectionbg {\\r\\n  background-color: #1d212d;\\r\\n  font-family: \\\"poppinsregular\\\";\\r\\n}\\r\\n\\r\\n.dark\\r\\n  .ag-header.ag-header-allow-overflow\\r\\n  .ag-header-row\\r\\n  .ag-root-wrapper.ag-layout-normal {\\r\\n  background-color: #1d212d;\\r\\n  font-family: \\\"poppinsregular\\\";\\r\\n}\\r\\n\\r\\n.dark .ag-header-container {\\r\\n  background-color: #1d212d;\\r\\n  font-family: \\\"poppinsregular\\\";\\r\\n}\\r\\n\\r\\n.dark .pagination-style {\\r\\n  color: white;\\r\\n  font-family: \\\"poppinsregular\\\";\\r\\n}\\r\\n.dark .ag-paging-button,\\r\\n.ag-paging-description {\\r\\n  font-family: \\\"poppinsregular\\\";\\r\\n  color: white;\\r\\n}\\r\\n.ag-paging-button,\\r\\n.ag-paging-description {\\r\\n  font-family: \\\"poppinsregular\\\";\\r\\n  color: #000;\\r\\n}\\r\\n.dark .ag-paging-button.ag-disabled {\\r\\n  font-family: \\\"poppinsregular\\\";\\r\\n  color: white;\\r\\n}\\r\\n\\r\\n.ag-body-horizontal-scroll-viewport {\\r\\n  display: none;\\r\\n} \\r\\n\\r\\n\\r\\n.ag-overlay-no-rows-center {\\r\\n  font-family: \\\"poppinsregular\\\";\\r\\n  font-size: 12px;\\r\\n}\\r\\n\\r\\nnav.sidebar .navbar-nav .open .dropdown-menu > li > a:hover,\\r\\nnav.sidebar .navbar-nav .open .dropdown-menu > li > a:focus {\\r\\n  color: #ccc;\\r\\n  background-color: transparent;\\r\\n}\\r\\n\\r\\nnav:hover .forAnimate {\\r\\n  opacity: 1;\\r\\n}\\r\\n\\r\\n.mainmenu {\\r\\n  background: #002d73;\\r\\n  border: #002d73;\\r\\n  border-radius: 0px;\\r\\n}\\r\\n\\r\\n.mainmenu .navbar-nav > .active > a,\\r\\n.mainmenu .navbar-nav > .active > a:hover,\\r\\n.mainmenu .navbar-nav > .active > a:focus {\\r\\n  color: #fff;\\r\\n  background-color: #2a3344;\\r\\n  border-left: 2px solid #1ca9c0;\\r\\n  padding-top: 15px;\\r\\n  padding-bottom: 15px;\\r\\n  display: flex;\\r\\n  flex-direction: column;\\r\\n  justify-content: center;\\r\\n}\\r\\n\\r\\n.mainmenu .navbar-nav > li > a:hover,\\r\\n.mainmenu .navbar-nav > li > a:focus {\\r\\n  color: #fff;\\r\\n  background-color: lightgray;\\r\\n  padding-top: 15px;\\r\\n  padding-bottom: 15px;\\r\\n}\\r\\n\\r\\n.navbar-default .navbar-nav > li > a {\\r\\n  color: #00b1a3;\\r\\n  padding-top: 15px;\\r\\n  padding-bottom: 15px;\\r\\n  display: flex;\\r\\n  flex-direction: column;\\r\\n  justify-content: center;\\r\\n  text-decoration: none;\\r\\n}\\r\\n\\r\\n.navbar {\\r\\n  min-height: 45px;\\r\\n}\\r\\n\\r\\n.page-heading {\\r\\n  color: #333333;\\r\\n  font-size: 14px;\\r\\n  margin-top: 0px !important;\\r\\n  margin-left: 25px;\\r\\n  /* position: absolute; */\\r\\n  top: 10px;\\r\\n  left: 90px;\\r\\n  font-weight: 600;\\r\\n}\\r\\n\\r\\n.page-heading h2 {\\r\\n  font-size: 16px;\\r\\n  margin-top: 15px !important;\\r\\n}\\r\\n\\r\\n/* ----------- Notification dropdown end ----------------------- */\\r\\n\\r\\nnav.sidebar .brand a {\\r\\n  padding: 0;\\r\\n}\\r\\n\\r\\n.brand {\\r\\n  font-size: 24px;\\r\\n  /* padding: 0px 5px; */\\r\\n  color: #fff;\\r\\n  width: 69px;\\r\\n  /* background-color: #002d73; */\\r\\n  text-align: center;\\r\\n  display: flex;\\r\\n  justify-content: center;\\r\\n  align-items: center;\\r\\n}\\r\\n\\r\\n.titlebar {\\r\\n  width: 100%;\\r\\n  position: fixed;\\r\\n  /* height: 55px; */\\r\\n  z-index: 98;\\r\\n  top: 0;\\r\\n  padding-left: 69px;\\r\\n}\\r\\n\\r\\n.accordion {\\r\\n  cursor: pointer;\\r\\n  width: 100%;\\r\\n  text-align: left;\\r\\n  outline: none;\\r\\n  transition: 0.4s;\\r\\n}\\r\\n\\r\\n.active,\\r\\n.accordion:hover {\\r\\n  background-color: transparent;\\r\\n}\\r\\n\\r\\n.panel {\\r\\n  background-color: white;\\r\\n  max-height: 0;\\r\\n  overflow: hidden;\\r\\n  transition: max-height 0.2s ease-out;\\r\\n}\\r\\n\\r\\n/* .reactSelectCustom .css-1fdsijx-ValueContainer , .reactSelectCustom .css-b62m3t-ValueContainer {\\r\\n  position: relative;\\r\\n  top: -5px;\\r\\n}\\r\\n\\r\\n.reactSelectCustom .css-1hb7zxy-IndicatorsContainer, .reactSelectCustom .css-1xc3v61-IndicatorsContainer{\\r\\n  position: relative;\\r\\n  top: -5px;\\r\\n} */\\r\\n/* .reactSelectCustom .css-1jgx7bw-control{\\r\\n  flex-wrap: nowrap !important;\\r\\n} */\\r\\ninput[type=date]:invalid::-ms-datetime-edit {\\r\\n  color: #808080;\\r\\n}\\r\\n/* label {\\r\\n  font-size: 14px;\\r\\n  color: #505050;\\r\\n} */\\r\\n.top-navbar {\\r\\n  background-color: white;\\r\\n}\\r\\n.dark .top-navbar {\\r\\n  background-color: #000;\\r\\n  color: white;\\r\\n}\\r\\n\\r\\n.dark .pageName {\\r\\n  color: white;\\r\\n}\\r\\n\\r\\ninput[type=\\\"number\\\"] {\\r\\n  -webkit-appearance: textfield;\\r\\n     -moz-appearance: textfield;\\r\\n          appearance: textfield;\\r\\n}\\r\\ninput[type=number]::-webkit-inner-spin-button, \\r\\ninput[type=number]::-webkit-outer-spin-button { \\r\\n  -webkit-appearance: none;\\r\\n}\\r\\n\\r\\ninput[type=\\\"text\\\"],\\r\\ninput[type=\\\"tel\\\"],\\r\\ninput[type=\\\"date\\\"],\\r\\ninput[type=\\\"email\\\"],\\r\\ninput[type=\\\"number\\\"],\\r\\nselect {\\r\\n  padding-top: 4px;\\r\\n  padding-bottom: 4px;\\r\\n  border-color: #d6d6d6;\\r\\n  outline: 2px solid #ffffff;\\r\\n  color: #333333;\\r\\n  font-family: \\\"poppinsregular\\\";\\r\\n}\\r\\n\\r\\ninput:focus, select:focus , textarea:focus{\\r\\n  outline-color: #0066ff;\\r\\n}\\r\\n\\r\\n.button {\\r\\n  padding: 4px 12px 4px 12px;\\r\\n  font-family: \\\"poppinsmedium\\\";\\r\\n}\\r\\n\\r\\n.labels {\\r\\n  font-family: \\\"poppinsregular\\\";\\r\\n  font-size: 12px;\\r\\n  color: #505050;\\r\\n}\\r\\n.dark .labels {\\r\\n  font-family: \\\"poppinsregular\\\";\\r\\n  font-size: 12px;\\r\\n  color: white;\\r\\n}\\r\\n\\r\\n.searchbar {\\r\\n  font-family: \\\"poppinsregular\\\";\\r\\n  font-size: 12px;\\r\\n  color: #AFAFAF;\\r\\n  background-color: #FFFFFF;\\r\\n}\\r\\n\\r\\n.dark .searchbar {\\r\\n  font-family: \\\"poppinsregular\\\";\\r\\n  font-size: 12px;\\r\\n  color: #c0c0c0;\\r\\n  background-color: #1d212d;\\r\\n  outline-color: #1d212d;\\r\\n}\\r\\n\\r\\n.checkboxbg {\\r\\n  background-color: #D9D9D9;\\r\\n}\\r\\n.dark .checkboxbg {\\r\\n  background-color: #4d4d4d;\\r\\n}\\r\\n\\r\\n.dark select {\\r\\n  background-color: #1d1d1d;\\r\\n  color: white;\\r\\n}\\r\\n\\r\\n.borderc-theme-blue2 {\\r\\n  border-color: #0066ff;\\r\\n}\\r\\n.dark .borderc-theme-blue2 {\\r\\n  border-color: #6699ff;\\r\\n}\\r\\n.textc-theme-blue2 {\\r\\n  color: #0066ff;\\r\\n}\\r\\n.dark .textc-theme-blue2 {\\r\\n  color: #6699ff;\\r\\n}\\r\\n\\r\\n.bgc-theme-blue2 {\\r\\n  background-color: #0066ff;\\r\\n}\\r\\n.dark .bgc-theme-blue2 {\\r\\n  background-color: #6699ff;\\r\\n}\\r\\n.textc-dark-gray {\\r\\n  color: #727272;\\r\\n}\\r\\n.dark .textc-dark-gray {\\r\\n  color: #a3a3a3;\\r\\n}\\r\\n.borderc-dark-gray {\\r\\n  color: #727272;\\r\\n}\\r\\n.dark .borderc-dark-gray {\\r\\n  color: #a3a3a3;\\r\\n}\\r\\n\\r\\n.input {\\r\\n  font-size: 12px;\\r\\n  font-family: \\\"poppinsregular\\\";\\r\\n}\\r\\n\\r\\n.dark .inputs {\\r\\n  font-family: \\\"poppinsregular\\\";\\r\\n  font-size: 12px;\\r\\n  color: white;\\r\\n}\\r\\n\\r\\n.confirmInputs {\\r\\n    font-size: 12px;\\r\\n    color: #333333;\\r\\n    font-family: \\\"poppinsmedium\\\";\\r\\n}\\r\\n\\r\\n.formtitle {\\r\\n  font-family: \\\"poppinssemibold\\\";\\r\\n  color: #333333;\\r\\n  font-size: 12px;\\r\\n}\\r\\n\\r\\n.subtitles {\\r\\n  font-family: \\\"poppinssemibold\\\";\\r\\n  color: #333333;\\r\\n  font-size: 14px;\\r\\n}\\r\\n\\r\\n.text-blackcolor {\\r\\n  color:#505050;\\r\\n  font-size: 12px;\\r\\n  font-family: \\\"poppinsregular\\\";\\r\\n}\\r\\n\\r\\n.ag-ltr .ag-cell-focus:not(.ag-cell-range-selected):focus-within {\\r\\n  border: none !important;\\r\\n}\\r\\n\\r\\n.ag-row-odd{\\r\\n  background-color: #f3f3f3 !important;\\r\\n}\\r\\n\\r\\n.ag-cell-value span {\\r\\n  height: auto !important;\\r\\n  line-height: normal !important;\\r\\n}\\r\\n\\r\\n.ag-cell{\\r\\n  display:flex;\\r\\n  align-items:center;\\r\\n  border: none !important;\\r\\n}\\r\\n\\r\\n.ag-cell-value, .ag-header-cell-text{\\r\\n  text-overflow: unset !important;\\r\\n}\\r\\n\\r\\n.pagination-style {\\r\\n  position: absolute;\\r\\n  bottom: 10px;\\r\\n  left: 20px;\\r\\n}\\r\\n\\r\\n.pagination-style select {\\r\\n  background-color: #f2f2f2;\\r\\n  border-radius: 3px;\\r\\n}\\r\\n\\r\\n.ag-ltr .ag-header-select-all{\\r\\n  margin-right: 12px !important; \\r\\n}\\r\\n\\r\\n.ag-root-wrapper.ag-layout-normal{\\r\\n  border-radius: 5px !important;\\r\\n}\\r\\n\\r\\n.distribution-point .ag-header-container {\\r\\n    /* width: 100% !important; */\\r\\n}\\r\\n\\r\\n.distribution-point .ag-header-row.ag-header-row-column {\\r\\n    /* width: 100% !important; */\\r\\n}\\r\\n\\r\\n.distribution-point .ag-center-cols-container {\\r\\n    /* width: 100% !important; */\\r\\n}\\r\\n\\r\\n.general_section .ag-header-container, .general_section .ag-center-cols-container, .general_section .ag-header-row  {\\r\\n  width: 100% !important;\\r\\n}\\r\\n\\r\\n.general_section .ag-cell {\\r\\n  /* display: flex; */\\r\\n  margin-right: 10px;\\r\\n}\\r\\n\\r\\n.product_link_def .ag-row .ag-cell {\\r\\n  display: flex;\\r\\n  align-items: center;\\r\\n}\\r\\n\\r\\n.product_data_def .ag-cell {\\r\\n  display: flex;\\r\\n  align-items: center;\\r\\n}\\r\\n\\r\\n/* @media (min-width: 640px) {\\r\\n  .ag-header-cell-text,.dark .ag-header-cell-text,.ag-ltr .ag-cell,.dark .ag-ltr .ag-cell,.ag-paging-row-summary-panel-number,.dark .ag-paging-row-summary-panel-number,.ag-paging-page-summary-panel,.dark .ag-paging-page-summary-panel,.pagination,.dark .pagination,.labels,.dark .labels,.searchbar,.dark .searchbar,.inputs,.dark .inputs,.formtitle{\\r\\n    font-size: 10px;\\r\\n  }\\r\\n} */\\r\\n\\r\\n.viewlog .ag-cell-value {\\r\\n    align-items: center;\\r\\n    display: flex;\\r\\n}\\r\\n\\r\\n@media (min-width: 765px) {\\r\\n  .main {\\r\\n    position: absolute;\\r\\n    width: calc(100% - 40px);\\r\\n    margin-left: 40px;\\r\\n    float: right;\\r\\n  }\\r\\n  nav.sidebar:hover + .main {\\r\\n    margin-left: 200px;\\r\\n  }\\r\\n  nav.sidebar.navbar.sidebar > .container .navbar-brand,\\r\\n  .navbar > .container-fluid .navbar-brand {\\r\\n    margin-left: 0px;\\r\\n  }\\r\\n  nav.sidebar .navbar-brand,\\r\\n  nav.sidebar .navbar-header {\\r\\n    text-align: center;\\r\\n    width: 100%;\\r\\n    margin-left: 0px;\\r\\n  }\\r\\n  /* nav.sidebar a {\\r\\n    padding-bottom: 34px;\\r\\n  } */\\r\\n\\r\\n  nav.sidebar .navbar-nav > li {\\r\\n    font-size: 13px;\\r\\n  }\\r\\n  nav.sidebar .navbar-nav .open .dropdown-menu {\\r\\n    position: static;\\r\\n    float: none;\\r\\n    width: auto;\\r\\n    margin-top: 0;\\r\\n    background-color: transparent;\\r\\n    border: 0;\\r\\n    box-shadow: none;\\r\\n  }\\r\\n  nav.sidebar .navbar-collapse,\\r\\n  nav.sidebar .container-fluid {\\r\\n    padding: 0 0px 0 0px;\\r\\n  }\\r\\n  .navbar-inverse .navbar-nav .open .dropdown-menu > li > a {\\r\\n    color: #777;\\r\\n  }\\r\\n  nav.sidebar {\\r\\n    width: 69px;\\r\\n    height: 100%;\\r\\n    margin-bottom: 0px;\\r\\n    position: fixed;\\r\\n    top: 0px;\\r\\n    display: flex;\\r\\n    align-items: flex-start;\\r\\n  }\\r\\n  nav.sidebar li {\\r\\n    width: 100%;\\r\\n  }\\r\\n\\r\\n  .forAnimate {\\r\\n    opacity: 0;\\r\\n  }\\r\\n}\\r\\n\\r\\n\\r\\n@media (min-width: 1024px) {\\r\\n  \\r\\n}\\r\\n\\r\\n/* @media (min-width: 1280px) {\\r\\n  .ag-header-cell-text,.dark .ag-header-cell-text,.ag-ltr .ag-cell,.dark .ag-ltr .ag-cell,.ag-paging-row-summary-panel-number,.dark .ag-paging-row-summary-panel-number,.ag-paging-page-summary-panel,.dark .ag-paging-page-summary-panel,.pagination,.dark .pagination,.labels,.dark .labels,.searchbar,.dark .searchbar,.inputs,.dark .inputs,.formtitle{\\r\\n    font-size: 14px;\\r\\n  }}\\r\\n\\r\\n@media (min-width: 1536px) {\\r\\n  \\r\\n}\\r\\n@media (min-width: 1940px) {\\r\\n  \\r\\n} */\\r\\n\\r\\n.desktop-view-message {\\r\\n  position: fixed;\\r\\n  top: 0;\\r\\n  left: 0;\\r\\n  width: 100%;\\r\\n  height: 100%;\\r\\n  background-color: rgba(0, 0, 0, 0.5); /* Semi-transparent black */\\r\\n  display: flex;\\r\\n  justify-content: center;\\r\\n  align-items: center;\\r\\n  z-index: 9999; /* Ensure it's on top of other content */\\r\\n}\\r\\n.block-view-message {\\r\\n  position: fixed;\\r\\n  top: 0;\\r\\n  left: 0;\\r\\n  width: 100%;\\r\\n  height: 100%;\\r\\n  background-color: rgba(214, 21, 21, 0.5); /* Semi-transparent black */\\r\\n  display: flex;\\r\\n  justify-content: center;\\r\\n  align-items: center;\\r\\n  z-index: 9999; /* Ensure it's on top of other content */\\r\\n}\\r\\n\\r\\n.message-content {\\r\\n  text-align: center;\\r\\n  color: white;\\r\\n}\\r\\n\\r\\n.message-content h2 {\\r\\n  font-size: 24px;\\r\\n  margin-bottom: 10px;\\r\\n}\\r\\n\\r\\n.message-content p {\\r\\n  font-size: 16px;\\r\\n}\\r\\n/* quarter filter css */\\r\\n\\r\\n #q1:checked  + .labelcheck, #q2:checked  + .labelcheck, #q3:checked  + .labelcheck, #q4:checked  + .labelcheck, #all:checked  + .labelcheck, #needsupdate:checked  + .labelcheck{\\r\\n  background-color: #00E0D5;\\r\\n  border:1px solid #00BBB2;\\r\\n  color:#fff;\\r\\n  font-weight: 500;\\r\\n  font-family: \\\"poppinsregular\\\";\\r\\n}\\r\\n/* section filter css */\\r\\n #volume:checked  + .labelcheck, #breakeven:checked  + .labelcheck, #unitprice:checked  + .labelcheck, #grossprofit:checked  + .labelcheck, #gppercent:checked  + .labelcheck, #value:checked  + .labelcheck{\\r\\n  background-color: #00E0D5;\\r\\n  border:1px solid #00BBB2;\\r\\n  color:#fff;\\r\\n  font-weight: 500;\\r\\n  font-family: \\\"poppinsregular\\\";\\r\\n}\\r\\n #bestatus-1:checked  + .labelcheck {\\r\\n  background-color: #FF9A03;\\r\\n  color:#fff;\\r\\n  font-weight: 500;\\r\\n  font-family: \\\"poppinsregular\\\";\\r\\n}\\r\\n#bestatus-2:checked  + .labelcheck{\\r\\n  background-color: #33CA7F;\\r\\n  /* border:1px solid #00BBB2; */\\r\\n  color:#444;\\r\\n  font-weight: 500;\\r\\n  font-family: \\\"poppinsregular\\\";\\r\\n}\\r\\n#bestatus-3:checked  + .labelcheck{\\r\\n  background-color: #FFDF37;\\r\\n  /* border:1px solid #00BBB2; */\\r\\n  color:#fff;\\r\\n  font-weight: 500;\\r\\n  font-family: \\\"poppinsregular\\\";\\r\\n}\\r\\n#bestatus-4:checked  + .labelcheck{\\r\\n  background-color: #FB4646;\\r\\n  /* border:1px solid #00BBB2; */\\r\\n  color:#fff;\\r\\n  font-weight: 500;\\r\\n  font-family: \\\"poppinsregular\\\";\\r\\n}\\r\\n\\r\\n.planningtoolgrid\\r\\n{\\r\\n   border-collapse: separate;\\r\\n   border-spacing: 0;\\r\\n   /* border: 1px solid #ddd; */\\r\\n}\\r\\n.input-number::-webkit-inner-spin-button,\\r\\n.input-number::-webkit-outer-spin-button {\\r\\n  -webkit-appearance: none;\\r\\n  margin: 0;\\r\\n}\\r\\n\\r\\n\\r\\n.planningtoolgrid thead th {\\r\\n  padding: 5px;\\r\\n  text-align: center;\\r\\n  position: -webkit-sticky; /* for Safari */\\r\\n  height: 35px;\\r\\n  width:90px;\\r\\n  overflow:hidden;\\r\\n  border: 1px solid #ddd;\\r\\n  font-family: \\\"poppinsregular\\\";\\r\\n}\\r\\n/* .planningtoolgrid thead tr, .planningtoolgrid tbody tr{\\r\\n  border: 1px solid #ddd;\\r\\n} */\\r\\n.planningtoolgrid thead tr:first-child th{\\r\\n  border: none !important;\\r\\n}\\r\\n/* .planningtoolgrid tbody tr{\\r\\n height:70px !important;\\r\\n} */\\r\\n.planningtoolgrid tbody th{\\r\\n  width:120px; \\r\\n  height: 40px;\\r\\n}\\r\\n.planningtoolgrid td{\\r\\n  padding: 5px;\\r\\n  border: 1px solid #ddd;\\r\\n  text-align: left;\\r\\n  z-index:0;\\r\\n  width:90px; \\r\\n  height: 35px !important;\\r\\n  overflow:hidden;\\r\\n  font-family: \\\"poppinsregular\\\";\\r\\n}\\r\\n.planningtoolgrid thead{\\r\\n  position:sticky;\\r\\n  top:0;\\r\\n  left:0;\\r\\n  background:#f3f8ff;\\r\\n  z-index:10;\\r\\n}\\r\\n\\r\\n.quartertotals{\\r\\n  /* border:0; */\\r\\n  table-layout: fixed;\\r\\n  width:50%;\\r\\n  text-align:center;\\r\\n}\\r\\n.quartertotals thead th, .quartertotals tbody td{\\r\\n  text-align: center;\\r\\n  border:0;\\r\\n}\\r\\n.quartertotals thead tr, .quartertotals tbody tr {\\r\\n    border: none;\\r\\n}\\r\\n.quartertotals thead th{\\r\\n  border-top-left-radius: 20px;\\r\\n  border-top-right-radius: 20px;\\r\\n  background-color: #fff !important;\\r\\n}\\r\\n.quartertotals thead{\\r\\n  z-index: 5 !important;\\r\\n}\\r\\n\\r\\n.quartertotals thead th:first-child, .quartertotals tbody td:first-child{\\r\\n  background-color: #f3f8ff !important;\\r\\n  width:50px !important;\\r\\n}\\r\\n.quartertotals tbody td:nth-child(2){\\r\\n  background-color: #201E50 !important;\\r\\n  color:#fff;\\r\\n  border-bottom: 1px solid #504F66;\\r\\n  border-right: 5px solid #f3f8ff;\\r\\n}\\r\\n.quartertotals tbody td:nth-child(3){\\r\\n  background-color: #D499B9 !important;\\r\\n  color:#fff;\\r\\n  border-bottom: 1px solid #C0749E;\\r\\n  border-right: 5px solid #f3f8ff;\\r\\n  border-left: 5px solid #f3f8ff;\\r\\n}\\r\\n.quartertotals tbody td:nth-child(4){\\r\\n  background-color: #EE6C4D !important;\\r\\n  color:#fff;\\r\\n  border-bottom: 1px solid #CE4B2C;\\r\\n  border-left: 5px solid #f3f8ff;\\r\\n}\\r\\n.quartertotals tbody tr:last-child td{\\r\\n    border-bottom-left-radius: 15px;\\r\\n    border-bottom-right-radius: 15px;\\r\\n    border-bottom: none;\\r\\n}\\r\\n\\r\\n.titlerow{\\r\\n  background-color: #00E0D5;\\r\\n  color:#fff;\\r\\n  z-index: 9;\\r\\n  font-size: 15px;\\r\\n  height: 28px !important;\\r\\n}\\r\\ntd.sectionrow,th.sectionrow{\\r\\n  background-color: #DDD;\\r\\n  color:#444;\\r\\n  font-weight: 600;\\r\\n  z-index: 9;\\r\\n  height: 28px !important;\\r\\n  font-size: 13px !important;\\r\\n}\\r\\ninput[type=\\\"text\\\"]:disabled, input[type=\\\"number\\\"]:disabled, select:disabled{\\r\\n  background-color: #F6F3F3;\\r\\n}\\r\\n\\r\\n#wrapper.closed .list {\\r\\n  display: none;\\r\\n   transition: height 200ms;\\r\\n}\\r\\n\\r\\n#wrapper.open .list {\\r\\n  display: block;\\r\\n   transition: height 200ms;\\r\\n}\\r\\n\\r\\n.bg-currentWeek{\\r\\n  background-color: #fcf6b1;\\r\\n}\\r\\n.nodata{\\r\\n  background-color:#ffecd4;\\r\\n  color:#b31818\\r\\n}\\r\\n.selected{\\r\\n  color: #0066FF;\\r\\n  border-bottom: 3px solid #0066FF;\\r\\n}\\r\\n.buttonText{\\r\\n  font-family: 'poppinsmedium';\\r\\n  \\r\\n}\\r\\n\\r\\n/*--- SLP CSS ---*/\\r\\n\\r\\n.service-level-grid{\\r\\n   border-collapse: separate;\\r\\n   border-spacing: 0;\\r\\n}\\r\\n\\r\\n.service-level-grid thead th {\\r\\n  padding: 5px;\\r\\n  text-align: left;\\r\\n  position: -webkit-sticky; /* for Safari */\\r\\n  height: 35px;\\r\\n  width:90px;\\r\\n  overflow:hidden;\\r\\n  border: 1px solid #ddd;\\r\\n  white-space: normal;\\r\\n  background:#f3f8ff;\\r\\n  font-family: \\\"poppinsregular\\\";\\r\\n}\\r\\n\\r\\n/* .service-level-grid thead tr:first-child th{\\r\\n  border: none !important;\\r\\n} */\\r\\n.service-level-grid thead tr th:last-child{\\r\\n  background:#f3f8ff;\\r\\n  position:sticky;\\r\\n  right:0;\\r\\n}\\r\\n.service-level-grid tbody tr td:last-child{\\r\\n  background:#fff;\\r\\n  position:sticky;\\r\\n  right:0;\\r\\n}\\r\\n\\r\\n.service-level-grid tbody th{\\r\\n  width:120px; \\r\\n  height: 40px;\\r\\n  border: 1px solid #ddd;\\r\\n  padding:5px;\\r\\n  z-index: 5;\\r\\n  background:#f3f8ff;\\r\\n  font-family: \\\"poppinsregular\\\";\\r\\n}\\r\\n.service-level-grid td{\\r\\n  padding: 5px;\\r\\n  border: 1px solid #ddd;\\r\\n  background-color:white;\\r\\n  text-align: left;\\r\\n  z-index:0;\\r\\n  width:90px; \\r\\n  height: 35px !important;\\r\\n  overflow:hidden;\\r\\n  font-family: \\\"poppinsregular\\\";\\r\\n}\\r\\n.service-level-grid thead{\\r\\n  position:sticky;\\r\\n  top:0;\\r\\n  left:0;\\r\\n  /* background:#f3f8ff; */\\r\\n  z-index:10;\\r\\n}\\r\\n\\r\\n.ag-grid-checkbox-cell .ag-checkbox-input {\\r\\n  cursor: pointer;\\r\\n}\\r\\n\\r\\n.depotdaterange .rdrDateDisplayWrapper{\\r\\n  display:none;\\r\\n}\\r\\n\\r\\nlabel{\\r\\n  font-family: \\\"poppinsregular\\\";\\r\\n}\\r\\nselect::-moz-placeholder {\\r\\n  color: #333333 !important;\\r\\n  opacity: 0.5;\\r\\n  font-weight: 500;\\r\\n  font-family:\\\"poppinsregular\\\";\\r\\n}\\r\\nselect::placeholder, .css-1jqq78o-placeholder, .placeholdertext {\\r\\n  color: #333333 !important;\\r\\n  opacity: 0.5;\\r\\n  font-weight: 500;\\r\\n  font-family:\\\"poppinsregular\\\";\\r\\n}\\r\\n.text-truncate2L {\\r\\n  display: -webkit-box;\\r\\n  -webkit-box-orient: vertical;\\r\\n  -webkit-line-clamp: 2;\\r\\n  overflow: hidden;\\r\\n  text-overflow: ellipsis;\\r\\n  }\\r\\n\\r\\n  .variety-disabled-block{\\r\\n    background-color: #f6f6f6;\\r\\n    color: #888888;\\r\\n    border:1px solid #E2E2E2;\\r\\n    box-shadow: none;\\r\\n  }\\r\\n  .variety-disabled-block h4, .variety-disabled-block label{\\r\\n    color:#797979;\\r\\n  }\\r\\n  .after\\\\:absolute::after {\\n  content: var(--tw-content);\\n  position: absolute;\\n}\\r\\n  .after\\\\:left-\\\\[1px\\\\]::after {\\n  content: var(--tw-content);\\n  left: 1px;\\n}\\r\\n  .after\\\\:top-2::after {\\n  content: var(--tw-content);\\n  top: 0.5rem;\\n}\\r\\n  .after\\\\:h-5::after {\\n  content: var(--tw-content);\\n  height: 1.25rem;\\n}\\r\\n  .after\\\\:w-5::after {\\n  content: var(--tw-content);\\n  width: 1.25rem;\\n}\\r\\n  .after\\\\:translate-x-0::after {\\n  content: var(--tw-content);\\n  --tw-translate-x: 0px;\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\r\\n  .after\\\\:rounded-full::after {\\n  content: var(--tw-content);\\n  border-radius: 9999px;\\n}\\r\\n  .after\\\\:border::after {\\n  content: var(--tw-content);\\n  border-width: 1px;\\n}\\r\\n  .after\\\\:border-light-gray::after {\\n  content: var(--tw-content);\\n  --tw-border-opacity: 1;\\n  border-color: rgb(214 214 214 / var(--tw-border-opacity));\\n}\\r\\n  .after\\\\:bg-white::after {\\n  content: var(--tw-content);\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(255 255 255 / var(--tw-bg-opacity));\\n}\\r\\n  .after\\\\:transition-all::after {\\n  content: var(--tw-content);\\n  transition-property: all;\\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\\n  transition-duration: 150ms;\\n}\\r\\n  .after\\\\:content-\\\\[\\\\'\\\\'\\\\]::after {\\n  --tw-content: '';\\n  content: var(--tw-content);\\n}\\r\\n  .focus-within\\\\:text-gray-600:focus-within {\\n  --tw-text-opacity: 1;\\n  color: rgb(75 85 99 / var(--tw-text-opacity));\\n}\\r\\n  .focus-within\\\\:outline-none:focus-within {\\n  outline: 2px solid transparent;\\n  outline-offset: 2px;\\n}\\r\\n  .hover\\\\:cursor-pointer:hover {\\n  cursor: pointer;\\n}\\r\\n  .hover\\\\:border-l-4:hover {\\n  border-left-width: 4px;\\n}\\r\\n  .hover\\\\:border-white:hover {\\n  --tw-border-opacity: 1;\\n  border-color: rgb(255 255 255 / var(--tw-border-opacity));\\n}\\r\\n  .hover\\\\:bg-blue-800:hover {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(30 64 175 / var(--tw-bg-opacity));\\n}\\r\\n  .hover\\\\:bg-confirm-status\\\\/80:hover {\\n  background-color: rgb(51 202 127 / 0.8);\\n}\\r\\n  .hover\\\\:bg-gray-100:hover {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(243 244 246 / var(--tw-bg-opacity));\\n}\\r\\n  .hover\\\\:bg-gray-200:hover {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(229 231 235 / var(--tw-bg-opacity));\\n}\\r\\n  .hover\\\\:bg-gray-50:hover {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(249 250 251 / var(--tw-bg-opacity));\\n}\\r\\n  .hover\\\\:bg-lighter-gray:hover {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(175 175 175 / var(--tw-bg-opacity));\\n}\\r\\n  .hover\\\\:bg-red-500:hover {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(239 68 68 / var(--tw-bg-opacity));\\n}\\r\\n  .hover\\\\:bg-skin-primary:hover {\\n  --tw-bg-opacity: 1;\\n  background-color: rgba(var(--color-primary), var(--tw-bg-opacity));\\n}\\r\\n  .hover\\\\:text-gray-900:hover {\\n  --tw-text-opacity: 1;\\n  color: rgb(17 24 39 / var(--tw-text-opacity));\\n}\\r\\n  .hover\\\\:text-black:hover {\\n  --tw-text-opacity: 1;\\n  color: rgb(0 0 0 / var(--tw-text-opacity));\\n}\\r\\n  .hover\\\\:opacity-80:hover {\\n  opacity: 0.8;\\n}\\r\\n  .focus\\\\:outline-none:focus {\\n  outline: 2px solid transparent;\\n  outline-offset: 2px;\\n}\\r\\n  .focus\\\\:outline-2:focus {\\n  outline-width: 2px;\\n}\\r\\n  .focus\\\\:\\\\!outline-skin-primary:focus {\\n  outline-color: rgb(var(--color-primary)) !important;\\n}\\r\\n  .focus\\\\:ring-2:focus {\\n  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);\\n  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);\\n  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);\\n}\\r\\n  .focus\\\\:ring-4:focus {\\n  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);\\n  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(4px + var(--tw-ring-offset-width)) var(--tw-ring-color);\\n  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);\\n}\\r\\n  .focus\\\\:ring-blue-300:focus {\\n  --tw-ring-opacity: 1;\\n  --tw-ring-color: rgb(147 197 253 / var(--tw-ring-opacity));\\n}\\r\\n  .focus\\\\:ring-blue-500:focus {\\n  --tw-ring-opacity: 1;\\n  --tw-ring-color: rgb(59 130 246 / var(--tw-ring-opacity));\\n}\\r\\n  .disabled\\\\:bg-slate-600:disabled {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(71 85 105 / var(--tw-bg-opacity));\\n}\\r\\n  .group:hover .group-hover\\\\:block {\\n  display: block;\\n}\\r\\n  .group:hover .group-hover\\\\:text-white {\\n  --tw-text-opacity: 1;\\n  color: rgb(255 255 255 / var(--tw-text-opacity));\\n}\\r\\n  .peer:checked ~ .peer-checked\\\\:bg-blue-600 {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(37 99 235 / var(--tw-bg-opacity));\\n}\\r\\n  .peer:checked ~ .peer-checked\\\\:bg-skin-primary {\\n  --tw-bg-opacity: 1;\\n  background-color: rgba(var(--color-primary), var(--tw-bg-opacity));\\n}\\r\\n  .peer:checked ~ .peer-checked\\\\:after\\\\:left-\\\\[3px\\\\]::after {\\n  content: var(--tw-content);\\n  left: 3px;\\n}\\r\\n  .peer:checked ~ .peer-checked\\\\:after\\\\:translate-x-full::after {\\n  content: var(--tw-content);\\n  --tw-translate-x: 100%;\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\r\\n  .peer:checked ~ .peer-checked\\\\:after\\\\:border-white::after {\\n  content: var(--tw-content);\\n  --tw-border-opacity: 1;\\n  border-color: rgb(255 255 255 / var(--tw-border-opacity));\\n}\\r\\n  @media (prefers-color-scheme: dark) {\\n\\n  .dark\\\\:border-gray-600 {\\n    --tw-border-opacity: 1;\\n    border-color: rgb(75 85 99 / var(--tw-border-opacity));\\n  }\\n\\n  .dark\\\\:bg-blue-600 {\\n    --tw-bg-opacity: 1;\\n    background-color: rgb(37 99 235 / var(--tw-bg-opacity));\\n  }\\n\\n  .dark\\\\:bg-gray-700 {\\n    --tw-bg-opacity: 1;\\n    background-color: rgb(55 65 81 / var(--tw-bg-opacity));\\n  }\\n\\n  .dark\\\\:text-gray-300 {\\n    --tw-text-opacity: 1;\\n    color: rgb(209 213 219 / var(--tw-text-opacity));\\n  }\\n\\n  .dark\\\\:text-white {\\n    --tw-text-opacity: 1;\\n    color: rgb(255 255 255 / var(--tw-text-opacity));\\n  }\\n\\n  .dark\\\\:hover\\\\:bg-blue-700:hover {\\n    --tw-bg-opacity: 1;\\n    background-color: rgb(29 78 216 / var(--tw-bg-opacity));\\n  }\\n\\n  .dark\\\\:hover\\\\:bg-gray-600:hover {\\n    --tw-bg-opacity: 1;\\n    background-color: rgb(75 85 99 / var(--tw-bg-opacity));\\n  }\\n\\n  .dark\\\\:hover\\\\:text-white:hover {\\n    --tw-text-opacity: 1;\\n    color: rgb(255 255 255 / var(--tw-text-opacity));\\n  }\\n\\n  .dark\\\\:focus\\\\:ring-blue-800:focus {\\n    --tw-ring-opacity: 1;\\n    --tw-ring-color: rgb(30 64 175 / var(--tw-ring-opacity));\\n  }\\n}\\r\\n  @media (max-width: 1600px) {\\n\\n  .max-\\\\[1600px\\\\]\\\\:hidden {\\n    display: none;\\n  }\\n}\\r\\n  @media not all and (min-width: 1024px) {\\n\\n  .max-lg\\\\:gap-10 {\\n    gap: 2.5rem;\\n  }\\n}\\r\\n  @media (min-width: 765px) {\\n\\n  .md\\\\:mb-3 {\\n    margin-bottom: 0.75rem;\\n  }\\n\\n  .md\\\\:mb-6 {\\n    margin-bottom: 1.5rem;\\n  }\\n\\n  .md\\\\:ml-\\\\[15\\\\%\\\\] {\\n    margin-left: 15%;\\n  }\\n\\n  .md\\\\:ml-\\\\[55px\\\\] {\\n    margin-left: 55px;\\n  }\\n\\n  .md\\\\:mr-12 {\\n    margin-right: 3rem;\\n  }\\n\\n  .md\\\\:w-1\\\\/2 {\\n    width: 50%;\\n  }\\n\\n  .md\\\\:w-auto {\\n    width: auto;\\n  }\\n\\n  .md\\\\:w-fit {\\n    width: -moz-fit-content;\\n    width: fit-content;\\n  }\\n\\n  .md\\\\:w-full {\\n    width: 100%;\\n  }\\n\\n  .md\\\\:grid-cols-2 {\\n    grid-template-columns: repeat(2, minmax(0, 1fr));\\n  }\\n\\n  .md\\\\:flex-row {\\n    flex-direction: row;\\n  }\\n\\n  .md\\\\:flex-col {\\n    flex-direction: column;\\n  }\\n\\n  .md\\\\:items-center {\\n    align-items: center;\\n  }\\n\\n  .md\\\\:py-2 {\\n    padding-top: 0.5rem;\\n    padding-bottom: 0.5rem;\\n  }\\n\\n  .md\\\\:text-base {\\n    font-size: 1rem;\\n    line-height: 1.5rem;\\n  }\\n}\\r\\n  @media (min-width: 1024px) {\\n\\n  .lg\\\\:mb-0 {\\n    margin-bottom: 0px;\\n  }\\n\\n  .lg\\\\:mb-6 {\\n    margin-bottom: 1.5rem;\\n  }\\n\\n  .lg\\\\:ml-\\\\[60px\\\\] {\\n    margin-left: 60px;\\n  }\\n\\n  .lg\\\\:mr-14 {\\n    margin-right: 3.5rem;\\n  }\\n\\n  .lg\\\\:mt-0 {\\n    margin-top: 0px;\\n  }\\n\\n  .lg\\\\:flex {\\n    display: flex;\\n  }\\n\\n  .lg\\\\:w-\\\\[95\\\\%\\\\] {\\n    width: 95%;\\n  }\\n\\n  .lg\\\\:w-auto {\\n    width: auto;\\n  }\\n\\n  .lg\\\\:w-full {\\n    width: 100%;\\n  }\\n\\n  .lg\\\\:-translate-x-\\\\[70\\\\%\\\\] {\\n    --tw-translate-x: -70%;\\n    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n  }\\n\\n  .lg\\\\:grid-cols-2 {\\n    grid-template-columns: repeat(2, minmax(0, 1fr));\\n  }\\n\\n  .lg\\\\:grid-cols-3 {\\n    grid-template-columns: repeat(3, minmax(0, 1fr));\\n  }\\n\\n  .lg\\\\:grid-cols-4 {\\n    grid-template-columns: repeat(4, minmax(0, 1fr));\\n  }\\n\\n  .lg\\\\:flex-row {\\n    flex-direction: row;\\n  }\\n\\n  .lg\\\\:items-center {\\n    align-items: center;\\n  }\\n\\n  .lg\\\\:gap-9 {\\n    gap: 2.25rem;\\n  }\\n\\n  .lg\\\\:py-1 {\\n    padding-top: 0.25rem;\\n    padding-bottom: 0.25rem;\\n  }\\n\\n  .lg\\\\:pl-0 {\\n    padding-left: 0px;\\n  }\\n\\n  .lg\\\\:text-left {\\n    text-align: left;\\n  }\\n\\n  .lg\\\\:text-sm {\\n    font-size: 0.875rem;\\n    line-height: 1.25rem;\\n  }\\n}\\r\\n  @media (min-width: 1280px) {\\n\\n  .xl\\\\:mb-6 {\\n    margin-bottom: 1.5rem;\\n  }\\n\\n  .xl\\\\:mt-20 {\\n    margin-top: 5rem;\\n  }\\n\\n  .xl\\\\:mt-4 {\\n    margin-top: 1rem;\\n  }\\n\\n  .xl\\\\:mt-6 {\\n    margin-top: 1.5rem;\\n  }\\n\\n  .xl\\\\:block {\\n    display: block;\\n  }\\n\\n  .xl\\\\:inline {\\n    display: inline;\\n  }\\n\\n  .xl\\\\:hidden {\\n    display: none;\\n  }\\n\\n  .xl\\\\:w-1\\\\/2 {\\n    width: 50%;\\n  }\\n\\n  .xl\\\\:w-3\\\\/5 {\\n    width: 60%;\\n  }\\n\\n  .xl\\\\:w-\\\\[100\\\\%\\\\] {\\n    width: 100%;\\n  }\\n\\n  .xl\\\\:w-\\\\[20\\\\%\\\\] {\\n    width: 20%;\\n  }\\n\\n  .xl\\\\:w-\\\\[40\\\\%\\\\] {\\n    width: 40%;\\n  }\\n\\n  .xl\\\\:w-\\\\[48\\\\%\\\\] {\\n    width: 48%;\\n  }\\n\\n  .xl\\\\:w-\\\\[65\\\\%\\\\] {\\n    width: 65%;\\n  }\\n\\n  .xl\\\\:w-\\\\[70\\\\%\\\\] {\\n    width: 70%;\\n  }\\n\\n  .xl\\\\:flex-row {\\n    flex-direction: row;\\n  }\\n\\n  .xl\\\\:gap-6 {\\n    gap: 1.5rem;\\n  }\\n\\n  .xl\\\\:border-e {\\n    border-inline-end-width: 1px;\\n  }\\n\\n  .xl\\\\:border-e-\\\\[1px\\\\] {\\n    border-inline-end-width: 1px;\\n  }\\n\\n  .xl\\\\:pe-8 {\\n    padding-inline-end: 2rem;\\n  }\\n\\n  .xl\\\\:pl-5 {\\n    padding-left: 1.25rem;\\n  }\\n\\n  .xl\\\\:ps-8 {\\n    padding-inline-start: 2rem;\\n  }\\n\\n  .xl\\\\:text-4xl {\\n    font-size: 2.25rem;\\n    line-height: 2.5rem;\\n  }\\n\\n  .xl\\\\:text-lg {\\n    font-size: 1.125rem;\\n    line-height: 1.75rem;\\n  }\\n\\n  .xl\\\\:tracking-normal {\\n    letter-spacing: 0em;\\n  }\\n}\\r\\n  @media (min-width: 1536px) {\\n\\n  .\\\\32xl\\\\:mb-0 {\\n    margin-bottom: 0px;\\n  }\\n\\n  .\\\\32xl\\\\:mb-0\\\\.5 {\\n    margin-bottom: 0.125rem;\\n  }\\n\\n  .\\\\32xl\\\\:block {\\n    display: block;\\n  }\\n\\n  .\\\\32xl\\\\:h-\\\\[calc\\\\(100\\\\%-60px\\\\)\\\\] {\\n    height: calc(100% - 60px);\\n  }\\n\\n  .\\\\32xl\\\\:h-full {\\n    height: 100%;\\n  }\\n\\n  .\\\\32xl\\\\:w-1\\\\/5 {\\n    width: 20%;\\n  }\\n\\n  .\\\\32xl\\\\:w-\\\\[50\\\\%\\\\] {\\n    width: 50%;\\n  }\\n\\n  .\\\\32xl\\\\:w-\\\\[55\\\\%\\\\] {\\n    width: 55%;\\n  }\\n\\n  .\\\\32xl\\\\:w-\\\\[60\\\\%\\\\] {\\n    width: 60%;\\n  }\\n\\n  .\\\\32xl\\\\:w-\\\\[calc\\\\(100\\\\%-70px\\\\)\\\\] {\\n    width: calc(100% - 70px);\\n  }\\n\\n  .\\\\32xl\\\\:\\\\!max-w-\\\\[70\\\\%\\\\] {\\n    max-width: 70% !important;\\n  }\\n\\n  .\\\\32xl\\\\:flex-row {\\n    flex-direction: row;\\n  }\\n\\n  .\\\\32xl\\\\:gap-3 {\\n    gap: 0.75rem;\\n  }\\n\\n  .\\\\32xl\\\\:p-3 {\\n    padding: 0.75rem;\\n  }\\n\\n  .\\\\32xl\\\\:px-3 {\\n    padding-left: 0.75rem;\\n    padding-right: 0.75rem;\\n  }\\n\\n  .\\\\32xl\\\\:px-3\\\\.5 {\\n    padding-left: 0.875rem;\\n    padding-right: 0.875rem;\\n  }\\n\\n  .\\\\32xl\\\\:px-4 {\\n    padding-left: 1rem;\\n    padding-right: 1rem;\\n  }\\n\\n  .\\\\32xl\\\\:px-6 {\\n    padding-left: 1.5rem;\\n    padding-right: 1.5rem;\\n  }\\n\\n  .\\\\32xl\\\\:py-1 {\\n    padding-top: 0.25rem;\\n    padding-bottom: 0.25rem;\\n  }\\n\\n  .\\\\32xl\\\\:py-2 {\\n    padding-top: 0.5rem;\\n    padding-bottom: 0.5rem;\\n  }\\n\\n  .\\\\32xl\\\\:py-3 {\\n    padding-top: 0.75rem;\\n    padding-bottom: 0.75rem;\\n  }\\n\\n  .\\\\32xl\\\\:pt-1 {\\n    padding-top: 0.25rem;\\n  }\\n\\n  .\\\\32xl\\\\:pt-1\\\\.5 {\\n    padding-top: 0.375rem;\\n  }\\n\\n  .\\\\32xl\\\\:text-2xl {\\n    font-size: 1.5rem;\\n    line-height: 2rem;\\n  }\\n\\n  .\\\\32xl\\\\:text-5xl {\\n    font-size: 3rem;\\n    line-height: 1;\\n  }\\n\\n  .\\\\32xl\\\\:text-lg {\\n    font-size: 1.125rem;\\n    line-height: 1.75rem;\\n  }\\n\\n  .\\\\32xl\\\\:text-xl {\\n    font-size: 1.25rem;\\n    line-height: 1.75rem;\\n  }\\n}\\r\\n  @media (min-width: 1600px) {\\n\\n  .min-\\\\[1600px\\\\]\\\\:w-\\\\[23\\\\%\\\\] {\\n    width: 23%;\\n  }\\n\\n  .min-\\\\[1600px\\\\]\\\\:w-\\\\[42\\\\%\\\\] {\\n    width: 42%;\\n  }\\n}\\r\\n  @media (min-width: 1610px) {\\n\\n  .min-\\\\[1610px\\\\]\\\\:inline {\\n    display: inline;\\n  }\\n\\n  .min-\\\\[1610px\\\\]\\\\:w-\\\\[35\\\\%\\\\] {\\n    width: 35%;\\n  }\\n}\", \"\",{\"version\":3,\"sources\":[\"webpack://styles/globals.css\",\"<no source>\"],\"names\":[],\"mappings\":\"AAAA;;CAAc,CAAd;;;CAAc;;AAAd;;;EAAA,sBAAc,EAAd,MAAc;EAAd,eAAc,EAAd,MAAc;EAAd,mBAAc,EAAd,MAAc;EAAd,qBAAc,EAAd,MAAc;AAAA;;AAAd;;EAAA,gBAAc;AAAA;;AAAd;;;;;;;CAAc;;AAAd;EAAA,gBAAc,EAAd,MAAc;EAAd,8BAAc,EAAd,MAAc;EAAd,gBAAc,EAAd,MAAc;EAAd,cAAc;KAAd,WAAc,EAAd,MAAc;EAAd,4NAAc,EAAd,MAAc;EAAd,6BAAc,EAAd,MAAc;EAAd,+BAAc,EAAd,MAAc;AAAA;;AAAd;;;CAAc;;AAAd;EAAA,SAAc,EAAd,MAAc;EAAd,oBAAc,EAAd,MAAc;AAAA;;AAAd;;;;CAAc;;AAAd;EAAA,SAAc,EAAd,MAAc;EAAd,cAAc,EAAd,MAAc;EAAd,qBAAc,EAAd,MAAc;AAAA;;AAAd;;CAAc;;AAAd;EAAA,yCAAc;UAAd,iCAAc;AAAA;;AAAd;;CAAc;;AAAd;;;;;;EAAA,kBAAc;EAAd,oBAAc;AAAA;;AAAd;;CAAc;;AAAd;EAAA,cAAc;EAAd,wBAAc;AAAA;;AAAd;;CAAc;;AAAd;;EAAA,mBAAc;AAAA;;AAAd;;;CAAc;;AAAd;;;;EAAA,+GAAc,EAAd,MAAc;EAAd,cAAc,EAAd,MAAc;AAAA;;AAAd;;CAAc;;AAAd;EAAA,cAAc;AAAA;;AAAd;;CAAc;;AAAd;;EAAA,cAAc;EAAd,cAAc;EAAd,kBAAc;EAAd,wBAAc;AAAA;;AAAd;EAAA,eAAc;AAAA;;AAAd;EAAA,WAAc;AAAA;;AAAd;;;;CAAc;;AAAd;EAAA,cAAc,EAAd,MAAc;EAAd,qBAAc,EAAd,MAAc;EAAd,yBAAc,EAAd,MAAc;AAAA;;AAAd;;;;CAAc;;AAAd;;;;;EAAA,oBAAc,EAAd,MAAc;EAAd,8BAAc,EAAd,MAAc;EAAd,gCAAc,EAAd,MAAc;EAAd,eAAc,EAAd,MAAc;EAAd,oBAAc,EAAd,MAAc;EAAd,oBAAc,EAAd,MAAc;EAAd,cAAc,EAAd,MAAc;EAAd,SAAc,EAAd,MAAc;EAAd,UAAc,EAAd,MAAc;AAAA;;AAAd;;CAAc;;AAAd;;EAAA,oBAAc;AAAA;;AAAd;;;CAAc;;AAAd;;;;EAAA,0BAAc,EAAd,MAAc;EAAd,6BAAc,EAAd,MAAc;EAAd,sBAAc,EAAd,MAAc;AAAA;;AAAd;;CAAc;;AAAd;EAAA,aAAc;AAAA;;AAAd;;CAAc;;AAAd;EAAA,gBAAc;AAAA;;AAAd;;CAAc;;AAAd;EAAA,wBAAc;AAAA;;AAAd;;CAAc;;AAAd;;EAAA,YAAc;AAAA;;AAAd;;;CAAc;;AAAd;EAAA,6BAAc,EAAd,MAAc;EAAd,oBAAc,EAAd,MAAc;AAAA;;AAAd;;CAAc;;AAAd;EAAA,wBAAc;AAAA;;AAAd;;;CAAc;;AAAd;EAAA,0BAAc,EAAd,MAAc;EAAd,aAAc,EAAd,MAAc;AAAA;;AAAd;;CAAc;;AAAd;EAAA,kBAAc;AAAA;;AAAd;;CAAc;;AAAd;;;;;;;;;;;;;EAAA,SAAc;AAAA;;AAAd;EAAA,SAAc;EAAd,UAAc;AAAA;;AAAd;EAAA,UAAc;AAAA;;AAAd;;;EAAA,gBAAc;EAAd,SAAc;EAAd,UAAc;AAAA;;AAAd;;CAAc;AAAd;EAAA,UAAc;AAAA;;AAAd;;CAAc;;AAAd;EAAA,gBAAc;AAAA;;AAAd;;;CAAc;;AAAd;EAAA,UAAc,EAAd,MAAc;EAAd,cAAc,EAAd,MAAc;AAAA;;AAAd;;EAAA,UAAc,EAAd,MAAc;EAAd,cAAc,EAAd,MAAc;AAAA;;AAAd;;CAAc;;AAAd;;EAAA,eAAc;AAAA;;AAAd;;CAAc;AAAd;EAAA,eAAc;AAAA;;AAAd;;;;CAAc;;AAAd;;;;;;;;EAAA,cAAc,EAAd,MAAc;EAAd,sBAAc,EAAd,MAAc;AAAA;;AAAd;;CAAc;;AAAd;;EAAA,eAAc;EAAd,YAAc;AAAA;;AAAd,wEAAc;AAAd;EAAA,aAAc;AAAA;;AAAd;EAAA,wBAAc;EAAd,wBAAc;EAAd,mBAAc;EAAd,mBAAc;EAAd,cAAc;EAAd,cAAc;EAAd,cAAc;EAAd,eAAc;EAAd,eAAc;EAAd,aAAc;EAAd,aAAc;EAAd,kBAAc;EAAd,sCAAc;EAAd,8BAAc;EAAd,6BAAc;EAAd,4BAAc;EAAd,eAAc;EAAd,oBAAc;EAAd,sBAAc;EAAd,uBAAc;EAAd,wBAAc;EAAd,kBAAc;EAAd,2BAAc;EAAd,4BAAc;EAAd,sCAAc;EAAd,kCAAc;EAAd,2BAAc;EAAd,sBAAc;EAAd,8BAAc;EAAd,YAAc;EAAd,kBAAc;EAAd,gBAAc;EAAd,iBAAc;EAAd,kBAAc;EAAd,cAAc;EAAd,gBAAc;EAAd,aAAc;EAAd,mBAAc;EAAd,qBAAc;EAAd,2BAAc;EAAd,yBAAc;EAAd,0BAAc;EAAd,2BAAc;EAAd,uBAAc;EAAd,wBAAc;EAAd,yBAAc;EAAd;AAAc;;AAAd;EAAA,wBAAc;EAAd,wBAAc;EAAd,mBAAc;EAAd,mBAAc;EAAd,cAAc;EAAd,cAAc;EAAd,cAAc;EAAd,eAAc;EAAd,eAAc;EAAd,aAAc;EAAd,aAAc;EAAd,kBAAc;EAAd,sCAAc;EAAd,8BAAc;EAAd,6BAAc;EAAd,4BAAc;EAAd,eAAc;EAAd,oBAAc;EAAd,sBAAc;EAAd,uBAAc;EAAd,wBAAc;EAAd,kBAAc;EAAd,2BAAc;EAAd,4BAAc;EAAd,sCAAc;EAAd,kCAAc;EAAd,2BAAc;EAAd,sBAAc;EAAd,8BAAc;EAAd,YAAc;EAAd,kBAAc;EAAd,gBAAc;EAAd,iBAAc;EAAd,kBAAc;EAAd,cAAc;EAAd,gBAAc;EAAd,aAAc;EAAd,mBAAc;EAAd,qBAAc;EAAd,2BAAc;EAAd,yBAAc;EAAd,0BAAc;EAAd,2BAAc;EAAd,uBAAc;EAAd,wBAAc;EAAd,yBAAc;EAAd;AAAc;AACd;EAAA;AAAoB;AAApB;;EAAA;IAAA;EAAoB;AAAA;AAApB;;EAAA;IAAA;EAAoB;AAAA;AAApB;;EAAA;IAAA;EAAoB;AAAA;AAApB;;EAAA;IAAA;EAAoB;AAAA;AAApB;;EAAA;IAAA;EAAoB;AAAA;AAApB;;EAAA;IAAA;EAAoB;AAAA;AACpB;EAAA,kBAAmB;EAAnB,UAAmB;EAAnB,WAAmB;EAAnB,UAAmB;EAAnB,YAAmB;EAAnB,gBAAmB;EAAnB,sBAAmB;EAAnB,mBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,8BAAmB;EAAnB;AAAmB;AAAnB;EAAA,mBAAmB;EAAnB;AAAmB;AAAnB;EAAA,iBAAmB;EAAnB;AAAmB;AAAnB;EAAA,iBAAmB;EAAnB;AAAmB;AAAnB;EAAA,mBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,gBAAmB;EAAnB;AAAmB;AAAnB;EAAA,gBAAmB;EAAnB;AAAmB;AAAnB;EAAA,mBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,gBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,sCAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA,wBAAmB;EAAnB;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA,qBAAmB;EAAnB;AAAmB;AAAnB;EAAA,yBAAmB;EAAnB;AAAmB;AAAnB;EAAA,mBAAmB;EAAnB;AAAmB;AAAnB;EAAA,mBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,eAAmB;EAAnB,eAAmB;EAAnB;AAAmB;AAAnB;EAAA,iBAAmB;EAAnB,iBAAmB;EAAnB;AAAmB;AAAnB;EAAA,eAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,wBAAmB;KAAnB,qBAAmB;UAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,uBAAmB;EAAnB,uDAAmB;EAAnB;AAAmB;AAAnB;EAAA,uBAAmB;EAAnB,sDAAmB;EAAnB;AAAmB;AAAnB;EAAA,uBAAmB;EAAnB,8DAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,+BAAmB;EAAnB;AAAmB;AAAnB;EAAA,kCAAmB;EAAnB;AAAmB;AAAnB;EAAA,8BAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,iCAAmB;EAAnB;AAAmB;AAAnB;EAAA,iCAAmB;EAAnB;AAAmB;AAAnB;EAAA,iCAAmB;EAAnB;AAAmB;AAAnB;EAAA,iCAAmB;EAAnB;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA,6BAAmB;EAAnB;AAAmB;AAAnB;EAAA,6BAAmB;EAAnB;AAAmB;AAAnB;EAAA,6BAAmB;EAAnB;AAAmB;AAAnB;EAAA,6BAAmB;EAAnB;AAAmB;AAAnB;EAAA,6BAAmB;EAAnB;AAAmB;AAAnB;EAAA,6BAAmB;EAAnB;AAAmB;AAAnB;EAAA,6BAAmB;EAAnB;AAAmB;AAAnB;EAAA,6BAAmB;EAAnB;AAAmB;AAAnB;EAAA,6BAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,6BAAmB;EAAnB;AAAmB;AAAnB;EAAA,6BAAmB;EAAnB;AAAmB;AAAnB;EAAA,6BAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,gCAAmB;EAAnB;AAAmB;AAAnB;EAAA,gCAAmB;EAAnB;AAAmB;AAAnB;EAAA,6BAAmB;EAAnB;AAAmB;AAAnB;EAAA,+BAAmB;EAAnB;AAAmB;AAAnB;EAAA,+BAAmB;EAAnB;AAAmB;AAAnB;EAAA,iBAAmB;EAAnB;AAAmB;AAAnB;EAAA,qBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA,qBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,qBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,qBAAmB;EAAnB;AAAmB;AAAnB;EAAA,gBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,mBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,iBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,gBAAmB;EAAnB;AAAmB;AAAnB;EAAA,iBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,0BAAmB;EAAnB;AAAmB;AAAnB;EAAA,iBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,eAAmB;EAAnB;AAAmB;AAAnB;EAAA,mBAAmB;EAAnB;AAAmB;AAAnB;EAAA,mBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,+BAAmB;EAAnB;AAAmB;AAAnB;EAAA,+BAAmB;EAAnB;AAAmB;AAAnB;EAAA,+BAAmB;EAAnB;AAAmB;AAAnB;EAAA,+BAAmB;EAAnB;AAAmB;AAAnB;EAAA,+BAAmB;EAAnB;AAAmB;AAAnB;EAAA,+BAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,2BAAmB;EAAnB;AAAmB;AAAnB;EAAA,2BAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,0FAAmB;EAAnB,8GAAmB;EAAnB;AAAmB;AAAnB;EAAA,0EAAmB;EAAnB,8FAAmB;EAAnB;AAAmB;AAAnB;EAAA,+EAAmB;EAAnB,mGAAmB;EAAnB;AAAmB;AAAnB;EAAA,0CAAmB;EAAnB,uDAAmB;EAAnB;AAAmB;AAAnB;EAAA,gFAAmB;EAAnB,oGAAmB;EAAnB;AAAmB;AAAnB;EAAA,6EAAmB;EAAnB,iGAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,2BAAmB;EAAnB,+QAAmB;UAAnB;AAAmB;AAAnB;EAAA,6BAAmB;EAAnB,+QAAmB;UAAnB;AAAmB;AAAnB;EAAA,gKAAmB;EAAnB,wJAAmB;EAAnB,iLAAmB;EAAnB,wDAAmB;EAAnB;AAAmB;AAAnB;EAAA,wBAAmB;EAAnB,wDAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AACnB,iCAAiC;;AAEjC;EACE,yBAAyB;EACzB,4CAA6C;AAC/C;AACA;EACE,+BAA+B;EAC/B,4CAAmD;AACrD;AACA;EACE,wBAAwB;EACxB,4CAA4C;AAC9C;AACA;EACE,8BAA8B;EAC9B,4CAAkD;AACpD;AACA;EACE,6BAA6B;EAC7B,4CAAiD;AACnD;AACA;EACE,mCAAmC;EACnC,4CAAuD;AACzD;AACA;EACE,8BAA8B;EAC9B,4CAAkD;AACpD;AACA;EACE,oCAAoC;EACpC,4CAAwD;AAC1D;AACA;EACE,0BAA0B;EAC1B,4CAA8C;AAChD;AACA;EACE,yBAAyB;EACzB,4CAA6C;AAC/C;AACA;EACE,+BAA+B;EAC/B,6CAAmD;AACrD;AACA;EACE,0BAA0B;EAC1B,6CAA8C;AAChD;AACA;EACE,gCAAgC;EAChC,6CAAoD;AACtD;AACA;EACE,2BAA2B;EAC3B,6CAA+C;AACjD;AACA;EACE,4BAA4B;EAC5B,6CAAgD;AAClD;AACA;EACE,kCAAkC;EAClC,6CAAsD;AACxD;AACA;EACE,0BAA0B;EAC1B,4CAA8C;AAChD;AACA;EACE,0BAA0B;EAC1B,4CAA8C;AAChD;;AAEA;;EAEE,YAAY;EACZ,yBAAyB;EACzB,6BAA6B;EAC7B,kBAAkB;EAClB,yBAAyB;EACzB,eAAe;AACjB;;AAEA;;EAEE,YAAY;EACZ,yBAAyB;EACzB,wCAAwC;EACxC,kBAAkB;EAClB,yBAAyB;EACzB,eAAe;AACjB;;AAEA;EACE,6BAA6B;AAC/B;;AAEA;EACE,eAAe;EACf,cAAc;EACd,4BAA4B;AAC9B;;AAEA;EACE,YAAY;EACZ,mBAAmB;AACrB;;AAEA;EACE,eAAe;EACf,WAAW;EACX,4BAA4B;AAC9B;;AAEA;EACE,cAAc;EACd,6BAA6B;EAC7B,eAAe;AACjB;;AAEA;EACE,YAAY;EACZ,6BAA6B;EAC7B,eAAe;EACf,yBAAyB;AAC3B;;AAEA;EACE,6BAA6B;EAC7B,eAAe;AACjB;AACA;EACE,6BAA6B;EAC7B,eAAe;EACf,YAAY;AACd;;AAEA;EACE,6BAA6B;EAC7B,eAAe;AACjB;AACA;EACE,6BAA6B;EAC7B,eAAe;EACf,YAAY;AACd;;AAEA;EACE,6BAA6B;EAC7B,eAAe;AACjB;AACA;EACE,6BAA6B;EAC7B,eAAe;EACf,YAAY;AACd;;AAEA;EACE,wBAAwB;AAC1B;AACA;EACE,wBAAwB;EACxB,YAAY;EACZ,yBAAyB;AAC3B;;AAEA;EACE,yBAAyB;AAC3B;;AAEA;EACE,uBAAuB;AACzB;;AAEA;EACE,yBAAyB;EACzB,6BAA6B;AAC/B;;AAEA;;;;EAIE,yBAAyB;EACzB,6BAA6B;AAC/B;;AAEA;EACE,yBAAyB;EACzB,6BAA6B;AAC/B;;AAEA;EACE,YAAY;EACZ,6BAA6B;AAC/B;AACA;;EAEE,6BAA6B;EAC7B,YAAY;AACd;AACA;;EAEE,6BAA6B;EAC7B,WAAW;AACb;AACA;EACE,6BAA6B;EAC7B,YAAY;AACd;;AAEA;EACE,aAAa;AACf;;;AAGA;EACE,6BAA6B;EAC7B,eAAe;AACjB;;AAEA;;EAEE,WAAW;EACX,6BAA6B;AAC/B;;AAEA;EACE,UAAU;AACZ;;AAEA;EACE,mBAAmB;EACnB,eAAe;EACf,kBAAkB;AACpB;;AAEA;;;EAGE,WAAW;EACX,yBAAyB;EACzB,8BAA8B;EAC9B,iBAAiB;EACjB,oBAAoB;EACpB,aAAa;EACb,sBAAsB;EACtB,uBAAuB;AACzB;;AAEA;;EAEE,WAAW;EACX,2BAA2B;EAC3B,iBAAiB;EACjB,oBAAoB;AACtB;;AAEA;EACE,cAAc;EACd,iBAAiB;EACjB,oBAAoB;EACpB,aAAa;EACb,sBAAsB;EACtB,uBAAuB;EACvB,qBAAqB;AACvB;;AAEA;EACE,gBAAgB;AAClB;;AAEA;EACE,cAAc;EACd,eAAe;EACf,0BAA0B;EAC1B,iBAAiB;EACjB,wBAAwB;EACxB,SAAS;EACT,UAAU;EACV,gBAAgB;AAClB;;AAEA;EACE,eAAe;EACf,2BAA2B;AAC7B;;AAEA,kEAAkE;;AAElE;EACE,UAAU;AACZ;;AAEA;EACE,eAAe;EACf,sBAAsB;EACtB,WAAW;EACX,WAAW;EACX,+BAA+B;EAC/B,kBAAkB;EAClB,aAAa;EACb,uBAAuB;EACvB,mBAAmB;AACrB;;AAEA;EACE,WAAW;EACX,eAAe;EACf,kBAAkB;EAClB,WAAW;EACX,MAAM;EACN,kBAAkB;AACpB;;AAEA;EACE,eAAe;EACf,WAAW;EACX,gBAAgB;EAChB,aAAa;EACb,gBAAgB;AAClB;;AAEA;;EAEE,6BAA6B;AAC/B;;AAEA;EACE,uBAAuB;EACvB,aAAa;EACb,gBAAgB;EAChB,oCAAoC;AACtC;;AAEA;;;;;;;;GAQG;AACH;;GAEG;AACH;EACE,cAAc;AAChB;AACA;;;GAGG;AACH;EACE,uBAAuB;AACzB;AACA;EACE,sBAAsB;EACtB,YAAY;AACd;;AAEA;EACE,YAAY;AACd;;AAEA;EACE,6BAA6B;KAC1B,0BAA0B;UACrB,qBAAqB;AAC/B;AACA;;EAEE,wBAAwB;AAC1B;;AAEA;;;;;;EAME,gBAAgB;EAChB,mBAAmB;EACnB,qBAAqB;EACrB,0BAA0B;EAC1B,cAAc;EACd,6BAA6B;AAC/B;;AAEA;EACE,sBAAsB;AACxB;;AAEA;EACE,0BAA0B;EAC1B,4BAA4B;AAC9B;;AAEA;EACE,6BAA6B;EAC7B,eAAe;EACf,cAAc;AAChB;AACA;EACE,6BAA6B;EAC7B,eAAe;EACf,YAAY;AACd;;AAEA;EACE,6BAA6B;EAC7B,eAAe;EACf,cAAc;EACd,yBAAyB;AAC3B;;AAEA;EACE,6BAA6B;EAC7B,eAAe;EACf,cAAc;EACd,yBAAyB;EACzB,sBAAsB;AACxB;;AAEA;EACE,yBAAyB;AAC3B;AACA;EACE,yBAAyB;AAC3B;;AAEA;EACE,yBAAyB;EACzB,YAAY;AACd;;AAEA;EACE,qBAAqB;AACvB;AACA;EACE,qBAAqB;AACvB;AACA;EACE,cAAc;AAChB;AACA;EACE,cAAc;AAChB;;AAEA;EACE,yBAAyB;AAC3B;AACA;EACE,yBAAyB;AAC3B;AACA;EACE,cAAc;AAChB;AACA;EACE,cAAc;AAChB;AACA;EACE,cAAc;AAChB;AACA;EACE,cAAc;AAChB;;AAEA;EACE,eAAe;EACf,6BAA6B;AAC/B;;AAEA;EACE,6BAA6B;EAC7B,eAAe;EACf,YAAY;AACd;;AAEA;IACI,eAAe;IACf,cAAc;IACd,4BAA4B;AAChC;;AAEA;EACE,8BAA8B;EAC9B,cAAc;EACd,eAAe;AACjB;;AAEA;EACE,8BAA8B;EAC9B,cAAc;EACd,eAAe;AACjB;;AAEA;EACE,aAAa;EACb,eAAe;EACf,6BAA6B;AAC/B;;AAEA;EACE,uBAAuB;AACzB;;AAEA;EACE,oCAAoC;AACtC;;AAEA;EACE,uBAAuB;EACvB,8BAA8B;AAChC;;AAEA;EACE,YAAY;EACZ,kBAAkB;EAClB,uBAAuB;AACzB;;AAEA;EACE,+BAA+B;AACjC;;AAEA;EACE,kBAAkB;EAClB,YAAY;EACZ,UAAU;AACZ;;AAEA;EACE,yBAAyB;EACzB,kBAAkB;AACpB;;AAEA;EACE,6BAA6B;AAC/B;;AAEA;EACE,6BAA6B;AAC/B;;AAEA;IACI,4BAA4B;AAChC;;AAEA;IACI,4BAA4B;AAChC;;AAEA;IACI,4BAA4B;AAChC;;AAEA;EACE,sBAAsB;AACxB;;AAEA;EACE,mBAAmB;EACnB,kBAAkB;AACpB;;AAEA;EACE,aAAa;EACb,mBAAmB;AACrB;;AAEA;EACE,aAAa;EACb,mBAAmB;AACrB;;AAEA;;;;GAIG;;AAEH;IACI,mBAAmB;IACnB,aAAa;AACjB;;AAEA;EACE;IACE,kBAAkB;IAClB,wBAAwB;IACxB,iBAAiB;IACjB,YAAY;EACd;EACA;IACE,kBAAkB;EACpB;EACA;;IAEE,gBAAgB;EAClB;EACA;;IAEE,kBAAkB;IAClB,WAAW;IACX,gBAAgB;EAClB;EACA;;KAEG;;EAEH;IACE,eAAe;EACjB;EACA;IACE,gBAAgB;IAChB,WAAW;IACX,WAAW;IACX,aAAa;IACb,6BAA6B;IAC7B,SAAS;IAET,gBAAgB;EAClB;EACA;;IAEE,oBAAoB;EACtB;EACA;IACE,WAAW;EACb;EACA;IACE,WAAW;IACX,YAAY;IACZ,kBAAkB;IAClB,eAAe;IACf,QAAQ;IACR,aAAa;IACb,uBAAuB;EACzB;EACA;IACE,WAAW;EACb;;EAEA;IACE,UAAU;EACZ;AACF;;;AAGA;;AAEA;;AAEA;;;;;;;;;;GAUG;;AAEH;EACE,eAAe;EACf,MAAM;EACN,OAAO;EACP,WAAW;EACX,YAAY;EACZ,oCAAoC,EAAE,2BAA2B;EACjE,aAAa;EACb,uBAAuB;EACvB,mBAAmB;EACnB,aAAa,EAAE,wCAAwC;AACzD;AACA;EACE,eAAe;EACf,MAAM;EACN,OAAO;EACP,WAAW;EACX,YAAY;EACZ,wCAAwC,EAAE,2BAA2B;EACrE,aAAa;EACb,uBAAuB;EACvB,mBAAmB;EACnB,aAAa,EAAE,wCAAwC;AACzD;;AAEA;EACE,kBAAkB;EAClB,YAAY;AACd;;AAEA;EACE,eAAe;EACf,mBAAmB;AACrB;;AAEA;EACE,eAAe;AACjB;AACA,uBAAuB;;CAEtB;EACC,yBAAyB;EACzB,wBAAwB;EACxB,UAAU;EACV,gBAAgB;EAChB,6BAA6B;AAC/B;AACA,uBAAuB;CACtB;EACC,yBAAyB;EACzB,wBAAwB;EACxB,UAAU;EACV,gBAAgB;EAChB,6BAA6B;AAC/B;CACC;EACC,yBAAyB;EACzB,UAAU;EACV,gBAAgB;EAChB,6BAA6B;AAC/B;AACA;EACE,yBAAyB;EACzB,8BAA8B;EAC9B,UAAU;EACV,gBAAgB;EAChB,6BAA6B;AAC/B;AACA;EACE,yBAAyB;EACzB,8BAA8B;EAC9B,UAAU;EACV,gBAAgB;EAChB,6BAA6B;AAC/B;AACA;EACE,yBAAyB;EACzB,8BAA8B;EAC9B,UAAU;EACV,gBAAgB;EAChB,6BAA6B;AAC/B;;AAEA;;GAEG,yBAAyB;GACzB,iBAAiB;GACjB,4BAA4B;AAC/B;AACA;;EAEE,wBAAwB;EACxB,SAAS;AACX;;;AAGA;EACE,YAAY;EACZ,kBAAkB;EAClB,wBAAwB,EAAE,eAAe;EACzC,YAAY;EACZ,UAAU;EACV,eAAe;EACf,sBAAsB;EAEtB,6BAA6B;AAC/B;AACA;;GAEG;AACH;EACE,uBAAuB;AACzB;AACA;;GAEG;AACH;EACE,WAAW;EACX,YAAY;AACd;AACA;EACE,YAAY;EACZ,sBAAsB;EACtB,gBAAgB;EAChB,SAAS;EACT,UAAU;EACV,uBAAuB;EACvB,eAAe;EACf,6BAA6B;AAC/B;AACA;EACE,eAAe;EACf,KAAK;EACL,MAAM;EACN,kBAAkB;EAClB,UAAU;AACZ;;AAEA;EACE,cAAc;EACd,mBAAmB;EACnB,SAAS;EACT,iBAAiB;AACnB;AACA;EACE,kBAAkB;EAClB,QAAQ;AACV;AACA;IACI,YAAY;AAChB;AACA;EACE,4BAA4B;EAC5B,6BAA6B;EAC7B,iCAAiC;AACnC;AACA;EACE,qBAAqB;AACvB;;AAEA;EACE,oCAAoC;EACpC,qBAAqB;AACvB;AACA;EACE,oCAAoC;EACpC,UAAU;EACV,gCAAgC;EAChC,+BAA+B;AACjC;AACA;EACE,oCAAoC;EACpC,UAAU;EACV,gCAAgC;EAChC,+BAA+B;EAC/B,8BAA8B;AAChC;AACA;EACE,oCAAoC;EACpC,UAAU;EACV,gCAAgC;EAChC,8BAA8B;AAChC;AACA;IACI,+BAA+B;IAC/B,gCAAgC;IAChC,mBAAmB;AACvB;;AAEA;EACE,yBAAyB;EACzB,UAAU;EACV,UAAU;EACV,eAAe;EACf,uBAAuB;AACzB;AACA;EACE,sBAAsB;EACtB,UAAU;EACV,gBAAgB;EAChB,UAAU;EACV,uBAAuB;EACvB,0BAA0B;AAC5B;AACA;EACE,yBAAyB;AAC3B;;AAEA;EACE,aAAa;GACZ,wBAAwB;AAC3B;;AAEA;EACE,cAAc;GACb,wBAAwB;AAC3B;;AAEA;EACE,yBAAyB;AAC3B;AACA;EACE,wBAAwB;EACxB;AACF;AACA;EACE,cAAc;EACd,gCAAgC;AAClC;AACA;EACE,4BAA4B;;AAE9B;;AAEA,kBAAkB;;AAElB;GACG,yBAAyB;GACzB,iBAAiB;AACpB;;AAEA;EACE,YAAY;EACZ,gBAAgB;EAChB,wBAAwB,EAAE,eAAe;EACzC,YAAY;EACZ,UAAU;EACV,eAAe;EACf,sBAAsB;EACtB,mBAAmB;EACnB,kBAAkB;EAClB,6BAA6B;AAC/B;;AAEA;;GAEG;AACH;EACE,kBAAkB;EAClB,eAAe;EACf,OAAO;AACT;AACA;EACE,eAAe;EACf,eAAe;EACf,OAAO;AACT;;AAEA;EACE,WAAW;EACX,YAAY;EACZ,sBAAsB;EACtB,WAAW;EACX,UAAU;EACV,kBAAkB;EAClB,6BAA6B;AAC/B;AACA;EACE,YAAY;EACZ,sBAAsB;EACtB,sBAAsB;EACtB,gBAAgB;EAChB,SAAS;EACT,UAAU;EACV,uBAAuB;EACvB,eAAe;EACf,6BAA6B;AAC/B;AACA;EACE,eAAe;EACf,KAAK;EACL,MAAM;EACN,wBAAwB;EACxB,UAAU;AACZ;;AAEA;EACE,eAAe;AACjB;;AAEA;EACE,YAAY;AACd;;AAEA;EACE,6BAA6B;AAC/B;AACA;EACE,yBAAyB;EACzB,YAAY;EACZ,gBAAgB;EAChB,4BAA4B;AAC9B;AALA;EACE,yBAAyB;EACzB,YAAY;EACZ,gBAAgB;EAChB,4BAA4B;AAC9B;AACA;EACE,oBAAoB;EACpB,4BAA4B;EAC5B,qBAAqB;EACrB,gBAAgB;EAChB,uBAAuB;EACvB;;EAEA;IACE,yBAAyB;IACzB,cAAc;IACd,wBAAwB;IACxB,gBAAgB;EAClB;EACA;IACE,aAAa;EACf;EAz+BF;EAAA,2BCAA;EDAA;CCAA;EDAA;EAAA,2BCAA;EDAA;CCAA;EDAA;EAAA,2BCAA;EDAA;CCAA;EDAA;EAAA,2BCAA;EDAA;CCAA;EDAA;EAAA,2BCAA;EDAA;CCAA;EDAA;EAAA,2BCAA;EDAA,sBCAA;EDAA;CCAA;EDAA;EAAA,2BCAA;EDAA;CCAA;EDAA;EAAA,2BCAA;EDAA;CCAA;EDAA;EAAA,2BCAA;EDAA,uBCAA;EDAA;CCAA;EDAA;EAAA,2BCAA;EDAA,mBCAA;EDAA;CCAA;EDAA;EAAA,2BCAA;EDAA,yBCAA;EDAA,yDCAA;EDAA;CCAA;EDAA;EAAA,iBCAA;EDAA;CCAA;EDAA;EAAA,qBCAA;EDAA;CCAA;EDAA;EAAA,+BCAA;EDAA;CCAA;EDAA;EAAA;CCAA;EDAA;EAAA;CCAA;EDAA;EAAA,uBCAA;EDAA;CCAA;EDAA;EAAA,mBCAA;EDAA;CCAA;EDAA;EAAA;CCAA;EDAA;EAAA,mBCAA;EDAA;CCAA;EDAA;EAAA,mBCAA;EDAA;CCAA;EDAA;EAAA,mBCAA;EDAA;CCAA;EDAA;EAAA,mBCAA;EDAA;CCAA;EDAA;EAAA,mBCAA;EDAA;CCAA;EDAA;EAAA,mBCAA;EDAA;CCAA;EDAA;EAAA,qBCAA;EDAA;CCAA;EDAA;EAAA,qBCAA;EDAA;CCAA;EDAA;EAAA;CCAA;EDAA;EAAA,+BCAA;EDAA;CCAA;EDAA;EAAA;CCAA;EDAA;EAAA;CCAA;EDAA;EAAA,4GCAA;EDAA,0GCAA;EDAA;CCAA;EDAA;EAAA,4GCAA;EDAA,0GCAA;EDAA;CCAA;EDAA;EAAA,qBCAA;EDAA;CCAA;EDAA;EAAA,qBCAA;EDAA;CCAA;EDAA;EAAA,mBCAA;EDAA;CCAA;EDAA;EAAA;CCAA;EDAA;EAAA,qBCAA;EDAA;CCAA;EDAA;EAAA,mBCAA;EDAA;CCAA;EDAA;EAAA,mBCAA;EDAA;CCAA;EDAA;EAAA,2BCAA;EDAA;CCAA;EDAA;EAAA,2BCAA;EDAA,uBCAA;EDAA;CCAA;EDAA;EAAA,2BCAA;EDAA,uBCAA;EDAA;CCAA;EDAA;;EAAA;IAAA,uBCAA;IDAA;GCAA;;EDAA;IAAA,mBCAA;IDAA;GCAA;;EDAA;IAAA,mBCAA;IDAA;GCAA;;EDAA;IAAA,qBCAA;IDAA;GCAA;;EDAA;IAAA,qBCAA;IDAA;GCAA;;EDAA;IAAA,mBCAA;IDAA;GCAA;;EDAA;IAAA,mBCAA;IDAA;GCAA;;EDAA;IAAA,qBCAA;IDAA;GCAA;;EDAA;IAAA,qBCAA;IDAA;GCAA;CAAA;EDAA;;EAAA;IAAA;GCAA;CAAA;EDAA;;EAAA;IAAA;GCAA;CAAA;EDAA;;EAAA;IAAA;GCAA;;EDAA;IAAA;GCAA;;EDAA;IAAA;GCAA;;EDAA;IAAA;GCAA;;EDAA;IAAA;GCAA;;EDAA;IAAA;GCAA;;EDAA;IAAA;GCAA;;EDAA;IAAA,wBCAA;IDAA;GCAA;;EDAA;IAAA;GCAA;;EDAA;IAAA;GCAA;;EDAA;IAAA;GCAA;;EDAA;IAAA;GCAA;;EDAA;IAAA;GCAA;;EDAA;IAAA,oBCAA;IDAA;GCAA;;EDAA;IAAA,gBCAA;IDAA;GCAA;CAAA;EDAA;;EAAA;IAAA;GCAA;;EDAA;IAAA;GCAA;;EDAA;IAAA;GCAA;;EDAA;IAAA;GCAA;;EDAA;IAAA;GCAA;;EDAA;IAAA;GCAA;;EDAA;IAAA;GCAA;;EDAA;IAAA;GCAA;;EDAA;IAAA;GCAA;;EDAA;IAAA,uBCAA;IDAA;GCAA;;EDAA;IAAA;GCAA;;EDAA;IAAA;GCAA;;EDAA;IAAA;GCAA;;EDAA;IAAA;GCAA;;EDAA;IAAA;GCAA;;EDAA;IAAA;GCAA;;EDAA;IAAA,qBCAA;IDAA;GCAA;;EDAA;IAAA;GCAA;;EDAA;IAAA;GCAA;;EDAA;IAAA,oBCAA;IDAA;GCAA;CAAA;EDAA;;EAAA;IAAA;GCAA;;EDAA;IAAA;GCAA;;EDAA;IAAA;GCAA;;EDAA;IAAA;GCAA;;EDAA;IAAA;GCAA;;EDAA;IAAA;GCAA;;EDAA;IAAA;GCAA;;EDAA;IAAA;GCAA;;EDAA;IAAA;GCAA;;EDAA;IAAA;GCAA;;EDAA;IAAA;GCAA;;EDAA;IAAA;GCAA;;EDAA;IAAA;GCAA;;EDAA;IAAA;GCAA;;EDAA;IAAA;GCAA;;EDAA;IAAA;GCAA;;EDAA;IAAA;GCAA;;EDAA;IAAA;GCAA;;EDAA;IAAA;GCAA;;EDAA;IAAA;GCAA;;EDAA;IAAA;GCAA;;EDAA;IAAA;GCAA;;EDAA;IAAA,mBCAA;IDAA;GCAA;;EDAA;IAAA,oBCAA;IDAA;GCAA;;EDAA;IAAA;GCAA;CAAA;EDAA;;EAAA;IAAA;GCAA;;EDAA;IAAA;GCAA;;EDAA;IAAA;GCAA;;EDAA;IAAA;GCAA;;EDAA;IAAA;GCAA;;EDAA;IAAA;GCAA;;EDAA;IAAA;GCAA;;EDAA;IAAA;GCAA;;EDAA;IAAA;GCAA;;EDAA;IAAA;GCAA;;EDAA;IAAA;GCAA;;EDAA;IAAA;GCAA;;EDAA;IAAA;GCAA;;EDAA;IAAA;GCAA;;EDAA;IAAA,sBCAA;IDAA;GCAA;;EDAA;IAAA,uBCAA;IDAA;GCAA;;EDAA;IAAA,mBCAA;IDAA;GCAA;;EDAA;IAAA,qBCAA;IDAA;GCAA;;EDAA;IAAA,qBCAA;IDAA;GCAA;;EDAA;IAAA,oBCAA;IDAA;GCAA;;EDAA;IAAA,qBCAA;IDAA;GCAA;;EDAA;IAAA;GCAA;;EDAA;IAAA;GCAA;;EDAA;IAAA,kBCAA;IDAA;GCAA;;EDAA;IAAA,gBCAA;IDAA;GCAA;;EDAA;IAAA,oBCAA;IDAA;GCAA;;EDAA;IAAA,mBCAA;IDAA;GCAA;CAAA;EDAA;;EAAA;IAAA;GCAA;;EDAA;IAAA;GCAA;CAAA;EDAA;;EAAA;IAAA;GCAA;;EDAA;IAAA;GCAA;CAAA\",\"sourcesContent\":[\"@tailwind base;\\r\\n@tailwind components;\\r\\n@tailwind utilities;\\r\\n/*@import \\\"bootstrap/bootstrap\\\";*/\\r\\n\\r\\n@font-face {\\r\\n  font-family: poppinsblack;\\r\\n  src: url(\\\"../assets/fonts/Poppins-Black.ttf\\\");\\r\\n}\\r\\n@font-face {\\r\\n  font-family: poppinsblackitalic;\\r\\n  src: url(\\\"../assets/fonts/Poppins-BlackItalic.ttf\\\");\\r\\n}\\r\\n@font-face {\\r\\n  font-family: poppinsbold;\\r\\n  src: url(\\\"../assets/fonts/Poppins-Bold.ttf\\\");\\r\\n}\\r\\n@font-face {\\r\\n  font-family: poppinsbolditalic;\\r\\n  src: url(\\\"../assets/fonts/Poppins-BoldItalic.ttf\\\");\\r\\n}\\r\\n@font-face {\\r\\n  font-family: poppinsextrabold;\\r\\n  src: url(\\\"../assets/fonts/Poppins-ExtraBold.ttf\\\");\\r\\n}\\r\\n@font-face {\\r\\n  font-family: poppinsextrabolditalic;\\r\\n  src: url(\\\"../assets/fonts/Poppins-ExtraBoldItalic.ttf\\\");\\r\\n}\\r\\n@font-face {\\r\\n  font-family: poppinsextralight;\\r\\n  src: url(\\\"../assets/fonts/Poppins-ExtraLight.ttf\\\");\\r\\n}\\r\\n@font-face {\\r\\n  font-family: poppinsextralightitalic;\\r\\n  src: url(\\\"../assets/fonts/Poppins-ExtraLightItalic.ttf\\\");\\r\\n}\\r\\n@font-face {\\r\\n  font-family: poppinsitalic;\\r\\n  src: url(\\\"../assets/fonts/Poppins-Italic.ttf\\\");\\r\\n}\\r\\n@font-face {\\r\\n  font-family: poppinslight;\\r\\n  src: url(\\\"../assets/fonts/Poppins-Light.ttf\\\");\\r\\n}\\r\\n@font-face {\\r\\n  font-family: poppinslightitalic;\\r\\n  src: url(\\\"../assets/fonts/Poppins-LightItalic.ttf\\\");\\r\\n}\\r\\n@font-face {\\r\\n  font-family: poppinsmedium;\\r\\n  src: url(\\\"../assets/fonts/Poppins-Medium.ttf\\\");\\r\\n}\\r\\n@font-face {\\r\\n  font-family: poppinsmediumitalic;\\r\\n  src: url(\\\"../assets/fonts/Poppins-MediumItalic.ttf\\\");\\r\\n}\\r\\n@font-face {\\r\\n  font-family: poppinsregular;\\r\\n  src: url(\\\"../assets/fonts/Poppins-Regular.ttf\\\");\\r\\n}\\r\\n@font-face {\\r\\n  font-family: poppinssemibold;\\r\\n  src: url(\\\"../assets/fonts/Poppins-SemiBold.ttf\\\");\\r\\n}\\r\\n@font-face {\\r\\n  font-family: poppinssemibolditalic;\\r\\n  src: url(\\\"../assets/fonts/Poppins-SemiBoldItalic.ttf\\\");\\r\\n}\\r\\n@font-face {\\r\\n  font-family: poppinsitalic;\\r\\n  src: url(\\\"../assets/fonts/Poppins-Italic.ttf\\\");\\r\\n}\\r\\n@font-face {\\r\\n  font-family: poppinsitalic;\\r\\n  src: url(\\\"../assets/fonts/Poppins-Italic.ttf\\\");\\r\\n}\\r\\n\\r\\nbody,\\r\\nhtml {\\r\\n  height: 100%;\\r\\n  background-repeat: repeat;\\r\\n  font-family: \\\"poppinsregular\\\";\\r\\n  overflow-x: hidden;\\r\\n  background-color: #f3f8ff;\\r\\n  font-size: 12px;\\r\\n}\\r\\n\\r\\n.dark body,\\r\\nhtml {\\r\\n  height: 100%;\\r\\n  background-repeat: repeat;\\r\\n  font-family: \\\"poppinsregular\\\" !important;\\r\\n  overflow-x: hidden;\\r\\n  background-color: #0e0e10;\\r\\n  font-size: 14px;\\r\\n}\\r\\n\\r\\n.ag-header .ag-header-cell.header-with-border  {\\r\\n  border-bottom: 1px solid #ccc;\\r\\n}\\r\\n\\r\\n.ag-header-cell-text {\\r\\n  font-size: 12px;\\r\\n  color: #333333;\\r\\n  font-family: \\\"poppinsmedium\\\";\\r\\n}\\r\\n\\r\\nbutton[disabled] {\\r\\n  opacity: 0.5;\\r\\n  cursor: not-allowed;\\r\\n}\\r\\n\\r\\n.dark .ag-header-cell-text {\\r\\n  font-size: 12px;\\r\\n  color: #fff;\\r\\n  font-family: \\\"poppinsmedium\\\";\\r\\n}\\r\\n\\r\\n.ag-ltr .ag-cell {\\r\\n  color: #333333;\\r\\n  font-family: \\\"poppinsregular\\\";\\r\\n  font-size: 12px;\\r\\n}\\r\\n\\r\\n.dark .ag-ltr .ag-cell {\\r\\n  color: white;\\r\\n  font-family: \\\"poppinsregular\\\";\\r\\n  font-size: 12px;\\r\\n  background-color: #1d212d;\\r\\n}\\r\\n\\r\\n.ag-paging-row-summary-panel-number {\\r\\n  font-family: \\\"poppinsregular\\\";\\r\\n  font-size: 12px;\\r\\n}\\r\\n.dark .ag-paging-row-summary-panel-number {\\r\\n  font-family: \\\"poppinsregular\\\";\\r\\n  font-size: 12px;\\r\\n  color: white;\\r\\n}\\r\\n\\r\\n.ag-paging-page-summary-panel {\\r\\n  font-family: \\\"poppinsregular\\\";\\r\\n  font-size: 12px;\\r\\n}\\r\\n.dark .ag-paging-page-summary-panel {\\r\\n  font-family: \\\"poppinsregular\\\";\\r\\n  font-size: 12px;\\r\\n  color: white;\\r\\n}\\r\\n\\r\\n.pagination {\\r\\n  font-family: \\\"poppinsregular\\\";\\r\\n  font-size: 12px;\\r\\n}\\r\\n.dark .pagination {\\r\\n  font-family: \\\"poppinsregular\\\";\\r\\n  font-size: 12px;\\r\\n  color: white;\\r\\n}\\r\\n\\r\\n.ag-paging-panel {\\r\\n  justify-content: between;\\r\\n}\\r\\n.dark .ag-paging-panel {\\r\\n  justify-content: between;\\r\\n  color: white;\\r\\n  background-color: #1d212d;\\r\\n}\\r\\n\\r\\n.dark .ag-center-cols-viewport {\\r\\n  background-color: #1d212d;\\r\\n}\\r\\n\\r\\n.contentsectionbg {\\r\\n  background-color: white;\\r\\n}\\r\\n\\r\\n.dark .contentsectionbg {\\r\\n  background-color: #1d212d;\\r\\n  font-family: \\\"poppinsregular\\\";\\r\\n}\\r\\n\\r\\n.dark\\r\\n  .ag-header.ag-header-allow-overflow\\r\\n  .ag-header-row\\r\\n  .ag-root-wrapper.ag-layout-normal {\\r\\n  background-color: #1d212d;\\r\\n  font-family: \\\"poppinsregular\\\";\\r\\n}\\r\\n\\r\\n.dark .ag-header-container {\\r\\n  background-color: #1d212d;\\r\\n  font-family: \\\"poppinsregular\\\";\\r\\n}\\r\\n\\r\\n.dark .pagination-style {\\r\\n  color: white;\\r\\n  font-family: \\\"poppinsregular\\\";\\r\\n}\\r\\n.dark .ag-paging-button,\\r\\n.ag-paging-description {\\r\\n  font-family: \\\"poppinsregular\\\";\\r\\n  color: white;\\r\\n}\\r\\n.ag-paging-button,\\r\\n.ag-paging-description {\\r\\n  font-family: \\\"poppinsregular\\\";\\r\\n  color: #000;\\r\\n}\\r\\n.dark .ag-paging-button.ag-disabled {\\r\\n  font-family: \\\"poppinsregular\\\";\\r\\n  color: white;\\r\\n}\\r\\n\\r\\n.ag-body-horizontal-scroll-viewport {\\r\\n  display: none;\\r\\n} \\r\\n\\r\\n\\r\\n.ag-overlay-no-rows-center {\\r\\n  font-family: \\\"poppinsregular\\\";\\r\\n  font-size: 12px;\\r\\n}\\r\\n\\r\\nnav.sidebar .navbar-nav .open .dropdown-menu > li > a:hover,\\r\\nnav.sidebar .navbar-nav .open .dropdown-menu > li > a:focus {\\r\\n  color: #ccc;\\r\\n  background-color: transparent;\\r\\n}\\r\\n\\r\\nnav:hover .forAnimate {\\r\\n  opacity: 1;\\r\\n}\\r\\n\\r\\n.mainmenu {\\r\\n  background: #002d73;\\r\\n  border: #002d73;\\r\\n  border-radius: 0px;\\r\\n}\\r\\n\\r\\n.mainmenu .navbar-nav > .active > a,\\r\\n.mainmenu .navbar-nav > .active > a:hover,\\r\\n.mainmenu .navbar-nav > .active > a:focus {\\r\\n  color: #fff;\\r\\n  background-color: #2a3344;\\r\\n  border-left: 2px solid #1ca9c0;\\r\\n  padding-top: 15px;\\r\\n  padding-bottom: 15px;\\r\\n  display: flex;\\r\\n  flex-direction: column;\\r\\n  justify-content: center;\\r\\n}\\r\\n\\r\\n.mainmenu .navbar-nav > li > a:hover,\\r\\n.mainmenu .navbar-nav > li > a:focus {\\r\\n  color: #fff;\\r\\n  background-color: lightgray;\\r\\n  padding-top: 15px;\\r\\n  padding-bottom: 15px;\\r\\n}\\r\\n\\r\\n.navbar-default .navbar-nav > li > a {\\r\\n  color: #00b1a3;\\r\\n  padding-top: 15px;\\r\\n  padding-bottom: 15px;\\r\\n  display: flex;\\r\\n  flex-direction: column;\\r\\n  justify-content: center;\\r\\n  text-decoration: none;\\r\\n}\\r\\n\\r\\n.navbar {\\r\\n  min-height: 45px;\\r\\n}\\r\\n\\r\\n.page-heading {\\r\\n  color: #333333;\\r\\n  font-size: 14px;\\r\\n  margin-top: 0px !important;\\r\\n  margin-left: 25px;\\r\\n  /* position: absolute; */\\r\\n  top: 10px;\\r\\n  left: 90px;\\r\\n  font-weight: 600;\\r\\n}\\r\\n\\r\\n.page-heading h2 {\\r\\n  font-size: 16px;\\r\\n  margin-top: 15px !important;\\r\\n}\\r\\n\\r\\n/* ----------- Notification dropdown end ----------------------- */\\r\\n\\r\\nnav.sidebar .brand a {\\r\\n  padding: 0;\\r\\n}\\r\\n\\r\\n.brand {\\r\\n  font-size: 24px;\\r\\n  /* padding: 0px 5px; */\\r\\n  color: #fff;\\r\\n  width: 69px;\\r\\n  /* background-color: #002d73; */\\r\\n  text-align: center;\\r\\n  display: flex;\\r\\n  justify-content: center;\\r\\n  align-items: center;\\r\\n}\\r\\n\\r\\n.titlebar {\\r\\n  width: 100%;\\r\\n  position: fixed;\\r\\n  /* height: 55px; */\\r\\n  z-index: 98;\\r\\n  top: 0;\\r\\n  padding-left: 69px;\\r\\n}\\r\\n\\r\\n.accordion {\\r\\n  cursor: pointer;\\r\\n  width: 100%;\\r\\n  text-align: left;\\r\\n  outline: none;\\r\\n  transition: 0.4s;\\r\\n}\\r\\n\\r\\n.active,\\r\\n.accordion:hover {\\r\\n  background-color: transparent;\\r\\n}\\r\\n\\r\\n.panel {\\r\\n  background-color: white;\\r\\n  max-height: 0;\\r\\n  overflow: hidden;\\r\\n  transition: max-height 0.2s ease-out;\\r\\n}\\r\\n\\r\\n/* .reactSelectCustom .css-1fdsijx-ValueContainer , .reactSelectCustom .css-b62m3t-ValueContainer {\\r\\n  position: relative;\\r\\n  top: -5px;\\r\\n}\\r\\n\\r\\n.reactSelectCustom .css-1hb7zxy-IndicatorsContainer, .reactSelectCustom .css-1xc3v61-IndicatorsContainer{\\r\\n  position: relative;\\r\\n  top: -5px;\\r\\n} */\\r\\n/* .reactSelectCustom .css-1jgx7bw-control{\\r\\n  flex-wrap: nowrap !important;\\r\\n} */\\r\\ninput[type=date]:invalid::-ms-datetime-edit {\\r\\n  color: #808080;\\r\\n}\\r\\n/* label {\\r\\n  font-size: 14px;\\r\\n  color: #505050;\\r\\n} */\\r\\n.top-navbar {\\r\\n  background-color: white;\\r\\n}\\r\\n.dark .top-navbar {\\r\\n  background-color: #000;\\r\\n  color: white;\\r\\n}\\r\\n\\r\\n.dark .pageName {\\r\\n  color: white;\\r\\n}\\r\\n\\r\\ninput[type=\\\"number\\\"] {\\r\\n  -webkit-appearance: textfield;\\r\\n     -moz-appearance: textfield;\\r\\n          appearance: textfield;\\r\\n}\\r\\ninput[type=number]::-webkit-inner-spin-button, \\r\\ninput[type=number]::-webkit-outer-spin-button { \\r\\n  -webkit-appearance: none;\\r\\n}\\r\\n\\r\\ninput[type=\\\"text\\\"],\\r\\ninput[type=\\\"tel\\\"],\\r\\ninput[type=\\\"date\\\"],\\r\\ninput[type=\\\"email\\\"],\\r\\ninput[type=\\\"number\\\"],\\r\\nselect {\\r\\n  padding-top: 4px;\\r\\n  padding-bottom: 4px;\\r\\n  border-color: #d6d6d6;\\r\\n  outline: 2px solid #ffffff;\\r\\n  color: #333333;\\r\\n  font-family: \\\"poppinsregular\\\";\\r\\n}\\r\\n\\r\\ninput:focus, select:focus , textarea:focus{\\r\\n  outline-color: #0066ff;\\r\\n}\\r\\n\\r\\n.button {\\r\\n  padding: 4px 12px 4px 12px;\\r\\n  font-family: \\\"poppinsmedium\\\";\\r\\n}\\r\\n\\r\\n.labels {\\r\\n  font-family: \\\"poppinsregular\\\";\\r\\n  font-size: 12px;\\r\\n  color: #505050;\\r\\n}\\r\\n.dark .labels {\\r\\n  font-family: \\\"poppinsregular\\\";\\r\\n  font-size: 12px;\\r\\n  color: white;\\r\\n}\\r\\n\\r\\n.searchbar {\\r\\n  font-family: \\\"poppinsregular\\\";\\r\\n  font-size: 12px;\\r\\n  color: #AFAFAF;\\r\\n  background-color: #FFFFFF;\\r\\n}\\r\\n\\r\\n.dark .searchbar {\\r\\n  font-family: \\\"poppinsregular\\\";\\r\\n  font-size: 12px;\\r\\n  color: #c0c0c0;\\r\\n  background-color: #1d212d;\\r\\n  outline-color: #1d212d;\\r\\n}\\r\\n\\r\\n.checkboxbg {\\r\\n  background-color: #D9D9D9;\\r\\n}\\r\\n.dark .checkboxbg {\\r\\n  background-color: #4d4d4d;\\r\\n}\\r\\n\\r\\n.dark select {\\r\\n  background-color: #1d1d1d;\\r\\n  color: white;\\r\\n}\\r\\n\\r\\n.borderc-theme-blue2 {\\r\\n  border-color: #0066ff;\\r\\n}\\r\\n.dark .borderc-theme-blue2 {\\r\\n  border-color: #6699ff;\\r\\n}\\r\\n.textc-theme-blue2 {\\r\\n  color: #0066ff;\\r\\n}\\r\\n.dark .textc-theme-blue2 {\\r\\n  color: #6699ff;\\r\\n}\\r\\n\\r\\n.bgc-theme-blue2 {\\r\\n  background-color: #0066ff;\\r\\n}\\r\\n.dark .bgc-theme-blue2 {\\r\\n  background-color: #6699ff;\\r\\n}\\r\\n.textc-dark-gray {\\r\\n  color: #727272;\\r\\n}\\r\\n.dark .textc-dark-gray {\\r\\n  color: #a3a3a3;\\r\\n}\\r\\n.borderc-dark-gray {\\r\\n  color: #727272;\\r\\n}\\r\\n.dark .borderc-dark-gray {\\r\\n  color: #a3a3a3;\\r\\n}\\r\\n\\r\\n.input {\\r\\n  font-size: 12px;\\r\\n  font-family: \\\"poppinsregular\\\";\\r\\n}\\r\\n\\r\\n.dark .inputs {\\r\\n  font-family: \\\"poppinsregular\\\";\\r\\n  font-size: 12px;\\r\\n  color: white;\\r\\n}\\r\\n\\r\\n.confirmInputs {\\r\\n    font-size: 12px;\\r\\n    color: #333333;\\r\\n    font-family: \\\"poppinsmedium\\\";\\r\\n}\\r\\n\\r\\n.formtitle {\\r\\n  font-family: \\\"poppinssemibold\\\";\\r\\n  color: #333333;\\r\\n  font-size: 12px;\\r\\n}\\r\\n\\r\\n.subtitles {\\r\\n  font-family: \\\"poppinssemibold\\\";\\r\\n  color: #333333;\\r\\n  font-size: 14px;\\r\\n}\\r\\n\\r\\n.text-blackcolor {\\r\\n  color:#505050;\\r\\n  font-size: 12px;\\r\\n  font-family: \\\"poppinsregular\\\";\\r\\n}\\r\\n\\r\\n.ag-ltr .ag-cell-focus:not(.ag-cell-range-selected):focus-within {\\r\\n  border: none !important;\\r\\n}\\r\\n\\r\\n.ag-row-odd{\\r\\n  background-color: #f3f3f3 !important;\\r\\n}\\r\\n\\r\\n.ag-cell-value span {\\r\\n  height: auto !important;\\r\\n  line-height: normal !important;\\r\\n}\\r\\n\\r\\n.ag-cell{\\r\\n  display:flex;\\r\\n  align-items:center;\\r\\n  border: none !important;\\r\\n}\\r\\n\\r\\n.ag-cell-value, .ag-header-cell-text{\\r\\n  text-overflow: unset !important;\\r\\n}\\r\\n\\r\\n.pagination-style {\\r\\n  position: absolute;\\r\\n  bottom: 10px;\\r\\n  left: 20px;\\r\\n}\\r\\n\\r\\n.pagination-style select {\\r\\n  background-color: #f2f2f2;\\r\\n  border-radius: 3px;\\r\\n}\\r\\n\\r\\n.ag-ltr .ag-header-select-all{\\r\\n  margin-right: 12px !important; \\r\\n}\\r\\n\\r\\n.ag-root-wrapper.ag-layout-normal{\\r\\n  border-radius: 5px !important;\\r\\n}\\r\\n\\r\\n.distribution-point .ag-header-container {\\r\\n    /* width: 100% !important; */\\r\\n}\\r\\n\\r\\n.distribution-point .ag-header-row.ag-header-row-column {\\r\\n    /* width: 100% !important; */\\r\\n}\\r\\n\\r\\n.distribution-point .ag-center-cols-container {\\r\\n    /* width: 100% !important; */\\r\\n}\\r\\n\\r\\n.general_section .ag-header-container, .general_section .ag-center-cols-container, .general_section .ag-header-row  {\\r\\n  width: 100% !important;\\r\\n}\\r\\n\\r\\n.general_section .ag-cell {\\r\\n  /* display: flex; */\\r\\n  margin-right: 10px;\\r\\n}\\r\\n\\r\\n.product_link_def .ag-row .ag-cell {\\r\\n  display: flex;\\r\\n  align-items: center;\\r\\n}\\r\\n\\r\\n.product_data_def .ag-cell {\\r\\n  display: flex;\\r\\n  align-items: center;\\r\\n}\\r\\n\\r\\n/* @media (min-width: 640px) {\\r\\n  .ag-header-cell-text,.dark .ag-header-cell-text,.ag-ltr .ag-cell,.dark .ag-ltr .ag-cell,.ag-paging-row-summary-panel-number,.dark .ag-paging-row-summary-panel-number,.ag-paging-page-summary-panel,.dark .ag-paging-page-summary-panel,.pagination,.dark .pagination,.labels,.dark .labels,.searchbar,.dark .searchbar,.inputs,.dark .inputs,.formtitle{\\r\\n    font-size: 10px;\\r\\n  }\\r\\n} */\\r\\n\\r\\n.viewlog .ag-cell-value {\\r\\n    align-items: center;\\r\\n    display: flex;\\r\\n}\\r\\n\\r\\n@media (min-width: 765px) {\\r\\n  .main {\\r\\n    position: absolute;\\r\\n    width: calc(100% - 40px);\\r\\n    margin-left: 40px;\\r\\n    float: right;\\r\\n  }\\r\\n  nav.sidebar:hover + .main {\\r\\n    margin-left: 200px;\\r\\n  }\\r\\n  nav.sidebar.navbar.sidebar > .container .navbar-brand,\\r\\n  .navbar > .container-fluid .navbar-brand {\\r\\n    margin-left: 0px;\\r\\n  }\\r\\n  nav.sidebar .navbar-brand,\\r\\n  nav.sidebar .navbar-header {\\r\\n    text-align: center;\\r\\n    width: 100%;\\r\\n    margin-left: 0px;\\r\\n  }\\r\\n  /* nav.sidebar a {\\r\\n    padding-bottom: 34px;\\r\\n  } */\\r\\n\\r\\n  nav.sidebar .navbar-nav > li {\\r\\n    font-size: 13px;\\r\\n  }\\r\\n  nav.sidebar .navbar-nav .open .dropdown-menu {\\r\\n    position: static;\\r\\n    float: none;\\r\\n    width: auto;\\r\\n    margin-top: 0;\\r\\n    background-color: transparent;\\r\\n    border: 0;\\r\\n    -webkit-box-shadow: none;\\r\\n    box-shadow: none;\\r\\n  }\\r\\n  nav.sidebar .navbar-collapse,\\r\\n  nav.sidebar .container-fluid {\\r\\n    padding: 0 0px 0 0px;\\r\\n  }\\r\\n  .navbar-inverse .navbar-nav .open .dropdown-menu > li > a {\\r\\n    color: #777;\\r\\n  }\\r\\n  nav.sidebar {\\r\\n    width: 69px;\\r\\n    height: 100%;\\r\\n    margin-bottom: 0px;\\r\\n    position: fixed;\\r\\n    top: 0px;\\r\\n    display: flex;\\r\\n    align-items: flex-start;\\r\\n  }\\r\\n  nav.sidebar li {\\r\\n    width: 100%;\\r\\n  }\\r\\n\\r\\n  .forAnimate {\\r\\n    opacity: 0;\\r\\n  }\\r\\n}\\r\\n\\r\\n\\r\\n@media (min-width: 1024px) {\\r\\n  \\r\\n}\\r\\n\\r\\n/* @media (min-width: 1280px) {\\r\\n  .ag-header-cell-text,.dark .ag-header-cell-text,.ag-ltr .ag-cell,.dark .ag-ltr .ag-cell,.ag-paging-row-summary-panel-number,.dark .ag-paging-row-summary-panel-number,.ag-paging-page-summary-panel,.dark .ag-paging-page-summary-panel,.pagination,.dark .pagination,.labels,.dark .labels,.searchbar,.dark .searchbar,.inputs,.dark .inputs,.formtitle{\\r\\n    font-size: 14px;\\r\\n  }}\\r\\n\\r\\n@media (min-width: 1536px) {\\r\\n  \\r\\n}\\r\\n@media (min-width: 1940px) {\\r\\n  \\r\\n} */\\r\\n\\r\\n.desktop-view-message {\\r\\n  position: fixed;\\r\\n  top: 0;\\r\\n  left: 0;\\r\\n  width: 100%;\\r\\n  height: 100%;\\r\\n  background-color: rgba(0, 0, 0, 0.5); /* Semi-transparent black */\\r\\n  display: flex;\\r\\n  justify-content: center;\\r\\n  align-items: center;\\r\\n  z-index: 9999; /* Ensure it's on top of other content */\\r\\n}\\r\\n.block-view-message {\\r\\n  position: fixed;\\r\\n  top: 0;\\r\\n  left: 0;\\r\\n  width: 100%;\\r\\n  height: 100%;\\r\\n  background-color: rgba(214, 21, 21, 0.5); /* Semi-transparent black */\\r\\n  display: flex;\\r\\n  justify-content: center;\\r\\n  align-items: center;\\r\\n  z-index: 9999; /* Ensure it's on top of other content */\\r\\n}\\r\\n\\r\\n.message-content {\\r\\n  text-align: center;\\r\\n  color: white;\\r\\n}\\r\\n\\r\\n.message-content h2 {\\r\\n  font-size: 24px;\\r\\n  margin-bottom: 10px;\\r\\n}\\r\\n\\r\\n.message-content p {\\r\\n  font-size: 16px;\\r\\n}\\r\\n/* quarter filter css */\\r\\n\\r\\n #q1:checked  + .labelcheck, #q2:checked  + .labelcheck, #q3:checked  + .labelcheck, #q4:checked  + .labelcheck, #all:checked  + .labelcheck, #needsupdate:checked  + .labelcheck{\\r\\n  background-color: #00E0D5;\\r\\n  border:1px solid #00BBB2;\\r\\n  color:#fff;\\r\\n  font-weight: 500;\\r\\n  font-family: \\\"poppinsregular\\\";\\r\\n}\\r\\n/* section filter css */\\r\\n #volume:checked  + .labelcheck, #breakeven:checked  + .labelcheck, #unitprice:checked  + .labelcheck, #grossprofit:checked  + .labelcheck, #gppercent:checked  + .labelcheck, #value:checked  + .labelcheck{\\r\\n  background-color: #00E0D5;\\r\\n  border:1px solid #00BBB2;\\r\\n  color:#fff;\\r\\n  font-weight: 500;\\r\\n  font-family: \\\"poppinsregular\\\";\\r\\n}\\r\\n #bestatus-1:checked  + .labelcheck {\\r\\n  background-color: #FF9A03;\\r\\n  color:#fff;\\r\\n  font-weight: 500;\\r\\n  font-family: \\\"poppinsregular\\\";\\r\\n}\\r\\n#bestatus-2:checked  + .labelcheck{\\r\\n  background-color: #33CA7F;\\r\\n  /* border:1px solid #00BBB2; */\\r\\n  color:#444;\\r\\n  font-weight: 500;\\r\\n  font-family: \\\"poppinsregular\\\";\\r\\n}\\r\\n#bestatus-3:checked  + .labelcheck{\\r\\n  background-color: #FFDF37;\\r\\n  /* border:1px solid #00BBB2; */\\r\\n  color:#fff;\\r\\n  font-weight: 500;\\r\\n  font-family: \\\"poppinsregular\\\";\\r\\n}\\r\\n#bestatus-4:checked  + .labelcheck{\\r\\n  background-color: #FB4646;\\r\\n  /* border:1px solid #00BBB2; */\\r\\n  color:#fff;\\r\\n  font-weight: 500;\\r\\n  font-family: \\\"poppinsregular\\\";\\r\\n}\\r\\n\\r\\n.planningtoolgrid\\r\\n{\\r\\n   border-collapse: separate;\\r\\n   border-spacing: 0;\\r\\n   /* border: 1px solid #ddd; */\\r\\n}\\r\\n.input-number::-webkit-inner-spin-button,\\r\\n.input-number::-webkit-outer-spin-button {\\r\\n  -webkit-appearance: none;\\r\\n  margin: 0;\\r\\n}\\r\\n\\r\\n\\r\\n.planningtoolgrid thead th {\\r\\n  padding: 5px;\\r\\n  text-align: center;\\r\\n  position: -webkit-sticky; /* for Safari */\\r\\n  height: 35px;\\r\\n  width:90px;\\r\\n  overflow:hidden;\\r\\n  border: 1px solid #ddd;\\r\\n  font-family: \\\"poppinsregular\\\";\\r\\n  font-family: \\\"poppinsregular\\\";\\r\\n}\\r\\n/* .planningtoolgrid thead tr, .planningtoolgrid tbody tr{\\r\\n  border: 1px solid #ddd;\\r\\n} */\\r\\n.planningtoolgrid thead tr:first-child th{\\r\\n  border: none !important;\\r\\n}\\r\\n/* .planningtoolgrid tbody tr{\\r\\n height:70px !important;\\r\\n} */\\r\\n.planningtoolgrid tbody th{\\r\\n  width:120px; \\r\\n  height: 40px;\\r\\n}\\r\\n.planningtoolgrid td{\\r\\n  padding: 5px;\\r\\n  border: 1px solid #ddd;\\r\\n  text-align: left;\\r\\n  z-index:0;\\r\\n  width:90px; \\r\\n  height: 35px !important;\\r\\n  overflow:hidden;\\r\\n  font-family: \\\"poppinsregular\\\";\\r\\n}\\r\\n.planningtoolgrid thead{\\r\\n  position:sticky;\\r\\n  top:0;\\r\\n  left:0;\\r\\n  background:#f3f8ff;\\r\\n  z-index:10;\\r\\n}\\r\\n\\r\\n.quartertotals{\\r\\n  /* border:0; */\\r\\n  table-layout: fixed;\\r\\n  width:50%;\\r\\n  text-align:center;\\r\\n}\\r\\n.quartertotals thead th, .quartertotals tbody td{\\r\\n  text-align: center;\\r\\n  border:0;\\r\\n}\\r\\n.quartertotals thead tr, .quartertotals tbody tr {\\r\\n    border: none;\\r\\n}\\r\\n.quartertotals thead th{\\r\\n  border-top-left-radius: 20px;\\r\\n  border-top-right-radius: 20px;\\r\\n  background-color: #fff !important;\\r\\n}\\r\\n.quartertotals thead{\\r\\n  z-index: 5 !important;\\r\\n}\\r\\n\\r\\n.quartertotals thead th:first-child, .quartertotals tbody td:first-child{\\r\\n  background-color: #f3f8ff !important;\\r\\n  width:50px !important;\\r\\n}\\r\\n.quartertotals tbody td:nth-child(2){\\r\\n  background-color: #201E50 !important;\\r\\n  color:#fff;\\r\\n  border-bottom: 1px solid #504F66;\\r\\n  border-right: 5px solid #f3f8ff;\\r\\n}\\r\\n.quartertotals tbody td:nth-child(3){\\r\\n  background-color: #D499B9 !important;\\r\\n  color:#fff;\\r\\n  border-bottom: 1px solid #C0749E;\\r\\n  border-right: 5px solid #f3f8ff;\\r\\n  border-left: 5px solid #f3f8ff;\\r\\n}\\r\\n.quartertotals tbody td:nth-child(4){\\r\\n  background-color: #EE6C4D !important;\\r\\n  color:#fff;\\r\\n  border-bottom: 1px solid #CE4B2C;\\r\\n  border-left: 5px solid #f3f8ff;\\r\\n}\\r\\n.quartertotals tbody tr:last-child td{\\r\\n    border-bottom-left-radius: 15px;\\r\\n    border-bottom-right-radius: 15px;\\r\\n    border-bottom: none;\\r\\n}\\r\\n\\r\\n.titlerow{\\r\\n  background-color: #00E0D5;\\r\\n  color:#fff;\\r\\n  z-index: 9;\\r\\n  font-size: 15px;\\r\\n  height: 28px !important;\\r\\n}\\r\\ntd.sectionrow,th.sectionrow{\\r\\n  background-color: #DDD;\\r\\n  color:#444;\\r\\n  font-weight: 600;\\r\\n  z-index: 9;\\r\\n  height: 28px !important;\\r\\n  font-size: 13px !important;\\r\\n}\\r\\ninput[type=\\\"text\\\"]:disabled, input[type=\\\"number\\\"]:disabled, select:disabled{\\r\\n  background-color: #F6F3F3;\\r\\n}\\r\\n\\r\\n#wrapper.closed .list {\\r\\n  display: none;\\r\\n   transition: height 200ms;\\r\\n}\\r\\n\\r\\n#wrapper.open .list {\\r\\n  display: block;\\r\\n   transition: height 200ms;\\r\\n}\\r\\n\\r\\n.bg-currentWeek{\\r\\n  background-color: #fcf6b1;\\r\\n}\\r\\n.nodata{\\r\\n  background-color:#ffecd4;\\r\\n  color:#b31818\\r\\n}\\r\\n.selected{\\r\\n  color: #0066FF;\\r\\n  border-bottom: 3px solid #0066FF;\\r\\n}\\r\\n.buttonText{\\r\\n  font-family: 'poppinsmedium';\\r\\n  \\r\\n}\\r\\n\\r\\n/*--- SLP CSS ---*/\\r\\n\\r\\n.service-level-grid{\\r\\n   border-collapse: separate;\\r\\n   border-spacing: 0;\\r\\n}\\r\\n\\r\\n.service-level-grid thead th {\\r\\n  padding: 5px;\\r\\n  text-align: left;\\r\\n  position: -webkit-sticky; /* for Safari */\\r\\n  height: 35px;\\r\\n  width:90px;\\r\\n  overflow:hidden;\\r\\n  border: 1px solid #ddd;\\r\\n  white-space: normal;\\r\\n  background:#f3f8ff;\\r\\n  font-family: \\\"poppinsregular\\\";\\r\\n}\\r\\n\\r\\n/* .service-level-grid thead tr:first-child th{\\r\\n  border: none !important;\\r\\n} */\\r\\n.service-level-grid thead tr th:last-child{\\r\\n  background:#f3f8ff;\\r\\n  position:sticky;\\r\\n  right:0;\\r\\n}\\r\\n.service-level-grid tbody tr td:last-child{\\r\\n  background:#fff;\\r\\n  position:sticky;\\r\\n  right:0;\\r\\n}\\r\\n\\r\\n.service-level-grid tbody th{\\r\\n  width:120px; \\r\\n  height: 40px;\\r\\n  border: 1px solid #ddd;\\r\\n  padding:5px;\\r\\n  z-index: 5;\\r\\n  background:#f3f8ff;\\r\\n  font-family: \\\"poppinsregular\\\";\\r\\n}\\r\\n.service-level-grid td{\\r\\n  padding: 5px;\\r\\n  border: 1px solid #ddd;\\r\\n  background-color:white;\\r\\n  text-align: left;\\r\\n  z-index:0;\\r\\n  width:90px; \\r\\n  height: 35px !important;\\r\\n  overflow:hidden;\\r\\n  font-family: \\\"poppinsregular\\\";\\r\\n}\\r\\n.service-level-grid thead{\\r\\n  position:sticky;\\r\\n  top:0;\\r\\n  left:0;\\r\\n  /* background:#f3f8ff; */\\r\\n  z-index:10;\\r\\n}\\r\\n\\r\\n.ag-grid-checkbox-cell .ag-checkbox-input {\\r\\n  cursor: pointer;\\r\\n}\\r\\n\\r\\n.depotdaterange .rdrDateDisplayWrapper{\\r\\n  display:none;\\r\\n}\\r\\n\\r\\nlabel{\\r\\n  font-family: \\\"poppinsregular\\\";\\r\\n}\\r\\nselect::placeholder, .css-1jqq78o-placeholder, .placeholdertext {\\r\\n  color: #333333 !important;\\r\\n  opacity: 0.5;\\r\\n  font-weight: 500;\\r\\n  font-family:\\\"poppinsregular\\\";\\r\\n}\\r\\n.text-truncate2L {\\r\\n  display: -webkit-box;\\r\\n  -webkit-box-orient: vertical;\\r\\n  -webkit-line-clamp: 2;\\r\\n  overflow: hidden;\\r\\n  text-overflow: ellipsis;\\r\\n  }\\r\\n\\r\\n  .variety-disabled-block{\\r\\n    background-color: #f6f6f6;\\r\\n    color: #888888;\\r\\n    border:1px solid #E2E2E2;\\r\\n    box-shadow: none;\\r\\n  }\\r\\n  .variety-disabled-block h4, .variety-disabled-block label{\\r\\n    color:#797979;\\r\\n  }\",null],\"sourceRoot\":\"\"}]);\n// Exports\n/* harmony default export */ __webpack_exports__[\"default\"] = (___CSS_LOADER_EXPORT___);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[8].oneOf[13].use[1]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[8].oneOf[13].use[2]!./styles/globals.css\n"));

/***/ })

});