"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/products",{

/***/ "./components/Navbar.js":
/*!******************************!*\
  !*** ./components/Navbar.js ***!
  \******************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   themes: function() { return /* binding */ themes; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _fortawesome_react_fontawesome__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @fortawesome/react-fontawesome */ \"./node_modules/@fortawesome/react-fontawesome/index.es.js\");\n/* harmony import */ var _fortawesome_free_solid_svg_icons__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @fortawesome/free-solid-svg-icons */ \"./node_modules/@fortawesome/free-solid-svg-icons/index.mjs\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/link */ \"./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _azure_msal_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @azure/msal-react */ \"./node_modules/@azure/msal-react/dist/index.js\");\n/* harmony import */ var js_cookie__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! js-cookie */ \"./node_modules/js-cookie/dist/js.cookie.mjs\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nconst themes = [\n    {\n        bg: \"#022d71\",\n        text: \"#022d71\",\n        name: \"DPS\"\n    },\n    {\n        bg: \"#2e9b28\",\n        text: \"#2e9b28\",\n        name: \"EFC\"\n    },\n    {\n        bg: \"#a91e23\",\n        text: \"#a91e23\",\n        name: \"M&S\"\n    },\n    {\n        bg: \"#3d6546\",\n        text: \"#3d6546\",\n        name: \"FPP\"\n    }\n];\nconst Navbar = (param)=>{\n    let { userData } = param;\n    _s();\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_4__.useRouter)();\n    const [pageName, setPageName] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [currentRoute, setCurrentRoute] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [isUpdating, setIsUpdating] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const currentPath = router.pathname;\n        setCurrentRoute(currentPath);\n        if (currentPath === \"/finished-product-request/add\") {\n            js_cookie__WEBPACK_IMPORTED_MODULE_6__[\"default\"].set(\"PreviousPage\", \"FG\");\n            setPageName(\"Finished Goods Request\");\n        } else if (currentPath === \"/raw-material-request/add\") {\n            setPageName(\"Raw Material Request\");\n            js_cookie__WEBPACK_IMPORTED_MODULE_6__[\"default\"].set(\"PreviousPage\", \"RM\");\n        } else if (currentPath === \"/packaging-form/add\") {\n            setPageName(\"Packaging Request\");\n        } else if (currentPath === \"/variety/add\") {\n            js_cookie__WEBPACK_IMPORTED_MODULE_6__[\"default\"].set(\"PreviousPage\", \"NV\");\n            setPageName(\"New Variety Request\");\n        } else if (currentPath === \"/variety/[productId]/edit\") {\n            js_cookie__WEBPACK_IMPORTED_MODULE_6__[\"default\"].set(\"PreviousPage\", \"NV\");\n            setPageName(\"Edit New Variety Request\");\n        } else if (currentPath === \"/packaging-form/[productId]/edit\") {\n            js_cookie__WEBPACK_IMPORTED_MODULE_6__[\"default\"].set(\"PreviousPage\", \"PK\");\n            setPageName(\"Edit Packaging Request\");\n        } else if (currentPath === \"/raw-material-request/[productId]/edit\") {\n            js_cookie__WEBPACK_IMPORTED_MODULE_6__[\"default\"].set(\"PreviousPage\", \"RM\");\n            setPageName(\"Edit Raw Material Request\");\n        } else if (currentPath === \"/finished-product-request/[productId]/edit\") {\n            js_cookie__WEBPACK_IMPORTED_MODULE_6__[\"default\"].set(\"PreviousPage\", \"FG\");\n            setPageName(\"Edit Finished Goods Request\");\n        } else if (currentPath === null || currentPath === void 0 ? void 0 : currentPath.endsWith(\"/edit\")) {\n            setPageName(\"Edit Supplier\");\n        } else if (currentPath === null || currentPath === void 0 ? void 0 : currentPath.endsWith(\"/add\")) {\n            setPageName(\"Add Supplier\");\n        } else if (currentPath === null || currentPath === void 0 ? void 0 : currentPath.endsWith(\"/edit/forms\")) {\n            setPageName(\"Supplier Form\");\n        } else if (currentPath === null || currentPath === void 0 ? void 0 : currentPath.endsWith(\"/confirm\")) {\n            setPageName(\"Confirm Details for Supplier\");\n        } else if (currentPath === \"/suppliers\") {\n            setPageName(\"Suppliers\");\n        } else if (currentPath === \"/users\") {\n            setPageName(\"User Management\");\n        } else if (currentPath === \"/viewlogs\") {\n            setPageName(\"View Logs\");\n        } else if (currentPath === \"/products\") {\n            js_cookie__WEBPACK_IMPORTED_MODULE_6__[\"default\"].set(\"PreviousPage\", \"RM\");\n            setPageName(\"Products\");\n        } else if (currentPath === \"/variety\") {\n            setPageName(\"Variety\");\n            js_cookie__WEBPACK_IMPORTED_MODULE_6__[\"default\"].set(\"PreviousPage\", \"NV\");\n        } else if (currentPath === \"/finishedProductRequest\") {\n            js_cookie__WEBPACK_IMPORTED_MODULE_6__[\"default\"].set(\"PreviousPage\", \"FG\");\n            setPageName(\"Finished Product Request\");\n        } else if (currentPath === \"/rawMaterialRequest\") {\n            js_cookie__WEBPACK_IMPORTED_MODULE_6__[\"default\"].set(\"PreviousPage\", \"RM\");\n            setPageName(\"Raw Material Request\");\n        } else if (currentPath === \"/whatif\") {\n            setPageName(\"Whatif\");\n        } else if (currentPath === \"/service_level\" || currentPath === \"/service_level/reports/masterForcast\") {\n            setPageName(\"Service Level\");\n        }\n    }, [\n        router.pathname\n    ]);\n    const baseCompanyOptions = [\n        {\n            value: \"dpsltd\",\n            label: \"DPS\"\n        },\n        {\n            value: \"dpsltdms\",\n            label: \"DPS M&S\"\n        },\n        {\n            value: \"efcltd\",\n            label: \"EFC\"\n        },\n        {\n            value: \"fpp-ltd\",\n            label: \"FPP\"\n        }\n    ];\n    const companyOptions = [\n        ...baseCompanyOptions,\n        ...(userData === null || userData === void 0 ? void 0 : userData.role_id) === 6 ? [\n            {\n                value: \"issproduce\",\n                label: \"ISS\"\n            },\n            {\n                value: \"flrs\",\n                label: \"FLRS\",\n                disabled: true\n            },\n            {\n                value: \"thl\",\n                label: \"THL\",\n                disabled: true\n            }\n        ] : []\n    ];\n    const [selectedCompany, setSelectedCompany] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)((userData === null || userData === void 0 ? void 0 : userData.ADCompanyName) === \"DPS MS\" ? \"dpsltdms\" : (userData === null || userData === void 0 ? void 0 : userData.company) || \"\");\n    const handleCompanyChange = async (event)=>{\n        const company = event.target.value;\n        if (isUpdating) return; // Prevent multiple rapid clicks\n        setIsUpdating(true);\n        try {\n            console.log(\"Updating company to:\", company);\n            const apiBase = \"http://localhost:8081\" || 0;\n            // Call the API to update company in session\n            const response = await fetch(\"\".concat(apiBase, \"/api/auth/update-company\"), {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                credentials: \"include\",\n                body: JSON.stringify({\n                    company\n                })\n            });\n            if (!response.ok) {\n                const errorData = await response.json();\n                throw new Error(errorData.error || \"Failed to update company\");\n            }\n            const result = await response.json();\n            console.log(\"Company updated successfully:\", result);\n            // Update local state\n            setSelectedCompany(company);\n            // Reload the page to reflect changes throughout the application\n            router.reload();\n        } catch (error) {\n            console.error(\"Error updating company:\", error);\n            alert(\"Failed to update company. Please try again.\");\n            // Reset the select to previous value on error\n            event.target.value = selectedCompany;\n        } finally{\n            setIsUpdating(false);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"titlebar\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex flex-row justify-between w-full bg-skin-primary h-14\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-row w-full items-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"page-heading cursor-pointer\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fortawesome_react_fontawesome__WEBPACK_IMPORTED_MODULE_2__.FontAwesomeIcon, {\n                                    icon: _fortawesome_free_solid_svg_icons__WEBPACK_IMPORTED_MODULE_7__.faChevronLeft,\n                                    className: \"pageName text-white\",\n                                    onClick: ()=>router.back()\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\Navbar.js\",\n                                    lineNumber: 159,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                    href: currentRoute,\n                                    className: \"ml-4 2xl:text-lg font-poppinssemibold pageName text-white tracking-wide\",\n                                    children: pageName\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\Navbar.js\",\n                                    lineNumber: 164,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\Navbar.js\",\n                            lineNumber: 158,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\Navbar.js\",\n                        lineNumber: 157,\n                        columnNumber: 11\n                    }, undefined),\n                    ((userData === null || userData === void 0 ? void 0 : userData.role_id) == 5 || (userData === null || userData === void 0 ? void 0 : userData.role_id) == 6) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-row justify-end w-1/2 items-center mr-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                value: selectedCompany,\n                                onChange: handleCompanyChange,\n                                disabled: isUpdating,\n                                className: \"bg-white text-black rounded disabled:opacity-50\",\n                                children: companyOptions.map((opt)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: opt.value,\n                                        disabled: opt.disabled,\n                                        children: opt.label\n                                    }, opt.value, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\Navbar.js\",\n                                        lineNumber: 181,\n                                        columnNumber: 19\n                                    }, undefined))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\Navbar.js\",\n                                lineNumber: 174,\n                                columnNumber: 15\n                            }, undefined),\n                            isUpdating && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"ml-2 text-white text-sm\",\n                                children: \"Updating...\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\Navbar.js\",\n                                lineNumber: 191,\n                                columnNumber: 17\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\Navbar.js\",\n                        lineNumber: 173,\n                        columnNumber: 13\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\Navbar.js\",\n                lineNumber: 156,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\Navbar.js\",\n            lineNumber: 155,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\Navbar.js\",\n        lineNumber: 154,\n        columnNumber: 5\n    }, undefined);\n};\n_s(Navbar, \"EcIBICVLCJu3IdXMDDAfVy7bwBw=\", false, function() {\n    return [\n        next_router__WEBPACK_IMPORTED_MODULE_4__.useRouter\n    ];\n});\n_c = Navbar;\n/* harmony default export */ __webpack_exports__[\"default\"] = (Navbar);\nvar _c;\n$RefreshReg$(_c, \"Navbar\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevExports = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevExports) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports on update so we can compare the boundary\n                // signatures.\n                module.hot.dispose(function (data) {\n                    data.prevExports = currentExports;\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevExports !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevExports, currentExports)) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevExports !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/Navbar.js\n"));

/***/ })

});