import React, {
  useEffect,
  useState,
  useRef,
  useCallback,
  Fragment,
} from "react";

import Layout from "@/components/Layout";
import Cookies from "js-cookie";
import Select from "react-select";
import { ToastContainer, toast } from "react-toastify";
import { apiConfig } from "@/services/apiConfig";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import prophetLogo from "@/public/images/ProphetLogo.png";
import DrawerComponent from "./DrawerComponent";
import debounce from "lodash/debounce";
import { Router, useRouter } from "next/router";
import { ThreeCircles } from "react-loader-spinner";
import { Dialog, Transition } from "@headlessui/react";
import { useLoading } from "@/utils/loaders/loadingContext";
import {
  FluentProvider,
  SSRProvider,
  webLightTheme,
} from "@fluentui/react-components";
import {
  faPlus,
  faInfoCircle,
  faSearch,
  faInfo,
  faXmark,
  faTriangleExclamation,
} from "@fortawesome/free-solid-svg-icons";
import exportExcel from "@/utils/exportExcel";
import DebouncedVarietySearch from "@/utils/DebouncedVarietySearch";

// const options = [
//   { value: "chocolate", label: "Chocolate" },
//   { value: "strawberry", label: "Strawberry" },
//   { value: "vanilla", label: "Vanilla" },
// ];

const customSelectStyles = {
  // Default style
  control: (base) => ({
    ...base,
    height: "28px",
    minHeight: "28px",
    // border: 0,
  }),
  // Style when the condition is true
  option: (base, { data }) => ({
    ...base,
    color: data.is_new == true ? "red" : "",
  }),

  valueContainer: (provided, state) => ({
    ...provided,
    height: "28px",
    padding: "0 6px",
  }),

  input: (provided, state) => ({
    ...provided,
    margin: "0px",
  }),
  indicatorSeparator: (state) => ({
    display: "none",
  }),
  indicatorsContainer: (provided, state) => ({
    ...provided,
    height: "28px",
  }),
};

const NewVarietyRequest = ({
  dropdowns,
  userData,
  newVarietyData,
  pageType,
}) => {
  const serverAddress = apiConfig.serverAddress;
  const [allDropdown, setAlldropDown] = useState({});
  const [submittedToISS, setSubmittedToISS] = useState();
  const [isUserHaveCreated, setIsUserHaveCreated] = useState(false);
  const [disabledClass, setDisabledClass] = useState("");
  const [rawMasterCode, setRawMasterCode] = useState("");
  const [isValidMasterCode, setIsValidMasterCode] = useState(true);
  const [isTechinicalTeam, setIsTechinicalTeam] = useState(false);
  const [isIssUser, setIsIssUser] = useState(false);
  const [varietySearchInput, setVarietySearchInput] = useState("");
  const [varietyCode, setVarietyCode] = useState("");
  const [varietyDescription, setVarietyDescription] = useState("");
  const [comments, setComments] = useState("");
  const [originatorName, setOriginatorName] = useState("");
  const [originatorEmail, setOriginatorEmail] = useState("");
  const [masterProductCode, setMasterProductCode] = useState([]);
  const [isDrawerOpen, setIsDrawerOpen] = useState(false);
  const masterProdRef = useRef(null);
  const [title, setTitle] = useState("");
  const [dataKey, setDataKey] = useState("");
  const [dropdownData, setDropDownData] = useState("");
  const [placeholderText, setPlaceholderText] = useState("");
  const [legend, setLegend] = useState("");
  const [minLength, setMinLength] = useState("");
  const [maxLength, setMaxLength] = useState("");
  const [filteredItems, setFilteredItems] = useState([]);
  const [isDropdownLoading, setIsDropdownLoading] = useState(false);
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  const [technicalReviewComment, setTechnicalReviewComment] = useState(null);
  const [issComment, setIssComment] = useState(null);
  const [approvalStatus, setApprovalStatus] = useState("yettoreview");
  const [approvalIssStatus, setApprovalIssStatus] = useState("isstosetup");
  const [prophetApprovalStatus, setProphetApprovalStatus] =
    useState("notcomplete");
  const [isSubmitted, setIsSubmitted] = useState(false);
  const [techFormSubmitted, setTechFormSubmitted] = useState(false);
  const [issFormSubmitted, setIssFormSubmitted] = useState(false);
  const [isValidVarietyDesc, setIsValidVarietyDesc] = useState(true);
  const [isValidVarietyCode, setIsValidVarietyCode] = useState(true);
  const [loading, setLoading] = useState(false);
  const [requestNumber, setRequestNumber] = useState("");
  const [isFormValid, setIsFormValid] = useState(false);
  const [prophetId, setProphetId] = useState(null);
  const [companyName, setCompanyName] = useState("");
  const [isOpen, setIsOpen] = useState(false);
  const [varietyId, setVarietyId] = useState("");
  const [requestCompany, setRequestCompany] = useState("");
  const [requestId, setRequestId] = useState("");
  const [nvStatus, setNVStatus] = useState("");
  const [onSubmit, setOnSubmit] = useState(false);
  const [buttonType, setButtonType] = useState("");
  const router = useRouter();
  const dropdownRef = useRef(null);
  const { setIsLoading } = useLoading();
  const [isRequestInfoEnabled, setIsRequestInfoEnabled] = useState(false);
  const [isTechnicalRequestEnabled, setIsTechnicalRequestEnabled] =
    useState(false);
  const [isISSFormEnabled, setIsISSFormEnabled] = useState(false);
  const [varietyCodeExists, setVarietyCodeExists] = useState(false);
  const [varietyNameExists, setVarietyNameExists] = useState(false);
  const [wasRejected, setWasRejected] = useState(false);
  const [isProphetApprovalEnabled, setIsProphetApprovalEnabled] =
    useState(false);

  const openModal = () => {
    setIsOpen(true);
  };

  const closeModal = () => {
    setIsOpen(false);
  };

  useEffect(() => {
    const isValid =
      originatorName.trim() &&
      masterProductCode.length > 0 &&
      varietyCode.trim() &&
      varietyDescription.trim();

    setIsFormValid(isValid);
  }, [originatorName, masterProductCode, varietyCode, varietyDescription]);

  useEffect(() => {
    const loggedInUserCompany = Cookies.get("company");
    const isISSU = loggedInUserCompany == "issproduce";
    const isNormalUser = userData?.department_id != 5 && !isISSU;
    const isTechnicalUser = userData?.department_id == 5 && !isISSU;

    // const isNormalUser = true;
    // const isTechnicalUser = false;
    // const isISSU = false;

    setIsTechinicalTeam(isTechnicalUser);
    setIsIssUser(isISSU);

    // #region For request form
    if (
      isNormalUser &&
      (originatorEmail == "" || originatorEmail == userData.email) &&
      (nvStatus == "" || nvStatus == "Draft" || nvStatus == "Rejected")
    ) {
      setIsRequestInfoEnabled(true);
    } else if (
      isTechnicalUser &&
      originatorEmail == userData.email &&
      (nvStatus == "" || nvStatus == "Draft" || nvStatus == "Pending Review")
    ) {
      setIsRequestInfoEnabled(true);
    } else if (
      isTechinicalTeam &&
      originatorEmail != userData.email &&
      nvStatus == "Pending Review"
    ) {
      setIsRequestInfoEnabled(true);
    } else {
      setIsRequestInfoEnabled(false);
    }

    // #region For technical form
    if (nvStatus == "Pending Review" && isTechnicalUser) {
      setIsTechnicalRequestEnabled(true);
    }

    if (
      nvStatus == "Prophet to Setup" &&
      (isNormalUser || isTechnicalUser) &&
      (loggedInUserCompany == "efcltd" || loggedInUserCompany == "fpp-ltd")
    ) {
      setIsProphetApprovalEnabled(true);
    }

    // #region For ISS team
    if (nvStatus == "ISS to Setup" && isISSU) {
      setIsISSFormEnabled(true);
    }
  }, [userData, nvStatus, originatorEmail]);

  useEffect(() => {
    const company = Cookies.get("company");
    setCompanyName(company);
    if (pageType === "update" && newVarietyData) {
      const data = newVarietyData;
      let prophetIdValue;
      if (data.company_name === "DPS MS") {
        prophetIdValue = 2;
      } else if (data.company_name === "dpsltd") {
        prophetIdValue = 1;
      } else if (data.company_name === "efcltd") {
        prophetIdValue = 3;
      } else if (data.company_name === "fpp-ltd") {
        prophetIdValue = 4;
      } else if (data.company_name === "issproduce") {
        prophetIdValue = 5;
      }
      setProphetId(prophetIdValue);
      setVarietyId(data.id);
      setRequestCompany(data.company_name);
      setWasRejected(data.was_rejected);
      setVarietyCode(data.variety_code || "");
      setVarietyDescription(data.product_description || "");
      setMasterProductCode([
        { value: data.master_product_id, label: data.master_product_name },
      ]);
      setRawMasterCode(data.master_product_code);
      setComments(data.originator_comment || "");
      setRequestNumber(data.request_no || "");
      setRequestId(data.request_id);
      setOriginatorName(data.originator_name || "");
      setOriginatorEmail(data.originator_email || "");

      setNVStatus(data.status);
      if (
        data.status == "ISS to Setup" ||
        data.status == "Prophet Setup Completed" ||
        data.status == "Prophet to Setup"
      ) {
        setApprovalStatus("approved");
      } else if (data.status == "Rejected") {
        setApprovalStatus("rejected");
      } else if (data.status == "Draft" || data.status == "Pending Review") {
        setApprovalStatus("yettoreview");
      }

      if (
        (data.status == "Pending Review" || data.status == "Draft") &&
        data.reviewer_comment != ""
      ) {
        setTechnicalReviewComment("");
      } else {
        setTechnicalReviewComment(data.reviewer_comment);
      }

      if (
        data.status == "Prophet Setup Completed" ||
        data.status == "Prophet to Setup"
      ) {
        setApprovalIssStatus("completed");
      }
      if (data.status == "Prophet Setup Completed") {
        setProphetApprovalStatus("completed");
      }

      setIssComment(data.completed_by_comment);
    } else {
      setOriginatorName(userData.name);
      setOriginatorEmail(userData.email);
      const company = Cookies.get("company");
      const ADCompanyName = Cookies.get("ADCompanyName");
      let prophetIdValue;
      if (company === "dpsltd" && ADCompanyName == "DPS MS") {
        prophetIdValue = 2;
      } else if (company === "dpsltd") {
        prophetIdValue = 1;
      } else if (company === "efcltd") {
        prophetIdValue = 3;
      } else if (company === "fpp-ltd") {
        prophetIdValue = 4;
      } else if (company === "issproduce") {
        prophetIdValue = 5;
      } else {
        prophetIdValue = 1;
      }

      setProphetId(prophetIdValue);
    }
  }, [pageType, newVarietyData]);

  useEffect(() => {
    setAlldropDown(dropdowns);
    setIsLoading(false);
  }, []);

  const handleCheckVariety = async () => {
    let serverAddress = apiConfig.serverAddress;

    try {
      const res = await fetch(`${serverAddress}products/check-variety`, {
        method: "POST",
        headers: {
          Accept: "application/json",
          "Content-Type": "application/json",
          Authorization: `Bearer ${userData?.token}`,
        },
        body: JSON.stringify({
          varietyCode: varietyCode,
          varietyName: varietyDescription,
          prophetId: prophetId,
          requestId: requestId,
        }),
      });

      if (!res.ok) {
        throw new Error(`Failed to check variety: ${res.statusText}`);
      }

      const data = await res.json();
      if (data.varietyCode || data.varietyName) {
        if (data.varietyCode) {
          setVarietyCodeExists(true);
        } else {
          setVarietyCodeExists(false);
        }
        if (data.varietyName) {
          setVarietyNameExists(true);
        } else {
          setVarietyNameExists(false);
        }
        toast.error(
          `The following Variety data already exists for your company. Please add a different one.`,
          {
            position: "top-right",
          }
        );
        setLoading(false);
        return false;
      } else {
        return true;
      }
    } catch (error) {
      console.error("Error checking variety data:", error);
      toast.error("Error occurred while checking variety data.", {
        position: "top-right",
      });
      setLoading(false);
      return false;
    }
  };

  const handleVarietyDataSelect = (varietyData) => {
    setVarietyCodeExists(false);
    setVarietyNameExists(false);
    const company = Cookies.get("company");

    // if (company === "efcltd" || company === "fpp-ltd") {

    const masterProductExists = allDropdown?.masterProductCode?.some(
      (item) =>
        item.code === varietyData.master_product_code &&
        item.label === varietyData.master_product_name
    );

    if (!masterProductExists) {
      toast.error(
        "The selected Master Product Code is not available for your company.",
        {
          position: "top-right",
        }
      );
    } else {
      setMasterProductCode([
        {
          value: varietyData.master_product_id,
          label: varietyData.master_product_name,
        },
      ]);
      setRawMasterCode(varietyData.master_product_code);
    }

    setVarietyCode(varietyData.code || "");
    setVarietyDescription(varietyData.description || "");
    setComments(varietyData.comment || "");

    setIsDropdownOpen(false);
    setFilteredItems([]);
    // } else {
    //   toast.info("Please select the variety option manually.", {
    //     position: "top-right",
    //   });
    // }
  };

  const handleSubmit = async (actionId) => {
    // openModal();
    setLoading(true);
    let company = Cookies.get("company");
    const AdCompany = Cookies.get("ADCompanyName");
    if (company == "dpsltd" && AdCompany == "DPS MS") {
      company = "DPS MS";
    }
    let formData;

    if (actionId == 11) {
      formData = {
        requestNumber: requestId || null,
        concatanatedRequestNumber: requestNumber,
        varietyId: varietyId,
        actionId,
        actionedByEmail: userData.email,
        actionedByName: userData.name,
        prophetId: prophetId,
        company: company,
        code: varietyCode,
        description: varietyDescription,
        requestCompany: requestCompany,
        originatorEmail: originatorEmail,
      };
    } else {
      if (!varietyCode) {
        setIsValidVarietyCode(false);
      }

      if (!varietyDescription) {
        setIsValidVarietyDesc(false);
      }

      if (masterProductCode.length <= 0 || masterProductCode[0] === null) {
        setIsValidMasterCode(false);
      }

      if (!varietyCode || !varietyDescription || !masterProductCode) {
        toast.error(`Please enter all the required fields `, {
          position: "center",
        });
        setLoading(false);
        return;
      }
      if (actionId != 2 && !(await handleCheckVariety())) {
        return;
      }

      if (pageType == "update") {
        formData = {
          requestNumber: requestId || null,
          concatanatedRequestNumber: requestNumber,
          varietyId: varietyId,
          code: varietyCode,
          description: varietyDescription,
          masterProductCode: rawMasterCode,
          actionId,
          actionedByEmail: userData.email,
          actionedByName: userData.name,
          comment: comments,
          prophetId: prophetId,
          isTechinicalTeam,
          technicalTeamSubmit:
            (approvalStatus == "" || approvalStatus == "yettoreview") &&
            isTechinicalTeam &&
            actionId == 2
              ? true
              : false,
          wasRejected: wasRejected,
          company: company,
        };
      } else {
        setIsSubmitted(false);
        setIsRequestInfoEnabled(false);
        setIsTechnicalRequestEnabled(false);
        setIsISSFormEnabled(false);
        setIsProphetApprovalEnabled(false);
        formData = {
          requestNumber: requestId || null,
          code: varietyCode,
          description: varietyDescription,
          masterProductCode: rawMasterCode,
          actionId,
          actionedByEmail: originatorEmail,
          actionedByName: originatorName,
          comment: comments,
          prophetId: prophetId,
          isTechinicalTeam,
          company: company,
        };
      }
    }

    try {
      const response = await fetch(
        `${serverAddress}products/add-update-varieties`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${userData.token}`,
          },
          body: JSON.stringify(formData),
        }
      );

      if (response.ok) {
        const result = await response.json();
        setRequestNumber(result.requestNumber);
        if (wasRejected && actionId == 2) {
          toast.success("New Variety request resubmitted succesfully", {
            position: "top-right",
          });
        } else if (actionId == 11) {
          toast.success(
            "New Variety request successfully marked as completed",
            {
              position: "top-right",
            }
          );
        } else {
          if (actionId != 1) {
            toast.success(result.msg || "Variety processed successfully!", {
              position: "top-right",
            });
          }
        }

        setIsSubmitted(true);
        if (actionId == 2 || actionId == 11) {
          setOnSubmit(true);
          router.replace("/products");
        }
        if (actionId == 1) {
          setLoading(false);
          setIsRequestInfoEnabled(false);
          openModal();
        }
      } else {
        console.error("Error processing variety:", response.statusText);
        toast.error(`Error: ${response.statusText}`, {
          position: "top-right",
        });
        setLoading(false);
      }
    } catch (error) {
      console.error("Error:", error);
      toast.error("An error occurred while processing the variety.", {
        position: "top-right",
      });
      setLoading(false);
    }
  };

  const handleMasterProductChange = (data) => {
    setMasterProductCode([data]);
    if (data) {
      setRawMasterCode(data.code);
      setIsValidMasterCode(true);
      const masterCode = data.label.indexOf("Organic");
    } else {
      setRawMasterCode("Click + to Add a New Master Code");
      setIsValidMasterCode(false);
    }
  };

  const handleIconClick = (dropDownType) => {
    const dropdownData = {
      master_product: {
        title: "Master Product Code",
        placeholder: "Master Product Code(5 Characters)",
        legend: "Add New Master Product Code",
        key: "masterProductCode",
        max_length: 5,
        min_length: 5,
      },
      mark_variety: {
        title: "Mark/Variety",
        placeholder: "Mark/Variety(5 Characters)",
        legend: "Add New Mark/Variety",
        key: "markVariety",
        max_length: 5,
        min_length: 5,
      },
      brand: {
        title: "Brand",
        placeholder: "Brand(5 Characters)",
        legend: "Add New Brand",
        key: "brand",
        max_length: 5,
        min_length: 1,
      },
      end_customer: {
        title: "End Customer",
        placeholder: "End Customer(5 Characters)",
        legend: "Add New End Customer",
        key: "end_customer",
        max_length: 5,
        min_length: 1,
      },
      country_of_origin: {
        title: "New Country of Origin",
        placeholder: "New Country of Origin(2 Characters)",
        legend: "Add New Country of Origin",
        key: "countryOfOrigin",
        max_length: 2,
        min_length: 2,
      },
      caliber_size: {
        title: "New Caliber Size",
        placeholder: "New Caliber Size(5 Characters)",
        legend: "Add New Caliber Size",
        key: "caliberSize",
        max_length: 5,
        min_length: 1,
      },
      variety: {
        title: "New Variety",
        placeholder: "New Variety(5 Characters)",
        legend: "Add New Variety",
        key: "variety",
        max_length: 5,
        min_length: 5,
      },
    };

    const data = dropdownData[dropDownType];

    setTitle(data.title);
    setPlaceholderText(data.placeholder);
    setLegend(data.legend);
    setDataKey(data.key);
    setMinLength(data.min_length);
    setMaxLength(data.max_length);

    if (allDropdown && allDropdown[data.key]) {
      setDropDownData(allDropdown[data.key]);
    }

    setIsDrawerOpen(true);
  };

  const filterItems = useCallback(
    debounce((searchString) => {
      if (!searchString.trim()) {
        setFilteredItems([]);
        return;
      }

      setIsDropdownLoading(true);

      fetch(
        `${serverAddress}products/get-filtered-variety-names/${searchString.trim()}/${prophetId}`,
        {
          method: "GET",
          headers: {
            Authorization: `Bearer ${userData.token}`,
            Accept: "application/json",
            "Content-Type": "application/json",
          },
        }
      )
        .then((res) => {
          if (res.status === 200) {
            return res.json();
          }
          return Promise.reject(res);
        })
        .then((data) => {
          if (data) {
            setFilteredItems(data);
          }
          setIsDropdownLoading(false);
        })
        .catch((error) => {
          console.error(error);
          setIsDropdownLoading(false);
        });
    }, 500),
    [prophetId]
  );

  const handleExit = () => {
    setLoading(true);
    setTimeout(() => {
      router.push("/products");
      setLoading(false);
    }, 2000);
  };

  const handleVarietySearchInputChange = (e) => {
    const value = e.target.value;
    setVarietySearchInput(value);
    filterItems(value);
  };

  // useEffect(() => {
  //   const handleClickOutside = (event) => {
  //     if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
  //       setIsDropdownOpen(false);
  //     }
  //   };

  //   document.addEventListener("mousedown", handleClickOutside);

  //   return () => {
  //     document.removeEventListener("mousedown", handleClickOutside);
  //   };

  // }, []);

  // useEffect(() => {
  //   const handleClickOutside = (event) => {
  //     if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
  //       setIsDropdownOpen(false);
  //     }
  //   };

  //   document.addEventListener("mousedown", handleClickOutside);

  //   return () => {
  //     document.removeEventListener("mousedown", handleClickOutside);
  //   };

  // }, []);

  const handleApprovalStatusChange = (statusType) => {
    setApprovalStatus(statusType);
    if (statusType == "rejected" && originatorEmail != userData.email) {
      setIsRequestInfoEnabled(false);
    } else {
      setIsRequestInfoEnabled(true);
    }
  };

  const handleTechnicalSubmit = async () => {
    setLoading(true);
    let company = Cookies.get("company");
    const AdCompany = Cookies.get("ADCompanyName");
    if (company == "dpsltd" && AdCompany == "DPS MS") {
      company = "DPS MS";
    }
    setTechFormSubmitted(false);
    setIsRequestInfoEnabled(false);
    setIsTechnicalRequestEnabled(false);
    setIsISSFormEnabled(false);
    setIsProphetApprovalEnabled(false);
    setIsSubmitted(true);
    // if (!(await handleCheckVariety())) {
    //   return;
    // }
    let actionId;
    switch (approvalStatus) {
      case "approved":
        actionId = 3;
        break;
      case "rejected":
        actionId = 4;
        break;
      default:
        actionId = 2;
    }
    let formData;
    if (pageType == "update") {
      formData = {
        requestNumber: requestId || null,
        concatanatedRequestNumber: requestNumber,
        varietyId: varietyId,
        code: varietyCode,
        description: varietyDescription,
        masterProductCode: rawMasterCode,
        actionId,
        actionedByEmail: userData.email,
        actionedByName: userData.name,
        originatorEmail: originatorEmail,
        comment: technicalReviewComment,
        prophetId: prophetId,
        isTechinicalTeam,
        company: company,
      };
    } else {
      if (!(await handleCheckVariety())) {
        return;
      }
      formData = {
        requestNumber: requestId || null,
        code: varietyCode,
        description: varietyDescription,
        masterProductCode: masterProductCode[0].value,
        actionId,
        actionedByEmail: originatorEmail,
        actionedByName: originatorName,
        comment: technicalReviewComment,
        prophetId: prophetId,
        isTechinicalTeam,
        company: company,
      };
    }

    try {
      const response = await fetch(
        `${serverAddress}products/add-update-varieties`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${userData.token}`,
          },
          body: JSON.stringify(formData),
        }
      );

      if (response.ok) {
        const result = await response.json();
        if (actionId == 4) {
          toast.success("Request rejected successfully.", {
            position: "top-right",
          });
        } else {
          toast.success("Request approved and forwarded to ISS for setup!", {
            position: "top-right",
          });
        }

        setTechFormSubmitted(true);
        setTimeout(() => {
          router.replace("/products");
          setLoading(false);
        }, 2000);
      } else {
        console.error("Error processing variety:", response.statusText);
        toast.error(`Error: ${response.statusText}`, {
          position: "top-right",
        });
      }
    } catch (error) {
      console.error("Error:", error);
      toast.error("An error occurred while processing the variety.", {
        position: "top-right",
      });
    } finally {
      setLoading(false);
    }
  };

  const handleIssSubmit = async () => {
    setLoading(true);
    setIssFormSubmitted(false);
    let company = Cookies.get("company");
    const AdCompany = Cookies.get("ADCompanyName");
    if (company == "dpsltd" && AdCompany == "DPS MS") {
      company = "DPS MS";
    }
    setIsRequestInfoEnabled(false);
    setIsTechnicalRequestEnabled(false);
    setIsISSFormEnabled(false);
    setIsProphetApprovalEnabled(false);
    setIsSubmitted(true);

    let actionId;
    if (approvalIssStatus === "completed" && requestCompany == "dpsltd") {
      actionId = 11;
    } else if (
      approvalIssStatus == "completed" &&
      (requestCompany == "efcltd" || requestCompany == "fpp-ltd")
    ) {
      actionId = 5;
    }
    let formData;
    if (pageType == "update") {
      formData = {
        requestNumber: requestId || null,
        varietyId: varietyId,
        code: varietyCode,
        description: varietyDescription,
        masterProductCode: rawMasterCode,
        actionId,
        actionedByEmail: userData.email,
        actionedByName: userData.name,
        comment: issComment,
        prophetId: prophetId,
        isTechinicalTeam,
        originatorEmail: originatorEmail,
        concatanatedRequestNumber: requestNumber,
        company: company,
        requestCompany: requestCompany,
      };
    } else {
      if (!(await handleCheckVariety())) {
        return;
      }
      formData = {
        requestNumber: requestId || null,
        code: varietyCode,
        description: varietyDescription,
        masterProductCode: masterProductCode[0].value,
        masterProductCodeLabel:masterProductCode[0].label,
        actionId,
        actionedByEmail: originatorEmail,
        actionedByName: originatorName,
        comment: comments,
        prophetId: prophetId,
        isTechinicalTeam,
        company: company,
      };
    }

    try {
      const response = await fetch(
        `${serverAddress}products/add-update-varieties`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${userData.token}`,
          },
          body: JSON.stringify(formData),
        }
      );

      if (response.ok) {
        const result = await response.json();
        toast.success("Request successfully marked as completed.", {
          position: "top-right",
        });
        setIssFormSubmitted(true);
        setTimeout(() => {
          router.replace("/products");
        }, 2000);
      } else {
        console.error("Error processing variety:", response.statusText);
        toast.error(`Error: ${response.statusText}`, {
          position: "top-right",
        });
      }
    } catch (error) {
      console.error("Error:", error);
      toast.error("An error occurred while processing the variety.", {
        position: "top-right",
      });
    } finally {
      setLoading(false);
    }
  };

  // const handleBlur = (e) => {
  //   if (!dropdownRef.current.contains(e.relatedTarget)) {
  //     setIsDropdownOpen(false);
  //     setFilteredItems([]);
  //   }
  // };

  const handleExtractData = async () => {
    let filteredISSExportData = [
      [
        "Variety",
        {
          "Variety Code": varietyCode,
          "Variety Description": varietyDescription,
          Active: 1,
          id: varietyId,
        },
      ],
      [
        "LimitVariety",
        {
          "Master Product Code": rawMasterCode,
          "Variety Description": varietyDescription,
          "Variety Code": varietyCode,
          Active: 1,
          id: varietyId,
        },
      ],
    ];
    let export_ISS_response = await exportExcel(
      filteredISSExportData,
      false,
      userData.token,
      requestCompany,
      userData,
      prophetId,
      userData.email,
      true,
      true,
      false,
      "",
      "",
      requestNumber,
      true
    );
  };

  return (
    <FluentProvider
      theme={webLightTheme}
      className="!bg-transparent"
      style={{ fontFamily: "poppinsregular" }}
    >
      <ToastContainer limit={1} />
      {loading ? (
        <div
          style={{
            display: "flex",
            alignItems: "center",
            justifyContent: "center",
            height: "calc(100vh - 100px)",
            width: "calc(100vw - 125px)",
          }}
        >
          <ThreeCircles
            color="#002D73"
            height={50}
            width={50}
            visible={true}
            ariaLabel="oval-loading"
            secondaryColor="#0066FF"
            strokeWidth={2}
            strokeWidthSecondary={2}
          />
        </div>
      ) : (
        <div className="m-4 flex gap-4 justify-between flex-1">
          <div
            className={`flex w-1/2 flex-col bg-white rounded-lg p-6 shadow justify-between
              ${isRequestInfoEnabled ? "" : "variety-disabled-block"}
            `}
          >
            <div className="">
              <h4 className="formtitle pb-1 border-b border-light-gray3 w-full mb-4">
                Request Information
              </h4>
              <div className="flex gap-8 w-full mb-6">
                <div className="flex flex-col w-1/2">
                  <label className="labels mb-1" htmlFor="Request Number">
                    Request Number
                  </label>
                  <input
                    type="text"
                    name="Request_number"
                    id="Request Number"
                    maxLength={50}
                    disabled={true}
                    placeholder="Will be generated on save"
                    className={`w-full py-1 px-2 2xl:px-3 2xl:py-3 border rounded-md bg-gray-100`}
                    required
                    // tabIndex={1}
                    value={requestNumber}
                  />
                </div>
                <div className="flex flex-col w-1/2">
                  <label className="labels mb-1" htmlFor="Request Number">
                    Name of Originator
                  </label>
                  <input
                    type="text"
                    name="name_of_originator"
                    id=""
                    maxLength={50}
                    disabled={true}
                    placeholder="Will be generated on submit"
                    className={`w-full py-1 px-2 2xl:px-3 2xl:py-3 border rounded-md bg-gray-100`}
                    required
                    // tabIndex={1}
                    value={originatorName}
                  />
                </div>
              </div>
              <h4 className="formtitle pb-1 border-b border-light-gray3 w-full mb-4">
                Variety Check
              </h4>
              <div className="flex w-full">
                <div className="flex flex-col w-full">
                  <label
                    disabled={!isRequestInfoEnabled}
                    className="labels mb-1"
                    htmlFor="Request Number"
                  >
                    Variety Search
                  </label>
                  <DebouncedVarietySearch
                    setIsDropdownOpen={setIsDropdownOpen}
                    varietySearchInput={varietySearchInput}
                    handleVarietySearchInputChange={
                      handleVarietySearchInputChange
                    }
                    filteredItems={filteredItems}
                    handleVarietyDataSelect={handleVarietyDataSelect}
                    dropdownRef={dropdownRef}
                    isDropdownLoading={isDropdownLoading}
                    setFilteredItems={setFilteredItems}
                    isDropdownOpen={isDropdownOpen}
                    isRequestInfoEnabled={isRequestInfoEnabled}
                  />
                </div>
                {/* <div className="w-1/2"></div> */}
              </div>
            </div>
            <div>
              <div className="flex flex-row mb-6 gap-8 items-center">
                <div className="flex flex-col w-1/2">
                  <label className="flex flex-row labels mb-1 mt-2" required>
                    Master Product Code{" "}
                    <span className="ml-1 text-red-500">*</span>
                  </label>
                  <div className="flex flex-row items-center">
                    <Select
                      options={allDropdown?.masterProductCode}
                      placeholder="Select..."
                      value={masterProductCode}
                      onChange={handleMasterProductChange}
                      isSearchable={true}
                      ref={masterProdRef}
                      openMenuOnFocus={true}
                      instanceId="selectbox"
                      className="reactSelectCustom w-full text-xs lg:text-sm"
                      classNames={{
                        control: (state) =>
                          state.is_new == true
                            ? "text-red-500"
                            : "!text-red-500",
                      }}
                      styles={customSelectStyles}
                      isClearable={true}
                      isDisabled={!isRequestInfoEnabled}
                      //onBlur={handleValidationChange}
                    />
                  </div>
                  {!isValidMasterCode ? (
                    <span className="text-red-500 text-sm">
                      Please Select a Master Product Code
                    </span>
                  ) : (
                    ""
                  )}
                </div>
                <div className="flex flex-col w-1/2">
                  <label
                    className={`w-full py-1 px-2 2xl:px-3 2xl:py-2 text-sm mt-7 xl:mt-6`}
                  >
                    Create a Raw Material request for a new Master Product Code
                  </label>
                </div>
              </div>

              <div></div>
              <h4 className="formtitle pb-1 border-b border-light-gray3 w-full mb-4">
                Variety Information
              </h4>
              <div className="flex gap-8 w-full mb-2">
                <div className="flex flex-col w-1/2">
                  <label className="labels mb-1" htmlFor="Request Number">
                    Variety Code <span className="text-red-600">*</span>
                  </label>
                  <input
                    type="text"
                    name=""
                    id=""
                    maxLength={5}
                    autoCapitalize="true"
                    className={`w-full py-1 px-2 2xl:px-3 2xl:py-3 border rounded-md `}
                    required
                    disabled={!isRequestInfoEnabled}
                    // tabIndex={1}
                    value={varietyCode}
                    onChange={(e) => {
                      const uppercasedValue = e.target.value.toUpperCase();
                      setVarietyCode(uppercasedValue);
                      setIsValidVarietyCode(true);
                      setVarietyCodeExists(false);
                    }}
                  />
                  {!isValidVarietyCode ? (
                    <span className="text-red-500 text-sm">
                      Please Enter The Variety Code
                    </span>
                  ) : varietyCodeExists ? (
                    <span className="text-red-500 text-sm">
                      Variety Code already exists for your company.
                    </span>
                  ) : (
                    ""
                  )}
                </div>
                <div className="flex flex-col w-1/2">
                  <label className="labels mb-1" htmlFor="Request Number">
                    Variety Description <span className="text-red-600">*</span>
                  </label>
                  <input
                    type="text"
                    name=""
                    id=""
                    value={varietyDescription}
                    onChange={(e) => {
                      setVarietyDescription(e.target.value);
                      setVarietyNameExists(false);
                      setIsValidVarietyDesc(true); // Reset validation
                    }}
                    maxLength={50}
                    className={`w-full py-1 px-2 2xl:px-3 2xl:py-3 border rounded-md `}
                    required
                    disabled={!isRequestInfoEnabled}
                    // tabIndex={1}
                    // value={requestNumber}
                  />
                  {!isValidVarietyDesc ? (
                    <span className="text-red-500 text-sm">
                      Please Enter The Variety Description
                    </span>
                  ) : varietyNameExists ? (
                    <span className="text-red-500 text-sm">
                      Variety Name already exists for your company.
                    </span>
                  ) : (
                    ""
                  )}
                </div>
              </div>
              <div className="flex flex-col w-full">
                <label className="labels mb-1" htmlFor="Request Number">
                  Comments
                </label>
                <textarea
                  maxLength={250}
                  disabled={
                    !isRequestInfoEnabled || userData.email != originatorEmail
                  }
                  value={comments}
                  onChange={(e) => setComments(e.target.value)}
                  className="w-full h-14 py-1 px-2 2xl:px-3 2xl:py-3 border rounded-md resize-none"
                ></textarea>
              </div>
              <div className="flex flex-row justify-between pt-5 items-center">
                <div className="flex justify-start">
                  <button
                    onClick={handleExit}
                    className="px-3 py-1 border rounded-md border-skin-primary text-skin-primary cursor-pointer"
                  >
                    Exit
                  </button>
                </div>

                <div className="flex justify-end gap-2">
                  <button
                    disabled={
                      !isRequestInfoEnabled ||
                      !isFormValid ||
                      isSubmitted ||
                      userData.email != originatorEmail
                      //  ||(nvStatus != "" && nvStatus != "Draft")
                    }
                    // onClick={() => handleSubmit(1)}
                    onClick={() => {
                      setOnSubmit(false); // Set to false for draft
                      handleSubmit(1);
                      setButtonType("Draft");
                    }}
                    className="px-3 py-1 border rounded-md border-skin-primary text-skin-primary cursor-pointer"
                  >
                    Save as Draft
                  </button>
                  <button
                    disabled={
                      !isRequestInfoEnabled ||
                      !isFormValid ||
                      isSubmitted ||
                      userData.email != originatorEmail
                    }
                    onClick={async () => {
                      if (!(await handleCheckVariety())) {
                        return;
                      }
                      setOnSubmit(true);
                      setButtonType("Submit");
                      openModal();
                    }}
                    className="ml-4 px-3 py-1 border rounded-md bg-skin-primary text-white cursor-pointer"
                  >
                    Submit
                  </button>
                </div>
              </div>
            </div>
          </div>
          <div className="flex flex-col w-1/2 ml-2 mr-12">
            <div
              className={`w-full bg-white rounded-lg p-4 mb-2 shadow 
              ${isTechnicalRequestEnabled ? "" : "variety-disabled-block"}`}
            >
              <div className="flex justify-between pb-1 border-b border-light-gray3 w-full mb-4">
                <h4 className="formtitle">Technical Team Approval</h4>
                <span className="text-sm">
                  {nvStatus == "ISS to Setup" ||
                  nvStatus == "Rejected" ||
                  nvStatus == "Prophet Setup Completed" ||
                  nvStatus == "Prophet to Setup"
                    ? `Actioned By:${
                        newVarietyData?.reviewer_email
                          ? newVarietyData?.reviewer_email
                          : "<EMAIL>"
                      }`
                    : ""}
                </span>
              </div>
              <div className="flex flex-col w-full">
                <div className="flex justify-between">
                  <label className="labels mb-1" htmlFor="Request Number">
                    Technical Team
                  </label>

                  <div className="flex gap-4 w-auto mb-4">
                    <div className="flex gap-3 items-center">
                      <input
                        type="radio"
                        name="techapproval"
                        id="approved"
                        className={`border rounded-md cursor-pointer`}
                        // disabled={!(isTechinicalTeam || isIssUser)}
                        disabled={!isTechnicalRequestEnabled}
                        checked={approvalStatus === "approved"}
                        onChange={() => handleApprovalStatusChange("approved")}
                        // tabIndex={1}
                        // value={requestNumber}
                      />
                      <label
                        htmlFor="approved"
                        className="cursor-pointer text-sm"
                      >
                        Approved
                      </label>
                    </div>

                    <div className="flex gap-3 items-center">
                      <input
                        type="radio"
                        name="techapproval"
                        id="rejected"
                        className={` border rounded-md cursor-pointer`}
                        checked={
                          approvalStatus === "rejected" ||
                          nvStatus == "Rejected"
                        }
                        onChange={() => handleApprovalStatusChange("rejected")}
                        //disabled={!(isTechinicalTeam || isIssUser)}
                        disabled={!isTechnicalRequestEnabled}
                      />
                      <label
                        htmlFor="rejected"
                        className="cursor-pointer text-sm"
                      >
                        Rejected
                      </label>
                    </div>
                    <div className="flex gap-3 items-center cursor-pointer">
                      <input
                        type="radio"
                        name="techapproval"
                        id="yettoreview"
                        className={`border rounded-md cursor-pointer`}
                        checked={approvalStatus === "yettoreview"}
                        onChange={() =>
                          handleApprovalStatusChange("yettoreview")
                        }
                        //disabled={!(isTechinicalTeam || isIssUser)}
                        disabled={!isTechnicalRequestEnabled}
                      />
                      <label
                        htmlFor="yettoreview"
                        className="flex w-full cursor-pointer text-sm"
                      >
                        Yet to Review
                      </label>
                    </div>
                  </div>
                </div>
                <div className="flex flex-col w-full">
                  <label className="labels mb-1" htmlFor="Request Number">
                    Review Comments
                    <span className="text-red-500">
                      {approvalStatus == "rejected" ? "*" : ""}
                    </span>
                  </label>
                  <textarea
                    maxLength={500}
                    value={technicalReviewComment}
                    onChange={(e) => setTechnicalReviewComment(e.target.value)}
                    disabled={!isTechnicalRequestEnabled}
                    className="w-full h-14 py-1 px-2 2xl:px-3 2xl:py-3 border rounded-md resize-none"
                  ></textarea>
                </div>
                <div className="flex w-full pt-3 justify-end">
                  <button
                    onClick={() => handleTechnicalSubmit()}
                    disabled={
                      nvStatus == "ISS to Setup" ||
                      nvStatus == "Setup Completed" ||
                      nvStatus == "Prophet to Setup" ||
                      nvStatus == "Prophet Setup Completed" ||
                      !isTechinicalTeam ||
                      isIssUser ||
                      techFormSubmitted ||
                      nvStatus === "Rejected" ||
                      approvalStatus === "yettoreview" ||
                      (approvalStatus == "rejected" &&
                        (technicalReviewComment == "" ||
                          technicalReviewComment == null))
                    }
                    className="ml-2 px-3 py-1 p-[6px] border rounded-md bg-skin-primary text-white cursor-pointer"
                  >
                    Save
                  </button>
                </div>
              </div>
            </div>
            {/* #region ISS review form */}
            <div
              className={`w-full bg-white rounded-lg p-4 mb-2 ${
                isISSFormEnabled ? "" : "variety-disabled-block"
              }`}
            >
              <div className="flex justify-between pb-1 border-b border-light-gray3 w-full mb-4">
                <h4 className="formtitle">ISS Team</h4>
                <span className="text-sm">
                  {nvStatus == "Prophet Setup Completed" ||
                  nvStatus == "Prophet to Setup"
                    ? `Actioned By:${
                        newVarietyData?.completed_by_email
                          ? newVarietyData?.completed_by_email
                          : "<EMAIL>"
                      }`
                    : ""}
                </span>
              </div>
              <div className="flex flex-col w-full mb-4">
                <div className="flex justify-between">
                  <label className="labels mb-1" htmlFor="Request Number">
                    ISS Setup
                  </label>

                  <div className="flex gap-4 w-auto mb-4">
                    <div className="flex gap-3 items-center">
                      <input
                        type="radio"
                        name="isssetup"
                        id="completed"
                        className={`border rounded-md cursor-pointer`}
                        disabled={!isISSFormEnabled}
                        checked={approvalIssStatus === "completed"}
                        onChange={() => setApprovalIssStatus("completed")}
                      />
                      <label
                        htmlFor="completed"
                        className="cursor-pointer text-sm"
                      >
                        Completed
                      </label>
                    </div>

                    <div className="flex gap-3 items-center">
                      <input
                        type="radio"
                        name="isssetup"
                        id="isstosetup"
                        className={`border rounded-md cursor-pointer`}
                        checked={approvalIssStatus === "isstosetup"}
                        onChange={() => setApprovalIssStatus("isstosetup")}
                        disabled={!isISSFormEnabled}
                      />
                      <label
                        htmlFor="isstosetup"
                        className="cursor-pointer text-sm"
                      >
                        ISS to Setup
                      </label>
                    </div>
                  </div>
                </div>

                <div className="flex flex-col w-full">
                  <label className="labels mb-1" htmlFor="Request Number">
                    Review Comments
                  </label>
                  <textarea
                    maxLength={500}
                    value={issComment}
                    onChange={(e) => setIssComment(e.target.value)}
                    disabled={!isISSFormEnabled}
                    className="w-full h-14 py-1 px-2 2xl:px-3 2xl:py-3 border rounded-md resize"
                  ></textarea>
                </div>

                <div className="flex flex-row justify-between pt-5 items-center">
                  <div className="flex justify-start">
                    <button
                      onClick={() => handleExtractData()}
                      disabled={
                        !isIssUser &&
                        companyName != "efcltd" &&
                        companyName != "fpp-ltd"
                      }
                      className="ml-2 px-3 py-1 p-[6px] border rounded-md border-skin-primary text-skin-primary cursor-pointer"
                    >
                      Extract
                    </button>
                  </div>
                  <div className="flex justify-end">
                    <button
                      onClick={() => handleIssSubmit()}
                      disabled={
                        !isISSFormEnabled || approvalIssStatus === "isstosetup"
                      }
                      className="ml-2 px-3 py-1 p-[6px] border rounded-md bg-skin-primary text-white cursor-pointer"
                    >
                      Save
                    </button>
                  </div>
                </div>
              </div>
            </div>
            {/* #region Prophet approval form */}
            {(requestCompany == "efcltd" || requestCompany == "fpp-ltd") && (
              <div
                className={`w-full bg-white rounded-lg p-4 ${
                  isProphetApprovalEnabled ? "" : "variety-disabled-block "
                }`}
              >
                <div className="flex justify-between pb-1 border-b border-light-gray3 w-full mb-2">
                  <h4 className="formtitle">Prophet Approval</h4>
                  <span className="text-sm">
                    {nvStatus == "ISS setup completed"
                      ? `Actioned By:${
                          newVarietyData?.completed_by_email
                            ? newVarietyData?.completed_by_email
                            : ""
                        }`
                      : ""}
                  </span>
                </div>
                <div className="flex w-full mb-2 justify-between">
                  <div className="flex w-1/2 justify-between items-center ">
                    <label className="labels" htmlFor="Request Number">
                      Setup completed in prophet?
                    </label>

                    <div className="flex gap-4 w-auto">
                      <div className="flex gap-3 items-center">
                        <input
                          type="radio"
                          name="prophetSetup"
                          id="prophetSetupCompleted"
                          className={`border rounded-md cursor-pointer`}
                          disabled={!isProphetApprovalEnabled}
                          checked={prophetApprovalStatus === "completed"}
                          onChange={() => setProphetApprovalStatus("completed")}
                        />
                        <label
                          htmlFor="prophetSetupCompleted"
                          className="cursor-pointer text-sm"
                        >
                          Yes
                        </label>
                      </div>

                      <div className="flex gap-3 items-center">
                        <input
                          type="radio"
                          name="prophetSetup"
                          id="prophetSetupPending"
                          className={`border rounded-md cursor-pointer`}
                          checked={prophetApprovalStatus === "notcomplete"}
                          onChange={() =>
                            setProphetApprovalStatus("notcomplete")
                          }
                          disabled={!isProphetApprovalEnabled}
                        />
                        <label
                          htmlFor="prophetSetupPending"
                          className="cursor-pointer text-sm"
                        >
                          No
                        </label>
                      </div>
                    </div>
                  </div>
                  <button
                    onClick={() => handleSubmit(11)}
                    disabled={
                      !isProphetApprovalEnabled ||
                      prophetApprovalStatus === "notcomplete"
                    }
                    className="ml-2 px-3 py-1 p-[6px] border rounded-md bg-skin-primary text-white cursor-pointer"
                  >
                    Save
                  </button>
                </div>
              </div>
            )}
          </div>
          <Transition appear show={isOpen} as={Fragment}>
            <Dialog as="div" className="relative z-10" onClose={closeModal}>
              <Transition.Child
                as={Fragment}
                enter="ease-out duration-300"
                enterFrom="opacity-0"
                enterTo="opacity-100"
                leave="ease-in duration-200"
                leaveFrom="opacity-100"
                leaveTo="opacity-0"
              >
                <div className="fixed inset-0 bg-black bg-opacity-25 backdrop-blur-sm" />
              </Transition.Child>

              <div className="fixed inset-0 overflow-y-auto">
                <div className="flex items-center justify-center min-h-full p-4 text-center">
                  <Transition.Child
                    as={Fragment}
                    enter="ease-out duration-300"
                    enterFrom="opacity-0 scale-95"
                    enterTo="opacity-100 scale-100"
                    leave="ease-in duration-200"
                    leaveFrom="opacity-100 scale-100"
                    leaveTo="opacity-0 scale-95"
                  >
                    <Dialog.Panel className="w-[45%] transform overflow-hidden rounded-xl bg-white text-left align-middle shadow-xl transition-all">
                      <div className="relative bg-white rounded-lg shadow">
                        <div className="flex items-start justify-between p-8 rounded-t">
                          <h3 className="flex flex-row text-xl font-semibold text-gray-900 items-center">
                            {onSubmit ? "Confirm Submission" : "Draft Saved"}
                          </h3>
                          <button
                            onClick={closeModal}
                            type="button"
                            className="text-black bg-transparent hover:bg-gray-200 hover:text-gray-900 rounded-lg text-xl w-8 h-8 ml-auto inline-flex justify-center items-center"
                          >
                            <FontAwesomeIcon
                              icon={faXmark}
                              className="text-skin-primary"
                            />
                          </button>
                        </div>
                        <div className="p-8 py-0 space-y-6">
                          <p className="text-base xl:text-md 2xl:text-lg leading-relaxed mt-2">
                            {onSubmit
                              ? wasRejected
                                ? "Are you sure you want to resubmit this request?"
                                : "Are you sure you want to submit this request?"
                              : "Your request has been saved as a draft. You can return later to continue."}
                          </p>
                        </div>
                        <div className="flex items-end p-6 justify-end gap-3">
                          {buttonType === "Submit" && (
                            <button
                              onClick={closeModal}
                              data-modal-hide="default-modal"
                              type="button"
                              className="buttonText border border-skin-primary text-skin-primary focus:ring-4 focus:outline-none focus:ring-blue-300 font-medium rounded-md text-l px-6 py-2 text-center"
                            >
                              No
                            </button>
                          )}
                          <button
                            onClick={() => {
                              if (onSubmit) {
                                handleSubmit(2);
                              } else if (!onSubmit) {
                                router.replace("/products");
                              }
                              closeModal();
                            }}
                            data-modal-hide="default-modal"
                            type="button"
                            className="buttonText text-white bg-skin-primary hover:bg-skin-primary focus:ring-4 focus:outline-none focus:ring-blue-300 font-medium rounded-md text-md px-6 py-2 text-center"
                          >
                            {onSubmit ? "Yes" : "Ok"}
                          </button>
                        </div>
                      </div>
                    </Dialog.Panel>
                  </Transition.Child>
                </div>
              </div>
            </Dialog>
          </Transition>
        </div>
      )}
    </FluentProvider>
  );
};

export default NewVarietyRequest;
