"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/raw-material-request/add",{

/***/ "./components/DrawerComponent.js":
/*!***************************************!*\
  !*** ./components/DrawerComponent.js ***!
  \***************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DrawerComponent: function() { return /* binding */ DrawerComponent; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _fortawesome_react_fontawesome__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @fortawesome/react-fontawesome */ \"./node_modules/@fortawesome/react-fontawesome/index.es.js\");\n/* harmony import */ var _fortawesome_free_solid_svg_icons__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @fortawesome/free-solid-svg-icons */ \"./node_modules/@fortawesome/free-solid-svg-icons/index.mjs\");\n/* harmony import */ var _fluentui_react_components__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @fluentui/react-components */ \"./node_modules/@fluentui/react-components/lib/index.js\");\n/* harmony import */ var _fluentui_react_icons__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @fluentui/react-icons */ \"./node_modules/@fluentui/react-icons/lib/index.js\");\n/* harmony import */ var ag_grid_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ag-grid-react */ \"./node_modules/ag-grid-react/lib/main.js\");\n/* harmony import */ var ag_grid_react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(ag_grid_react__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _services_apiConfig__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/services/apiConfig */ \"./services/apiConfig.js\");\n/* harmony import */ var react_toastify__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! react-toastify */ \"./node_modules/react-toastify/dist/react-toastify.esm.mjs\");\n/* harmony import */ var react_toastify_dist_ReactToastify_css__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! react-toastify/dist/ReactToastify.css */ \"./node_modules/react-toastify/dist/ReactToastify.css\");\n/* harmony import */ var react_toastify_dist_ReactToastify_css__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(react_toastify_dist_ReactToastify_css__WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var _utils_renderer_productActionRenderer__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/utils/renderer/productActionRenderer */ \"./utils/renderer/productActionRenderer.js\");\n/* harmony import */ var _utils_renderer_productReferenceRenderer__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/utils/renderer/productReferenceRenderer */ \"./utils/renderer/productReferenceRenderer.js\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n // Import useRef and useCallback\n\n\n\n\n\nconst useStyles = (0,_fluentui_react_components__WEBPACK_IMPORTED_MODULE_9__.makeStyles)({\n    root: {\n        ..._fluentui_react_components__WEBPACK_IMPORTED_MODULE_9__.shorthands.border(\"2px\", \"solid\", \"#ccc\"),\n        ..._fluentui_react_components__WEBPACK_IMPORTED_MODULE_9__.shorthands.overflow(\"hidden\"),\n        display: \"flex\",\n        height: \"480px\",\n        backgroundColor: \"#fff\"\n    },\n    content: {\n        ..._fluentui_react_components__WEBPACK_IMPORTED_MODULE_9__.shorthands.flex(1),\n        ..._fluentui_react_components__WEBPACK_IMPORTED_MODULE_9__.shorthands.padding(\"16px\"),\n        display: \"grid\",\n        justifyContent: \"flex-start\",\n        alignItems: \"flex-start\",\n        gridRowGap: _fluentui_react_components__WEBPACK_IMPORTED_MODULE_9__.tokens.spacingVerticalXXL,\n        gridAutoRows: \"max-content\"\n    },\n    field: {\n        display: \"grid\",\n        gridRowGap: _fluentui_react_components__WEBPACK_IMPORTED_MODULE_9__.tokens.spacingVerticalS\n    }\n});\nconst DrawerComponent = (param)=>{\n    let { isDrawerOpen, setIsDrawerOpen, title, dropdownData, placeholderText, legend, max_length, min_length, dataKey, onNewDropdownData, username, useremail, userData, prophetId } = param;\n    _s();\n    const [type, setType] = react__WEBPACK_IMPORTED_MODULE_1__.useState(\"overlay\");\n    const [description, setDescription] = react__WEBPACK_IMPORTED_MODULE_1__.useState(\"\");\n    const [removedRows, setRemovedRows] = react__WEBPACK_IMPORTED_MODULE_1__.useState(\"\");\n    const [value, setValue] = react__WEBPACK_IMPORTED_MODULE_1__.useState(\"\");\n    const [isButtonDisabled, setIsButtonDisabled] = react__WEBPACK_IMPORTED_MODULE_1__.useState(true);\n    const [isUpdateMode, setIsUpdateMode] = react__WEBPACK_IMPORTED_MODULE_1__.useState(false);\n    const [code, setCode] = react__WEBPACK_IMPORTED_MODULE_1__.useState(\"\");\n    const [rowData, setRowData] = react__WEBPACK_IMPORTED_MODULE_1__.useState([]);\n    const [addNewValue, setAddNewValue] = react__WEBPACK_IMPORTED_MODULE_1__.useState(false);\n    //const [keyy, setKey] =  React.useState(dataKey ? dataKey : \"\")\n    const serverAddress = _services_apiConfig__WEBPACK_IMPORTED_MODULE_4__.apiConfig.serverAddress;\n    const [isValidDescription, setisValidDescription] = react__WEBPACK_IMPORTED_MODULE_1__.useState(true);\n    const [isValidCode, setisValidCode] = react__WEBPACK_IMPORTED_MODULE_1__.useState(true);\n    // Define gridRef using useRef\n    const gridRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Define onFilterTextBoxChanged function using useCallback\n    const onFilterTextBoxChanged = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        gridRef.current.api.setQuickFilter(document.getElementById(\"filter-text-box\").value);\n    // setGridApi(params.api);\n    }, []);\n    const formatName = (params)=>{\n        // Check condition and return formatted value\n        // console.log(typeof params.data.is_new);\n        if (params.data.is_new === true) {\n            // console.log(\"inside here\");\n            // console.log(params.data.name);\n            return \"*\".concat(params.data.label);\n        }\n    //return null;\n    };\n    const formatCode = (params)=>{\n        // Check condition and return formatted value\n        if (params.data.is_new === true) {\n            // console.log(params.data.code);\n            return \"*\".concat(params.data.code);\n        }\n    //return null;\n    };\n    // Define defaultColDef and columnDefs using useMemo\n    const defaultColDef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>({\n            sortable: true,\n            filter: false,\n            resizable: true,\n            flex: 1,\n            suppressMenu: false\n        }), []);\n    const trimInputText = (input)=>{\n        return input.trim();\n    };\n    react__WEBPACK_IMPORTED_MODULE_1__.useEffect(()=>{\n        if (!addNewValue) {\n            setRowData(dropdownData);\n        }\n    }, []);\n    const columnDefs = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>[\n            {\n                headerName: \"Name\",\n                field: \"label\",\n                flex: \"3%\",\n                headerClass: \"header-with-border\",\n                cellStyle: {\n                    display: \"flex\"\n                },\n                valueFormatter: formatName,\n                cellStyle: function(params) {\n                    if (params.data.is_new === true || params.data.is_new === 1) {\n                        //Here you can check the value and based on that you can change the color\n                        return {\n                            color: \"red\"\n                        };\n                    } else {\n                        return null;\n                    }\n                }\n            },\n            {\n                headerName: \"Code\",\n                field: \"code\",\n                flex: \"2%\",\n                headerClass: \"header-with-border\",\n                cellStyle: {\n                    display: \"flex\"\n                },\n                valueFormatter: formatCode,\n                cellStyle: function(params) {\n                    if (params.data.is_new === true || params.data.is_new === 1) {\n                        //Here you can check the value and based on that you can change the color\n                        return {\n                            color: \"red\"\n                        };\n                    } else {\n                        return null;\n                    }\n                }\n            },\n            {\n                headerName: \"Is New\",\n                field: \"is_new\",\n                hide: true\n            },\n            {\n                headerName: \"Action\",\n                field: \"\",\n                headerClass: \"header-with-border\",\n                cellRenderer: (params)=>(0,_utils_renderer_productReferenceRenderer__WEBPACK_IMPORTED_MODULE_8__[\"default\"])(params, userData, isUpdateMode),\n                // cellRenderer: () => addRow(),\n                flex: \"2%\",\n                cellStyle: ()=>({\n                        display: \"flex\",\n                        justifyContent: \"center\",\n                        paddingRight: \"20px\"\n                    }),\n                sortable: false,\n                cellRendererParams: {\n                    setCode: setCode,\n                    setDescription: setDescription,\n                    setValue: setValue,\n                    setIsUpdateMode: setIsUpdateMode,\n                    setIsButtonDisabled: setIsButtonDisabled,\n                    isUpdateMode: isUpdateMode\n                },\n                onCellClicked: ()=>{\n                    setisValidDescription(true);\n                    setisValidCode(true);\n                // addRow();\n                }\n            }\n        ], []);\n    // console.log(\"isUpdateMode\",isUpdateMode);\n    // console.log('description: ',description,'\\ncode: ', code,'\\nvalue: ', value)\n    const handelupdate = ()=>{\n        const checkDescription = rowData.find((desc)=>desc.label == description && desc.value != value);\n        // console.log(code);\n        const checkProduct = rowData.find((product)=>product.code == code && product.value != value);\n        if (checkDescription) {\n            setisValidDescription(false);\n        // console.log(\"yes\");\n        } else {\n            setisValidDescription(true);\n        // console.log(\"no\");\n        }\n        if (checkProduct) {\n            setisValidCode(false);\n        // console.log(\"yes yes\");\n        } else {\n            setisValidCode(true);\n        // console.log(\"no no\");\n        }\n        if (!checkDescription && !checkProduct) {\n            const saveData = {\n                value: value,\n                description: description,\n                code: code,\n                is_new: true,\n                tableName: dataKey,\n                username: username,\n                useremail: useremail,\n                prophetId: prophetId\n            };\n            try {\n                fetch(\"\".concat(serverAddress, \"products/update-all-dropdown-value\"), {\n                    method: \"PUT\",\n                    headers: {\n                        \"Content-Type\": \"application/json\",\n                        Authorization: \"Bearer \".concat(userData.token)\n                    },\n                    body: JSON.stringify(saveData)\n                }).then((res)=>{\n                    if (res.status === 200) {\n                        return res.json();\n                    } else {\n                        react_toastify__WEBPACK_IMPORTED_MODULE_5__.toast.error(\"Failed to save raw material.\");\n                    //setLoading(false);\n                    }\n                }).then((json)=>{\n                    // console.log(json);\n                    if (dataKey == \"masterProductCode\") {\n                        onNewDropdownData(dataKey, json.masterProductCode);\n                        setRowData(json.masterProductCode);\n                        react_toastify__WEBPACK_IMPORTED_MODULE_5__.toast.success(\"Updated New Master Code.\", {\n                            position: \"top-left\"\n                        });\n                    } else if (dataKey == \"markVariety\") {\n                        setRowData(json.markVariety);\n                        onNewDropdownData(dataKey, json.markVariety);\n                        react_toastify__WEBPACK_IMPORTED_MODULE_5__.toast.success(\"Updated New Mark Variety.\", {\n                            position: \"top-left\"\n                        });\n                    } else if (dataKey == \"brand\") {\n                        setRowData(json.brand);\n                        onNewDropdownData(dataKey, json.brand);\n                        react_toastify__WEBPACK_IMPORTED_MODULE_5__.toast.success(\"Updated New Brand.\", {\n                            position: \"top-left\"\n                        });\n                    } else if (dataKey == \"end_customer\") {\n                        setRowData(json.endCustomer);\n                        onNewDropdownData(dataKey, json.endCustomer);\n                        react_toastify__WEBPACK_IMPORTED_MODULE_5__.toast.success(\"Updated New End Customer.\", {\n                            position: \"top-left\"\n                        });\n                    } else if (dataKey == \"countryOfOrigin\") {\n                        setRowData(json.countryOfOrigin);\n                        onNewDropdownData(dataKey, json.countryOfOrigin);\n                        react_toastify__WEBPACK_IMPORTED_MODULE_5__.toast.success(\"Updated New Country of Origin.\", {\n                            position: \"top-left\"\n                        });\n                    } else if (dataKey == \"caliberSize\") {\n                        setRowData(json.caliberSize);\n                        onNewDropdownData(dataKey, json.caliberSize);\n                        react_toastify__WEBPACK_IMPORTED_MODULE_5__.toast.success(\"Updated New Caliber Size.\", {\n                            position: \"top-left\"\n                        });\n                    } else if (dataKey == \"variety\") {\n                        setRowData(json.variety);\n                        onNewDropdownData(dataKey, json.variety);\n                        react_toastify__WEBPACK_IMPORTED_MODULE_5__.toast.success(\"Updated New Variety.\", {\n                            position: \"top-left\"\n                        });\n                    } else if (dataKey == \"newOuterBoxType\") {\n                        setRowData(json.newOuterBoxType);\n                        onNewDropdownData(dataKey, json.newOuterBoxType);\n                        react_toastify__WEBPACK_IMPORTED_MODULE_5__.toast.success(\"Updated New Box Type.\", {\n                            position: \"top-left\"\n                        });\n                    }\n                    setDescription(\"\");\n                    setCode(\"\");\n                    setIsButtonDisabled(true);\n                    setIsUpdateMode(false);\n                    setAddNewValue(true);\n                //setIsDrawerOpen(false);\n                //alert(\"after new add\")\n                });\n            } catch (error) {\n                // toast.error(\"Failed to save reference code.\", {\n                //   position: \"top-left\",\n                // });\n                console.error(\"Failed to save reference code.\", error);\n            //setLoading(false);\n            }\n        } else {\n            // toast.error(\"Product code or description already exist.\", {\n            //   position: \"top-left\",\n            // });\n            console.error(\"Failed to save reference code.\");\n        }\n    };\n    const handleAdd = ()=>{\n        const checkDescription = rowData.find((desc)=>desc.label == description);\n        const checkProduct = rowData.find((product)=>product.code == code);\n        if (checkDescription) {\n            setisValidDescription(false);\n        } else {\n            setisValidDescription(true);\n        }\n        if (checkProduct) {\n            setisValidCode(false);\n        } else {\n            setisValidCode(true);\n        }\n        // console.log('checkDescription: ',checkDescription,'\\n checkProduct: ', checkProduct)\n        if (!checkDescription && !checkProduct) {\n            const saveData = {\n                description: description,\n                code: code,\n                is_new: true,\n                tableName: dataKey,\n                username: username,\n                useremail: useremail,\n                prophetId: prophetId\n            };\n            try {\n                fetch(\"\".concat(serverAddress, \"products/add-all-dropdown-value\"), {\n                    method: \"POST\",\n                    headers: {\n                        \"Content-Type\": \"application/json\",\n                        Authorization: \"Bearer \".concat(userData.token)\n                    },\n                    body: JSON.stringify(saveData)\n                }).then((res)=>{\n                    if (res.status === 200) {\n                        return res.json();\n                    } else {\n                        react_toastify__WEBPACK_IMPORTED_MODULE_5__.toast.error(\"Failed to save raw material.\");\n                    //setLoading(false);\n                    }\n                }).then((json)=>{\n                    // console.log(json);\n                    if (dataKey == \"masterProductCode\") {\n                        onNewDropdownData(dataKey, json.masterProductCode);\n                        setRowData(json.masterProductCode);\n                        react_toastify__WEBPACK_IMPORTED_MODULE_5__.toast.success(\"Added New Master Code.\", {\n                            position: \"top-left\"\n                        });\n                        setDescription(\"\");\n                        setCode(\"\");\n                        setIsButtonDisabled(true);\n                    } else if (dataKey == \"markVariety\") {\n                        setRowData(json.markVariety);\n                        onNewDropdownData(dataKey, json.markVariety);\n                        react_toastify__WEBPACK_IMPORTED_MODULE_5__.toast.success(\"Added New Mark Variety.\", {\n                            position: \"top-left\"\n                        });\n                        setDescription(\"\");\n                        setCode(\"\");\n                        setIsButtonDisabled(true);\n                    } else if (dataKey == \"brand\") {\n                        setRowData(json.brand);\n                        onNewDropdownData(dataKey, json.brand);\n                        react_toastify__WEBPACK_IMPORTED_MODULE_5__.toast.success(\"Added New Brand.\", {\n                            position: \"top-left\"\n                        });\n                        setDescription(\"\");\n                        setCode(\"\");\n                        setIsButtonDisabled(true);\n                    } else if (dataKey == \"end_customer\") {\n                        setRowData(json.endCustomer);\n                        onNewDropdownData(dataKey, json.endCustomer);\n                        react_toastify__WEBPACK_IMPORTED_MODULE_5__.toast.success(\"Added New End Customer.\", {\n                            position: \"top-left\"\n                        });\n                        setDescription(\"\");\n                        setCode(\"\");\n                        setIsButtonDisabled(true);\n                    } else if (dataKey == \"countryOfOrigin\") {\n                        setRowData(json.countryOfOrigin);\n                        onNewDropdownData(dataKey, json.countryOfOrigin);\n                        react_toastify__WEBPACK_IMPORTED_MODULE_5__.toast.success(\"Added New Country of Origin.\", {\n                            position: \"top-left\"\n                        });\n                        setDescription(\"\");\n                        setCode(\"\");\n                        setIsButtonDisabled(true);\n                    } else if (dataKey == \"caliberSize\") {\n                        setRowData(json.caliberSize);\n                        onNewDropdownData(dataKey, json.caliberSize);\n                        react_toastify__WEBPACK_IMPORTED_MODULE_5__.toast.success(\"Added New Caliber Size.\", {\n                            position: \"top-left\"\n                        });\n                        setDescription(\"\");\n                        setCode(\"\");\n                        setIsButtonDisabled(true);\n                    } else if (dataKey == \"variety\") {\n                        setRowData(json.variety);\n                        onNewDropdownData(dataKey, json.variety);\n                        react_toastify__WEBPACK_IMPORTED_MODULE_5__.toast.success(\"Added New Variety.\", {\n                            position: \"top-left\"\n                        });\n                        setDescription(\"\");\n                        setCode(\"\");\n                        setIsButtonDisabled(true);\n                    } else if (dataKey == \"newOuterBoxType\") {\n                        setRowData(json.newOuterBoxType);\n                        onNewDropdownData(dataKey, json.newOuterBoxType);\n                        react_toastify__WEBPACK_IMPORTED_MODULE_5__.toast.success(\"Added New Box Type.\", {\n                            position: \"top-left\"\n                        });\n                        setDescription(\"\");\n                        setCode(\"\");\n                        setIsButtonDisabled(true);\n                    }\n                    setAddNewValue(true);\n                });\n            } catch (error) {\n                console.error(\"Failed to save reference code.\", error);\n            }\n        } else {\n            console.error(\"Failed to save reference code.\");\n        }\n    };\n    const getRowStyle = (params)=>{\n        if (params.data.is_new === true) {\n            // Apply custom styling for rows where is_new is true\n            return {\n                color: \"red !important\"\n            }; // Example background color\n        }\n        return null;\n    };\n    return(// <div className={styles.root}>\n    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_toastify__WEBPACK_IMPORTED_MODULE_5__.ToastContainer, {\n                limit: 1\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\DrawerComponent.js\",\n                lineNumber: 460,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fluentui_react_components__WEBPACK_IMPORTED_MODULE_9__.Drawer, {\n                    type: type,\n                    separator: true,\n                    open: isDrawerOpen,\n                    position: \"end\",\n                    className: \"!bg-white !w-[450px] p-3 px-5 !shadow-lg !border-0\",\n                    onOpenChange: (_, param)=>{\n                        let { open } = param;\n                        return setIsDrawerOpen(open);\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fluentui_react_components__WEBPACK_IMPORTED_MODULE_9__.DrawerHeader, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fluentui_react_components__WEBPACK_IMPORTED_MODULE_9__.DrawerHeaderTitle, {\n                                action: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fluentui_react_components__WEBPACK_IMPORTED_MODULE_9__.Button, {\n                                    appearance: \"subtle\",\n                                    \"aria-label\": \"Close\",\n                                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fluentui_react_icons__WEBPACK_IMPORTED_MODULE_10__.Dismiss24Regular, {}, void 0, false, void 0, void 0),\n                                    onClick: ()=>setIsDrawerOpen(false)\n                                }, void 0, false, void 0, void 0),\n                                className: \"font-bold\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"font-bold\",\n                                    children: title\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\DrawerComponent.js\",\n                                    lineNumber: 482,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\DrawerComponent.js\",\n                                lineNumber: 471,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\DrawerComponent.js\",\n                            lineNumber: 470,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fluentui_react_components__WEBPACK_IMPORTED_MODULE_9__.DrawerBody, {\n                            className: \"!max-h-full !overflow-hidden\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"border rounded-md relative mt-3 mb-3 \",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"absolute -top-3 left-5 bg-white z-50 w-auto inline px-3\",\n                                                style: {\n                                                    opacity: !isUpdateMode ? 1 : 0,\n                                                    transform: !isUpdateMode ? \"scale(1)\" : \"scale(0.9)\",\n                                                    transition: \"opacity 0.1s ease, transform 0.1s ease\",\n                                                    pointerEvents: !isUpdateMode ? \"auto\" : \"none\"\n                                                },\n                                                children: legend\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\DrawerComponent.js\",\n                                                lineNumber: 489,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                onClick: ()=>{\n                                                    setIsUpdateMode(false);\n                                                    setIsButtonDisabled(true);\n                                                    setDescription(\"\");\n                                                    setCode(\"\");\n                                                    // setRemovedRows(\"\");\n                                                    setValue(\"\");\n                                                    setisValidCode(true);\n                                                    setisValidDescription(true);\n                                                // addRow();\n                                                // console.log(\"removedRows\",removedRows);\n                                                },\n                                                style: {\n                                                    opacity: isUpdateMode ? 1 : 0,\n                                                    transform: isUpdateMode ? \"scale(1)\" : \"scale(0.9)\",\n                                                    transition: \"opacity 0.1s ease, transform 0.1s ease\",\n                                                    pointerEvents: isUpdateMode ? \"auto\" : \"none\"\n                                                },\n                                                className: \"absolute flex items-center justify-center border rounded-md border-skin-primary -top-3 right-5 text-skin-primary bg-white z-50 cursor-pointer shadow-sm hover:bg-gray-50 transition-all duration-300 ease-in-out\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fortawesome_react_fontawesome__WEBPACK_IMPORTED_MODULE_2__.FontAwesomeIcon, {\n                                                        icon: _fortawesome_free_solid_svg_icons__WEBPACK_IMPORTED_MODULE_11__.faPlus,\n                                                        className: \"px-1 py-1 text-skin-primary cursor-pointer rotate-45 transition-all duration-300 ease-in-out transform\",\n                                                        title: \"Go back to add new \".concat(title)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\DrawerComponent.js\",\n                                                        lineNumber: 522,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"mr-[5px]\",\n                                                        children: \"Cancel\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\DrawerComponent.js\",\n                                                        lineNumber: 527,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\DrawerComponent.js\",\n                                                lineNumber: 501,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"hidden\",\n                                                name: \"type\",\n                                                value: \"product\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\DrawerComponent.js\",\n                                                lineNumber: 530,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex flex-col w-full p-4 pt-5 overflow-hidden\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        id: \"description\",\n                                                        name: \"description\",\n                                                        maxLength: 50,\n                                                        className: \"w-full py-1 px-2 2xl:px-3 2xl:py-3 border rounded-md\",\n                                                        placeholder: \"Description\",\n                                                        onChange: (e)=>{\n                                                            setDescription(e.target.value);\n                                                            setisValidDescription(true);\n                                                            setIsButtonDisabled(e.target.value === \"\" || code === \"\");\n                                                        },\n                                                        onBlur: (e)=>{\n                                                            const trimmedValue = trimInputText(e.target.value);\n                                                            setDescription(trimmedValue);\n                                                            setisValidDescription(true);\n                                                            setIsButtonDisabled(trimmedValue === \"\" || code === \"\");\n                                                        },\n                                                        // style={{ textTransform: \"capitalize\" }}\n                                                        value: description\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\DrawerComponent.js\",\n                                                        lineNumber: 532,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    !isValidDescription && // ?\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-red-500 text-xs mt-1 ml-1\",\n                                                        children: \"Description Exists Please Enter Different Description\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\DrawerComponent.js\",\n                                                        lineNumber: 555,\n                                                        columnNumber: 23\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex flex-row mt-2 transition-all duration-300 ease-in-out\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex flex-col w-full \",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                        id: \"addNew\",\n                                                                        name: \"add new\",\n                                                                        className: \"w-full py-1 px-2 2xl:px-3 2xl:py-3 border rounded-md \",\n                                                                        onChange: (e)=>{\n                                                                            // console.log(\"max_length\",min_length - 1, e.target.value.length);\n                                                                            // console.log(\"hi:\");\n                                                                            if (e.target.value.length <= max_length) {\n                                                                                setCode(e.target.value.toUpperCase());\n                                                                                setisValidCode(true);\n                                                                                setIsButtonDisabled(e.target.value === \"\" || description === \"\" || (typeof e.target.value === \"number\" ? e.target.value.toString().length < min_length : e.target.value.length < min_length));\n                                                                            }\n                                                                        },\n                                                                        onBlur: (e)=>{\n                                                                            const trimmedValue = trimInputText(e.target.value);\n                                                                            if (trimmedValue.length <= max_length) {\n                                                                                setCode(trimmedValue.toUpperCase());\n                                                                                setisValidCode(true);\n                                                                                setIsButtonDisabled(e.target.value === \"\" || description === \"\" || (typeof e.target.value === \"number\" ? e.target.value.toString().length < min_length : e.target.value.length < min_length));\n                                                                            } else {\n                                                                                setisValidCode(false);\n                                                                            }\n                                                                        },\n                                                                        placeholder: placeholderText,\n                                                                        // style={{ textTransform: \"uppercase\" }}\n                                                                        value: code\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\DrawerComponent.js\",\n                                                                        lineNumber: 567,\n                                                                        columnNumber: 23\n                                                                    }, undefined),\n                                                                    !isValidCode && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-red-500 text-xs mt-1 ml-1\",\n                                                                        children: \"Code Already Exists\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\DrawerComponent.js\",\n                                                                        lineNumber: 608,\n                                                                        columnNumber: 25\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\DrawerComponent.js\",\n                                                                lineNumber: 566,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                className: \"border border-skin-primary text-white bg-skin-primary py-1 px-5 ml-4 \".concat(!isValidCode ? \"mb-[18px]\" : \"\", ' w-[130px] font-medium rounded-md scale-x-100\"\\n                      }'),\n                                                                onClick: isUpdateMode ? handelupdate : handleAdd,\n                                                                disabled: isButtonDisabled,\n                                                                children: isUpdateMode ? \"Update\" : \"Add New\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\DrawerComponent.js\",\n                                                                lineNumber: 613,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\DrawerComponent.js\",\n                                                        lineNumber: 565,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\DrawerComponent.js\",\n                                                lineNumber: 531,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\DrawerComponent.js\",\n                                        lineNumber: 488,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-col\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"relative block w-full text-gray-400 mt-0 pt-0 mb-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"absolute px-4 py-1 pt-[0.4rem] 2xl:pt-1.5 text-black\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fortawesome_react_fontawesome__WEBPACK_IMPORTED_MODULE_2__.FontAwesomeIcon, {\n                                                            icon: _fortawesome_free_solid_svg_icons__WEBPACK_IMPORTED_MODULE_11__.faSearch,\n                                                            className: \"fw-bold\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\DrawerComponent.js\",\n                                                            lineNumber: 629,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\DrawerComponent.js\",\n                                                        lineNumber: 628,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"text\",\n                                                        id: \"filter-text-box\",\n                                                        placeholder: \"Search\",\n                                                        onInput: onFilterTextBoxChanged,\n                                                        className: \"block w-full px-4 pl-10 text-gray-500 placeholder-gray-400 searchbar border rounded-lg appearance-none form-input focus:outline-none\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\DrawerComponent.js\",\n                                                        lineNumber: 631,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\DrawerComponent.js\",\n                                                lineNumber: 627,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"!rounded-md px-5 border border-gray-300\",\n                                                style: {\n                                                    height: \"calc(100vh - 210px)\"\n                                                },\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ag_grid_react__WEBPACK_IMPORTED_MODULE_3__.AgGridReact, {\n                                                    rowData: rowData,\n                                                    ref: gridRef,\n                                                    columnDefs: columnDefs,\n                                                    defaultColDef: defaultColDef,\n                                                    getRowStyle: getRowStyle\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\DrawerComponent.js\",\n                                                    lineNumber: 643,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\DrawerComponent.js\",\n                                                lineNumber: 639,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\DrawerComponent.js\",\n                                        lineNumber: 626,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\DrawerComponent.js\",\n                                lineNumber: 487,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\DrawerComponent.js\",\n                            lineNumber: 486,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\DrawerComponent.js\",\n                    lineNumber: 462,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\DrawerComponent.js\",\n                lineNumber: 461,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true));\n};\n_s(DrawerComponent, \"zpbapB7wDtu66vJweoDKOcOYtUI=\");\n_c = DrawerComponent;\n/* harmony default export */ __webpack_exports__[\"default\"] = (DrawerComponent);\nvar _c;\n$RefreshReg$(_c, \"DrawerComponent\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevExports = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevExports) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports on update so we can compare the boundary\n                // signatures.\n                module.hot.dispose(function (data) {\n                    data.prevExports = currentExports;\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevExports !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevExports, currentExports)) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevExports !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/DrawerComponent.js\n"));

/***/ })

});