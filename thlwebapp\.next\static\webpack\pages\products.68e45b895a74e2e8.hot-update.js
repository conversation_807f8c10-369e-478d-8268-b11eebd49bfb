"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/products",{

/***/ "./pages/products.js":
/*!***************************!*\
  !*** ./pages/products.js ***!
  \***************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __N_SSP: function() { return /* binding */ __N_SSP; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _components_Layout__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/Layout */ \"./components/Layout.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _fortawesome_react_fontawesome__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @fortawesome/react-fontawesome */ \"./node_modules/@fortawesome/react-fontawesome/index.es.js\");\n/* harmony import */ var _fortawesome_free_solid_svg_icons__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! @fortawesome/free-solid-svg-icons */ \"./node_modules/@fortawesome/free-solid-svg-icons/index.mjs\");\n/* harmony import */ var ag_grid_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ag-grid-react */ \"./node_modules/ag-grid-react/lib/main.js\");\n/* harmony import */ var ag_grid_react__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(ag_grid_react__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var ag_grid_community_styles_ag_grid_css__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ag-grid-community/styles//ag-grid.css */ \"./node_modules/ag-grid-community/styles/ag-grid.css\");\n/* harmony import */ var ag_grid_community_styles_ag_grid_css__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(ag_grid_community_styles_ag_grid_css__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var ag_grid_community_styles_ag_theme_alpine_css__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ag-grid-community/styles//ag-theme-alpine.css */ \"./node_modules/ag-grid-community/styles/ag-theme-alpine.css\");\n/* harmony import */ var ag_grid_community_styles_ag_theme_alpine_css__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(ag_grid_community_styles_ag_theme_alpine_css__WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_7__);\n/* harmony import */ var react_toastify__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! react-toastify */ \"./node_modules/react-toastify/dist/react-toastify.esm.mjs\");\n/* harmony import */ var react_toastify_dist_ReactToastify_css__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! react-toastify/dist/ReactToastify.css */ \"./node_modules/react-toastify/dist/ReactToastify.css\");\n/* harmony import */ var react_toastify_dist_ReactToastify_css__WEBPACK_IMPORTED_MODULE_9___default = /*#__PURE__*/__webpack_require__.n(react_toastify_dist_ReactToastify_css__WEBPACK_IMPORTED_MODULE_9__);\n/* harmony import */ var _utils_loaders_loadingContext__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/utils/loaders/loadingContext */ \"./utils/loaders/loadingContext.js\");\n/* harmony import */ var react_select__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! react-select */ \"./node_modules/react-select/dist/react-select.esm.js\");\n/* harmony import */ var _utils_rolePermissionsContext__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/utils/rolePermissionsContext */ \"./utils/rolePermissionsContext.js\");\n/* harmony import */ var _utils_renderer_productStatusRenderer__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/utils/renderer/productStatusRenderer */ \"./utils/renderer/productStatusRenderer.js\");\n/* harmony import */ var _utils_renderer_productActionRenderer__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/utils/renderer/productActionRenderer */ \"./utils/renderer/productActionRenderer.js\");\n/* harmony import */ var _services_apiConfig__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/services/apiConfig */ \"./services/apiConfig.js\");\n/* harmony import */ var js_cookie__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! js-cookie */ \"./node_modules/js-cookie/dist/js.cookie.mjs\");\n/* harmony import */ var _azure_msal_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @azure/msal-react */ \"./node_modules/@azure/msal-react/dist/index.js\");\n/* harmony import */ var _components_ProductDialog__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! @/components/ProductDialog */ \"./components/ProductDialog.js\");\n/* harmony import */ var _utils_secureStorage__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! @/utils/secureStorage */ \"./utils/secureStorage.js\");\n/* harmony import */ var _utils_extractCompanyFromEmail__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! @/utils/extractCompanyFromEmail */ \"./utils/extractCompanyFromEmail.js\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst customSelectStyles = {\n    // Default style\n    control: (base)=>({\n            ...base,\n            height: \"28px\",\n            minHeight: \"28px\"\n        }),\n    // Style when the condition is true\n    option: (base, param)=>{\n        let { data } = param;\n        return {\n            ...base,\n            color: data.is_new == true ? \"red\" : \"\"\n        };\n    },\n    valueContainer: (provided, state)=>({\n            ...provided,\n            height: \"26px\",\n            width: \"300px\",\n            padding: \"0 6px\"\n        }),\n    input: (provided, state)=>({\n            ...provided,\n            margin: \"0px\"\n        }),\n    indicatorSeparator: (state)=>({\n            display: \"none\"\n        }),\n    indicatorsContainer: (provided, state)=>({\n            ...provided,\n            height: \"28px\"\n        })\n};\nconst Products = (param)=>{\n    let { userData, PreviousPage, pageTypeId } = param;\n    _s();\n    console.log(\"userData, PreviousPage, pageTypeId\", userData, PreviousPage, pageTypeId);\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_7__.useRouter)();\n    const [pageSize, setPageSize] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(15);\n    const gridRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)();\n    const [isRMChecked, setIsRMChecked] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [isFGChecked, setIsFGChecked] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [isNVChecked, setIsNVChecked] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const { setIsLoading } = (0,_utils_loaders_loadingContext__WEBPACK_IMPORTED_MODULE_10__.useLoading)();\n    const [rowData, setRowData] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [company, setCompany] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"\");\n    const [blockScreen, setBlockScreen] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [typeId, setTypeId] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(pageTypeId);\n    const [isDialogOpen, setIsDialogOpen] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [isIssUser, setIsIssUser] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [isIssProcurementTeamUser, setIsIssProcurementTeamUser] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [isIssAdmin, setisIssAdmin] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [nvStatus, setNVStatus] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [statusOptions] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([\n        {\n            value: \"Draft\",\n            label: \"Draft\"\n        },\n        {\n            value: \"Pending Review\",\n            label: \"Pending Review\"\n        },\n        {\n            value: \"Rejected\",\n            label: \"Rejected\"\n        },\n        {\n            value: \"ISS to Setup\",\n            label: \"ISS to Setup\"\n        },\n        {\n            value: \"Setup Completed\",\n            label: \"Setup Completed\"\n        },\n        {\n            value: \"Cancelled\",\n            label: \"Cancelled\"\n        }\n    ]);\n    const [filteredRowData, setFilteredRowData] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [checkedValue, setCheckedValue] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [incompleteToast, setIncompleteToast] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [searchInput, setSearchInput] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"\");\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        // const userCompany = Cookies.get(\"company\");\n        const userCompany = (userData === null || userData === void 0 ? void 0 : userData.company) || (0,_utils_extractCompanyFromEmail__WEBPACK_IMPORTED_MODULE_19__.extractCompanyFromEmail)(userData === null || userData === void 0 ? void 0 : userData.email);\n        if (userCompany === \"issproduce\") {\n            setIsIssUser(true);\n        }\n        console.log(userData);\n        if (userData.department_id === 2) {\n            setIsIssProcurementTeamUser(true);\n            console.log(\"checking if it is an admin\", userData.role);\n            if (userData.role_id === 1) {\n                console.log(\"is admin\");\n                setisIssAdmin(true);\n            }\n        }\n        console.log(\"isIssProcurementTeamUser\", isIssProcurementTeamUser);\n    }, []);\n    const openDialog = ()=>{\n        setIsDialogOpen(true);\n    };\n    const closeDialog = ()=>{\n        setIsDialogOpen(false);\n    };\n    // const [isOpenOption, setIsOpenOption] = useState(false);\n    const [selectedRequestType, setSelectedRequestType] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(PreviousPage);\n    const handleTypeIdChange = (e)=>{\n        setSearchInput(\"\");\n        const filterTextBox = document.getElementById(\"filter-text-box\");\n        filterTextBox.value = \"\";\n        gridRef.current.api.setQuickFilter(\"\");\n        setNVStatus([]);\n        setTypeId(parseInt(e.target.value));\n        if (e.target.value === \"1\") {\n            setSelectedRequestType(\"rawMaterialRequest\");\n        } else if (e.target.value === \"3\") {\n            setSelectedRequestType(\"newVarietyRequest\");\n        } else if (e.target.value === \"4\") {\n            setSelectedRequestType(\"packagingform\");\n        }\n    };\n    // const closeOptionModal = () => {\n    //   setIsOpenOption(false);\n    // };\n    const handleRequestType = (type)=>{\n        setSelectedRequestType(type);\n    };\n    const handleFormType = ()=>{\n        if (selectedRequestType) {\n            localStorage.setItem(\"formType\", selectedRequestType);\n            console.log(selectedRequestType);\n            if (selectedRequestType === \"rawMaterialRequest\") {\n                router.push({\n                    pathname: \"/raw-material-request/add\"\n                });\n            // } else if (selectedRequestType == \"finishedProductRequest\") {\n            //   router.push({\n            //     pathname: `/finished-product-request/add` });\n            } else if (selectedRequestType == \"newVarietyRequest\") {\n                router.push({\n                    pathname: \"/variety/add\"\n                });\n            } else if (selectedRequestType == \"packagingform\") {\n                // router.push({ pathname: `/packaging/add` });\n                router.push({\n                    pathname: \"/packaging-form/add\"\n                }); //TODO\n            }\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        if (incompleteToast) {\n            react_toastify__WEBPACK_IMPORTED_MODULE_8__.toast.error(\"Kindly Submit the Request to Export it.\", {\n                position: \"top-right\",\n                autoClose: 1000,\n                hideProgressBar: false,\n                closeOnClick: true,\n                pauseOnHover: false,\n                draggable: true,\n                progress: undefined,\n                theme: \"light\"\n            });\n        }\n    }, [\n        incompleteToast\n    ]);\n    const [productsExtractData, setProductsExtractData] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([\n        [\n            \"Product Extract Data\"\n        ]\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        if (productsExtractData.length > 0) {\n            setCheckedValue(true);\n        } else {\n            setCheckedValue(false);\n        }\n    }, [\n        productsExtractData\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        if (typeof document !== \"undefined\") {\n            document.title = \"Products\";\n        }\n        setIsLoading(false);\n        getData().then((data)=>{\n            console.log(data);\n            const formattedData = data === null || data === void 0 ? void 0 : data.map((row)=>({\n                    id: row.id,\n                    code: row.code,\n                    action_id: row.action_id,\n                    request_no: row.request_no,\n                    type: row.product_type,\n                    product_type_name: row.product_type_label,\n                    product_type_id: row.product_type_id,\n                    reason: (row === null || row === void 0 ? void 0 : row.reason_description) ? row === null || row === void 0 ? void 0 : row.reason_description : \"Not Entered\",\n                    delivery_date: row.delivery_date ? new Date(row.delivery_date).toISOString().split(\"T\")[0] : \"Not Entered\",\n                    // delivery_date: new Date(row.delivery_date),\n                    product_code: row.master_product_code ? row.master_product_code : \"Not Entered\",\n                    product_description: row.product_description ? row.product_description : \"Not Entered\",\n                    originator: row.originator ? row.originator : row.originator_name,\n                    originator_email: row.originator_email,\n                    coo: row.coo ? row.coo : \"Not Entered\",\n                    status: row.status_label,\n                    master_product_code: row === null || row === void 0 ? void 0 : row.master_product_code,\n                    count_or_size: row === null || row === void 0 ? void 0 : row.count_or_size,\n                    units_in_outer: row === null || row === void 0 ? void 0 : row.units_in_outer,\n                    cases_per_pallet: row === null || row === void 0 ? void 0 : row.cases_per_pallet,\n                    outer_net_weight: row === null || row === void 0 ? void 0 : row.outer_net_weight,\n                    outer_gross_weight: row === null || row === void 0 ? void 0 : row.outer_gross_weight,\n                    sub_product_code: row === null || row === void 0 ? void 0 : row.sub_product_code,\n                    temperature_grade: row === null || row === void 0 ? void 0 : row.temperature_grade,\n                    intrastat_commodity_code: row === null || row === void 0 ? void 0 : row.intrastat_commodity_code,\n                    temperature_grade_name: row === null || row === void 0 ? void 0 : row.temperature_grade_name,\n                    intrastat_commodity_code_name: row === null || row === void 0 ? void 0 : row.intrastat_commodity_code_name,\n                    mark_variety_name: row === null || row === void 0 ? void 0 : row.mark_variety_name,\n                    intrastat_commodity_code_id: row === null || row === void 0 ? void 0 : row.intrastat_commodity_code_id,\n                    sort_group_id: row === null || row === void 0 ? void 0 : row.group_id,\n                    company: row === null || row === void 0 ? void 0 : row.company_name,\n                    temperature_grade_id: row === null || row === void 0 ? void 0 : row.temperature_grade_id,\n                    userText4: row === null || row === void 0 ? void 0 : row.userText4,\n                    userText5: row === null || row === void 0 ? void 0 : row.userText5,\n                    userText6: row === null || row === void 0 ? void 0 : row.userText6\n                }));\n            setCompany(formattedData[0].company);\n            setRowData(formattedData);\n            setFilteredRowData(formattedData);\n        }).catch((error)=>{\n            return error;\n        });\n    }, [\n        typeId\n    ]);\n    function getData() {\n        setRowData([]);\n        setFilteredRowData([]);\n        let serverAddress = _services_apiConfig__WEBPACK_IMPORTED_MODULE_14__.apiConfig.serverAddress;\n        const company_name = (userData === null || userData === void 0 ? void 0 : userData.company) || (0,_utils_extractCompanyFromEmail__WEBPACK_IMPORTED_MODULE_19__.extractCompanyFromEmail)(userData === null || userData === void 0 ? void 0 : userData.email);\n        const AdCompany = (userData === null || userData === void 0 ? void 0 : userData.companyName) || (userData === null || userData === void 0 ? void 0 : userData.ADCompanyName);\n        return fetch(\"\".concat(serverAddress, \"products/get-products/\").concat(company_name == \"dpsltd\" && AdCompany == \"DPS MS\" ? \"DPS MS\" : company_name, \"\\n      /\").concat(typeId), {\n            method: \"GET\",\n            headers: {\n                \"Content-Type\": \"application/json\"\n            },\n            credentials: \"include\"\n        }).then(async (res)=>{\n            if (res.status == 502) {\n                setBlockScreen(true);\n                return;\n            }\n            setBlockScreen(false);\n            if (res.status === 200) {\n                return res.json();\n            }\n            if (res.status === 400) {\n                react_toastify__WEBPACK_IMPORTED_MODULE_8__.toast.error(\"There was an error with your request. Please check your data and try again.\");\n            } else if (res.status === 401) {\n                react_toastify__WEBPACK_IMPORTED_MODULE_8__.toast.error(\"Your session has expired. Please log in again.\");\n                setTimeout(async ()=>{\n                    await (0,_utils_secureStorage__WEBPACK_IMPORTED_MODULE_18__.logout)();\n                    router.push(\"/login\");\n                }, 3000);\n            }\n            throw new Error(\"Failed to fetch data\");\n        }).catch((error)=>{\n            console.error(error);\n        });\n    }\n    const handlePageSizeChange = (event)=>{\n        const newPageSize = parseInt(event.target.value, 15);\n        setPageSize(newPageSize);\n        gridRef.current.api.paginationSetPageSize(newPageSize);\n    };\n    const handleStatusChange = (selectedOptions)=>{\n        setNVStatus(selectedOptions);\n        filterData(selectedOptions);\n    };\n    const filterData = (statuses)=>{\n        if (statuses.length == 0) {\n            setFilteredRowData(rowData);\n            return;\n        }\n        const filteredData = rowData.filter((row)=>statuses.some((status)=>status.value === row.status));\n        setFilteredRowData(filteredData.length > 0 ? filteredData : []);\n    };\n    // const clearFilters = () => {\n    //   setSelectedStatuses([]);\n    //   setFilteredRowData(rowData);\n    // };\n    const defaultColDef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useMemo)(()=>({\n            sortable: true,\n            filter: false,\n            resizable: true,\n            flex: 1,\n            suppressMenu: false\n        }));\n    const gridOptions = {\n        responsive: true\n    };\n    const columnDefs = [\n        {\n            headerName: \"Request No.\",\n            field: \"request_no\",\n            // cellRenderer: nameRenderer,\n            suppressMenu: true,\n            suppressSizeToFit: true,\n            suppressSizeToFit: false,\n            cellClass: \"ag-grid-checkbox-cell\",\n            flex: \"2%\",\n            filter: true\n        },\n        {\n            headerName: \"Reason\",\n            field: \"reason\",\n            flex: \"2%\",\n            cellRenderer: _utils_renderer_productStatusRenderer__WEBPACK_IMPORTED_MODULE_12__[\"default\"],\n            hide: typeId == 1 || 4 ? false : 0,\n            cellStyle: (params)=>{\n                if (params.value == \"Not Entered\") {\n                    return {\n                        color: \"#B31312\"\n                    };\n                }\n                return null;\n            }\n        },\n        {\n            headerName: \"Delivery Date\",\n            field: \"delivery_date\",\n            flex: \"2%\",\n            hide: typeId == 1 || 4 ? false : 0,\n            cellRenderer: (params)=>{\n                if (params.value === \"Not Entered\") {\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-[#B31312]\",\n                        children: \"Not Entered\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\products.js\",\n                        lineNumber: 381,\n                        columnNumber: 18\n                    }, undefined);\n                } else {\n                    // params.value\n                    // Format the date from yyyy-mm-dd to dd/mm/yyyy\n                    const dateParts = params.value.split(\"-\");\n                    const formattedDate = dateParts.reverse().join(\"/\");\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: formattedDate\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\products.js\",\n                        lineNumber: 388,\n                        columnNumber: 18\n                    }, undefined);\n                }\n            }\n        },\n        {\n            headerName: \"Product Code\",\n            field: \"product_code\",\n            flex: \"2%\",\n            // cellClass: (params) => {\n            //   return params.value === \"Not Entered\" ? 'not-entered' : '';\n            // }\n            cellStyle: (params)=>{\n                if (params.value == \"Not Entered\") {\n                    return {\n                        color: \"#B31312\"\n                    };\n                }\n                return null;\n            }\n        },\n        {\n            // headerName: \"Product description\",\n            // field: \"product_description\",\n            headerName: \"Product Description\",\n            field: \"product_description\",\n            // cellRenderer: statusRenderer,\n            flex: \"3%\",\n            cellStyle: (params)=>{\n                if (params.value == \"Not Entered\") {\n                    return {\n                        color: \"#B31312\"\n                    };\n                }\n                return null;\n            }\n        },\n        {\n            headerName: \"Originator\",\n            field: \"originator\",\n            headerName: \"Originator\",\n            field: \"originator\",\n            // cellRenderer: statusRenderer,\n            flex: \"2%\",\n            flex: \"2%\"\n        },\n        // {\n        //   headerName: \"COO\",\n        //   field: \"coo\",\n        //   headerName: \"COO\",\n        //   field: \"coo\",\n        //   // cellRenderer: statusRenderer,\n        //   flex: \"2%\",\n        //   cellStyle: (params) => {\n        //     if (params.value == \"Not Entered\") {\n        //       return { color: \"#B31312\" };\n        //     }\n        //     return null;\n        //   },\n        // },\n        {\n            headerName: \"Status\",\n            field: \"status\",\n            cellRenderer: _utils_renderer_productStatusRenderer__WEBPACK_IMPORTED_MODULE_12__[\"default\"],\n            cellStyle: ()=>({\n                    justifyContent: \"center\"\n                }),\n            flex: \"2%\",\n            hide: false\n        },\n        {\n            // field: \"Action(s)\",\n            field: typeId == 3 ? \"Action(s)\" : \"Action(s)\",\n            cellRenderer: (params)=>(0,_utils_renderer_productActionRenderer__WEBPACK_IMPORTED_MODULE_13__[\"default\"])(params, userData, userData.token, company, typeId, setIsLoading, isIssUser),\n            flex: \"2%\",\n            cellStyle: ()=>({}),\n            cellStyle: {\n                justifyContent: \"end\",\n                paddingRight: \"10px\"\n            },\n            sortable: false\n        },\n        {\n            field: \"role_num\",\n            hide: true\n        }\n    ];\n    const handleGridReady = (params)=>{\n        params.api.setColumnDefs(columnDefs);\n    };\n    const onFilterTextBoxChanged = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(()=>{\n        gridRef.current.api.setQuickFilter(document.getElementById(\"filter-text-box\").value);\n        setSearchInput(document.getElementById(\"filter-text-box\").value);\n    }, []);\n    const handleFilterProductType = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)((e)=>{\n        if (e.target.value == \"RM\") {\n            if (e.target.checked) {\n                setIsRMChecked(true);\n                setIsFGChecked(false);\n                setIsNVChecked(false);\n                gridRef.current.api.setQuickFilter(\"RM\");\n            } else {\n                setIsRMChecked(false);\n                gridRef.current.api.setQuickFilter(\"\");\n            }\n        } else if (e.target.value == \"FG\") {\n            if (e.target.checked) {\n                setIsFGChecked(true);\n                setIsRMChecked(false);\n                setIsNVChecked(false);\n                gridRef.current.api.setQuickFilter(\"FG\");\n            } else {\n                setIsFGChecked(false);\n                gridRef.current.api.setQuickFilter(\"\");\n            }\n        } else if (e.target.value == \"NV\") {\n            if (e.target.checked) {\n                setIsNVChecked(true);\n                setIsFGChecked(false);\n                setIsRMChecked(false);\n                gridRef.current.api.setQuickFilter(\"NV\");\n            } else {\n                setIsNVChecked(true);\n                gridRef.current.api.setQuickFilter(\"\");\n            }\n        } else {\n            setIsRMChecked(false);\n            setIsFGChecked(false);\n            setIsNVChecked(false);\n            gridRef.current.api.setQuickFilter(\"\");\n        }\n    }, []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_toastify__WEBPACK_IMPORTED_MODULE_8__.ToastContainer, {\n                limit: 1\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\products.js\",\n                lineNumber: 528,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Layout__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                userData: userData,\n                blockScreen: blockScreen,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mr-20 md:mr-12 lg:mr-14\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-row md:flex-col lg:flex-row justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"border border-light-gray rounded-md px-3 py-1\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"flex items-center gap-2 cursor-pointer\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                type: \"radio\",\n                                                                name: \"requestType\",\n                                                                value: \"1\",\n                                                                className: \"form-radio w-5 h-5 cursor-pointer\",\n                                                                checked: typeId === 1,\n                                                                onChange: handleTypeIdChange\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\products.js\",\n                                                                lineNumber: 536,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"labels\",\n                                                                children: \"Raw Materials\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\products.js\",\n                                                                lineNumber: 544,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\products.js\",\n                                                        lineNumber: 535,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\products.js\",\n                                                    lineNumber: 534,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"border border-light-gray rounded-md px-3 py-1\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"flex items-center gap-2 cursor-pointer\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                type: \"radio\",\n                                                                name: \"requestType\",\n                                                                value: \"3\",\n                                                                className: \"form-radio w-5 h-5 cursor-pointer\",\n                                                                checked: typeId === 3,\n                                                                onChange: handleTypeIdChange\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\products.js\",\n                                                                lineNumber: 560,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"labels\",\n                                                                children: \"New Variety\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\products.js\",\n                                                                lineNumber: 568,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\products.js\",\n                                                        lineNumber: 559,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\products.js\",\n                                                    lineNumber: 558,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                isIssUser && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"border border-light-gray rounded-md px-3 py-1\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"flex items-center gap-2 cursor-pointer\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                type: \"radio\",\n                                                                name: \"requestType\",\n                                                                value: \"4\",\n                                                                className: \"form-radio w-5 h-5 cursor-pointer\",\n                                                                checked: typeId === 4,\n                                                                onChange: handleTypeIdChange\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\products.js\",\n                                                                lineNumber: 574,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"labels\",\n                                                                children: \"Packaging\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\products.js\",\n                                                                lineNumber: 582,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\products.js\",\n                                                        lineNumber: 573,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\products.js\",\n                                                    lineNumber: 572,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\products.js\",\n                                            lineNumber: 533,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"labels\",\n                                            style: {\n                                                marginLeft: \"10px\"\n                                            },\n                                            children: typeId === 3 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_select__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                isMulti: true,\n                                                options: statusOptions,\n                                                onChange: handleStatusChange,\n                                                placeholder: \"Select Status...\",\n                                                className: \"basic-multi-select\",\n                                                classNamePrefix: \"select\",\n                                                value: nvStatus,\n                                                isSearchable: false,\n                                                styles: customSelectStyles\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\products.js\",\n                                                lineNumber: 589,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\products.js\",\n                                            lineNumber: 587,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center px-3 w-auto md:w-fit lg:w-auto md:py-2 lg:py-1 md:mb-3 lg:mb-0\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\products.js\",\n                                            lineNumber: 605,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\products.js\",\n                                    lineNumber: 532,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"relative block w-[47vh] text-gray-400 mt-0 pt-0\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"absolute px-4 py-1 pt-[0.4rem] 2xl:pt-1.5 text-black\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fortawesome_react_fontawesome__WEBPACK_IMPORTED_MODULE_3__.FontAwesomeIcon, {\n                                                        icon: _fortawesome_free_solid_svg_icons__WEBPACK_IMPORTED_MODULE_21__.faSearch,\n                                                        className: \"fw-bold\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\products.js\",\n                                                        lineNumber: 636,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\products.js\",\n                                                    lineNumber: 635,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"text\",\n                                                    id: \"filter-text-box\",\n                                                    placeholder: \"Search...\",\n                                                    onInput: onFilterTextBoxChanged,\n                                                    value: searchInput,\n                                                    className: \"block w-full px-4 pl-10 text-gray-500 placeholder-gray-400 searchbar border rounded-lg appearance-none form-input focus:outline-none\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\products.js\",\n                                                    lineNumber: 638,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\products.js\",\n                                            lineNumber: 634,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ProductDialog__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                            isOpen: openDialog,\n                                            onClose: closeDialog,\n                                            handleFormType: handleFormType,\n                                            selectedRequestType: selectedRequestType,\n                                            isIssUser: isIssUser,\n                                            isIssProcurmentUser: isIssProcurementTeamUser,\n                                            handleRequestType: handleRequestType,\n                                            admin: isIssAdmin\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\products.js\",\n                                            lineNumber: 657,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\products.js\",\n                                    lineNumber: 633,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\products.js\",\n                            lineNumber: 531,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"my-5\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative ag-theme-alpine !rounded-md\",\n                                style: {\n                                    height: \"calc(100vh - 151px)\"\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ag_grid_react__WEBPACK_IMPORTED_MODULE_4__.AgGridReact, {\n                                        rowData: filteredRowData,\n                                        ref: gridRef,\n                                        columnDefs: columnDefs,\n                                        defaultColDef: defaultColDef,\n                                        suppressRowClickSelection: true,\n                                        pagination: true,\n                                        paginationPageSize: pageSize,\n                                        onPageSizeChanged: handlePageSizeChange,\n                                        tooltipShowDelay: 0,\n                                        tooltipHideDelay: 1000,\n                                        onGridReady: handleGridReady,\n                                        gridOptions: gridOptions\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\products.js\",\n                                        lineNumber: 674,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-start mt-2 pagination-style\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            htmlFor: \"page-size-select pagination\",\n                                            className: \"inputs\",\n                                            children: [\n                                                \"Show\",\n                                                \" \",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                    id: \"page-size-select\",\n                                                    onChange: handlePageSizeChange,\n                                                    value: pageSize,\n                                                    className: \"focus:outline-none\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: 10,\n                                                            children: \"10\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\products.js\",\n                                                            lineNumber: 697,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: 15,\n                                                            children: \"15\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\products.js\",\n                                                            lineNumber: 698,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: 25,\n                                                            children: \"25\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\products.js\",\n                                                            lineNumber: 699,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: 50,\n                                                            children: \"50\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\products.js\",\n                                                            lineNumber: 700,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: 100,\n                                                            children: \"100\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\products.js\",\n                                                            lineNumber: 701,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\products.js\",\n                                                    lineNumber: 691,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                \" \",\n                                                \"Entries\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\products.js\",\n                                            lineNumber: 689,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\products.js\",\n                                        lineNumber: 688,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\products.js\",\n                                lineNumber: 670,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\products.js\",\n                            lineNumber: 669,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\products.js\",\n                    lineNumber: 530,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\products.js\",\n                lineNumber: 529,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true);\n};\n_s(Products, \"K/vbnwHMd05vB3GAmUTG2EVlocA=\", false, function() {\n    return [\n        next_router__WEBPACK_IMPORTED_MODULE_7__.useRouter,\n        _utils_loaders_loadingContext__WEBPACK_IMPORTED_MODULE_10__.useLoading\n    ];\n});\n_c = Products;\nvar __N_SSP = true;\n/* harmony default export */ __webpack_exports__[\"default\"] = (Products);\nvar _c;\n$RefreshReg$(_c, \"Products\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevExports = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevExports) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports on update so we can compare the boundary\n                // signatures.\n                module.hot.dispose(function (data) {\n                    data.prevExports = currentExports;\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevExports !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevExports, currentExports)) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevExports !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./pages/products.js\n"));

/***/ })

});