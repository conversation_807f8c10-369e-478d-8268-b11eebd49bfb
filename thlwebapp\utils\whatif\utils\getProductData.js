import { apiConfig } from "@/services/apiConfig";
import Cookies from "js-cookie";
import { toast } from "react-toastify";

export async function getData(url, token) {
 
  return await fetch(`${apiConfig.serverAddress}whatif/${url}`, {
    method: "GET",
    headers: {
      Authorization: `Bearer ${token}`,
    },
  })
    .then(async (res) => {
     
      if (res.status === 400) {
        toast.error(
          "There was an error with your request. Please check your data and try again."
        );
        return [];
      } else if (res.status === 401) {
        localStorage.removeItem("superUser");
        localStorage.removeItem("company");
        localStorage.removeItem("id");
        localStorage.removeItem("name");
        localStorage.removeItem("role");
        localStorage.removeItem("email");
        Cookies.remove("user");
        Cookies.remove("theme");
        Cookies.remove("token");
        return null;
      }
      if (res.status === 200) {
        return res.json();
      }
      throw new Error("Failed to fetch data");
    })
    .catch((error) => {
      console.log(error);
    });
}
