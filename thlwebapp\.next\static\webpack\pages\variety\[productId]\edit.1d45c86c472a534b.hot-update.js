"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/variety/[productId]/edit",{

/***/ "./pages/variety/[productId]/edit/index.js":
/*!*************************************************!*\
  !*** ./pages/variety/[productId]/edit/index.js ***!
  \*************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __N_SSP: function() { return /* binding */ __N_SSP; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _components_Layout__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/Layout */ \"./components/Layout.js\");\n/* harmony import */ var _services_apiConfig__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/services/apiConfig */ \"./services/apiConfig.js\");\n/* harmony import */ var js_cookie__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! js-cookie */ \"./node_modules/js-cookie/dist/js.cookie.mjs\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var react_loader_spinner__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! react-loader-spinner */ \"./node_modules/react-loader-spinner/dist/module.js\");\n/* harmony import */ var react_toastify__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! react-toastify */ \"./node_modules/react-toastify/dist/react-toastify.esm.mjs\");\n/* harmony import */ var react_toastify_dist_ReactToastify_css__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! react-toastify/dist/ReactToastify.css */ \"./node_modules/react-toastify/dist/ReactToastify.css\");\n/* harmony import */ var react_toastify_dist_ReactToastify_css__WEBPACK_IMPORTED_MODULE_8___default = /*#__PURE__*/__webpack_require__.n(react_toastify_dist_ReactToastify_css__WEBPACK_IMPORTED_MODULE_8__);\n/* harmony import */ var _components_NewVarietyRequest__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/NewVarietyRequest */ \"./components/NewVarietyRequest.js\");\n/* harmony import */ var _utils_extractCompanyFromEmail__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/utils/extractCompanyFromEmail */ \"./utils/extractCompanyFromEmail.js\");\n/* harmony import */ var _utils_secureStorage__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/utils/secureStorage */ \"./utils/secureStorage.js\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\nconst index = (param)=>{\n    let { userData } = param;\n    _s();\n    const [dropdowns, setDropdowns] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(null);\n    const [newVarietyData, setNewVarietyData] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(null);\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_4__.useRouter)();\n    const { productId } = router.query;\n    (0,react__WEBPACK_IMPORTED_MODULE_5__.useEffect)(()=>{\n        // const company = Cookies.get(\"company\");\n        const company = (userData === null || userData === void 0 ? void 0 : userData.company) || (0,_utils_extractCompanyFromEmail__WEBPACK_IMPORTED_MODULE_10__.extractCompanyFromEmail)(userData === null || userData === void 0 ? void 0 : userData.email);\n        let prophetId = 1;\n        if (company == \"dpsltd\") {\n            prophetId = 1;\n        } else if (company == \"efcltd\") {\n            prophetId = 3;\n        } else if (company == \"fpp-ltd\") {\n            prophetId = 4;\n        }\n        const fetchData = async ()=>{\n            const serverAddress = _services_apiConfig__WEBPACK_IMPORTED_MODULE_2__.apiConfig.serverAddress;\n            try {\n                const allDropDowns = [\n                    \"masterProductCode\"\n                ];\n                const dropdownsRequest = await fetch(\"\".concat(serverAddress, \"products/get-products-dropdowns-list?prophetId=\").concat(prophetId), {\n                    method: \"POST\",\n                    headers: {\n                        Accept: \"application/json\",\n                        \"Content-Type\": \"application/json\"\n                    },\n                    credentials: \"include\",\n                    body: JSON.stringify(allDropDowns)\n                });\n                // console.log(\"dropdownsRequest\",dropdownsRequest);\n                if (dropdownsRequest.status === 401) {\n                    react_toastify__WEBPACK_IMPORTED_MODULE_7__.toast.error(\"Your session has expired. Please log in again.\");\n                    setTimeout(async ()=>{\n                        await (0,_utils_secureStorage__WEBPACK_IMPORTED_MODULE_11__.logout)();\n                        const redirectUrl = \"/login?redirect=\".concat(encodeURIComponent(window.location.pathname));\n                        router.push(redirectUrl);\n                    }, 3000);\n                    return null;\n                }\n                const newVarietyRequest = fetch(\"\".concat(serverAddress, \"products/get-nv-product-by-id/\").concat(productId), {\n                    method: \"GET\",\n                    headers: {\n                        Accept: \"application/json\",\n                        \"Content-Type\": \"application/json\"\n                    }\n                });\n                const [dropdownsResponse, newVarietyResponse] = await Promise.all([\n                    dropdownsRequest,\n                    newVarietyRequest\n                ]);\n                const allDropdownsList = await dropdownsResponse.json();\n                const newVarietyData = await newVarietyResponse.json();\n                setDropdowns(allDropdownsList);\n                setNewVarietyData(newVarietyData);\n            } catch (error) {\n                console.error(\"Error fetching data\", error);\n            }\n        };\n        if (productId) {\n            fetchData();\n        }\n    }, [\n        productId\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Layout__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n        userData: userData,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_toastify__WEBPACK_IMPORTED_MODULE_7__.ToastContainer, {\n                limit: 1\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\variety\\\\[productId]\\\\edit\\\\index.js\",\n                lineNumber: 98,\n                columnNumber: 7\n            }, undefined),\n            !dropdowns && !newVarietyData ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    display: \"flex\",\n                    alignItems: \"center\",\n                    justifyContent: \"center\",\n                    height: \"100vh\"\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_loader_spinner__WEBPACK_IMPORTED_MODULE_6__.ThreeCircles, {\n                    color: \"#002D73\",\n                    height: 50,\n                    width: 50,\n                    visible: true,\n                    ariaLabel: \"oval-loading\",\n                    secondaryColor: \"#0066FF\",\n                    strokeWidth: 2,\n                    strokeWidthSecondary: 2\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\variety\\\\[productId]\\\\edit\\\\index.js\",\n                    lineNumber: 108,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\variety\\\\[productId]\\\\edit\\\\index.js\",\n                lineNumber: 100,\n                columnNumber: 9\n            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_NewVarietyRequest__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                dropdowns: dropdowns,\n                userData: userData,\n                newVarietyData: newVarietyData[0],\n                pageType: \"update\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\variety\\\\[productId]\\\\edit\\\\index.js\",\n                lineNumber: 120,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\variety\\\\[productId]\\\\edit\\\\index.js\",\n        lineNumber: 97,\n        columnNumber: 5\n    }, undefined);\n};\n_s(index, \"Hed4/xYb5dPQ/rk452hUP3Z4GUA=\", false, function() {\n    return [\n        next_router__WEBPACK_IMPORTED_MODULE_4__.useRouter\n    ];\n});\nvar __N_SSP = true;\n/* harmony default export */ __webpack_exports__[\"default\"] = (index);\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevExports = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevExports) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports on update so we can compare the boundary\n                // signatures.\n                module.hot.dispose(function (data) {\n                    data.prevExports = currentExports;\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevExports !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevExports, currentExports)) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevExports !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./pages/variety/[productId]/edit/index.js\n"));

/***/ })

});