"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/suppliers",{

/***/ "./utils/exportExcel.js":
/*!******************************!*\
  !*** ./utils/exportExcel.js ***!
  \******************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var exceljs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! exceljs */ \"./node_modules/exceljs/dist/exceljs.min.js\");\n/* harmony import */ var exceljs__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(exceljs__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react_toastify__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react-toastify */ \"./node_modules/react-toastify/dist/react-toastify.esm.mjs\");\n/* harmony import */ var react_toastify_dist_ReactToastify_css__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-toastify/dist/ReactToastify.css */ \"./node_modules/react-toastify/dist/ReactToastify.css\");\n/* harmony import */ var react_toastify_dist_ReactToastify_css__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react_toastify_dist_ReactToastify_css__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _services_apiConfig__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/services/apiConfig */ \"./services/apiConfig.js\");\n/* harmony import */ var _secureStorage__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./secureStorage */ \"./utils/secureStorage.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_5__);\n\n\n\n\n\n\n// todo:move the supplierCode column from the excel file to first column\nconst exportExcel = async function(data, isInternal, token, company, userData, prophet_id, requestor_email, isMultiple) {\n    let isProductExtract = arguments.length > 8 && arguments[8] !== void 0 ? arguments[8] : false, onProductSubmit = arguments.length > 9 && arguments[9] !== void 0 ? arguments[9] : false, productEmailParagraph = arguments.length > 10 && arguments[10] !== void 0 ? arguments[10] : \"\", productEmailCommentPlaceholder = arguments.length > 11 && arguments[11] !== void 0 ? arguments[11] : \"\", request_no = arguments.length > 12 && arguments[12] !== void 0 ? arguments[12] : \"\", varietyRequest = arguments.length > 13 && arguments[13] !== void 0 ? arguments[13] : false;\n    let serverAddress = _services_apiConfig__WEBPACK_IMPORTED_MODULE_3__.apiConfig.serverAddress;\n    let isEmergencyAndFinanceNotCompleteObj = [];\n    let supplierNames = [];\n    const workbook = new (exceljs__WEBPACK_IMPORTED_MODULE_0___default().Workbook)();\n    if (isInternal) {\n        data.forEach((sheetData, index1)=>{\n            if (sheetData.length === 0) {\n                console.error(\"sheetData is empty for index:\", index1);\n                return;\n            }\n            const sheetName = sheetData[0] || \"Sheet\".concat(index1 + 1);\n            let worksheet;\n            if (index1 < 9) {\n                if (Array.isArray(sheetName)) {\n                    const actualSheetName = sheetName[0];\n                    worksheet = workbook.addWorksheet(actualSheetName);\n                } else {\n                    worksheet = workbook.addWorksheet(sheetName);\n                }\n            }\n            if (sheetData.length > 1) {\n                let headers;\n                if (index1 < 9) {\n                    headers = Object.keys(sheetData[1]);\n                    worksheet.addRow(headers);\n                }\n                sheetData.slice(1).forEach((row, internalIndex)=>{\n                    if (index1 < 9) {\n                        const rowData = headers.map((header)=>row[header] || \"\");\n                        if (index1 != 4 && index1 != 3) {\n                            worksheet.addRow(rowData);\n                        } else if (index1 == 4) {\n                            worksheet.addRow(rowData);\n                        } else if (index1 == 3 && rowData[1] != \"\") {\n                            worksheet.addRow(rowData);\n                        }\n                    }\n                    if (index1 === 9) {\n                        supplierNames.push({\n                            supplierName: row === null || row === void 0 ? void 0 : row.supplierName,\n                            supplierCode: row === null || row === void 0 ? void 0 : row.supplierCode\n                        });\n                        if (row.isEmergencyAndFinanceNotComplete) {\n                            let isEmergencyAndFinanceNotCompleteSupplier = {\n                                isEmergencyAndFinanceNotComplete: row.isEmergencyAndFinanceNotComplete,\n                                supplierName: row.supplierName\n                            };\n                            isEmergencyAndFinanceNotCompleteObj.push(isEmergencyAndFinanceNotCompleteSupplier);\n                        }\n                        fetch(\"\".concat(serverAddress, \"suppliers/update-supplier/\").concat(row.id), {\n                            method: \"PUT\",\n                            headers: {\n                                Accept: \"application/json\",\n                                \"Content-Type\": \"application/json\"\n                            },\n                            credentials: \"include\",\n                            body: JSON.stringify({\n                                sectionName: \"updateStatus\",\n                                type: \"exportExcel\",\n                                status: 5,\n                                exported: true,\n                                updated_date: new Date().toISOString(),\n                                company_name: row.supplierName,\n                                to: \"Internal\"\n                            })\n                        }).then((res)=>{\n                            if (res.status === 200) {\n                                return res.json();\n                            }\n                            if (res.status === 401) {\n                                react_toastify__WEBPACK_IMPORTED_MODULE_1__.toast.error(\"Your session has expired. Please log in again.\");\n                                setTimeout(async ()=>{\n                                    await (0,_secureStorage__WEBPACK_IMPORTED_MODULE_4__.logout)();\n                                }, 3000);\n                                return null;\n                            }\n                            return Promise.reject(res);\n                        }).then((json)=>{\n                            if (json.status == 200) {\n                                return true;\n                            }\n                        }).catch((error)=>{\n                            console.log(error);\n                        });\n                    }\n                });\n            }\n        });\n    } else if (isProductExtract) {\n        if (data.length === 0) {\n            console.error(\"sheetData is empty for index:\");\n            return;\n        }\n        data.forEach((sheetData)=>{\n            var _Object;\n            const sheetName = sheetData[0] || \"Sheet\".concat(index + 1);\n            let worksheet;\n            if (Array.isArray(sheetName)) {\n                const actualSheetName = sheetName[0];\n                worksheet = workbook.addWorksheet(actualSheetName);\n            } else {\n                worksheet = workbook.addWorksheet(sheetName);\n            }\n            // console.log(\"Object.keys(sheetData[1])\",Object?.keys(sheetData[1])? Object?.keys(sheetData[1]) : 'not there');\n            let headers = ((_Object = Object) === null || _Object === void 0 ? void 0 : _Object.keys(sheetData[1])) ? Object.keys(sheetData[1]) : \"null\";\n            if (isMultiple) {\n                headers = headers.slice(0, -1);\n            }\n            worksheet.addRow(headers);\n            sheetData.slice(1).forEach((row, internalIndex)=>{\n                let rowData;\n                rowData = headers.map((header)=>row[header] || \"\");\n                worksheet.addRow(rowData);\n            });\n        });\n    } else {\n        if (data.length === 0) {\n            console.error(\"sheetData is empty for index:\");\n            return;\n        }\n        data.forEach((row, index1)=>{\n            const sheetName = row[0] || \"Sheet\".concat(index1 + 1);\n            let worksheet;\n            if (Array.isArray(sheetName)) {\n                const actualSheetName = sheetName[0];\n                worksheet = workbook.addWorksheet(actualSheetName);\n            } else {\n                worksheet = workbook.addWorksheet(sheetName);\n            }\n            let headers;\n            if (index1 == 1) {\n                headers = Object.keys(row[1]).slice(0, -2);\n            } else {\n                headers = Object.keys(row[1]);\n            }\n            worksheet.addRow(headers);\n            for(let i = 1; i < row.length; i++){\n                if (index1 == 0 && i > 0) {\n                    supplierNames.push({\n                        supplierName: row[i][\"Supplier name\"],\n                        supplierCode: row[i][\"Supplier code\"]\n                    });\n                }\n                let rowData;\n                if (index1 == 1) {\n                    rowData = headers.map((header)=>row[i][header] || \"\").slice(0, -2);\n                } else {\n                    rowData = headers.map((header)=>row[i][header] || \"\");\n                }\n                worksheet.addRow(rowData);\n                if (row[i].isEmergencyAndFinanceNotComplete) {\n                    let isEmergencyAndFinanceNotCompleteSupplier = {\n                        isEmergencyAndFinanceNotComplete: row[i].isEmergencyAndFinanceNotComplete,\n                        supplierName: row[i][\"Supplier name\"]\n                    };\n                    isEmergencyAndFinanceNotCompleteObj.push(isEmergencyAndFinanceNotCompleteSupplier);\n                }\n                if (index1 == 1) {\n                    fetch(\"\".concat(serverAddress, \"suppliers/update-supplier/\").concat(row[i].id), {\n                        method: \"PUT\",\n                        headers: {\n                            Accept: \"application/json\",\n                            \"Content-Type\": \"application/json\"\n                        },\n                        credentials: \"include\",\n                        body: JSON.stringify({\n                            sectionName: \"updateStatus\",\n                            type: \"exportExcel\",\n                            status: 5,\n                            exported: true,\n                            updated_date: new Date().toISOString(),\n                            company_name: row[\"Supplier name\"],\n                            to: \"ISS\"\n                        })\n                    }).then((res)=>{\n                        if (res.status === 200) {\n                            return res.json();\n                        }\n                        // if (res.status === 401){\n                        //   toast.error(\"Your session has expired. Please log in again.\");\n                        //   setTimeout(() => {\n                        //     const redirectUrl = `/login?redirect=${encodeURIComponent(\n                        //       window.location.pathname\n                        //     )}`;\n                        //     logoutHandler(instance, redirectUrl);\n                        //   }, 3000);\n                        //   return null;\n                        // }\n                        return Promise.reject(res);\n                    }).then((json)=>{\n                        if (json.status == 200) {\n                            return true;\n                        }\n                    }).catch((error)=>{\n                        console.log(error);\n                    });\n                }\n            }\n        });\n    }\n    const buffer = await workbook.xlsx.writeBuffer();\n    const blob = new Blob([\n        buffer\n    ], {\n        type: \"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet\"\n    });\n    const blobUrl = URL.createObjectURL(blob);\n    const a = document.createElement(\"a\");\n    a.style.display = \"none\";\n    a.href = blobUrl;\n    const now = new Date();\n    const timestamp = \"\".concat(now.getFullYear(), \"-\").concat((now.getMonth() + 1).toString().padStart(2, \"0\"), \"-\").concat(now.getDate().toString().padStart(2, \"0\"), \"_\").concat(now.getHours().toString().padStart(2, \"0\"), \"-\").concat(now.getMinutes().toString().padStart(2, \"0\"), \"-\").concat(now.getSeconds().toString().padStart(2, \"0\"));\n    if (isInternal) {\n        a.download = \"internal_export_\".concat(timestamp, \"_\").concat(data[0].length - 1 === 1 ? \"S\" : \"G\", \".xlsx\");\n    } else if (isProductExtract) {\n        if (request_no && !varietyRequest) {\n            a.download = \"\".concat(request_no, \"_product_export.xlsx\");\n        } else if (request_no && varietyRequest) {\n            a.download = \"\".concat(request_no, \"_export.xlsx\");\n        } else {\n            a.download = \"product_export.xlsx\";\n        }\n    } else {\n        a.download = \"iss_export_\".concat(timestamp, \"_\").concat(data.length === 1 ? \"S\" : \"G\", \".xlsx\");\n    }\n    let fileName;\n    console.log(\"fle name\", fileName);\n    if (isInternal) {\n        fileName = \"internal_export_\".concat(timestamp, \"_\").concat(data[0].length - 1 === 1 ? \"S\" : \"G\", \".xlsx\");\n    } else if (isProductExtract && !varietyRequest) {\n        fileName = request_no ? \"\".concat(request_no, \"_product_export.xlsx\") : \"product_export.xlsx\";\n    } else if (varietyRequest) {\n        fileName = request_no ? \"\".concat(request_no, \"_NV_export.xlsx\") : \"NV_export.xlsx\";\n    } else {\n        fileName = \"iss_export_\".concat(timestamp, \"_\").concat(data.length === 1 ? \"S\" : \"G\", \".xlsx\");\n    }\n    console.log(\"fle name --- \\n\", fileName);\n    document.body.appendChild(a);\n    a.click();\n    // Clean up\n    URL.revokeObjectURL(blobUrl);\n    document.body.removeChild(a);\n    react_toastify__WEBPACK_IMPORTED_MODULE_1__.toast.success(\"Request details extracted successfully.\", {\n        toastId: 22,\n        position: \"top-right\",\n        autoClose: 3000,\n        pauseOnHover: false\n    });\n    if (!varietyRequest) {\n        const formData = new FormData();\n        formData.append(\"file\", blob, fileName);\n        formData.append(\"company\", company);\n        formData.append(\"prophet_id\", prophet_id);\n        formData.append(\"name\", userData === null || userData === void 0 ? void 0 : userData.name);\n        formData.append(\"isInternal\", isInternal);\n        formData.append(\"exporterEmail\", userData === null || userData === void 0 ? void 0 : userData.email);\n        formData.append(\"requestorEmail\", requestor_email);\n        formData.append(\"isProductRequest\", isProductExtract);\n        formData.append(\"supplierNames\", JSON.stringify(supplierNames));\n        formData.append(\"onProductSubmit\", onProductSubmit);\n        formData.append(\"request_no\", request_no);\n        formData.append(\"productEmailCommentPlaceholder\", productEmailCommentPlaceholder);\n        formData.append(\"productEmailParagraph\", productEmailParagraph);\n        const serializedData = JSON.stringify(isEmergencyAndFinanceNotCompleteObj);\n        formData.append(\"isEmergencyAndFinanceNotCompleteObj\", serializedData);\n        const response = await fetch(\"\".concat(serverAddress, \"email/send-email\"), {\n            method: \"POST\",\n            body: formData,\n            credentials: \"include\"\n        });\n        if (response.ok) {\n            react_toastify__WEBPACK_IMPORTED_MODULE_1__.toast.success(\"Email sent\", {\n                toastId: 22,\n                position: \"top-right\",\n                autoClose: 3000,\n                pauseOnHover: false\n            });\n        } else if (response.status === 401) {\n            // console.log(\"error YES 401\");\n            react_toastify__WEBPACK_IMPORTED_MODULE_1__.toast.error(\"Failed to send email\", {\n                toastId: 22,\n                position: \"top-right\",\n                autoClose: 3000,\n                pauseOnHover: false\n            });\n            return response.status;\n        } else {\n            react_toastify__WEBPACK_IMPORTED_MODULE_1__.toast.error(\"Failed to send email\", {\n                toastId: 22,\n                position: \"top-right\",\n                autoClose: 3000,\n                pauseOnHover: false\n            });\n        }\n    }\n    return true;\n};\n/* harmony default export */ __webpack_exports__[\"default\"] = (exportExcel);\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevExports = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevExports) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports on update so we can compare the boundary\n                // signatures.\n                module.hot.dispose(function (data) {\n                    data.prevExports = currentExports;\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevExports !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevExports, currentExports)) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevExports !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./utils/exportExcel.js\n"));

/***/ })

});