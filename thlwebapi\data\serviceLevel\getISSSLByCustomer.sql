WITH sales AS (
    SELECT
        ds.orddetid AS [OrdDetID],
        ds.ordnum AS [Ord Num],
        ds.order_status AS ord_status_id,
        ds.oh_type AS OrdTypeID,
        ds.delivery_date AS [Delivery Date],
        ds.canc_flag AS IS_CANCELLED,
        ds.new_line_flag,
        COALESCE(CAST(ds.od_altfilid as nvarchar(255)), 'Unknown') altfilid,
        ds.delivery_cust_code as custcode,
        ds.servicecustcode as [Service Customer],
        cust_customer.hocustcode [HO Cust Code],
        COALESCE(CAST(ds.orig_ordqty AS INT), 0) AS [Order Cases (orig)],
        COALESCE(received_qty, 0) AS [Order Cases (amended)],
        COALESCE(ds.sold_qty, 0) [Shipped Cases],
        -- COALESCE(sold_qty, 0) AS [Inv Cases],
        COALESCE(CAST(orig_ordunits AS INT), 0) AS [Order Units (orig)],
        COALESCE(CAST(received_units AS INT), 0) AS [Order Units (amended)],
        COALESCE(CAST(sold_units AS INT), 0) AS [Shipped Units],
        -- COALESCE(CAST(sold_units AS INT), 0) AS [Inv Units],
        --COALESCE(CAST(orig_orddet_weight AS DECIMAL(18, 2)), 0) AS [Order Weight (orig)],
        --COALESCE(CAST(ds.weight_g AS DECIMAL(18, 2)), 0) AS [Order Weight (amended)],
        ----COALESCE(CAST(shipped_wgt_g_cases AS DECIMAL(18, 2)), 0) AS [Shipped Weight],
        --COALESCE(CAST(ds.weight_g AS DECIMAL(18, 2)), 0) AS [Inv Weight],
        ds.unit_price
        --Gross_Line_Value,
        --Net_Line_Value
        ----shipped_gross_price_value,
        ----shipped_net_price_value
    FROM
        FLRS_DEV_TEST_ISS_DW.[dbo].vw_dm_sales_received_sold_rep ds
        LEFT JOIN FLRS_DEV_TEST_ISS_DW.[dbo].dm_altfil_rep ALT ON ALT.altfilid = ds.od_altfilid
        join FLRS_DEV_TEST_ISS_DW.base.custac_nl cust_customer on cust_customer.custcode = ds.custcode
        join FLRS_DEV_TEST_ISS_DW.base.custac_nl delcust_customer on ds.delivery_cust_code = delcust_customer.custcode
    WHERE
        --  ds.od_altfilid > 0
        ds.Provisional_Order_Flag = 0
        AND ds.delivery_date >= @start_date
        AND ds.delivery_date <= @end_date
        --AND cust_customer.catnum = 1 -- AND canc_flag = 0 cancelled products to be shown in the portal to add cancellation reasons
        AND ds.custcatnum = 1
),
allproducts AS (
    SELECT
        DISTINCT alt.product_number AS [Product Number],
        alt.countsize,
        alt.product_type,
        SPGD.business_unit [Business Unit],
        SPGD.mascode_desc AS [Master Code],
        COALESCE(CAST(ds.od_altfilid as nvarchar(255)), 'Unknown') altfilid,
        alt.TPND2 AS [Alternate Product Number],
        COALESCE(NULLIF(TRIM(alt.product_desc), ''), 'Unknown') AS [Product Desc],
        COALESCE(NULLIF(TRIM(alt.segment1), ''), 'Unknown') AS [End Customer],
        COALESCE(NULLIF(TRIM(alt.segment2), ''), 'Unknown') AS [Brand]
    FROM
        FLRS_DEV_TEST_ISS_DW.[dbo].dm_prod_group_desc_rep SPGD
        JOIN FLRS_DEV_TEST_ISS_DW.[dbo].dm_product_lvl_grouping_rep SPLG ON SPGD.mascode = SPLG.mascode
        JOIN FLRS_DEV_TEST_ISS_DW.[dbo].vw_dm_sales_received_sold_rep ds ON SPLG.prodnum = ds.od_prodnum
        JOIN FLRS_DEV_TEST_ISS_DW.[dbo].dm_altfil_rep ALT ON ALT.altfilid = ds.od_altfilid
        --LEFT JOIN FLRS_DEV_TEST_ISS_DW.[dbo].vw_finance_master_code_group vwMG ON vwMG.mascode = SPLG.mascode ----------------View does not exist for ISS
    WHERE
        -- ds.od_altfilid > 0
        ds.Provisional_Order_Flag = 0
        AND delivery_date >= @start_date
        AND delivery_date <= @end_date
),
cte_rno AS (
    SELECT
        *,
        ROW_NUMBER() OVER (
            PARTITION BY altfilid
            ORDER BY
                altfilid
        ) AS rno
    FROM
        allproducts
),
products AS (
    SELECT
        *
    FROM
        cte_rno
    WHERE
        rno = 1
	UNION
	SELECT 
		'','','Unknown','Unknown','Unknown','Unknown','','Unknown','Unknown','Unknown',''
),
CTE_Cases_Difference AS (
    SELECT
        s.[OrdDetID],
        s.[Order Cases (orig)],
        ----s.[Order Cases (amended)],
        s.[Shipped Cases],
        p.altfilid AS ALTFILID,
        p.product_type AS CATEGORY,
        p.countsize AS CASE_SIZE,
        p.[Product Desc] AS PRODUCT_DESCRIPTION,
        -- abs(s.[Order Cases (orig)] - s.[Shipped Cases]) AS CASES_DIFFERENCE,
        CASE
            WHEN s.new_line_flag = 0 THEN abs(s.[Order Cases (orig)] - s.[Shipped Cases])
            WHEN s.[Order Cases (orig)] = s.[Shipped Cases] THEN s.[Shipped Cases]
            ELSE abs(s.[Order Cases (orig)] - s.[Shipped Cases])
        END AS CASES_DIFFERENCE,
        p.[Master Code] AS MASTER_PRODUCT_CODE
    FROM
        sales s
        left JOIN products p ON p.altfilid = s.altfilid
    WHERE
        @cust_code = 'All Customers'
        OR s.[HO Cust Code] = @cust_code
),
CTE_Cases_Added_Reasons AS (
    SELECT
        s.[OrdDetID],
        COALESCE(
            SUM(slr.quantity),
            0
        ) AS CASES_ADDED_REASONS
    FROM
        sales s
        LEFT JOIN [THL_Webapp_Portal].[dbo].[sl_reasons] slr ON slr.order_id = s.[OrdDetID]
        AND slr.is_deleted = 0
    WHERE
        (
            @cust_code = 'All Customers'
            OR s.[HO Cust Code] = @cust_code
        )
        AND NOT (
            (
                s.[Order Cases (orig)] = s.[Shipped Cases]
                AND s.new_line_flag = 0
            )
            ----OR (
            ----    s.[Order Cases (orig)] = s.[Order Cases (amended)]
            ----    AND s.[Order Cases (amended)] = s.[Shipped Cases]
            ----    AND s.new_line_flag = 0
            ----)
            --OR
            --(s.[Order Cases (amended)]>[Order Cases (orig)] and [Order Cases (amended)]=[Shipped Cases])
        )
    GROUP BY
        s.[OrdDetID],
        s.[Order Cases (orig)],
        ----s.[Order Cases (amended)],
        s.[Shipped Cases]
),
CTE_locks AS (
    SELECT
        DISTINCT custcode,
        order_id,
        [user_name],
        lock_attained_at
    FROM
        sl_locks
    WHERE
        lock_active = 1
),
CTE_calender AS (
    SELECT
        iss_fiscal_wk,
        startweek,
        endweek
    FROM
        FLRS_DEV_TEST_ISS_DW.[dbo].dm_cal_start_end_week_rep
    WHERE
        calendar_name = 'ISS Financial Calendar' --AND startweek >= @start_date AND endweek <= @end_date
)
SELECT
    s.IS_CANCELLED,
    s.new_line_flag,
    s.[Service Customer] AS SERVICE_CUSTOMER,
    s.[OrdDetID] AS ORD_ID,
    s.[Ord Num] AS ORD_NUMBER,
    S.[custcode] AS CUSTOMER,
    s.[Delivery Date] AS DEPOT_DATE,
    cal.iss_fiscal_wk AS FISCAL_WEEK,
    --(
    --    CASE
    --        WHEN s.IS_CANCELLED = 1 THEN 'Cancelled'
    --        ELSE sm.descr
    --    END
    --) AS ORD_STATUS,
    sm.descr AS ORD_STATUS,
    s.[OrdTypeID] AS ORD_TYPE_ID,
    ot.descr AS ORDER_TYPE,
    ctd.ALTFILID,
    ctd.CATEGORY,
    ctd.CASE_SIZE,
    ctd.PRODUCT_DESCRIPTION,
    ctd.MASTER_PRODUCT_CODE AS MASTER_PRODUCT_CODE,
    s.[Order Cases (orig)] AS CASES_ORIGINAL,
    ----s.[Order Cases (amended)] AS CASES_AMENDED,
    s.[Shipped Cases] AS CASES_DELIVERED,
    ctd.CASES_DIFFERENCE,
    CASE
        WHEN s.[Order Cases (orig)] > 0 THEN (
            (
                CAST(s.[Shipped Cases] AS DECIMAL) / s.[Order Cases (orig)]
            ) * 100
        )
        ELSE 0
    END AS SERVICE_LEVEL_PERCENT,
    cnr.CASES_ADDED_REASONS AS CASES_ADDED_REASONS,
    case
        when IS_CANCELLED = 1 then uomnprice
        else unit_price
    end as UNIT_PRICE,
    --unit_price as UNIT_PRICE,
    CASE
        WHEN IS_CANCELLED = 1 THEN uomnprice
        ELSE unit_price
    END * ctd.CASES_DIFFERENCE AS TOTAL_VALUE,
    slr.id AS REASON_ID,
    slr.reason_id AS MAIN_REASON_ID,
    rmm.reason AS MAIN_REASON,
    slr.subreason_id AS SUB_REASON_ID,
    rms.reason AS SUB_REASON,
    slr.added_by AS REASON_ADDED_BY,
    slr.quantity AS REASON_QTY,
    slr.comment AS REASON_COMMENT,
    slr.updated_by AS REASON_UPDATED_BY,
    slr.[added_timestamp] AS REASON_ADDED_TIMESTAMP,
    slr.[updated_timestamp] as REASON_UPDATED_TIMESTAMP,
    l.[user_name] AS LOCKED_BY
FROM
    sales s
    JOIN CTE_Cases_Difference ctd ON ctd.[OrdDetID] = s.[OrdDetID]
    JOIN FLRS_DEV_TEST_ISS_DW.[base].[orderstatusdesc_nl] sm ON sm.[status] = s.ord_status_id
    JOIN FLRS_DEV_TEST_ISS_DW.[base].[ordertypedesc_nl] ot ON ot.type = s.[OrdTypeID] --LEFT JOIN cte_customer cust on cust.[Cust Code] = s.custcode
    LEFT JOIN CTE_calender cal ON s.[Delivery Date] >= cal.startweek
    AND s.[Delivery Date] <= cal.endweek
    LEFT JOIN CTE_Cases_Added_Reasons cnr ON cnr.[OrdDetID] = s.[OrdDetID]
    LEFT JOIN [THL_Webapp_Portal].[dbo].[sl_reasons] slr ON slr.order_id = s.[OrdDetID]
    AND slr.is_deleted = 0
    LEFT JOIN CTE_locks l ON l.custcode COLLATE DATABASE_DEFAULT = s.custcode COLLATE DATABASE_DEFAULT
    AND l.order_id = s.[OrdDetID]
    LEFT JOIN [THL_Webapp_Portal].[dbo].[sl_reasons_master] rmm ON rmm.id = slr.reason_id
    LEFT JOIN [THL_Webapp_Portal].[dbo].[sl_reasons_master] rms ON rms.id = slr.subreason_id
    left join FLRS_DEV_TEST_ISS_DW.[dbo].dm_dim_sales_audit_price_rep ap on s.OrdDetID = ap.orddetid
WHERE
    (
        @cust_code = 'All Customers'
        OR s.[HO Cust Code] = @cust_code
    )
    AND
    (
    @serviceCustomer= 'All Service Customers'
        OR s.[Service Customer] =@serviceCustomer
    )
    AND NOT (
        (
            s.[Order Cases (orig)] = s.[Shipped Cases]
            AND s.new_line_flag = 0 --AND s.[Order Cases (amended)] = 0
        )
        ----OR (
        ----    s.[Order Cases (orig)] = s.[Order Cases (amended)]
        ----    AND s.[Order Cases (amended)] = s.[Shipped Cases]
        ----    AND s.new_line_flag = 0
        ----)
        --OR
        --(s.[Order Cases (amended)]>[Order Cases (orig)] and [Order Cases (amended)]=[Shipped Cases])
    )
    AND (
        @get_all = 1
        OR (
            @get_all = 0
            AND (
                ctd.CASES_DIFFERENCE - cnr.CASES_ADDED_REASONS <> 0
                or s.new_line_flag = 1
            )
        )
    )
    AND s.[OrdTypeID] IN (5)
    AND (
        @orderId is NULL
        AND 1 = 1
        OR (
            @orderId is not NULL
            AND s.[OrdDetID] = @orderId
        )
    )
    and (
        uomnprice > 0
        or uomnprice is null
    )
ORDER BY 
    s.[Delivery Date],
    s.[custcode],
    ctd.PRODUCT_DESCRIPTION