"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/raw-material-request/[productId]/edit",{

/***/ "./pages/raw-material-request/[productId]/edit/index.js":
/*!**************************************************************!*\
  !*** ./pages/raw-material-request/[productId]/edit/index.js ***!
  \**************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __N_SSP: function() { return /* binding */ __N_SSP; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _components_Layout__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/Layout */ \"./components/Layout.js\");\n/* harmony import */ var _components_RawMaterialRequest__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/RawMaterialRequest */ \"./components/RawMaterialRequest.js\");\n/* harmony import */ var _services_apiConfig__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/services/apiConfig */ \"./services/apiConfig.js\");\n/* harmony import */ var _utils_extractCompanyFromEmail__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/utils/extractCompanyFromEmail */ \"./utils/extractCompanyFromEmail.js\");\n/* harmony import */ var _utils_secureStorage__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/utils/secureStorage */ \"./utils/secureStorage.js\");\n/* harmony import */ var js_cookie__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! js-cookie */ \"./node_modules/js-cookie/dist/js.cookie.mjs\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_7__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_8___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_8__);\n/* harmony import */ var react_loader_spinner__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! react-loader-spinner */ \"./node_modules/react-loader-spinner/dist/module.js\");\n/* harmony import */ var react_toastify__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! react-toastify */ \"./node_modules/react-toastify/dist/react-toastify.esm.mjs\");\n/* harmony import */ var react_toastify_dist_ReactToastify_css__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! react-toastify/dist/ReactToastify.css */ \"./node_modules/react-toastify/dist/ReactToastify.css\");\n/* harmony import */ var react_toastify_dist_ReactToastify_css__WEBPACK_IMPORTED_MODULE_11___default = /*#__PURE__*/__webpack_require__.n(react_toastify_dist_ReactToastify_css__WEBPACK_IMPORTED_MODULE_11__);\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\nconst index = (param)=>{\n    let { userData } = param;\n    _s();\n    const [dropdowns, setDropdowns] = (0,react__WEBPACK_IMPORTED_MODULE_8__.useState)(null);\n    const [rawMaterialData, setRawMaterialData] = (0,react__WEBPACK_IMPORTED_MODULE_8__.useState)(null);\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_7__.useRouter)();\n    const { productId } = router.query;\n    (0,react__WEBPACK_IMPORTED_MODULE_8__.useEffect)(()=>{\n        const company = (userData === null || userData === void 0 ? void 0 : userData.company) || (0,_utils_extractCompanyFromEmail__WEBPACK_IMPORTED_MODULE_4__.extractCompanyFromEmail)(userData === null || userData === void 0 ? void 0 : userData.email);\n        let prophetId = 1;\n        if (company == \"dpsltd\") {\n            prophetId = 1;\n        } else if (company == \"efcltd\") {\n            prophetId = 3;\n        } else if (company == \"fpp-ltd\") {\n            prophetId = 4;\n        }\n        const fetchData = async ()=>{\n            const serverAddress = _services_apiConfig__WEBPACK_IMPORTED_MODULE_3__.apiConfig.serverAddress;\n            try {\n                const allDropDowns = [\n                    \"reasonForRequest\",\n                    \"masterProductCode\",\n                    \"markVariety\",\n                    \"productType\",\n                    \"organicCertification\",\n                    \"temperatureGrade\",\n                    \"intrastatCommodityCode\",\n                    \"classifiedAllergicTypes\",\n                    \"countryOfOrigin\",\n                    \"brand\",\n                    \"caliberSize\",\n                    \"endCustomer\",\n                    \"variety\",\n                    \"subProductCode\"\n                ];\n                const productType = 0;\n                const dropdownsRequest = await fetch(\"\".concat(serverAddress, \"products/get-products-dropdowns-list?prophetId=\").concat(prophetId, \"&productType=\").concat(productType), {\n                    method: \"POST\",\n                    headers: {\n                        Accept: \"application/json\",\n                        \"Content-Type\": \"application/json\"\n                    },\n                    credentials: \"include\",\n                    body: JSON.stringify(allDropDowns)\n                });\n                // console.log(\"dropdownsRequest\",dropdownsRequest);\n                if (dropdownsRequest.status === 401) {\n                    react_toastify__WEBPACK_IMPORTED_MODULE_10__.toast.error(\"Your session has expired. Please log in again.\");\n                    setTimeout(async ()=>{\n                        await (0,_utils_secureStorage__WEBPACK_IMPORTED_MODULE_5__.logout)();\n                        const redirectUrl = \"/login?redirect=\".concat(encodeURIComponent(window.location.pathname));\n                        router.push(redirectUrl);\n                    }, 3000);\n                }\n                if (dropdownsRequest.status === 400) {\n                    react_toastify__WEBPACK_IMPORTED_MODULE_10__.toast.error(\"There was an error with your request. Please check your data and try again.\");\n                }\n                const rawMaterialRequest = fetch(\"\".concat(serverAddress, \"products/get-raw-materials-by-id/\").concat(productId), {\n                    method: \"GET\",\n                    headers: {\n                        Accept: \"application/json\",\n                        \"Content-Type\": \"application/json\"\n                    },\n                    credentials: \"include\"\n                });\n                if (rawMaterialRequest.status === 401) {\n                    react_toastify__WEBPACK_IMPORTED_MODULE_10__.toast.error(\"Your session has expired. Please log in again.\");\n                    setTimeout(async ()=>{\n                        await (0,_utils_secureStorage__WEBPACK_IMPORTED_MODULE_5__.logout)();\n                        const redirectUrl = \"/login?redirect=\".concat(encodeURIComponent(window.location.pathname));\n                        router.push(redirectUrl);\n                    }, 3000);\n                }\n                if (rawMaterialRequest.status === 400) {\n                    react_toastify__WEBPACK_IMPORTED_MODULE_10__.toast.error(\"There was an error with your request. Please check your data and try again.\");\n                }\n                const [dropdownsResponse, rawMaterialResponse] = await Promise.all([\n                    dropdownsRequest,\n                    rawMaterialRequest\n                ]);\n                const allDropdownsList = await dropdownsResponse.json();\n                const rawMaterialData = await rawMaterialResponse.json();\n                // console.log(\"dropdowns\", allDropdownsList);\n                // console.log(\"raw material data\", rawMaterialData);\n                setDropdowns(allDropdownsList);\n                setRawMaterialData(rawMaterialData);\n            } catch (error) {\n                console.error(\"Error fetching data\", error);\n                react_toastify__WEBPACK_IMPORTED_MODULE_10__.toast.error(\"There was an error with your request. Please check your data and try again.\");\n            }\n        };\n        if (productId) {\n            fetchData();\n        }\n    }, [\n        productId\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Layout__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n        userData: userData,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_toastify__WEBPACK_IMPORTED_MODULE_10__.ToastContainer, {\n                limit: 1\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\raw-material-request\\\\[productId]\\\\edit\\\\index.js\",\n                lineNumber: 135,\n                columnNumber: 7\n            }, undefined),\n            !dropdowns && !rawMaterialData ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    display: \"flex\",\n                    alignItems: \"center\",\n                    justifyContent: \"center\",\n                    height: \"100vh\"\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_loader_spinner__WEBPACK_IMPORTED_MODULE_9__.ThreeCircles, {\n                    color: \"#002D73\",\n                    height: 50,\n                    width: 50,\n                    visible: true,\n                    ariaLabel: \"oval-loading\",\n                    secondaryColor: \"#0066FF\",\n                    strokeWidth: 2,\n                    strokeWidthSecondary: 2\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\raw-material-request\\\\[productId]\\\\edit\\\\index.js\",\n                    lineNumber: 145,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\raw-material-request\\\\[productId]\\\\edit\\\\index.js\",\n                lineNumber: 137,\n                columnNumber: 9\n            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_RawMaterialRequest__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                dropdowns: dropdowns,\n                userData: userData,\n                rawMaterialData: rawMaterialData,\n                pageType: \"update\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\raw-material-request\\\\[productId]\\\\edit\\\\index.js\",\n                lineNumber: 157,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\pages\\\\raw-material-request\\\\[productId]\\\\edit\\\\index.js\",\n        lineNumber: 134,\n        columnNumber: 5\n    }, undefined);\n};\n_s(index, \"ZiNiUt9ey4zIrA4z+mYzQjxuVaE=\", false, function() {\n    return [\n        next_router__WEBPACK_IMPORTED_MODULE_7__.useRouter\n    ];\n});\nvar __N_SSP = true;\n/* harmony default export */ __webpack_exports__[\"default\"] = (index);\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevExports = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevExports) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports on update so we can compare the boundary\n                // signatures.\n                module.hot.dispose(function (data) {\n                    data.prevExports = currentExports;\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevExports !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevExports, currentExports)) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevExports !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./pages/raw-material-request/[productId]/edit/index.js\n"));

/***/ }),

/***/ "./utils/extractCompanyFromEmail.js":
/*!******************************************!*\
  !*** ./utils/extractCompanyFromEmail.js ***!
  \******************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   extractCompanyFromEmail: function() { return /* binding */ extractCompanyFromEmail; }\n/* harmony export */ });\n// Helper function to extract company from email\nconst extractCompanyFromEmail = (email)=>{\n    if (!email) return \"unknown\";\n    const domain = email.split(\"@\")[1];\n    return domain.split(\".\")[0];\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevExports = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevExports) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports on update so we can compare the boundary\n                // signatures.\n                module.hot.dispose(function (data) {\n                    data.prevExports = currentExports;\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevExports !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevExports, currentExports)) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevExports !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi91dGlscy9leHRyYWN0Q29tcGFueUZyb21FbWFpbC5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUUsZ0RBQWdEO0FBQ3pDLE1BQU1BLDBCQUEwQixDQUFDQztJQUN0QyxJQUFJLENBQUNBLE9BQU8sT0FBTztJQUNuQixNQUFNQyxTQUFTRCxNQUFNRSxLQUFLLENBQUMsSUFBSSxDQUFDLEVBQUU7SUFDbEMsT0FBT0QsT0FBT0MsS0FBSyxDQUFDLElBQUksQ0FBQyxFQUFFO0FBQzdCLEVBQUUiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vdXRpbHMvZXh0cmFjdENvbXBhbnlGcm9tRW1haWwuanM/MDQ0OSJdLCJzb3VyY2VzQ29udGVudCI6WyIgIC8vIEhlbHBlciBmdW5jdGlvbiB0byBleHRyYWN0IGNvbXBhbnkgZnJvbSBlbWFpbFxyXG4gIGV4cG9ydCBjb25zdCBleHRyYWN0Q29tcGFueUZyb21FbWFpbCA9IChlbWFpbCkgPT4ge1xyXG4gICAgaWYgKCFlbWFpbCkgcmV0dXJuICd1bmtub3duJztcclxuICAgIGNvbnN0IGRvbWFpbiA9IGVtYWlsLnNwbGl0KFwiQFwiKVsxXTtcclxuICAgIHJldHVybiBkb21haW4uc3BsaXQoXCIuXCIpWzBdO1xyXG4gIH07Il0sIm5hbWVzIjpbImV4dHJhY3RDb21wYW55RnJvbUVtYWlsIiwiZW1haWwiLCJkb21haW4iLCJzcGxpdCJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///./utils/extractCompanyFromEmail.js\n"));

/***/ })

});