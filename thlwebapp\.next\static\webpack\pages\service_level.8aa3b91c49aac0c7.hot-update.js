"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/service_level",{

/***/ "./components/service_level/SLTable.js":
/*!*********************************************!*\
  !*** ./components/service_level/SLTable.js ***!
  \*********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _ViewDetails__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./ViewDetails */ \"./components/service_level/ViewDetails.jsx\");\n/* harmony import */ var _services_apiConfig__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/services/apiConfig */ \"./services/apiConfig.js\");\n/* harmony import */ var _ReasonsDetails__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./ReasonsDetails */ \"./components/service_level/ReasonsDetails.jsx\");\n/* harmony import */ var _utils_getCookieData__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/utils/getCookieData */ \"./utils/getCookieData.js\");\n/* harmony import */ var _common_NoDataFound__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../common/NoDataFound */ \"./components/common/NoDataFound.jsx\");\n/* harmony import */ var _fluentui_react_components__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @fluentui/react-components */ \"./node_modules/@fluentui/react-components/lib/index.js\");\n/* harmony import */ var react_toastify__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! react-toastify */ \"./node_modules/react-toastify/dist/react-toastify.esm.mjs\");\n/* harmony import */ var js_cookie__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! js-cookie */ \"./node_modules/js-cookie/dist/js.cookie.mjs\");\n/* harmony import */ var _utils_secureStorage__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/utils/secureStorage */ \"./utils/secureStorage.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_10___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_10__);\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n//\n\n\n\nconst SLTable = (param)=>{\n    let { gridcolumnRefs, gridcolumnWidths, setIsTableRendered, checkedStates, customerSLData, userData, selectedProducts, seeAll, recordCount, setRecordsCount, searchBoxContent, slFilters, selectedMasterProductCode, selectedCustomer, toggle, selectedRows, setSelectedRows, isBulkUpdate, setIsBulkUpdate, bulkUpdateData, isOpen, setIsOpen, setBulkUpdateData, masterProducts, bulkDeleteOrdIds, setBulkDeleteOrdIds, setNoDataExists, setShowLoadingMessage, setAllReasonsSubreasons, allReasonsSubreasons, selectedReasons, setSelectedReasons, selectedSubReasons, setSelectedSubReasons, showLoadingMessage } = param;\n    var _checkedStates_columns, _checkedStates_columns1, _checkedStates_columns2, _checkedStates_columns3, _checkedStates_columns4, _checkedStates_columns5, _checkedStates_columns6, _columnTotals_totalValue;\n    _s();\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_10__.useRouter)();\n    let ADCompanyName = (userData === null || userData === void 0 ? void 0 : userData.companyName) || (userData === null || userData === void 0 ? void 0 : userData.ADCompanyName);\n    const [reasonsMasterList, setReasonsMasterList] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [parentReasonList, setParentReasonList] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [reasonsData, setReasonsData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [allSelectedProducts, setAllSelectedProducts] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isHeaderChecked, setIsHeaderChecked] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [columnTotals, setColumnTotals] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const setMapForReasonsParentsAndTheirCorrespondingChildren = ()=>{\n        setParentReasonList((prevList)=>{\n            const parentReasonIds = reasonsMasterList.filter((typeOfReason)=>typeOfReason.parent_id === null).map((typeOfReason)=>typeOfReason);\n            const uniqueParentIds = new Set([\n                ...prevList,\n                ...parentReasonIds\n            ]);\n            return Array.from(uniqueParentIds); // Update the state\n        });\n    };\n    const handleCheckboxChange = (data)=>{\n        setSelectedRows((prevSelected)=>{\n            let updatedSelected;\n            if (prevSelected.includes(data)) {\n                updatedSelected = prevSelected.filter((item)=>item !== data);\n            } else {\n                updatedSelected = [\n                    ...prevSelected,\n                    data\n                ];\n            }\n            const ordIds = updatedSelected.filter((product)=>product.CASES_ADDED_REASONS > 0).map((product)=>product.ORD_ID);\n            setBulkDeleteOrdIds(ordIds.length > 0 ? ordIds : []);\n            return updatedSelected;\n        });\n    };\n    const handleHeaderCheckboxChange = (data)=>{\n        const selectableRows = data.filter((product)=>(product.LOCKED_BY === null || product.LOCKED_BY === \"\") && product.CASES_DIFFERENCE != 0);\n        const ordIds = selectableRows.filter((product)=>product.CASES_ADDED_REASONS > 0).map((product)=>product.ORD_ID);\n        if (ordIds.length > 0) {\n            setBulkDeleteOrdIds(ordIds);\n        } else {\n            setBulkDeleteOrdIds([]);\n        }\n        if (selectedRows.length === selectableRows.length) {\n            // If all rows are selected, deselect all\n            setSelectedRows([]);\n            setIsHeaderChecked(false);\n        } else {\n            // If not all rows are selected, select all\n            setSelectedRows(selectableRows);\n            setIsHeaderChecked(true);\n        }\n    };\n    //#region getReasons\n    const fetchReasonData = async (orderId, customerName)=>{\n        setReasonsData([]);\n        const serverAddress = _services_apiConfig__WEBPACK_IMPORTED_MODULE_3__.apiConfig.serverAddress;\n        try {\n            const serviceLevelReasons = await fetch(\"\".concat(serverAddress, \"serviceLevel/get-service-level-reasons/\").concat(orderId, \"?customerName=\").concat(customerName), {\n                method: \"GET\",\n                headers: {\n                    Accept: \"application/json\",\n                    \"Content-Type\": \"application/json\"\n                },\n                credentials: \"include\"\n            }).then((res)=>{\n                if (res.status === 400) {\n                    react_toastify__WEBPACK_IMPORTED_MODULE_7__.toast.error(\"There was an error with your request. Please check your data and try again.\");\n                    return null;\n                } else if (res.status === 401) {\n                    react_toastify__WEBPACK_IMPORTED_MODULE_7__.toast.error(\"Your session has expired. Please log in again.\");\n                    setTimeout(async ()=>{\n                        await (0,_utils_secureStorage__WEBPACK_IMPORTED_MODULE_9__.logout)();\n                        const redirectUrl = \"/login?redirect=\".concat(encodeURIComponent(window.location.pathname));\n                        router.push(redirectUrl);\n                    }, 3000);\n                    return null;\n                }\n                if (res.status === 200) {\n                    return res.json();\n                }\n            }) // Ensure you parse the JSON\n            .then((data)=>{\n                if (!isBulkUpdate) {\n                    setReasonsData(data);\n                }\n            });\n        } catch (error) {\n            console.log(\"error in fetching\", error);\n            react_toastify__WEBPACK_IMPORTED_MODULE_7__.toast.error(\"Error fetching reasons:\".concat(error.message), {\n                position: \"top-right\"\n            });\n        }\n    };\n    //#endregion\n    //#region getMasterReasons\n    const fetchReasonMaster = async ()=>{\n        // console.log(\"service Level Reasons Master code start\");\n        try {\n            const serverAddress = _services_apiConfig__WEBPACK_IMPORTED_MODULE_3__.apiConfig.serverAddress;\n            const serviceLevelReasonsMaster = await fetch(\"\".concat(serverAddress, \"serviceLevel/get-service-level-reasons-master\"), {\n                method: \"GET\",\n                headers: {\n                    Accept: \"application/json\",\n                    \"Content-Type\": \"application/json\"\n                },\n                credentials: \"include\"\n            }).then((res)=>{\n                if (res.status === 400) {\n                    react_toastify__WEBPACK_IMPORTED_MODULE_7__.toast.error(\"There was an error with your request. Please check your data and try again.\");\n                    return null;\n                } else if (res.status === 401) {\n                    react_toastify__WEBPACK_IMPORTED_MODULE_7__.toast.error(\"Your session has expired. Please log in again.\");\n                    setTimeout(async ()=>{\n                        await (0,_utils_secureStorage__WEBPACK_IMPORTED_MODULE_9__.logout)();\n                        const redirectUrl = \"/login?redirect=\".concat(encodeURIComponent(window.location.pathname));\n                        router.push(redirectUrl);\n                    }, 3000);\n                    return null;\n                }\n                if (res.status === 200) {\n                    return res.json();\n                }\n            }) // Ensure you parse the JSON\n            .then((data)=>{\n                setReasonsMasterList(data);\n            });\n        } catch (err) {\n            console.log(\"error in fetching\", err);\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        setIsTableRendered((prev)=>!prev);\n    }, [\n        setIsTableRendered\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        fetchReasonMaster(); // To fetch the master list of reasons\n        setMapForReasonsParentsAndTheirCorrespondingChildren();\n    }, []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const cookieSelectedProducts = (slFilters === null || slFilters === void 0 ? void 0 : slFilters.selectedProducts) || [];\n        const stateSelectedProducts = selectedProducts || [];\n        const combinedSelectedProducts = [\n            ...cookieSelectedProducts.map((product)=>({\n                    label: product.productDescription,\n                    value: product.altFillId\n                })),\n            ...stateSelectedProducts.map((product)=>({\n                    label: product.label,\n                    value: product.value\n                }))\n        ];\n        const customerSLArray = Object.values(customerSLData);\n        let filteredProducts = combinedSelectedProducts.length ? customerSLArray.filter((product)=>combinedSelectedProducts.some((selectedProduct)=>selectedProduct.label === product.PRODUCT_DESCRIPTION && (!searchBoxContent || !!searchBoxContent && product.ORD_NUMBER.toString().includes(searchBoxContent)))) : customerSLArray.filter((product)=>!searchBoxContent || !!searchBoxContent && product.ORD_NUMBER.toString().includes(searchBoxContent));\n        if (selectedMasterProductCode !== \"all\") {\n            filteredProducts = filteredProducts.filter((prod)=>prod.MASTER_PRODUCT_CODE == selectedMasterProductCode);\n        }\n        if (selectedReasons.length > 0) {\n            filteredProducts = filteredProducts.filter((product)=>product.reasons && product.reasons.some((reason)=>selectedReasons.includes(reason.MAIN_REASON_ID)));\n        } else if (selectedSubReasons.length > 0) {\n            filteredProducts = filteredProducts.filter((product)=>product.reasons && product.reasons.some((reason)=>selectedSubReasons.includes(reason.SUB_REASON_ID)));\n        }\n        if (filteredProducts.length > 0) {\n            setAllSelectedProducts(filteredProducts);\n            const visibleRows = filteredProducts.filter((data)=>{\n                if (!seeAll && data.CASES_DIFFERENCE - data.CASES_ADDED_REASONS === 0) {\n                    return false;\n                }\n                return true;\n            });\n            const totals = visibleRows.reduce((acc, data)=>{\n                acc.casesOrdered += data.CASES_ORIGINAL || 0;\n                acc.casesDelivered += data.CASES_DELIVERED || 0;\n                acc.casesDifference += data.CASES_DIFFERENCE || 0;\n                acc.addedReasons += data.CASES_ADDED_REASONS || 0;\n                acc.totalValue += (data.CASE_SIZE || 0) * (data.UNIT_PRICE || 0) * (data.CASES_DIFFERENCE || 0);\n                return acc;\n            }, {\n                casesOrdered: 0,\n                casesDelivered: 0,\n                casesDifference: 0,\n                addedReasons: 0,\n                totalValue: 0\n            });\n            setColumnTotals(totals);\n        }\n        var _filteredProducts_length;\n        setRecordsCount((_filteredProducts_length = filteredProducts === null || filteredProducts === void 0 ? void 0 : filteredProducts.length) !== null && _filteredProducts_length !== void 0 ? _filteredProducts_length : 0);\n    }, [\n        toggle,\n        customerSLData,\n        selectedProducts,\n        slFilters,\n        searchBoxContent,\n        selectedMasterProductCode,\n        selectedReasons,\n        selectedSubReasons\n    ]);\n    const [selectedData, setSelectedData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const handleViewDetailsClick = (data)=>{\n        setSelectedRows([\n            data\n        ]);\n        setIsOpen(true);\n    };\n    var _columnTotals_casesOrdered, _columnTotals_casesDelivered, _columnTotals_casesDifference, _columnTotals_addedReasons, _columnTotals_totalValue1;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"!fontFamily-poppinsregular\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_toastify__WEBPACK_IMPORTED_MODULE_7__.ToastContainer, {\n                limit: 1\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                lineNumber: 322,\n                columnNumber: 7\n            }, undefined),\n            recordCount != 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                className: \"service-level-grid relative table-fixed w-full text-sm !fontFamily-poppinsregular\",\n                cellSpacing: 0,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                    className: \"sticky top-0 z-10 !w-10\",\n                                    ref: gridcolumnRefs.checkboxRef,\n                                    style: {\n                                        left: \"\".concat(0, \"px\")\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-full flex justify-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"checkbox\",\n                                            className: \"w-5 h-5 text-blue border-theme-blue2 rounded accent-skin-primary text-center\",\n                                            disabled: selectedMasterProductCode == \"all\" && masterProducts.length > 2 || selectedCustomer == \"All Customers\",\n                                            onChange: ()=>handleHeaderCheckboxChange(allSelectedProducts),\n                                            checked: isHeaderChecked\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                            lineNumber: 337,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                        lineNumber: 336,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                    lineNumber: 331,\n                                    columnNumber: 15\n                                }, undefined),\n                                checkedStates.columns.depotdate && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                    className: \"sticky top-0 z-10\",\n                                    ref: gridcolumnRefs.depotdate,\n                                    style: {\n                                        left: \"\".concat(gridcolumnWidths.checkboxWidth, \"px\")\n                                    },\n                                    children: \"Depot Date\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                    lineNumber: 353,\n                                    columnNumber: 17\n                                }, undefined),\n                                ADCompanyName == \"Integrated Service Solutions Ltd\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                    className: \"sticky top-0 z-10 !text-center\",\n                                    ref: gridcolumnRefs.serviceCustomers,\n                                    style: {\n                                        left: \"\".concat(gridcolumnWidths.checkboxWidth, \"px\")\n                                    },\n                                    children: [\n                                        \"Service Customers\",\n                                        \" \"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                    lineNumber: 361,\n                                    columnNumber: 69\n                                }, undefined),\n                                checkedStates.columns.weekNo && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                    className: \"sticky top-0  z-10\",\n                                    ref: gridcolumnRefs.weekNo,\n                                    style: {\n                                        left: \"\".concat(ADCompanyName == \"Integrated Service Solutions Ltd\" ? gridcolumnWidths.serviceCustomerswidth : gridcolumnWidths.depotdate, \"px\")\n                                    },\n                                    children: \"Week No\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                    lineNumber: 370,\n                                    columnNumber: 17\n                                }, undefined),\n                                checkedStates.columns.altfill && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                    className: \"sticky top-0  z-10\",\n                                    ref: gridcolumnRefs.altfill,\n                                    style: {\n                                        left: \"\".concat(gridcolumnWidths.weekNo, \"px\")\n                                    },\n                                    children: \"Alt Fill\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                    lineNumber: 381,\n                                    columnNumber: 17\n                                }, undefined),\n                                checkedStates.columns.customer && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                    className: \"!w-28 sticky top-0  z-10 \",\n                                    ref: gridcolumnRefs.customer,\n                                    style: {\n                                        left: \"\".concat(gridcolumnWidths.altfill, \"px\")\n                                    },\n                                    children: \"Customer\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                    lineNumber: 390,\n                                    columnNumber: 17\n                                }, undefined),\n                                checkedStates.columns.salesorder && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                    className: \"sticky top-0  z-10 \",\n                                    ref: gridcolumnRefs.salesorder,\n                                    style: {\n                                        left: \"\".concat(gridcolumnWidths.customer, \"px\")\n                                    },\n                                    children: \"Sales Order\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                    lineNumber: 399,\n                                    columnNumber: 17\n                                }, undefined),\n                                checkedStates.columns.salesOrderId && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                    className: \"sticky top-0  z-10 \",\n                                    ref: gridcolumnRefs.salesOrderId,\n                                    style: {\n                                        left: \"\".concat(gridcolumnWidths.customer, \"px\")\n                                    },\n                                    children: \"Order Det Id\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                    lineNumber: 408,\n                                    columnNumber: 17\n                                }, undefined),\n                                checkedStates.columns.product && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                    className: \"!w-80 sticky top-0  z-10 \",\n                                    ref: gridcolumnRefs.product,\n                                    style: {\n                                        left: \"\".concat(gridcolumnWidths.salesorder, \"px\")\n                                    },\n                                    children: \"Product\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                    lineNumber: 417,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                    className: \"top-0  z-10 !text-center\",\n                                    ref: gridcolumnRefs.casesize,\n                                    style: {\n                                        left: \"\".concat(gridcolumnWidths.product, \"px\")\n                                    },\n                                    children: \"Case Size\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                    lineNumber: 425,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                    className: \"sticky top-0 !text-center\",\n                                    children: \"Cases Ordered\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                    lineNumber: 432,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                    className: \"sticky top-0 !text-center\",\n                                    children: \"Cases Delivered\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                    lineNumber: 433,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                    className: \"sticky top-0 !text-center\",\n                                    children: \"Cases Different\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                    lineNumber: 434,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                    className: \"sticky top-0 !text-center\",\n                                    children: \"Added Reasons\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                    lineNumber: 435,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                    className: \"!w-28 sticky top-0 !text-center\",\n                                    children: \"Order Fulfillment %\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                    lineNumber: 436,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                    className: \"sticky top-0 !text-center\",\n                                    children: \"Unit Price\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                    lineNumber: 439,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                    className: \"sticky top-0 !text-center\",\n                                    children: \"Case Value\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                    lineNumber: 440,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                    className: \"sticky top-0 !text-center\",\n                                    children: \"Total Value\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                    lineNumber: 441,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                    className: \"sticky top-0 !text-center\",\n                                    children: \"Status\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                    lineNumber: 442,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                    className: \"!w-60 relative\",\n                                    children: [\n                                        \"Reason\",\n                                        \" \",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ReasonsDetails__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                            reasonsData: allReasonsSubreasons.reasons,\n                                            selectedReasons: selectedReasons,\n                                            selectedSubReasons: selectedSubReasons,\n                                            setSelectedReasons: setSelectedReasons,\n                                            setSelectedSubReasons: setSelectedSubReasons,\n                                            type: \"reasonsList\",\n                                            seeAll: seeAll\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                            lineNumber: 445,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                    lineNumber: 443,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                    className: \"!w-60 relative\",\n                                    children: [\n                                        \"Sub Reason\",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ReasonsDetails__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                            reasonsData: allReasonsSubreasons.subReasons,\n                                            selectedReasons: selectedReasons,\n                                            selectedSubReasons: selectedSubReasons,\n                                            setSelectedReasons: setSelectedReasons,\n                                            setSelectedSubReasons: setSelectedSubReasons,\n                                            type: \"subReasonsList\",\n                                            seeAll: seeAll\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                            lineNumber: 457,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                    lineNumber: 455,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                    className: \"!w-52\",\n                                    children: \"Comments\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                    lineNumber: 467,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                    className: \"!text-center\",\n                                    children: \"Actions\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                    lineNumber: 468,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                            lineNumber: 330,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                        lineNumber: 329,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                className: \"font-bold bg-[#f3f8ff]\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                        className: \"text-center sticky top-0 font-bold text-sm\",\n                                        style: {\n                                            left: \"\".concat(0, \"px\")\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                        lineNumber: 474,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    (checkedStates === null || checkedStates === void 0 ? void 0 : (_checkedStates_columns = checkedStates.columns) === null || _checkedStates_columns === void 0 ? void 0 : _checkedStates_columns.depotdate) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                        className: \"text-center sticky top-0 font-bold text-sm\",\n                                        style: {\n                                            left: \"\".concat(gridcolumnWidths.checkboxWidth, \"px\")\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                        lineNumber: 479,\n                                        columnNumber: 19\n                                    }, undefined),\n                                    ADCompanyName == \"Integrated Service Solutions Ltd\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                        className: \"text-center sticky top-0 font-bold text-sm\",\n                                        style: {\n                                            left: \"\".concat(gridcolumnWidths.depotDate, \"px\")\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                        lineNumber: 484,\n                                        columnNumber: 71\n                                    }, undefined),\n                                    (checkedStates === null || checkedStates === void 0 ? void 0 : (_checkedStates_columns1 = checkedStates.columns) === null || _checkedStates_columns1 === void 0 ? void 0 : _checkedStates_columns1.weekNo) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                        className: \"sticky top-0 font-bold text-center text-sm\",\n                                        style: {\n                                            left: \"\".concat(gridcolumnWidths.serviceCustomerswidth, \"px\")\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                        lineNumber: 490,\n                                        columnNumber: 19\n                                    }, undefined),\n                                    (checkedStates === null || checkedStates === void 0 ? void 0 : (_checkedStates_columns2 = checkedStates.columns) === null || _checkedStates_columns2 === void 0 ? void 0 : _checkedStates_columns2.altfill) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                        className: \"sticky top-0 font-bold text-sm\",\n                                        style: {\n                                            left: \"\".concat(gridcolumnWidths.weekNo, \"px\")\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                        lineNumber: 496,\n                                        columnNumber: 19\n                                    }, undefined),\n                                    (checkedStates === null || checkedStates === void 0 ? void 0 : (_checkedStates_columns3 = checkedStates.columns) === null || _checkedStates_columns3 === void 0 ? void 0 : _checkedStates_columns3.customer) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                        className: \"sticky top-0 font-bold text-sm\",\n                                        style: {\n                                            left: \"\".concat(gridcolumnWidths.altfill, \"px\")\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                        lineNumber: 502,\n                                        columnNumber: 19\n                                    }, undefined),\n                                    (checkedStates === null || checkedStates === void 0 ? void 0 : (_checkedStates_columns4 = checkedStates.columns) === null || _checkedStates_columns4 === void 0 ? void 0 : _checkedStates_columns4.salesorder) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                        className: \"sticky top-0 font-bold text-sm\",\n                                        style: {\n                                            left: \"\".concat(gridcolumnWidths.customer, \"px\")\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                        lineNumber: 508,\n                                        columnNumber: 19\n                                    }, undefined),\n                                    (checkedStates === null || checkedStates === void 0 ? void 0 : (_checkedStates_columns5 = checkedStates.columns) === null || _checkedStates_columns5 === void 0 ? void 0 : _checkedStates_columns5.salesOrderId) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                        className: \"sticky top-0 font-bold text-sm\",\n                                        style: {\n                                            left: \"\".concat(gridcolumnWidths.customer, \"px\")\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                        lineNumber: 514,\n                                        columnNumber: 19\n                                    }, undefined),\n                                    (checkedStates === null || checkedStates === void 0 ? void 0 : (_checkedStates_columns6 = checkedStates.columns) === null || _checkedStates_columns6 === void 0 ? void 0 : _checkedStates_columns6.product) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                        className: \"sticky top-0 font-bold text-sm\",\n                                        style: {\n                                            left: \"\".concat(gridcolumnWidths.salesorder, \"px\")\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                        lineNumber: 520,\n                                        columnNumber: 19\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                        className: \"text-center top-0 font-bold text-sm\",\n                                        style: {\n                                            left: \"\".concat(gridcolumnWidths.product, \"px\")\n                                        },\n                                        children: \"TOTAL\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                        lineNumber: 525,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                        className: \"!text-center sticky top-0 font-bold text-sm\",\n                                        children: (_columnTotals_casesOrdered = columnTotals.casesOrdered) !== null && _columnTotals_casesOrdered !== void 0 ? _columnTotals_casesOrdered : \"-\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                        lineNumber: 532,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                        className: \"!text-center sticky top-0 font-bold text-sm\",\n                                        children: (_columnTotals_casesDelivered = columnTotals.casesDelivered) !== null && _columnTotals_casesDelivered !== void 0 ? _columnTotals_casesDelivered : \"-\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                        lineNumber: 535,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                        className: \"!text-center sticky top-0 font-bold text-sm\",\n                                        children: (_columnTotals_casesDifference = columnTotals.casesDifference) !== null && _columnTotals_casesDifference !== void 0 ? _columnTotals_casesDifference : \"-\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                        lineNumber: 538,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                        className: \"!text-center sticky top-0 font-bold text-sm\",\n                                        children: (_columnTotals_addedReasons = columnTotals.addedReasons) !== null && _columnTotals_addedReasons !== void 0 ? _columnTotals_addedReasons : \"-\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                        lineNumber: 541,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                        className: \"sticky top-0 font-bold text-sm\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                        lineNumber: 544,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                        className: \"sticky top-0 font-bold text-sm\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                        lineNumber: 545,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                        className: \"sticky top-0 font-bold text-sm\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                        lineNumber: 546,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                        className: \"!text-center sticky top-0 font-bold text-sm\",\n                                        children: ((_columnTotals_totalValue1 = columnTotals.totalValue) !== null && _columnTotals_totalValue1 !== void 0 ? _columnTotals_totalValue1 : \"-\") ? \"\\xa3\".concat((_columnTotals_totalValue = columnTotals.totalValue) === null || _columnTotals_totalValue === void 0 ? void 0 : _columnTotals_totalValue.toFixed(2)) : \"-\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                        lineNumber: 547,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                        className: \"sticky top-0 font-bold text-sm\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                        lineNumber: 553,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                        className: \"sticky top-0 font-bold text-sm\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                        lineNumber: 554,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                        className: \"sticky top-0 font-bold text-sm\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                        lineNumber: 555,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                        className: \"sticky top-0 font-bold text-sm\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                        lineNumber: 556,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                        className: \"sticky top-0 font-bold text-sm\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                        lineNumber: 557,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                lineNumber: 473,\n                                columnNumber: 15\n                            }, undefined),\n                            allSelectedProducts.map((data, index)=>{\n                                var _data_reasons, _checkedStates_columns, _checkedStates_columns1, _checkedStates_columns2, _checkedStates_columns3, _checkedStates_columns4, _checkedStates_columns5, _checkedStates_columns6, _data_SERVICE_LEVEL_PERCENT, _data_UNIT_PRICE, _this, _this1, _data_reasons_, _data_reasons_1, _data_reasons_2;\n                                if (!seeAll && data.CASES_DIFFERENCE - data.CASES_ADDED_REASONS == 0) return;\n                                const tooltip = data.LOCKED_BY ? \"\".concat(data.LOCKED_BY, \" is currently editing this order.\") : data.NEW_LINE_FLAG == 1 ? \"This is an additional order.\" : \"\";\n                                const isSelected = selectedRows === null || selectedRows === void 0 ? void 0 : selectedRows.includes(data);\n                                const rowHighlight = \"\".concat(isSelected ? \"bg-locked-products\" : ((_data_reasons = data.reasons) === null || _data_reasons === void 0 ? void 0 : _data_reasons.length) > 0 && data.CASES_DIFFERENCE - data.CASES_ADDED_REASONS > 0 ? \"bg-needsupdate-status\" : !!data.LOCKED_BY ? \"bg-locked-products\" : data.NEW_LINE_FLAG == 1 ? \"bg-volumechange-status\" : \"\");\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                            className: \"sticky top-0 font-normal text-sm \".concat(rowHighlight ? \"!\".concat(rowHighlight) : \"\"),\n                                            style: {\n                                                left: \"\".concat(0, \"px\")\n                                            },\n                                            title: tooltip,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-full flex justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"checkbox\",\n                                                    title: \"\".concat(data.NEW_LINE_FLAG == 1 ? \"This is an additional order.\" : \"\"),\n                                                    className: \"w-5 h-5 text-blue border-theme-blue2 rounded accent-skin-primary\",\n                                                    onChange: ()=>handleCheckboxChange(data),\n                                                    disabled: selectedMasterProductCode === \"all\" && masterProducts.length > 2 || selectedCustomer === \"All Customers\" || data.LOCKED_BY !== null && data.LOCKED_BY !== \"\" || data.CASES_DIFFERENCE == 0,\n                                                    // disabled={data.CASES_ADDED_REASONS!=0}\n                                                    checked: selectedRows.includes(data)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                                    lineNumber: 595,\n                                                    columnNumber: 23\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                                lineNumber: 594,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                            lineNumber: 587,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        (checkedStates === null || checkedStates === void 0 ? void 0 : (_checkedStates_columns = checkedStates.columns) === null || _checkedStates_columns === void 0 ? void 0 : _checkedStates_columns.depotdate) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                            className: \"sticky top-0 font-normal text-sm \".concat(rowHighlight ? \"!\".concat(rowHighlight) : \"\"),\n                                            style: {\n                                                left: \"\".concat(gridcolumnWidths.checkboxWidth, \"px\")\n                                            },\n                                            title: tooltip,\n                                            children: data.DEPOT_DATE && new Date(data.DEPOT_DATE).toLocaleDateString(\"en-GB\")\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                            lineNumber: 617,\n                                            columnNumber: 21\n                                        }, undefined),\n                                        ADCompanyName == \"Integrated Service Solutions Ltd\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                            className: \"sticky top-0 font-normal text-sm \".concat(rowHighlight ? \"!\".concat(rowHighlight) : \"\"),\n                                            style: {\n                                                left: \"\".concat(gridcolumnWidths.checkboxWidth, \"px\")\n                                            },\n                                            title: tooltip,\n                                            children: data.SERVICE_CUSTOMERS && data.SERVICE_CUSTOMERS\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                            lineNumber: 628,\n                                            columnNumber: 73\n                                        }, undefined),\n                                        (checkedStates === null || checkedStates === void 0 ? void 0 : (_checkedStates_columns1 = checkedStates.columns) === null || _checkedStates_columns1 === void 0 ? void 0 : _checkedStates_columns1.weekNo) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                            className: \"sticky top-0 font-normal text-center text-sm \".concat(rowHighlight ? \"!\".concat(rowHighlight) : \"\"),\n                                            style: {\n                                                left: \"\".concat(gridcolumnWidths.depotdate, \"px\")\n                                            },\n                                            title: tooltip,\n                                            children: data === null || data === void 0 ? void 0 : data.FISCAL_WEEK\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                            lineNumber: 639,\n                                            columnNumber: 21\n                                        }, undefined),\n                                        (checkedStates === null || checkedStates === void 0 ? void 0 : (_checkedStates_columns2 = checkedStates.columns) === null || _checkedStates_columns2 === void 0 ? void 0 : _checkedStates_columns2.altfill) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                            className: \"sticky top-0 font-normal  text-sm \".concat(rowHighlight ? \"!\".concat(rowHighlight) : \"\"),\n                                            style: {\n                                                left: \"\".concat(gridcolumnWidths.weekNo, \"px\")\n                                            },\n                                            title: tooltip,\n                                            children: data.ALTFILID\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                            lineNumber: 650,\n                                            columnNumber: 21\n                                        }, undefined),\n                                        (checkedStates === null || checkedStates === void 0 ? void 0 : (_checkedStates_columns3 = checkedStates.columns) === null || _checkedStates_columns3 === void 0 ? void 0 : _checkedStates_columns3.customer) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                            className: \"sticky top-0 font-normal  text-sm \".concat(rowHighlight ? \"!\".concat(rowHighlight) : \"\"),\n                                            style: {\n                                                left: \"\".concat(gridcolumnWidths.altfill, \"px\")\n                                            },\n                                            title: tooltip,\n                                            children: data.CUSTOMER\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                            lineNumber: 661,\n                                            columnNumber: 21\n                                        }, undefined),\n                                        (checkedStates === null || checkedStates === void 0 ? void 0 : (_checkedStates_columns4 = checkedStates.columns) === null || _checkedStates_columns4 === void 0 ? void 0 : _checkedStates_columns4.salesorder) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                            className: \"sticky top-0 font-normal text-sm \".concat(rowHighlight ? \"!\".concat(rowHighlight) : \"\"),\n                                            style: {\n                                                left: \"\".concat(gridcolumnWidths.customer, \"px\")\n                                            },\n                                            title: tooltip,\n                                            children: data.ORD_NUMBER\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                            lineNumber: 672,\n                                            columnNumber: 21\n                                        }, undefined),\n                                        (checkedStates === null || checkedStates === void 0 ? void 0 : (_checkedStates_columns5 = checkedStates.columns) === null || _checkedStates_columns5 === void 0 ? void 0 : _checkedStates_columns5.salesOrderId) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                            className: \"sticky top-0 font-normal text-sm \".concat(rowHighlight ? \"!\".concat(rowHighlight) : \"\"),\n                                            style: {\n                                                left: \"\".concat(gridcolumnWidths.customer, \"px\")\n                                            },\n                                            title: tooltip,\n                                            children: data.ORD_ID\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                            lineNumber: 683,\n                                            columnNumber: 21\n                                        }, undefined),\n                                        (checkedStates === null || checkedStates === void 0 ? void 0 : (_checkedStates_columns6 = checkedStates.columns) === null || _checkedStates_columns6 === void 0 ? void 0 : _checkedStates_columns6.product) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                            className: \"!w-80 sticky top-0 font-normal text-sm \".concat(rowHighlight ? \"!\".concat(rowHighlight) : \"\"),\n                                            style: {\n                                                left: \"\".concat(gridcolumnWidths.salesorder, \"px\")\n                                            },\n                                            title: tooltip,\n                                            children: data.PRODUCT_DESCRIPTION\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                            lineNumber: 694,\n                                            columnNumber: 21\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                            className: \"top-0 text-center font-normal text-sm \".concat(rowHighlight ? \"!\".concat(rowHighlight) : \"\"),\n                                            style: {\n                                                left: \"\".concat(gridcolumnWidths.product, \"px\")\n                                            },\n                                            title: tooltip,\n                                            children: data.CASE_SIZE\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                            lineNumber: 704,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"!text-center text-sm \".concat(rowHighlight ? \"!\".concat(rowHighlight) : \"\"),\n                                            title: tooltip,\n                                            children: data.CASES_ORIGINAL\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                            lineNumber: 713,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"!text-center text-sm \".concat(rowHighlight ? \"!\".concat(rowHighlight) : \"\"),\n                                            title: tooltip,\n                                            children: data.CASES_DELIVERED\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                            lineNumber: 721,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"!text-center text-sm font-bold \".concat(rowHighlight ? \"!\".concat(rowHighlight) : \"\"),\n                                            title: tooltip,\n                                            children: data.CASES_DIFFERENCE - data.CASES_ADDED_REASONS\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                            lineNumber: 729,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"!text-center text-sm \".concat(rowHighlight ? \"!\".concat(rowHighlight) : \"\"),\n                                            title: tooltip,\n                                            children: data.CASES_ADDED_REASONS\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                            lineNumber: 737,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"!text-center text-sm \".concat(rowHighlight ? \"!\".concat(rowHighlight) : \"\"),\n                                            title: tooltip,\n                                            children: \"\".concat((_data_SERVICE_LEVEL_PERCENT = data.SERVICE_LEVEL_PERCENT) === null || _data_SERVICE_LEVEL_PERCENT === void 0 ? void 0 : _data_SERVICE_LEVEL_PERCENT.toFixed(2), \"%\")\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                            lineNumber: 745,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"!text-center text-sm \".concat(rowHighlight ? \"!\".concat(rowHighlight) : \"\"),\n                                            title: tooltip,\n                                            children: [\n                                                \"\\xa3\",\n                                                (_data_UNIT_PRICE = data.UNIT_PRICE) === null || _data_UNIT_PRICE === void 0 ? void 0 : _data_UNIT_PRICE.toFixed(2)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                            lineNumber: 753,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"!text-center text-sm \".concat(rowHighlight ? \"!\".concat(rowHighlight) : \"\"),\n                                            title: tooltip,\n                                            children: \"\\xa3\".concat((_this = data.CASE_SIZE * data.UNIT_PRICE) === null || _this === void 0 ? void 0 : _this.toFixed(2))\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                            lineNumber: 761,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"!text-center text-sm \".concat(rowHighlight ? \"!\".concat(rowHighlight) : \"\"),\n                                            title: tooltip,\n                                            children: \"\\xa3\".concat((_this1 = data.CASE_SIZE * data.UNIT_PRICE * data.CASES_DIFFERENCE) === null || _this1 === void 0 ? void 0 : _this1.toFixed(2))\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                            lineNumber: 769,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"!text-center text-sm \".concat(rowHighlight ? \"!\".concat(rowHighlight) : \"\"),\n                                            title: tooltip,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"rounded-lg capitalize px-2 py-1 !text-center \".concat(data.ORD_STATUS === \"Cancelled\" ? \"bg-[#ff2929] text-white\" : data.ORD_STATUS === \"Open\" ? \"bg-[#54C5ED] text-white\" : data.ORD_STATUS === \"Invoiced\" ? \"bg-[#FFAE00] text-white\" : data.ORD_STATUS === \"Delivered\" ? \"bg-[#3EAB58] text-white\" : data.ORD_STATUS === \"Picked\" ? \"bg-[#FF6C09] text-white\" : \"bg-qtydiff-status !text-gray-700\" // Default style for any other status\n                                                ),\n                                                title: tooltip,\n                                                children: data.ORD_STATUS.toLowerCase()\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                                lineNumber: 787,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                            lineNumber: 781,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"relative \".concat(rowHighlight ? \"!\".concat(rowHighlight) : \"\"),\n                                            title: tooltip,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: (_data_reasons_ = data.reasons[0]) === null || _data_reasons_ === void 0 ? void 0 : _data_reasons_.MAIN_REASON\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                                    lineNumber: 813,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ReasonsDetails__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                    reasonsData: data.reasons,\n                                                    type: \"reasonsDetails\",\n                                                    seeAll: seeAll\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                                    lineNumber: 814,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                            lineNumber: 806,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"!text-left text-sm capitalize \".concat(rowHighlight ? \"!\".concat(rowHighlight) : \"\"),\n                                            title: tooltip,\n                                            children: (_data_reasons_1 = data.reasons[0]) === null || _data_reasons_1 === void 0 ? void 0 : _data_reasons_1.SUB_REASON\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                            lineNumber: 821,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"!text-left text-sm capitalize \".concat(rowHighlight ? \"!\".concat(rowHighlight) : \"\"),\n                                            title: tooltip,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-full text-truncate2L \",\n                                                children: (_data_reasons_2 = data.reasons[0]) === null || _data_reasons_2 === void 0 ? void 0 : _data_reasons_2.COMMENT\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                                lineNumber: 835,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                            lineNumber: 829,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"!text-center text-sm !bg-[#f3f8ff] \".concat(rowHighlight ? \"!\".concat(rowHighlight) : \"\"),\n                                            title: \"\".concat(!!data.LOCKED_BY ? \"You cannot edit the order while someone is already working on it.\" : \"\"),\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>{\n                                                    handleViewDetailsClick(data);\n                                                    setIsBulkUpdate(false);\n                                                },\n                                                className: \"cursor-pointer\",\n                                                disabled: !!data.LOCKED_BY || data.CASES_DIFFERENCE == 0 || selectedRows.length > 0,\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fluentui_react_components__WEBPACK_IMPORTED_MODULE_11__.Tooltip, {\n                                                    content: \"View Order Details\",\n                                                    relationship: \"label\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        xmlns: \"http://www.w3.org/2000/svg\",\n                                                        viewBox: \"0 0 512 512\",\n                                                        className: \" w-5 h-5 !text-skin-primary \",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            fill: \"currentcolor\",\n                                                            d: \"M480 448c0 17.7-14.3 32-32 32l-256 0c-17.7 0-32-14.3-32-32l0-80-32 0 0 80c0 35.3 28.7 64 64 64l256 0c35.3 0 64-28.7 64-64l0-284.1c0-12.7-5.1-24.9-14.1-33.9L382.1 14.1c-9-9-21.2-14.1-33.9-14.1L192 0c-35.3 0-64 28.7-64 64l0 192 32 0 0-192c0-17.7 14.3-32 32-32l128 0 0 112c0 26.5 21.5 48 48 48l112 0 0 256zm-.5-288L368 160c-8.8 0-16-7.2-16-16l0-111.5c2.8 .7 5.4 2.1 7.4 4.2L475.3 152.6c2.1 2.1 3.5 4.6 4.2 7.4zM283.3 212.7c-6.2-6.2-16.4-6.2-22.6 0s-6.2 16.4 0 22.6L329.4 304 16 304c-8.8 0-16 7.2-16 16s7.2 16 16 16l313.4 0-68.7 68.7c-6.2 6.2-6.2 16.4 0 22.6s16.4 6.2 22.6 0l96-96c6.2-6.2 6.2-16.4 0-22.6l-96-96z\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                                            lineNumber: 871,\n                                                            columnNumber: 27\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                                        lineNumber: 866,\n                                                        columnNumber: 25\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                                    lineNumber: 862,\n                                                    columnNumber: 23\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                                lineNumber: 850,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                            lineNumber: 840,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, index, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                                    lineNumber: 586,\n                                    columnNumber: 17\n                                }, undefined);\n                            })\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                        lineNumber: 471,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                lineNumber: 325,\n                columnNumber: 9\n            }, undefined),\n            isOpen && selectedRows.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ViewDetails__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                data: selectedRows,\n                setData: setSelectedRows,\n                setAllSelectedProducts: setAllSelectedProducts,\n                setMapForReasonsParentsAndTheirCorrespondingChildren: setMapForReasonsParentsAndTheirCorrespondingChildren,\n                reasonsMasterList: reasonsMasterList,\n                parentReasonList: parentReasonList,\n                reasonsData: reasonsData,\n                fetchReasonData: fetchReasonData,\n                userData: userData,\n                isOpen: isOpen,\n                setIsOpen: setIsOpen,\n                bulkUpdateData: bulkUpdateData,\n                isBulkUpdate: isBulkUpdate,\n                setReasonsData: setReasonsData,\n                setBulkUpdateData: setBulkUpdateData,\n                setIsHeaderChecked: setIsHeaderChecked,\n                setSelectedRows: setSelectedRows\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n                lineNumber: 886,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\SLTable.js\",\n        lineNumber: 321,\n        columnNumber: 5\n    }, undefined);\n};\n_s(SLTable, \"pUNbTwt98f43GScfNYyl7zEwxNM=\", false, function() {\n    return [\n        next_router__WEBPACK_IMPORTED_MODULE_10__.useRouter\n    ];\n});\n_c = SLTable;\n/* harmony default export */ __webpack_exports__[\"default\"] = (SLTable);\nvar _c;\n$RefreshReg$(_c, \"SLTable\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevExports = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevExports) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports on update so we can compare the boundary\n                // signatures.\n                module.hot.dispose(function (data) {\n                    data.prevExports = currentExports;\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevExports !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevExports, currentExports)) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevExports !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/service_level/SLTable.js\n"));

/***/ })

});