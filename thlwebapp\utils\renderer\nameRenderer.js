import { useEffect, useState } from "react";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { apiConfig } from "@/services/apiConfig";
import {
  faTriangleExclamation,
  faCircleCheck,
  faLink,
} from "@fortawesome/free-solid-svg-icons";
import { Popover } from "react-tiny-popover";
import { getCookieData } from "@/utils/getCookieData";
import { indexOf } from "lodash";
import { Bars } from "react-loader-spinner";
import { useRouter } from "next/router";
import Cookies from "js-cookie";
//

async function getFilteredSuppliers(linkedSup) {
  const domain = Cookies.get("company");
  let serverAddress = apiConfig.serverAddress;
  const user = getCookieData("user");
  return fetch(
    `${serverAddress}suppliers/get-linked-suppliers/${linkedSup}/${domain}`,
    {
      method: "GET",
      headers: {
        Authorization: `Bearer ${user.token}`,
      },
    }
  )
    .then(async (res) => {
      if (res.status === 401) {
        localStorage.removeItem("superUser");
        Cookies.remove("company");
        Cookies.remove("ADCompanyName");
        localStorage.removeItem("id");
        localStorage.removeItem("name");
        localStorage.removeItem("role");
        localStorage.removeItem("email");
        Cookies.remove("user");
        Cookies.remove("theme");
        Cookies.remove("token");
        const redirectUrl = `/login?redirect=${encodeURIComponent(
          window.location.pathname
        )}`;
        logoutHandler(instance, redirectUrl);
      } else if (res.status === 400) {
        console.error("Error fetching data");
      }
      if (res.status === 200) {
        return res.json();
      }
      throw new Error("Failed to fetch data");
    })
    .catch((error) => {
      console.log("Error", error);
    });
}

const nameRenderer = (params) => {
  //console.log(params);
  const serverAddress = apiConfig.serverAddress;
  const [isExportPopoverOpen, setIsExportPopoverOpen] = useState(false);
  const [isExclaimPopoverOpen, setIsExclaimPopoverOpen] = useState(false);
  const [isLinkPopoverOpen, setIsLinkPopoverOpen] = useState(false);
  const [status, setStatus] = useState("");
  const [emergencyRequest, setEmergencyRequest] = useState(false);
  const [supplierLinks, setSupplierLinks] = useState([]);
  const [data, setData] = useState([]);
  const [loading, setLoading] = useState(false);
  const router = useRouter();

  const supplierId = params.data.id;
  const supplierStatus = params.data.status;
  const supplierData = params.data;

  const roleIds = supplierData.roleId.map((item) => {
    return item.role_id;
  });

  const supplier_group = supplierData.supplier_group
    ? JSON.parse(supplierData.supplier_group)
    : "";

  useEffect(() => {
    if (supplierId) {
      setStatus(supplierStatus);
      setEmergencyRequest(supplierData.isEmergencyRequest);

      if (supplierData.supplierLinks) {
        const supplierLinkParse = JSON.parse(supplierData.supplierLinks);

        const roleMap = new Map();
        supplierLinkParse?.forEach((item) => {
          if (!roleMap.has(item.name)) {
            roleMap.set(item.name, {
              id: item.supplier_id,
              role_id: [],
              roles: [],
            });
          }
          roleMap.get(item.name).roles.push(item.role_names);
          roleMap.get(item.name).role_id.push(item.role_id);
        });

        const result = [...roleMap.entries()].map(
          ([name, { id, role_id, roles }]) => ({
            id,
            name,
            role_id,
            role_names: roles.join(" , "),
          })
        );

        const link_data = result?.map((row) => {
          return {
            value: row.id,
            label: row.name + " ( " + row.role_names.trim() + " ) ",
            role_id: row.role_id,
          };
        });
        setSupplierLinks(
          link_data.filter((item) => item.value != params.data.id)
        );
      }
    }
  }, []);

  const filterLinkedSuppliers = (linkedSupplier, selectedData) => {
    getFilteredSuppliers(linkedSupplier)
      .then((data) => {
        const formattedData = data?.map((row) => ({
          id: row.id,
          company_name: row.name ? row.name : "Not Entered",
          currency: row.currency ? row.currency : "Not Entered",
          Compliance: row.compliance,
          Financials: row.financial,
          General: row.technical,
          Procurement: row.procurement,
          requestor: row.requestor_name,
          companies: row.prophet_names ? row.prophet_names : "Not Entered",
          role: row.role_names ? row.role_names : "Not Entered",
          roleId: row.role_ids ? JSON.parse(row.role_ids) : [],
          prophets: row.prophet_ids ? JSON.parse(row.prophet_ids) : [],
          supplier_code: row.prophet_ids
            ? JSON.parse(row.prophet_ids)[0].prophet_code
            : [],
          isProducerSupplier: row.product_supplier,
          isEmergencyRequest: row.emergency_request,
          supplier_group: row.sendac_groups_json,
          supplierLinks: row.supplier_links_json,
          status: row.label,
        }));

        params.setSuppliers(formattedData);
        params.setIsFiltered(true);
        params.setIsFilteredName(selectedData.company_name);
      })
      .catch((error) => {
        console.log(error);
      });
  };
  function routePage(id) {
    if (params.data.status != "Exported") {
      setLoading(true);

      setTimeout(() => {
        router.push({
          pathname: `/supplier/${id}/edit`,
        });
      }, 1000);
    } else {
      return;
    }
  }
  return (
    <>
      {loading ? (
        <div
          style={{
            width: "80px",
            textAlign: "left",
            display: "inline-block",
            verticalAlign: "middle",
            lineHeight: "24px",
            height: "32px",
          }}
        >
          {" "}
          <Bars
            color="#002D73"
            height={30}
            width={30}
            visible={true}
            ariaLabel="oval-loading"
            secondaryColor="#0066FF"
            strokeWidth={2}
            strokeWidthSecondary={2}
          />
        </div>
      ) : (
        //!make sure the status column is not clickable

        <div className="flex flex-row gap-4 items-center">
          {/* <input id="checked-checkbox" type="checkbox" value="" className="w-5 h-5 text-blue border-blue-300 rounded " /> */}
          <label
            className={`${
              params.data.status !== "Exported" ? "cursor-pointer" : ""
            }`}
            onClick={() => routePage(params.data.id)}
          >
            {params.data.company_name}
          </label>
          <div className="flex flex-row gap-2">
            {status == "Exported" && (
              <Popover
                isOpen={isExportPopoverOpen}
                positions={["bottom"]} // preferred positions by priority
                content={
                  <div className="flex px-4 py-5 text-green-500 bg-white shadow-lg rounded-md">
                    Exported supplier
                  </div>
                }
              >
                <div
                  onMouseEnter={() => setIsExportPopoverOpen(true)}
                  onMouseLeave={() => setIsExportPopoverOpen(false)}
                >
                  <button>
                    <FontAwesomeIcon
                      icon={faCircleCheck}
                      style={{ color: "rgb(62, 171, 88)" }}
                    />
                  </button>
                </div>
              </Popover>
            )}
            {emergencyRequest && (
              <Popover
                isOpen={isExclaimPopoverOpen}
                positions={["bottom"]} // preferred positions by priority
                content={
                  <div className="flex px-4 py-5 text-red-500 bg-white shadow-lg rounded-md">
                    Emergency supplier request
                  </div>
                }
              >
                <div
                  onMouseEnter={() => setIsExclaimPopoverOpen(true)}
                  onMouseLeave={() => setIsExclaimPopoverOpen(false)}
                >
                  <button>
                    <FontAwesomeIcon
                      icon={faTriangleExclamation}
                      className="text-red-500"
                    />
                  </button>
                </div>
              </Popover>
            )}

            {supplierLinks && supplierLinks.length > 0 && (
              <Popover
                isOpen={isLinkPopoverOpen}
                positions={["bottom"]} // preferred positions by priority
                content={
                  <div className="flex flex-col px-4 py-5 bg-white shadow-lg rounded-md">
                    <span
                      className="text-blue-500 cursor-pointer"
                      onClick={() => {
                        filterLinkedSuppliers(
                          supplier_group ? supplier_group[0].group_id : "",
                          params.data
                        );
                        // alert(params.data.id)
                      }}
                    >
                      Linked Suppliers
                    </span>
                    <ul className="list-none">
                      {supplierLinks?.map((item, i) => {
                        return <li key={i}>{item?.label}</li>;
                      })}
                      {/* <li>234-High Bridge group</li>
									<li>356-UK Packaging Supplies Ltd.</li>
									<li>453-UK Packers</li> */}
                    </ul>
                  </div>
                }
              >
                <div
                  onClick={() => setIsLinkPopoverOpen((prev) => !prev)}
                  onBlur={() =>
                    setTimeout(() => {
                      setIsLinkPopoverOpen(false);
                    }, [500])
                  }
                >
                  <button>
                    <FontAwesomeIcon icon={faLink} className="text-blue-500" />
                  </button>
                </div>
              </Popover>
            )}
          </div>
        </div>
      )}
    </>
  );
};

export default nameRenderer;
