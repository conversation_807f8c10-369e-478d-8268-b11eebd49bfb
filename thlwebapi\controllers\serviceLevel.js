"use strict";

const verifyToken = require("../auth");
const serviceLevelData = require("../data/serviceLevel");
const logger = require("../utils/logger");
const socket = require("../utils/socket");

const getSLCustomers = async (req, res, next) => {
  try {
    if (await verifyToken(req, res)) {
      const email = req?.user?.preferred_username;
      const name = req?.user?.name;
      const serviceLevelCustomerData = await serviceLevelData.getSLCustomers(
        name,
        email
      );
      if (serviceLevelCustomerData && serviceLevelCustomerData?.length > 0) {
        res.status(200).send(serviceLevelCustomerData);
      } else {
        res.status(404).send("No service level data found");
      }
    } else {
      res.status(401).send("Invalid token");
    }
  } catch (error) {
    res.status(400).send(error.message);
  }
};

const getLastTwoDays = () => {
  const today = new Date();
  const twoWeeksAgo = new Date(today);
  twoWeeksAgo.setDate(today.getDate() -1);

  const formatDate = (date) => {
    const day = String(date.getDate()).padStart(2, "0");
    const month = String(date.getMonth() + 1).padStart(2, "0");
    const year = date.getFullYear();
    return `${month}-${day}-${year}`;
  };

  return {
    start_date: formatDate(twoWeeksAgo),
    end_date: formatDate(today),
  };
};

const formatSLData = (SlData) => {
  const dataMap = {};
  const allReasonSubReasons = {
    reasons: {"Select All":"Select All"},
    subReasons: {"Select All":"Select All"}
  };  SlData?.sLDataByCustomer?.forEach((data) => {
    const order_id = data?.ORD_ID;
    if (!dataMap[order_id]) {
      dataMap[order_id] = {
        ORD_ID: data.ORD_ID,
        NEW_LINE_FLAG: data.new_line_flag,
        SERVICE_CUSTOMERS:data.SERVICE_CUSTOMER,
        ORD_NUMBER: data.ORD_NUMBER,
        CUSTOMER: data.CUSTOMER,
        DEPOT_DATE: data.DEPOT_DATE,
        FISCAL_WEEK: data.FISCAL_WEEK,
        ORD_STATUS: data.ORD_STATUS,
        ORD_TYPE_ID: data.ORD_TYPE_ID,
        ORDER_TYPE: data.ORDER_TYPE,
        ALTFILID: data.ALTFILID,
        CATEGORY: data.CATEGORY,
        CASE_SIZE: data.CASE_SIZE,
        PRODUCT_DESCRIPTION: data.PRODUCT_DESCRIPTION,
        MASTER_PRODUCT_CODE: data.MASTER_PRODUCT_CODE,
        CASES_ORIGINAL: data.CASES_ORIGINAL,
        CASES_AMENDED: data.CASES_AMENDED,
        CASES_DELIVERED: data.CASES_DELIVERED,
        CASES_DIFFERENCE: data.CASES_DIFFERENCE,
        SERVICE_LEVEL_PERCENT: data.SERVICE_LEVEL_PERCENT,
        CASES_ADDED_REASONS: data.CASES_ADDED_REASONS,
        UNIT_PRICE: data.UNIT_PRICE,
        TOTAL_VALUE: data.TOTAL_VALUE,
        LOCKED_BY: data.LOCKED_BY,
        reasons: [],
      };
    }

    let reason;
    if (data.REASON_ID !== null) {
      const reasonExists = dataMap[order_id].reasons.some(
        (r) => r.REASON_ID === data.REASON_ID
      );
      if (!reasonExists) {
        reason = {
          REASON_ID: data.REASON_ID,
          MAIN_REASON_ID: data.MAIN_REASON_ID,
          MAIN_REASON: data.MAIN_REASON,
          SUB_REASON_ID: data.SUB_REASON_ID,
          SUB_REASON: data.SUB_REASON,
          REASON_ADDED_BY: data.REASON_ADDED_BY,
          REASON_QTY: data.REASON_QTY,
          COMMENT: data.REASON_COMMENT,
          REASON_UPDATED_BY: data.REASON_UPDATED_BY,
          REASON_ADDED_TIMESTAMP: data.REASON_ADDED_TIMESTAMP,
          REASON_UPDATED_TIMESTAMP: data.REASON_UPDATED_TIMESTAMP,
        };
        // console.log("dataMap[order_id].reasons",dataMap)
        dataMap[order_id].reasons?.push(reason);
        if (data.MAIN_REASON_ID && data.MAIN_REASON) {
          allReasonSubReasons.reasons[data.MAIN_REASON] = data.MAIN_REASON_ID;
        }

        // Store unique sub-reasons
        if (data.SUB_REASON_ID && data.SUB_REASON) {
          allReasonSubReasons.subReasons[data.SUB_REASON] = data.SUB_REASON_ID;
        }
      }
    }
  });
  return {dataMap,allReasonSubReasons};
};

const getIntialSLData = async (req, res, next) => {
  try {
    if (await verifyToken(req, res)) {
      const email = req?.user?.preferred_username;
      const name = req?.user?.name;
      const cust_code = req.params.cust_code;
      let start_date = req.params.start_date;
      let end_date = req.params.end_date;
      let orderTypeId = req.params.orderTypeId;
      let company = req.params.company;
      let ADCompanyName = req.params.ADCompanyName;
      let serviceCustomer=req?.query?.serviceCustomer;
      const seeAll = req.params.seeAll === "true";
      if (
        !start_date ||
        start_date === "null" ||
        !end_date ||
        end_date === "null"
      ) {
        const lastTwoWeeks = getLastTwoDays();
        start_date =
          start_date === "null" || !start_date
            ? lastTwoWeeks.start_date
            : start_date;

        end_date =
          end_date === "null" || !end_date ? lastTwoWeeks.end_date : end_date;
      }
      const initialData = await serviceLevelData.getIntialSLData(
        name,
        start_date,
        end_date,
        email,
        cust_code,
        seeAll,
        orderTypeId,company,ADCompanyName,serviceCustomer
      );

      const {dataMap,allReasonSubReasons} = formatSLData(initialData);
      initialData.formattedSLData = dataMap;
      initialData.allReasonSubReasons = allReasonSubReasons;
      delete initialData.sLDataByCustomer;
      res.send(initialData);
    } else {
      console.log("Invalid token");
      res.status(401).send("Invalid Token");
    }
  } catch (error) {
    console.error("error on get initial sl data", error);
    res.status(400).send(error.message);
  }
};

const getServiceLevelReasons = async (req, res, next) => {
  try {
    if (await verifyToken(req, res)) {
      const cust_codes = req.query.customerName.split(",");
      const orderIds = req.params.orderId.split(",");

      orderIds.forEach(async (orderId, index) => {
        await addSLLock(req, res, orderId, cust_codes[index]);
      });
      const serviceLevelReasons = await serviceLevelData.getServiceLevelReasons(
        orderIds[0]
      );
      if (serviceLevelReasons && serviceLevelReasons?.length > 0) {
        res.status(200).send(serviceLevelReasons);
      } else {
        res.status(200).send([]);
      }
    } else {
      res.status(401).send("invalid token");
    }
  } catch (err) {
    console.log("Failed getting service level reasons: ", err);
    res.status(400).send(err.message);
  }
};
const getServiceLevelAuditReasons = async (req, res, next) => {
  // console.log("reached in controller");
  try {
    if (await verifyToken(req, res)) {
      const orderId = req.params.orderId;
      const serviceLevelReasons =
        await serviceLevelData.getServiceLevelAuditReasons(orderId);
      if (serviceLevelReasons && serviceLevelReasons?.length > 0) {
        res.status(200).send(serviceLevelReasons);
      } else {
        res.status(404).send("no service level reasons found");
      }
    } else {
      res.status(401).send("invalid token");
    }
  } catch (err) {
    console.log("Failed getting service level reasons: ", err);
    res.status(400).send(err.message);
  }
};

const getServiceLevelResonsMaster = async (req, res) => {
  try {
    if (await verifyToken(req, res)) {
      const serviceLevelReasonMaster =
        await serviceLevelData.getServiceLevelResonsMaster();
      if (serviceLevelReasonMaster && serviceLevelReasonMaster?.length > 0) {
        res.status(200).send(serviceLevelReasonMaster);
      } else {
        res.status(404).send("no service level resaon master found");
      }
    } else {
      res.status(401).send("invalid token");
    }
  } catch (err) {
    console.log("Failed getting service level reasons master: ", err);
    res.status(400).send(err.message);
  }
};
const addNewReason = async (req, res) => {
  try {
    if (await verifyToken(req, res)) {
      const payload = req?.body;

      const result = await serviceLevelData.addNewReason(payload);

      result.forEach((res) => {
        const matchingPayload = payload.find(
          (item) => item.orderId.toString() === res.order_id
        );
        if (matchingPayload) {
          matchingPayload.id = res.id;
        }
      });

      const io = socket.getIO();
      io.emit("slAdded", payload);

      res.status(201).send(result);
    } else {
      res.status(401).send("invalid token");
    }
  } catch (err) {
    console.log("Failed getting service level reasons master: ", err);
    res.status(400).send(err.message);
  }
};
const editReason = async (req, res) => {
  try {
    if (await verifyToken(req, res)) {
      const payload = req?.body;
      payload.originalQuantity = req.body.originalEditQuantity;
      const result = await serviceLevelData.editReason(payload);
      const io = socket.getIO();
      io.emit("slUpdated", payload);

      res.status(201).send(result);
    } else {
      res.status(401).send("invalid token");
    }
  } catch (err) {
    console.log("Failed getting service level reasons master: ", err);
    res.status(400).send(err.message);
  }
};
const deleteReason = async (req, res) => {
  try {
    if (await verifyToken(req, res)) {
      const payload = req?.body;

      const result = await serviceLevelData.deleteReason(payload);
      const io = socket.getIO();
      io.emit("slDeleted", payload);

      res.status(201).send(result);
    } else {
      res.status(401).send("invalid token");
    }
  } catch (err) {
    console.log("Failed to delete reason: ", err);
    res.status(400).send(err.message);
  }
};
const deleteBulkReason = async (req, res) => {
  try {
    if (await verifyToken(req, res)) {
      const payload = req?.body;
      const result = await serviceLevelData.deleteBulkReason(payload);
      const io = socket.getIO();
      io.emit("slDeleted", result);
      res.status(201).send(result);
    } else {
      res.status(401).send("invalid token");
    }
  } catch (err) {
    console.log("Failed to delete reason: ", err);
    res.status(400).send(err.message);
  }
};

const removeSLLocks = async (req, res, next) => {
  try {
    if (await verifyToken(req, res)) {
      const email = req?.body?.email;
      const isPayloadRequired = req.body.isPayloadRequired;

      if (isPayloadRequired) {
        const custCodes = req?.body?.custCode;
        const orderIds = req?.body?.orderId;
        const io = socket.getIO();
        orderIds.forEach(async (oid, i) => {
          const payload = { orderId: oid, custCode: custCodes[i] };

          io.emit("SLlockRemoved", payload);

          await serviceLevelData.removeSLLocks(email, custCodes[i], oid);
        });
      } else {
        await serviceLevelData.removeSLLocks(email);
      }

      res.send(true);
    } else {
      res.status(401).send("Invalid token");
    }
  } catch (error) {
    res.status(400).send(error.message);
  }
};

const addSLLock = async (req, res, orderId, custCode) => {
  try {
    const email = req?.user?.preferred_username;
    const name = req?.user?.name;

    const payload = {
      email,
      name,
      orderId,
      custCode,
    };

    const io = socket.getIO();
    io.emit("SLlockAdded", payload);

    await serviceLevelData.addSLLock(payload);
  } catch (error) {}
};

module.exports = {
  getSLCustomers,
  getIntialSLData,
  getServiceLevelReasons,
  getServiceLevelAuditReasons,
  getServiceLevelResonsMaster,
  addNewReason,
  editReason,
  deleteReason,
  removeSLLocks,
  deleteBulkReason,
};
