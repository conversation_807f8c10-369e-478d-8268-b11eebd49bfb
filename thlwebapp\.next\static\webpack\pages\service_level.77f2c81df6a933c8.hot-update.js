"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/service_level",{

/***/ "./components/service_level/ViewDetails.jsx":
/*!**************************************************!*\
  !*** ./components/service_level/ViewDetails.jsx ***!
  \**************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _fluentui_react_components__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @fluentui/react-components */ \"./node_modules/@fluentui/react-components/lib/index.js\");\n/* harmony import */ var _AuditDetails__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./AuditDetails */ \"./components/service_level/AuditDetails.jsx\");\n/* harmony import */ var _services_apiConfig__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/services/apiConfig */ \"./services/apiConfig.js\");\n/* harmony import */ var _DeleteReasonPopover__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./DeleteReasonPopover */ \"./components/service_level/DeleteReasonPopover.jsx\");\n/* harmony import */ var _utils_whatif_utils_getFormattedDate__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/utils/whatif/utils/getFormattedDate */ \"./utils/whatif/utils/getFormattedDate.js\");\n/* harmony import */ var react_toastify__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! react-toastify */ \"./node_modules/react-toastify/dist/react-toastify.esm.mjs\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_7__);\n/* harmony import */ var _utils_secureStorage__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/utils/secureStorage */ \"./utils/secureStorage.js\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\nconst ViewDetails = (param)=>{\n    let { data, setData, setMapForReasonsParentsAndTheirCorrespondingChildren, reasonsMasterList, parentReasonList, reasonsData, fetchReasonData, userData, isOpen, setIsOpen, isBulkUpdate, bulkUpdateData, setReasonsData, setAllSelectedProducts, setBulkUpdateData, setIsHeaderChecked, setSelectedRows } = param;\n    _s();\n    const [lockedBy, setLockedBy] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isBeingEdited, setIsBeingEdited] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [selectedReasonDropdownValue, setSelectedReasonDropdownValue] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [subReasonsList, setSubReasonsList] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedSubReasonDropdownValue, setSelectedSubReasonDropdownValue] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [quantity, setQuantity] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [comment, setComment] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [onEditQuantityValue, setOnEditQuantityValue] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [onEditSelectedReason, setOnEditSelectedReason] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [onEditSelectedSubReason, setOnEditSelectedSubReason] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [onEditComment, setOnEditComment] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [deleteId, setDeleteId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [deleteReason, setDeleteReason] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [isDeleteTrue, setIsDeleteTrue] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isValidQuantity, setIsValidQuantity] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [isValidReason, setIsValidReasons] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [isValidSubReason, setIsValidSubReasons] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [isValidEditQuantity, setIsValidEditQuantity] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [isValidEditReason, setIsValidEditReasons] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [isValidEditSubReason, setIsValidEditSubReasons] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [invalidEditQuantityId, setInvalidEditQuantityId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [invalidEditReasonsId, setInvalidEditReasonsId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [invalidEditSubReasonsId, setInvalidEditSubReasonsId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [originalEditQuantity, setOriginalEditQuantity] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [isAuditDetailsOpen, setIsAuditDetailsOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isValidComment, setIsValidComment] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [isOtherSelected, setIsOtherSelected] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [currentData, setCurrentData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(data);\n    const [currentCustomers, setCurrentCustomers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [orderId, setOrderId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isSaveButtonDisabled, setIsSaveButtonDisabled] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_7__.useRouter)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        setMapForReasonsParentsAndTheirCorrespondingChildren();\n    }, [\n        reasonsMasterList\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const ids = data.map((item)=>item.ORD_ID);\n        const customers = data.map((item)=>item.CUSTOMER);\n        setOrderId(ids);\n        setCurrentData(data);\n        setCurrentCustomers(customers);\n        return ()=>{\n            setOrderId([]);\n        };\n    }, []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (orderId.length > 0) {\n            fetchReasonData(orderId, currentCustomers);\n        }\n    }, [\n        orderId[0],\n        currentCustomers[0]\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (currentData.length > 0) {\n            setLockedBy(currentData[0].LOCKED_BY);\n        }\n    }, [\n        currentData\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        setLockedBy(data[0].LOCKED_BY);\n    }, [\n        data[0].LOCKED_BY\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (isDeleteTrue) {\n            handleDeleteReason(orderId);\n            setIsDeleteTrue(false);\n        }\n    }, [\n        isDeleteTrue\n    ]);\n    const saveReasons = ()=>{\n        setIsSaveButtonDisabled(true);\n        if (isBulkUpdate) {\n            let totalAddedReasons = bulkUpdateData.totalCasesDifferent;\n            setBulkUpdateData((prev)=>{\n                return {\n                    ...prev,\n                    totalCasesDifferent: 0,\n                    totalCasesAddedReasons: totalAddedReasons\n                };\n            });\n        }\n        const saveData = currentData.map((item)=>{\n            var _reasonsMasterList_filter_, _reasonsMasterList_filter_1;\n            return {\n                quantity: isBulkUpdate ? item.CASES_DIFFERENCE : typeof quantity === \"string\" ? quantity.trim() : quantity,\n                reasons: selectedReasonDropdownValue,\n                reasonsLabel: (_reasonsMasterList_filter_ = reasonsMasterList.filter((r)=>r.id == +selectedReasonDropdownValue)[0]) === null || _reasonsMasterList_filter_ === void 0 ? void 0 : _reasonsMasterList_filter_.reason,\n                subReason: selectedSubReasonDropdownValue,\n                subReasonLabel: (_reasonsMasterList_filter_1 = reasonsMasterList.filter((r)=>r.id === selectedSubReasonDropdownValue)[0]) === null || _reasonsMasterList_filter_1 === void 0 ? void 0 : _reasonsMasterList_filter_1.reason,\n                comment: comment.trim(),\n                orderId: item.ORD_ID,\n                addedBy: userData.email,\n                addedByName: userData.name,\n                custCode: item.CUSTOMER\n            };\n        });\n        const isValid = saveData.every((item)=>{\n            const isQuantityValid = item.quantity && item.quantity !== \"\" && item.quantiy !== 0;\n            const isReasonValid = item.reasons && item !== \"\";\n            const isSubReasonValid = item.subReason && item.subReason !== \"\";\n            if (selectedReasonDropdownValue === \"30\" && !item.comment) {\n                setIsValidComment(false);\n                return;\n            } else {\n                setIsValidComment(true);\n            }\n            // Set individual validation states\n            if (!isQuantityValid) {\n                alert(\"not valid\");\n                setIsValidQuantity(false);\n            }\n            if (!isReasonValid) {\n                setIsValidReasons(false);\n            }\n            if (!isSubReasonValid) {\n                setIsValidSubReasons(false);\n            }\n            return isQuantityValid && isReasonValid && isSubReasonValid;\n        });\n        // If any of the items are invalid, set the overall validation states\n        if (!isValid) {\n            return; // Exit if any validation fails\n        }\n        setIsValidQuantity(true);\n        setIsValidReasons(true);\n        setIsValidSubReasons(true);\n        try {\n            const serverAddress = _services_apiConfig__WEBPACK_IMPORTED_MODULE_3__.apiConfig.serverAddress;\n            fetch(\"\".concat(serverAddress, \"servicelevel/add-new-reason\"), {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                credentials: \"include\",\n                body: JSON.stringify(saveData)\n            }).then((res)=>{\n                if (res.status == 401) {\n                    react_toastify__WEBPACK_IMPORTED_MODULE_6__.toast.error(\"Your session has expired. Please log in again.\");\n                    setTimeout(async ()=>{\n                        await (0,_utils_secureStorage__WEBPACK_IMPORTED_MODULE_8__.logout)();\n                        const redirectUrl = \"/login?redirect=\".concat(encodeURIComponent(window.location.pathname));\n                        router.push(redirectUrl);\n                    }, 3000);\n                    return null;\n                } else if (res.status == 400) {\n                    react_toastify__WEBPACK_IMPORTED_MODULE_6__.toast.error(\"There was an error with your request. Please check your data and try again.\");\n                    return null;\n                } else {\n                    return res.json(); // Ensure you parse the JSON\n                }\n            }).then((json)=>{\n                if (!isBulkUpdate) {\n                    fetchReasonData(orderId[0], currentCustomers[0]);\n                } else {\n                    const reasonsDataArr = json.reduce((acc, item)=>{\n                        const existingEntry = acc.find((entry)=>entry.reason_id === item.reason_id);\n                        if (existingEntry) {\n                            existingEntry.quantity += item.quantity;\n                            existingEntry.id.push(item.id);\n                            existingEntry.order_id.push(item.order_id);\n                            existingEntry.cust_codes.push(item.cust_code);\n                        } else {\n                            acc.push({\n                                added_by: item.added_by,\n                                comment: item.comment,\n                                delete_reason: null,\n                                deleted_by: null,\n                                id: [\n                                    item.id\n                                ],\n                                is_deleted: false,\n                                order_id: [\n                                    item.order_id\n                                ],\n                                quantity: item.quantity,\n                                reason: item.reasonsLabel,\n                                sub_reason: item.subReasonLabel,\n                                reason_id: item.reason_id,\n                                subreason_id: item.subreason_id,\n                                cust_codes: [\n                                    item.cust_code\n                                ]\n                            });\n                        }\n                        return acc;\n                    }, []);\n                    // Set the aggregated reasons data\n                    setReasonsData(reasonsDataArr);\n                }\n                setQuantity(\"\");\n                setComment(\"\");\n                setSelectedReasonDropdownValue(\"\");\n                setSelectedSubReasonDropdownValue(\"\");\n                setIsSaveButtonDisabled(false);\n            });\n        } catch (error) {\n            setIsSaveButtonDisabled(false);\n            console.error(\"Failed to save new reason.\", error);\n        }\n    };\n    const handleEdit = (id, orderId)=>{\n        setIsSaveButtonDisabled(false);\n        // console.log(\"save reasons data\",reasonsData);\n        if (!isValidEditQuantity) {\n            return;\n        }\n        const editData = currentData.map((item, index)=>{\n            var _reasonsMasterList_filter_, _reasonsMasterList_filter_1;\n            return {\n                quantity: isBulkUpdate ? item.CASES_DIFFERENCE : onEditQuantityValue,\n                reasons: onEditSelectedReason,\n                reasonsLabel: (_reasonsMasterList_filter_ = reasonsMasterList.filter((r)=>r.id == +onEditSelectedReason)[0]) === null || _reasonsMasterList_filter_ === void 0 ? void 0 : _reasonsMasterList_filter_.reason,\n                subReason: onEditSelectedSubReason,\n                subReasonLabel: (_reasonsMasterList_filter_1 = reasonsMasterList.filter((r)=>r.id == +onEditSelectedSubReason)[0]) === null || _reasonsMasterList_filter_1 === void 0 ? void 0 : _reasonsMasterList_filter_1.reason,\n                comment: onEditComment.trim(),\n                orderId: item.ORD_ID,\n                id: Array.isArray(id) ? id[index] : id,\n                updatedBy: userData.email,\n                originalEditQuantity: originalEditQuantity,\n                cust_code: item.CUSTOMER\n            };\n        });\n        const isValid = editData.every((item)=>{\n            const isQuantityValid = item.quantity && item.quantity !== \"\";\n            const isReasonValid = item.reasons && item !== \"\";\n            const isSubReasonValid = item.subReason && item.subReason !== \"\";\n            // Set individual validation states\n            if (!isQuantityValid) {\n                setIsValidEditQuantity(false);\n                setInvalidEditQuantityId(item.id);\n            }\n            if (!isReasonValid) {\n                setIsValidEditReasons(false);\n                setInvalidEditReasonsId(item.id);\n            }\n            if (!isSubReasonValid) {\n                setIsValidEditSubReasons(false);\n                setInvalidEditSubReasonsId(item.id);\n            }\n            return isQuantityValid && isReasonValid && isSubReasonValid;\n        });\n        if (!isValid) {\n            return;\n        }\n        setIsValidEditQuantity(true);\n        setIsValidEditReasons(true);\n        setInvalidEditQuantityId(\"\");\n        setInvalidEditReasonsId(\"\");\n        setIsBeingEdited(null);\n        setOriginalEditQuantity(null);\n        setIsValidEditSubReasons(true);\n        setInvalidEditSubReasonsId(\"\");\n        try {\n            const serverAddress = _services_apiConfig__WEBPACK_IMPORTED_MODULE_3__.apiConfig.serverAddress;\n            fetch(\"\".concat(serverAddress, \"servicelevel/edit-reason\"), {\n                method: \"PUT\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                credentials: \"include\",\n                body: JSON.stringify(editData)\n            }).then((res)=>{\n                if (res.status == 401) {\n                    react_toastify__WEBPACK_IMPORTED_MODULE_6__.toast.error(\"Your session has expired. Please log in again.\");\n                    setTimeout(async ()=>{\n                        await (0,_utils_secureStorage__WEBPACK_IMPORTED_MODULE_8__.logout)();\n                        const redirectUrl = \"/login?redirect=\".concat(encodeURIComponent(window.location.pathname));\n                        router.push(redirectUrl);\n                    }, 3000);\n                    return null;\n                } else if (res.status == 400) {\n                    react_toastify__WEBPACK_IMPORTED_MODULE_6__.toast.error(\"There was an error with your request. Please check your data and try again.\");\n                    return null;\n                } else {\n                    return res.json(); // Ensure you parse the JSON\n                }\n            }).then((json)=>{\n                if (!isBulkUpdate) {\n                    fetchReasonData(data[0].ORD_ID, currentCustomers[0]);\n                } else {\n                    const reasonsDataArr = editData.reduce((acc, item)=>{\n                        const existingEntry = acc.find((entry)=>parseInt(entry.reason_id) === parseInt(item.reasons));\n                        if (existingEntry) {\n                            existingEntry.quantity += item.quantity;\n                            existingEntry.id.push(item.id);\n                            existingEntry.order_id.push(item.orderId.toString());\n                            existingEntry.cust_codes.push(item.cust_code);\n                        } else {\n                            acc.push({\n                                added_by: item.updatedBy,\n                                comment: item.comment,\n                                delete_reason: null,\n                                deleted_by: null,\n                                id: [\n                                    item.id\n                                ],\n                                is_deleted: false,\n                                order_id: [\n                                    item.orderId.toString()\n                                ],\n                                quantity: item.quantity,\n                                reason: item.reasonsLabel,\n                                sub_reason: item.subReasonLabel,\n                                reason_id: parseInt(item.reasons),\n                                subreason_id: parseInt(item.subReason),\n                                cust_codes: [\n                                    item.cust_code\n                                ]\n                            });\n                        }\n                        return acc;\n                    }, []);\n                    // Set the aggregated reasons data\n                    setReasonsData(reasonsDataArr);\n                }\n            });\n            setIsSaveButtonDisabled(false);\n        } catch (error) {\n            setIsSaveButtonDisabled(false);\n            console.error(\"Failed to save new reason.\", error);\n        }\n    };\n    const handleDeleteReason = (orderId)=>{\n        // console.log(\"save reasons data\",reasonsData);\n        const deleteData = {\n            orderId,\n            deleteReason: deleteReason,\n            id: Array.isArray(deleteId) ? deleteId : [\n                deleteId\n            ],\n            deletedBy: userData.email,\n            deletedByName: userData.name\n        };\n        let totalCasesDifferent = bulkUpdateData.totalCasesAddedReasons;\n        setBulkUpdateData((prev)=>{\n            return {\n                ...prev,\n                totalCasesDifferent: totalCasesDifferent\n            };\n        });\n        try {\n            const serverAddress = _services_apiConfig__WEBPACK_IMPORTED_MODULE_3__.apiConfig.serverAddress;\n            fetch(\"\".concat(serverAddress, \"servicelevel/delete-reason\"), {\n                method: \"PUT\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                credentials: \"include\",\n                body: JSON.stringify(deleteData)\n            }).then((res)=>{\n                if (res.status == 401) {\n                    react_toastify__WEBPACK_IMPORTED_MODULE_6__.toast.error(\"Your session has expired. Please log in again.\");\n                    setTimeout(async ()=>{\n                        await (0,_utils_secureStorage__WEBPACK_IMPORTED_MODULE_8__.logout)();\n                        const redirectUrl = \"/login?redirect=\".concat(encodeURIComponent(window.location.pathname));\n                        router.push(redirectUrl);\n                    }, 3000);\n                    return null;\n                } else if (res.status == 400) {\n                    react_toastify__WEBPACK_IMPORTED_MODULE_6__.toast.error(\"There was an error with your request. Please check your data and try again.\");\n                    return null;\n                } else {\n                    return res.json(); // Ensure you parse the JSON\n                }\n            }).then((json)=>{\n                fetchReasonData(orderId, currentCustomers[0]);\n            });\n        } catch (error) {\n            console.error(\"Failed to save new reason.\", error);\n        }\n    };\n    const handleParentDropdownReasonChange = (e, type)=>{\n        let parentId;\n        if (typeof e === \"object\") {\n            parentId = parseInt(e.target.value);\n        } else {\n            parentId = e;\n        }\n        if (type == \"add\") {\n            setSelectedReasonDropdownValue(e.target.value);\n            setIsValidReasons(true);\n            if (e.target.value === \"30\") {\n                setSelectedSubReasonDropdownValue(31);\n                setIsValidComment(true);\n                setIsOtherSelected(true);\n            } else {\n                setSelectedSubReasonDropdownValue(\"\");\n                setIsOtherSelected(false);\n            }\n        }\n        setSubReasonsList(reasonsMasterList.filter((child)=>child.parent_id == parentId));\n    };\n    const handleChildDropdownSubReasonChange = (e)=>{\n        setSelectedSubReasonDropdownValue(parseInt(e.target.value));\n        setIsValidSubReasons(true);\n    };\n    const handleQuantityChange = (e)=>{\n        const value = e.target.value;\n        setQuantity(value);\n        const quantityValue = parseInt(value, 10) || 0;\n        22;\n        if (quantityValue <= 0 || quantityValue > data[0].CASES_DIFFERENCE - data[0].CASES_ADDED_REASONS) {\n            setIsValidQuantity(false);\n        } else {\n            setIsValidQuantity(true);\n        }\n    };\n    const handleCommentChange = (e)=>{\n        const value = e.target.value;\n        setComment(value);\n    };\n    const handleEditCommentChange = (e)=>{\n        const value = e.target.value;\n        setOnEditComment(value);\n    };\n    const handleEditQuantity = (e, reasonId)=>{\n        const value = e.target.value;\n        const quantityValue = parseInt(value, 10) || 0;\n        let totalExistingQuantity = reasonsData.reduce((total, reason)=>{\n            return reason.id === reasonId ? total : total + reason.quantity;\n        }, 0);\n        const maxAllowedQuantity = data[0].CASES_DIFFERENCE - data[0].CASES_ADDED_REASONS + originalEditQuantity;\n        if (quantityValue <= 0 || quantityValue > maxAllowedQuantity) {\n            setInvalidEditQuantityId(reasonId);\n            setIsValidEditQuantity(false);\n        } else {\n            setInvalidEditQuantityId(\"\");\n            setIsValidEditQuantity(true);\n        }\n        setOnEditQuantityValue(value);\n    };\n    const handleOpenChange = async (event, data)=>{\n        setIsOpen(data.open);\n        if (!data.open) {\n            setIsHeaderChecked(false);\n            setLockedBy(false);\n            setIsBeingEdited(null);\n            setOriginalEditQuantity(null);\n            setQuantity(\"\");\n            setComment(\"\");\n            setOnEditQuantityValue(\"\");\n            setOnEditSelectedReason(\"\");\n            setOnEditSelectedSubReason(\"\");\n            setDeleteId(\"\");\n            setDeleteReason(\"\");\n            setInvalidEditQuantityId(\"\");\n            setInvalidEditReasonsId(\"\");\n            setOriginalEditQuantity(\"\");\n            setIsValidQuantity(true);\n            setIsValidReasons(true);\n            setIsValidSubReasons(true);\n            setReasonsData([]);\n            setData([]);\n            setSelectedRows([]);\n            const serverAddress = _services_apiConfig__WEBPACK_IMPORTED_MODULE_3__.apiConfig.serverAddress;\n            const user = getCookieData(\"user\");\n            await fetch(\"\".concat(serverAddress, \"serviceLevel/remove-locks\"), {\n                method: \"POST\",\n                headers: {\n                    Accept: \"application/json\",\n                    \"Content-Type\": \"application/json\"\n                },\n                credentials: \"include\",\n                body: JSON.stringify({\n                    email: userData.email,\n                    custCode: currentCustomers,\n                    orderId: orderId,\n                    isPayloadRequired: true\n                })\n            }).catch((error)=>{\n                console.log(\"error\", error);\n            });\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!isBulkUpdate && data[0] && data[0].CASES_DIFFERENCE - data[0].CASES_ADDED_REASONS != 0) {\n            setQuantity(String(data[0].CASES_DIFFERENCE - data[0].CASES_ADDED_REASONS));\n        } else {\n            setQuantity(bulkUpdateData.totalCasesDifferent);\n        }\n        if (quantity <= data[0].CASES_DIFFERENCE - data[0].CASES_ADDED_REASONS && quantity != 0) {\n            setIsValidQuantity(true);\n        }\n    }, [\n        data,\n        isBulkUpdate\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fluentui_react_components__WEBPACK_IMPORTED_MODULE_9__.Dialog, {\n                modalType: \"non-modal\",\n                style: {\n                    fontFamily: \"poppinsregular\"\n                },\n                open: isOpen,\n                onOpenChange: handleOpenChange,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fluentui_react_components__WEBPACK_IMPORTED_MODULE_9__.DialogSurface, {\n                    className: \"!max-w-[60%]\",\n                    style: {\n                        fontFamily: \"poppinsregular\"\n                    },\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fluentui_react_components__WEBPACK_IMPORTED_MODULE_9__.DialogBody, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fluentui_react_components__WEBPACK_IMPORTED_MODULE_9__.DialogTitle, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-xl\",\n                                                children: \"View Details\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                                lineNumber: 608,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            !isBulkUpdate && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fluentui_react_components__WEBPACK_IMPORTED_MODULE_9__.Tooltip, {\n                                                content: \"Audit details for order\",\n                                                relationship: \"label\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>setIsAuditDetailsOpen(true),\n                                                    className: \"tooltip-button\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        fill: \"currentcolor\",\n                                                        xmlns: \"http://www.w3.org/2000/svg\",\n                                                        viewBox: \"0 0 512 512\",\n                                                        className: \"w-4 h-5 !text-skin-primary\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            d: \"M16 96a48 48 0 1 1 96 0A48 48 0 1 1 16 96zM64 208a48 48 0 1 1 0 96 48 48 0 1 1 0-96zm0 160a48 48 0 1 1 0 96 48 48 0 1 1 0-96zM191.5 54.4c5.5-4.2 12.3-6.4 19.2-6.4L424 48c13.3 0 24 10.7 24 24l0 48c0 13.3-10.7 24-24 24l-213.3 0c-6.9 0-13.7-2.2-19.2-6.4l-38.4-28.8c-8.5-6.4-8.5-19.2 0-25.6l38.4-28.8zM153.1 243.2l38.4-28.8c5.5-4.2 12.3-6.4 19.2-6.4L488 208c13.3 0 24 10.7 24 24l0 48c0 13.3-10.7 24-24 24l-277.3 0c-6.9 0-13.7-2.2-19.2-6.4l-38.4-28.8c-8.5-6.4-8.5-19.2 0-25.6zm0 160l38.4-28.8c5.5-4.2 12.3-6.4 19.2-6.4L424 368c13.3 0 24 10.7 24 24l0 48c0 13.3-10.7 24-24 24l-213.3 0c-6.9 0-13.7-2.2-19.2-6.4l-38.4-28.8c-8.5-6.4-8.5-19.2 0-25.6z\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                                            lineNumber: 624,\n                                                            columnNumber: 25\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                                        lineNumber: 618,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                                    lineNumber: 614,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                                lineNumber: 610,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                        lineNumber: 607,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"hr\", {\n                                        className: \"border-b border-gray-200\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                        lineNumber: 630,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                lineNumber: 606,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fluentui_react_components__WEBPACK_IMPORTED_MODULE_9__.DialogContent, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-col gap-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex gap-5 justify-between w-full\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-1/4 flex flex-col \",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            htmlFor: \"depotdate\",\n                                                            className: \"text-gray-500\",\n                                                            children: \"Sales Order / Order Det Id\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                                            lineNumber: 638,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"text\",\n                                                            className: \"px-2 2xl:px-3 border rounded-md w-full\",\n                                                            id: \"depotdate\",\n                                                            value: isBulkUpdate ? \"Multiple\" : \"\".concat(currentData[0].ORD_NUMBER, \" / \").concat(currentData[0].ORD_ID),\n                                                            readOnly: true,\n                                                            disabled: true\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                                            lineNumber: 641,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                                    lineNumber: 637,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \" flex w-1/2 flex-col\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            htmlFor: \"depotdate\",\n                                                            className: \"text-gray-500\",\n                                                            children: \"Product\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                                            lineNumber: 655,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"text\",\n                                                            className: \"px-2 2xl:px-3 border rounded-md w-full\",\n                                                            id: \"depotdate\",\n                                                            value: data[0].PRODUCT_DESCRIPTION,\n                                                            readOnly: true,\n                                                            disabled: true\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                                            lineNumber: 658,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                                    lineNumber: 654,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \" flex w-1/4 flex-col justify-end \",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-center rounded-lg w-28 capitalize h-[30px] px-2 py-1 !text-center \".concat(isBulkUpdate ? \"bg-theme-blue2 text-white\" : data[0].ORD_STATUS === \"Cancelled\" ? \"bg-[#ff2929] text-white\" : data[0].ORD_STATUS === \"Open\" ? \"bg-[#54C5ED] text-white\" : data[0].ORD_STATUS === \"Invoiced\" ? \"bg-[#FFAE00] text-white\" : data[0].ORD_STATUS === \"Delivered\" ? \"bg-[#3EAB58] text-white\" : data[0].ORD_STATUS === \"Picked\" ? \"bg-[#FF6C09] text-white\" : data[0].ORD_STATUS === \"CANCELLED-Invoiced\" ? \"bg-cancelled-status text-white\" : \"bg-qtydiff-status !text-gray-700\" // Default style for any other status\n                                                        ),\n                                                        children: isBulkUpdate ? \"Multiple\" : \"\".concat(data[0].ORD_STATUS.toLowerCase())\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                                        lineNumber: 669,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                                    lineNumber: 668,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                            lineNumber: 634,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex gap-5 justify-between w-full\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-1/4 flex flex-col\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            htmlFor: \"depotdate\",\n                                                            className: \"text-gray-500\",\n                                                            children: \"Depot Date\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                                            lineNumber: 696,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"text\",\n                                                            className: \"px-2 2xl:px-3 border rounded-md w-full\",\n                                                            id: \"depotdate\",\n                                                            value: isBulkUpdate ? \"Multiple\" : \"\".concat((0,_utils_whatif_utils_getFormattedDate__WEBPACK_IMPORTED_MODULE_5__.formatDisplay)(data[0].DEPOT_DATE)),\n                                                            readOnly: true,\n                                                            disabled: true\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                                            lineNumber: 699,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                                    lineNumber: 695,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-1/4 flex flex-col\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            htmlFor: \"altfill\",\n                                                            className: \"text-gray-500\",\n                                                            children: \"Altfill\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                                            lineNumber: 713,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"text\",\n                                                            className: \"px-2 2xl:px-3 border rounded-md w-full\",\n                                                            id: \"altfill\",\n                                                            value: data[0].ALTFILID,\n                                                            readOnly: true,\n                                                            disabled: true\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                                            lineNumber: 716,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                                    lineNumber: 712,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-1/4 flex flex-col\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            htmlFor: \"customer\",\n                                                            className: \"text-gray-500\",\n                                                            children: \"Customer\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                                            lineNumber: 726,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"text\",\n                                                            className: \"px-2 2xl:px-3 border rounded-md w-full\",\n                                                            id: \"customer\",\n                                                            value: isBulkUpdate ? \"Multiple\" : \"\".concat(data[0].CUSTOMER),\n                                                            readOnly: true,\n                                                            disabled: true\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                                            lineNumber: 729,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                                    lineNumber: 725,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-1/4 flex flex-col\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            htmlFor: \"category\",\n                                                            className: \"text-gray-500\",\n                                                            children: \"Master Product Code\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                                            lineNumber: 739,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"text\",\n                                                            className: \"px-2 2xl:px-3 border rounded-md w-full\",\n                                                            id: \"category\",\n                                                            value: data[0].MASTER_PRODUCT_CODE,\n                                                            readOnly: true,\n                                                            disabled: true\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                                            lineNumber: 742,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                                    lineNumber: 738,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                            lineNumber: 694,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex gap-5 justify-between w-full\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-1/4 flex flex-col\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            htmlFor: \"casesize\",\n                                                            className: \"text-gray-500\",\n                                                            children: \"Case Size\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                                            lineNumber: 755,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"text\",\n                                                            className: \"px-2 2xl:px-3 border rounded-md w-full\",\n                                                            id: \"casesize\",\n                                                            value: data[0].CASE_SIZE,\n                                                            readOnly: true,\n                                                            disabled: true\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                                            lineNumber: 758,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                                    lineNumber: 754,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-1/4 flex flex-col\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            htmlFor: \"casesord\",\n                                                            className: \"text-gray-500\",\n                                                            children: \"Cases Ordered\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                                            lineNumber: 768,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"text\",\n                                                            className: \"px-2 2xl:px-3 border rounded-md w-full\",\n                                                            id: \"casesord\",\n                                                            value: isBulkUpdate ? bulkUpdateData.totalCasesOrdered : \"\".concat(data[0].CASES_ORIGINAL),\n                                                            readOnly: true,\n                                                            disabled: true\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                                            lineNumber: 771,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                                    lineNumber: 767,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-1/4 flex flex-col\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            htmlFor: \"casedel\",\n                                                            className: \"text-gray-500\",\n                                                            children: \"Cases Delivered\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                                            lineNumber: 785,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"text\",\n                                                            className: \"px-2 2xl:px-3 border rounded-md w-full\",\n                                                            id: \"casedel\",\n                                                            value: isBulkUpdate ? bulkUpdateData.totalCasesDelivered : \"\".concat(data[0].CASES_DELIVERED),\n                                                            readOnly: true,\n                                                            disabled: true\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                                            lineNumber: 788,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                                    lineNumber: 784,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-1/4 flex flex-col\",\n                                                    title: \"The following case differences are the absolute sum of all differences\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            htmlFor: \"casesdiff\",\n                                                            className: \"text-gray-500 font-bold\",\n                                                            children: \"Cases Different\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                                            lineNumber: 805,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"text\",\n                                                            className: \"px-2 2xl:px-3 border rounded-md w-full font-bold\",\n                                                            id: \"casesdiff\",\n                                                            value: isBulkUpdate ? bulkUpdateData.totalCasesDifferent : \"\".concat(data[0].CASES_DIFFERENCE - data[0].CASES_ADDED_REASONS),\n                                                            readOnly: true,\n                                                            disabled: true\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                                            lineNumber: 811,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                                    lineNumber: 801,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                            lineNumber: 753,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        (!isBulkUpdate || isBulkUpdate && reasonsData.length == 0) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"border border-gray-200 p-4 rounded-md\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    style: {\n                                                        fontFamily: \"poppinsregular\"\n                                                    },\n                                                    children: \"Add the reason(s)\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                                    lineNumber: 834,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"hr\", {\n                                                    className: \"border-b border-gray-200\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                                    lineNumber: 837,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex gap-4 justify-between pt-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-[10%] flex flex-col\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    htmlFor: \"quantity\",\n                                                                    className: \"text-gray-500\",\n                                                                    children: \"Quantity\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                                                    lineNumber: 840,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                    type: \"number\",\n                                                                    onChange: handleQuantityChange,\n                                                                    className: \"px-2 2xl:px-3 border rounded-md \".concat(!isValidQuantity && \"!border-red-500\"),\n                                                                    value: quantity,\n                                                                    max: data[0].CASES_DIFFERENCE - data[0].CASES_ADDED_REASONS,\n                                                                    id: \"quantity\",\n                                                                    disabled: isBeingEdited || isBulkUpdate\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                                                    lineNumber: 843,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                                            lineNumber: 839,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-[30%] flex flex-col\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    htmlFor: \"reason\",\n                                                                    className: \"text-gray-500\",\n                                                                    children: \"Reason\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                                                    lineNumber: 859,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                                    onChange: (e)=>handleParentDropdownReasonChange(e, \"add\"),\n                                                                    className: \"px-2 2xl:px-3 border \".concat(!isValidReason && \"!border-red-500\", \" rounded-md w-full h-[31px]\"),\n                                                                    value: selectedReasonDropdownValue,\n                                                                    id: \"reason\",\n                                                                    disabled: isBeingEdited,\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                            value: \"\",\n                                                                            children: \"Select...\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                                                            lineNumber: 874,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        parentReasonList === null || parentReasonList === void 0 ? void 0 : parentReasonList.map((parentReason, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                value: parentReason.id,\n                                                                                children: parentReason.reason\n                                                                            }, \"\".concat(parentReason.id, \"-\").concat(index), false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                                                                lineNumber: 876,\n                                                                                columnNumber: 29\n                                                                            }, undefined))\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                                                    lineNumber: 863,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                                            lineNumber: 858,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-[30%] flex flex-col\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    htmlFor: \"subreasons\",\n                                                                    className: \"text-gray-500\",\n                                                                    children: \"Sub Reason\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                                                    lineNumber: 887,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                                    onChange: handleChildDropdownSubReasonChange,\n                                                                    disabled: !selectedReasonDropdownValue || isBeingEdited || isOtherSelected,\n                                                                    className: \"px-2 2xl:px-3 border rounded-md w-full h-[31px] \".concat(!isValidSubReason && \"!border-red-500\"),\n                                                                    value: selectedSubReasonDropdownValue,\n                                                                    id: \"subreasons\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                            value: \"\",\n                                                                            children: \"Select...\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                                                            lineNumber: 904,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        subReasonsList === null || subReasonsList === void 0 ? void 0 : subReasonsList.map((subReason, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                value: subReason.id,\n                                                                                children: subReason.reason\n                                                                            }, \"\".concat(subReason.id, \"-\").concat(index), false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                                                                lineNumber: 906,\n                                                                                columnNumber: 29\n                                                                            }, undefined))\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                                                    lineNumber: 891,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                                            lineNumber: 886,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-[25%] flex flex-col\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    htmlFor: \"comment\",\n                                                                    className: \"text-gray-500\",\n                                                                    children: \"Comment\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                                                    lineNumber: 916,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                    type: \"text\",\n                                                                    onChange: handleCommentChange,\n                                                                    maxLength: 200,\n                                                                    className: \"px-2 2xl:px-3 border rounded-md w-full \".concat(!isValidComment && \"!border-red-500\"),\n                                                                    id: \"comment\",\n                                                                    value: comment,\n                                                                    disabled: isBeingEdited\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                                                    lineNumber: 919,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                                            lineNumber: 915,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-[5%] flex flex-col\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                className: \"mt-8\",\n                                                                onClick: ()=>saveReasons(),\n                                                                disabled: isSaveButtonDisabled || isBeingEdited || !isValidQuantity,\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                    fill: \"currentcolor\",\n                                                                    xmlns: \"http://www.w3.org/2000/svg\",\n                                                                    viewBox: \"0 0 512 512\",\n                                                                    className: \"w-5 h-5 fill !text-skin-primary\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                        d: \"M57.6 35.8C23.1 20.6-11.3 57.4 6.1 90.9l63 121.2c4.4 8.4 12.6 14.1 22 15.3c0 0 0 0 0 0L266 249.3c3.4 .4 6 3.3 6 6.7s-2.6 6.3-6 6.7L91.1 284.6s0 0 0 0c-9.4 1.2-17.6 6.9-22 15.3L6.1 421.1c-17.4 33.5 17 70.2 51.6 55.1L492.9 285.3c25.5-11.2 25.5-47.4 0-58.6L57.6 35.8z\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                                                        lineNumber: 947,\n                                                                        columnNumber: 29\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                                                    lineNumber: 941,\n                                                                    columnNumber: 27\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                                                lineNumber: 932,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                                            lineNumber: 931,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                                    lineNumber: 838,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                            lineNumber: 833,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"previousreasons flex flex-col gap-3\",\n                                            children: reasonsData === null || reasonsData === void 0 ? void 0 : reasonsData.map((reason, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex flex-col gap-3 bg-gray-100 rounded-md p-4 my-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex gap-4 justify-between pt-3 \",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"w-[10%] flex flex-col\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                htmlFor: \"quantity\",\n                                                                                className: \"text-gray-500\",\n                                                                                children: \"Quantity\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                                                                lineNumber: 967,\n                                                                                columnNumber: 29\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                type: \"number\",\n                                                                                onChange: (e)=>handleEditQuantity(e, reason.id),\n                                                                                className: \"px-2 2xl:px-3 border \".concat(invalidEditQuantityId == reason.id && !isValidEditQuantity && \"!border-red-500\", \" rounded-md w-full\"),\n                                                                                id: \"quantity\",\n                                                                                disabled: isBeingEdited != reason.id || isBulkUpdate,\n                                                                                defaultValue: reason.quantity\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                                                                lineNumber: 970,\n                                                                                columnNumber: 29\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                                                        lineNumber: 966,\n                                                                        columnNumber: 27\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"w-[30%] flex flex-col\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                htmlFor: \"reason1\",\n                                                                                className: \"text-gray-500\",\n                                                                                children: \"Reason\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                                                                lineNumber: 986,\n                                                                                columnNumber: 29\n                                                                            }, undefined),\n                                                                            isBeingEdited != reason.id ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                type: \"text\",\n                                                                                className: \"px-2 2xl:px-3 border \".concat(invalidEditReasonsId == reason.id && !isValidEditReason && \"!border-red-500\", \" rounded-md w-full\"),\n                                                                                id: \"reason1\",\n                                                                                disabled: isBeingEdited != reason.id,\n                                                                                defaultValue: reason.reason\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                                                                lineNumber: 990,\n                                                                                columnNumber: 31\n                                                                            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                                                onChange: (e)=>{\n                                                                                    const selectedValue = e.target.value;\n                                                                                    setOnEditSelectedReason(selectedValue);\n                                                                                    setIsValidEditReasons(true);\n                                                                                    setInvalidEditReasonsId(\"\");\n                                                                                    handleParentDropdownReasonChange(e, \"edit\");\n                                                                                },\n                                                                                className: \"px-2 2xl:px-3 border \".concat(invalidEditReasonsId == reason.id && !isValidEditReason && \"!border-red-500\", \" rounded-md w-full h-[31px]\"),\n                                                                                id: \"reason1\",\n                                                                                value: onEditSelectedReason,\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                        value: \"\",\n                                                                                        children: \"Select...\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                                                                        lineNumber: 1018,\n                                                                                        columnNumber: 33\n                                                                                    }, undefined),\n                                                                                    parentReasonList === null || parentReasonList === void 0 ? void 0 : parentReasonList.map((parentReason, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                            value: parentReason.id,\n                                                                                            children: parentReason.reason\n                                                                                        }, \"\".concat(parentReason.id, \"-\").concat(index), false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                                                                            lineNumber: 1020,\n                                                                                            columnNumber: 35\n                                                                                        }, undefined))\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                                                                lineNumber: 1002,\n                                                                                columnNumber: 31\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                                                        lineNumber: 985,\n                                                                        columnNumber: 27\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"w-[30%] flex flex-col\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                htmlFor: \"subreasons1\",\n                                                                                className: \"text-gray-500\",\n                                                                                children: \"Sub Reason\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                                                                lineNumber: 1031,\n                                                                                columnNumber: 29\n                                                                            }, undefined),\n                                                                            isBeingEdited != reason.id ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                type: \"text\",\n                                                                                onChange: (e)=>setOnEditSelectedSubReason(e.target.value),\n                                                                                className: \"px-2 2xl:px-3 border rounded-md w-full \".concat(invalidEditSubReasonsId == reason.id && !isValidEditSubReason && \"!border-red-500\"),\n                                                                                id: \"subreasons1\",\n                                                                                disabled: isBeingEdited != reason.id,\n                                                                                defaultValue: reason.sub_reason\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                                                                lineNumber: 1038,\n                                                                                columnNumber: 31\n                                                                            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                                                onChange: (e)=>setOnEditSelectedSubReason(e.target.value),\n                                                                                className: \"px-2 2xl:px-3 border rounded-md w-full h-[31px]\",\n                                                                                id: \"subreasons1\",\n                                                                                value: onEditSelectedSubReason,\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                        value: \"\",\n                                                                                        children: \"Select...\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                                                                        lineNumber: 1061,\n                                                                                        columnNumber: 33\n                                                                                    }, undefined),\n                                                                                    subReasonsList === null || subReasonsList === void 0 ? void 0 : subReasonsList.map((subReason)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                            value: subReason.id,\n                                                                                            children: subReason.reason\n                                                                                        }, \"\".concat(subReason.id, \"-\").concat(index), false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                                                                            lineNumber: 1063,\n                                                                                            columnNumber: 35\n                                                                                        }, undefined))\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                                                                lineNumber: 1053,\n                                                                                columnNumber: 31\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                                                        lineNumber: 1030,\n                                                                        columnNumber: 27\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"w-[25%] flex flex-col\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                htmlFor: \"comment\",\n                                                                                className: \"text-gray-500\",\n                                                                                children: \"Comment\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                                                                lineNumber: 1074,\n                                                                                columnNumber: 29\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                type: \"text\",\n                                                                                onChange: handleEditCommentChange,\n                                                                                className: \"px-2 2xl:px-3 border rounded-md w-full\",\n                                                                                id: \"comment\",\n                                                                                disabled: isBeingEdited != reason.id,\n                                                                                defaultValue: reason.comment\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                                                                lineNumber: 1077,\n                                                                                columnNumber: 29\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                                                        lineNumber: 1073,\n                                                                        columnNumber: 27\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"w-[5%] flex flex-col\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                                                        lineNumber: 1086,\n                                                                        columnNumber: 27\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                                                lineNumber: 965,\n                                                                columnNumber: 25\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex justify-between items-center\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex gap-2 items-center\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fluentui_react_components__WEBPACK_IMPORTED_MODULE_9__.Avatar, {\n                                                                                // initials=\"LT\"\n                                                                                color: \"light-teal\",\n                                                                                name: reason.added_by\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                                                                lineNumber: 1090,\n                                                                                columnNumber: 29\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                children: [\n                                                                                    reason.added_by,\n                                                                                    \" \"\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                                                                lineNumber: 1095,\n                                                                                columnNumber: 29\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-gray-400\",\n                                                                                children: reason.date\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                                                                lineNumber: 1096,\n                                                                                columnNumber: 29\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                                                        lineNumber: 1089,\n                                                                        columnNumber: 27\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex justify-end gap-4 pt-2\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_DeleteReasonPopover__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                                setDeleteId: setDeleteId,\n                                                                                setDeleteReason: setDeleteReason,\n                                                                                setIsDeleteTrue: setIsDeleteTrue,\n                                                                                id: reason.id\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                                                                lineNumber: 1099,\n                                                                                columnNumber: 29\n                                                                            }, undefined),\n                                                                            isBeingEdited && isBeingEdited == reason.id ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                                    fill: \"currentcolor\",\n                                                                                    xmlns: \"http://www.w3.org/2000/svg\",\n                                                                                    viewBox: \"0 0 512 512\",\n                                                                                    className: \"w-5 h-5 !text-skin-primary\",\n                                                                                    onClick: ()=>handleEdit(reason.id, data[0].ORD_ID),\n                                                                                    disabled: !isValidEditQuantity || isSaveButtonDisabled,\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                        d: \"M57.6 35.8C23.1 20.6-11.3 57.4 6.1 90.9l63 121.2c4.4 8.4 12.6 14.1 22 15.3c0 0 0 0 0 0L266 249.3c3.4 .4 6 3.3 6 6.7s-2.6 6.3-6 6.7L91.1 284.6s0 0 0 0c-9.4 1.2-17.6 6.9-22 15.3L6.1 421.1c-17.4 33.5 17 70.2 51.6 55.1L492.9 285.3c25.5-11.2 25.5-47.4 0-58.6L57.6 35.8z\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                                                                        lineNumber: 1119,\n                                                                                        columnNumber: 35\n                                                                                    }, undefined)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                                                                    lineNumber: 1107,\n                                                                                    columnNumber: 33\n                                                                                }, undefined)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                                                                lineNumber: 1106,\n                                                                                columnNumber: 31\n                                                                            }, undefined) : !isBulkUpdate ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                className: \"b\",\n                                                                                href: \"\",\n                                                                                onClick: ()=>{\n                                                                                    setIsBeingEdited(reason.id);\n                                                                                    setOriginalEditQuantity(reason.quantity);\n                                                                                    setMapForReasonsParentsAndTheirCorrespondingChildren();\n                                                                                    handleParentDropdownReasonChange(reason.reason_id, \"edit\");\n                                                                                    setOnEditComment(reason.comment);\n                                                                                    setOnEditQuantityValue(reason.quantity);\n                                                                                    setOnEditSelectedReason(reason.reason_id);\n                                                                                    setOnEditSelectedSubReason(reason.subreason_id);\n                                                                                },\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                                    fill: \"currentcolor\",\n                                                                                    xmlns: \"http://www.w3.org/2000/svg\",\n                                                                                    viewBox: \"0 0 512 512\",\n                                                                                    className: \"w-5 h-5 !text-skin-primary\",\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                        d: \"M362.7 19.3L314.3 67.7 444.3 197.7l48.4-48.4c25-25 25-65.5 0-90.5L453.3 19.3c-25-25-65.5-25-90.5 0zm-71 71L58.6 323.5c-10.4 10.4-18 23.3-22.2 37.4L1 481.2C-1.5 489.7 .8 498.8 7 505s15.3 8.5 23.7 6.1l120.3-35.4c14.1-4.2 27-11.8 37.4-22.2L421.7 220.3 291.7 90.3z\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                                                                        lineNumber: 1148,\n                                                                                        columnNumber: 35\n                                                                                    }, undefined)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                                                                    lineNumber: 1142,\n                                                                                    columnNumber: 33\n                                                                                }, undefined)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                                                                lineNumber: 1123,\n                                                                                columnNumber: 31\n                                                                            }, undefined) : \"\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                                                        lineNumber: 1098,\n                                                                        columnNumber: 27\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                                                lineNumber: 1088,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                                        lineNumber: 964,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                }, \"\".concat(reason.id, \"-\").concat(index), false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                                    lineNumber: 963,\n                                                    columnNumber: 21\n                                                }, undefined))\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                            lineNumber: 960,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                    lineNumber: 633,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                lineNumber: 632,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fluentui_react_components__WEBPACK_IMPORTED_MODULE_9__.DialogActions, {\n                                className: \"!mt-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"hr\", {\n                                        className: \"border-b border-gray-200\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                        lineNumber: 1163,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fluentui_react_components__WEBPACK_IMPORTED_MODULE_9__.DialogTrigger, {\n                                        disableButtonEnhancement: true,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            className: \"border rounded-md border-skin-primary text-skin-primary px-5 py-1\",\n                                            children: \"Close\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                            lineNumber: 1165,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                        lineNumber: 1164,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                                lineNumber: 1162,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                        lineNumber: 605,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                    lineNumber: 601,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                lineNumber: 595,\n                columnNumber: 7\n            }, undefined),\n            isAuditDetailsOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_AuditDetails__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                orderId: data[0].ORD_ID,\n                isAuditDetailsOpen: isAuditDetailsOpen,\n                setIsAuditDetailsOpen: setIsAuditDetailsOpen\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\service_level\\\\ViewDetails.jsx\",\n                lineNumber: 1174,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true);\n};\n_s(ViewDetails, \"ZpEicqoPiKnnlcki/uC6TfmdFX4=\", false, function() {\n    return [\n        next_router__WEBPACK_IMPORTED_MODULE_7__.useRouter\n    ];\n});\n_c = ViewDetails;\n/* harmony default export */ __webpack_exports__[\"default\"] = (ViewDetails);\nvar _c;\n$RefreshReg$(_c, \"ViewDetails\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevExports = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevExports) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports on update so we can compare the boundary\n                // signatures.\n                module.hot.dispose(function (data) {\n                    data.prevExports = currentExports;\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevExports !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevExports, currentExports)) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevExports !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/service_level/ViewDetails.jsx\n"));

/***/ })

});