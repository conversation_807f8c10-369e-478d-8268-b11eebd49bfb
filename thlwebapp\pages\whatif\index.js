import React, { useEffect, useState } from "react";
import Layout from "@/components/Layout";
import {
  FluentProvider,
  SSRProvider,
  webLightTheme,
} from "@fluentui/react-components";
import { ModalProvider } from "../../components/whatif/providers/ModalProvider";
import { CurrencyProvider } from "../../components/whatif/providers/CurrencyProvider";
import { getData } from "@/utils/whatif/utils/getProductData";
import Root from "../../components/whatif/Root";
//
import { useMsal } from "@azure/msal-react";
import { toast, ToastContainer } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import Cookies from "js-cookie";
import { useLoading } from "@/utils/loaders/loadingContext";

let userData = {};
const whatif = ({
  userData,
  isTokenExpired,
  requestError,
  customerList,
  productList,
  currentQuarterCalendarData,
  customerDataTotals,
  currentQuarterStdCalendarData,
}) => {
  const { setIsLoading } = useLoading();
  const { instance } = useMsal();

  // const sendMessage = () => {
  //   if (message) {
  //     socket.emit('message', message);
  //     setMessage('');
  //   }
  // };

  useEffect(()=>{
    if(currentQuarterCalendarData.length>0){
      Cookies.set("currentWeek",currentQuarterCalendarData[0].currentWeek)
    }
  },[currentQuarterCalendarData])

  useEffect(() => {
    if(typeof document !=="undefined"){
      document.title="What If"
    }
    setIsLoading(false);
    if (isTokenExpired) {
      console.log("logging out");
      toast.error("Your session has expired. Please log in again.");
      setTimeout(() => {
        localStorage.removeItem("superUser");
        localStorage.removeItem("company");
        localStorage.removeItem("id");
        localStorage.removeItem("name");
        localStorage.removeItem("role");
        localStorage.removeItem("email");
        Cookies.remove("user");
        Cookies.remove("theme");
        Cookies.remove("token");
        const redirectUrl = `/login?redirect=${encodeURIComponent(window.location.pathname)}`;
logoutHandler(instance,redirectUrl);;
      }, 3000);
    } else if (requestError) {
      toast.error(
        "There was an error with your request. Please check your data and try again."
      );
    }
  }, [isTokenExpired, instance]);

  return (
    <Layout userData={userData}>
      <ToastContainer limit={1} />
      <SSRProvider>
        <FluentProvider theme={webLightTheme} className="!bg-transparent" style={{fontFamily:"poppinsregular"}}>
          <CurrencyProvider>
            <ModalProvider userData={userData}>
              <Root
                userData={userData}
                customerList={customerList}
                productList={productList}
                calenderData={currentQuarterCalendarData}
                customerDataTotals={customerDataTotals}
                currentQuarterStdCalendarData={currentQuarterStdCalendarData}
              />
            </ModalProvider>
          </CurrencyProvider>
        </FluentProvider>
      </SSRProvider>
    </Layout>
  );
};

export default whatif;

export const getServerSideProps = async (context) => {
  const token = context.req.cookies.token;
  const filters = context.req.cookies.filters ? JSON.parse(context.req.cookies.filters): null;
  const cust_code = filters?.customer || null;
  
  if (!token) {
    return {
      redirect: {
        destination: `/login?redirect=${encodeURIComponent(
          context.resolvedUrl
        )}`,
        permanent: false,
      },
    };
  }

  try {
    userData = JSON.parse(context.req.cookies.user || "{}");
  } catch (error) {
    console.error("Error parsing userData:", error);
  }

  let customerList = [];

  let currentQuarterCalendarData = [];
  let currentQuarterStdCalendarData = [];
  let customerDataTotals = [];
  let isTokenExpired = false;
  let requestError = false;
  const data = await getData(`get-initial-data/${cust_code}`, userData.token);
 
  if (!data) {
    isTokenExpired = true;
  }
  if (data?.length == 0) {
    requestError = true;
  }

  if (data) {
    currentQuarterCalendarData = data?.currentQuarterCalendarData;
    currentQuarterStdCalendarData = data?.currentQuarterStdCalendarData;
    customerList = data?.whatifCustomers;
    customerDataTotals = data?.whatifTotalsByCustomers;
  }

  return {
    props: {
      isTokenExpired,
      requestError,
      userData,
      token,
      customerList,
      currentQuarterCalendarData,
      customerDataTotals,
      currentQuarterStdCalendarData,
    },
  };
};