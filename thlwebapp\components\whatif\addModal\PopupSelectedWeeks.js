import React, { useContext, useEffect, useRef, useState } from "react";
import { getClculatedFinancials } from "../../../utils/whatif/utils/getClculatedFinancials";
import {
  GRID_PRICE,
  WHATIF_TYPE_BREAKEVEN_ID,
  WHATIF_TYPE_PRICE_ID,
  WHATIF_TYPE_PROMO_ID,
  WHATIF_TYPE_RR_VOLUME_ID,
  WHATIF_TYPE_SUPPLIR_ISSUE_ID,
} from "../../../utils/whatif/utils/constants";
import { toast } from "react-toastify";
import PriceInput from "./PriceInput";
import {
  formatDate,
  formatDisplay,
} from "@/utils/whatif/utils/getFormattedDate";
import { CurrencyContext } from "../providers/CurrencyProvider";
import { getNextQuarterDataByPkey } from "@/utils/whatif/utils/getNextQuarterDataByPkey";
import Cookies from "js-cookie";
import { isDateInPastWeek } from "@/utils/whatif/isDateInPastWeek";
import { getCurrentWeeksFriday } from "@/utils/whatif/utils/getCurrentWeeksFriday";
import { stripTime } from "@/utils/whatif/utils/stripTime";
import findQuarter from "@/utils/whatif/utils/findQuarter";
import { ModalContext } from "../providers/ModalProvider";
import findFirstEmptyKey from "@/utils/whatif/utils/findFirstEmptyKey";
import { addNextQuarterByPkey } from "@/utils/whatif/utils/indexedDB";

function findHighestWeek(data) {
  return data.reduce(
    (max, current) => (current.week > max.week ? current : max),
    data[0]
  );
}

export default function PopupSelectedWeeks({
  popupData,
  setPopupData,
  modalConfig,
  setModalConfig,
  isRemoveConfirmationShown,
  allQuarters,
  setPreviousPromoValues,
  quarters,
  setRemoveExtra,
  currentYear,
  overlappingWeek,
  storedWeeks,
  updatePopupData,
  currentData,
  isFetchingNextQuarterData,
  initialValue,
  setInitialValue,
}) {
  const { setCurrentData, ctxCalenderData } = useContext(ModalContext);
  const { currency } = useContext(CurrencyContext);
  const lastVolumeRef = useRef(null);

  const [rowAdded, setRowAdded] = useState(false);
  const [initialValueLoad, setInitialValueLoad] = useState(true);

  useEffect(() => {
    if (
      (initialValueLoad || rowAdded || updatePopupData) &&
      popupData.selectedWeeks &&
      popupData.selectedWeeks.length > 0
    ) {
      // Filter out weeks that are already in initialValue
      const newWeeks = popupData.selectedWeeks.filter(
        (week) => !initialValue[week.weekNumber]
      );

      // Transform the filtered array into an object with weekNumber as the key
      const weeksByNumber = newWeeks.reduce((acc, week) => {
        acc[week.weekNumber] = week;
        return acc;
      }, {});

      // Merge the new weeks with the existing initialValue
      const updatedInitialValue = { ...initialValue, ...weeksByNumber };

      // Set the updated initial value
      setInitialValue(updatedInitialValue);
      setInitialValueLoad(false);
    } else {
      console.log("No new values added", rowAdded);
    }
  }, [updatePopupData, popupData, rowAdded]);

  useEffect(() => {
    // console.log("popupData---------------------------------",popupData);
    if (rowAdded && lastVolumeRef.current) {
      setRowAdded(false);
      lastVolumeRef.current.focus();
    }
    // popupData
  }, [rowAdded]);

  const changeBeHandler = async (newValue, week) => {
    const claculatedSales = await getClculatedFinancials(
      week?.current?.price ?? week?.previous?.price,
      week?.current?.volume ?? week?.previous?.volume,
      +newValue,
      modalConfig.caseSize
    );

    setPopupData((prev) => {
      return {
        ...prev,
        selectedWeeks: prev.selectedWeeks.map((week2) => {
          if (week2.weekNumber === week.weekNumber) {
            return {
              ...week2,
              current: {
                ...week2.current,
                ["be"]: +newValue,
                ["sales"]: claculatedSales[0],
                ["gp"]: claculatedSales[1],
                ["gp_percent"]: claculatedSales[2],
              },
            };
          }
          return week2;
        }),
      };
    });
  };

  const changePriceHandler = async (newValue, week) => {
    const claculatedSales = await getClculatedFinancials(
      +newValue,
      week?.current?.volume ?? week?.previous?.volume,
      week?.current?.be ?? week?.previous?.be,
      modalConfig.caseSize
    );

    setPopupData((prev) => {
      return {
        ...prev,
        selectedWeeks: prev.selectedWeeks.map((week2) => {
          if (modalConfig.grid !== GRID_PRICE) {
            if (week2.weekNumber === week.weekNumber) {
              return {
                ...week2,
                current: {
                  ...week2.current,
                  ["price"]: +newValue,
                  ["sales"]: claculatedSales[0],
                  ["gp"]: claculatedSales[1],
                  ["gp_percent"]: claculatedSales[2],
                },
              };
            }
          } else {
            return {
              ...week2,
              current: {
                ...week2.current,
                ["price"]: +newValue,
                ["sales"]: claculatedSales[0],
                ["gp"]: claculatedSales[1],
                ["gp_percent"]: claculatedSales[2],
              },
            };
          }

          return week2;
        }),
      };
    });
  };
  return (
    <>
      <div className="flex flex-row justify-between mt-3">
        <h3 className="font-poppinssemibold text-sm pl-1">Prophet value</h3>
        <div className="flex flex-row gap-3">
          <div
            className={` flex flex-row gap-2 ${
              overlappingWeek.length == 0
                ? "cursor-pointer text-skin-primary"
                : "text-gray-300 cursor-not-allowed"
            }`}
            onClick={async () => {
              if (overlappingWeek.length > 0) {
                return;
              }
              let fetchedNow = false;
              let previousCombined = {};
              let nextSessionData = [];
              let allQuartersCombined = [];

              if (isRemoveConfirmationShown) return;

              const lastItem = popupData.selectedWeeks.slice(-1);

              let nextWeekNumber;
              let nextFiscalYear = parseInt(currentYear.value, 10);

              if (lastItem[0].weekNumber === 52) {
                nextWeekNumber = 1;
                nextFiscalYear += 1;
              } else {
                nextWeekNumber = lastItem[0].weekNumber + 1;
              }

              if (
                lastItem[0].weekNumber === 52 &&
                nextWeekNumber == 1 &&
                modalConfig.NoDataNextFY_Q1 == 1
              ) {
                toast.info(
                  "Product data not available for the next financial year",
                  {
                    theme: "colored",
                    autoClose: 5000,
                  }
                );
                return;
              }

              const nextWeekKey = `${nextWeekNumber}-${nextFiscalYear}`;
              
              if (nextWeekNumber === 1 && !quarters.Q5.length > 0) {
                toast.info("Data not available for the next financial year", {
                  theme: "colored",
                  autoClose: 5000,
                });
                return;
              } else if (modalConfig.previousValues[nextWeekKey] && modalConfig.previousValues[nextWeekKey].hasData == 0) {
                toast.info("No data available for next week", {
                  theme: "colored",
                  autoClose: 5000,
                });
                return;
              } else if (
                modalConfig.previousValues[nextWeekKey] &&
                nextWeekNumber >= lastItem[0].nextWeekHavingWhatif &&
                nextFiscalYear == lastItem[0].fiscalYear &&
                !lastItem[0].isNextWeekHavingWhatifInNextFY
              ) {
                toast.info(
                  "Cannot add week as there already exists a Promotion in the next week",
                  {
                    theme: "colored",
                    autoClose: 5000,
                  }
                );
                return;
              } else if (!modalConfig.previousValues[nextWeekKey]) {
                const token = Cookies.get("token");

                nextSessionData =
                  (await getNextQuarterDataByPkey(
                    token,
                    modalConfig.selectedProductPkey,
                    lastItem[0].weekNumber + 1,
                    currentYear.value,
                    modalConfig.whatIfs[0].whatif_id
                  )) ?? [];

                setCurrentData((prev) => {
                  const productIndex = prev.findIndex(
                    (product) =>
                      product.pkey === modalConfig.selectedProductPkey
                  );
                  const nextQuarterKey = findFirstEmptyKey(
                    prev[productIndex].quarters
                  );

                  if (!nextQuarterKey) return prev;

                  prev[productIndex].quarters[nextQuarterKey] =
                    nextSessionData.nextQuarterProductDataByWeek;
                  addNextQuarterByPkey(
                    prev[productIndex].pkey,
                    nextQuarterKey,
                    nextSessionData.nextQuarterProductDataByWeek
                  );
                  return prev;
                });

                if (!nextSessionData) {
                  toast.error(
                    "Error occured while trying to add next quarters data."
                  );
                  return;
                }
                allQuartersCombined = [
                  ...allQuarters,
                  ...nextSessionData.nextQuarterProductDataByWeek,
                ];

                allQuartersCombined.map((data) => {
                  previousCombined[`${data.week}-${nextFiscalYear}`] = {
                    volume: !!data.data[0].value ? data.data[0].value : 0,
                    be: !!data.data[1].value ? data.data[1].value : 0,
                    price: !!data.data[2].value ? data.data[2].value : 0,
                    sales: !!data.data[7].value ? data.data[7].value : 0,
                    gp: !!data.data[3].value ? data.data[3].value : 0,
                    gp_percent: !!data.data[4].value ? data.data[4].value : 0,
                  };
                });

                setModalConfig((prev) => {
                  return { ...prev, previousValues: previousCombined };
                });

                fetchedNow = true;
              }

              let currentDate = new Date(lastItem[0].weekStartDate);

              const index = ctxCalenderData.findIndex(
                (item) =>
                  new Date(item.startweek).getTime() === currentDate.getTime()
              );

              if (index !== -1 && index + 1 < ctxCalenderData.length) {
                // Get the next week's start date
                currentDate = new Date(ctxCalenderData[index + 1].startweek);
              } else {
                console.error(
                  "Next week start date not found or out of bounds."
                );
              }

              let startWeekDate = new Date(currentDate);

              // Check if the date is valid
              if (!isNaN(startWeekDate.getTime())) {
                startWeekDate = startWeekDate.toISOString().split("T")[0]; // Format to YYYY-MM-DD
              }

              const matchingWeek = ctxCalenderData.filter(
                (item) => item.startweek == startWeekDate
              );

              let endWeekDate =
                matchingWeek.length > 0 && matchingWeek[0].endweek;
              const previous = {
                volume: fetchedNow
                  ? previousCombined[nextWeekKey]?.volume ?? 0
                  : modalConfig.previousValues[nextWeekKey]?.volume,
                price: fetchedNow
                  ? previousCombined[nextWeekKey]?.price ?? 0
                  : modalConfig.previousValues[nextWeekKey]?.price,
                sales: fetchedNow
                  ? previousCombined[nextWeekKey]?.sales ?? 0
                  : modalConfig.previousValues[nextWeekKey]?.sales,
                be: fetchedNow
                  ? previousCombined[nextWeekKey]?.be ?? 0
                  : modalConfig.previousValues[nextWeekKey]?.be,
                gp: fetchedNow
                  ? previousCombined[nextWeekKey]?.gp ?? 0
                  : modalConfig.previousValues[nextWeekKey]?.gp,
                gp_percent: fetchedNow
                  ? previousCombined[nextWeekKey]?.gp_percent ?? 0
                  : modalConfig.previousValues[nextWeekKey]?.gp_percent,
              };

              let lastWeek = findHighestWeek(allQuartersCombined)?.week + 1;
              // #region nexthaving whatif part start
              const currentProduct = currentData.filter(
                (data) => data.pkey === modalConfig.selectedProductPkey
              );
              const promotions = currentProduct[0].promotions;
              const nextWeekWhatifs = promotions.filter(
                (promo) => promo.promo_start_week_no > nextWeekNumber
              );

              let isNextWeekHavingWhatifInNextFY = false;
              let fiscalYear = modalConfig.fiscalYear;
              if (!nextWeekWhatifs.length) {
                isNextWeekHavingWhatifInNextFY =
                  ctxCalenderData.slice(-1)[0].fiscalyear > fiscalYear;
              }
              // #region nexthaving whatif part end

              if (nextSessionData.nextWeekHavingWhatif) {
                lastWeek = nextSessionData.nextWeekHavingWhatif;
              }

              setPopupData((prev) => {
                return {
                  ...prev,
                  selectedWeeks: [
                    ...prev.selectedWeeks,
                    {
                      weekNumber: nextWeekNumber,
                      isNew: true,
                      weekStartDate: formatDate(currentDate.toISOString()),
                      weekEndDate: endWeekDate,
                      previous,
                      fiscalYear: nextFiscalYear,
                      isNextWeekHavingWhatifInNextFY:
                        isNextWeekHavingWhatifInNextFY,
                      current: previous,
                      nextWeekHavingWhatif: !fetchedNow
                        ? lastItem[0].nextWeekHavingWhatif
                        : lastWeek,
                    },
                  ],
                };
              });

              setPreviousPromoValues((prev) => {
                const quarter = findQuarter(nextWeekNumber, quarters);
                prev[nextWeekNumber] = {
                  quarter,
                  current: previous,
                };
                return prev;
              });

              setRowAdded(true);
            }}
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              viewBox="0 0 512 512"
              className={`w-6 h-6 ${
                overlappingWeek.length == 0
                  ? "fill-skin-primary"
                  : "fill-gray-300"
              }`}
            >
              <path d="M256 32a224 224 0 1 1 0 448 224 224 0 1 1 0-448zm0 480A256 256 0 1 0 256 0a256 256 0 1 0 0 512zM240 352c0 8.8 7.2 16 16 16s16-7.2 16-16V272h80c8.8 0 16-7.2 16-16s-7.2-16-16-16H272V160c0-8.8-7.2-16-16-16s-16 7.2-16 16v80H160c-8.8 0-16 7.2-16 16s7.2 16 16 16h80v80z" />
            </svg>
            Add Week
          </div>
          <div
            className={` text-issue-status flex flex-row gap-2 ${
              overlappingWeek.length == 0
                ? "cursor-pointer text-issue-status"
                : "!text-gray-300 cursor-not-allowed"
            }`}
            onClick={() => {
              if (isRemoveConfirmationShown) return;
              if (popupData.selectedWeeks.length > 1) {
                const modifiedSelectedWeeks = [
                  ...popupData.selectedWeeks.slice(0, -1),
                ];

                setPopupData((prev) => {
                  return {
                    ...prev,
                    selectedWeeks: modifiedSelectedWeeks,
                  };
                });

                setPreviousPromoValues((prev) => {
                  const newPrev = { ...prev };

                  // Get the keys and sort them
                  let keys = Object.keys(newPrev);
                  keys = keys.sort();

                  // Get the last key in the sorted list
                  const lastKey = keys.slice(-1)[0];
                  // Delete the last key
                  delete prev[lastKey];

                  setRemoveExtra((prev) => {
                    if (!lastKey) return prev;

                    if (!Object.keys(prev).includes(lastKey)) {
                      prev[lastKey] = {};
                      const thisWeek = popupData.selectedWeeks.filter(
                        (week) => week.weekNumber == lastKey
                      )[0];
                      prev[lastKey].volumeDiff =
                        thisWeek.current.volume - thisWeek.previous.volume;
                      prev[lastKey].saleDiff =
                        thisWeek.current.sales - thisWeek.previous.sales;
                      prev[lastKey].gpDiff =
                        thisWeek.current.gp - thisWeek.previous.gp;
                    }

                    return prev;
                  });

                  return prev;
                });
              } else {
                toast.info(
                  "You cannot remove the last week. At least one week must be selected.",
                  {
                    theme: "colored",
                    autoClose: 5000,
                  }
                );
              }
            }}
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              viewBox="0 0 512 512"
              fill={overlappingWeek.length == 0 ? `#FB4646` : "#d1d5db"}
              className="w-6 h-6"
            >
              <path d="M256 32a224 224 0 1 1 0 448 224 224 0 1 1 0-448zm0 480A256 256 0 1 0 256 0a256 256 0 1 0 0 512zM160 240c-8.8 0-16 7.2-16 16s7.2 16 16 16H352c8.8 0 16-7.2 16-16s-7.2-16-16-16H160z" />
            </svg>
            Remove Week
          </div>
        </div>
      </div>

      <div className="pb-3">
        <table className="w-full table-fixed" cellSpacing={4} cellPadding={4}>
          <thead>
            <tr>
              <td className="text-gray-400 !w-48">Week(s)</td>
              <td className="text-gray-400">Volume</td>
              <td className="text-gray-400">BE</td>
              <td className="text-gray-400">Price</td>
              <td className="text-gray-400">GP</td>
              <td className="text-gray-400">Sales</td>
              <td className="text-gray-400">GP%</td>
            </tr>
          </thead>
          <tbody>
            {popupData.selectedWeeks &&
              popupData.selectedWeeks.map((week) => {
                return (
                  <tr key={week.weekNumber}>
                    <td>
                      <div className="w-full flex flex-row">
                        <input
                          type="text"
                          name=""
                          className="px-2 2xl:px-3 !bg-seablue border !border-seablue rounded-tl-md rounded-bl-md w-1/4 text-center text-bold"
                          value={week.weekNumber}
                          disabled={true}
                          readOnly
                        />
                        <input
                          type="text"
                          name=""
                          className="px-2 2xl:px-3 border rounded-tr-md rounded-br-md w-3/4 ml-0"
                          value={formatDisplay(week.weekStartDate)}
                          disabled={true}
                          readOnly
                        />
                      </div>
                    </td>
                    <td>
                      <input
                        type="text"
                        name="volume_prev"
                        className="px-2 2xl:px-3 border rounded-md w-full"
                        value={week?.previous?.volume ?? 0}
                        disabled={true}
                        readOnly
                      />
                    </td>
                    <td>
                      <input
                        type="text"
                        name="be_prev"
                        className="px-2 2xl:px-3 border rounded-md w-full"
                        value={
                          week?.previous?.be < 0
                            ? `-${currency}${Math.abs(
                                week?.previous?.be
                              ).toFixed(2)}`
                            : `${currency}${week?.previous?.be?.toFixed(2)}` ??
                              "0.00"
                        }
                        disabled={true}
                        readOnly
                      />
                    </td>
                    <td>
                      <input
                        type="text"
                        name="price_prev"
                        className="px-2 2xl:px-3 border rounded-md w-full"
                        value={
                          week?.previous?.price < 0
                            ? `-${currency}${Math.abs(
                                week?.previous?.price
                              ).toFixed(2)}`
                            : `${currency}${week?.previous?.price?.toFixed(
                                2
                              )}` ?? `${currency}0.00`
                        }
                        disabled={true}
                        readOnly
                      />
                    </td>
                    <td>
                      <input
                        type="text"
                        name=""
                        className="px-2 2xl:px-3 border rounded-md w-full"
                        value={
                          week?.previous?.gp < 0
                            ? `-${currency}${Math.abs(
                                week?.previous?.gp
                              ).toFixed(2)}`
                            : `${currency}${week?.previous?.gp?.toFixed(2)}` ??
                              `${currency}0.00`
                        }
                        disabled={true}
                        readOnly
                      />
                    </td>
                    <td>
                      <input
                        type="text"
                        name=""
                        className="px-2 2xl:px-3 border rounded-md w-full"
                        value={
                          week?.previous?.sales
                            ? week?.previous?.sales < 0
                              ? `-${currency}${Math.abs(
                                  week?.previous?.sales
                                ).toFixed(2)}`
                              : `${currency}${week?.previous?.sales?.toFixed(
                                  2
                                )}`
                            : `${currency}0.00`
                        }
                        disabled={true}
                        readOnly
                      />
                    </td>
                    <td>
                      <input
                        type="text"
                        name=""
                        className="px-2 2xl:px-3 border rounded-md w-full"
                        value={
                          week?.previous?.gp_percent < 0
                            ? `-${Math.abs(week?.previous?.gp_percent).toFixed(
                                2
                              )}%`
                            : `${week?.previous?.gp_percent?.toFixed(2)}%` ??
                              `0.00%`
                        }
                        disabled={true}
                        readOnly
                      />
                    </td>
                  </tr>
                );
              })}
            <tr>
              <td colSpan={7}>
                <h3 className="font-poppinssemibold text-sm pt-3">Current</h3>
                <hr className="border border-gray-200 mb-2"></hr>
              </td>
            </tr>

            {popupData.selectedWeeks &&
              popupData.selectedWeeks.map((week, i) => {
                // setInitialValue(week?.current);
                // console.log("***************initialValue",initialValue);
                const gp =
                  week?.current?.gp?.toFixed(2) ??
                  week?.previous?.gp?.toFixed(2);
                const sales =
                  week?.current?.sales?.toFixed(2) ??
                  week?.previous?.sales?.toFixed(2);
                const gpPer =
                  week?.current?.gp_percent?.toFixed(2) ??
                  week?.previous?.gp_percent?.toFixed(2);

                const isLast = popupData.selectedWeeks.length - 1 === i;
                const isInPastWeek =
                  isDateInPastWeek(week.weekStartDate) ||
                  stripTime(new Date()) >=
                    stripTime(getCurrentWeeksFriday(week.weekStartDate, true));

                return (
                  <tr key={week.weekNumber}>
                    <td>
                      <div className="w-full flex flex-row">
                        <input
                          type="text"
                          name="weekNumber"
                          className="px-2 2xl:px-3 !bg-seablue border !border-seablue rounded-tl-md rounded-bl-md w-1/4 text-center  text-bold"
                          value={week.weekNumber}
                          readOnly
                          disabled={true}
                        />
                        <input
                          type="text"
                          name=""
                          className="px-2 2xl:px-3 border rounded-tr-md rounded-br-md w-3/4 ml-0"
                          value={formatDisplay(week.weekStartDate)}
                          readOnly
                          disabled={true}
                        />
                      </div>
                    </td>
                    <td>
                      <input
                        type="number"
                        name="volume_curr"
                        step="1"
                        min="0"
                        max="10000"
                        ref={isLast ? lastVolumeRef : null}
                        className="px-2 2xl:px-3 border rounded-md w-full"
                        maxLength={10}
                        value={week?.current?.volume ?? week?.previous?.volume}
                        onChange={async (e) => {
                          const newValue = e.target.value;
                          const claculatedSales = await getClculatedFinancials(
                            week?.current?.price ?? week?.previous?.price,
                            +newValue,
                            week?.current?.be ?? week?.previous?.be,
                            modalConfig.caseSize
                          );

                          setPopupData((prev) => {
                            return {
                              ...prev,
                              selectedWeeks: prev.selectedWeeks.map((week2) => {
                                if (week2.weekNumber === week.weekNumber) {
                                  return {
                                    ...week2,
                                    current: {
                                      ...week2.current,
                                      ["volume"]: +newValue,
                                      ["sales"]: claculatedSales[0],
                                      ["gp"]: claculatedSales[1],
                                      ["gp_percent"]: claculatedSales[2],
                                    },
                                  };
                                }
                                return week2;
                              }),
                            };
                          });
                        }}
                        disabled={
                          (popupData.whatIfType !== WHATIF_TYPE_PROMO_ID &&
                            popupData.whatIfType !== WHATIF_TYPE_RR_VOLUME_ID &&
                            popupData.whatIfType !==
                              WHATIF_TYPE_SUPPLIR_ISSUE_ID) ||
                          isRemoveConfirmationShown ||
                          isInPastWeek
                        }
                      />
                    </td>
                    <td>
                      <PriceInput
                        name="be_curr"
                        previous={week?.current?.be ?? week?.previous?.be}
                        disabled={
                          (popupData.whatIfType !== WHATIF_TYPE_PROMO_ID &&
                            popupData.whatIfType !==
                              WHATIF_TYPE_BREAKEVEN_ID) ||
                          isRemoveConfirmationShown ||
                          isInPastWeek
                        }
                        whatIfType={popupData.whatIfType}
                        onChange={(newValue) => {
                          changeBeHandler(newValue, week);
                        }}
                      />
                    </td>
                    <td>
                      <PriceInput
                        name="price_curr"
                        previous={week?.current?.price ?? week?.previous?.price}
                        disabled={
                          (popupData.whatIfType !== WHATIF_TYPE_PROMO_ID &&
                            popupData.whatIfType !== WHATIF_TYPE_PRICE_ID) ||
                          isRemoveConfirmationShown ||
                          isInPastWeek
                        }
                        whatIfType={popupData.whatIfType}
                        onChange={(newValue) => {
                          changePriceHandler(newValue, week);
                        }}
                      />
                    </td>
                    <td>
                      <input
                        type="text"
                        name="gp_curr"
                        className="px-2 2xl:px-3 border rounded-md w-full"
                        value={
                          gp < 0
                            ? `-${currency}${Math.abs(gp)}`
                            : `${currency}${gp}` ?? "0.00"
                        }
                        disabled={true}
                        readOnly
                      />
                    </td>
                    <td>
                      <input
                        type="text"
                        name="sales_curr"
                        className="px-2 2xl:px-3 border rounded-md w-full"
                        value={
                          sales < 0
                            ? `-${currency}${Math.abs(sales)}`
                            : `${currency}${sales}` ?? "0.00"
                        }
                        disabled={true}
                        readOnly
                      />
                    </td>
                    <td>
                      <input
                        type="text"
                        name="gpper_curr"
                        className="px-2 2xl:px-3 border rounded-md w-full"
                        value={
                          gpPer < 0
                            ? `-${Math.abs(gpPer)}%`
                            : `${gpPer}%` ?? "0.00%"
                        }
                        disabled={true}
                        readOnly
                      />
                    </td>
                  </tr>
                );
              })}
          </tbody>
        </table>
        {overlappingWeek.length > 0 && (
          <div className="flex justify-between bg-gray-100 px-3 py-2 mt-2 items-center border border-gray-300 rounded-lg">
            <p className="font-bold">
              This promotion spans multiple quarters. Click "Load" to fetch the
              entire promotion.{" "}
            </p>
            <div className="space-x-2 flex justify-end">
              <button
                className={`px-4 py-2 ${
                  isFetchingNextQuarterData
                    ? "bg-green-300 cursor-not-allowed"
                    : "bg-green-500 cursor-pointer"
                } rounded-md text-white`}
                onClick={updatePopupData}
              >
                Load
              </button>
            </div>
          </div>
        )}
      </div>
    </>
  );
}
