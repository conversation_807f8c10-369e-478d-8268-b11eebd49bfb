"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/suppliers",{

/***/ "./utils/renderer/actionRenderer.js":
/*!******************************************!*\
  !*** ./utils/renderer/actionRenderer.js ***!
  \******************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _fortawesome_react_fontawesome__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @fortawesome/react-fontawesome */ \"./node_modules/@fortawesome/react-fontawesome/index.es.js\");\n/* harmony import */ var _fortawesome_free_solid_svg_icons__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @fortawesome/free-solid-svg-icons */ \"./node_modules/@fortawesome/free-solid-svg-icons/index.mjs\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/link */ \"./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _services_apiConfig__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/services/apiConfig */ \"./services/apiConfig.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var js_cookie__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! js-cookie */ \"./node_modules/js-cookie/dist/js.cookie.mjs\");\n/* harmony import */ var _exportExcel__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../exportExcel */ \"./utils/exportExcel.js\");\n/* harmony import */ var react_toastify__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! react-toastify */ \"./node_modules/react-toastify/dist/react-toastify.esm.mjs\");\n/* harmony import */ var react_toastify_dist_ReactToastify_css__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! react-toastify/dist/ReactToastify.css */ \"./node_modules/react-toastify/dist/ReactToastify.css\");\n/* harmony import */ var react_toastify_dist_ReactToastify_css__WEBPACK_IMPORTED_MODULE_9___default = /*#__PURE__*/__webpack_require__.n(react_toastify_dist_ReactToastify_css__WEBPACK_IMPORTED_MODULE_9__);\n/* harmony import */ var _utils_userContext__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/utils/userContext */ \"./utils/userContext.js\");\n/* harmony import */ var _utils_getCookieData__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/utils/getCookieData */ \"./utils/getCookieData.js\");\n/* harmony import */ var _components_CopySupplier__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/components/CopySupplier */ \"./components/CopySupplier.js\");\n/* harmony import */ var _barrel_optimize_names_Dialog_Transition_headlessui_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Dialog,Transition!=!@headlessui/react */ \"__barrel_optimize__?names=Dialog,Transition!=!./node_modules/@headlessui/react/dist/headlessui.esm.js\");\n/* harmony import */ var _utils_rolePermissionsContext__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/utils/rolePermissionsContext */ \"./utils/rolePermissionsContext.js\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst actionRenderer = (params, userData, token, company)=>{\n    var _supplierData_prophets_;\n    _s();\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const canExport = (userData === null || userData === void 0 ? void 0 : userData.role_id) === 1 || (userData === null || userData === void 0 ? void 0 : userData.role_id) === 2 || (userData === null || userData === void 0 ? void 0 : userData.role_id) === 5 || (userData === null || userData === void 0 ? void 0 : userData.role_id) === 6 || params.data.isEmergencyRequest;\n    const { permissions, updatePermissions } = (0,_utils_rolePermissionsContext__WEBPACK_IMPORTED_MODULE_13__.usePermissions)();\n    const serverAddress = _services_apiConfig__WEBPACK_IMPORTED_MODULE_4__.apiConfig.serverAddress;\n    const { userDetails, updateToken } = (0,_utils_userContext__WEBPACK_IMPORTED_MODULE_10__.useUser)();\n    const supplierData = params.data;\n    let prophet_id = (supplierData === null || supplierData === void 0 ? void 0 : supplierData.prophets.length) > 0 && (supplierData === null || supplierData === void 0 ? void 0 : (_supplierData_prophets_ = supplierData.prophets[0]) === null || _supplierData_prophets_ === void 0 ? void 0 : _supplierData_prophets_.prophet_id);\n    const role_ids = params.data.roleId.map((ele)=>ele.role_id);\n    const supplier_id = params.data.id;\n    const supplier_status = params.data.status;\n    const [status, setStatus] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(\"\");\n    const [data, setData] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)([]);\n    const user = (0,_utils_getCookieData__WEBPACK_IMPORTED_MODULE_11__.getCookieData)(\"user\");\n    const [isOpen, setIsOpen] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(false);\n    const [isOpenOption, setIsOpenOption] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(false);\n    const [selectedExportType, setSelectedExportType] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(\"\");\n    const [emailStatusPopup, setEmailStatusPopup] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(false);\n    const [popupMessage, setPopUpMessage] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(\"\");\n    const [internalExportSuccess, setInternalExportSuccess] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(false);\n    const [ISSExportSuccess, setISSExportSuccess] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(false);\n    const closeOptionModal = ()=>{\n        setIsOpenOption(false);\n    };\n    const [isCancelOpen, setIsCancelOpen] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(false);\n    const cancelProduct = ()=>{\n        setIsCancelOpen(true);\n    };\n    const closeCancelModal = ()=>{\n        setIsCancelOpen(false);\n    };\n    const closeEmailPopup = ()=>{\n        setEmailStatusPopup(false);\n        if (internalExportSuccess && ISSExportSuccess) {\n            params.setUpdateStatusChange(data === null || data === void 0 ? void 0 : data.id, data === null || data === void 0 ? void 0 : data.status);\n        }\n    };\n    function saveModalData() {\n        var _params_data, _params_data1;\n        let serverAddress = _services_apiConfig__WEBPACK_IMPORTED_MODULE_4__.apiConfig.serverAddress;\n        // setLoading(true);\n        fetch(\"\".concat(serverAddress, \"suppliers/update-supplier/\").concat(supplier_id), {\n            method: \"PUT\",\n            headers: {\n                Accept: \"application/json\",\n                \"Content-Type\": \"application/json\",\n                Authorization: \"Bearer \".concat(userData.token)\n            },\n            body: JSON.stringify({\n                sectionName: \"updateStatus\",\n                type: \"cancelProduct\",\n                status: 6,\n                updated_date: new Date().toISOString(),\n                company_name: params === null || params === void 0 ? void 0 : (_params_data = params.data) === null || _params_data === void 0 ? void 0 : _params_data.company_name,\n                requestor_name: params.data.requestor,\n                requestor_email: (_params_data1 = params.data) === null || _params_data1 === void 0 ? void 0 : _params_data1.requestor_email\n            })\n        }).then((res)=>{\n            if (res.status === 400) {\n                react_toastify__WEBPACK_IMPORTED_MODULE_8__.toast.error(\"There was an error with your request. Please check your data and try again.\");\n            } else if (res.status === 401) {\n                react_toastify__WEBPACK_IMPORTED_MODULE_8__.toast.error(\"Your session has expired. Please log in again.\");\n            // setTimeout(() => {\n            //   localStorage.removeItem(\"superUser\");\n            //   Cookies.remove(\"company\")\n            //   localStorage.removeItem(\"id\");\n            //   localStorage.removeItem(\"name\");\n            //   localStorage.removeItem(\"role\");\n            //   localStorage.removeItem(\"email\");\n            //   Cookies.remove(\"user\")\n            //   Cookies.remove(\"theme\")\n            //   Cookies.remove(\"token\")\n            //   // logoutHandler(instance);\n            // }, 3000);\n            }\n            if (res.status === 200) {\n                return res.json();\n            }\n            return Promise.reject(res);\n        }).then((json)=>{\n            react_toastify__WEBPACK_IMPORTED_MODULE_8__.toast.success(\"Supplier cancelled successfully\", {\n                position: \"top-right\"\n            });\n            params.setUpdateStatusChange(data === null || data === void 0 ? void 0 : data.id, data === null || data === void 0 ? void 0 : data.status);\n            closeCancelModal();\n        }).catch((err)=>{\n            // setLoading(false);\n            react_toastify__WEBPACK_IMPORTED_MODULE_8__.toast.error(\"Error cancelling product:\", err.statusText, {\n                position: \"top-right\"\n            });\n            return err;\n        });\n    }\n    const openOptionModal = (supplier_id)=>{\n        if (supplierData.status == \"Completed\" || supplierData.status == \"Exported\" || params.data.isEmergencyRequest && params.data.General === \"Complete\") {\n            var _params_data_prophets_, _params_data, _params_data_prophets_1, _params_data1, _params_data2, _params_data3, _params_data_roleIds, _params_data4, _params_data_roleIds1, _params_data5, _params_data6, _params_data7, _params_data8;\n            let isExportableBasedOnCodeUnique = false;\n            const codeCount = (_params_data = params.data) === null || _params_data === void 0 ? void 0 : (_params_data_prophets_ = _params_data.prophets[0]) === null || _params_data_prophets_ === void 0 ? void 0 : _params_data_prophets_.code_count;\n            const prophetCode = (_params_data1 = params.data) === null || _params_data1 === void 0 ? void 0 : (_params_data_prophets_1 = _params_data1.prophets[0]) === null || _params_data_prophets_1 === void 0 ? void 0 : _params_data_prophets_1.prophet_code;\n            const prophet_id = ((_params_data2 = params.data) === null || _params_data2 === void 0 ? void 0 : _params_data2.prophets.length) > 0 && ((_params_data3 = params.data) === null || _params_data3 === void 0 ? void 0 : _params_data3.prophets[0].prophet_id);\n            const isSupplierAccount = ((_params_data4 = params.data) === null || _params_data4 === void 0 ? void 0 : (_params_data_roleIds = _params_data4.roleIds) === null || _params_data_roleIds === void 0 ? void 0 : _params_data_roleIds.includes(1)) || ((_params_data5 = params.data) === null || _params_data5 === void 0 ? void 0 : (_params_data_roleIds1 = _params_data5.roleIds) === null || _params_data_roleIds1 === void 0 ? void 0 : _params_data_roleIds1.includes(6));\n            let currency = ((_params_data6 = params.data) === null || _params_data6 === void 0 ? void 0 : _params_data6.currency) == \"$\" ? \"\\\\\".concat((_params_data7 = params.data) === null || _params_data7 === void 0 ? void 0 : _params_data7.currency) : (_params_data8 = params.data) === null || _params_data8 === void 0 ? void 0 : _params_data8.currency;\n            let actualCurr;\n            if (currency && currency == \"Not Entered\") {\n                actualCurr = \"\";\n            } else {\n                actualCurr = currency;\n            }\n            let isValid = true;\n            if (isSupplierAccount) {\n                if (prophet_id == 1) {\n                    let regexPattern;\n                    regexPattern = new RegExp(\"^[A-Z0-9]{4}[A-Z0145678]\".concat(actualCurr, \"$\"));\n                    // let regexPattern = new RegExp(`^([A-Z0]{5})${actualCurr}$`);\n                    isValid = regexPattern.test(prophetCode);\n                } else if (prophet_id == 2) {\n                    let regexPattern;\n                    regexPattern = new RegExp(\"^([A-Z0]{4})9\".concat(actualCurr, \"$\"));\n                    // let regexPattern = new RegExp(`^([A-Z0]{4})9${actualCurr}$`);\n                    isValid = regexPattern.test(prophetCode);\n                } else if (prophet_id == 3) {\n                    let regexPattern = new RegExp(\"^[A-Z0-9]{4}[A-Z01345678][A-Z0-9]*$\");\n                    isValid = regexPattern.test(prophetCode) && prophetCode.length == 6;\n                } else if (prophet_id == 4) {\n                    let regexPattern;\n                    regexPattern = new RegExp(\"^([A-Z0]{4})2\".concat(actualCurr, \"$\"));\n                    isValid = regexPattern.test(prophetCode);\n                }\n            }\n            if (codeCount && codeCount > 1 && prophetCode && prophetCode !== \"\") {\n                isExportableBasedOnCodeUnique = false;\n            } else if (codeCount && codeCount == 1 && prophetCode && prophetCode !== \"\") {\n                isExportableBasedOnCodeUnique = true;\n            }\n            if (!isExportableBasedOnCodeUnique && !isValid) {\n                react_toastify__WEBPACK_IMPORTED_MODULE_8__.toast.error(\"Supplier code is not unique and valid, kindly make sure the supplier code is unique and is valid.\");\n                return;\n            } else if (!isExportableBasedOnCodeUnique) {\n                react_toastify__WEBPACK_IMPORTED_MODULE_8__.toast.error(\"Supplier code is not unique, kindly make sure the supplier code is unique.\");\n                return;\n            } else if (!isValid) {\n                react_toastify__WEBPACK_IMPORTED_MODULE_8__.toast.error(\"Supplier code is not vaild, kindly make sure the supplier code is valid.\");\n                return;\n            }\n            handleSingleExportSupplier(supplier_id);\n        } else {\n            handleSingleExportSupplier(supplier_id);\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_5__.useEffect)(()=>{\n        if (supplier_id) {\n            setData(supplierData);\n            setStatus(supplier_status);\n        }\n    }, [\n        supplier_id\n    ]);\n    const editSupplier = ()=>{\n        if (true) {\n            router.push({\n                pathname: \"/supplier/\".concat(supplier_id, \"/edit\")\n            });\n        }\n    };\n    const confirmPage = ()=>{\n        const mappedPermissions = role_ids.map((roleId)=>({\n                roleId: roleId,\n                permissions: permissions[roleId]\n            }));\n        const uniqueSections = [\n            ...new Set(mappedPermissions.flatMap((item)=>item.permissions))\n        ];\n        localStorage.setItem(\"allowedSections\", uniqueSections);\n        if (true) {\n            router.push({\n                pathname: \"/supplier/\".concat(supplier_id, \"/confirm\")\n            });\n        }\n    };\n    function getGLCode(internal_ledger_code, department, currency, roleIds) {\n        if (internal_ledger_code) {\n            return internal_ledger_code;\n        } else {\n            return \"\";\n        }\n    }\n    const extractContacts = (contactsJsonStr)=>{\n        try {\n            const contacts = contactsJsonStr ? JSON.parse(contactsJsonStr) : [];\n            if (Array.isArray(contacts)) {\n                return contacts.map((contact)=>{\n                    var _data_prophets_, _data_prophets__prophet_code, _data_prophets_1;\n                    return {\n                        \"Supplier code\": (data === null || data === void 0 ? void 0 : (_data_prophets_ = data.prophets[0]) === null || _data_prophets_ === void 0 ? void 0 : _data_prophets_.prophet_code) ? data === null || data === void 0 ? void 0 : (_data_prophets_1 = data.prophets[0]) === null || _data_prophets_1 === void 0 ? void 0 : (_data_prophets__prophet_code = _data_prophets_1.prophet_code) === null || _data_prophets__prophet_code === void 0 ? void 0 : _data_prophets__prophet_code.trim() : \"\",\n                        \"Contact ID\": \"\",\n                        Name: data.company_name || \"\",\n                        \"Email Address\": contact.email_id || \"\",\n                        \"Telephone number\": contact.telephone || \"\",\n                        \"Cell phone number\": \"\",\n                        \"Fax number\": \"\",\n                        \"Instant Message\": \"\",\n                        \"Physical Address\": \"\",\n                        \"Postal Address\": \"\",\n                        \"Row verision\": \"\",\n                        \"Created timestamp\": \"\"\n                    };\n                });\n            }\n        } catch (error) {\n            console.error(\"Error parsing contacts_json:\", error);\n        }\n        return [\n            {\n                \"Contact ID\": \"\",\n                Name: data.company_name || \"\",\n                \"Email Address\": \"\",\n                \"Telephone number\": \"\",\n                \"Cell phone number\": \"\",\n                \"Fax number\": \"\",\n                \"Instant Message\": \"\",\n                \"Physical Address\": \"\",\n                \"Postal Address\": \"\",\n                \"Row verision\": \"\",\n                \"Created timestamp\": \"\"\n            }\n        ];\n    };\n    const extractSendacGroup = (sendacGroupJson, id)=>{\n        try {\n            const sendacGroups = sendacGroupJson ? JSON.parse(sendacGroupJson) : [];\n            if (Array.isArray(sendacGroups)) {\n                const filteredGroups = sendacGroups.filter((group)=>(group === null || group === void 0 ? void 0 : group.created_by) === id);\n                if (filteredGroups.length > 0) {\n                    return filteredGroups.map((group)=>({\n                            \"Supplier group\": \"\",\n                            Description: (group === null || group === void 0 ? void 0 : group.created_by) ? group === null || group === void 0 ? void 0 : group.label : \"\"\n                        }));\n                } else {\n                    // Handle the case when no matching group is found\n                    return []; // or any other default value or action\n                }\n            }\n        } catch (error) {\n            console.error(\"Error parsing contacts_json:\", error);\n        }\n    };\n    const multipleSendRoleOnRoleNums = ()=>{\n        var _data_role_num;\n        const roleNums = data === null || data === void 0 ? void 0 : (_data_role_num = data.role_num) === null || _data_role_num === void 0 ? void 0 : _data_role_num.split(\",\").map((num)=>num.trim());\n        return roleNums.map((num)=>{\n            var _data_prophets_, _data_prophets_1;\n            return {\n                Sendacroleid: \"\",\n                \"Supplier code\": (data === null || data === void 0 ? void 0 : (_data_prophets_ = data.prophets[0]) === null || _data_prophets_ === void 0 ? void 0 : _data_prophets_.prophet_code) ? data.prophets[0].prophet_code.trim() : \"\",\n                Description: (data === null || data === void 0 ? void 0 : (_data_prophets_1 = data.prophets[0]) === null || _data_prophets_1 === void 0 ? void 0 : _data_prophets_1.prophet_code) ? data.prophets[0].prophet_code.trim() : \"\",\n                \"Supplier Code Supplier Name\": data.company_name,\n                Type: num,\n                \"Type Description\": data === null || data === void 0 ? void 0 : data[\"role names\"],\n                \"Supplier code Global gap number\": data === null || data === void 0 ? void 0 : data.global_gap_number,\n                \"Created timestamp\": \"\",\n                Active: 1\n            };\n        });\n    };\n    const handleSingleExportSupplier = async (id)=>{\n        var _data_distribution_points_json, _data_roleIds;\n        const company = js_cookie__WEBPACK_IMPORTED_MODULE_6__[\"default\"].get(\"company\");\n        // setIsOpenOption(false);\n        let rolesArray = params.data.roleId.map((ele)=>{\n            return ele.role_id;\n        });\n        const formattedDistributionData = data === null || data === void 0 ? void 0 : (_data_distribution_points_json = data.distribution_points_json) === null || _data_distribution_points_json === void 0 ? void 0 : _data_distribution_points_json.map((row)=>({\n                distributionPoint: row === null || row === void 0 ? void 0 : row.name,\n                directDPvalue: row.direct_dp ? \"True\" : \"False\",\n                directDP: row.direct_dp,\n                from_dp: row.from_dp\n            }));\n        let filteredInternalExportData = [];\n        let filteredISSExportData = [];\n        // const isInternal = selectedExportType === \"internalExport\";\n        if (supplier_status === \"Completed\" || supplier_status === \"Exported\" || params.data.isEmergencyRequest && params.data.status != \"Cancelled\" && params.data.General === \"Complete\" && ((data === null || data === void 0 ? void 0 : data.roleIds.includes(1)) || (data === null || data === void 0 ? void 0 : (_data_roleIds = data.roleIds) === null || _data_roleIds === void 0 ? void 0 : _data_roleIds.includes(6))) && params.data.currency_id) {\n            var _params_data;\n            if (true) {\n                var _data_roleIds1, _data_roleIds2, _data_roleIds3, _data_prophets__prophet_code, _data_prophets_, _formattedDistributionData_, _data_prophets__prophet_code1, _data_prophets_1, _data_prophets_2, _data_prophets_3, _data_prophets_4, _data_prophets__prophet_code2, _data_prophets_5, _data_prophets_6, _data_prophets__prophet_code3, _data_prophets_7, _data_prophets_8, _data_prophets__prophet_code4, _data_prophets_9, _data_prophets_10, _data_prophets__prophet_code5, _data_prophets_11, _data_prophets_12, _data_prophets__prophet_code6, _data_prophets_13;\n                let sort_code = \"\";\n                let account_number = \"\";\n                let swiftBicCode = \"\";\n                let iban = \"\";\n                const swiftBicRegex = /^([A-Z0-9]{4}[A-Z0-9]{2}[A-Z0-9]{2}|[A-Z0-9]{4}[A-Z0-9]{2}[A-Z0-9]{2}[A-Z0-9]{3})$/;\n                if (swiftBicRegex.test(data === null || data === void 0 ? void 0 : data.decryptedSort_Bic) && (data === null || data === void 0 ? void 0 : data.has_iban)) {\n                    var _data_decryptedAccountNumber;\n                    sort_code = \"000000\";\n                    account_number = data === null || data === void 0 ? void 0 : (_data_decryptedAccountNumber = data.decryptedAccountNumber) === null || _data_decryptedAccountNumber === void 0 ? void 0 : _data_decryptedAccountNumber.slice(-8);\n                    swiftBicCode = data === null || data === void 0 ? void 0 : data.decryptedSort_Bic;\n                    iban = data === null || data === void 0 ? void 0 : data.decryptedAccountNumber;\n                } else if (!(data === null || data === void 0 ? void 0 : data.has_iban) && swiftBicRegex.test(data === null || data === void 0 ? void 0 : data.decryptedSort_Bic)) {\n                    sort_code = \"000000\";\n                    account_number = data === null || data === void 0 ? void 0 : data.decryptedAccountNumber;\n                    swiftBicCode = data === null || data === void 0 ? void 0 : data.decryptedSort_Bic;\n                } else {\n                    sort_code = data === null || data === void 0 ? void 0 : data.decryptedSort_Bic;\n                    account_number = data === null || data === void 0 ? void 0 : data.decryptedAccountNumber;\n                }\n                let regional_cert = \"\";\n                if ((data === null || data === void 0 ? void 0 : (_data_roleIds1 = data.roleIds) === null || _data_roleIds1 === void 0 ? void 0 : _data_roleIds1.includes(2)) || (data === null || data === void 0 ? void 0 : (_data_roleIds2 = data.roleIds) === null || _data_roleIds2 === void 0 ? void 0 : _data_roleIds2.includes(3))) {\n                    if ((data === null || data === void 0 ? void 0 : data.country_code) == \"UK\") {\n                        regional_cert = data === null || data === void 0 ? void 0 : data.red_tractor;\n                    } else if ((data === null || data === void 0 ? void 0 : data.country_code) == \"ZA\") {\n                        regional_cert = data === null || data === void 0 ? void 0 : data.puc_code;\n                    } else if ((data === null || data === void 0 ? void 0 : data.country_code) == \"CL\") {\n                        regional_cert = data === null || data === void 0 ? void 0 : data.chile_certificate_number;\n                    }\n                }\n                let currencyId = \"\";\n                let currencyName = \"\";\n                if ((data === null || data === void 0 ? void 0 : data.roleIds.includes(1)) || (data === null || data === void 0 ? void 0 : data.roleIds.includes(5)) || (data === null || data === void 0 ? void 0 : (_data_roleIds3 = data.roleIds) === null || _data_roleIds3 === void 0 ? void 0 : _data_roleIds3.includes(6))) {\n                    currencyId = (data === null || data === void 0 ? void 0 : data.currency_id) || 1;\n                    currencyName = (data === null || data === void 0 ? void 0 : data.currency_name) || \"Sterling\";\n                } else {\n                    currencyId = 1;\n                    currencyName = \"Sterling\";\n                }\n                function getCorrespondingUserLookup(curr) {\n                    if (curr == \"GBP\") {\n                        return \"GBPBACS\";\n                    } else if (curr == \"EUR\") {\n                        return \"EUROSEPA\";\n                    } else if (curr == \"USD\") {\n                        return \"USDPRIORITY\";\n                    } else {\n                        return \"\";\n                    }\n                }\n                console.log(\"supplier type\", data === null || data === void 0 ? void 0 : data.supplier_type);\n                var _data_edi;\n                filteredInternalExportData = [\n                    [\n                        \"UlpFil\",\n                        {\n                            \"Distribution point\": \"\",\n                            Description: (formattedDistributionData === null || formattedDistributionData === void 0 ? void 0 : formattedDistributionData.length) > 0 && (formattedDistributionData[0].from_dp === null || formattedDistributionData[0].from_dp === undefined) ? formattedDistributionData[0].distributionPoint : \"\",\n                            \"Service Supplier Code\": (formattedDistributionData === null || formattedDistributionData === void 0 ? void 0 : formattedDistributionData.length) > 0 && (formattedDistributionData[0].from_dp === null || formattedDistributionData[0].from_dp === undefined) && (data === null || data === void 0 ? void 0 : (_data_prophets_ = data.prophets[0]) === null || _data_prophets_ === void 0 ? void 0 : (_data_prophets__prophet_code = _data_prophets_.prophet_code) === null || _data_prophets__prophet_code === void 0 ? void 0 : _data_prophets__prophet_code.trim()),\n                            \"Default expected stock status\": (formattedDistributionData === null || formattedDistributionData === void 0 ? void 0 : formattedDistributionData.length) > 0 && (formattedDistributionData[0].from_dp === null || formattedDistributionData[0].from_dp === undefined) && \"0\",\n                            \"Default received stock status\": (formattedDistributionData === null || formattedDistributionData === void 0 ? void 0 : formattedDistributionData.length) > 0 && (formattedDistributionData[0].from_dp === null || formattedDistributionData[0].from_dp === undefined) && \"0\",\n                            \"Pallets in packhouse\": (formattedDistributionData === null || formattedDistributionData === void 0 ? void 0 : formattedDistributionData.length) > 0 && (formattedDistributionData[0].from_dp === null || formattedDistributionData[0].from_dp === undefined) && \"0\",\n                            \"Default haulier\": (formattedDistributionData === null || formattedDistributionData === void 0 ? void 0 : formattedDistributionData.length) > 0 && (formattedDistributionData[0].from_dp === null || formattedDistributionData[0].from_dp === undefined) && \"ZZZZZ\",\n                            \"Default expected location id\": (formattedDistributionData === null || formattedDistributionData === void 0 ? void 0 : formattedDistributionData.length) > 0 && (formattedDistributionData[0].from_dp === null || formattedDistributionData[0].from_dp === undefined) && 21,\n                            \"Default receiving location id\": (formattedDistributionData === null || formattedDistributionData === void 0 ? void 0 : formattedDistributionData.length) > 0 && (formattedDistributionData[0].from_dp === null || formattedDistributionData[0].from_dp === undefined) && 21,\n                            \"Packhouse location id\": (formattedDistributionData === null || formattedDistributionData === void 0 ? void 0 : formattedDistributionData.length) > 0 && (formattedDistributionData[0].from_dp === null || formattedDistributionData[0].from_dp === undefined) && 21,\n                            \"Default pick location id\": (formattedDistributionData === null || formattedDistributionData === void 0 ? void 0 : formattedDistributionData.length) > 0 && (formattedDistributionData[0].from_dp === null || formattedDistributionData[0].from_dp === undefined) && 21,\n                            \"Despatch location id\": (formattedDistributionData === null || formattedDistributionData === void 0 ? void 0 : formattedDistributionData.length) > 0 && (formattedDistributionData[0].from_dp === null || formattedDistributionData[0].from_dp === undefined) && 21,\n                            \"Default waste location id\": (formattedDistributionData === null || formattedDistributionData === void 0 ? void 0 : formattedDistributionData.length) > 0 && (formattedDistributionData[0].from_dp === null || formattedDistributionData[0].from_dp === undefined) && 21,\n                            \"Default pre-pick location id\": (formattedDistributionData === null || formattedDistributionData === void 0 ? void 0 : formattedDistributionData.length) > 0 && (formattedDistributionData[0].from_dp === null || formattedDistributionData[0].from_dp === undefined) && 21,\n                            \"Default returns location id\": (formattedDistributionData === null || formattedDistributionData === void 0 ? void 0 : formattedDistributionData.length) > 0 && (formattedDistributionData[0].from_dp === null || formattedDistributionData[0].from_dp === undefined) && 21,\n                            Address: \"\",\n                            \"Service supplier code\": \"\",\n                            \"EDI Reference Code\": \"\",\n                            \"EDI ANA Code\": \"\",\n                            \"User Integer 1\": (formattedDistributionData === null || formattedDistributionData === void 0 ? void 0 : formattedDistributionData.length) > 0 && (formattedDistributionData[0].from_dp === null || formattedDistributionData[0].from_dp === undefined) && 1,\n                            \"Movement resource group\": \"\",\n                            \"Handheld application used\": (formattedDistributionData === null || formattedDistributionData === void 0 ? void 0 : formattedDistributionData.length) > 0 && (formattedDistributionData[0].from_dp === null || formattedDistributionData[0].from_dp === undefined) && \"0\",\n                            \"Pallets in procure/receiving\": (formattedDistributionData === null || formattedDistributionData === void 0 ? void 0 : formattedDistributionData.length) > 0 && (formattedDistributionData[0].from_dp === null || formattedDistributionData[0].from_dp === undefined) && \"0\",\n                            \"Operational depo\": \"\",\n                            \"Enabled for masterfile sending\": \"\",\n                            \"Connected registed depot\": \"\",\n                            \"EDI Transmission type of depo\": \"\",\n                            \"Container loading depo\": (formattedDistributionData === null || formattedDistributionData === void 0 ? void 0 : formattedDistributionData.length) > 0 && (formattedDistributionData[0].from_dp === null || formattedDistributionData[0].from_dp === undefined) && \"0\",\n                            \"Airport depot\": \"\",\n                            \"Sms notification\": \"\",\n                            Port: \"\",\n                            Dormant: \"\",\n                            Active: (formattedDistributionData === null || formattedDistributionData === void 0 ? void 0 : formattedDistributionData.length) > 0 && (formattedDistributionData[0].from_dp === null || formattedDistributionData[0].from_dp === undefined) && 1,\n                            \"Ingredient distribution point\": \"\",\n                            \"Show in CE\": (formattedDistributionData === null || formattedDistributionData === void 0 ? void 0 : formattedDistributionData.length) > 0 && (formattedDistributionData[0].from_dp === null || formattedDistributionData[0].from_dp === undefined) && 1,\n                            \"Charge direction\": \"\",\n                            \"Pallet receive time\": \"\",\n                            \"User string 3\": \"\",\n                            \"Direct DP\": (formattedDistributionData === null || formattedDistributionData === void 0 ? void 0 : formattedDistributionData.length) > 0 && (formattedDistributionData[0].from_dp === null || formattedDistributionData[0].from_dp === undefined) ? ((_formattedDistributionData_ = formattedDistributionData[0]) === null || _formattedDistributionData_ === void 0 ? void 0 : _formattedDistributionData_.directDP) ? 1 : \"0\" : \"\",\n                            \"Include on XML\": (formattedDistributionData === null || formattedDistributionData === void 0 ? void 0 : formattedDistributionData.length) > 0 && (formattedDistributionData[0].from_dp === null || formattedDistributionData[0].from_dp === undefined) && \"0\"\n                        }\n                    ],\n                    [\n                        \"sendac (Supplier file)\",\n                        {\n                            \"Supplier Active\": (data === null || data === void 0 ? void 0 : data.isActive) ? 1 : 0,\n                            \"Haulage cube local\": \"\",\n                            \"Haulage cube name\": \"\",\n                            \"update guesstimates type\": 1,\n                            \"Organization ID\": \"\",\n                            \"Vat number 1\": data === null || data === void 0 ? void 0 : data.vat_number,\n                            \"Organic cert\": data === null || data === void 0 ? void 0 : data.organic_certificate_number,\n                            \"Regional cert\": regional_cert,\n                            \"Global gap number\": data === null || data === void 0 ? void 0 : data.global_gap_number,\n                            \"Enforce department\": \"\",\n                            \"Sendac Group\": (data === null || data === void 0 ? void 0 : data.supplier_group) ? JSON.parse(data.supplier_group)[0].value : \"\",\n                            \"Supplier code\": data === null || data === void 0 ? void 0 : (_data_prophets_1 = data.prophets[0]) === null || _data_prophets_1 === void 0 ? void 0 : (_data_prophets__prophet_code1 = _data_prophets_1.prophet_code) === null || _data_prophets__prophet_code1 === void 0 ? void 0 : _data_prophets__prophet_code1.trim(),\n                            \"Supplier name\": data.company_name,\n                            \"Supplier type\": data === null || data === void 0 ? void 0 : data.supplier_type,\n                            \"User Lookup 2\": \"\",\n                            \"Address Line 1\": data === null || data === void 0 ? void 0 : data.address_line_1,\n                            \"Address Line 2\": data === null || data === void 0 ? void 0 : data.address_line_2,\n                            \"Address Line 3\": data === null || data === void 0 ? void 0 : data.address_line_3,\n                            \"Address Line 4\": data === null || data === void 0 ? void 0 : data.address_line_4,\n                            \"Post code\": data === null || data === void 0 ? void 0 : data.postal_code,\n                            \"Country Code\": data === null || data === void 0 ? void 0 : data.country_code,\n                            \"Payee supplier code\": \"\",\n                            \"Invoice supplier\": \"\",\n                            \"Head office\": \"\",\n                            \"Settlement days\": data === null || data === void 0 ? void 0 : data.payment_terms,\n                            \"Bank general ledger code Currency number if bank\": currencyId,\n                            \"Currency number\": currencyId,\n                            \"Currency number Currency name\": currencyName,\n                            \"Bank general ledger code\": getGLCode(data === null || data === void 0 ? void 0 : data.internal_ledger_code, data === null || data === void 0 ? void 0 : (_data_prophets_2 = data.prophets[0]) === null || _data_prophets_2 === void 0 ? void 0 : _data_prophets_2.prophet_id, data === null || data === void 0 ? void 0 : data.currency_code, data === null || data === void 0 ? void 0 : data.roleIds),\n                            \"payment Type\": data === null || data === void 0 ? void 0 : data.payment_type,\n                            \"payment type name\": data === null || data === void 0 ? void 0 : data.payment_type_name,\n                            \"Country code\": data === null || data === void 0 ? void 0 : data.country_code,\n                            Vatable: (data === null || data === void 0 ? void 0 : data.vatable) != null ? (data === null || data === void 0 ? void 0 : data.vatable) ? \"1\" : \"0\" : \"0\",\n                            \"vatable desc\": (data === null || data === void 0 ? void 0 : data.vatable) != null ? (data === null || data === void 0 ? void 0 : data.vatable) ? \"Vatable\" : \"None vatable\" : \"None vatable\",\n                            \"Area Number\": (data === null || data === void 0 ? void 0 : data.prophets[0].prophet_id) == 3 || (data === null || data === void 0 ? void 0 : data.prophets[0].prophet_id) == 4 ? 1 : 7,\n                            Buyer: 1,\n                            \"Multiple Lot Indicator\": \"0\",\n                            \"multiple lot indicator desc\": \"By Lot\",\n                            \"Generate Pallet Loading Plan\": \"\",\n                            \"Distribution point for supplier\": 6,\n                            \"Payment terms\": \"\",\n                            \"Department Number\": (data === null || data === void 0 ? void 0 : (_data_prophets_3 = data.prophets[0]) === null || _data_prophets_3 === void 0 ? void 0 : _data_prophets_3.prophet_id) == 1 ? 1 : (data === null || data === void 0 ? void 0 : (_data_prophets_4 = data.prophets[0]) === null || _data_prophets_4 === void 0 ? void 0 : _data_prophets_4.prophet_id) == 2 ? 9 : (data === null || data === void 0 ? void 0 : data.prophets[0].prophet_id) == 3 ? 3 : (data === null || data === void 0 ? void 0 : data.prophets[0].prophet_id) == 4 ? 2 : \"\",\n                            \"Allow credit rebates\": \"\",\n                            \"Alternative DP for supplier\": 1,\n                            \"Actual posting stops purchase charges\": \"\",\n                            \"Authorise on register\": \"\",\n                            \"User text 1\": \"\",\n                            \"User lookup 1\": (data === null || data === void 0 ? void 0 : data.prophets[0].prophet_id) == 3 || (data === null || data === void 0 ? void 0 : data.prophets[0].prophet_id) == 4 ? getCorrespondingUserLookup(data === null || data === void 0 ? void 0 : data.currency_code) : \"\",\n                            \"Receive orders from edi\": \"\",\n                            \"Send invoices from edi\": \"\",\n                            \"send orders from edi\": \"\",\n                            \"EDI partner\": (data === null || data === void 0 ? void 0 : data.prophets[0].prophet_id) == 3 || (data === null || data === void 0 ? void 0 : data.prophets[0].prophet_id) == 4 ? 2000 : \"\",\n                            \"Generic code\": (data === null || data === void 0 ? void 0 : data.prophets[0].prophet_id) == 3 || (data === null || data === void 0 ? void 0 : data.prophets[0].prophet_id) == 4 ? \"STOCK\" : \"\",\n                            \"EDI ANA number\": (_data_edi = data === null || data === void 0 ? void 0 : data.edi) !== null && _data_edi !== void 0 ? _data_edi : \"\",\n                            \"User % authorize rule\": 5,\n                            FromDP: (formattedDistributionData === null || formattedDistributionData === void 0 ? void 0 : formattedDistributionData.length) > 0 ? formattedDistributionData[0].from_dp || \"\" : \"\"\n                        }\n                    ],\n                    [\n                        \"sendacrole (Supplier role file)\"\n                    ],\n                    [\n                        \"sendacgroup (Sendac group file)\"\n                    ],\n                    [\n                        \"bankac (Bank account details table)\",\n                        {\n                            \"Supplier code\": data === null || data === void 0 ? void 0 : (_data_prophets_5 = data.prophets[0]) === null || _data_prophets_5 === void 0 ? void 0 : (_data_prophets__prophet_code2 = _data_prophets_5.prophet_code) === null || _data_prophets__prophet_code2 === void 0 ? void 0 : _data_prophets__prophet_code2.trim(),\n                            \"Record id\": \"\",\n                            \"Bank sort code\": sort_code,\n                            \"Account number\": account_number,\n                            \"Country code\": (data === null || data === void 0 ? void 0 : data.country_code) == \"UK\" ? \"GB\" : data === null || data === void 0 ? void 0 : data.country_code,\n                            \"Account holder\": data.company_name,\n                            \"Currency number\": currencyId,\n                            \"BACS currency\": data === null || data === void 0 ? void 0 : data.bacs_currency_code,\n                            \"Address Line 1\": \"\",\n                            \"Address Line 2\": \"\",\n                            \"BIC/Swift address\": swiftBicCode,\n                            \"Internation bank reference code\": iban,\n                            \"Account user id\": \"\",\n                            \"Post code\": \"\"\n                        }\n                    ],\n                    [\n                        \"senbnk (Supplier bank link table)\",\n                        {\n                            \"Supplier code\": (data === null || data === void 0 ? void 0 : (_data_prophets_6 = data.prophets[0]) === null || _data_prophets_6 === void 0 ? void 0 : _data_prophets_6.prophet_code) ? data === null || data === void 0 ? void 0 : (_data_prophets_7 = data.prophets[0]) === null || _data_prophets_7 === void 0 ? void 0 : (_data_prophets__prophet_code3 = _data_prophets_7.prophet_code) === null || _data_prophets__prophet_code3 === void 0 ? void 0 : _data_prophets__prophet_code3.trim() : \"\",\n                            Bankacid: \"\",\n                            \"Header bank record id\": \"\",\n                            \"Intermediary bank account id\": \"\",\n                            \"Intermediary bank account id Internation bank reference code\": \"\"\n                        }\n                    ],\n                    [\n                        \"contactdet (Supplier personnel contact details)\"\n                    ],\n                    [\n                        \"organization (Organization)\",\n                        {\n                            \"Organization ID\": \"\",\n                            \"Organization Name\": (data === null || data === void 0 ? void 0 : (_data_prophets_8 = data.prophets[0]) === null || _data_prophets_8 === void 0 ? void 0 : _data_prophets_8.prophet_code) ? data === null || data === void 0 ? void 0 : (_data_prophets_9 = data.prophets[0]) === null || _data_prophets_9 === void 0 ? void 0 : (_data_prophets__prophet_code4 = _data_prophets_9.prophet_code) === null || _data_prophets__prophet_code4 === void 0 ? void 0 : _data_prophets__prophet_code4.trim() : \"\"\n                        }\n                    ],\n                    [\n                        \"orgroles (Organization Roles)\",\n                        {\n                            \"Organization ID\": \"\",\n                            \"Organization Code\": (data === null || data === void 0 ? void 0 : (_data_prophets_10 = data.prophets[0]) === null || _data_prophets_10 === void 0 ? void 0 : _data_prophets_10.prophet_code) ? data === null || data === void 0 ? void 0 : (_data_prophets_11 = data.prophets[0]) === null || _data_prophets_11 === void 0 ? void 0 : (_data_prophets__prophet_code5 = _data_prophets_11.prophet_code) === null || _data_prophets__prophet_code5 === void 0 ? void 0 : _data_prophets__prophet_code5.trim() : \"\",\n                            \"Role Type ID\": \"\",\n                            Selected: \"\",\n                            \"Organisation ID\": \"\",\n                            \"role Type ID\": \"\",\n                            \"Contact ID\": \"\",\n                            \"Contact ID Email Address\": \"\",\n                            \"Contact ID Telephone\": \"\",\n                            \"Contact ID Fax\": \"\"\n                        }\n                    ],\n                    [\n                        \"sheetSuppliersId\",\n                        {\n                            id: data === null || data === void 0 ? void 0 : data.id,\n                            supplierName: formattedDistributionData === null || formattedDistributionData === void 0 ? void 0 : formattedDistributionData.company_name,\n                            isEmergencyAndFinanceNotComplete: data === null || data === void 0 ? void 0 : data.isEmergencyAndFinanceNotComplete,\n                            supplierCode: (data === null || data === void 0 ? void 0 : (_data_prophets_12 = data.prophets[0]) === null || _data_prophets_12 === void 0 ? void 0 : _data_prophets_12.prophet_code) ? data === null || data === void 0 ? void 0 : (_data_prophets_13 = data.prophets[0]) === null || _data_prophets_13 === void 0 ? void 0 : (_data_prophets__prophet_code6 = _data_prophets_13.prophet_code) === null || _data_prophets__prophet_code6 === void 0 ? void 0 : _data_prophets__prophet_code6.trim() : \"\"\n                        }\n                    ]\n                ];\n                const addDataToSheet = (sheetIndex, dataToAdd)=>{\n                    if (filteredInternalExportData[sheetIndex].length === 2) {\n                        filteredInternalExportData[sheetIndex].push(...dataToAdd);\n                    } else {\n                        filteredInternalExportData[sheetIndex] = [\n                            filteredInternalExportData[sheetIndex][0],\n                            ...dataToAdd\n                        ];\n                    }\n                };\n                // Extract contacts and add to the contacts sheet\n                const contacts = extractContacts(data === null || data === void 0 ? void 0 : data.contacts_json);\n                const extractedSendacGroup = extractSendacGroup(data.supplier_group, data === null || data === void 0 ? void 0 : data.id);\n                let sendacRoleOnRoleNums = multipleSendRoleOnRoleNums(data);\n                addDataToSheet(6, contacts);\n                addDataToSheet(2, sendacRoleOnRoleNums);\n                addDataToSheet(3, extractedSendacGroup);\n            }\n            let export_ISS_response;\n            if (rolesArray.includes(1) || rolesArray.includes(2) || rolesArray.includes(3) || rolesArray.includes(4)) {\n                var _data_prophets__prophet_code7, _data_prophets_14, _formattedDistributionData_1, _data_prophets__prophet_code8, _data_prophets_15, _data_prophets__prophet_code9, _data_prophets_16, _data_distribution_points_json1, _data_prophets_17, _data_prophets_18, _params_data1;\n                let sendacRoleOnRoleNums = multipleSendRoleOnRoleNums(data === null || data === void 0 ? void 0 : data.role_num);\n                var _data_edi1;\n                filteredISSExportData = [\n                    [\n                        \"UlpFil\",\n                        {\n                            \"Distribution point\": \"\",\n                            Description: (formattedDistributionData === null || formattedDistributionData === void 0 ? void 0 : formattedDistributionData.length) > 0 && (formattedDistributionData[0].from_dp === null || formattedDistributionData[0].from_dp === undefined) ? formattedDistributionData[0].distributionPoint : \"\",\n                            \"Service Supplier Code\": (formattedDistributionData === null || formattedDistributionData === void 0 ? void 0 : formattedDistributionData.length) > 0 && (formattedDistributionData[0].from_dp === null || formattedDistributionData[0].from_dp === undefined) && (data === null || data === void 0 ? void 0 : (_data_prophets_14 = data.prophets[0]) === null || _data_prophets_14 === void 0 ? void 0 : (_data_prophets__prophet_code7 = _data_prophets_14.prophet_code) === null || _data_prophets__prophet_code7 === void 0 ? void 0 : _data_prophets__prophet_code7.trim()),\n                            \"Default expected stock status\": (formattedDistributionData === null || formattedDistributionData === void 0 ? void 0 : formattedDistributionData.length) > 0 && (formattedDistributionData[0].from_dp === null || formattedDistributionData[0].from_dp === undefined) && \"0\",\n                            \"Default received stock status\": (formattedDistributionData === null || formattedDistributionData === void 0 ? void 0 : formattedDistributionData.length) > 0 && (formattedDistributionData[0].from_dp === null || formattedDistributionData[0].from_dp === undefined) && \"0\",\n                            \"Pallets in packhouse\": (formattedDistributionData === null || formattedDistributionData === void 0 ? void 0 : formattedDistributionData.length) > 0 && (formattedDistributionData[0].from_dp === null || formattedDistributionData[0].from_dp === undefined) && \"0\",\n                            \"Default haulier\": (formattedDistributionData === null || formattedDistributionData === void 0 ? void 0 : formattedDistributionData.length) > 0 && (formattedDistributionData[0].from_dp === null || formattedDistributionData[0].from_dp === undefined) && \"ZZZZZ\",\n                            \"Default expected location id\": (formattedDistributionData === null || formattedDistributionData === void 0 ? void 0 : formattedDistributionData.length) > 0 && (formattedDistributionData[0].from_dp === null || formattedDistributionData[0].from_dp === undefined) && 21,\n                            \"Default receiving location id\": (formattedDistributionData === null || formattedDistributionData === void 0 ? void 0 : formattedDistributionData.length) > 0 && (formattedDistributionData[0].from_dp === null || formattedDistributionData[0].from_dp === undefined) && 21,\n                            \"Packhouse location id\": (formattedDistributionData === null || formattedDistributionData === void 0 ? void 0 : formattedDistributionData.length) > 0 && (formattedDistributionData[0].from_dp === null || formattedDistributionData[0].from_dp === undefined) && 21,\n                            \"Default pick location id\": (formattedDistributionData === null || formattedDistributionData === void 0 ? void 0 : formattedDistributionData.length) > 0 && (formattedDistributionData[0].from_dp === null || formattedDistributionData[0].from_dp === undefined) && 21,\n                            \"Despatch location id\": (formattedDistributionData === null || formattedDistributionData === void 0 ? void 0 : formattedDistributionData.length) > 0 && (formattedDistributionData[0].from_dp === null || formattedDistributionData[0].from_dp === undefined) && 21,\n                            \"Default waste location id\": (formattedDistributionData === null || formattedDistributionData === void 0 ? void 0 : formattedDistributionData.length) > 0 && (formattedDistributionData[0].from_dp === null || formattedDistributionData[0].from_dp === undefined) && 21,\n                            \"Default pre-pick location id\": (formattedDistributionData === null || formattedDistributionData === void 0 ? void 0 : formattedDistributionData.length) > 0 && (formattedDistributionData[0].from_dp === null || formattedDistributionData[0].from_dp === undefined) && 21,\n                            \"Default returns location id\": (formattedDistributionData === null || formattedDistributionData === void 0 ? void 0 : formattedDistributionData.length) > 0 && (formattedDistributionData[0].from_dp === null || formattedDistributionData[0].from_dp === undefined) && 21,\n                            Address: \"\",\n                            \"Service supplier code\": \"\",\n                            \"EDI Reference Code\": \"\",\n                            \"EDI ANA Code\": \"\",\n                            \"User Integer 1\": (formattedDistributionData === null || formattedDistributionData === void 0 ? void 0 : formattedDistributionData.length) > 0 && (formattedDistributionData[0].from_dp === null || formattedDistributionData[0].from_dp === undefined) && 1,\n                            \"Movement resource group\": \"\",\n                            \"Handheld application used\": (formattedDistributionData === null || formattedDistributionData === void 0 ? void 0 : formattedDistributionData.length) > 0 && (formattedDistributionData[0].from_dp === null || formattedDistributionData[0].from_dp === undefined) && \"0\",\n                            \"Pallets in procure/receiving\": (formattedDistributionData === null || formattedDistributionData === void 0 ? void 0 : formattedDistributionData.length) > 0 && (formattedDistributionData[0].from_dp === null || formattedDistributionData[0].from_dp === undefined) && \"0\",\n                            \"Operational depo\": \"\",\n                            \"Enabled for masterfile sending\": \"\",\n                            \"Connected registed depot\": \"\",\n                            \"EDI Transmission type of depo\": \"\",\n                            \"Container loading depo\": (formattedDistributionData === null || formattedDistributionData === void 0 ? void 0 : formattedDistributionData.length) > 0 && (formattedDistributionData[0].from_dp === null || formattedDistributionData[0].from_dp === undefined) && \"0\",\n                            \"Airport depot\": \"\",\n                            \"Sms notification\": \"\",\n                            Port: \"\",\n                            Dormant: \"\",\n                            Active: (formattedDistributionData === null || formattedDistributionData === void 0 ? void 0 : formattedDistributionData.length) > 0 && (formattedDistributionData[0].from_dp === null || formattedDistributionData[0].from_dp === undefined) && 1,\n                            \"Ingredient distribution point\": \"\",\n                            \"Show in CE\": (formattedDistributionData === null || formattedDistributionData === void 0 ? void 0 : formattedDistributionData.length) > 0 && (formattedDistributionData[0].from_dp === null || formattedDistributionData[0].from_dp === undefined) && 1,\n                            \"Charge direction\": \"\",\n                            \"Pallet receive time\": \"\",\n                            \"User string 3\": \"\",\n                            \"Direct DP\": (formattedDistributionData === null || formattedDistributionData === void 0 ? void 0 : formattedDistributionData.length) > 0 && (formattedDistributionData[0].from_dp === null || formattedDistributionData[0].from_dp === undefined) ? ((_formattedDistributionData_1 = formattedDistributionData[0]) === null || _formattedDistributionData_1 === void 0 ? void 0 : _formattedDistributionData_1.directDP) ? 1 : \"0\" : \"\",\n                            \"Include on XML\": (formattedDistributionData === null || formattedDistributionData === void 0 ? void 0 : formattedDistributionData.length) > 0 && (formattedDistributionData[0].from_dp === null || formattedDistributionData[0].from_dp === undefined) && \"0\"\n                        }\n                    ],\n                    [\n                        \"Supplier data\",\n                        {\n                            \"Supplier Active\": \"N/A\",\n                            \"Supplier code\": data === null || data === void 0 ? void 0 : (_data_prophets_15 = data.prophets[0]) === null || _data_prophets_15 === void 0 ? void 0 : (_data_prophets__prophet_code8 = _data_prophets_15.prophet_code) === null || _data_prophets__prophet_code8 === void 0 ? void 0 : _data_prophets__prophet_code8.trim(),\n                            \"EDI Partner\": \"N/A\",\n                            \"Supplier name\": data.company_name,\n                            \"EDI ANA number\": (_data_edi1 = data === null || data === void 0 ? void 0 : data.edi) !== null && _data_edi1 !== void 0 ? _data_edi1 : \"N/A\",\n                            \"Producer (supplier)\": \"N/A\",\n                            \"Department number\": \"N/A\",\n                            \"Currency number\": (data === null || data === void 0 ? void 0 : data.currency_id) ? data === null || data === void 0 ? void 0 : data.currency_id : 1,\n                            \"Global gap number\": data === null || data === void 0 ? void 0 : data.global_gap_number,\n                            \"Grower group\": \"N/A\",\n                            \"Defra county number\": \"N/A\",\n                            \"Date start\": \"N/A\",\n                            \"Date end\": \"N/A\",\n                            \"Organic cert\": data === null || data === void 0 ? void 0 : data.organic_certificate_number,\n                            \"Regional cert\": data === null || data === void 0 ? void 0 : data.chile_certificate_number,\n                            \"Head office\": data === null || data === void 0 ? void 0 : (_data_prophets_16 = data.prophets[0]) === null || _data_prophets_16 === void 0 ? void 0 : (_data_prophets__prophet_code9 = _data_prophets_16.prophet_code) === null || _data_prophets__prophet_code9 === void 0 ? void 0 : _data_prophets__prophet_code9.trim(),\n                            \"Country Code\": data === null || data === void 0 ? void 0 : data.country_code,\n                            \"Distribution point for supplier\": (data === null || data === void 0 ? void 0 : (_data_distribution_points_json1 = data.distribution_points_json) === null || _data_distribution_points_json1 === void 0 ? void 0 : _data_distribution_points_json1.length) > 0 ? data === null || data === void 0 ? void 0 : data.distribution_points_json[0].from_dp : \"N/A\",\n                            \"Bool 2\": \"N/A\",\n                            \"Bool 3\": \"N/A\",\n                            \"Address line 1\": data === null || data === void 0 ? void 0 : data.address_line_1,\n                            \"Address line 2\": data === null || data === void 0 ? void 0 : data.address_line_2,\n                            \"Address line 3\": data === null || data === void 0 ? void 0 : data.address_line_3,\n                            \"Address line 4\": data === null || data === void 0 ? void 0 : data.address_line_4,\n                            \"Post code\": data === null || data === void 0 ? void 0 : data.postal_code,\n                            \"Currency Number\": (data === null || data === void 0 ? void 0 : data.currency_id) ? data === null || data === void 0 ? void 0 : data.currency_id : 1,\n                            \"Bank general ledger code\": (data === null || data === void 0 ? void 0 : data.iss_ledger_code) ? data === null || data === void 0 ? void 0 : data.iss_ledger_code : \"12200\",\n                            \"Bank general ledger code Currency number if bank\": (data === null || data === void 0 ? void 0 : data.currency_id) ? data === null || data === void 0 ? void 0 : data.currency_id : 1,\n                            \"Settlement days\": data === null || data === void 0 ? void 0 : data.payment_terms,\n                            \"Department Number\": (data === null || data === void 0 ? void 0 : (_data_prophets_17 = data.prophets[0]) === null || _data_prophets_17 === void 0 ? void 0 : _data_prophets_17.prophet_id) == 1 ? 1 : (data === null || data === void 0 ? void 0 : (_data_prophets_18 = data.prophets[0]) === null || _data_prophets_18 === void 0 ? void 0 : _data_prophets_18.prophet_id) == 2 ? 9 : (data === null || data === void 0 ? void 0 : data.prophets[0].prophet_id) == 3 ? 3 : (data === null || data === void 0 ? void 0 : data.prophets[0].prophet_id) == 4 ? 2 : \"N/A\",\n                            \"Area Number\": \"1\",\n                            Vatable: (data === null || data === void 0 ? void 0 : data.vatable) != null ? (data === null || data === void 0 ? void 0 : data.vatable) ? \"1\" : \"0\" : \"0\",\n                            Buyer: \"1\",\n                            \"Billing type\": \"0\",\n                            \"Payment type\": (data === null || data === void 0 ? void 0 : data.payment_type) ? data === null || data === void 0 ? void 0 : data.payment_type : 2,\n                            \"Expense general ledger code\": \"N/A\",\n                            \"Authorise on register\": \"N/A\",\n                            \"Use % authorise rule\": 5,\n                            \"User text 1\": \"N/A\",\n                            \"Mandatory altfil on service jobs\": \"N/A\",\n                            \"Organization ID\": \"N/A\",\n                            FromDP: (formattedDistributionData === null || formattedDistributionData === void 0 ? void 0 : formattedDistributionData.length) > 0 ? formattedDistributionData[0].from_dp || \"\" : \"\",\n                            id: data === null || data === void 0 ? void 0 : data.id,\n                            isEmergencyAndFinanceNotComplete: data === null || data === void 0 ? void 0 : data.isEmergencyAndFinanceNotComplete\n                        }\n                    ],\n                    [\n                        \"sendacrole (Supplier role file)\"\n                    ]\n                ];\n                const addSendacRoleDataToSheet = (sheetIndex, dataToAdd)=>{\n                    if (filteredISSExportData[sheetIndex].length === 1) {\n                        filteredISSExportData[sheetIndex].push(...dataToAdd);\n                    } else {\n                        filteredISSExportData[sheetIndex] = [\n                            filteredISSExportData[sheetIndex][0],\n                            ...dataToAdd\n                        ];\n                    }\n                };\n                addSendacRoleDataToSheet(2, sendacRoleOnRoleNums);\n                export_ISS_response = await (0,_exportExcel__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(filteredISSExportData, false, token, company, userData, prophet_id, (_params_data1 = params.data) === null || _params_data1 === void 0 ? void 0 : _params_data1.requestor_email, \"\");\n            } else {\n                export_ISS_response = \"Not sent\";\n            }\n            const exportInternal_response = await (0,_exportExcel__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(filteredInternalExportData, true, token, company, userData, prophet_id, (_params_data = params.data) === null || _params_data === void 0 ? void 0 : _params_data.requestor_email, \"\");\n            setEmailStatusPopup(true);\n            if (export_ISS_response && exportInternal_response && export_ISS_response != \"Not sent\") {\n                setPopUpMessage(\"Email successfully sent to both Finance Department and ISS Admin Team\");\n                setISSExportSuccess(true);\n                setInternalExportSuccess(true);\n            } else if (export_ISS_response && export_ISS_response != \"Not sent\") {\n                setPopUpMessage(\"Email sent to ISS Admin Team , but not to Finance Department\");\n                setInternalExportSuccess(true);\n            } else if (exportInternal_response && export_ISS_response != \"Not sent\") {\n                setISSExportSuccess(true);\n                setPopUpMessage(\"Email sent to ISS Admin Team, but not to Finance Department\");\n            } else if (exportInternal_response && export_ISS_response == \"Not sent\") {\n                setPopUpMessage(\"Email sent to Finance Department , but not to ISS Admin as only Haulier or Expense role not allowed to export on ISS\");\n                setInternalExportSuccess(true);\n            } else {\n                setPopUpMessage(\"Email not sent to either Finance Department or ISS Admin Team\");\n            }\n        } else {\n            var _data_roleIds4;\n            if (params.data.isEmergencyRequest && (params.data.General === \"Incomplete\" || params.data.General == \"Not Entered\")) {\n                react_toastify__WEBPACK_IMPORTED_MODULE_8__.toast.error(\"General section needs to complete\", {\n                    position: \"top-right\",\n                    autoClose: 3000,\n                    hideProgressBar: false,\n                    closeOnClick: true,\n                    pauseOnHover: false,\n                    draggable: true,\n                    progress: undefined,\n                    theme: \"light\"\n                });\n                return;\n            } else if (params.data.isEmergencyRequest && ((data === null || data === void 0 ? void 0 : data.roleIds.includes(1)) || (data === null || data === void 0 ? void 0 : (_data_roleIds4 = data.roleIds) === null || _data_roleIds4 === void 0 ? void 0 : _data_roleIds4.includes(6))) && !params.data.currency_id) {\n                react_toastify__WEBPACK_IMPORTED_MODULE_8__.toast.error(\"Please select a currency and a valid supplier code to export\", {\n                    position: \"top-right\",\n                    autoClose: 3000,\n                    hideProgressBar: false,\n                    closeOnClick: true,\n                    pauseOnHover: false,\n                    draggable: true,\n                    progress: undefined,\n                    theme: \"light\"\n                });\n                return;\n            } else {\n                react_toastify__WEBPACK_IMPORTED_MODULE_8__.toast.error(\"Supplier details are incomplete or not confirmed.\", {\n                    position: \"top-right\",\n                    autoClose: 3000,\n                    hideProgressBar: false,\n                    closeOnClick: true,\n                    pauseOnHover: false,\n                    draggable: true,\n                    progress: undefined,\n                    theme: \"light\"\n                });\n                return;\n            }\n        }\n        setSelectedExportType(\"\");\n    };\n    const handleCopySupplier = ()=>{\n        setIsOpen(true);\n    };\n    if (isOpen) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_CopySupplier__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n            isOpen: true,\n            setIsOpen: setIsOpen,\n            supplierData: data,\n            userData: userData\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\utils\\\\renderer\\\\actionRenderer.js\",\n            lineNumber: 1122,\n            columnNumber: 7\n        }, undefined);\n    }\n    const disabledClass = \"text-gray-500 cursor-not-allowed\";\n    const handleExportType = (e)=>{\n        setSelectedExportType(e.target.value);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex flex-row gap-4 justify-center text-blue-500\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: ()=>status != \"Exported\" && status != \"Cancelled\" ? editSupplier() : confirmPage(),\n                        children: status == \"Exported\" || status == \"Cancelled\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fortawesome_react_fontawesome__WEBPACK_IMPORTED_MODULE_1__.FontAwesomeIcon, {\n                            icon: _fortawesome_free_solid_svg_icons__WEBPACK_IMPORTED_MODULE_14__.faEye,\n                            size: \"lg\",\n                            title: \"View Supplier\",\n                            className: \"text-skin-primary\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\utils\\\\renderer\\\\actionRenderer.js\",\n                            lineNumber: 1151,\n                            columnNumber: 13\n                        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fortawesome_react_fontawesome__WEBPACK_IMPORTED_MODULE_1__.FontAwesomeIcon, {\n                            icon: _fortawesome_free_solid_svg_icons__WEBPACK_IMPORTED_MODULE_14__.faPenToSquare,\n                            size: \"lg\",\n                            title: \"Edit Supplier\",\n                            className: \"text-skin-primary\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\utils\\\\renderer\\\\actionRenderer.js\",\n                            lineNumber: 1158,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\utils\\\\renderer\\\\actionRenderer.js\",\n                        lineNumber: 1143,\n                        columnNumber: 9\n                    }, undefined),\n                    status != \"Cancelled\" && canExport && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: ()=>openOptionModal(supplier_id),\n                        title: \"Export Supplier\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fortawesome_react_fontawesome__WEBPACK_IMPORTED_MODULE_1__.FontAwesomeIcon, {\n                            icon: _fortawesome_free_solid_svg_icons__WEBPACK_IMPORTED_MODULE_14__.faFileExport,\n                            size: \"lg\",\n                            className: \"text-skin-primary\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\utils\\\\renderer\\\\actionRenderer.js\",\n                            lineNumber: 1171,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\utils\\\\renderer\\\\actionRenderer.js\",\n                        lineNumber: 1167,\n                        columnNumber: 11\n                    }, undefined),\n                    status != \"Cancelled\" && status != \"Exported\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: cancelProduct,\n                        title: \"Cancel Product\",\n                        className: \"flex items-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fortawesome_react_fontawesome__WEBPACK_IMPORTED_MODULE_1__.FontAwesomeIcon, {\n                            icon: _fortawesome_free_solid_svg_icons__WEBPACK_IMPORTED_MODULE_14__.faXmark,\n                            size: \"sm\",\n                            className: \"border rounded-sm border-skin-primary text-skin-primary m-0 w-[15px] h-[15px]\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\utils\\\\renderer\\\\actionRenderer.js\",\n                            lineNumber: 1184,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\utils\\\\renderer\\\\actionRenderer.js\",\n                        lineNumber: 1179,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\utils\\\\renderer\\\\actionRenderer.js\",\n                lineNumber: 1139,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Dialog_Transition_headlessui_react__WEBPACK_IMPORTED_MODULE_15__.Transition, {\n                appear: true,\n                show: isCancelOpen,\n                as: react__WEBPACK_IMPORTED_MODULE_5__.Fragment,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Dialog_Transition_headlessui_react__WEBPACK_IMPORTED_MODULE_15__.Dialog, {\n                    as: \"div\",\n                    className: \"relative z-10\",\n                    onClose: closeCancelModal,\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Dialog_Transition_headlessui_react__WEBPACK_IMPORTED_MODULE_15__.Transition.Child, {\n                            as: react__WEBPACK_IMPORTED_MODULE_5__.Fragment,\n                            enter: \"ease-out duration-300\",\n                            enterFrom: \"opacity-0\",\n                            enterTo: \"opacity-100\",\n                            leave: \"ease-in duration-200\",\n                            leaveFrom: \"opacity-100\",\n                            leaveTo: \"opacity-0\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"fixed inset-0 bg-black bg-opacity-25 backdrop-blur-sm\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\utils\\\\renderer\\\\actionRenderer.js\",\n                                lineNumber: 1204,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\utils\\\\renderer\\\\actionRenderer.js\",\n                            lineNumber: 1195,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"fixed inset-0 overflow-y-auto\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-center min-h-full p-4 text-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Dialog_Transition_headlessui_react__WEBPACK_IMPORTED_MODULE_15__.Transition.Child, {\n                                    as: react__WEBPACK_IMPORTED_MODULE_5__.Fragment,\n                                    enter: \"ease-out duration-300\",\n                                    enterFrom: \"opacity-0 scale-95\",\n                                    enterTo: \"opacity-100 scale-100\",\n                                    leave: \"ease-in duration-200\",\n                                    leaveFrom: \"opacity-100 scale-100\",\n                                    leaveTo: \"opacity-0 scale-95\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Dialog_Transition_headlessui_react__WEBPACK_IMPORTED_MODULE_15__.Dialog.Panel, {\n                                        className: \" w-[45%] transform overflow-hidden rounded-xl bg-white  text-left align-middle shadow-xl transition-all\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative bg-white rounded-lg shadow\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-start justify-between p-8 rounded-t\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"flex flex-row text-xl font-semibold text-gray-900  items-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"flex justify-center items-center border border-skin-primary text-skin-primary w-[25px] h-[25px] text-center leading-5 rounded-full text-base me-4\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fortawesome_react_fontawesome__WEBPACK_IMPORTED_MODULE_1__.FontAwesomeIcon, {\n                                                                            icon: _fortawesome_free_solid_svg_icons__WEBPACK_IMPORTED_MODULE_14__.faInfo\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\utils\\\\renderer\\\\actionRenderer.js\",\n                                                                            lineNumber: 1225,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        \" \"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\utils\\\\renderer\\\\actionRenderer.js\",\n                                                                    lineNumber: 1224,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                \" \",\n                                                                \"Warning\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\utils\\\\renderer\\\\actionRenderer.js\",\n                                                            lineNumber: 1223,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: closeCancelModal,\n                                                            type: \"button\",\n                                                            className: \"text-black bg-transparent hover:bg-gray-200 hover:text-gray-900 rounded-lg text-xl w-8 h-8 ml-auto inline-flex justify-center items-center \",\n                                                            \"data-modal-hide\": \"default-modal\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fortawesome_react_fontawesome__WEBPACK_IMPORTED_MODULE_1__.FontAwesomeIcon, {\n                                                                    icon: _fortawesome_free_solid_svg_icons__WEBPACK_IMPORTED_MODULE_14__.faXmark,\n                                                                    className: \"text-skin-primary\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\utils\\\\renderer\\\\actionRenderer.js\",\n                                                                    lineNumber: 1235,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                \" \"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\utils\\\\renderer\\\\actionRenderer.js\",\n                                                            lineNumber: 1229,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\utils\\\\renderer\\\\actionRenderer.js\",\n                                                    lineNumber: 1222,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"p-8 py-0 space-y-6\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-base xl:text-md 2xl:text-lg leading-relaxed mt-2\",\n                                                        children: \"Are you sure you want to cancel supplier?\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\utils\\\\renderer\\\\actionRenderer.js\",\n                                                        lineNumber: 1243,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\utils\\\\renderer\\\\actionRenderer.js\",\n                                                    lineNumber: 1242,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-end p-6 space-x-2 justify-end\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: closeCancelModal,\n                                                            \"data-modal-hide\": \"default-modal\",\n                                                            type: \"button\",\n                                                            className: \"border border-skin-primary text-skin-primary focus:ring-4 focus:outline-none focus:ring-blue-300 font-medium rounded-md text-md px-6 py-2 text-center \",\n                                                            children: \"No\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\utils\\\\renderer\\\\actionRenderer.js\",\n                                                            lineNumber: 1249,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: saveModalData,\n                                                            \"data-modal-hide\": \"default-modal\",\n                                                            type: \"button\",\n                                                            className: \"text-white bg-red-500 hover:bg-red-500 focus:ring-4 focus:outline-none focus:ring-blue-300 font-medium rounded-md text-md px-6 py-2 text-center \",\n                                                            children: \"Yes\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\utils\\\\renderer\\\\actionRenderer.js\",\n                                                            lineNumber: 1257,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\utils\\\\renderer\\\\actionRenderer.js\",\n                                                    lineNumber: 1248,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\utils\\\\renderer\\\\actionRenderer.js\",\n                                            lineNumber: 1220,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\utils\\\\renderer\\\\actionRenderer.js\",\n                                        lineNumber: 1218,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\utils\\\\renderer\\\\actionRenderer.js\",\n                                    lineNumber: 1209,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\utils\\\\renderer\\\\actionRenderer.js\",\n                                lineNumber: 1208,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\utils\\\\renderer\\\\actionRenderer.js\",\n                            lineNumber: 1207,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\utils\\\\renderer\\\\actionRenderer.js\",\n                    lineNumber: 1194,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\utils\\\\renderer\\\\actionRenderer.js\",\n                lineNumber: 1193,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Dialog_Transition_headlessui_react__WEBPACK_IMPORTED_MODULE_15__.Transition, {\n                appear: true,\n                show: emailStatusPopup,\n                as: react__WEBPACK_IMPORTED_MODULE_5__.Fragment,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Dialog_Transition_headlessui_react__WEBPACK_IMPORTED_MODULE_15__.Dialog, {\n                    as: \"div\",\n                    className: \"relative z-10\",\n                    onClose: closeEmailPopup,\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Dialog_Transition_headlessui_react__WEBPACK_IMPORTED_MODULE_15__.Transition.Child, {\n                            as: react__WEBPACK_IMPORTED_MODULE_5__.Fragment,\n                            enter: \"ease-out duration-300\",\n                            enterFrom: \"opacity-0\",\n                            enterTo: \"opacity-100\",\n                            leave: \"ease-in duration-200\",\n                            leaveFrom: \"opacity-100\",\n                            leaveTo: \"opacity-0\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"fixed inset-0 bg-black bg-opacity-25 backdrop-blur-sm\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\utils\\\\renderer\\\\actionRenderer.js\",\n                                lineNumber: 1284,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\utils\\\\renderer\\\\actionRenderer.js\",\n                            lineNumber: 1275,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"fixed inset-0 overflow-y-auto\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-center min-h-full p-4 text-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Dialog_Transition_headlessui_react__WEBPACK_IMPORTED_MODULE_15__.Transition.Child, {\n                                    as: react__WEBPACK_IMPORTED_MODULE_5__.Fragment,\n                                    enter: \"ease-out duration-300\",\n                                    enterFrom: \"opacity-0 scale-95\",\n                                    enterTo: \"opacity-100 scale-100\",\n                                    leave: \"ease-in duration-200\",\n                                    leaveFrom: \"opacity-100 scale-100\",\n                                    leaveTo: \"opacity-0 scale-95\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Dialog_Transition_headlessui_react__WEBPACK_IMPORTED_MODULE_15__.Dialog.Panel, {\n                                        className: \" w-[45%] transform overflow-hidden rounded-xl bg-white  text-left align-middle shadow-xl transition-all\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative bg-white rounded-lg shadow\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-start justify-between p-8 rounded-t\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"flex flex-row text-xl font-semibold text-gray-900  items-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"flex justify-center items-center border border-skin-primary text-skin-primary w-[25px] h-[25px] text-center leading-5 rounded-full text-base me-4\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fortawesome_react_fontawesome__WEBPACK_IMPORTED_MODULE_1__.FontAwesomeIcon, {\n                                                                            icon: _fortawesome_free_solid_svg_icons__WEBPACK_IMPORTED_MODULE_14__.faInfo\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\utils\\\\renderer\\\\actionRenderer.js\",\n                                                                            lineNumber: 1305,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        \" \"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\utils\\\\renderer\\\\actionRenderer.js\",\n                                                                    lineNumber: 1304,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                \" \",\n                                                                \"Status Message\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\utils\\\\renderer\\\\actionRenderer.js\",\n                                                            lineNumber: 1303,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: closeEmailPopup,\n                                                            type: \"button\",\n                                                            className: \"text-black bg-transparent hover:bg-gray-200 hover:text-gray-900 rounded-lg text-xl w-8 h-8 ml-auto inline-flex justify-center items-center \",\n                                                            \"data-modal-hide\": \"default-modal\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fortawesome_react_fontawesome__WEBPACK_IMPORTED_MODULE_1__.FontAwesomeIcon, {\n                                                                    icon: _fortawesome_free_solid_svg_icons__WEBPACK_IMPORTED_MODULE_14__.faXmark,\n                                                                    className: \"text-skin-primary\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\utils\\\\renderer\\\\actionRenderer.js\",\n                                                                    lineNumber: 1315,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                \" \"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\utils\\\\renderer\\\\actionRenderer.js\",\n                                                            lineNumber: 1309,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\utils\\\\renderer\\\\actionRenderer.js\",\n                                                    lineNumber: 1302,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"p-8 py-0 space-y-6\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-base xl:text-md 2xl:text-lg leading-relaxed mt-2\",\n                                                        children: popupMessage\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\utils\\\\renderer\\\\actionRenderer.js\",\n                                                        lineNumber: 1323,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\utils\\\\renderer\\\\actionRenderer.js\",\n                                                    lineNumber: 1322,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-end p-6 space-x-2 justify-end\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: closeEmailPopup,\n                                                        \"data-modal-hide\": \"default-modal\",\n                                                        type: \"button\",\n                                                        className: \"border text-dark-gray focus:ring-4 focus:outline-none focus:ring-blue-300 font-medium rounded-md text-md px-6 py-2 text-center \",\n                                                        children: \"Ok\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\utils\\\\renderer\\\\actionRenderer.js\",\n                                                        lineNumber: 1329,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\utils\\\\renderer\\\\actionRenderer.js\",\n                                                    lineNumber: 1328,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\utils\\\\renderer\\\\actionRenderer.js\",\n                                            lineNumber: 1300,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\utils\\\\renderer\\\\actionRenderer.js\",\n                                        lineNumber: 1298,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\utils\\\\renderer\\\\actionRenderer.js\",\n                                    lineNumber: 1289,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\utils\\\\renderer\\\\actionRenderer.js\",\n                                lineNumber: 1288,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\utils\\\\renderer\\\\actionRenderer.js\",\n                            lineNumber: 1287,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\utils\\\\renderer\\\\actionRenderer.js\",\n                    lineNumber: 1274,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\utils\\\\renderer\\\\actionRenderer.js\",\n                lineNumber: 1273,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true);\n};\n_s(actionRenderer, \"oYr84Owku3ZZ/XUqh43OyVW+Os0=\", false, function() {\n    return [\n        next_router__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        _utils_rolePermissionsContext__WEBPACK_IMPORTED_MODULE_13__.usePermissions,\n        _utils_userContext__WEBPACK_IMPORTED_MODULE_10__.useUser\n    ];\n});\n/* harmony default export */ __webpack_exports__[\"default\"] = (actionRenderer);\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevExports = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevExports) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports on update so we can compare the boundary\n                // signatures.\n                module.hot.dispose(function (data) {\n                    data.prevExports = currentExports;\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevExports !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevExports, currentExports)) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevExports !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi91dGlscy9yZW5kZXJlci9hY3Rpb25SZW5kZXJlci5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFBaUU7QUFNdEI7QUFDSztBQUNuQjtBQUNvQjtBQUNLO0FBQ3RCO0FBQ0g7QUFDZ0I7QUFDVTtBQUNSO0FBQ0Q7QUFDUTtBQUNEO0FBQ0U7QUFDc0I7QUFFYjtBQUN2QjtBQUN6QyxNQUFNMkIsaUJBQWlCLENBQUNDLFFBQVFDLFVBQVVDLE9BQU9DO1FBY1JDOztJQWJ2QyxNQUFNQyxTQUFTM0Isc0RBQVNBO0lBQ3hCLE1BQU00QixZQUNKTCxDQUFBQSxxQkFBQUEsK0JBQUFBLFNBQVVNLE9BQU8sTUFBSyxLQUN0Qk4sQ0FBQUEscUJBQUFBLCtCQUFBQSxTQUFVTSxPQUFPLE1BQUssS0FDdEJOLENBQUFBLHFCQUFBQSwrQkFBQUEsU0FBVU0sT0FBTyxNQUFLLEtBQ3RCTixDQUFBQSxxQkFBQUEsK0JBQUFBLFNBQVVNLE9BQU8sTUFBSyxLQUN0QlAsT0FBT1EsSUFBSSxDQUFDQyxrQkFBa0I7SUFDaEMsTUFBTSxFQUFFQyxXQUFXLEVBQUVDLGlCQUFpQixFQUFFLEdBQUdkLDhFQUFjQTtJQUV6RCxNQUFNZSxnQkFBZ0JoQywwREFBU0EsQ0FBQ2dDLGFBQWE7SUFDN0MsTUFBTSxFQUFFQyxXQUFXLEVBQUVDLFdBQVcsRUFBRSxHQUFHekIsNERBQU9BO0lBQzVDLE1BQU1lLGVBQWVKLE9BQU9RLElBQUk7SUFDaEMsSUFBSU8sYUFDRlgsQ0FBQUEseUJBQUFBLG1DQUFBQSxhQUFjWSxRQUFRLENBQUNDLE1BQU0sSUFBRyxNQUFLYix5QkFBQUEsb0NBQUFBLDBCQUFBQSxhQUFjWSxRQUFRLENBQUMsRUFBRSxjQUF6QlosOENBQUFBLHdCQUEyQlcsVUFBVTtJQUM1RSxNQUFNRyxXQUFXbEIsT0FBT1EsSUFBSSxDQUFDVyxNQUFNLENBQUNDLEdBQUcsQ0FBQyxDQUFDQyxNQUFRQSxJQUFJZCxPQUFPO0lBRTVELE1BQU1lLGNBQWN0QixPQUFPUSxJQUFJLENBQUNlLEVBQUU7SUFDbEMsTUFBTUMsa0JBQWtCeEIsT0FBT1EsSUFBSSxDQUFDaUIsTUFBTTtJQUMxQyxNQUFNLENBQUNBLFFBQVFDLFVBQVUsR0FBRzdDLCtDQUFRQSxDQUFDO0lBQ3JDLE1BQU0sQ0FBQzJCLE1BQU1tQixRQUFRLEdBQUc5QywrQ0FBUUEsQ0FBQyxFQUFFO0lBQ25DLE1BQU0rQyxPQUFPdEMsb0VBQWFBLENBQUM7SUFDM0IsTUFBTSxDQUFDdUMsUUFBUUMsVUFBVSxHQUFHakQsK0NBQVFBLENBQUM7SUFDckMsTUFBTSxDQUFDa0QsY0FBY0MsZ0JBQWdCLEdBQUduRCwrQ0FBUUEsQ0FBQztJQUNqRCxNQUFNLENBQUNvRCxvQkFBb0JDLHNCQUFzQixHQUFHckQsK0NBQVFBLENBQUM7SUFDN0QsTUFBTSxDQUFDc0Qsa0JBQWtCQyxvQkFBb0IsR0FBR3ZELCtDQUFRQSxDQUFDO0lBQ3pELE1BQU0sQ0FBQ3dELGNBQWNDLGdCQUFnQixHQUFHekQsK0NBQVFBLENBQUM7SUFDakQsTUFBTSxDQUFDMEQsdUJBQXVCQyx5QkFBeUIsR0FBRzNELCtDQUFRQSxDQUFDO0lBQ25FLE1BQU0sQ0FBQzRELGtCQUFrQkMsb0JBQW9CLEdBQUc3RCwrQ0FBUUEsQ0FBQztJQUV6RCxNQUFNOEQsbUJBQW1CO1FBQ3ZCWCxnQkFBZ0I7SUFDbEI7SUFFQSxNQUFNLENBQUNZLGNBQWNDLGdCQUFnQixHQUFHaEUsK0NBQVFBLENBQUM7SUFDakQsTUFBTWlFLGdCQUFnQjtRQUNwQkQsZ0JBQWdCO0lBQ2xCO0lBRUEsTUFBTUUsbUJBQW1CO1FBQ3ZCRixnQkFBZ0I7SUFDbEI7SUFDQSxNQUFNRyxrQkFBa0I7UUFDdEJaLG9CQUFvQjtRQUNwQixJQUFJRyx5QkFBeUJFLGtCQUFrQjtZQUM3Q3pDLE9BQU9pRCxxQkFBcUIsQ0FBQ3pDLGlCQUFBQSwyQkFBQUEsS0FBTWUsRUFBRSxFQUFFZixpQkFBQUEsMkJBQUFBLEtBQU1pQixNQUFNO1FBQ3JEO0lBQ0Y7SUFFQSxTQUFTeUI7WUFnQldsRCxjQUVHQTtRQWpCckIsSUFBSVksZ0JBQWdCaEMsMERBQVNBLENBQUNnQyxhQUFhO1FBRTNDLG9CQUFvQjtRQUNwQnVDLE1BQU0sR0FBNkM3QixPQUExQ1YsZUFBYyw4QkFBd0MsT0FBWlUsY0FBZTtZQUNoRThCLFFBQVE7WUFDUkMsU0FBUztnQkFDUEMsUUFBUTtnQkFDUixnQkFBZ0I7Z0JBQ2hCQyxlQUFlLFVBQXlCLE9BQWZ0RCxTQUFTQyxLQUFLO1lBQ3pDO1lBQ0FzRCxNQUFNQyxLQUFLQyxTQUFTLENBQUM7Z0JBQ25CQyxhQUFhO2dCQUNiQyxNQUFNO2dCQUNObkMsUUFBUTtnQkFDUm9DLGNBQWMsSUFBSUMsT0FBT0MsV0FBVztnQkFDcENDLFlBQVksRUFBRWhFLG1CQUFBQSw4QkFBQUEsZUFBQUEsT0FBUVEsSUFBSSxjQUFaUixtQ0FBQUEsYUFBY2dFLFlBQVk7Z0JBQ3hDQyxnQkFBZ0JqRSxPQUFPUSxJQUFJLENBQUMwRCxTQUFTO2dCQUNyQ0MsZUFBZSxHQUFFbkUsZ0JBQUFBLE9BQU9RLElBQUksY0FBWFIsb0NBQUFBLGNBQWFtRSxlQUFlO1lBQy9DO1FBQ0YsR0FDR0MsSUFBSSxDQUFDLENBQUNDO1lBQ0wsSUFBSUEsSUFBSTVDLE1BQU0sS0FBSyxLQUFLO2dCQUN0QnJDLGlEQUFLQSxDQUFDa0YsS0FBSyxDQUNUO1lBRUosT0FBTyxJQUFJRCxJQUFJNUMsTUFBTSxLQUFLLEtBQUs7Z0JBQzdCckMsaURBQUtBLENBQUNrRixLQUFLLENBQUM7WUFDWixxQkFBcUI7WUFDckIsMENBQTBDO1lBQzFDLDhCQUE4QjtZQUM5QixtQ0FBbUM7WUFDbkMscUNBQXFDO1lBQ3JDLHFDQUFxQztZQUNyQyxzQ0FBc0M7WUFDdEMsMkJBQTJCO1lBQzNCLDRCQUE0QjtZQUM1Qiw0QkFBNEI7WUFDNUIsZ0NBQWdDO1lBQ2hDLFlBQVk7WUFDZDtZQUNBLElBQUlELElBQUk1QyxNQUFNLEtBQUssS0FBSztnQkFDdEIsT0FBTzRDLElBQUlFLElBQUk7WUFDakI7WUFDQSxPQUFPQyxRQUFRQyxNQUFNLENBQUNKO1FBQ3hCLEdBQ0NELElBQUksQ0FBQyxDQUFDRztZQUNMbkYsaURBQUtBLENBQUNzRixPQUFPLENBQUMsbUNBQW1DO2dCQUMvQ0MsVUFBVTtZQUNaO1lBQ0EzRSxPQUFPaUQscUJBQXFCLENBQUN6QyxpQkFBQUEsMkJBQUFBLEtBQU1lLEVBQUUsRUFBRWYsaUJBQUFBLDJCQUFBQSxLQUFNaUIsTUFBTTtZQUNuRHNCO1FBQ0YsR0FDQzZCLEtBQUssQ0FBQyxDQUFDQztZQUNOLHFCQUFxQjtZQUNyQnpGLGlEQUFLQSxDQUFDa0YsS0FBSyxDQUFDLDZCQUE2Qk8sSUFBSUMsVUFBVSxFQUFFO2dCQUN2REgsVUFBVTtZQUNaO1lBQ0EsT0FBT0U7UUFDVDtJQUNKO0lBRUEsTUFBTUUsa0JBQWtCLENBQUN6RDtRQUN2QixJQUNFbEIsYUFBYXFCLE1BQU0sSUFBSSxlQUN2QnJCLGFBQWFxQixNQUFNLElBQUksY0FDdEJ6QixPQUFPUSxJQUFJLENBQUNDLGtCQUFrQixJQUFJVCxPQUFPUSxJQUFJLENBQUN3RSxPQUFPLEtBQUssWUFDM0Q7Z0JBR2tCaEYsd0JBQUFBLGNBQ0VBLHlCQUFBQSxlQUdsQkEsZUFBb0NBLGVBR3BDQSxzQkFBQUEsZUFBcUNBLHVCQUFBQSxlQUdyQ0EsZUFDU0EsZUFDTEE7WUFkTixJQUFJaUYsZ0NBQWdDO1lBRXBDLE1BQU1DLGFBQVlsRixlQUFBQSxPQUFPUSxJQUFJLGNBQVhSLG9DQUFBQSx5QkFBQUEsYUFBYWdCLFFBQVEsQ0FBQyxFQUFFLGNBQXhCaEIsNkNBQUFBLHVCQUEwQm1GLFVBQVU7WUFDdEQsTUFBTUMsZUFBY3BGLGdCQUFBQSxPQUFPUSxJQUFJLGNBQVhSLHFDQUFBQSwwQkFBQUEsY0FBYWdCLFFBQVEsQ0FBQyxFQUFFLGNBQXhCaEIsOENBQUFBLHdCQUEwQnFGLFlBQVk7WUFFMUQsTUFBTXRFLGFBQ0pmLEVBQUFBLGdCQUFBQSxPQUFPUSxJQUFJLGNBQVhSLG9DQUFBQSxjQUFhZ0IsUUFBUSxDQUFDQyxNQUFNLElBQUcsT0FBS2pCLGdCQUFBQSxPQUFPUSxJQUFJLGNBQVhSLG9DQUFBQSxjQUFhZ0IsUUFBUSxDQUFDLEVBQUUsQ0FBQ0QsVUFBVTtZQUV6RSxNQUFNdUUsb0JBQ0p0RixFQUFBQSxnQkFBQUEsT0FBT1EsSUFBSSxjQUFYUixxQ0FBQUEsdUJBQUFBLGNBQWF1RixPQUFPLGNBQXBCdkYsMkNBQUFBLHFCQUFzQndGLFFBQVEsQ0FBQyxTQUFNeEYsZ0JBQUFBLE9BQU9RLElBQUksY0FBWFIscUNBQUFBLHdCQUFBQSxjQUFhdUYsT0FBTyxjQUFwQnZGLDRDQUFBQSxzQkFBc0J3RixRQUFRLENBQUM7WUFFdEUsSUFBSUMsV0FDRnpGLEVBQUFBLGdCQUFBQSxPQUFPUSxJQUFJLGNBQVhSLG9DQUFBQSxjQUFheUYsUUFBUSxLQUFJLE1BQ3JCLEtBQTJCLFFBQXRCekYsZ0JBQUFBLE9BQU9RLElBQUksY0FBWFIsb0NBQUFBLGNBQWF5RixRQUFRLEtBQzFCekYsZ0JBQUFBLE9BQU9RLElBQUksY0FBWFIsb0NBQUFBLGNBQWF5RixRQUFRO1lBQzNCLElBQUlDO1lBQ0osSUFBSUQsWUFBWUEsWUFBWSxlQUFlO2dCQUN6Q0MsYUFBYTtZQUNmLE9BQU87Z0JBQ0xBLGFBQWFEO1lBQ2Y7WUFFQSxJQUFJRSxVQUFVO1lBQ2QsSUFBSUwsbUJBQW1CO2dCQUNyQixJQUFJdkUsY0FBYyxHQUFHO29CQUNuQixJQUFJNkU7b0JBRUpBLGVBQWUsSUFBSUMsT0FBTywyQkFBc0MsT0FBWEgsWUFBVztvQkFFaEUsK0RBQStEO29CQUMvREMsVUFBVUMsYUFBYUUsSUFBSSxDQUFDVjtnQkFDOUIsT0FBTyxJQUFJckUsY0FBYyxHQUFHO29CQUMxQixJQUFJNkU7b0JBRUpBLGVBQWUsSUFBSUMsT0FBTyxnQkFBMkIsT0FBWEgsWUFBVztvQkFFckQsZ0VBQWdFO29CQUNoRUMsVUFBVUMsYUFBYUUsSUFBSSxDQUFDVjtnQkFDOUIsT0FBTyxJQUFJckUsY0FBYyxHQUFHO29CQUMxQixJQUFJNkUsZUFBZSxJQUFJQyxPQUFRO29CQUMvQkYsVUFBVUMsYUFBYUUsSUFBSSxDQUFDVixnQkFBZ0JBLFlBQVluRSxNQUFNLElBQUk7Z0JBQ3BFLE9BQU8sSUFBSUYsY0FBYyxHQUFHO29CQUMxQixJQUFJNkU7b0JBRUpBLGVBQWUsSUFBSUMsT0FBTyxnQkFBMkIsT0FBWEgsWUFBVztvQkFFckRDLFVBQVVDLGFBQWFFLElBQUksQ0FBQ1Y7Z0JBQzlCO1lBQ0Y7WUFFQSxJQUFJRixhQUFhQSxZQUFZLEtBQUtFLGVBQWVBLGdCQUFnQixJQUFJO2dCQUNuRUgsZ0NBQWdDO1lBQ2xDLE9BQU8sSUFDTEMsYUFDQUEsYUFBYSxLQUNiRSxlQUNBQSxnQkFBZ0IsSUFDaEI7Z0JBQ0FILGdDQUFnQztZQUNsQztZQUVBLElBQUksQ0FBQ0EsaUNBQWlDLENBQUNVLFNBQVM7Z0JBQzlDdkcsaURBQUtBLENBQUNrRixLQUFLLENBQ1Q7Z0JBRUY7WUFDRixPQUFPLElBQUksQ0FBQ1csK0JBQStCO2dCQUN6QzdGLGlEQUFLQSxDQUFDa0YsS0FBSyxDQUNUO2dCQUVGO1lBQ0YsT0FBTyxJQUFJLENBQUNxQixTQUFTO2dCQUNuQnZHLGlEQUFLQSxDQUFDa0YsS0FBSyxDQUNUO2dCQUVGO1lBQ0Y7WUFDQXlCLDJCQUEyQnpFO1FBQzdCLE9BQU87WUFDTHlFLDJCQUEyQnpFO1FBQzdCO0lBQ0Y7SUFFQXhDLGdEQUFTQSxDQUFDO1FBQ1IsSUFBSXdDLGFBQWE7WUFDZkssUUFBUXZCO1lBQ1JzQixVQUFVRjtRQUNaO0lBQ0YsR0FBRztRQUFDRjtLQUFZO0lBRWhCLE1BQU0wRSxlQUFlO1FBQ25CLElBQUksSUFBNkIsRUFBRTtZQUNqQzNGLE9BQU80RixJQUFJLENBQUM7Z0JBQ1ZDLFVBQVUsYUFBeUIsT0FBWjVFLGFBQVk7WUFFckM7UUFDRjtJQUNGO0lBQ0EsTUFBTTZFLGNBQWM7UUFDbEIsTUFBTUMsb0JBQW9CbEYsU0FBU0UsR0FBRyxDQUFDLENBQUNELFNBQVk7Z0JBQ2xEQSxRQUFRQTtnQkFDUlQsYUFBYUEsV0FBVyxDQUFDUyxPQUFPO1lBQ2xDO1FBQ0EsTUFBTWtGLGlCQUFpQjtlQUNsQixJQUFJQyxJQUFJRixrQkFBa0JHLE9BQU8sQ0FBQyxDQUFDQyxPQUFTQSxLQUFLOUYsV0FBVztTQUNoRTtRQUVEK0YsYUFBYUMsT0FBTyxDQUFDLG1CQUFtQkw7UUFDeEMsSUFBSSxJQUE2QixFQUFFO1lBQ2pDaEcsT0FBTzRGLElBQUksQ0FBQztnQkFDVkMsVUFBVSxhQUF5QixPQUFaNUUsYUFBWTtZQUNyQztRQUNGO0lBQ0Y7SUFFQSxTQUFTcUYsVUFBVUMsb0JBQW9CLEVBQUVDLFVBQVUsRUFBRXBCLFFBQVEsRUFBRUYsT0FBTztRQUNwRSxJQUFJcUIsc0JBQXNCO1lBQ3hCLE9BQU9BO1FBQ1QsT0FBTztZQUNMLE9BQU87UUFDVDtJQUNGO0lBRUEsTUFBTUUsa0JBQWtCLENBQUNDO1FBQ3ZCLElBQUk7WUFDRixNQUFNQyxXQUFXRCxrQkFBa0J0RCxLQUFLd0QsS0FBSyxDQUFDRixtQkFBbUIsRUFBRTtZQUNuRSxJQUFJRyxNQUFNQyxPQUFPLENBQUNILFdBQVc7Z0JBQzNCLE9BQU9BLFNBQVM1RixHQUFHLENBQUMsQ0FBQ2dHO3dCQUNGNUcsaUJBQ2JBLDhCQUFBQTsyQkFGNEI7d0JBQ2hDLGlCQUFpQkEsQ0FBQUEsaUJBQUFBLDRCQUFBQSxrQkFBQUEsS0FBTVEsUUFBUSxDQUFDLEVBQUUsY0FBakJSLHNDQUFBQSxnQkFBbUI2RSxZQUFZLElBQzVDN0UsaUJBQUFBLDRCQUFBQSxtQkFBQUEsS0FBTVEsUUFBUSxDQUFDLEVBQUUsY0FBakJSLHdDQUFBQSwrQkFBQUEsaUJBQW1CNkUsWUFBWSxjQUEvQjdFLG1EQUFBQSw2QkFBaUM2RyxJQUFJLEtBQ3JDO3dCQUNKLGNBQWM7d0JBQ2RDLE1BQU05RyxLQUFLd0QsWUFBWSxJQUFJO3dCQUMzQixpQkFBaUJvRCxRQUFRRyxRQUFRLElBQUk7d0JBQ3JDLG9CQUFvQkgsUUFBUUksU0FBUyxJQUFJO3dCQUN6QyxxQkFBcUI7d0JBQ3JCLGNBQWM7d0JBQ2QsbUJBQW1CO3dCQUNuQixvQkFBb0I7d0JBQ3BCLGtCQUFrQjt3QkFDbEIsZ0JBQWdCO3dCQUNoQixxQkFBcUI7b0JBQ3ZCOztZQUNGO1FBQ0YsRUFBRSxPQUFPbEQsT0FBTztZQUNkbUQsUUFBUW5ELEtBQUssQ0FBQyxnQ0FBZ0NBO1FBQ2hEO1FBRUEsT0FBTztZQUNMO2dCQUNFLGNBQWM7Z0JBQ2RnRCxNQUFNOUcsS0FBS3dELFlBQVksSUFBSTtnQkFDM0IsaUJBQWlCO2dCQUNqQixvQkFBb0I7Z0JBQ3BCLHFCQUFxQjtnQkFDckIsY0FBYztnQkFDZCxtQkFBbUI7Z0JBQ25CLG9CQUFvQjtnQkFDcEIsa0JBQWtCO2dCQUNsQixnQkFBZ0I7Z0JBQ2hCLHFCQUFxQjtZQUN2QjtTQUNEO0lBQ0g7SUFFQSxNQUFNMEQscUJBQXFCLENBQUNDLGlCQUFpQnBHO1FBQzNDLElBQUk7WUFDRixNQUFNcUcsZUFBZUQsa0JBQWtCbEUsS0FBS3dELEtBQUssQ0FBQ1UsbUJBQW1CLEVBQUU7WUFDdkUsSUFBSVQsTUFBTUMsT0FBTyxDQUFDUyxlQUFlO2dCQUMvQixNQUFNQyxpQkFBaUJELGFBQWFFLE1BQU0sQ0FDeEMsQ0FBQ0MsUUFBVUEsQ0FBQUEsa0JBQUFBLDRCQUFBQSxNQUFPQyxVQUFVLE1BQUt6RztnQkFFbkMsSUFBSXNHLGVBQWU1RyxNQUFNLEdBQUcsR0FBRztvQkFDN0IsT0FBTzRHLGVBQWV6RyxHQUFHLENBQUMsQ0FBQzJHLFFBQVc7NEJBQ3BDLGtCQUFrQjs0QkFDbEJFLGFBQWFGLENBQUFBLGtCQUFBQSw0QkFBQUEsTUFBT0MsVUFBVSxJQUFHRCxrQkFBQUEsNEJBQUFBLE1BQU9HLEtBQUssR0FBRzt3QkFDbEQ7Z0JBQ0YsT0FBTztvQkFDTCxrREFBa0Q7b0JBQ2xELE9BQU8sRUFBRSxFQUFFLHVDQUF1QztnQkFDcEQ7WUFDRjtRQUNGLEVBQUUsT0FBTzVELE9BQU87WUFDZG1ELFFBQVFuRCxLQUFLLENBQUMsZ0NBQWdDQTtRQUNoRDtJQUNGO0lBQ0EsTUFBTTZELDZCQUE2QjtZQUNoQjNIO1FBQWpCLE1BQU00SCxXQUFXNUgsaUJBQUFBLDRCQUFBQSxpQkFBQUEsS0FBTTZILFFBQVEsY0FBZDdILHFDQUFBQSxlQUFnQjhILEtBQUssQ0FBQyxLQUFLbEgsR0FBRyxDQUFDLENBQUNtSCxNQUFRQSxJQUFJbEIsSUFBSTtRQUNqRSxPQUFPZSxTQUFTaEgsR0FBRyxDQUFDLENBQUNtSDtnQkFFRi9ILGlCQUdKQTttQkFMZTtnQkFDNUJnSSxjQUFjO2dCQUNkLGlCQUFpQmhJLENBQUFBLGlCQUFBQSw0QkFBQUEsa0JBQUFBLEtBQU1RLFFBQVEsQ0FBQyxFQUFFLGNBQWpCUixzQ0FBQUEsZ0JBQW1CNkUsWUFBWSxJQUM1QzdFLEtBQUtRLFFBQVEsQ0FBQyxFQUFFLENBQUNxRSxZQUFZLENBQUNnQyxJQUFJLEtBQ2xDO2dCQUNKWSxhQUFhekgsQ0FBQUEsaUJBQUFBLDRCQUFBQSxtQkFBQUEsS0FBTVEsUUFBUSxDQUFDLEVBQUUsY0FBakJSLHVDQUFBQSxpQkFBbUI2RSxZQUFZLElBQ3hDN0UsS0FBS1EsUUFBUSxDQUFDLEVBQUUsQ0FBQ3FFLFlBQVksQ0FBQ2dDLElBQUksS0FDbEM7Z0JBQ0osK0JBQStCN0csS0FBS3dELFlBQVk7Z0JBQ2hEeUUsTUFBTUY7Z0JBQ04sa0JBQWtCLEVBQUUvSCxpQkFBQUEsMkJBQUFBLElBQU0sQ0FBQyxhQUFhO2dCQUN4QyxpQ0FBaUMsRUFBRUEsaUJBQUFBLDJCQUFBQSxLQUFNa0ksaUJBQWlCO2dCQUMxRCxxQkFBcUI7Z0JBQ3JCQyxRQUFRO1lBQ1Y7O0lBQ0Y7SUFFQSxNQUFNNUMsNkJBQTZCLE9BQU94RTtZQVFOZixnQ0FrQkRBO1FBekJqQyxNQUFNTCxVQUFVbkIscURBQVcsQ0FBQztRQUU1QiwwQkFBMEI7UUFDMUIsSUFBSTZKLGFBQWE3SSxPQUFPUSxJQUFJLENBQUNXLE1BQU0sQ0FBQ0MsR0FBRyxDQUFDLENBQUNDO1lBQ3ZDLE9BQU9BLElBQUlkLE9BQU87UUFDcEI7UUFFQSxNQUFNdUksNEJBQTRCdEksaUJBQUFBLDRCQUFBQSxpQ0FBQUEsS0FBTXVJLHdCQUF3QixjQUE5QnZJLHFEQUFBQSwrQkFBZ0NZLEdBQUcsQ0FDbkUsQ0FBQzRILE1BQVM7Z0JBQ1JDLGlCQUFpQixFQUFFRCxnQkFBQUEsMEJBQUFBLElBQUtFLElBQUk7Z0JBQzVCQyxlQUFlSCxJQUFJSSxTQUFTLEdBQUcsU0FBUztnQkFDeENDLFVBQVVMLElBQUlJLFNBQVM7Z0JBQ3ZCRSxTQUFTTixJQUFJTSxPQUFPO1lBQ3RCO1FBRUYsSUFBSUMsNkJBQTZCLEVBQUU7UUFDbkMsSUFBSUMsd0JBQXdCLEVBQUU7UUFDOUIsOERBQThEO1FBRTlELElBQ0VoSSxvQkFBb0IsZUFDcEJBLG9CQUFvQixjQUNuQnhCLE9BQU9RLElBQUksQ0FBQ0Msa0JBQWtCLElBQzdCVCxPQUFPUSxJQUFJLENBQUNpQixNQUFNLElBQUksZUFDdEJ6QixPQUFPUSxJQUFJLENBQUN3RSxPQUFPLEtBQUssY0FDdkJ4RSxDQUFBQSxDQUFBQSxpQkFBQUEsMkJBQUFBLEtBQU0rRSxPQUFPLENBQUNDLFFBQVEsQ0FBQyxRQUFLaEYsaUJBQUFBLDRCQUFBQSxnQkFBQUEsS0FBTStFLE9BQU8sY0FBYi9FLG9DQUFBQSxjQUFlZ0YsUUFBUSxDQUFDLEdBQUMsS0FDdER4RixPQUFPUSxJQUFJLENBQUNpSixXQUFXLEVBQ3pCO2dCQTZvQkV6SjtZQTVvQkYsSUFBSSxJQUFJLEVBQUU7b0JBMkJKUSxnQkFBOEJBLGdCQWNoQ0EsZ0JBb0NNQSw4QkFBQUEsaUJBZ0hJc0ksNkJBMkJXdEksK0JBQUFBLGtCQW1CZkEsa0JBMkJBQSxrQkFFSUEsa0JBNENXQSwrQkFBQUEsa0JBb0JBQSxrQkFDYkEsK0JBQUFBLGtCQWVpQkEsa0JBQ2pCQSwrQkFBQUEsa0JBUWlCQSxtQkFDakJBLCtCQUFBQSxtQkFtQlVBLG1CQUNWQSwrQkFBQUE7Z0JBclhWLElBQUlrSixZQUFZO2dCQUNoQixJQUFJQyxpQkFBaUI7Z0JBQ3JCLElBQUlDLGVBQWU7Z0JBQ25CLElBQUlDLE9BQU87Z0JBRVgsTUFBTUMsZ0JBQ0o7Z0JBRUYsSUFBSUEsY0FBY2hFLElBQUksQ0FBQ3RGLGlCQUFBQSwyQkFBQUEsS0FBTXVKLGlCQUFpQixNQUFLdkosaUJBQUFBLDJCQUFBQSxLQUFNd0osUUFBUSxHQUFFO3dCQUVoRHhKO29CQURqQmtKLFlBQVk7b0JBQ1pDLGlCQUFpQm5KLGlCQUFBQSw0QkFBQUEsK0JBQUFBLEtBQU15SixzQkFBc0IsY0FBNUJ6SixtREFBQUEsNkJBQThCMEosS0FBSyxDQUFDLENBQUM7b0JBQ3RETixlQUFlcEosaUJBQUFBLDJCQUFBQSxLQUFNdUosaUJBQWlCO29CQUN0Q0YsT0FBT3JKLGlCQUFBQSwyQkFBQUEsS0FBTXlKLHNCQUFzQjtnQkFDckMsT0FBTyxJQUNMLEVBQUN6SixpQkFBQUEsMkJBQUFBLEtBQU13SixRQUFRLEtBQ2ZGLGNBQWNoRSxJQUFJLENBQUN0RixpQkFBQUEsMkJBQUFBLEtBQU11SixpQkFBaUIsR0FDMUM7b0JBQ0FMLFlBQVk7b0JBQ1pDLGlCQUFpQm5KLGlCQUFBQSwyQkFBQUEsS0FBTXlKLHNCQUFzQjtvQkFDN0NMLGVBQWVwSixpQkFBQUEsMkJBQUFBLEtBQU11SixpQkFBaUI7Z0JBQ3hDLE9BQU87b0JBQ0xMLFlBQVlsSixpQkFBQUEsMkJBQUFBLEtBQU11SixpQkFBaUI7b0JBQ25DSixpQkFBaUJuSixpQkFBQUEsMkJBQUFBLEtBQU15SixzQkFBc0I7Z0JBQy9DO2dCQUVBLElBQUlFLGdCQUFnQjtnQkFDcEIsSUFBSTNKLENBQUFBLGlCQUFBQSw0QkFBQUEsaUJBQUFBLEtBQU0rRSxPQUFPLGNBQWIvRSxxQ0FBQUEsZUFBZWdGLFFBQVEsQ0FBQyxRQUFNaEYsaUJBQUFBLDRCQUFBQSxpQkFBQUEsS0FBTStFLE9BQU8sY0FBYi9FLHFDQUFBQSxlQUFlZ0YsUUFBUSxDQUFDLEtBQUk7b0JBQzVELElBQUloRixDQUFBQSxpQkFBQUEsMkJBQUFBLEtBQU00SixZQUFZLEtBQUksTUFBTTt3QkFDOUJELGdCQUFnQjNKLGlCQUFBQSwyQkFBQUEsS0FBTTZKLFdBQVc7b0JBQ25DLE9BQU8sSUFBSTdKLENBQUFBLGlCQUFBQSwyQkFBQUEsS0FBTTRKLFlBQVksS0FBSSxNQUFNO3dCQUNyQ0QsZ0JBQWdCM0osaUJBQUFBLDJCQUFBQSxLQUFNOEosUUFBUTtvQkFDaEMsT0FBTyxJQUFJOUosQ0FBQUEsaUJBQUFBLDJCQUFBQSxLQUFNNEosWUFBWSxLQUFJLE1BQU07d0JBQ3JDRCxnQkFBZ0IzSixpQkFBQUEsMkJBQUFBLEtBQU0rSix3QkFBd0I7b0JBQ2hEO2dCQUNGO2dCQUNBLElBQUlDLGFBQWE7Z0JBQ2pCLElBQUlDLGVBQWU7Z0JBQ25CLElBQ0VqSyxDQUFBQSxpQkFBQUEsMkJBQUFBLEtBQU0rRSxPQUFPLENBQUNDLFFBQVEsQ0FBQyxRQUN2QmhGLGlCQUFBQSwyQkFBQUEsS0FBTStFLE9BQU8sQ0FBQ0MsUUFBUSxDQUFDLFFBQ3ZCaEYsaUJBQUFBLDRCQUFBQSxpQkFBQUEsS0FBTStFLE9BQU8sY0FBYi9FLHFDQUFBQSxlQUFlZ0YsUUFBUSxDQUFDLEtBQ3hCO29CQUNBZ0YsYUFBYWhLLENBQUFBLGlCQUFBQSwyQkFBQUEsS0FBTWlKLFdBQVcsS0FBSTtvQkFDbENnQixlQUFlakssQ0FBQUEsaUJBQUFBLDJCQUFBQSxLQUFNa0ssYUFBYSxLQUFJO2dCQUN4QyxPQUFPO29CQUNMRixhQUFhO29CQUNiQyxlQUFlO2dCQUNqQjtnQkFDQSxTQUFTRSwyQkFBMkJDLElBQUk7b0JBQ3RDLElBQUlBLFFBQVEsT0FBTzt3QkFDakIsT0FBTztvQkFDVCxPQUFPLElBQUlBLFFBQVEsT0FBTzt3QkFDeEIsT0FBTztvQkFDVCxPQUFPLElBQUlBLFFBQVEsT0FBTzt3QkFDeEIsT0FBTztvQkFDVCxPQUFPO3dCQUNMLE9BQU87b0JBQ1Q7Z0JBQ0Y7Z0JBQ0FuRCxRQUFRb0QsR0FBRyxDQUFDLGlCQUFpQnJLLGlCQUFBQSwyQkFBQUEsS0FBTXNLLGFBQWE7b0JBME94QnRLO2dCQXhPeEIrSSw2QkFBNkI7b0JBQzNCO3dCQUNFO3dCQUNBOzRCQUNFLHNCQUFzQjs0QkFDdEJ0QixhQUNFYSxDQUFBQSxzQ0FBQUEsZ0RBQUFBLDBCQUEyQjdILE1BQU0sSUFBRyxLQUNuQzZILENBQUFBLHlCQUF5QixDQUFDLEVBQUUsQ0FBQ1EsT0FBTyxLQUFLLFFBQ3hDUix5QkFBeUIsQ0FBQyxFQUFFLENBQUNRLE9BQU8sS0FBS3lCLFNBQVEsSUFDL0NqQyx5QkFBeUIsQ0FBQyxFQUFFLENBQUNHLGlCQUFpQixHQUM5Qzs0QkFDTix5QkFDRUgsQ0FBQUEsc0NBQUFBLGdEQUFBQSwwQkFBMkI3SCxNQUFNLElBQUcsS0FDbkM2SCxDQUFBQSx5QkFBeUIsQ0FBQyxFQUFFLENBQUNRLE9BQU8sS0FBSyxRQUN4Q1IseUJBQXlCLENBQUMsRUFBRSxDQUFDUSxPQUFPLEtBQUt5QixTQUFRLE1BQ25EdkssaUJBQUFBLDRCQUFBQSxrQkFBQUEsS0FBTVEsUUFBUSxDQUFDLEVBQUUsY0FBakJSLHVDQUFBQSwrQkFBQUEsZ0JBQW1CNkUsWUFBWSxjQUEvQjdFLG1EQUFBQSw2QkFBaUM2RyxJQUFJOzRCQUN2QyxpQ0FDRXlCLENBQUFBLHNDQUFBQSxnREFBQUEsMEJBQTJCN0gsTUFBTSxJQUFHLEtBQ25DNkgsQ0FBQUEseUJBQXlCLENBQUMsRUFBRSxDQUFDUSxPQUFPLEtBQUssUUFDeENSLHlCQUF5QixDQUFDLEVBQUUsQ0FBQ1EsT0FBTyxLQUFLeUIsU0FBUSxLQUNuRDs0QkFDRixpQ0FDRWpDLENBQUFBLHNDQUFBQSxnREFBQUEsMEJBQTJCN0gsTUFBTSxJQUFHLEtBQ25DNkgsQ0FBQUEseUJBQXlCLENBQUMsRUFBRSxDQUFDUSxPQUFPLEtBQUssUUFDeENSLHlCQUF5QixDQUFDLEVBQUUsQ0FBQ1EsT0FBTyxLQUFLeUIsU0FBUSxLQUNuRDs0QkFDRix3QkFDRWpDLENBQUFBLHNDQUFBQSxnREFBQUEsMEJBQTJCN0gsTUFBTSxJQUFHLEtBQ25DNkgsQ0FBQUEseUJBQXlCLENBQUMsRUFBRSxDQUFDUSxPQUFPLEtBQUssUUFDeENSLHlCQUF5QixDQUFDLEVBQUUsQ0FBQ1EsT0FBTyxLQUFLeUIsU0FBUSxLQUNuRDs0QkFDRixtQkFDRWpDLENBQUFBLHNDQUFBQSxnREFBQUEsMEJBQTJCN0gsTUFBTSxJQUFHLEtBQ25DNkgsQ0FBQUEseUJBQXlCLENBQUMsRUFBRSxDQUFDUSxPQUFPLEtBQUssUUFDeENSLHlCQUF5QixDQUFDLEVBQUUsQ0FBQ1EsT0FBTyxLQUFLeUIsU0FBUSxLQUNuRDs0QkFDRixnQ0FDRWpDLENBQUFBLHNDQUFBQSxnREFBQUEsMEJBQTJCN0gsTUFBTSxJQUFHLEtBQ25DNkgsQ0FBQUEseUJBQXlCLENBQUMsRUFBRSxDQUFDUSxPQUFPLEtBQUssUUFDeENSLHlCQUF5QixDQUFDLEVBQUUsQ0FBQ1EsT0FBTyxLQUFLeUIsU0FBUSxLQUNuRDs0QkFDRixpQ0FDRWpDLENBQUFBLHNDQUFBQSxnREFBQUEsMEJBQTJCN0gsTUFBTSxJQUFHLEtBQ25DNkgsQ0FBQUEseUJBQXlCLENBQUMsRUFBRSxDQUFDUSxPQUFPLEtBQUssUUFDeENSLHlCQUF5QixDQUFDLEVBQUUsQ0FBQ1EsT0FBTyxLQUFLeUIsU0FBUSxLQUNuRDs0QkFDRix5QkFDRWpDLENBQUFBLHNDQUFBQSxnREFBQUEsMEJBQTJCN0gsTUFBTSxJQUFHLEtBQ25DNkgsQ0FBQUEseUJBQXlCLENBQUMsRUFBRSxDQUFDUSxPQUFPLEtBQUssUUFDeENSLHlCQUF5QixDQUFDLEVBQUUsQ0FBQ1EsT0FBTyxLQUFLeUIsU0FBUSxLQUNuRDs0QkFDRiw0QkFDRWpDLENBQUFBLHNDQUFBQSxnREFBQUEsMEJBQTJCN0gsTUFBTSxJQUFHLEtBQ25DNkgsQ0FBQUEseUJBQXlCLENBQUMsRUFBRSxDQUFDUSxPQUFPLEtBQUssUUFDeENSLHlCQUF5QixDQUFDLEVBQUUsQ0FBQ1EsT0FBTyxLQUFLeUIsU0FBUSxLQUNuRDs0QkFDRix3QkFDRWpDLENBQUFBLHNDQUFBQSxnREFBQUEsMEJBQTJCN0gsTUFBTSxJQUFHLEtBQ25DNkgsQ0FBQUEseUJBQXlCLENBQUMsRUFBRSxDQUFDUSxPQUFPLEtBQUssUUFDeENSLHlCQUF5QixDQUFDLEVBQUUsQ0FBQ1EsT0FBTyxLQUFLeUIsU0FBUSxLQUNuRDs0QkFDRiw2QkFDRWpDLENBQUFBLHNDQUFBQSxnREFBQUEsMEJBQTJCN0gsTUFBTSxJQUFHLEtBQ25DNkgsQ0FBQUEseUJBQXlCLENBQUMsRUFBRSxDQUFDUSxPQUFPLEtBQUssUUFDeENSLHlCQUF5QixDQUFDLEVBQUUsQ0FBQ1EsT0FBTyxLQUFLeUIsU0FBUSxLQUNuRDs0QkFDRixnQ0FDRWpDLENBQUFBLHNDQUFBQSxnREFBQUEsMEJBQTJCN0gsTUFBTSxJQUFHLEtBQ25DNkgsQ0FBQUEseUJBQXlCLENBQUMsRUFBRSxDQUFDUSxPQUFPLEtBQUssUUFDeENSLHlCQUF5QixDQUFDLEVBQUUsQ0FBQ1EsT0FBTyxLQUFLeUIsU0FBUSxLQUNuRDs0QkFDRiwrQkFDRWpDLENBQUFBLHNDQUFBQSxnREFBQUEsMEJBQTJCN0gsTUFBTSxJQUFHLEtBQ25DNkgsQ0FBQUEseUJBQXlCLENBQUMsRUFBRSxDQUFDUSxPQUFPLEtBQUssUUFDeENSLHlCQUF5QixDQUFDLEVBQUUsQ0FBQ1EsT0FBTyxLQUFLeUIsU0FBUSxLQUNuRDs0QkFDRkMsU0FBUzs0QkFDVCx5QkFBeUI7NEJBQ3pCLHNCQUFzQjs0QkFDdEIsZ0JBQWdCOzRCQUNoQixrQkFDRWxDLENBQUFBLHNDQUFBQSxnREFBQUEsMEJBQTJCN0gsTUFBTSxJQUFHLEtBQ25DNkgsQ0FBQUEseUJBQXlCLENBQUMsRUFBRSxDQUFDUSxPQUFPLEtBQUssUUFDeENSLHlCQUF5QixDQUFDLEVBQUUsQ0FBQ1EsT0FBTyxLQUFLeUIsU0FBUSxLQUNuRDs0QkFDRiwyQkFBMkI7NEJBQzNCLDZCQUNFakMsQ0FBQUEsc0NBQUFBLGdEQUFBQSwwQkFBMkI3SCxNQUFNLElBQUcsS0FDbkM2SCxDQUFBQSx5QkFBeUIsQ0FBQyxFQUFFLENBQUNRLE9BQU8sS0FBSyxRQUN4Q1IseUJBQXlCLENBQUMsRUFBRSxDQUFDUSxPQUFPLEtBQUt5QixTQUFRLEtBQ25EOzRCQUNGLGdDQUNFakMsQ0FBQUEsc0NBQUFBLGdEQUFBQSwwQkFBMkI3SCxNQUFNLElBQUcsS0FDbkM2SCxDQUFBQSx5QkFBeUIsQ0FBQyxFQUFFLENBQUNRLE9BQU8sS0FBSyxRQUN4Q1IseUJBQXlCLENBQUMsRUFBRSxDQUFDUSxPQUFPLEtBQUt5QixTQUFRLEtBQ25EOzRCQUNGLG9CQUFvQjs0QkFDcEIsa0NBQWtDOzRCQUNsQyw0QkFBNEI7NEJBQzVCLGlDQUFpQzs0QkFDakMsMEJBQ0VqQyxDQUFBQSxzQ0FBQUEsZ0RBQUFBLDBCQUEyQjdILE1BQU0sSUFBRyxLQUNuQzZILENBQUFBLHlCQUF5QixDQUFDLEVBQUUsQ0FBQ1EsT0FBTyxLQUFLLFFBQ3hDUix5QkFBeUIsQ0FBQyxFQUFFLENBQUNRLE9BQU8sS0FBS3lCLFNBQVEsS0FDbkQ7NEJBQ0YsaUJBQWlCOzRCQUNqQixvQkFBb0I7NEJBQ3BCRSxNQUFNOzRCQUNOQyxTQUFTOzRCQUNUdkMsUUFDRUcsQ0FBQUEsc0NBQUFBLGdEQUFBQSwwQkFBMkI3SCxNQUFNLElBQUcsS0FDbkM2SCxDQUFBQSx5QkFBeUIsQ0FBQyxFQUFFLENBQUNRLE9BQU8sS0FBSyxRQUN4Q1IseUJBQXlCLENBQUMsRUFBRSxDQUFDUSxPQUFPLEtBQUt5QixTQUFRLEtBQ25EOzRCQUNGLGlDQUFpQzs0QkFDakMsY0FDRWpDLENBQUFBLHNDQUFBQSxnREFBQUEsMEJBQTJCN0gsTUFBTSxJQUFHLEtBQ25DNkgsQ0FBQUEseUJBQXlCLENBQUMsRUFBRSxDQUFDUSxPQUFPLEtBQUssUUFDeENSLHlCQUF5QixDQUFDLEVBQUUsQ0FBQ1EsT0FBTyxLQUFLeUIsU0FBUSxLQUNuRDs0QkFDRixvQkFBb0I7NEJBQ3BCLHVCQUF1Qjs0QkFDdkIsaUJBQWlCOzRCQUNqQixhQUNFakMsQ0FBQUEsc0NBQUFBLGdEQUFBQSwwQkFBMkI3SCxNQUFNLElBQUcsS0FDbkM2SCxDQUFBQSx5QkFBeUIsQ0FBQyxFQUFFLENBQUNRLE9BQU8sS0FBSyxRQUN4Q1IseUJBQXlCLENBQUMsRUFBRSxDQUFDUSxPQUFPLEtBQUt5QixTQUFRLElBQy9DakMsRUFBQUEsOEJBQUFBLHlCQUF5QixDQUFDLEVBQUUsY0FBNUJBLGtEQUFBQSw0QkFBOEJPLFFBQVEsSUFDcEMsSUFDQSxNQUNGOzRCQUNOLGtCQUNFUCxDQUFBQSxzQ0FBQUEsZ0RBQUFBLDBCQUEyQjdILE1BQU0sSUFBRyxLQUNuQzZILENBQUFBLHlCQUF5QixDQUFDLEVBQUUsQ0FBQ1EsT0FBTyxLQUFLLFFBQ3hDUix5QkFBeUIsQ0FBQyxFQUFFLENBQUNRLE9BQU8sS0FBS3lCLFNBQVEsS0FDbkQ7d0JBQ0o7cUJBQ0Q7b0JBQ0Q7d0JBQ0U7d0JBQ0E7NEJBQ0UsbUJBQW1CdkssQ0FBQUEsaUJBQUFBLDJCQUFBQSxLQUFNMkssUUFBUSxJQUFHLElBQUk7NEJBQ3hDLHNCQUFzQjs0QkFDdEIscUJBQXFCOzRCQUNyQiw0QkFBNEI7NEJBQzVCLG1CQUFtQjs0QkFDbkIsY0FBYyxFQUFFM0ssaUJBQUFBLDJCQUFBQSxLQUFNNEssVUFBVTs0QkFDaEMsY0FBYyxFQUFFNUssaUJBQUFBLDJCQUFBQSxLQUFNNkssMEJBQTBCOzRCQUNoRCxpQkFBaUJsQjs0QkFDakIsbUJBQW1CLEVBQUUzSixpQkFBQUEsMkJBQUFBLEtBQU1rSSxpQkFBaUI7NEJBQzVDLHNCQUFzQjs0QkFDdEIsZ0JBQWdCbEksQ0FBQUEsaUJBQUFBLDJCQUFBQSxLQUFNOEssY0FBYyxJQUNoQzdILEtBQUt3RCxLQUFLLENBQUN6RyxLQUFLOEssY0FBYyxDQUFDLENBQUMsRUFBRSxDQUFDQyxLQUFLLEdBQ3hDOzRCQUNKLGVBQWUsRUFBRS9LLGlCQUFBQSw0QkFBQUEsbUJBQUFBLEtBQU1RLFFBQVEsQ0FBQyxFQUFFLGNBQWpCUix3Q0FBQUEsZ0NBQUFBLGlCQUFtQjZFLFlBQVksY0FBL0I3RSxvREFBQUEsOEJBQWlDNkcsSUFBSTs0QkFDdEQsaUJBQWlCN0csS0FBS3dELFlBQVk7NEJBQ2xDLGVBQWUsRUFBRXhELGlCQUFBQSwyQkFBQUEsS0FBTXNLLGFBQWE7NEJBQ3BDLGlCQUFpQjs0QkFDakIsZ0JBQWdCLEVBQUV0SyxpQkFBQUEsMkJBQUFBLEtBQU1nTCxjQUFjOzRCQUN0QyxnQkFBZ0IsRUFBRWhMLGlCQUFBQSwyQkFBQUEsS0FBTWlMLGNBQWM7NEJBQ3RDLGdCQUFnQixFQUFFakwsaUJBQUFBLDJCQUFBQSxLQUFNa0wsY0FBYzs0QkFDdEMsZ0JBQWdCLEVBQUVsTCxpQkFBQUEsMkJBQUFBLEtBQU1tTCxjQUFjOzRCQUN0QyxXQUFXLEVBQUVuTCxpQkFBQUEsMkJBQUFBLEtBQU1vTCxXQUFXOzRCQUM5QixjQUFjLEVBQUVwTCxpQkFBQUEsMkJBQUFBLEtBQU00SixZQUFZOzRCQUNsQyx1QkFBdUI7NEJBQ3ZCLG9CQUFvQjs0QkFDcEIsZUFBZTs0QkFDZixpQkFBaUIsRUFBRTVKLGlCQUFBQSwyQkFBQUEsS0FBTXFMLGFBQWE7NEJBQ3RDLG9EQUFvRHJCOzRCQUNwRCxtQkFBbUJBOzRCQUNuQixpQ0FBaUNDOzRCQUNqQyw0QkFBNEI5RCxVQUMxQm5HLGlCQUFBQSwyQkFBQUEsS0FBTW9HLG9CQUFvQixFQUMxQnBHLGlCQUFBQSw0QkFBQUEsbUJBQUFBLEtBQU1RLFFBQVEsQ0FBQyxFQUFFLGNBQWpCUix1Q0FBQUEsaUJBQW1CTyxVQUFVLEVBQzdCUCxpQkFBQUEsMkJBQUFBLEtBQU1zTCxhQUFhLEVBQ25CdEwsaUJBQUFBLDJCQUFBQSxLQUFNK0UsT0FBTzs0QkFFZixjQUFjLEVBQUUvRSxpQkFBQUEsMkJBQUFBLEtBQU11TCxZQUFZOzRCQUNsQyxtQkFBbUIsRUFBRXZMLGlCQUFBQSwyQkFBQUEsS0FBTXdMLGlCQUFpQjs0QkFDNUMsY0FBYyxFQUFFeEwsaUJBQUFBLDJCQUFBQSxLQUFNNEosWUFBWTs0QkFDbEM2QixTQUNFekwsQ0FBQUEsaUJBQUFBLDJCQUFBQSxLQUFNMEwsT0FBTyxLQUFJLE9BQVExTCxDQUFBQSxpQkFBQUEsMkJBQUFBLEtBQU0wTCxPQUFPLElBQUcsTUFBTSxNQUFPOzRCQUN4RCxnQkFDRTFMLENBQUFBLGlCQUFBQSwyQkFBQUEsS0FBTTBMLE9BQU8sS0FBSSxPQUNiMUwsQ0FBQUEsaUJBQUFBLDJCQUFBQSxLQUFNMEwsT0FBTyxJQUNYLFlBQ0EsaUJBQ0Y7NEJBQ04sZUFDRTFMLENBQUFBLGlCQUFBQSwyQkFBQUEsS0FBTVEsUUFBUSxDQUFDLEVBQUUsQ0FBQ0QsVUFBVSxLQUFJLEtBQ2hDUCxDQUFBQSxpQkFBQUEsMkJBQUFBLEtBQU1RLFFBQVEsQ0FBQyxFQUFFLENBQUNELFVBQVUsS0FBSSxJQUM1QixJQUNBOzRCQUNOb0wsT0FBTzs0QkFDUCwwQkFBMEI7NEJBQzFCLCtCQUErQjs0QkFDL0IsZ0NBQWdDOzRCQUNoQyxtQ0FBbUM7NEJBQ25DLGlCQUFpQjs0QkFDakIscUJBQ0UzTCxDQUFBQSxpQkFBQUEsNEJBQUFBLG1CQUFBQSxLQUFNUSxRQUFRLENBQUMsRUFBRSxjQUFqQlIsdUNBQUFBLGlCQUFtQk8sVUFBVSxLQUFJLElBQzdCLElBQ0FQLENBQUFBLGlCQUFBQSw0QkFBQUEsbUJBQUFBLEtBQU1RLFFBQVEsQ0FBQyxFQUFFLGNBQWpCUix1Q0FBQUEsaUJBQW1CTyxVQUFVLEtBQUksSUFDakMsSUFDQVAsQ0FBQUEsaUJBQUFBLDJCQUFBQSxLQUFNUSxRQUFRLENBQUMsRUFBRSxDQUFDRCxVQUFVLEtBQUksSUFDaEMsSUFDQVAsQ0FBQUEsaUJBQUFBLDJCQUFBQSxLQUFNUSxRQUFRLENBQUMsRUFBRSxDQUFDRCxVQUFVLEtBQUksSUFDaEMsSUFDQTs0QkFDTix3QkFBd0I7NEJBQ3hCLCtCQUErQjs0QkFDL0IseUNBQXlDOzRCQUN6Qyx5QkFBeUI7NEJBQ3pCLGVBQWU7NEJBQ2YsaUJBQ0VQLENBQUFBLGlCQUFBQSwyQkFBQUEsS0FBTVEsUUFBUSxDQUFDLEVBQUUsQ0FBQ0QsVUFBVSxLQUFJLEtBQ2hDUCxDQUFBQSxpQkFBQUEsMkJBQUFBLEtBQU1RLFFBQVEsQ0FBQyxFQUFFLENBQUNELFVBQVUsS0FBSSxJQUM1QjRKLDJCQUEyQm5LLGlCQUFBQSwyQkFBQUEsS0FBTXNMLGFBQWEsSUFDOUM7NEJBQ04sMkJBQTJCOzRCQUMzQiwwQkFBMEI7NEJBQzFCLHdCQUF3Qjs0QkFDeEIsZUFDRXRMLENBQUFBLGlCQUFBQSwyQkFBQUEsS0FBTVEsUUFBUSxDQUFDLEVBQUUsQ0FBQ0QsVUFBVSxLQUFJLEtBQ2hDUCxDQUFBQSxpQkFBQUEsMkJBQUFBLEtBQU1RLFFBQVEsQ0FBQyxFQUFFLENBQUNELFVBQVUsS0FBSSxJQUM1QixPQUNBOzRCQUNOLGdCQUNFUCxDQUFBQSxpQkFBQUEsMkJBQUFBLEtBQU1RLFFBQVEsQ0FBQyxFQUFFLENBQUNELFVBQVUsS0FBSSxLQUNoQ1AsQ0FBQUEsaUJBQUFBLDJCQUFBQSxLQUFNUSxRQUFRLENBQUMsRUFBRSxDQUFDRCxVQUFVLEtBQUksSUFDNUIsVUFDQTs0QkFDTixrQkFBa0JQLENBQUFBLFlBQUFBLGlCQUFBQSwyQkFBQUEsS0FBTTRMLEdBQUcsY0FBVDVMLHVCQUFBQSxZQUFhOzRCQUMvQix5QkFBeUI7NEJBQ3pCNkwsUUFDRXZELENBQUFBLHNDQUFBQSxnREFBQUEsMEJBQTJCN0gsTUFBTSxJQUFHLElBQ2hDNkgseUJBQXlCLENBQUMsRUFBRSxDQUFDUSxPQUFPLElBQUksS0FDeEM7d0JBQ1I7cUJBQ0Q7b0JBQ0Q7d0JBQUM7cUJBQWtDO29CQUVuQzt3QkFBQztxQkFBa0M7b0JBQ25DO3dCQUNFO3dCQUNBOzRCQUNFLGVBQWUsRUFBRTlJLGlCQUFBQSw0QkFBQUEsbUJBQUFBLEtBQU1RLFFBQVEsQ0FBQyxFQUFFLGNBQWpCUix3Q0FBQUEsZ0NBQUFBLGlCQUFtQjZFLFlBQVksY0FBL0I3RSxvREFBQUEsOEJBQWlDNkcsSUFBSTs0QkFDdEQsYUFBYTs0QkFDYixrQkFBa0JxQzs0QkFDbEIsa0JBQWtCQzs0QkFDbEIsZ0JBQ0VuSixDQUFBQSxpQkFBQUEsMkJBQUFBLEtBQU00SixZQUFZLEtBQUksT0FBTyxPQUFPNUosaUJBQUFBLDJCQUFBQSxLQUFNNEosWUFBWTs0QkFDeEQsa0JBQWtCNUosS0FBS3dELFlBQVk7NEJBQ25DLG1CQUFtQndHOzRCQUNuQixlQUFlLEVBQUVoSyxpQkFBQUEsMkJBQUFBLEtBQU04TCxrQkFBa0I7NEJBQ3pDLGtCQUFrQjs0QkFDbEIsa0JBQWtCOzRCQUNsQixxQkFBcUIxQzs0QkFDckIsbUNBQW1DQzs0QkFDbkMsbUJBQW1COzRCQUNuQixhQUFhO3dCQUNmO3FCQUNEO29CQUNEO3dCQUNFO3dCQUNBOzRCQUNFLGlCQUFpQnJKLENBQUFBLGlCQUFBQSw0QkFBQUEsbUJBQUFBLEtBQU1RLFFBQVEsQ0FBQyxFQUFFLGNBQWpCUix1Q0FBQUEsaUJBQW1CNkUsWUFBWSxJQUM1QzdFLGlCQUFBQSw0QkFBQUEsbUJBQUFBLEtBQU1RLFFBQVEsQ0FBQyxFQUFFLGNBQWpCUix3Q0FBQUEsZ0NBQUFBLGlCQUFtQjZFLFlBQVksY0FBL0I3RSxvREFBQUEsOEJBQWlDNkcsSUFBSSxLQUNyQzs0QkFDSmtGLFVBQVU7NEJBRVYseUJBQXlCOzRCQUN6QixnQ0FBZ0M7NEJBQ2hDLGdFQUNFO3dCQUNKO3FCQUNEO29CQUNEO3dCQUFDO3FCQUFrRDtvQkFDbkQ7d0JBQ0U7d0JBQ0E7NEJBQ0UsbUJBQW1COzRCQUNuQixxQkFBcUIvTCxDQUFBQSxpQkFBQUEsNEJBQUFBLG1CQUFBQSxLQUFNUSxRQUFRLENBQUMsRUFBRSxjQUFqQlIsdUNBQUFBLGlCQUFtQjZFLFlBQVksSUFDaEQ3RSxpQkFBQUEsNEJBQUFBLG1CQUFBQSxLQUFNUSxRQUFRLENBQUMsRUFBRSxjQUFqQlIsd0NBQUFBLGdDQUFBQSxpQkFBbUI2RSxZQUFZLGNBQS9CN0Usb0RBQUFBLDhCQUFpQzZHLElBQUksS0FDckM7d0JBQ047cUJBQ0Q7b0JBQ0Q7d0JBQ0U7d0JBQ0E7NEJBQ0UsbUJBQW1COzRCQUNuQixxQkFBcUI3RyxDQUFBQSxpQkFBQUEsNEJBQUFBLG9CQUFBQSxLQUFNUSxRQUFRLENBQUMsRUFBRSxjQUFqQlIsd0NBQUFBLGtCQUFtQjZFLFlBQVksSUFDaEQ3RSxpQkFBQUEsNEJBQUFBLG9CQUFBQSxLQUFNUSxRQUFRLENBQUMsRUFBRSxjQUFqQlIseUNBQUFBLGdDQUFBQSxrQkFBbUI2RSxZQUFZLGNBQS9CN0Usb0RBQUFBLDhCQUFpQzZHLElBQUksS0FDckM7NEJBQ0osZ0JBQWdCOzRCQUNoQm1GLFVBQVU7NEJBQ1YsbUJBQW1COzRCQUNuQixnQkFBZ0I7NEJBQ2hCLGNBQWM7NEJBQ2QsNEJBQTRCOzRCQUM1Qix3QkFBd0I7NEJBQ3hCLGtCQUFrQjt3QkFDcEI7cUJBQ0Q7b0JBQ0Q7d0JBQ0U7d0JBQ0E7NEJBQ0VqTCxFQUFFLEVBQUVmLGlCQUFBQSwyQkFBQUEsS0FBTWUsRUFBRTs0QkFDWmtMLFlBQVksRUFBRTNELHNDQUFBQSxnREFBQUEsMEJBQTJCOUUsWUFBWTs0QkFDckQwSSxnQ0FBZ0MsRUFDOUJsTSxpQkFBQUEsMkJBQUFBLEtBQU1rTSxnQ0FBZ0M7NEJBQ3hDQyxjQUFjbk0sQ0FBQUEsaUJBQUFBLDRCQUFBQSxvQkFBQUEsS0FBTVEsUUFBUSxDQUFDLEVBQUUsY0FBakJSLHdDQUFBQSxrQkFBbUI2RSxZQUFZLElBQ3pDN0UsaUJBQUFBLDRCQUFBQSxvQkFBQUEsS0FBTVEsUUFBUSxDQUFDLEVBQUUsY0FBakJSLHlDQUFBQSxnQ0FBQUEsa0JBQW1CNkUsWUFBWSxjQUEvQjdFLG9EQUFBQSw4QkFBaUM2RyxJQUFJLEtBQ3JDO3dCQUNOO3FCQUNEO2lCQUNGO2dCQUNELE1BQU11RixpQkFBaUIsQ0FBQ0MsWUFBWUM7b0JBQ2xDLElBQUl2RCwwQkFBMEIsQ0FBQ3NELFdBQVcsQ0FBQzVMLE1BQU0sS0FBSyxHQUFHO3dCQUN2RHNJLDBCQUEwQixDQUFDc0QsV0FBVyxDQUFDNUcsSUFBSSxJQUFJNkc7b0JBQ2pELE9BQU87d0JBQ0x2RCwwQkFBMEIsQ0FBQ3NELFdBQVcsR0FBRzs0QkFDdkN0RCwwQkFBMEIsQ0FBQ3NELFdBQVcsQ0FBQyxFQUFFOytCQUN0Q0M7eUJBQ0o7b0JBQ0g7Z0JBQ0Y7Z0JBRUEsaURBQWlEO2dCQUNqRCxNQUFNOUYsV0FBV0YsZ0JBQWdCdEcsaUJBQUFBLDJCQUFBQSxLQUFNdU0sYUFBYTtnQkFDcEQsTUFBTUMsdUJBQXVCdEYsbUJBQzNCbEgsS0FBSzhLLGNBQWMsRUFDbkI5SyxpQkFBQUEsMkJBQUFBLEtBQU1lLEVBQUU7Z0JBRVYsSUFBSTBMLHVCQUF1QjlFLDJCQUEyQjNIO2dCQUN0RG9NLGVBQWUsR0FBRzVGO2dCQUNsQjRGLGVBQWUsR0FBR0s7Z0JBQ2xCTCxlQUFlLEdBQUdJO1lBQ3BCO1lBQ0EsSUFBSUU7WUFDSixJQUNFckUsV0FBV3JELFFBQVEsQ0FBQyxNQUNwQnFELFdBQVdyRCxRQUFRLENBQUMsTUFDcEJxRCxXQUFXckQsUUFBUSxDQUFDLE1BQ3BCcUQsV0FBV3JELFFBQVEsQ0FBQyxJQUNwQjtvQkFpQlFoRiwrQkFBQUEsbUJBZ0hJc0ksOEJBZVd0SSwrQkFBQUEsbUJBY0ZBLCtCQUFBQSxtQkFHYkEsaUNBa0JBQSxtQkFFSUEsbUJBa0RWUjtnQkF0T0YsSUFBSWlOLHVCQUF1QjlFLDJCQUEyQjNILGlCQUFBQSwyQkFBQUEsS0FBTTZILFFBQVE7b0JBa0o1QzdIO2dCQWpKeEJnSix3QkFBd0I7b0JBQ3RCO3dCQUNFO3dCQUNBOzRCQUNFLHNCQUFzQjs0QkFDdEJ2QixhQUNFYSxDQUFBQSxzQ0FBQUEsZ0RBQUFBLDBCQUEyQjdILE1BQU0sSUFBRyxLQUNuQzZILENBQUFBLHlCQUF5QixDQUFDLEVBQUUsQ0FBQ1EsT0FBTyxLQUFLLFFBQ3hDUix5QkFBeUIsQ0FBQyxFQUFFLENBQUNRLE9BQU8sS0FBS3lCLFNBQVEsSUFDL0NqQyx5QkFBeUIsQ0FBQyxFQUFFLENBQUNHLGlCQUFpQixHQUM5Qzs0QkFDTix5QkFDRUgsQ0FBQUEsc0NBQUFBLGdEQUFBQSwwQkFBMkI3SCxNQUFNLElBQUcsS0FDbkM2SCxDQUFBQSx5QkFBeUIsQ0FBQyxFQUFFLENBQUNRLE9BQU8sS0FBSyxRQUN4Q1IseUJBQXlCLENBQUMsRUFBRSxDQUFDUSxPQUFPLEtBQUt5QixTQUFRLE1BQ25EdkssaUJBQUFBLDRCQUFBQSxvQkFBQUEsS0FBTVEsUUFBUSxDQUFDLEVBQUUsY0FBakJSLHlDQUFBQSxnQ0FBQUEsa0JBQW1CNkUsWUFBWSxjQUEvQjdFLG9EQUFBQSw4QkFBaUM2RyxJQUFJOzRCQUN2QyxpQ0FDRXlCLENBQUFBLHNDQUFBQSxnREFBQUEsMEJBQTJCN0gsTUFBTSxJQUFHLEtBQ25DNkgsQ0FBQUEseUJBQXlCLENBQUMsRUFBRSxDQUFDUSxPQUFPLEtBQUssUUFDeENSLHlCQUF5QixDQUFDLEVBQUUsQ0FBQ1EsT0FBTyxLQUFLeUIsU0FBUSxLQUNuRDs0QkFDRixpQ0FDRWpDLENBQUFBLHNDQUFBQSxnREFBQUEsMEJBQTJCN0gsTUFBTSxJQUFHLEtBQ25DNkgsQ0FBQUEseUJBQXlCLENBQUMsRUFBRSxDQUFDUSxPQUFPLEtBQUssUUFDeENSLHlCQUF5QixDQUFDLEVBQUUsQ0FBQ1EsT0FBTyxLQUFLeUIsU0FBUSxLQUNuRDs0QkFDRix3QkFDRWpDLENBQUFBLHNDQUFBQSxnREFBQUEsMEJBQTJCN0gsTUFBTSxJQUFHLEtBQ25DNkgsQ0FBQUEseUJBQXlCLENBQUMsRUFBRSxDQUFDUSxPQUFPLEtBQUssUUFDeENSLHlCQUF5QixDQUFDLEVBQUUsQ0FBQ1EsT0FBTyxLQUFLeUIsU0FBUSxLQUNuRDs0QkFDRixtQkFDRWpDLENBQUFBLHNDQUFBQSxnREFBQUEsMEJBQTJCN0gsTUFBTSxJQUFHLEtBQ25DNkgsQ0FBQUEseUJBQXlCLENBQUMsRUFBRSxDQUFDUSxPQUFPLEtBQUssUUFDeENSLHlCQUF5QixDQUFDLEVBQUUsQ0FBQ1EsT0FBTyxLQUFLeUIsU0FBUSxLQUNuRDs0QkFDRixnQ0FDRWpDLENBQUFBLHNDQUFBQSxnREFBQUEsMEJBQTJCN0gsTUFBTSxJQUFHLEtBQ25DNkgsQ0FBQUEseUJBQXlCLENBQUMsRUFBRSxDQUFDUSxPQUFPLEtBQUssUUFDeENSLHlCQUF5QixDQUFDLEVBQUUsQ0FBQ1EsT0FBTyxLQUFLeUIsU0FBUSxLQUNuRDs0QkFDRixpQ0FDRWpDLENBQUFBLHNDQUFBQSxnREFBQUEsMEJBQTJCN0gsTUFBTSxJQUFHLEtBQ25DNkgsQ0FBQUEseUJBQXlCLENBQUMsRUFBRSxDQUFDUSxPQUFPLEtBQUssUUFDeENSLHlCQUF5QixDQUFDLEVBQUUsQ0FBQ1EsT0FBTyxLQUFLeUIsU0FBUSxLQUNuRDs0QkFDRix5QkFDRWpDLENBQUFBLHNDQUFBQSxnREFBQUEsMEJBQTJCN0gsTUFBTSxJQUFHLEtBQ25DNkgsQ0FBQUEseUJBQXlCLENBQUMsRUFBRSxDQUFDUSxPQUFPLEtBQUssUUFDeENSLHlCQUF5QixDQUFDLEVBQUUsQ0FBQ1EsT0FBTyxLQUFLeUIsU0FBUSxLQUNuRDs0QkFDRiw0QkFDRWpDLENBQUFBLHNDQUFBQSxnREFBQUEsMEJBQTJCN0gsTUFBTSxJQUFHLEtBQ25DNkgsQ0FBQUEseUJBQXlCLENBQUMsRUFBRSxDQUFDUSxPQUFPLEtBQUssUUFDeENSLHlCQUF5QixDQUFDLEVBQUUsQ0FBQ1EsT0FBTyxLQUFLeUIsU0FBUSxLQUNuRDs0QkFDRix3QkFDRWpDLENBQUFBLHNDQUFBQSxnREFBQUEsMEJBQTJCN0gsTUFBTSxJQUFHLEtBQ25DNkgsQ0FBQUEseUJBQXlCLENBQUMsRUFBRSxDQUFDUSxPQUFPLEtBQUssUUFDeENSLHlCQUF5QixDQUFDLEVBQUUsQ0FBQ1EsT0FBTyxLQUFLeUIsU0FBUSxLQUNuRDs0QkFDRiw2QkFDRWpDLENBQUFBLHNDQUFBQSxnREFBQUEsMEJBQTJCN0gsTUFBTSxJQUFHLEtBQ25DNkgsQ0FBQUEseUJBQXlCLENBQUMsRUFBRSxDQUFDUSxPQUFPLEtBQUssUUFDeENSLHlCQUF5QixDQUFDLEVBQUUsQ0FBQ1EsT0FBTyxLQUFLeUIsU0FBUSxLQUNuRDs0QkFDRixnQ0FDRWpDLENBQUFBLHNDQUFBQSxnREFBQUEsMEJBQTJCN0gsTUFBTSxJQUFHLEtBQ25DNkgsQ0FBQUEseUJBQXlCLENBQUMsRUFBRSxDQUFDUSxPQUFPLEtBQUssUUFDeENSLHlCQUF5QixDQUFDLEVBQUUsQ0FBQ1EsT0FBTyxLQUFLeUIsU0FBUSxLQUNuRDs0QkFDRiwrQkFDRWpDLENBQUFBLHNDQUFBQSxnREFBQUEsMEJBQTJCN0gsTUFBTSxJQUFHLEtBQ25DNkgsQ0FBQUEseUJBQXlCLENBQUMsRUFBRSxDQUFDUSxPQUFPLEtBQUssUUFDeENSLHlCQUF5QixDQUFDLEVBQUUsQ0FBQ1EsT0FBTyxLQUFLeUIsU0FBUSxLQUNuRDs0QkFDRkMsU0FBUzs0QkFDVCx5QkFBeUI7NEJBQ3pCLHNCQUFzQjs0QkFDdEIsZ0JBQWdCOzRCQUNoQixrQkFDRWxDLENBQUFBLHNDQUFBQSxnREFBQUEsMEJBQTJCN0gsTUFBTSxJQUFHLEtBQ25DNkgsQ0FBQUEseUJBQXlCLENBQUMsRUFBRSxDQUFDUSxPQUFPLEtBQUssUUFDeENSLHlCQUF5QixDQUFDLEVBQUUsQ0FBQ1EsT0FBTyxLQUFLeUIsU0FBUSxLQUNuRDs0QkFDRiwyQkFBMkI7NEJBQzNCLDZCQUNFakMsQ0FBQUEsc0NBQUFBLGdEQUFBQSwwQkFBMkI3SCxNQUFNLElBQUcsS0FDbkM2SCxDQUFBQSx5QkFBeUIsQ0FBQyxFQUFFLENBQUNRLE9BQU8sS0FBSyxRQUN4Q1IseUJBQXlCLENBQUMsRUFBRSxDQUFDUSxPQUFPLEtBQUt5QixTQUFRLEtBQ25EOzRCQUNGLGdDQUNFakMsQ0FBQUEsc0NBQUFBLGdEQUFBQSwwQkFBMkI3SCxNQUFNLElBQUcsS0FDbkM2SCxDQUFBQSx5QkFBeUIsQ0FBQyxFQUFFLENBQUNRLE9BQU8sS0FBSyxRQUN4Q1IseUJBQXlCLENBQUMsRUFBRSxDQUFDUSxPQUFPLEtBQUt5QixTQUFRLEtBQ25EOzRCQUNGLG9CQUFvQjs0QkFDcEIsa0NBQWtDOzRCQUNsQyw0QkFBNEI7NEJBQzVCLGlDQUFpQzs0QkFDakMsMEJBQ0VqQyxDQUFBQSxzQ0FBQUEsZ0RBQUFBLDBCQUEyQjdILE1BQU0sSUFBRyxLQUNuQzZILENBQUFBLHlCQUF5QixDQUFDLEVBQUUsQ0FBQ1EsT0FBTyxLQUFLLFFBQ3hDUix5QkFBeUIsQ0FBQyxFQUFFLENBQUNRLE9BQU8sS0FBS3lCLFNBQVEsS0FDbkQ7NEJBQ0YsaUJBQWlCOzRCQUNqQixvQkFBb0I7NEJBQ3BCRSxNQUFNOzRCQUNOQyxTQUFTOzRCQUNUdkMsUUFDRUcsQ0FBQUEsc0NBQUFBLGdEQUFBQSwwQkFBMkI3SCxNQUFNLElBQUcsS0FDbkM2SCxDQUFBQSx5QkFBeUIsQ0FBQyxFQUFFLENBQUNRLE9BQU8sS0FBSyxRQUN4Q1IseUJBQXlCLENBQUMsRUFBRSxDQUFDUSxPQUFPLEtBQUt5QixTQUFRLEtBQ25EOzRCQUNGLGlDQUFpQzs0QkFDakMsY0FDRWpDLENBQUFBLHNDQUFBQSxnREFBQUEsMEJBQTJCN0gsTUFBTSxJQUFHLEtBQ25DNkgsQ0FBQUEseUJBQXlCLENBQUMsRUFBRSxDQUFDUSxPQUFPLEtBQUssUUFDeENSLHlCQUF5QixDQUFDLEVBQUUsQ0FBQ1EsT0FBTyxLQUFLeUIsU0FBUSxLQUNuRDs0QkFDRixvQkFBb0I7NEJBQ3BCLHVCQUF1Qjs0QkFDdkIsaUJBQWlCOzRCQUNqQixhQUNFakMsQ0FBQUEsc0NBQUFBLGdEQUFBQSwwQkFBMkI3SCxNQUFNLElBQUcsS0FDbkM2SCxDQUFBQSx5QkFBeUIsQ0FBQyxFQUFFLENBQUNRLE9BQU8sS0FBSyxRQUN4Q1IseUJBQXlCLENBQUMsRUFBRSxDQUFDUSxPQUFPLEtBQUt5QixTQUFRLElBQy9DakMsRUFBQUEsK0JBQUFBLHlCQUF5QixDQUFDLEVBQUUsY0FBNUJBLG1EQUFBQSw2QkFBOEJPLFFBQVEsSUFDcEMsSUFDQSxNQUNGOzRCQUNOLGtCQUNFUCxDQUFBQSxzQ0FBQUEsZ0RBQUFBLDBCQUEyQjdILE1BQU0sSUFBRyxLQUNuQzZILENBQUFBLHlCQUF5QixDQUFDLEVBQUUsQ0FBQ1EsT0FBTyxLQUFLLFFBQ3hDUix5QkFBeUIsQ0FBQyxFQUFFLENBQUNRLE9BQU8sS0FBS3lCLFNBQVEsS0FDbkQ7d0JBQ0o7cUJBQ0Q7b0JBQ0Q7d0JBQ0U7d0JBQ0E7NEJBQ0UsbUJBQW1COzRCQUNuQixlQUFlLEVBQUV2SyxpQkFBQUEsNEJBQUFBLG9CQUFBQSxLQUFNUSxRQUFRLENBQUMsRUFBRSxjQUFqQlIseUNBQUFBLGdDQUFBQSxrQkFBbUI2RSxZQUFZLGNBQS9CN0Usb0RBQUFBLDhCQUFpQzZHLElBQUk7NEJBQ3RELGVBQWU7NEJBQ2YsaUJBQWlCN0csS0FBS3dELFlBQVk7NEJBQ2xDLGtCQUFrQnhELENBQUFBLGFBQUFBLGlCQUFBQSwyQkFBQUEsS0FBTTRMLEdBQUcsY0FBVDVMLHdCQUFBQSxhQUFhOzRCQUMvQix1QkFBdUI7NEJBQ3ZCLHFCQUFxQjs0QkFDckIsbUJBQW1CQSxDQUFBQSxpQkFBQUEsMkJBQUFBLEtBQU1pSixXQUFXLElBQUdqSixpQkFBQUEsMkJBQUFBLEtBQU1pSixXQUFXLEdBQUc7NEJBQzNELG1CQUFtQixFQUFFakosaUJBQUFBLDJCQUFBQSxLQUFNa0ksaUJBQWlCOzRCQUM1QyxnQkFBZ0I7NEJBQ2hCLHVCQUF1Qjs0QkFDdkIsY0FBYzs0QkFDZCxZQUFZOzRCQUNaLGNBQWMsRUFBRWxJLGlCQUFBQSwyQkFBQUEsS0FBTTZLLDBCQUEwQjs0QkFDaEQsZUFBZSxFQUFFN0ssaUJBQUFBLDJCQUFBQSxLQUFNK0osd0JBQXdCOzRCQUMvQyxhQUFhLEVBQUUvSixpQkFBQUEsNEJBQUFBLG9CQUFBQSxLQUFNUSxRQUFRLENBQUMsRUFBRSxjQUFqQlIseUNBQUFBLGdDQUFBQSxrQkFBbUI2RSxZQUFZLGNBQS9CN0Usb0RBQUFBLDhCQUFpQzZHLElBQUk7NEJBQ3BELGNBQWMsRUFBRTdHLGlCQUFBQSwyQkFBQUEsS0FBTTRKLFlBQVk7NEJBQ2xDLG1DQUNFNUosQ0FBQUEsaUJBQUFBLDRCQUFBQSxrQ0FBQUEsS0FBTXVJLHdCQUF3QixjQUE5QnZJLHNEQUFBQSxnQ0FBZ0NTLE1BQU0sSUFBRyxJQUNyQ1QsaUJBQUFBLDJCQUFBQSxLQUFNdUksd0JBQXdCLENBQUMsRUFBRSxDQUFDTyxPQUFPLEdBQ3pDOzRCQUNOLFVBQVU7NEJBQ1YsVUFBVTs0QkFDVixnQkFBZ0IsRUFBRTlJLGlCQUFBQSwyQkFBQUEsS0FBTWdMLGNBQWM7NEJBQ3RDLGdCQUFnQixFQUFFaEwsaUJBQUFBLDJCQUFBQSxLQUFNaUwsY0FBYzs0QkFDdEMsZ0JBQWdCLEVBQUVqTCxpQkFBQUEsMkJBQUFBLEtBQU1rTCxjQUFjOzRCQUN0QyxnQkFBZ0IsRUFBRWxMLGlCQUFBQSwyQkFBQUEsS0FBTW1MLGNBQWM7NEJBQ3RDLFdBQVcsRUFBRW5MLGlCQUFBQSwyQkFBQUEsS0FBTW9MLFdBQVc7NEJBQzlCLG1CQUFtQnBMLENBQUFBLGlCQUFBQSwyQkFBQUEsS0FBTWlKLFdBQVcsSUFBR2pKLGlCQUFBQSwyQkFBQUEsS0FBTWlKLFdBQVcsR0FBRzs0QkFDM0QsNEJBQTRCakosQ0FBQUEsaUJBQUFBLDJCQUFBQSxLQUFNMk0sZUFBZSxJQUM3QzNNLGlCQUFBQSwyQkFBQUEsS0FBTTJNLGVBQWUsR0FDckI7NEJBQ0osb0RBQ0UzTSxDQUFBQSxpQkFBQUEsMkJBQUFBLEtBQU1pSixXQUFXLElBQUdqSixpQkFBQUEsMkJBQUFBLEtBQU1pSixXQUFXLEdBQUc7NEJBQzFDLGlCQUFpQixFQUFFakosaUJBQUFBLDJCQUFBQSxLQUFNcUwsYUFBYTs0QkFDdEMscUJBQ0VyTCxDQUFBQSxpQkFBQUEsNEJBQUFBLG9CQUFBQSxLQUFNUSxRQUFRLENBQUMsRUFBRSxjQUFqQlIsd0NBQUFBLGtCQUFtQk8sVUFBVSxLQUFJLElBQzdCLElBQ0FQLENBQUFBLGlCQUFBQSw0QkFBQUEsb0JBQUFBLEtBQU1RLFFBQVEsQ0FBQyxFQUFFLGNBQWpCUix3Q0FBQUEsa0JBQW1CTyxVQUFVLEtBQUksSUFDakMsSUFDQVAsQ0FBQUEsaUJBQUFBLDJCQUFBQSxLQUFNUSxRQUFRLENBQUMsRUFBRSxDQUFDRCxVQUFVLEtBQUksSUFDaEMsSUFDQVAsQ0FBQUEsaUJBQUFBLDJCQUFBQSxLQUFNUSxRQUFRLENBQUMsRUFBRSxDQUFDRCxVQUFVLEtBQUksSUFDaEMsSUFDQTs0QkFDTixlQUFlOzRCQUNma0wsU0FDRXpMLENBQUFBLGlCQUFBQSwyQkFBQUEsS0FBTTBMLE9BQU8sS0FBSSxPQUFRMUwsQ0FBQUEsaUJBQUFBLDJCQUFBQSxLQUFNMEwsT0FBTyxJQUFHLE1BQU0sTUFBTzs0QkFDeERDLE9BQU87NEJBQ1AsZ0JBQWdCOzRCQUNoQixnQkFBZ0IzTCxDQUFBQSxpQkFBQUEsMkJBQUFBLEtBQU11TCxZQUFZLElBQUd2TCxpQkFBQUEsMkJBQUFBLEtBQU11TCxZQUFZLEdBQUc7NEJBQzFELCtCQUErQjs0QkFDL0IseUJBQXlCOzRCQUN6Qix3QkFBd0I7NEJBQ3hCLGVBQWU7NEJBQ2Ysb0NBQW9DOzRCQUNwQyxtQkFBbUI7NEJBQ25CTSxRQUNFdkQsQ0FBQUEsc0NBQUFBLGdEQUFBQSwwQkFBMkI3SCxNQUFNLElBQUcsSUFDaEM2SCx5QkFBeUIsQ0FBQyxFQUFFLENBQUNRLE9BQU8sSUFBSSxLQUN4Qzs0QkFDTi9ILEVBQUUsRUFBRWYsaUJBQUFBLDJCQUFBQSxLQUFNZSxFQUFFOzRCQUNabUwsZ0NBQWdDLEVBQzlCbE0saUJBQUFBLDJCQUFBQSxLQUFNa00sZ0NBQWdDO3dCQUMxQztxQkFDRDtvQkFDRDt3QkFBQztxQkFBa0M7aUJBQ3BDO2dCQUVELE1BQU1VLDJCQUEyQixDQUFDUCxZQUFZQztvQkFDNUMsSUFBSXRELHFCQUFxQixDQUFDcUQsV0FBVyxDQUFDNUwsTUFBTSxLQUFLLEdBQUc7d0JBQ2xEdUkscUJBQXFCLENBQUNxRCxXQUFXLENBQUM1RyxJQUFJLElBQUk2RztvQkFDNUMsT0FBTzt3QkFDTHRELHFCQUFxQixDQUFDcUQsV0FBVyxHQUFHOzRCQUNsQ3JELHFCQUFxQixDQUFDcUQsV0FBVyxDQUFDLEVBQUU7K0JBQ2pDQzt5QkFDSjtvQkFDSDtnQkFDRjtnQkFFQU0seUJBQXlCLEdBQUdIO2dCQUM1QkMsc0JBQXNCLE1BQU1wTix3REFBV0EsQ0FDckMwSix1QkFDQSxPQUNBdEosT0FDQUMsU0FDQUYsVUFDQWMsYUFDQWYsZ0JBQUFBLE9BQU9RLElBQUksY0FBWFIsb0NBQUFBLGNBQWFtRSxlQUFlLEVBQzVCO1lBRUosT0FBTztnQkFDTCtJLHNCQUFzQjtZQUN4QjtZQUVBLE1BQU1HLDBCQUEwQixNQUFNdk4sd0RBQVdBLENBQy9DeUosNEJBQ0EsTUFDQXJKLE9BQ0FDLFNBQ0FGLFVBQ0FjLGFBQ0FmLGVBQUFBLE9BQU9RLElBQUksY0FBWFIsbUNBQUFBLGFBQWFtRSxlQUFlLEVBQzVCO1lBR0YvQixvQkFBb0I7WUFDcEIsSUFDRThLLHVCQUNBRywyQkFDQUgsdUJBQXVCLFlBQ3ZCO2dCQUNBNUssZ0JBQ0U7Z0JBRUZJLG9CQUFvQjtnQkFDcEJGLHlCQUF5QjtZQUMzQixPQUFPLElBQUkwSyx1QkFBdUJBLHVCQUF1QixZQUFZO2dCQUNuRTVLLGdCQUNFO2dCQUVGRSx5QkFBeUI7WUFDM0IsT0FBTyxJQUFJNkssMkJBQTJCSCx1QkFBdUIsWUFBWTtnQkFDdkV4SyxvQkFBb0I7Z0JBQ3BCSixnQkFDRTtZQUVKLE9BQU8sSUFBSStLLDJCQUEyQkgsdUJBQXVCLFlBQVk7Z0JBQ3ZFNUssZ0JBQ0U7Z0JBRUZFLHlCQUF5QjtZQUMzQixPQUFPO2dCQUNMRixnQkFDRTtZQUVKO1FBQ0YsT0FBTztnQkFtQjJCOUI7WUFsQmhDLElBQ0VSLE9BQU9RLElBQUksQ0FBQ0Msa0JBQWtCLElBQzdCVCxDQUFBQSxPQUFPUSxJQUFJLENBQUN3RSxPQUFPLEtBQUssZ0JBQ3ZCaEYsT0FBT1EsSUFBSSxDQUFDd0UsT0FBTyxJQUFJLGFBQVksR0FDckM7Z0JBQ0E1RixpREFBS0EsQ0FBQ2tGLEtBQUssQ0FBQyxxQ0FBcUM7b0JBQy9DSyxVQUFVO29CQUNWMkksV0FBVztvQkFDWEMsaUJBQWlCO29CQUNqQkMsY0FBYztvQkFDZEMsY0FBYztvQkFDZEMsV0FBVztvQkFDWEMsVUFBVTVDO29CQUNWNkMsT0FBTztnQkFDVDtnQkFDQTtZQUNGLE9BQU8sSUFDTDVOLE9BQU9RLElBQUksQ0FBQ0Msa0JBQWtCLElBQzdCRCxDQUFBQSxDQUFBQSxpQkFBQUEsMkJBQUFBLEtBQU0rRSxPQUFPLENBQUNDLFFBQVEsQ0FBQyxRQUFNaEYsaUJBQUFBLDRCQUFBQSxpQkFBQUEsS0FBTStFLE9BQU8sY0FBYi9FLHFDQUFBQSxlQUFlZ0YsUUFBUSxDQUFDLEdBQUMsS0FDdkQsQ0FBQ3hGLE9BQU9RLElBQUksQ0FBQ2lKLFdBQVcsRUFDeEI7Z0JBQ0FySyxpREFBS0EsQ0FBQ2tGLEtBQUssQ0FDVCxnRUFDQTtvQkFDRUssVUFBVTtvQkFDVjJJLFdBQVc7b0JBQ1hDLGlCQUFpQjtvQkFDakJDLGNBQWM7b0JBQ2RDLGNBQWM7b0JBQ2RDLFdBQVc7b0JBQ1hDLFVBQVU1QztvQkFDVjZDLE9BQU87Z0JBQ1Q7Z0JBRUY7WUFDRixPQUFPO2dCQUNMeE8saURBQUtBLENBQUNrRixLQUFLLENBQUMscURBQXFEO29CQUMvREssVUFBVTtvQkFDVjJJLFdBQVc7b0JBQ1hDLGlCQUFpQjtvQkFDakJDLGNBQWM7b0JBQ2RDLGNBQWM7b0JBQ2RDLFdBQVc7b0JBQ1hDLFVBQVU1QztvQkFDVjZDLE9BQU87Z0JBQ1Q7Z0JBQ0E7WUFDRjtRQUNGO1FBQ0ExTCxzQkFBc0I7SUFDeEI7SUFFQSxNQUFNMkwscUJBQXFCO1FBQ3pCL0wsVUFBVTtJQUNaO0lBRUEsSUFBSUQsUUFBUTtRQUNWLHFCQUNFLDhEQUFDdEMsaUVBQVlBO1lBQ1hzQyxRQUFRO1lBQ1JDLFdBQVdBO1lBQ1gxQixjQUFjSTtZQUNkUCxVQUFVQTs7Ozs7O0lBR2hCO0lBRUEsTUFBTTZOLGdCQUFnQjtJQUN0QixNQUFNQyxtQkFBbUIsQ0FBQ0M7UUFDeEI5TCxzQkFBc0I4TCxFQUFFQyxNQUFNLENBQUMxQyxLQUFLO0lBQ3RDO0lBRUEscUJBQ0U7OzBCQUVFLDhEQUFDMkM7Z0JBQUlDLFdBQVU7O2tDQUliLDhEQUFDQzt3QkFDQ0MsU0FBUyxJQUNQNU0sVUFBVSxjQUFjQSxVQUFVLGNBQzlCdUUsaUJBQ0FHO2tDQUdMMUUsVUFBVSxjQUFjQSxVQUFVLDRCQUNqQyw4REFBQ3JELDJFQUFlQTs0QkFDZGtRLE1BQU0vUCxxRUFBS0E7NEJBQ1hnUSxNQUFLOzRCQUNMQyxPQUFNOzRCQUNOTCxXQUFVOzs7OztzREFHWiw4REFBQy9QLDJFQUFlQTs0QkFDZGtRLE1BQU1oUSw2RUFBYUE7NEJBQ25CaVEsTUFBSzs0QkFDTEMsT0FBTTs0QkFDTkwsV0FBVTs7Ozs7Ozs7Ozs7b0JBSWYxTSxVQUFVLGVBQWVuQiwyQkFDeEIsOERBQUM4Tjt3QkFDQ0MsU0FBUyxJQUFNdEosZ0JBQWdCekQ7d0JBQy9Ca04sT0FBTTtrQ0FFTiw0RUFBQ3BRLDJFQUFlQTs0QkFDZGtRLE1BQU05UCw0RUFBWUE7NEJBQ2xCK1AsTUFBSzs0QkFDTEosV0FBVTs7Ozs7Ozs7Ozs7b0JBSWYxTSxVQUFVLGVBQWVBLFVBQVUsNEJBQ2xDLDhEQUFDMk07d0JBQ0NDLFNBQVN2TDt3QkFDVDBMLE9BQU07d0JBQ05MLFdBQVU7a0NBRVYsNEVBQUMvUCwyRUFBZUE7NEJBQ2RrUSxNQUFNMU8sdUVBQU9BOzRCQUNiMk8sTUFBSzs0QkFDTEosV0FBVTs7Ozs7Ozs7Ozs7Ozs7Ozs7MEJBTWxCLDhEQUFDMU8sa0dBQVVBO2dCQUFDZ1AsTUFBTTtnQkFBQ0MsTUFBTTlMO2dCQUFjK0wsSUFBSTVQLDJDQUFRQTswQkFDakQsNEVBQUNTLDhGQUFNQTtvQkFBQ21QLElBQUc7b0JBQU1SLFdBQVU7b0JBQWdCUyxTQUFTN0w7O3NDQUNsRCw4REFBQ3RELGtHQUFVQSxDQUFDb1AsS0FBSzs0QkFDZkYsSUFBSTVQLDJDQUFRQTs0QkFDWitQLE9BQU07NEJBQ05DLFdBQVU7NEJBQ1ZDLFNBQVE7NEJBQ1JDLE9BQU07NEJBQ05DLFdBQVU7NEJBQ1ZDLFNBQVE7c0NBRVIsNEVBQUNqQjtnQ0FBSUMsV0FBVTs7Ozs7Ozs7Ozs7c0NBR2pCLDhEQUFDRDs0QkFBSUMsV0FBVTtzQ0FDYiw0RUFBQ0Q7Z0NBQUlDLFdBQVU7MENBQ2IsNEVBQUMxTyxrR0FBVUEsQ0FBQ29QLEtBQUs7b0NBQ2ZGLElBQUk1UCwyQ0FBUUE7b0NBQ1orUCxPQUFNO29DQUNOQyxXQUFVO29DQUNWQyxTQUFRO29DQUNSQyxPQUFNO29DQUNOQyxXQUFVO29DQUNWQyxTQUFROzhDQUVSLDRFQUFDM1AsOEZBQU1BLENBQUM0UCxLQUFLO3dDQUFDakIsV0FBVTtrREFFdEIsNEVBQUNEOzRDQUFJQyxXQUFVOzs4REFFYiw4REFBQ0Q7b0RBQUlDLFdBQVU7O3NFQUNiLDhEQUFDa0I7NERBQUdsQixXQUFVOzs4RUFDWiw4REFBQ21CO29FQUFLbkIsV0FBVTs7c0ZBQ2QsOERBQUMvUCwyRUFBZUE7NEVBQUNrUSxNQUFNNU8sc0VBQU1BOzs7Ozs7d0VBQUs7Ozs7Ozs7Z0VBQzVCO2dFQUFJOzs7Ozs7O3NFQUdkLDhEQUFDME87NERBQ0NDLFNBQVN0TDs0REFDVGEsTUFBSzs0REFDTHVLLFdBQVU7NERBQ1ZvQixtQkFBZ0I7OzhFQUVoQiw4REFBQ25SLDJFQUFlQTtvRUFDZGtRLE1BQU0xTyx1RUFBT0E7b0VBQ2J1TyxXQUFVOzs7Ozs7Z0VBQ1Q7Ozs7Ozs7Ozs7Ozs7OERBSVAsOERBQUNEO29EQUFJQyxXQUFVOzhEQUNiLDRFQUFDcUI7d0RBQUVyQixXQUFVO2tFQUF3RDs7Ozs7Ozs7Ozs7OERBS3ZFLDhEQUFDRDtvREFBSUMsV0FBVTs7c0VBQ2IsOERBQUNDOzREQUNDQyxTQUFTdEw7NERBQ1R3TSxtQkFBZ0I7NERBQ2hCM0wsTUFBSzs0REFDTHVLLFdBQVU7c0VBQ1g7Ozs7OztzRUFHRCw4REFBQ0M7NERBQ0NDLFNBQVNuTDs0REFDVHFNLG1CQUFnQjs0REFDaEIzTCxNQUFLOzREQUNMdUssV0FBVTtzRUFDWDs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzswQkFXakIsOERBQUMxTyxrR0FBVUE7Z0JBQUNnUCxNQUFNO2dCQUFDQyxNQUFNdk07Z0JBQWtCd00sSUFBSTVQLDJDQUFRQTswQkFDckQsNEVBQUNTLDhGQUFNQTtvQkFBQ21QLElBQUc7b0JBQU1SLFdBQVU7b0JBQWdCUyxTQUFTNUw7O3NDQUNsRCw4REFBQ3ZELGtHQUFVQSxDQUFDb1AsS0FBSzs0QkFDZkYsSUFBSTVQLDJDQUFRQTs0QkFDWitQLE9BQU07NEJBQ05DLFdBQVU7NEJBQ1ZDLFNBQVE7NEJBQ1JDLE9BQU07NEJBQ05DLFdBQVU7NEJBQ1ZDLFNBQVE7c0NBRVIsNEVBQUNqQjtnQ0FBSUMsV0FBVTs7Ozs7Ozs7Ozs7c0NBR2pCLDhEQUFDRDs0QkFBSUMsV0FBVTtzQ0FDYiw0RUFBQ0Q7Z0NBQUlDLFdBQVU7MENBQ2IsNEVBQUMxTyxrR0FBVUEsQ0FBQ29QLEtBQUs7b0NBQ2ZGLElBQUk1UCwyQ0FBUUE7b0NBQ1orUCxPQUFNO29DQUNOQyxXQUFVO29DQUNWQyxTQUFRO29DQUNSQyxPQUFNO29DQUNOQyxXQUFVO29DQUNWQyxTQUFROzhDQUVSLDRFQUFDM1AsOEZBQU1BLENBQUM0UCxLQUFLO3dDQUFDakIsV0FBVTtrREFFdEIsNEVBQUNEOzRDQUFJQyxXQUFVOzs4REFFYiw4REFBQ0Q7b0RBQUlDLFdBQVU7O3NFQUNiLDhEQUFDa0I7NERBQUdsQixXQUFVOzs4RUFDWiw4REFBQ21CO29FQUFLbkIsV0FBVTs7c0ZBQ2QsOERBQUMvUCwyRUFBZUE7NEVBQUNrUSxNQUFNNU8sc0VBQU1BOzs7Ozs7d0VBQUs7Ozs7Ozs7Z0VBQzVCO2dFQUFJOzs7Ozs7O3NFQUdkLDhEQUFDME87NERBQ0NDLFNBQVNyTDs0REFDVFksTUFBSzs0REFDTHVLLFdBQVU7NERBQ1ZvQixtQkFBZ0I7OzhFQUVoQiw4REFBQ25SLDJFQUFlQTtvRUFDZGtRLE1BQU0xTyx1RUFBT0E7b0VBQ2J1TyxXQUFVOzs7Ozs7Z0VBQ1Q7Ozs7Ozs7Ozs7Ozs7OERBSVAsOERBQUNEO29EQUFJQyxXQUFVOzhEQUNiLDRFQUFDcUI7d0RBQUVyQixXQUFVO2tFQUNWOUw7Ozs7Ozs7Ozs7OzhEQUlMLDhEQUFDNkw7b0RBQUlDLFdBQVU7OERBQ2IsNEVBQUNDO3dEQUNDQyxTQUFTckw7d0RBQ1R1TSxtQkFBZ0I7d0RBQ2hCM0wsTUFBSzt3REFDTHVLLFdBQVU7a0VBQ1g7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBYXZCO0dBMXlDTXBPOztRQUNXckIsa0RBQVNBO1FBT21CbUIsMEVBQWNBO1FBR3BCUix3REFBT0E7OztBQWl5QzlDLCtEQUFlVSxjQUFjQSxFQUFDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL3V0aWxzL3JlbmRlcmVyL2FjdGlvblJlbmRlcmVyLmpzPzBiYmEiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgRm9udEF3ZXNvbWVJY29uIH0gZnJvbSBcIkBmb3J0YXdlc29tZS9yZWFjdC1mb250YXdlc29tZVwiO1xyXG5pbXBvcnQge1xyXG4gIGZhQ29weSxcclxuICBmYVBlblRvU3F1YXJlLFxyXG4gIGZhRXllLFxyXG4gIGZhRmlsZUV4cG9ydCxcclxufSBmcm9tIFwiQGZvcnRhd2Vzb21lL2ZyZWUtc29saWQtc3ZnLWljb25zXCI7XHJcbmltcG9ydCB7IFJvdXRlciwgdXNlUm91dGVyIH0gZnJvbSBcIm5leHQvcm91dGVyXCI7XHJcbmltcG9ydCBMaW5rIGZyb20gXCJuZXh0L2xpbmtcIjtcclxuaW1wb3J0IHsgYXBpQ29uZmlnIH0gZnJvbSBcIkAvc2VydmljZXMvYXBpQ29uZmlnXCI7XHJcbmltcG9ydCB7IHVzZVN0YXRlLCB1c2VFZmZlY3QsIEZyYWdtZW50IH0gZnJvbSBcInJlYWN0XCI7XHJcbmltcG9ydCBDb29raWVzIGZyb20gXCJqcy1jb29raWVcIjtcclxuaW1wb3J0ICogYXMgWExTWCBmcm9tIFwieGxzeFwiO1xyXG5pbXBvcnQgZXhwb3J0RXhjZWxEYXRhIGZyb20gXCIuLi9leHBvcnRFeGNlbFwiO1xyXG5pbXBvcnQgeyBUb2FzdENvbnRhaW5lciwgdG9hc3QgfSBmcm9tIFwicmVhY3QtdG9hc3RpZnlcIjtcclxuaW1wb3J0IFwicmVhY3QtdG9hc3RpZnkvZGlzdC9SZWFjdFRvYXN0aWZ5LmNzc1wiO1xyXG5pbXBvcnQgeyB1c2VVc2VyIH0gZnJvbSBcIkAvdXRpbHMvdXNlckNvbnRleHRcIjtcclxuaW1wb3J0IHsgZ2V0Q29va2llRGF0YSB9IGZyb20gXCJAL3V0aWxzL2dldENvb2tpZURhdGFcIjtcclxuaW1wb3J0IENvcHlTdXBwbGllciBmcm9tIFwiQC9jb21wb25lbnRzL0NvcHlTdXBwbGllclwiO1xyXG5pbXBvcnQgeyBEaWFsb2csIFRyYW5zaXRpb24gfSBmcm9tIFwiQGhlYWRsZXNzdWkvcmVhY3RcIjtcclxuaW1wb3J0IHsgZmFJbmZvLCBmYUNsb3NlLCBmYVhtYXJrIH0gZnJvbSBcIkBmb3J0YXdlc29tZS9mcmVlLXNvbGlkLXN2Zy1pY29uc1wiO1xyXG5cclxuaW1wb3J0IHsgdXNlUGVybWlzc2lvbnMgfSBmcm9tIFwiQC91dGlscy9yb2xlUGVybWlzc2lvbnNDb250ZXh0XCI7XHJcbmltcG9ydCBleHBvcnRFeGNlbCBmcm9tIFwiLi4vZXhwb3J0RXhjZWxcIjtcclxuY29uc3QgYWN0aW9uUmVuZGVyZXIgPSAocGFyYW1zLCB1c2VyRGF0YSwgdG9rZW4sIGNvbXBhbnkpID0+IHtcclxuICBjb25zdCByb3V0ZXIgPSB1c2VSb3V0ZXIoKTtcclxuICBjb25zdCBjYW5FeHBvcnQgPVxyXG4gICAgdXNlckRhdGE/LnJvbGVfaWQgPT09IDEgfHxcclxuICAgIHVzZXJEYXRhPy5yb2xlX2lkID09PSAyIHx8XHJcbiAgICB1c2VyRGF0YT8ucm9sZV9pZCA9PT0gNSB8fFxyXG4gICAgdXNlckRhdGE/LnJvbGVfaWQgPT09IDYgfHxcclxuICAgIHBhcmFtcy5kYXRhLmlzRW1lcmdlbmN5UmVxdWVzdDtcclxuICBjb25zdCB7IHBlcm1pc3Npb25zLCB1cGRhdGVQZXJtaXNzaW9ucyB9ID0gdXNlUGVybWlzc2lvbnMoKTtcclxuXHJcbiAgY29uc3Qgc2VydmVyQWRkcmVzcyA9IGFwaUNvbmZpZy5zZXJ2ZXJBZGRyZXNzO1xyXG4gIGNvbnN0IHsgdXNlckRldGFpbHMsIHVwZGF0ZVRva2VuIH0gPSB1c2VVc2VyKCk7XHJcbiAgY29uc3Qgc3VwcGxpZXJEYXRhID0gcGFyYW1zLmRhdGE7XHJcbiAgbGV0IHByb3BoZXRfaWQgPVxyXG4gICAgc3VwcGxpZXJEYXRhPy5wcm9waGV0cy5sZW5ndGggPiAwICYmIHN1cHBsaWVyRGF0YT8ucHJvcGhldHNbMF0/LnByb3BoZXRfaWQ7XHJcbiAgY29uc3Qgcm9sZV9pZHMgPSBwYXJhbXMuZGF0YS5yb2xlSWQubWFwKChlbGUpID0+IGVsZS5yb2xlX2lkKTtcclxuXHJcbiAgY29uc3Qgc3VwcGxpZXJfaWQgPSBwYXJhbXMuZGF0YS5pZDtcclxuICBjb25zdCBzdXBwbGllcl9zdGF0dXMgPSBwYXJhbXMuZGF0YS5zdGF0dXM7XHJcbiAgY29uc3QgW3N0YXR1cywgc2V0U3RhdHVzXSA9IHVzZVN0YXRlKFwiXCIpO1xyXG4gIGNvbnN0IFtkYXRhLCBzZXREYXRhXSA9IHVzZVN0YXRlKFtdKTtcclxuICBjb25zdCB1c2VyID0gZ2V0Q29va2llRGF0YShcInVzZXJcIik7XHJcbiAgY29uc3QgW2lzT3Blbiwgc2V0SXNPcGVuXSA9IHVzZVN0YXRlKGZhbHNlKTtcclxuICBjb25zdCBbaXNPcGVuT3B0aW9uLCBzZXRJc09wZW5PcHRpb25dID0gdXNlU3RhdGUoZmFsc2UpO1xyXG4gIGNvbnN0IFtzZWxlY3RlZEV4cG9ydFR5cGUsIHNldFNlbGVjdGVkRXhwb3J0VHlwZV0gPSB1c2VTdGF0ZShcIlwiKTtcclxuICBjb25zdCBbZW1haWxTdGF0dXNQb3B1cCwgc2V0RW1haWxTdGF0dXNQb3B1cF0gPSB1c2VTdGF0ZShmYWxzZSk7XHJcbiAgY29uc3QgW3BvcHVwTWVzc2FnZSwgc2V0UG9wVXBNZXNzYWdlXSA9IHVzZVN0YXRlKFwiXCIpO1xyXG4gIGNvbnN0IFtpbnRlcm5hbEV4cG9ydFN1Y2Nlc3MsIHNldEludGVybmFsRXhwb3J0U3VjY2Vzc10gPSB1c2VTdGF0ZShmYWxzZSk7XHJcbiAgY29uc3QgW0lTU0V4cG9ydFN1Y2Nlc3MsIHNldElTU0V4cG9ydFN1Y2Nlc3NdID0gdXNlU3RhdGUoZmFsc2UpO1xyXG5cclxuICBjb25zdCBjbG9zZU9wdGlvbk1vZGFsID0gKCkgPT4ge1xyXG4gICAgc2V0SXNPcGVuT3B0aW9uKGZhbHNlKTtcclxuICB9O1xyXG5cclxuICBjb25zdCBbaXNDYW5jZWxPcGVuLCBzZXRJc0NhbmNlbE9wZW5dID0gdXNlU3RhdGUoZmFsc2UpO1xyXG4gIGNvbnN0IGNhbmNlbFByb2R1Y3QgPSAoKSA9PiB7XHJcbiAgICBzZXRJc0NhbmNlbE9wZW4odHJ1ZSk7XHJcbiAgfTtcclxuXHJcbiAgY29uc3QgY2xvc2VDYW5jZWxNb2RhbCA9ICgpID0+IHtcclxuICAgIHNldElzQ2FuY2VsT3BlbihmYWxzZSk7XHJcbiAgfTtcclxuICBjb25zdCBjbG9zZUVtYWlsUG9wdXAgPSAoKSA9PiB7XHJcbiAgICBzZXRFbWFpbFN0YXR1c1BvcHVwKGZhbHNlKTtcclxuICAgIGlmIChpbnRlcm5hbEV4cG9ydFN1Y2Nlc3MgJiYgSVNTRXhwb3J0U3VjY2Vzcykge1xyXG4gICAgICBwYXJhbXMuc2V0VXBkYXRlU3RhdHVzQ2hhbmdlKGRhdGE/LmlkLCBkYXRhPy5zdGF0dXMpO1xyXG4gICAgfVxyXG4gIH07XHJcblxyXG4gIGZ1bmN0aW9uIHNhdmVNb2RhbERhdGEoKSB7XHJcbiAgICBsZXQgc2VydmVyQWRkcmVzcyA9IGFwaUNvbmZpZy5zZXJ2ZXJBZGRyZXNzO1xyXG5cclxuICAgIC8vIHNldExvYWRpbmcodHJ1ZSk7XHJcbiAgICBmZXRjaChgJHtzZXJ2ZXJBZGRyZXNzfXN1cHBsaWVycy91cGRhdGUtc3VwcGxpZXIvJHtzdXBwbGllcl9pZH1gLCB7XHJcbiAgICAgIG1ldGhvZDogXCJQVVRcIixcclxuICAgICAgaGVhZGVyczoge1xyXG4gICAgICAgIEFjY2VwdDogXCJhcHBsaWNhdGlvbi9qc29uXCIsXHJcbiAgICAgICAgXCJDb250ZW50LVR5cGVcIjogXCJhcHBsaWNhdGlvbi9qc29uXCIsXHJcbiAgICAgICAgQXV0aG9yaXphdGlvbjogYEJlYXJlciAke3VzZXJEYXRhLnRva2VufWAsXHJcbiAgICAgIH0sXHJcbiAgICAgIGJvZHk6IEpTT04uc3RyaW5naWZ5KHtcclxuICAgICAgICBzZWN0aW9uTmFtZTogXCJ1cGRhdGVTdGF0dXNcIixcclxuICAgICAgICB0eXBlOiBcImNhbmNlbFByb2R1Y3RcIixcclxuICAgICAgICBzdGF0dXM6IDYsXHJcbiAgICAgICAgdXBkYXRlZF9kYXRlOiBuZXcgRGF0ZSgpLnRvSVNPU3RyaW5nKCksXHJcbiAgICAgICAgY29tcGFueV9uYW1lOiBwYXJhbXM/LmRhdGE/LmNvbXBhbnlfbmFtZSxcclxuICAgICAgICByZXF1ZXN0b3JfbmFtZTogcGFyYW1zLmRhdGEucmVxdWVzdG9yLFxyXG4gICAgICAgIHJlcXVlc3Rvcl9lbWFpbDogcGFyYW1zLmRhdGE/LnJlcXVlc3Rvcl9lbWFpbCxcclxuICAgICAgfSksXHJcbiAgICB9KVxyXG4gICAgICAudGhlbigocmVzKSA9PiB7XHJcbiAgICAgICAgaWYgKHJlcy5zdGF0dXMgPT09IDQwMCkge1xyXG4gICAgICAgICAgdG9hc3QuZXJyb3IoXHJcbiAgICAgICAgICAgIFwiVGhlcmUgd2FzIGFuIGVycm9yIHdpdGggeW91ciByZXF1ZXN0LiBQbGVhc2UgY2hlY2sgeW91ciBkYXRhIGFuZCB0cnkgYWdhaW4uXCJcclxuICAgICAgICAgICk7XHJcbiAgICAgICAgfSBlbHNlIGlmIChyZXMuc3RhdHVzID09PSA0MDEpIHtcclxuICAgICAgICAgIHRvYXN0LmVycm9yKFwiWW91ciBzZXNzaW9uIGhhcyBleHBpcmVkLiBQbGVhc2UgbG9nIGluIGFnYWluLlwiKTtcclxuICAgICAgICAgIC8vIHNldFRpbWVvdXQoKCkgPT4ge1xyXG4gICAgICAgICAgLy8gICBsb2NhbFN0b3JhZ2UucmVtb3ZlSXRlbShcInN1cGVyVXNlclwiKTtcclxuICAgICAgICAgIC8vICAgQ29va2llcy5yZW1vdmUoXCJjb21wYW55XCIpXHJcbiAgICAgICAgICAvLyAgIGxvY2FsU3RvcmFnZS5yZW1vdmVJdGVtKFwiaWRcIik7XHJcbiAgICAgICAgICAvLyAgIGxvY2FsU3RvcmFnZS5yZW1vdmVJdGVtKFwibmFtZVwiKTtcclxuICAgICAgICAgIC8vICAgbG9jYWxTdG9yYWdlLnJlbW92ZUl0ZW0oXCJyb2xlXCIpO1xyXG4gICAgICAgICAgLy8gICBsb2NhbFN0b3JhZ2UucmVtb3ZlSXRlbShcImVtYWlsXCIpO1xyXG4gICAgICAgICAgLy8gICBDb29raWVzLnJlbW92ZShcInVzZXJcIilcclxuICAgICAgICAgIC8vICAgQ29va2llcy5yZW1vdmUoXCJ0aGVtZVwiKVxyXG4gICAgICAgICAgLy8gICBDb29raWVzLnJlbW92ZShcInRva2VuXCIpXHJcbiAgICAgICAgICAvLyAgIC8vIGxvZ291dEhhbmRsZXIoaW5zdGFuY2UpO1xyXG4gICAgICAgICAgLy8gfSwgMzAwMCk7XHJcbiAgICAgICAgfVxyXG4gICAgICAgIGlmIChyZXMuc3RhdHVzID09PSAyMDApIHtcclxuICAgICAgICAgIHJldHVybiByZXMuanNvbigpO1xyXG4gICAgICAgIH1cclxuICAgICAgICByZXR1cm4gUHJvbWlzZS5yZWplY3QocmVzKTtcclxuICAgICAgfSlcclxuICAgICAgLnRoZW4oKGpzb24pID0+IHtcclxuICAgICAgICB0b2FzdC5zdWNjZXNzKFwiU3VwcGxpZXIgY2FuY2VsbGVkIHN1Y2Nlc3NmdWxseVwiLCB7XHJcbiAgICAgICAgICBwb3NpdGlvbjogXCJ0b3AtcmlnaHRcIixcclxuICAgICAgICB9KTtcclxuICAgICAgICBwYXJhbXMuc2V0VXBkYXRlU3RhdHVzQ2hhbmdlKGRhdGE/LmlkLCBkYXRhPy5zdGF0dXMpO1xyXG4gICAgICAgIGNsb3NlQ2FuY2VsTW9kYWwoKTtcclxuICAgICAgfSlcclxuICAgICAgLmNhdGNoKChlcnIpID0+IHtcclxuICAgICAgICAvLyBzZXRMb2FkaW5nKGZhbHNlKTtcclxuICAgICAgICB0b2FzdC5lcnJvcihcIkVycm9yIGNhbmNlbGxpbmcgcHJvZHVjdDpcIiwgZXJyLnN0YXR1c1RleHQsIHtcclxuICAgICAgICAgIHBvc2l0aW9uOiBcInRvcC1yaWdodFwiLFxyXG4gICAgICAgIH0pO1xyXG4gICAgICAgIHJldHVybiBlcnI7XHJcbiAgICAgIH0pO1xyXG4gIH1cclxuXHJcbiAgY29uc3Qgb3Blbk9wdGlvbk1vZGFsID0gKHN1cHBsaWVyX2lkKSA9PiB7XHJcbiAgICBpZiAoXHJcbiAgICAgIHN1cHBsaWVyRGF0YS5zdGF0dXMgPT0gXCJDb21wbGV0ZWRcIiB8fFxyXG4gICAgICBzdXBwbGllckRhdGEuc3RhdHVzID09IFwiRXhwb3J0ZWRcIiB8fFxyXG4gICAgICAocGFyYW1zLmRhdGEuaXNFbWVyZ2VuY3lSZXF1ZXN0ICYmIHBhcmFtcy5kYXRhLkdlbmVyYWwgPT09IFwiQ29tcGxldGVcIilcclxuICAgICkge1xyXG4gICAgICBsZXQgaXNFeHBvcnRhYmxlQmFzZWRPbkNvZGVVbmlxdWUgPSBmYWxzZTtcclxuXHJcbiAgICAgIGNvbnN0IGNvZGVDb3VudCA9IHBhcmFtcy5kYXRhPy5wcm9waGV0c1swXT8uY29kZV9jb3VudDtcclxuICAgICAgY29uc3QgcHJvcGhldENvZGUgPSBwYXJhbXMuZGF0YT8ucHJvcGhldHNbMF0/LnByb3BoZXRfY29kZTtcclxuXHJcbiAgICAgIGNvbnN0IHByb3BoZXRfaWQgPVxyXG4gICAgICAgIHBhcmFtcy5kYXRhPy5wcm9waGV0cy5sZW5ndGggPiAwICYmIHBhcmFtcy5kYXRhPy5wcm9waGV0c1swXS5wcm9waGV0X2lkO1xyXG5cclxuICAgICAgY29uc3QgaXNTdXBwbGllckFjY291bnQgPVxyXG4gICAgICAgIHBhcmFtcy5kYXRhPy5yb2xlSWRzPy5pbmNsdWRlcygxKSB8fCBwYXJhbXMuZGF0YT8ucm9sZUlkcz8uaW5jbHVkZXMoNik7XHJcblxyXG4gICAgICBsZXQgY3VycmVuY3kgPVxyXG4gICAgICAgIHBhcmFtcy5kYXRhPy5jdXJyZW5jeSA9PSBcIiRcIlxyXG4gICAgICAgICAgPyBgXFxcXCR7cGFyYW1zLmRhdGE/LmN1cnJlbmN5fWBcclxuICAgICAgICAgIDogcGFyYW1zLmRhdGE/LmN1cnJlbmN5O1xyXG4gICAgICBsZXQgYWN0dWFsQ3VycjtcclxuICAgICAgaWYgKGN1cnJlbmN5ICYmIGN1cnJlbmN5ID09IFwiTm90IEVudGVyZWRcIikge1xyXG4gICAgICAgIGFjdHVhbEN1cnIgPSBcIlwiO1xyXG4gICAgICB9IGVsc2Uge1xyXG4gICAgICAgIGFjdHVhbEN1cnIgPSBjdXJyZW5jeTtcclxuICAgICAgfVxyXG5cclxuICAgICAgbGV0IGlzVmFsaWQgPSB0cnVlO1xyXG4gICAgICBpZiAoaXNTdXBwbGllckFjY291bnQpIHtcclxuICAgICAgICBpZiAocHJvcGhldF9pZCA9PSAxKSB7XHJcbiAgICAgICAgICBsZXQgcmVnZXhQYXR0ZXJuO1xyXG5cclxuICAgICAgICAgIHJlZ2V4UGF0dGVybiA9IG5ldyBSZWdFeHAoYF5bQS1aMC05XXs0fVtBLVowMTQ1Njc4XSR7YWN0dWFsQ3Vycn0kYCk7XHJcblxyXG4gICAgICAgICAgLy8gbGV0IHJlZ2V4UGF0dGVybiA9IG5ldyBSZWdFeHAoYF4oW0EtWjBdezV9KSR7YWN0dWFsQ3Vycn0kYCk7XHJcbiAgICAgICAgICBpc1ZhbGlkID0gcmVnZXhQYXR0ZXJuLnRlc3QocHJvcGhldENvZGUpO1xyXG4gICAgICAgIH0gZWxzZSBpZiAocHJvcGhldF9pZCA9PSAyKSB7XHJcbiAgICAgICAgICBsZXQgcmVnZXhQYXR0ZXJuO1xyXG5cclxuICAgICAgICAgIHJlZ2V4UGF0dGVybiA9IG5ldyBSZWdFeHAoYF4oW0EtWjBdezR9KTkke2FjdHVhbEN1cnJ9JGApO1xyXG5cclxuICAgICAgICAgIC8vIGxldCByZWdleFBhdHRlcm4gPSBuZXcgUmVnRXhwKGBeKFtBLVowXXs0fSk5JHthY3R1YWxDdXJyfSRgKTtcclxuICAgICAgICAgIGlzVmFsaWQgPSByZWdleFBhdHRlcm4udGVzdChwcm9waGV0Q29kZSk7XHJcbiAgICAgICAgfSBlbHNlIGlmIChwcm9waGV0X2lkID09IDMpIHtcclxuICAgICAgICAgIGxldCByZWdleFBhdHRlcm4gPSBuZXcgUmVnRXhwKGBeW0EtWjAtOV17NH1bQS1aMDEzNDU2NzhdW0EtWjAtOV0qJGApO1xyXG4gICAgICAgICAgaXNWYWxpZCA9IHJlZ2V4UGF0dGVybi50ZXN0KHByb3BoZXRDb2RlKSAmJiBwcm9waGV0Q29kZS5sZW5ndGggPT0gNjtcclxuICAgICAgICB9IGVsc2UgaWYgKHByb3BoZXRfaWQgPT0gNCkge1xyXG4gICAgICAgICAgbGV0IHJlZ2V4UGF0dGVybjtcclxuXHJcbiAgICAgICAgICByZWdleFBhdHRlcm4gPSBuZXcgUmVnRXhwKGBeKFtBLVowXXs0fSkyJHthY3R1YWxDdXJyfSRgKTtcclxuXHJcbiAgICAgICAgICBpc1ZhbGlkID0gcmVnZXhQYXR0ZXJuLnRlc3QocHJvcGhldENvZGUpO1xyXG4gICAgICAgIH1cclxuICAgICAgfVxyXG5cclxuICAgICAgaWYgKGNvZGVDb3VudCAmJiBjb2RlQ291bnQgPiAxICYmIHByb3BoZXRDb2RlICYmIHByb3BoZXRDb2RlICE9PSBcIlwiKSB7XHJcbiAgICAgICAgaXNFeHBvcnRhYmxlQmFzZWRPbkNvZGVVbmlxdWUgPSBmYWxzZTtcclxuICAgICAgfSBlbHNlIGlmIChcclxuICAgICAgICBjb2RlQ291bnQgJiZcclxuICAgICAgICBjb2RlQ291bnQgPT0gMSAmJlxyXG4gICAgICAgIHByb3BoZXRDb2RlICYmXHJcbiAgICAgICAgcHJvcGhldENvZGUgIT09IFwiXCJcclxuICAgICAgKSB7XHJcbiAgICAgICAgaXNFeHBvcnRhYmxlQmFzZWRPbkNvZGVVbmlxdWUgPSB0cnVlO1xyXG4gICAgICB9XHJcblxyXG4gICAgICBpZiAoIWlzRXhwb3J0YWJsZUJhc2VkT25Db2RlVW5pcXVlICYmICFpc1ZhbGlkKSB7XHJcbiAgICAgICAgdG9hc3QuZXJyb3IoXHJcbiAgICAgICAgICBcIlN1cHBsaWVyIGNvZGUgaXMgbm90IHVuaXF1ZSBhbmQgdmFsaWQsIGtpbmRseSBtYWtlIHN1cmUgdGhlIHN1cHBsaWVyIGNvZGUgaXMgdW5pcXVlIGFuZCBpcyB2YWxpZC5cIlxyXG4gICAgICAgICk7XHJcbiAgICAgICAgcmV0dXJuO1xyXG4gICAgICB9IGVsc2UgaWYgKCFpc0V4cG9ydGFibGVCYXNlZE9uQ29kZVVuaXF1ZSkge1xyXG4gICAgICAgIHRvYXN0LmVycm9yKFxyXG4gICAgICAgICAgXCJTdXBwbGllciBjb2RlIGlzIG5vdCB1bmlxdWUsIGtpbmRseSBtYWtlIHN1cmUgdGhlIHN1cHBsaWVyIGNvZGUgaXMgdW5pcXVlLlwiXHJcbiAgICAgICAgKTtcclxuICAgICAgICByZXR1cm47XHJcbiAgICAgIH0gZWxzZSBpZiAoIWlzVmFsaWQpIHtcclxuICAgICAgICB0b2FzdC5lcnJvcihcclxuICAgICAgICAgIFwiU3VwcGxpZXIgY29kZSBpcyBub3QgdmFpbGQsIGtpbmRseSBtYWtlIHN1cmUgdGhlIHN1cHBsaWVyIGNvZGUgaXMgdmFsaWQuXCJcclxuICAgICAgICApO1xyXG4gICAgICAgIHJldHVybjtcclxuICAgICAgfVxyXG4gICAgICBoYW5kbGVTaW5nbGVFeHBvcnRTdXBwbGllcihzdXBwbGllcl9pZCk7XHJcbiAgICB9IGVsc2Uge1xyXG4gICAgICBoYW5kbGVTaW5nbGVFeHBvcnRTdXBwbGllcihzdXBwbGllcl9pZCk7XHJcbiAgICB9XHJcbiAgfTtcclxuXHJcbiAgdXNlRWZmZWN0KCgpID0+IHtcclxuICAgIGlmIChzdXBwbGllcl9pZCkge1xyXG4gICAgICBzZXREYXRhKHN1cHBsaWVyRGF0YSk7XHJcbiAgICAgIHNldFN0YXR1cyhzdXBwbGllcl9zdGF0dXMpO1xyXG4gICAgfVxyXG4gIH0sIFtzdXBwbGllcl9pZF0pO1xyXG5cclxuICBjb25zdCBlZGl0U3VwcGxpZXIgPSAoKSA9PiB7XHJcbiAgICBpZiAodHlwZW9mIHdpbmRvdyAhPT0gXCJ1bmRlZmluZWRcIikge1xyXG4gICAgICByb3V0ZXIucHVzaCh7XHJcbiAgICAgICAgcGF0aG5hbWU6IGAvc3VwcGxpZXIvJHtzdXBwbGllcl9pZH0vZWRpdGAsXHJcbiAgICAgICAgLy8gcXVlcnk6IHsgaXRlbV9pZDogc3VwcGxpZXJfaWQgfSxcclxuICAgICAgfSk7XHJcbiAgICB9XHJcbiAgfTtcclxuICBjb25zdCBjb25maXJtUGFnZSA9ICgpID0+IHtcclxuICAgIGNvbnN0IG1hcHBlZFBlcm1pc3Npb25zID0gcm9sZV9pZHMubWFwKChyb2xlSWQpID0+ICh7XHJcbiAgICAgIHJvbGVJZDogcm9sZUlkLFxyXG4gICAgICBwZXJtaXNzaW9uczogcGVybWlzc2lvbnNbcm9sZUlkXSxcclxuICAgIH0pKTtcclxuICAgIGNvbnN0IHVuaXF1ZVNlY3Rpb25zID0gW1xyXG4gICAgICAuLi5uZXcgU2V0KG1hcHBlZFBlcm1pc3Npb25zLmZsYXRNYXAoKGl0ZW0pID0+IGl0ZW0ucGVybWlzc2lvbnMpKSxcclxuICAgIF07XHJcblxyXG4gICAgbG9jYWxTdG9yYWdlLnNldEl0ZW0oXCJhbGxvd2VkU2VjdGlvbnNcIiwgdW5pcXVlU2VjdGlvbnMpO1xyXG4gICAgaWYgKHR5cGVvZiB3aW5kb3cgIT09IFwidW5kZWZpbmVkXCIpIHtcclxuICAgICAgcm91dGVyLnB1c2goe1xyXG4gICAgICAgIHBhdGhuYW1lOiBgL3N1cHBsaWVyLyR7c3VwcGxpZXJfaWR9L2NvbmZpcm1gLFxyXG4gICAgICB9KTtcclxuICAgIH1cclxuICB9O1xyXG5cclxuICBmdW5jdGlvbiBnZXRHTENvZGUoaW50ZXJuYWxfbGVkZ2VyX2NvZGUsIGRlcGFydG1lbnQsIGN1cnJlbmN5LCByb2xlSWRzKSB7XHJcbiAgICBpZiAoaW50ZXJuYWxfbGVkZ2VyX2NvZGUpIHtcclxuICAgICAgcmV0dXJuIGludGVybmFsX2xlZGdlcl9jb2RlO1xyXG4gICAgfSBlbHNlIHtcclxuICAgICAgcmV0dXJuIFwiXCI7XHJcbiAgICB9XHJcbiAgfVxyXG5cclxuICBjb25zdCBleHRyYWN0Q29udGFjdHMgPSAoY29udGFjdHNKc29uU3RyKSA9PiB7XHJcbiAgICB0cnkge1xyXG4gICAgICBjb25zdCBjb250YWN0cyA9IGNvbnRhY3RzSnNvblN0ciA/IEpTT04ucGFyc2UoY29udGFjdHNKc29uU3RyKSA6IFtdO1xyXG4gICAgICBpZiAoQXJyYXkuaXNBcnJheShjb250YWN0cykpIHtcclxuICAgICAgICByZXR1cm4gY29udGFjdHMubWFwKChjb250YWN0KSA9PiAoe1xyXG4gICAgICAgICAgXCJTdXBwbGllciBjb2RlXCI6IGRhdGE/LnByb3BoZXRzWzBdPy5wcm9waGV0X2NvZGVcclxuICAgICAgICAgICAgPyBkYXRhPy5wcm9waGV0c1swXT8ucHJvcGhldF9jb2RlPy50cmltKClcclxuICAgICAgICAgICAgOiBcIlwiLCAvLyFoYXZlIGFkZGVkIHN1cHBsaWVyIGNvZGUgaGVyZSB3aGljaCBpcyB0byBrbm93IHVucW5pdWUgY29udGFjdHMgdGhpcyB3YXMgbm90IGluIHRoZWlyIGV4dHJhY3QgcHJvY2Vzc1xyXG4gICAgICAgICAgXCJDb250YWN0IElEXCI6IFwiXCIsXHJcbiAgICAgICAgICBOYW1lOiBkYXRhLmNvbXBhbnlfbmFtZSB8fCBcIlwiLCAvLyF0aGlzIGxvb2tzIGxpa2Ugc3VwcGxpZXIgbmFtZSBidXQgc2hvdWxkbnQgaXQgYmUgY29udGFjdCBuYW1lXHJcbiAgICAgICAgICBcIkVtYWlsIEFkZHJlc3NcIjogY29udGFjdC5lbWFpbF9pZCB8fCBcIlwiLFxyXG4gICAgICAgICAgXCJUZWxlcGhvbmUgbnVtYmVyXCI6IGNvbnRhY3QudGVsZXBob25lIHx8IFwiXCIsXHJcbiAgICAgICAgICBcIkNlbGwgcGhvbmUgbnVtYmVyXCI6IFwiXCIsXHJcbiAgICAgICAgICBcIkZheCBudW1iZXJcIjogXCJcIixcclxuICAgICAgICAgIFwiSW5zdGFudCBNZXNzYWdlXCI6IFwiXCIsXHJcbiAgICAgICAgICBcIlBoeXNpY2FsIEFkZHJlc3NcIjogXCJcIixcclxuICAgICAgICAgIFwiUG9zdGFsIEFkZHJlc3NcIjogXCJcIixcclxuICAgICAgICAgIFwiUm93IHZlcmlzaW9uXCI6IFwiXCIsXHJcbiAgICAgICAgICBcIkNyZWF0ZWQgdGltZXN0YW1wXCI6IFwiXCIsXHJcbiAgICAgICAgfSkpO1xyXG4gICAgICB9XHJcbiAgICB9IGNhdGNoIChlcnJvcikge1xyXG4gICAgICBjb25zb2xlLmVycm9yKFwiRXJyb3IgcGFyc2luZyBjb250YWN0c19qc29uOlwiLCBlcnJvcik7XHJcbiAgICB9XHJcblxyXG4gICAgcmV0dXJuIFtcclxuICAgICAge1xyXG4gICAgICAgIFwiQ29udGFjdCBJRFwiOiBcIlwiLFxyXG4gICAgICAgIE5hbWU6IGRhdGEuY29tcGFueV9uYW1lIHx8IFwiXCIsIC8vIXRoaXMgbG9va3MgbGlrZSBzdXBwbGllciBuYW1lIGJ1dCBzaG91bGRudCBpdCBiZSBjb250YWN0IG5hbWVcclxuICAgICAgICBcIkVtYWlsIEFkZHJlc3NcIjogXCJcIixcclxuICAgICAgICBcIlRlbGVwaG9uZSBudW1iZXJcIjogXCJcIixcclxuICAgICAgICBcIkNlbGwgcGhvbmUgbnVtYmVyXCI6IFwiXCIsXHJcbiAgICAgICAgXCJGYXggbnVtYmVyXCI6IFwiXCIsXHJcbiAgICAgICAgXCJJbnN0YW50IE1lc3NhZ2VcIjogXCJcIixcclxuICAgICAgICBcIlBoeXNpY2FsIEFkZHJlc3NcIjogXCJcIixcclxuICAgICAgICBcIlBvc3RhbCBBZGRyZXNzXCI6IFwiXCIsXHJcbiAgICAgICAgXCJSb3cgdmVyaXNpb25cIjogXCJcIixcclxuICAgICAgICBcIkNyZWF0ZWQgdGltZXN0YW1wXCI6IFwiXCIsXHJcbiAgICAgIH0sXHJcbiAgICBdO1xyXG4gIH07XHJcblxyXG4gIGNvbnN0IGV4dHJhY3RTZW5kYWNHcm91cCA9IChzZW5kYWNHcm91cEpzb24sIGlkKSA9PiB7XHJcbiAgICB0cnkge1xyXG4gICAgICBjb25zdCBzZW5kYWNHcm91cHMgPSBzZW5kYWNHcm91cEpzb24gPyBKU09OLnBhcnNlKHNlbmRhY0dyb3VwSnNvbikgOiBbXTtcclxuICAgICAgaWYgKEFycmF5LmlzQXJyYXkoc2VuZGFjR3JvdXBzKSkge1xyXG4gICAgICAgIGNvbnN0IGZpbHRlcmVkR3JvdXBzID0gc2VuZGFjR3JvdXBzLmZpbHRlcihcclxuICAgICAgICAgIChncm91cCkgPT4gZ3JvdXA/LmNyZWF0ZWRfYnkgPT09IGlkXHJcbiAgICAgICAgKTtcclxuICAgICAgICBpZiAoZmlsdGVyZWRHcm91cHMubGVuZ3RoID4gMCkge1xyXG4gICAgICAgICAgcmV0dXJuIGZpbHRlcmVkR3JvdXBzLm1hcCgoZ3JvdXApID0+ICh7XHJcbiAgICAgICAgICAgIFwiU3VwcGxpZXIgZ3JvdXBcIjogXCJcIixcclxuICAgICAgICAgICAgRGVzY3JpcHRpb246IGdyb3VwPy5jcmVhdGVkX2J5ID8gZ3JvdXA/LmxhYmVsIDogXCJcIixcclxuICAgICAgICAgIH0pKTtcclxuICAgICAgICB9IGVsc2Uge1xyXG4gICAgICAgICAgLy8gSGFuZGxlIHRoZSBjYXNlIHdoZW4gbm8gbWF0Y2hpbmcgZ3JvdXAgaXMgZm91bmRcclxuICAgICAgICAgIHJldHVybiBbXTsgLy8gb3IgYW55IG90aGVyIGRlZmF1bHQgdmFsdWUgb3IgYWN0aW9uXHJcbiAgICAgICAgfVxyXG4gICAgICB9XHJcbiAgICB9IGNhdGNoIChlcnJvcikge1xyXG4gICAgICBjb25zb2xlLmVycm9yKFwiRXJyb3IgcGFyc2luZyBjb250YWN0c19qc29uOlwiLCBlcnJvcik7XHJcbiAgICB9XHJcbiAgfTtcclxuICBjb25zdCBtdWx0aXBsZVNlbmRSb2xlT25Sb2xlTnVtcyA9ICgpID0+IHtcclxuICAgIGNvbnN0IHJvbGVOdW1zID0gZGF0YT8ucm9sZV9udW0/LnNwbGl0KFwiLFwiKS5tYXAoKG51bSkgPT4gbnVtLnRyaW0oKSk7XHJcbiAgICByZXR1cm4gcm9sZU51bXMubWFwKChudW0pID0+ICh7XHJcbiAgICAgIFNlbmRhY3JvbGVpZDogXCJcIixcclxuICAgICAgXCJTdXBwbGllciBjb2RlXCI6IGRhdGE/LnByb3BoZXRzWzBdPy5wcm9waGV0X2NvZGVcclxuICAgICAgICA/IGRhdGEucHJvcGhldHNbMF0ucHJvcGhldF9jb2RlLnRyaW0oKVxyXG4gICAgICAgIDogXCJcIixcclxuICAgICAgRGVzY3JpcHRpb246IGRhdGE/LnByb3BoZXRzWzBdPy5wcm9waGV0X2NvZGVcclxuICAgICAgICA/IGRhdGEucHJvcGhldHNbMF0ucHJvcGhldF9jb2RlLnRyaW0oKVxyXG4gICAgICAgIDogXCJcIiwgLy8gSXMgdGhpcyByb2xlIG5hbWU/XHJcbiAgICAgIFwiU3VwcGxpZXIgQ29kZSBTdXBwbGllciBOYW1lXCI6IGRhdGEuY29tcGFueV9uYW1lLFxyXG4gICAgICBUeXBlOiBudW0sXHJcbiAgICAgIFwiVHlwZSBEZXNjcmlwdGlvblwiOiBkYXRhPy5bXCJyb2xlIG5hbWVzXCJdLFxyXG4gICAgICBcIlN1cHBsaWVyIGNvZGUgR2xvYmFsIGdhcCBudW1iZXJcIjogZGF0YT8uZ2xvYmFsX2dhcF9udW1iZXIsXHJcbiAgICAgIFwiQ3JlYXRlZCB0aW1lc3RhbXBcIjogXCJcIixcclxuICAgICAgQWN0aXZlOiAxLFxyXG4gICAgfSkpO1xyXG4gIH07XHJcblxyXG4gIGNvbnN0IGhhbmRsZVNpbmdsZUV4cG9ydFN1cHBsaWVyID0gYXN5bmMgKGlkKSA9PiB7XHJcbiAgICBjb25zdCBjb21wYW55ID0gQ29va2llcy5nZXQoXCJjb21wYW55XCIpO1xyXG5cclxuICAgIC8vIHNldElzT3Blbk9wdGlvbihmYWxzZSk7XHJcbiAgICBsZXQgcm9sZXNBcnJheSA9IHBhcmFtcy5kYXRhLnJvbGVJZC5tYXAoKGVsZSkgPT4ge1xyXG4gICAgICByZXR1cm4gZWxlLnJvbGVfaWQ7XHJcbiAgICB9KTtcclxuXHJcbiAgICBjb25zdCBmb3JtYXR0ZWREaXN0cmlidXRpb25EYXRhID0gZGF0YT8uZGlzdHJpYnV0aW9uX3BvaW50c19qc29uPy5tYXAoXHJcbiAgICAgIChyb3cpID0+ICh7XHJcbiAgICAgICAgZGlzdHJpYnV0aW9uUG9pbnQ6IHJvdz8ubmFtZSxcclxuICAgICAgICBkaXJlY3REUHZhbHVlOiByb3cuZGlyZWN0X2RwID8gXCJUcnVlXCIgOiBcIkZhbHNlXCIsXHJcbiAgICAgICAgZGlyZWN0RFA6IHJvdy5kaXJlY3RfZHAsXHJcbiAgICAgICAgZnJvbV9kcDogcm93LmZyb21fZHAsXHJcbiAgICAgIH0pXHJcbiAgICApO1xyXG4gICAgbGV0IGZpbHRlcmVkSW50ZXJuYWxFeHBvcnREYXRhID0gW107XHJcbiAgICBsZXQgZmlsdGVyZWRJU1NFeHBvcnREYXRhID0gW107XHJcbiAgICAvLyBjb25zdCBpc0ludGVybmFsID0gc2VsZWN0ZWRFeHBvcnRUeXBlID09PSBcImludGVybmFsRXhwb3J0XCI7XHJcblxyXG4gICAgaWYgKFxyXG4gICAgICBzdXBwbGllcl9zdGF0dXMgPT09IFwiQ29tcGxldGVkXCIgfHxcclxuICAgICAgc3VwcGxpZXJfc3RhdHVzID09PSBcIkV4cG9ydGVkXCIgfHxcclxuICAgICAgKHBhcmFtcy5kYXRhLmlzRW1lcmdlbmN5UmVxdWVzdCAmJlxyXG4gICAgICAgIHBhcmFtcy5kYXRhLnN0YXR1cyAhPSBcIkNhbmNlbGxlZFwiICYmXHJcbiAgICAgICAgcGFyYW1zLmRhdGEuR2VuZXJhbCA9PT0gXCJDb21wbGV0ZVwiICYmXHJcbiAgICAgICAgKGRhdGE/LnJvbGVJZHMuaW5jbHVkZXMoMSl8fCBkYXRhPy5yb2xlSWRzPy5pbmNsdWRlcyg2KSkgJiZcclxuICAgICAgICBwYXJhbXMuZGF0YS5jdXJyZW5jeV9pZClcclxuICAgICkge1xyXG4gICAgICBpZiAodHJ1ZSkge1xyXG4gICAgICAgIGxldCBzb3J0X2NvZGUgPSBcIlwiO1xyXG4gICAgICAgIGxldCBhY2NvdW50X251bWJlciA9IFwiXCI7XHJcbiAgICAgICAgbGV0IHN3aWZ0QmljQ29kZSA9IFwiXCI7XHJcbiAgICAgICAgbGV0IGliYW4gPSBcIlwiO1xyXG5cclxuICAgICAgICBjb25zdCBzd2lmdEJpY1JlZ2V4ID1cclxuICAgICAgICAgIC9eKFtBLVowLTldezR9W0EtWjAtOV17Mn1bQS1aMC05XXsyfXxbQS1aMC05XXs0fVtBLVowLTldezJ9W0EtWjAtOV17Mn1bQS1aMC05XXszfSkkLztcclxuXHJcbiAgICAgICAgaWYgKHN3aWZ0QmljUmVnZXgudGVzdChkYXRhPy5kZWNyeXB0ZWRTb3J0X0JpYykgJiYgZGF0YT8uaGFzX2liYW4pIHtcclxuICAgICAgICAgIHNvcnRfY29kZSA9IFwiMDAwMDAwXCI7XHJcbiAgICAgICAgICBhY2NvdW50X251bWJlciA9IGRhdGE/LmRlY3J5cHRlZEFjY291bnROdW1iZXI/LnNsaWNlKC04KTtcclxuICAgICAgICAgIHN3aWZ0QmljQ29kZSA9IGRhdGE/LmRlY3J5cHRlZFNvcnRfQmljO1xyXG4gICAgICAgICAgaWJhbiA9IGRhdGE/LmRlY3J5cHRlZEFjY291bnROdW1iZXI7XHJcbiAgICAgICAgfSBlbHNlIGlmIChcclxuICAgICAgICAgICFkYXRhPy5oYXNfaWJhbiAmJlxyXG4gICAgICAgICAgc3dpZnRCaWNSZWdleC50ZXN0KGRhdGE/LmRlY3J5cHRlZFNvcnRfQmljKVxyXG4gICAgICAgICkge1xyXG4gICAgICAgICAgc29ydF9jb2RlID0gXCIwMDAwMDBcIjtcclxuICAgICAgICAgIGFjY291bnRfbnVtYmVyID0gZGF0YT8uZGVjcnlwdGVkQWNjb3VudE51bWJlcjtcclxuICAgICAgICAgIHN3aWZ0QmljQ29kZSA9IGRhdGE/LmRlY3J5cHRlZFNvcnRfQmljO1xyXG4gICAgICAgIH0gZWxzZSB7XHJcbiAgICAgICAgICBzb3J0X2NvZGUgPSBkYXRhPy5kZWNyeXB0ZWRTb3J0X0JpYztcclxuICAgICAgICAgIGFjY291bnRfbnVtYmVyID0gZGF0YT8uZGVjcnlwdGVkQWNjb3VudE51bWJlcjtcclxuICAgICAgICB9XHJcblxyXG4gICAgICAgIGxldCByZWdpb25hbF9jZXJ0ID0gXCJcIjtcclxuICAgICAgICBpZiAoZGF0YT8ucm9sZUlkcz8uaW5jbHVkZXMoMikgfHwgZGF0YT8ucm9sZUlkcz8uaW5jbHVkZXMoMykpIHtcclxuICAgICAgICAgIGlmIChkYXRhPy5jb3VudHJ5X2NvZGUgPT0gXCJVS1wiKSB7XHJcbiAgICAgICAgICAgIHJlZ2lvbmFsX2NlcnQgPSBkYXRhPy5yZWRfdHJhY3RvcjtcclxuICAgICAgICAgIH0gZWxzZSBpZiAoZGF0YT8uY291bnRyeV9jb2RlID09IFwiWkFcIikge1xyXG4gICAgICAgICAgICByZWdpb25hbF9jZXJ0ID0gZGF0YT8ucHVjX2NvZGU7XHJcbiAgICAgICAgICB9IGVsc2UgaWYgKGRhdGE/LmNvdW50cnlfY29kZSA9PSBcIkNMXCIpIHtcclxuICAgICAgICAgICAgcmVnaW9uYWxfY2VydCA9IGRhdGE/LmNoaWxlX2NlcnRpZmljYXRlX251bWJlcjtcclxuICAgICAgICAgIH1cclxuICAgICAgICB9XHJcbiAgICAgICAgbGV0IGN1cnJlbmN5SWQgPSBcIlwiO1xyXG4gICAgICAgIGxldCBjdXJyZW5jeU5hbWUgPSBcIlwiO1xyXG4gICAgICAgIGlmIChcclxuICAgICAgICAgIGRhdGE/LnJvbGVJZHMuaW5jbHVkZXMoMSkgfHxcclxuICAgICAgICAgIGRhdGE/LnJvbGVJZHMuaW5jbHVkZXMoNSkgfHxcclxuICAgICAgICAgIGRhdGE/LnJvbGVJZHM/LmluY2x1ZGVzKDYpXHJcbiAgICAgICAgKSB7XHJcbiAgICAgICAgICBjdXJyZW5jeUlkID0gZGF0YT8uY3VycmVuY3lfaWQgfHwgMTtcclxuICAgICAgICAgIGN1cnJlbmN5TmFtZSA9IGRhdGE/LmN1cnJlbmN5X25hbWUgfHwgXCJTdGVybGluZ1wiO1xyXG4gICAgICAgIH0gZWxzZSB7XHJcbiAgICAgICAgICBjdXJyZW5jeUlkID0gMTtcclxuICAgICAgICAgIGN1cnJlbmN5TmFtZSA9IFwiU3RlcmxpbmdcIjtcclxuICAgICAgICB9XHJcbiAgICAgICAgZnVuY3Rpb24gZ2V0Q29ycmVzcG9uZGluZ1VzZXJMb29rdXAoY3Vycikge1xyXG4gICAgICAgICAgaWYgKGN1cnIgPT0gXCJHQlBcIikge1xyXG4gICAgICAgICAgICByZXR1cm4gXCJHQlBCQUNTXCI7XHJcbiAgICAgICAgICB9IGVsc2UgaWYgKGN1cnIgPT0gXCJFVVJcIikge1xyXG4gICAgICAgICAgICByZXR1cm4gXCJFVVJPU0VQQVwiO1xyXG4gICAgICAgICAgfSBlbHNlIGlmIChjdXJyID09IFwiVVNEXCIpIHtcclxuICAgICAgICAgICAgcmV0dXJuIFwiVVNEUFJJT1JJVFlcIjtcclxuICAgICAgICAgIH0gZWxzZSB7XHJcbiAgICAgICAgICAgIHJldHVybiBcIlwiO1xyXG4gICAgICAgICAgfVxyXG4gICAgICAgIH1cclxuICAgICAgICBjb25zb2xlLmxvZyhcInN1cHBsaWVyIHR5cGVcIiwgZGF0YT8uc3VwcGxpZXJfdHlwZSk7XHJcblxyXG4gICAgICAgIGZpbHRlcmVkSW50ZXJuYWxFeHBvcnREYXRhID0gW1xyXG4gICAgICAgICAgW1xyXG4gICAgICAgICAgICBcIlVscEZpbFwiLFxyXG4gICAgICAgICAgICB7XHJcbiAgICAgICAgICAgICAgXCJEaXN0cmlidXRpb24gcG9pbnRcIjogXCJcIixcclxuICAgICAgICAgICAgICBEZXNjcmlwdGlvbjpcclxuICAgICAgICAgICAgICAgIGZvcm1hdHRlZERpc3RyaWJ1dGlvbkRhdGE/Lmxlbmd0aCA+IDAgJiZcclxuICAgICAgICAgICAgICAgIChmb3JtYXR0ZWREaXN0cmlidXRpb25EYXRhWzBdLmZyb21fZHAgPT09IG51bGwgfHxcclxuICAgICAgICAgICAgICAgICAgZm9ybWF0dGVkRGlzdHJpYnV0aW9uRGF0YVswXS5mcm9tX2RwID09PSB1bmRlZmluZWQpXHJcbiAgICAgICAgICAgICAgICAgID8gZm9ybWF0dGVkRGlzdHJpYnV0aW9uRGF0YVswXS5kaXN0cmlidXRpb25Qb2ludFxyXG4gICAgICAgICAgICAgICAgICA6IFwiXCIsXHJcbiAgICAgICAgICAgICAgXCJTZXJ2aWNlIFN1cHBsaWVyIENvZGVcIjpcclxuICAgICAgICAgICAgICAgIGZvcm1hdHRlZERpc3RyaWJ1dGlvbkRhdGE/Lmxlbmd0aCA+IDAgJiZcclxuICAgICAgICAgICAgICAgIChmb3JtYXR0ZWREaXN0cmlidXRpb25EYXRhWzBdLmZyb21fZHAgPT09IG51bGwgfHxcclxuICAgICAgICAgICAgICAgICAgZm9ybWF0dGVkRGlzdHJpYnV0aW9uRGF0YVswXS5mcm9tX2RwID09PSB1bmRlZmluZWQpICYmXHJcbiAgICAgICAgICAgICAgICBkYXRhPy5wcm9waGV0c1swXT8ucHJvcGhldF9jb2RlPy50cmltKCksXHJcbiAgICAgICAgICAgICAgXCJEZWZhdWx0IGV4cGVjdGVkIHN0b2NrIHN0YXR1c1wiOlxyXG4gICAgICAgICAgICAgICAgZm9ybWF0dGVkRGlzdHJpYnV0aW9uRGF0YT8ubGVuZ3RoID4gMCAmJlxyXG4gICAgICAgICAgICAgICAgKGZvcm1hdHRlZERpc3RyaWJ1dGlvbkRhdGFbMF0uZnJvbV9kcCA9PT0gbnVsbCB8fFxyXG4gICAgICAgICAgICAgICAgICBmb3JtYXR0ZWREaXN0cmlidXRpb25EYXRhWzBdLmZyb21fZHAgPT09IHVuZGVmaW5lZCkgJiZcclxuICAgICAgICAgICAgICAgIFwiMFwiLFxyXG4gICAgICAgICAgICAgIFwiRGVmYXVsdCByZWNlaXZlZCBzdG9jayBzdGF0dXNcIjpcclxuICAgICAgICAgICAgICAgIGZvcm1hdHRlZERpc3RyaWJ1dGlvbkRhdGE/Lmxlbmd0aCA+IDAgJiZcclxuICAgICAgICAgICAgICAgIChmb3JtYXR0ZWREaXN0cmlidXRpb25EYXRhWzBdLmZyb21fZHAgPT09IG51bGwgfHxcclxuICAgICAgICAgICAgICAgICAgZm9ybWF0dGVkRGlzdHJpYnV0aW9uRGF0YVswXS5mcm9tX2RwID09PSB1bmRlZmluZWQpICYmXHJcbiAgICAgICAgICAgICAgICBcIjBcIixcclxuICAgICAgICAgICAgICBcIlBhbGxldHMgaW4gcGFja2hvdXNlXCI6XHJcbiAgICAgICAgICAgICAgICBmb3JtYXR0ZWREaXN0cmlidXRpb25EYXRhPy5sZW5ndGggPiAwICYmXHJcbiAgICAgICAgICAgICAgICAoZm9ybWF0dGVkRGlzdHJpYnV0aW9uRGF0YVswXS5mcm9tX2RwID09PSBudWxsIHx8XHJcbiAgICAgICAgICAgICAgICAgIGZvcm1hdHRlZERpc3RyaWJ1dGlvbkRhdGFbMF0uZnJvbV9kcCA9PT0gdW5kZWZpbmVkKSAmJlxyXG4gICAgICAgICAgICAgICAgXCIwXCIsXHJcbiAgICAgICAgICAgICAgXCJEZWZhdWx0IGhhdWxpZXJcIjpcclxuICAgICAgICAgICAgICAgIGZvcm1hdHRlZERpc3RyaWJ1dGlvbkRhdGE/Lmxlbmd0aCA+IDAgJiZcclxuICAgICAgICAgICAgICAgIChmb3JtYXR0ZWREaXN0cmlidXRpb25EYXRhWzBdLmZyb21fZHAgPT09IG51bGwgfHxcclxuICAgICAgICAgICAgICAgICAgZm9ybWF0dGVkRGlzdHJpYnV0aW9uRGF0YVswXS5mcm9tX2RwID09PSB1bmRlZmluZWQpICYmXHJcbiAgICAgICAgICAgICAgICBcIlpaWlpaXCIsXHJcbiAgICAgICAgICAgICAgXCJEZWZhdWx0IGV4cGVjdGVkIGxvY2F0aW9uIGlkXCI6XHJcbiAgICAgICAgICAgICAgICBmb3JtYXR0ZWREaXN0cmlidXRpb25EYXRhPy5sZW5ndGggPiAwICYmXHJcbiAgICAgICAgICAgICAgICAoZm9ybWF0dGVkRGlzdHJpYnV0aW9uRGF0YVswXS5mcm9tX2RwID09PSBudWxsIHx8XHJcbiAgICAgICAgICAgICAgICAgIGZvcm1hdHRlZERpc3RyaWJ1dGlvbkRhdGFbMF0uZnJvbV9kcCA9PT0gdW5kZWZpbmVkKSAmJlxyXG4gICAgICAgICAgICAgICAgMjEsXHJcbiAgICAgICAgICAgICAgXCJEZWZhdWx0IHJlY2VpdmluZyBsb2NhdGlvbiBpZFwiOlxyXG4gICAgICAgICAgICAgICAgZm9ybWF0dGVkRGlzdHJpYnV0aW9uRGF0YT8ubGVuZ3RoID4gMCAmJlxyXG4gICAgICAgICAgICAgICAgKGZvcm1hdHRlZERpc3RyaWJ1dGlvbkRhdGFbMF0uZnJvbV9kcCA9PT0gbnVsbCB8fFxyXG4gICAgICAgICAgICAgICAgICBmb3JtYXR0ZWREaXN0cmlidXRpb25EYXRhWzBdLmZyb21fZHAgPT09IHVuZGVmaW5lZCkgJiZcclxuICAgICAgICAgICAgICAgIDIxLFxyXG4gICAgICAgICAgICAgIFwiUGFja2hvdXNlIGxvY2F0aW9uIGlkXCI6XHJcbiAgICAgICAgICAgICAgICBmb3JtYXR0ZWREaXN0cmlidXRpb25EYXRhPy5sZW5ndGggPiAwICYmXHJcbiAgICAgICAgICAgICAgICAoZm9ybWF0dGVkRGlzdHJpYnV0aW9uRGF0YVswXS5mcm9tX2RwID09PSBudWxsIHx8XHJcbiAgICAgICAgICAgICAgICAgIGZvcm1hdHRlZERpc3RyaWJ1dGlvbkRhdGFbMF0uZnJvbV9kcCA9PT0gdW5kZWZpbmVkKSAmJlxyXG4gICAgICAgICAgICAgICAgMjEsXHJcbiAgICAgICAgICAgICAgXCJEZWZhdWx0IHBpY2sgbG9jYXRpb24gaWRcIjpcclxuICAgICAgICAgICAgICAgIGZvcm1hdHRlZERpc3RyaWJ1dGlvbkRhdGE/Lmxlbmd0aCA+IDAgJiZcclxuICAgICAgICAgICAgICAgIChmb3JtYXR0ZWREaXN0cmlidXRpb25EYXRhWzBdLmZyb21fZHAgPT09IG51bGwgfHxcclxuICAgICAgICAgICAgICAgICAgZm9ybWF0dGVkRGlzdHJpYnV0aW9uRGF0YVswXS5mcm9tX2RwID09PSB1bmRlZmluZWQpICYmXHJcbiAgICAgICAgICAgICAgICAyMSxcclxuICAgICAgICAgICAgICBcIkRlc3BhdGNoIGxvY2F0aW9uIGlkXCI6XHJcbiAgICAgICAgICAgICAgICBmb3JtYXR0ZWREaXN0cmlidXRpb25EYXRhPy5sZW5ndGggPiAwICYmXHJcbiAgICAgICAgICAgICAgICAoZm9ybWF0dGVkRGlzdHJpYnV0aW9uRGF0YVswXS5mcm9tX2RwID09PSBudWxsIHx8XHJcbiAgICAgICAgICAgICAgICAgIGZvcm1hdHRlZERpc3RyaWJ1dGlvbkRhdGFbMF0uZnJvbV9kcCA9PT0gdW5kZWZpbmVkKSAmJlxyXG4gICAgICAgICAgICAgICAgMjEsXHJcbiAgICAgICAgICAgICAgXCJEZWZhdWx0IHdhc3RlIGxvY2F0aW9uIGlkXCI6XHJcbiAgICAgICAgICAgICAgICBmb3JtYXR0ZWREaXN0cmlidXRpb25EYXRhPy5sZW5ndGggPiAwICYmXHJcbiAgICAgICAgICAgICAgICAoZm9ybWF0dGVkRGlzdHJpYnV0aW9uRGF0YVswXS5mcm9tX2RwID09PSBudWxsIHx8XHJcbiAgICAgICAgICAgICAgICAgIGZvcm1hdHRlZERpc3RyaWJ1dGlvbkRhdGFbMF0uZnJvbV9kcCA9PT0gdW5kZWZpbmVkKSAmJlxyXG4gICAgICAgICAgICAgICAgMjEsXHJcbiAgICAgICAgICAgICAgXCJEZWZhdWx0IHByZS1waWNrIGxvY2F0aW9uIGlkXCI6XHJcbiAgICAgICAgICAgICAgICBmb3JtYXR0ZWREaXN0cmlidXRpb25EYXRhPy5sZW5ndGggPiAwICYmXHJcbiAgICAgICAgICAgICAgICAoZm9ybWF0dGVkRGlzdHJpYnV0aW9uRGF0YVswXS5mcm9tX2RwID09PSBudWxsIHx8XHJcbiAgICAgICAgICAgICAgICAgIGZvcm1hdHRlZERpc3RyaWJ1dGlvbkRhdGFbMF0uZnJvbV9kcCA9PT0gdW5kZWZpbmVkKSAmJlxyXG4gICAgICAgICAgICAgICAgMjEsXHJcbiAgICAgICAgICAgICAgXCJEZWZhdWx0IHJldHVybnMgbG9jYXRpb24gaWRcIjpcclxuICAgICAgICAgICAgICAgIGZvcm1hdHRlZERpc3RyaWJ1dGlvbkRhdGE/Lmxlbmd0aCA+IDAgJiZcclxuICAgICAgICAgICAgICAgIChmb3JtYXR0ZWREaXN0cmlidXRpb25EYXRhWzBdLmZyb21fZHAgPT09IG51bGwgfHxcclxuICAgICAgICAgICAgICAgICAgZm9ybWF0dGVkRGlzdHJpYnV0aW9uRGF0YVswXS5mcm9tX2RwID09PSB1bmRlZmluZWQpICYmXHJcbiAgICAgICAgICAgICAgICAyMSxcclxuICAgICAgICAgICAgICBBZGRyZXNzOiBcIlwiLFxyXG4gICAgICAgICAgICAgIFwiU2VydmljZSBzdXBwbGllciBjb2RlXCI6IFwiXCIsXHJcbiAgICAgICAgICAgICAgXCJFREkgUmVmZXJlbmNlIENvZGVcIjogXCJcIixcclxuICAgICAgICAgICAgICBcIkVESSBBTkEgQ29kZVwiOiBcIlwiLFxyXG4gICAgICAgICAgICAgIFwiVXNlciBJbnRlZ2VyIDFcIjpcclxuICAgICAgICAgICAgICAgIGZvcm1hdHRlZERpc3RyaWJ1dGlvbkRhdGE/Lmxlbmd0aCA+IDAgJiZcclxuICAgICAgICAgICAgICAgIChmb3JtYXR0ZWREaXN0cmlidXRpb25EYXRhWzBdLmZyb21fZHAgPT09IG51bGwgfHxcclxuICAgICAgICAgICAgICAgICAgZm9ybWF0dGVkRGlzdHJpYnV0aW9uRGF0YVswXS5mcm9tX2RwID09PSB1bmRlZmluZWQpICYmXHJcbiAgICAgICAgICAgICAgICAxLFxyXG4gICAgICAgICAgICAgIFwiTW92ZW1lbnQgcmVzb3VyY2UgZ3JvdXBcIjogXCJcIixcclxuICAgICAgICAgICAgICBcIkhhbmRoZWxkIGFwcGxpY2F0aW9uIHVzZWRcIjpcclxuICAgICAgICAgICAgICAgIGZvcm1hdHRlZERpc3RyaWJ1dGlvbkRhdGE/Lmxlbmd0aCA+IDAgJiZcclxuICAgICAgICAgICAgICAgIChmb3JtYXR0ZWREaXN0cmlidXRpb25EYXRhWzBdLmZyb21fZHAgPT09IG51bGwgfHxcclxuICAgICAgICAgICAgICAgICAgZm9ybWF0dGVkRGlzdHJpYnV0aW9uRGF0YVswXS5mcm9tX2RwID09PSB1bmRlZmluZWQpICYmXHJcbiAgICAgICAgICAgICAgICBcIjBcIixcclxuICAgICAgICAgICAgICBcIlBhbGxldHMgaW4gcHJvY3VyZS9yZWNlaXZpbmdcIjpcclxuICAgICAgICAgICAgICAgIGZvcm1hdHRlZERpc3RyaWJ1dGlvbkRhdGE/Lmxlbmd0aCA+IDAgJiZcclxuICAgICAgICAgICAgICAgIChmb3JtYXR0ZWREaXN0cmlidXRpb25EYXRhWzBdLmZyb21fZHAgPT09IG51bGwgfHxcclxuICAgICAgICAgICAgICAgICAgZm9ybWF0dGVkRGlzdHJpYnV0aW9uRGF0YVswXS5mcm9tX2RwID09PSB1bmRlZmluZWQpICYmXHJcbiAgICAgICAgICAgICAgICBcIjBcIixcclxuICAgICAgICAgICAgICBcIk9wZXJhdGlvbmFsIGRlcG9cIjogXCJcIixcclxuICAgICAgICAgICAgICBcIkVuYWJsZWQgZm9yIG1hc3RlcmZpbGUgc2VuZGluZ1wiOiBcIlwiLFxyXG4gICAgICAgICAgICAgIFwiQ29ubmVjdGVkIHJlZ2lzdGVkIGRlcG90XCI6IFwiXCIsXHJcbiAgICAgICAgICAgICAgXCJFREkgVHJhbnNtaXNzaW9uIHR5cGUgb2YgZGVwb1wiOiBcIlwiLFxyXG4gICAgICAgICAgICAgIFwiQ29udGFpbmVyIGxvYWRpbmcgZGVwb1wiOlxyXG4gICAgICAgICAgICAgICAgZm9ybWF0dGVkRGlzdHJpYnV0aW9uRGF0YT8ubGVuZ3RoID4gMCAmJlxyXG4gICAgICAgICAgICAgICAgKGZvcm1hdHRlZERpc3RyaWJ1dGlvbkRhdGFbMF0uZnJvbV9kcCA9PT0gbnVsbCB8fFxyXG4gICAgICAgICAgICAgICAgICBmb3JtYXR0ZWREaXN0cmlidXRpb25EYXRhWzBdLmZyb21fZHAgPT09IHVuZGVmaW5lZCkgJiZcclxuICAgICAgICAgICAgICAgIFwiMFwiLFxyXG4gICAgICAgICAgICAgIFwiQWlycG9ydCBkZXBvdFwiOiBcIlwiLFxyXG4gICAgICAgICAgICAgIFwiU21zIG5vdGlmaWNhdGlvblwiOiBcIlwiLFxyXG4gICAgICAgICAgICAgIFBvcnQ6IFwiXCIsXHJcbiAgICAgICAgICAgICAgRG9ybWFudDogXCJcIixcclxuICAgICAgICAgICAgICBBY3RpdmU6XHJcbiAgICAgICAgICAgICAgICBmb3JtYXR0ZWREaXN0cmlidXRpb25EYXRhPy5sZW5ndGggPiAwICYmXHJcbiAgICAgICAgICAgICAgICAoZm9ybWF0dGVkRGlzdHJpYnV0aW9uRGF0YVswXS5mcm9tX2RwID09PSBudWxsIHx8XHJcbiAgICAgICAgICAgICAgICAgIGZvcm1hdHRlZERpc3RyaWJ1dGlvbkRhdGFbMF0uZnJvbV9kcCA9PT0gdW5kZWZpbmVkKSAmJlxyXG4gICAgICAgICAgICAgICAgMSxcclxuICAgICAgICAgICAgICBcIkluZ3JlZGllbnQgZGlzdHJpYnV0aW9uIHBvaW50XCI6IFwiXCIsXHJcbiAgICAgICAgICAgICAgXCJTaG93IGluIENFXCI6XHJcbiAgICAgICAgICAgICAgICBmb3JtYXR0ZWREaXN0cmlidXRpb25EYXRhPy5sZW5ndGggPiAwICYmXHJcbiAgICAgICAgICAgICAgICAoZm9ybWF0dGVkRGlzdHJpYnV0aW9uRGF0YVswXS5mcm9tX2RwID09PSBudWxsIHx8XHJcbiAgICAgICAgICAgICAgICAgIGZvcm1hdHRlZERpc3RyaWJ1dGlvbkRhdGFbMF0uZnJvbV9kcCA9PT0gdW5kZWZpbmVkKSAmJlxyXG4gICAgICAgICAgICAgICAgMSxcclxuICAgICAgICAgICAgICBcIkNoYXJnZSBkaXJlY3Rpb25cIjogXCJcIixcclxuICAgICAgICAgICAgICBcIlBhbGxldCByZWNlaXZlIHRpbWVcIjogXCJcIixcclxuICAgICAgICAgICAgICBcIlVzZXIgc3RyaW5nIDNcIjogXCJcIixcclxuICAgICAgICAgICAgICBcIkRpcmVjdCBEUFwiOlxyXG4gICAgICAgICAgICAgICAgZm9ybWF0dGVkRGlzdHJpYnV0aW9uRGF0YT8ubGVuZ3RoID4gMCAmJlxyXG4gICAgICAgICAgICAgICAgKGZvcm1hdHRlZERpc3RyaWJ1dGlvbkRhdGFbMF0uZnJvbV9kcCA9PT0gbnVsbCB8fFxyXG4gICAgICAgICAgICAgICAgICBmb3JtYXR0ZWREaXN0cmlidXRpb25EYXRhWzBdLmZyb21fZHAgPT09IHVuZGVmaW5lZClcclxuICAgICAgICAgICAgICAgICAgPyBmb3JtYXR0ZWREaXN0cmlidXRpb25EYXRhWzBdPy5kaXJlY3REUFxyXG4gICAgICAgICAgICAgICAgICAgID8gMVxyXG4gICAgICAgICAgICAgICAgICAgIDogXCIwXCJcclxuICAgICAgICAgICAgICAgICAgOiBcIlwiLFxyXG4gICAgICAgICAgICAgIFwiSW5jbHVkZSBvbiBYTUxcIjpcclxuICAgICAgICAgICAgICAgIGZvcm1hdHRlZERpc3RyaWJ1dGlvbkRhdGE/Lmxlbmd0aCA+IDAgJiZcclxuICAgICAgICAgICAgICAgIChmb3JtYXR0ZWREaXN0cmlidXRpb25EYXRhWzBdLmZyb21fZHAgPT09IG51bGwgfHxcclxuICAgICAgICAgICAgICAgICAgZm9ybWF0dGVkRGlzdHJpYnV0aW9uRGF0YVswXS5mcm9tX2RwID09PSB1bmRlZmluZWQpICYmXHJcbiAgICAgICAgICAgICAgICBcIjBcIixcclxuICAgICAgICAgICAgfSxcclxuICAgICAgICAgIF0sXHJcbiAgICAgICAgICBbXHJcbiAgICAgICAgICAgIFwic2VuZGFjIChTdXBwbGllciBmaWxlKVwiLFxyXG4gICAgICAgICAgICB7XHJcbiAgICAgICAgICAgICAgXCJTdXBwbGllciBBY3RpdmVcIjogZGF0YT8uaXNBY3RpdmUgPyAxIDogMCxcclxuICAgICAgICAgICAgICBcIkhhdWxhZ2UgY3ViZSBsb2NhbFwiOiBcIlwiLFxyXG4gICAgICAgICAgICAgIFwiSGF1bGFnZSBjdWJlIG5hbWVcIjogXCJcIixcclxuICAgICAgICAgICAgICBcInVwZGF0ZSBndWVzc3RpbWF0ZXMgdHlwZVwiOiAxLFxyXG4gICAgICAgICAgICAgIFwiT3JnYW5pemF0aW9uIElEXCI6IFwiXCIsXHJcbiAgICAgICAgICAgICAgXCJWYXQgbnVtYmVyIDFcIjogZGF0YT8udmF0X251bWJlcixcclxuICAgICAgICAgICAgICBcIk9yZ2FuaWMgY2VydFwiOiBkYXRhPy5vcmdhbmljX2NlcnRpZmljYXRlX251bWJlcixcclxuICAgICAgICAgICAgICBcIlJlZ2lvbmFsIGNlcnRcIjogcmVnaW9uYWxfY2VydCxcclxuICAgICAgICAgICAgICBcIkdsb2JhbCBnYXAgbnVtYmVyXCI6IGRhdGE/Lmdsb2JhbF9nYXBfbnVtYmVyLFxyXG4gICAgICAgICAgICAgIFwiRW5mb3JjZSBkZXBhcnRtZW50XCI6IFwiXCIsXHJcbiAgICAgICAgICAgICAgXCJTZW5kYWMgR3JvdXBcIjogZGF0YT8uc3VwcGxpZXJfZ3JvdXBcclxuICAgICAgICAgICAgICAgID8gSlNPTi5wYXJzZShkYXRhLnN1cHBsaWVyX2dyb3VwKVswXS52YWx1ZVxyXG4gICAgICAgICAgICAgICAgOiBcIlwiLFxyXG4gICAgICAgICAgICAgIFwiU3VwcGxpZXIgY29kZVwiOiBkYXRhPy5wcm9waGV0c1swXT8ucHJvcGhldF9jb2RlPy50cmltKCksXHJcbiAgICAgICAgICAgICAgXCJTdXBwbGllciBuYW1lXCI6IGRhdGEuY29tcGFueV9uYW1lLFxyXG4gICAgICAgICAgICAgIFwiU3VwcGxpZXIgdHlwZVwiOiBkYXRhPy5zdXBwbGllcl90eXBlLFxyXG4gICAgICAgICAgICAgIFwiVXNlciBMb29rdXAgMlwiOiBcIlwiLFxyXG4gICAgICAgICAgICAgIFwiQWRkcmVzcyBMaW5lIDFcIjogZGF0YT8uYWRkcmVzc19saW5lXzEsXHJcbiAgICAgICAgICAgICAgXCJBZGRyZXNzIExpbmUgMlwiOiBkYXRhPy5hZGRyZXNzX2xpbmVfMixcclxuICAgICAgICAgICAgICBcIkFkZHJlc3MgTGluZSAzXCI6IGRhdGE/LmFkZHJlc3NfbGluZV8zLFxyXG4gICAgICAgICAgICAgIFwiQWRkcmVzcyBMaW5lIDRcIjogZGF0YT8uYWRkcmVzc19saW5lXzQsXHJcbiAgICAgICAgICAgICAgXCJQb3N0IGNvZGVcIjogZGF0YT8ucG9zdGFsX2NvZGUsXHJcbiAgICAgICAgICAgICAgXCJDb3VudHJ5IENvZGVcIjogZGF0YT8uY291bnRyeV9jb2RlLFxyXG4gICAgICAgICAgICAgIFwiUGF5ZWUgc3VwcGxpZXIgY29kZVwiOiBcIlwiLFxyXG4gICAgICAgICAgICAgIFwiSW52b2ljZSBzdXBwbGllclwiOiBcIlwiLFxyXG4gICAgICAgICAgICAgIFwiSGVhZCBvZmZpY2VcIjogXCJcIixcclxuICAgICAgICAgICAgICBcIlNldHRsZW1lbnQgZGF5c1wiOiBkYXRhPy5wYXltZW50X3Rlcm1zLFxyXG4gICAgICAgICAgICAgIFwiQmFuayBnZW5lcmFsIGxlZGdlciBjb2RlIEN1cnJlbmN5IG51bWJlciBpZiBiYW5rXCI6IGN1cnJlbmN5SWQsXHJcbiAgICAgICAgICAgICAgXCJDdXJyZW5jeSBudW1iZXJcIjogY3VycmVuY3lJZCxcclxuICAgICAgICAgICAgICBcIkN1cnJlbmN5IG51bWJlciBDdXJyZW5jeSBuYW1lXCI6IGN1cnJlbmN5TmFtZSxcclxuICAgICAgICAgICAgICBcIkJhbmsgZ2VuZXJhbCBsZWRnZXIgY29kZVwiOiBnZXRHTENvZGUoXHJcbiAgICAgICAgICAgICAgICBkYXRhPy5pbnRlcm5hbF9sZWRnZXJfY29kZSxcclxuICAgICAgICAgICAgICAgIGRhdGE/LnByb3BoZXRzWzBdPy5wcm9waGV0X2lkLFxyXG4gICAgICAgICAgICAgICAgZGF0YT8uY3VycmVuY3lfY29kZSxcclxuICAgICAgICAgICAgICAgIGRhdGE/LnJvbGVJZHNcclxuICAgICAgICAgICAgICApLFxyXG4gICAgICAgICAgICAgIFwicGF5bWVudCBUeXBlXCI6IGRhdGE/LnBheW1lbnRfdHlwZSxcclxuICAgICAgICAgICAgICBcInBheW1lbnQgdHlwZSBuYW1lXCI6IGRhdGE/LnBheW1lbnRfdHlwZV9uYW1lLFxyXG4gICAgICAgICAgICAgIFwiQ291bnRyeSBjb2RlXCI6IGRhdGE/LmNvdW50cnlfY29kZSxcclxuICAgICAgICAgICAgICBWYXRhYmxlOlxyXG4gICAgICAgICAgICAgICAgZGF0YT8udmF0YWJsZSAhPSBudWxsID8gKGRhdGE/LnZhdGFibGUgPyBcIjFcIiA6IFwiMFwiKSA6IFwiMFwiLFxyXG4gICAgICAgICAgICAgIFwidmF0YWJsZSBkZXNjXCI6XHJcbiAgICAgICAgICAgICAgICBkYXRhPy52YXRhYmxlICE9IG51bGxcclxuICAgICAgICAgICAgICAgICAgPyBkYXRhPy52YXRhYmxlXHJcbiAgICAgICAgICAgICAgICAgICAgPyBcIlZhdGFibGVcIlxyXG4gICAgICAgICAgICAgICAgICAgIDogXCJOb25lIHZhdGFibGVcIlxyXG4gICAgICAgICAgICAgICAgICA6IFwiTm9uZSB2YXRhYmxlXCIsXHJcbiAgICAgICAgICAgICAgXCJBcmVhIE51bWJlclwiOlxyXG4gICAgICAgICAgICAgICAgZGF0YT8ucHJvcGhldHNbMF0ucHJvcGhldF9pZCA9PSAzIHx8XHJcbiAgICAgICAgICAgICAgICBkYXRhPy5wcm9waGV0c1swXS5wcm9waGV0X2lkID09IDRcclxuICAgICAgICAgICAgICAgICAgPyAxXHJcbiAgICAgICAgICAgICAgICAgIDogNyxcclxuICAgICAgICAgICAgICBCdXllcjogMSxcclxuICAgICAgICAgICAgICBcIk11bHRpcGxlIExvdCBJbmRpY2F0b3JcIjogXCIwXCIsXHJcbiAgICAgICAgICAgICAgXCJtdWx0aXBsZSBsb3QgaW5kaWNhdG9yIGRlc2NcIjogXCJCeSBMb3RcIixcclxuICAgICAgICAgICAgICBcIkdlbmVyYXRlIFBhbGxldCBMb2FkaW5nIFBsYW5cIjogXCJcIixcclxuICAgICAgICAgICAgICBcIkRpc3RyaWJ1dGlvbiBwb2ludCBmb3Igc3VwcGxpZXJcIjogNixcclxuICAgICAgICAgICAgICBcIlBheW1lbnQgdGVybXNcIjogXCJcIixcclxuICAgICAgICAgICAgICBcIkRlcGFydG1lbnQgTnVtYmVyXCI6XHJcbiAgICAgICAgICAgICAgICBkYXRhPy5wcm9waGV0c1swXT8ucHJvcGhldF9pZCA9PSAxXHJcbiAgICAgICAgICAgICAgICAgID8gMVxyXG4gICAgICAgICAgICAgICAgICA6IGRhdGE/LnByb3BoZXRzWzBdPy5wcm9waGV0X2lkID09IDJcclxuICAgICAgICAgICAgICAgICAgPyA5XHJcbiAgICAgICAgICAgICAgICAgIDogZGF0YT8ucHJvcGhldHNbMF0ucHJvcGhldF9pZCA9PSAzXHJcbiAgICAgICAgICAgICAgICAgID8gM1xyXG4gICAgICAgICAgICAgICAgICA6IGRhdGE/LnByb3BoZXRzWzBdLnByb3BoZXRfaWQgPT0gNFxyXG4gICAgICAgICAgICAgICAgICA/IDJcclxuICAgICAgICAgICAgICAgICAgOiBcIlwiLFxyXG4gICAgICAgICAgICAgIFwiQWxsb3cgY3JlZGl0IHJlYmF0ZXNcIjogXCJcIixcclxuICAgICAgICAgICAgICBcIkFsdGVybmF0aXZlIERQIGZvciBzdXBwbGllclwiOiAxLFxyXG4gICAgICAgICAgICAgIFwiQWN0dWFsIHBvc3Rpbmcgc3RvcHMgcHVyY2hhc2UgY2hhcmdlc1wiOiBcIlwiLFxyXG4gICAgICAgICAgICAgIFwiQXV0aG9yaXNlIG9uIHJlZ2lzdGVyXCI6IFwiXCIsXHJcbiAgICAgICAgICAgICAgXCJVc2VyIHRleHQgMVwiOiBcIlwiLFxyXG4gICAgICAgICAgICAgIFwiVXNlciBsb29rdXAgMVwiOlxyXG4gICAgICAgICAgICAgICAgZGF0YT8ucHJvcGhldHNbMF0ucHJvcGhldF9pZCA9PSAzIHx8XHJcbiAgICAgICAgICAgICAgICBkYXRhPy5wcm9waGV0c1swXS5wcm9waGV0X2lkID09IDRcclxuICAgICAgICAgICAgICAgICAgPyBnZXRDb3JyZXNwb25kaW5nVXNlckxvb2t1cChkYXRhPy5jdXJyZW5jeV9jb2RlKVxyXG4gICAgICAgICAgICAgICAgICA6IFwiXCIsXHJcbiAgICAgICAgICAgICAgXCJSZWNlaXZlIG9yZGVycyBmcm9tIGVkaVwiOiBcIlwiLFxyXG4gICAgICAgICAgICAgIFwiU2VuZCBpbnZvaWNlcyBmcm9tIGVkaVwiOiBcIlwiLFxyXG4gICAgICAgICAgICAgIFwic2VuZCBvcmRlcnMgZnJvbSBlZGlcIjogXCJcIixcclxuICAgICAgICAgICAgICBcIkVESSBwYXJ0bmVyXCI6XHJcbiAgICAgICAgICAgICAgICBkYXRhPy5wcm9waGV0c1swXS5wcm9waGV0X2lkID09IDMgfHxcclxuICAgICAgICAgICAgICAgIGRhdGE/LnByb3BoZXRzWzBdLnByb3BoZXRfaWQgPT0gNFxyXG4gICAgICAgICAgICAgICAgICA/IDIwMDBcclxuICAgICAgICAgICAgICAgICAgOiBcIlwiLFxyXG4gICAgICAgICAgICAgIFwiR2VuZXJpYyBjb2RlXCI6XHJcbiAgICAgICAgICAgICAgICBkYXRhPy5wcm9waGV0c1swXS5wcm9waGV0X2lkID09IDMgfHxcclxuICAgICAgICAgICAgICAgIGRhdGE/LnByb3BoZXRzWzBdLnByb3BoZXRfaWQgPT0gNFxyXG4gICAgICAgICAgICAgICAgICA/IFwiU1RPQ0tcIlxyXG4gICAgICAgICAgICAgICAgICA6IFwiXCIsXHJcbiAgICAgICAgICAgICAgXCJFREkgQU5BIG51bWJlclwiOiBkYXRhPy5lZGkgPz8gXCJcIixcclxuICAgICAgICAgICAgICBcIlVzZXIgJSBhdXRob3JpemUgcnVsZVwiOiA1LFxyXG4gICAgICAgICAgICAgIEZyb21EUDpcclxuICAgICAgICAgICAgICAgIGZvcm1hdHRlZERpc3RyaWJ1dGlvbkRhdGE/Lmxlbmd0aCA+IDBcclxuICAgICAgICAgICAgICAgICAgPyBmb3JtYXR0ZWREaXN0cmlidXRpb25EYXRhWzBdLmZyb21fZHAgfHwgXCJcIlxyXG4gICAgICAgICAgICAgICAgICA6IFwiXCIsXHJcbiAgICAgICAgICAgIH0sXHJcbiAgICAgICAgICBdLFxyXG4gICAgICAgICAgW1wic2VuZGFjcm9sZSAoU3VwcGxpZXIgcm9sZSBmaWxlKVwiXSxcclxuXHJcbiAgICAgICAgICBbXCJzZW5kYWNncm91cCAoU2VuZGFjIGdyb3VwIGZpbGUpXCJdLFxyXG4gICAgICAgICAgW1xyXG4gICAgICAgICAgICBcImJhbmthYyAoQmFuayBhY2NvdW50IGRldGFpbHMgdGFibGUpXCIsXHJcbiAgICAgICAgICAgIHtcclxuICAgICAgICAgICAgICBcIlN1cHBsaWVyIGNvZGVcIjogZGF0YT8ucHJvcGhldHNbMF0/LnByb3BoZXRfY29kZT8udHJpbSgpLFxyXG4gICAgICAgICAgICAgIFwiUmVjb3JkIGlkXCI6IFwiXCIsXHJcbiAgICAgICAgICAgICAgXCJCYW5rIHNvcnQgY29kZVwiOiBzb3J0X2NvZGUsXHJcbiAgICAgICAgICAgICAgXCJBY2NvdW50IG51bWJlclwiOiBhY2NvdW50X251bWJlcixcclxuICAgICAgICAgICAgICBcIkNvdW50cnkgY29kZVwiOlxyXG4gICAgICAgICAgICAgICAgZGF0YT8uY291bnRyeV9jb2RlID09IFwiVUtcIiA/IFwiR0JcIiA6IGRhdGE/LmNvdW50cnlfY29kZSxcclxuICAgICAgICAgICAgICBcIkFjY291bnQgaG9sZGVyXCI6IGRhdGEuY29tcGFueV9uYW1lLFxyXG4gICAgICAgICAgICAgIFwiQ3VycmVuY3kgbnVtYmVyXCI6IGN1cnJlbmN5SWQsXHJcbiAgICAgICAgICAgICAgXCJCQUNTIGN1cnJlbmN5XCI6IGRhdGE/LmJhY3NfY3VycmVuY3lfY29kZSxcclxuICAgICAgICAgICAgICBcIkFkZHJlc3MgTGluZSAxXCI6IFwiXCIsXHJcbiAgICAgICAgICAgICAgXCJBZGRyZXNzIExpbmUgMlwiOiBcIlwiLFxyXG4gICAgICAgICAgICAgIFwiQklDL1N3aWZ0IGFkZHJlc3NcIjogc3dpZnRCaWNDb2RlLFxyXG4gICAgICAgICAgICAgIFwiSW50ZXJuYXRpb24gYmFuayByZWZlcmVuY2UgY29kZVwiOiBpYmFuLFxyXG4gICAgICAgICAgICAgIFwiQWNjb3VudCB1c2VyIGlkXCI6IFwiXCIsXHJcbiAgICAgICAgICAgICAgXCJQb3N0IGNvZGVcIjogXCJcIixcclxuICAgICAgICAgICAgfSxcclxuICAgICAgICAgIF0sXHJcbiAgICAgICAgICBbXHJcbiAgICAgICAgICAgIFwic2VuYm5rIChTdXBwbGllciBiYW5rIGxpbmsgdGFibGUpXCIsXHJcbiAgICAgICAgICAgIHtcclxuICAgICAgICAgICAgICBcIlN1cHBsaWVyIGNvZGVcIjogZGF0YT8ucHJvcGhldHNbMF0/LnByb3BoZXRfY29kZVxyXG4gICAgICAgICAgICAgICAgPyBkYXRhPy5wcm9waGV0c1swXT8ucHJvcGhldF9jb2RlPy50cmltKClcclxuICAgICAgICAgICAgICAgIDogXCJcIixcclxuICAgICAgICAgICAgICBCYW5rYWNpZDogXCJcIixcclxuXHJcbiAgICAgICAgICAgICAgXCJIZWFkZXIgYmFuayByZWNvcmQgaWRcIjogXCJcIixcclxuICAgICAgICAgICAgICBcIkludGVybWVkaWFyeSBiYW5rIGFjY291bnQgaWRcIjogXCJcIixcclxuICAgICAgICAgICAgICBcIkludGVybWVkaWFyeSBiYW5rIGFjY291bnQgaWQgSW50ZXJuYXRpb24gYmFuayByZWZlcmVuY2UgY29kZVwiOlxyXG4gICAgICAgICAgICAgICAgXCJcIixcclxuICAgICAgICAgICAgfSxcclxuICAgICAgICAgIF0sXHJcbiAgICAgICAgICBbXCJjb250YWN0ZGV0IChTdXBwbGllciBwZXJzb25uZWwgY29udGFjdCBkZXRhaWxzKVwiXSxcclxuICAgICAgICAgIFtcclxuICAgICAgICAgICAgXCJvcmdhbml6YXRpb24gKE9yZ2FuaXphdGlvbilcIixcclxuICAgICAgICAgICAge1xyXG4gICAgICAgICAgICAgIFwiT3JnYW5pemF0aW9uIElEXCI6IFwiXCIsXHJcbiAgICAgICAgICAgICAgXCJPcmdhbml6YXRpb24gTmFtZVwiOiBkYXRhPy5wcm9waGV0c1swXT8ucHJvcGhldF9jb2RlXHJcbiAgICAgICAgICAgICAgICA/IGRhdGE/LnByb3BoZXRzWzBdPy5wcm9waGV0X2NvZGU/LnRyaW0oKVxyXG4gICAgICAgICAgICAgICAgOiBcIlwiLFxyXG4gICAgICAgICAgICB9LFxyXG4gICAgICAgICAgXSxcclxuICAgICAgICAgIFtcclxuICAgICAgICAgICAgXCJvcmdyb2xlcyAoT3JnYW5pemF0aW9uIFJvbGVzKVwiLFxyXG4gICAgICAgICAgICB7XHJcbiAgICAgICAgICAgICAgXCJPcmdhbml6YXRpb24gSURcIjogXCJcIixcclxuICAgICAgICAgICAgICBcIk9yZ2FuaXphdGlvbiBDb2RlXCI6IGRhdGE/LnByb3BoZXRzWzBdPy5wcm9waGV0X2NvZGVcclxuICAgICAgICAgICAgICAgID8gZGF0YT8ucHJvcGhldHNbMF0/LnByb3BoZXRfY29kZT8udHJpbSgpXHJcbiAgICAgICAgICAgICAgICA6IFwiXCIsXHJcbiAgICAgICAgICAgICAgXCJSb2xlIFR5cGUgSURcIjogXCJcIiwgLy8haW4gcmVjb3JkaW5nIHRoZXJlIGlzIGFub3RoZXIgY29sdW1uIHdpdGggc2ltaWxhciBzdGFydGluZyBuYW1lIHRoZW4gaXQgZ29lcyBsaWtlIC4uLiBzbyBub3Qgc3VyZSB3aHRhIGlzIHRoZSBmdWxsIG5hbWUgZm8gdGhlIGNvbHVtbiB3aGljaCB3aWxsIGJlIGFmdGVyIHRoaXNcclxuICAgICAgICAgICAgICBTZWxlY3RlZDogXCJcIixcclxuICAgICAgICAgICAgICBcIk9yZ2FuaXNhdGlvbiBJRFwiOiBcIlwiLFxyXG4gICAgICAgICAgICAgIFwicm9sZSBUeXBlIElEXCI6IFwiXCIsXHJcbiAgICAgICAgICAgICAgXCJDb250YWN0IElEXCI6IFwiXCIsXHJcbiAgICAgICAgICAgICAgXCJDb250YWN0IElEIEVtYWlsIEFkZHJlc3NcIjogXCJcIixcclxuICAgICAgICAgICAgICBcIkNvbnRhY3QgSUQgVGVsZXBob25lXCI6IFwiXCIsXHJcbiAgICAgICAgICAgICAgXCJDb250YWN0IElEIEZheFwiOiBcIlwiLFxyXG4gICAgICAgICAgICB9LFxyXG4gICAgICAgICAgXSxcclxuICAgICAgICAgIFtcclxuICAgICAgICAgICAgXCJzaGVldFN1cHBsaWVyc0lkXCIsXHJcbiAgICAgICAgICAgIHtcclxuICAgICAgICAgICAgICBpZDogZGF0YT8uaWQsXHJcbiAgICAgICAgICAgICAgc3VwcGxpZXJOYW1lOiBmb3JtYXR0ZWREaXN0cmlidXRpb25EYXRhPy5jb21wYW55X25hbWUsXHJcbiAgICAgICAgICAgICAgaXNFbWVyZ2VuY3lBbmRGaW5hbmNlTm90Q29tcGxldGU6XHJcbiAgICAgICAgICAgICAgICBkYXRhPy5pc0VtZXJnZW5jeUFuZEZpbmFuY2VOb3RDb21wbGV0ZSxcclxuICAgICAgICAgICAgICBzdXBwbGllckNvZGU6IGRhdGE/LnByb3BoZXRzWzBdPy5wcm9waGV0X2NvZGVcclxuICAgICAgICAgICAgICAgID8gZGF0YT8ucHJvcGhldHNbMF0/LnByb3BoZXRfY29kZT8udHJpbSgpXHJcbiAgICAgICAgICAgICAgICA6IFwiXCIsXHJcbiAgICAgICAgICAgIH0sXHJcbiAgICAgICAgICBdLFxyXG4gICAgICAgIF07XHJcbiAgICAgICAgY29uc3QgYWRkRGF0YVRvU2hlZXQgPSAoc2hlZXRJbmRleCwgZGF0YVRvQWRkKSA9PiB7XHJcbiAgICAgICAgICBpZiAoZmlsdGVyZWRJbnRlcm5hbEV4cG9ydERhdGFbc2hlZXRJbmRleF0ubGVuZ3RoID09PSAyKSB7XHJcbiAgICAgICAgICAgIGZpbHRlcmVkSW50ZXJuYWxFeHBvcnREYXRhW3NoZWV0SW5kZXhdLnB1c2goLi4uZGF0YVRvQWRkKTtcclxuICAgICAgICAgIH0gZWxzZSB7XHJcbiAgICAgICAgICAgIGZpbHRlcmVkSW50ZXJuYWxFeHBvcnREYXRhW3NoZWV0SW5kZXhdID0gW1xyXG4gICAgICAgICAgICAgIGZpbHRlcmVkSW50ZXJuYWxFeHBvcnREYXRhW3NoZWV0SW5kZXhdWzBdLFxyXG4gICAgICAgICAgICAgIC4uLmRhdGFUb0FkZCxcclxuICAgICAgICAgICAgXTtcclxuICAgICAgICAgIH1cclxuICAgICAgICB9O1xyXG5cclxuICAgICAgICAvLyBFeHRyYWN0IGNvbnRhY3RzIGFuZCBhZGQgdG8gdGhlIGNvbnRhY3RzIHNoZWV0XHJcbiAgICAgICAgY29uc3QgY29udGFjdHMgPSBleHRyYWN0Q29udGFjdHMoZGF0YT8uY29udGFjdHNfanNvbik7XHJcbiAgICAgICAgY29uc3QgZXh0cmFjdGVkU2VuZGFjR3JvdXAgPSBleHRyYWN0U2VuZGFjR3JvdXAoXHJcbiAgICAgICAgICBkYXRhLnN1cHBsaWVyX2dyb3VwLFxyXG4gICAgICAgICAgZGF0YT8uaWRcclxuICAgICAgICApO1xyXG4gICAgICAgIGxldCBzZW5kYWNSb2xlT25Sb2xlTnVtcyA9IG11bHRpcGxlU2VuZFJvbGVPblJvbGVOdW1zKGRhdGEpO1xyXG4gICAgICAgIGFkZERhdGFUb1NoZWV0KDYsIGNvbnRhY3RzKTtcclxuICAgICAgICBhZGREYXRhVG9TaGVldCgyLCBzZW5kYWNSb2xlT25Sb2xlTnVtcyk7XHJcbiAgICAgICAgYWRkRGF0YVRvU2hlZXQoMywgZXh0cmFjdGVkU2VuZGFjR3JvdXApO1xyXG4gICAgICB9XHJcbiAgICAgIGxldCBleHBvcnRfSVNTX3Jlc3BvbnNlO1xyXG4gICAgICBpZiAoXHJcbiAgICAgICAgcm9sZXNBcnJheS5pbmNsdWRlcygxKSB8fFxyXG4gICAgICAgIHJvbGVzQXJyYXkuaW5jbHVkZXMoMikgfHxcclxuICAgICAgICByb2xlc0FycmF5LmluY2x1ZGVzKDMpIHx8XHJcbiAgICAgICAgcm9sZXNBcnJheS5pbmNsdWRlcyg0KVxyXG4gICAgICApIHtcclxuICAgICAgICBsZXQgc2VuZGFjUm9sZU9uUm9sZU51bXMgPSBtdWx0aXBsZVNlbmRSb2xlT25Sb2xlTnVtcyhkYXRhPy5yb2xlX251bSk7XHJcbiAgICAgICAgZmlsdGVyZWRJU1NFeHBvcnREYXRhID0gW1xyXG4gICAgICAgICAgW1xyXG4gICAgICAgICAgICBcIlVscEZpbFwiLFxyXG4gICAgICAgICAgICB7XHJcbiAgICAgICAgICAgICAgXCJEaXN0cmlidXRpb24gcG9pbnRcIjogXCJcIixcclxuICAgICAgICAgICAgICBEZXNjcmlwdGlvbjpcclxuICAgICAgICAgICAgICAgIGZvcm1hdHRlZERpc3RyaWJ1dGlvbkRhdGE/Lmxlbmd0aCA+IDAgJiZcclxuICAgICAgICAgICAgICAgIChmb3JtYXR0ZWREaXN0cmlidXRpb25EYXRhWzBdLmZyb21fZHAgPT09IG51bGwgfHxcclxuICAgICAgICAgICAgICAgICAgZm9ybWF0dGVkRGlzdHJpYnV0aW9uRGF0YVswXS5mcm9tX2RwID09PSB1bmRlZmluZWQpXHJcbiAgICAgICAgICAgICAgICAgID8gZm9ybWF0dGVkRGlzdHJpYnV0aW9uRGF0YVswXS5kaXN0cmlidXRpb25Qb2ludFxyXG4gICAgICAgICAgICAgICAgICA6IFwiXCIsXHJcbiAgICAgICAgICAgICAgXCJTZXJ2aWNlIFN1cHBsaWVyIENvZGVcIjpcclxuICAgICAgICAgICAgICAgIGZvcm1hdHRlZERpc3RyaWJ1dGlvbkRhdGE/Lmxlbmd0aCA+IDAgJiZcclxuICAgICAgICAgICAgICAgIChmb3JtYXR0ZWREaXN0cmlidXRpb25EYXRhWzBdLmZyb21fZHAgPT09IG51bGwgfHxcclxuICAgICAgICAgICAgICAgICAgZm9ybWF0dGVkRGlzdHJpYnV0aW9uRGF0YVswXS5mcm9tX2RwID09PSB1bmRlZmluZWQpICYmXHJcbiAgICAgICAgICAgICAgICBkYXRhPy5wcm9waGV0c1swXT8ucHJvcGhldF9jb2RlPy50cmltKCksXHJcbiAgICAgICAgICAgICAgXCJEZWZhdWx0IGV4cGVjdGVkIHN0b2NrIHN0YXR1c1wiOlxyXG4gICAgICAgICAgICAgICAgZm9ybWF0dGVkRGlzdHJpYnV0aW9uRGF0YT8ubGVuZ3RoID4gMCAmJlxyXG4gICAgICAgICAgICAgICAgKGZvcm1hdHRlZERpc3RyaWJ1dGlvbkRhdGFbMF0uZnJvbV9kcCA9PT0gbnVsbCB8fFxyXG4gICAgICAgICAgICAgICAgICBmb3JtYXR0ZWREaXN0cmlidXRpb25EYXRhWzBdLmZyb21fZHAgPT09IHVuZGVmaW5lZCkgJiZcclxuICAgICAgICAgICAgICAgIFwiMFwiLFxyXG4gICAgICAgICAgICAgIFwiRGVmYXVsdCByZWNlaXZlZCBzdG9jayBzdGF0dXNcIjpcclxuICAgICAgICAgICAgICAgIGZvcm1hdHRlZERpc3RyaWJ1dGlvbkRhdGE/Lmxlbmd0aCA+IDAgJiZcclxuICAgICAgICAgICAgICAgIChmb3JtYXR0ZWREaXN0cmlidXRpb25EYXRhWzBdLmZyb21fZHAgPT09IG51bGwgfHxcclxuICAgICAgICAgICAgICAgICAgZm9ybWF0dGVkRGlzdHJpYnV0aW9uRGF0YVswXS5mcm9tX2RwID09PSB1bmRlZmluZWQpICYmXHJcbiAgICAgICAgICAgICAgICBcIjBcIixcclxuICAgICAgICAgICAgICBcIlBhbGxldHMgaW4gcGFja2hvdXNlXCI6XHJcbiAgICAgICAgICAgICAgICBmb3JtYXR0ZWREaXN0cmlidXRpb25EYXRhPy5sZW5ndGggPiAwICYmXHJcbiAgICAgICAgICAgICAgICAoZm9ybWF0dGVkRGlzdHJpYnV0aW9uRGF0YVswXS5mcm9tX2RwID09PSBudWxsIHx8XHJcbiAgICAgICAgICAgICAgICAgIGZvcm1hdHRlZERpc3RyaWJ1dGlvbkRhdGFbMF0uZnJvbV9kcCA9PT0gdW5kZWZpbmVkKSAmJlxyXG4gICAgICAgICAgICAgICAgXCIwXCIsXHJcbiAgICAgICAgICAgICAgXCJEZWZhdWx0IGhhdWxpZXJcIjpcclxuICAgICAgICAgICAgICAgIGZvcm1hdHRlZERpc3RyaWJ1dGlvbkRhdGE/Lmxlbmd0aCA+IDAgJiZcclxuICAgICAgICAgICAgICAgIChmb3JtYXR0ZWREaXN0cmlidXRpb25EYXRhWzBdLmZyb21fZHAgPT09IG51bGwgfHxcclxuICAgICAgICAgICAgICAgICAgZm9ybWF0dGVkRGlzdHJpYnV0aW9uRGF0YVswXS5mcm9tX2RwID09PSB1bmRlZmluZWQpICYmXHJcbiAgICAgICAgICAgICAgICBcIlpaWlpaXCIsXHJcbiAgICAgICAgICAgICAgXCJEZWZhdWx0IGV4cGVjdGVkIGxvY2F0aW9uIGlkXCI6XHJcbiAgICAgICAgICAgICAgICBmb3JtYXR0ZWREaXN0cmlidXRpb25EYXRhPy5sZW5ndGggPiAwICYmXHJcbiAgICAgICAgICAgICAgICAoZm9ybWF0dGVkRGlzdHJpYnV0aW9uRGF0YVswXS5mcm9tX2RwID09PSBudWxsIHx8XHJcbiAgICAgICAgICAgICAgICAgIGZvcm1hdHRlZERpc3RyaWJ1dGlvbkRhdGFbMF0uZnJvbV9kcCA9PT0gdW5kZWZpbmVkKSAmJlxyXG4gICAgICAgICAgICAgICAgMjEsXHJcbiAgICAgICAgICAgICAgXCJEZWZhdWx0IHJlY2VpdmluZyBsb2NhdGlvbiBpZFwiOlxyXG4gICAgICAgICAgICAgICAgZm9ybWF0dGVkRGlzdHJpYnV0aW9uRGF0YT8ubGVuZ3RoID4gMCAmJlxyXG4gICAgICAgICAgICAgICAgKGZvcm1hdHRlZERpc3RyaWJ1dGlvbkRhdGFbMF0uZnJvbV9kcCA9PT0gbnVsbCB8fFxyXG4gICAgICAgICAgICAgICAgICBmb3JtYXR0ZWREaXN0cmlidXRpb25EYXRhWzBdLmZyb21fZHAgPT09IHVuZGVmaW5lZCkgJiZcclxuICAgICAgICAgICAgICAgIDIxLFxyXG4gICAgICAgICAgICAgIFwiUGFja2hvdXNlIGxvY2F0aW9uIGlkXCI6XHJcbiAgICAgICAgICAgICAgICBmb3JtYXR0ZWREaXN0cmlidXRpb25EYXRhPy5sZW5ndGggPiAwICYmXHJcbiAgICAgICAgICAgICAgICAoZm9ybWF0dGVkRGlzdHJpYnV0aW9uRGF0YVswXS5mcm9tX2RwID09PSBudWxsIHx8XHJcbiAgICAgICAgICAgICAgICAgIGZvcm1hdHRlZERpc3RyaWJ1dGlvbkRhdGFbMF0uZnJvbV9kcCA9PT0gdW5kZWZpbmVkKSAmJlxyXG4gICAgICAgICAgICAgICAgMjEsXHJcbiAgICAgICAgICAgICAgXCJEZWZhdWx0IHBpY2sgbG9jYXRpb24gaWRcIjpcclxuICAgICAgICAgICAgICAgIGZvcm1hdHRlZERpc3RyaWJ1dGlvbkRhdGE/Lmxlbmd0aCA+IDAgJiZcclxuICAgICAgICAgICAgICAgIChmb3JtYXR0ZWREaXN0cmlidXRpb25EYXRhWzBdLmZyb21fZHAgPT09IG51bGwgfHxcclxuICAgICAgICAgICAgICAgICAgZm9ybWF0dGVkRGlzdHJpYnV0aW9uRGF0YVswXS5mcm9tX2RwID09PSB1bmRlZmluZWQpICYmXHJcbiAgICAgICAgICAgICAgICAyMSxcclxuICAgICAgICAgICAgICBcIkRlc3BhdGNoIGxvY2F0aW9uIGlkXCI6XHJcbiAgICAgICAgICAgICAgICBmb3JtYXR0ZWREaXN0cmlidXRpb25EYXRhPy5sZW5ndGggPiAwICYmXHJcbiAgICAgICAgICAgICAgICAoZm9ybWF0dGVkRGlzdHJpYnV0aW9uRGF0YVswXS5mcm9tX2RwID09PSBudWxsIHx8XHJcbiAgICAgICAgICAgICAgICAgIGZvcm1hdHRlZERpc3RyaWJ1dGlvbkRhdGFbMF0uZnJvbV9kcCA9PT0gdW5kZWZpbmVkKSAmJlxyXG4gICAgICAgICAgICAgICAgMjEsXHJcbiAgICAgICAgICAgICAgXCJEZWZhdWx0IHdhc3RlIGxvY2F0aW9uIGlkXCI6XHJcbiAgICAgICAgICAgICAgICBmb3JtYXR0ZWREaXN0cmlidXRpb25EYXRhPy5sZW5ndGggPiAwICYmXHJcbiAgICAgICAgICAgICAgICAoZm9ybWF0dGVkRGlzdHJpYnV0aW9uRGF0YVswXS5mcm9tX2RwID09PSBudWxsIHx8XHJcbiAgICAgICAgICAgICAgICAgIGZvcm1hdHRlZERpc3RyaWJ1dGlvbkRhdGFbMF0uZnJvbV9kcCA9PT0gdW5kZWZpbmVkKSAmJlxyXG4gICAgICAgICAgICAgICAgMjEsXHJcbiAgICAgICAgICAgICAgXCJEZWZhdWx0IHByZS1waWNrIGxvY2F0aW9uIGlkXCI6XHJcbiAgICAgICAgICAgICAgICBmb3JtYXR0ZWREaXN0cmlidXRpb25EYXRhPy5sZW5ndGggPiAwICYmXHJcbiAgICAgICAgICAgICAgICAoZm9ybWF0dGVkRGlzdHJpYnV0aW9uRGF0YVswXS5mcm9tX2RwID09PSBudWxsIHx8XHJcbiAgICAgICAgICAgICAgICAgIGZvcm1hdHRlZERpc3RyaWJ1dGlvbkRhdGFbMF0uZnJvbV9kcCA9PT0gdW5kZWZpbmVkKSAmJlxyXG4gICAgICAgICAgICAgICAgMjEsXHJcbiAgICAgICAgICAgICAgXCJEZWZhdWx0IHJldHVybnMgbG9jYXRpb24gaWRcIjpcclxuICAgICAgICAgICAgICAgIGZvcm1hdHRlZERpc3RyaWJ1dGlvbkRhdGE/Lmxlbmd0aCA+IDAgJiZcclxuICAgICAgICAgICAgICAgIChmb3JtYXR0ZWREaXN0cmlidXRpb25EYXRhWzBdLmZyb21fZHAgPT09IG51bGwgfHxcclxuICAgICAgICAgICAgICAgICAgZm9ybWF0dGVkRGlzdHJpYnV0aW9uRGF0YVswXS5mcm9tX2RwID09PSB1bmRlZmluZWQpICYmXHJcbiAgICAgICAgICAgICAgICAyMSxcclxuICAgICAgICAgICAgICBBZGRyZXNzOiBcIlwiLFxyXG4gICAgICAgICAgICAgIFwiU2VydmljZSBzdXBwbGllciBjb2RlXCI6IFwiXCIsXHJcbiAgICAgICAgICAgICAgXCJFREkgUmVmZXJlbmNlIENvZGVcIjogXCJcIixcclxuICAgICAgICAgICAgICBcIkVESSBBTkEgQ29kZVwiOiBcIlwiLFxyXG4gICAgICAgICAgICAgIFwiVXNlciBJbnRlZ2VyIDFcIjpcclxuICAgICAgICAgICAgICAgIGZvcm1hdHRlZERpc3RyaWJ1dGlvbkRhdGE/Lmxlbmd0aCA+IDAgJiZcclxuICAgICAgICAgICAgICAgIChmb3JtYXR0ZWREaXN0cmlidXRpb25EYXRhWzBdLmZyb21fZHAgPT09IG51bGwgfHxcclxuICAgICAgICAgICAgICAgICAgZm9ybWF0dGVkRGlzdHJpYnV0aW9uRGF0YVswXS5mcm9tX2RwID09PSB1bmRlZmluZWQpICYmXHJcbiAgICAgICAgICAgICAgICAxLFxyXG4gICAgICAgICAgICAgIFwiTW92ZW1lbnQgcmVzb3VyY2UgZ3JvdXBcIjogXCJcIixcclxuICAgICAgICAgICAgICBcIkhhbmRoZWxkIGFwcGxpY2F0aW9uIHVzZWRcIjpcclxuICAgICAgICAgICAgICAgIGZvcm1hdHRlZERpc3RyaWJ1dGlvbkRhdGE/Lmxlbmd0aCA+IDAgJiZcclxuICAgICAgICAgICAgICAgIChmb3JtYXR0ZWREaXN0cmlidXRpb25EYXRhWzBdLmZyb21fZHAgPT09IG51bGwgfHxcclxuICAgICAgICAgICAgICAgICAgZm9ybWF0dGVkRGlzdHJpYnV0aW9uRGF0YVswXS5mcm9tX2RwID09PSB1bmRlZmluZWQpICYmXHJcbiAgICAgICAgICAgICAgICBcIjBcIixcclxuICAgICAgICAgICAgICBcIlBhbGxldHMgaW4gcHJvY3VyZS9yZWNlaXZpbmdcIjpcclxuICAgICAgICAgICAgICAgIGZvcm1hdHRlZERpc3RyaWJ1dGlvbkRhdGE/Lmxlbmd0aCA+IDAgJiZcclxuICAgICAgICAgICAgICAgIChmb3JtYXR0ZWREaXN0cmlidXRpb25EYXRhWzBdLmZyb21fZHAgPT09IG51bGwgfHxcclxuICAgICAgICAgICAgICAgICAgZm9ybWF0dGVkRGlzdHJpYnV0aW9uRGF0YVswXS5mcm9tX2RwID09PSB1bmRlZmluZWQpICYmXHJcbiAgICAgICAgICAgICAgICBcIjBcIixcclxuICAgICAgICAgICAgICBcIk9wZXJhdGlvbmFsIGRlcG9cIjogXCJcIixcclxuICAgICAgICAgICAgICBcIkVuYWJsZWQgZm9yIG1hc3RlcmZpbGUgc2VuZGluZ1wiOiBcIlwiLFxyXG4gICAgICAgICAgICAgIFwiQ29ubmVjdGVkIHJlZ2lzdGVkIGRlcG90XCI6IFwiXCIsXHJcbiAgICAgICAgICAgICAgXCJFREkgVHJhbnNtaXNzaW9uIHR5cGUgb2YgZGVwb1wiOiBcIlwiLFxyXG4gICAgICAgICAgICAgIFwiQ29udGFpbmVyIGxvYWRpbmcgZGVwb1wiOlxyXG4gICAgICAgICAgICAgICAgZm9ybWF0dGVkRGlzdHJpYnV0aW9uRGF0YT8ubGVuZ3RoID4gMCAmJlxyXG4gICAgICAgICAgICAgICAgKGZvcm1hdHRlZERpc3RyaWJ1dGlvbkRhdGFbMF0uZnJvbV9kcCA9PT0gbnVsbCB8fFxyXG4gICAgICAgICAgICAgICAgICBmb3JtYXR0ZWREaXN0cmlidXRpb25EYXRhWzBdLmZyb21fZHAgPT09IHVuZGVmaW5lZCkgJiZcclxuICAgICAgICAgICAgICAgIFwiMFwiLFxyXG4gICAgICAgICAgICAgIFwiQWlycG9ydCBkZXBvdFwiOiBcIlwiLFxyXG4gICAgICAgICAgICAgIFwiU21zIG5vdGlmaWNhdGlvblwiOiBcIlwiLFxyXG4gICAgICAgICAgICAgIFBvcnQ6IFwiXCIsXHJcbiAgICAgICAgICAgICAgRG9ybWFudDogXCJcIixcclxuICAgICAgICAgICAgICBBY3RpdmU6XHJcbiAgICAgICAgICAgICAgICBmb3JtYXR0ZWREaXN0cmlidXRpb25EYXRhPy5sZW5ndGggPiAwICYmXHJcbiAgICAgICAgICAgICAgICAoZm9ybWF0dGVkRGlzdHJpYnV0aW9uRGF0YVswXS5mcm9tX2RwID09PSBudWxsIHx8XHJcbiAgICAgICAgICAgICAgICAgIGZvcm1hdHRlZERpc3RyaWJ1dGlvbkRhdGFbMF0uZnJvbV9kcCA9PT0gdW5kZWZpbmVkKSAmJlxyXG4gICAgICAgICAgICAgICAgMSxcclxuICAgICAgICAgICAgICBcIkluZ3JlZGllbnQgZGlzdHJpYnV0aW9uIHBvaW50XCI6IFwiXCIsXHJcbiAgICAgICAgICAgICAgXCJTaG93IGluIENFXCI6XHJcbiAgICAgICAgICAgICAgICBmb3JtYXR0ZWREaXN0cmlidXRpb25EYXRhPy5sZW5ndGggPiAwICYmXHJcbiAgICAgICAgICAgICAgICAoZm9ybWF0dGVkRGlzdHJpYnV0aW9uRGF0YVswXS5mcm9tX2RwID09PSBudWxsIHx8XHJcbiAgICAgICAgICAgICAgICAgIGZvcm1hdHRlZERpc3RyaWJ1dGlvbkRhdGFbMF0uZnJvbV9kcCA9PT0gdW5kZWZpbmVkKSAmJlxyXG4gICAgICAgICAgICAgICAgMSxcclxuICAgICAgICAgICAgICBcIkNoYXJnZSBkaXJlY3Rpb25cIjogXCJcIixcclxuICAgICAgICAgICAgICBcIlBhbGxldCByZWNlaXZlIHRpbWVcIjogXCJcIixcclxuICAgICAgICAgICAgICBcIlVzZXIgc3RyaW5nIDNcIjogXCJcIixcclxuICAgICAgICAgICAgICBcIkRpcmVjdCBEUFwiOlxyXG4gICAgICAgICAgICAgICAgZm9ybWF0dGVkRGlzdHJpYnV0aW9uRGF0YT8ubGVuZ3RoID4gMCAmJlxyXG4gICAgICAgICAgICAgICAgKGZvcm1hdHRlZERpc3RyaWJ1dGlvbkRhdGFbMF0uZnJvbV9kcCA9PT0gbnVsbCB8fFxyXG4gICAgICAgICAgICAgICAgICBmb3JtYXR0ZWREaXN0cmlidXRpb25EYXRhWzBdLmZyb21fZHAgPT09IHVuZGVmaW5lZClcclxuICAgICAgICAgICAgICAgICAgPyBmb3JtYXR0ZWREaXN0cmlidXRpb25EYXRhWzBdPy5kaXJlY3REUFxyXG4gICAgICAgICAgICAgICAgICAgID8gMVxyXG4gICAgICAgICAgICAgICAgICAgIDogXCIwXCJcclxuICAgICAgICAgICAgICAgICAgOiBcIlwiLFxyXG4gICAgICAgICAgICAgIFwiSW5jbHVkZSBvbiBYTUxcIjpcclxuICAgICAgICAgICAgICAgIGZvcm1hdHRlZERpc3RyaWJ1dGlvbkRhdGE/Lmxlbmd0aCA+IDAgJiZcclxuICAgICAgICAgICAgICAgIChmb3JtYXR0ZWREaXN0cmlidXRpb25EYXRhWzBdLmZyb21fZHAgPT09IG51bGwgfHxcclxuICAgICAgICAgICAgICAgICAgZm9ybWF0dGVkRGlzdHJpYnV0aW9uRGF0YVswXS5mcm9tX2RwID09PSB1bmRlZmluZWQpICYmXHJcbiAgICAgICAgICAgICAgICBcIjBcIixcclxuICAgICAgICAgICAgfSxcclxuICAgICAgICAgIF0sXHJcbiAgICAgICAgICBbXHJcbiAgICAgICAgICAgIFwiU3VwcGxpZXIgZGF0YVwiLFxyXG4gICAgICAgICAgICB7XHJcbiAgICAgICAgICAgICAgXCJTdXBwbGllciBBY3RpdmVcIjogXCJOL0FcIixcclxuICAgICAgICAgICAgICBcIlN1cHBsaWVyIGNvZGVcIjogZGF0YT8ucHJvcGhldHNbMF0/LnByb3BoZXRfY29kZT8udHJpbSgpLFxyXG4gICAgICAgICAgICAgIFwiRURJIFBhcnRuZXJcIjogXCJOL0FcIixcclxuICAgICAgICAgICAgICBcIlN1cHBsaWVyIG5hbWVcIjogZGF0YS5jb21wYW55X25hbWUsXHJcbiAgICAgICAgICAgICAgXCJFREkgQU5BIG51bWJlclwiOiBkYXRhPy5lZGkgPz8gXCJOL0FcIixcclxuICAgICAgICAgICAgICBcIlByb2R1Y2VyIChzdXBwbGllcilcIjogXCJOL0FcIixcclxuICAgICAgICAgICAgICBcIkRlcGFydG1lbnQgbnVtYmVyXCI6IFwiTi9BXCIsXHJcbiAgICAgICAgICAgICAgXCJDdXJyZW5jeSBudW1iZXJcIjogZGF0YT8uY3VycmVuY3lfaWQgPyBkYXRhPy5jdXJyZW5jeV9pZCA6IDEsXHJcbiAgICAgICAgICAgICAgXCJHbG9iYWwgZ2FwIG51bWJlclwiOiBkYXRhPy5nbG9iYWxfZ2FwX251bWJlcixcclxuICAgICAgICAgICAgICBcIkdyb3dlciBncm91cFwiOiBcIk4vQVwiLFxyXG4gICAgICAgICAgICAgIFwiRGVmcmEgY291bnR5IG51bWJlclwiOiBcIk4vQVwiLFxyXG4gICAgICAgICAgICAgIFwiRGF0ZSBzdGFydFwiOiBcIk4vQVwiLFxyXG4gICAgICAgICAgICAgIFwiRGF0ZSBlbmRcIjogXCJOL0FcIixcclxuICAgICAgICAgICAgICBcIk9yZ2FuaWMgY2VydFwiOiBkYXRhPy5vcmdhbmljX2NlcnRpZmljYXRlX251bWJlcixcclxuICAgICAgICAgICAgICBcIlJlZ2lvbmFsIGNlcnRcIjogZGF0YT8uY2hpbGVfY2VydGlmaWNhdGVfbnVtYmVyLFxyXG4gICAgICAgICAgICAgIFwiSGVhZCBvZmZpY2VcIjogZGF0YT8ucHJvcGhldHNbMF0/LnByb3BoZXRfY29kZT8udHJpbSgpLFxyXG4gICAgICAgICAgICAgIFwiQ291bnRyeSBDb2RlXCI6IGRhdGE/LmNvdW50cnlfY29kZSxcclxuICAgICAgICAgICAgICBcIkRpc3RyaWJ1dGlvbiBwb2ludCBmb3Igc3VwcGxpZXJcIjpcclxuICAgICAgICAgICAgICAgIGRhdGE/LmRpc3RyaWJ1dGlvbl9wb2ludHNfanNvbj8ubGVuZ3RoID4gMFxyXG4gICAgICAgICAgICAgICAgICA/IGRhdGE/LmRpc3RyaWJ1dGlvbl9wb2ludHNfanNvblswXS5mcm9tX2RwXHJcbiAgICAgICAgICAgICAgICAgIDogXCJOL0FcIixcclxuICAgICAgICAgICAgICBcIkJvb2wgMlwiOiBcIk4vQVwiLFxyXG4gICAgICAgICAgICAgIFwiQm9vbCAzXCI6IFwiTi9BXCIsXHJcbiAgICAgICAgICAgICAgXCJBZGRyZXNzIGxpbmUgMVwiOiBkYXRhPy5hZGRyZXNzX2xpbmVfMSxcclxuICAgICAgICAgICAgICBcIkFkZHJlc3MgbGluZSAyXCI6IGRhdGE/LmFkZHJlc3NfbGluZV8yLFxyXG4gICAgICAgICAgICAgIFwiQWRkcmVzcyBsaW5lIDNcIjogZGF0YT8uYWRkcmVzc19saW5lXzMsXHJcbiAgICAgICAgICAgICAgXCJBZGRyZXNzIGxpbmUgNFwiOiBkYXRhPy5hZGRyZXNzX2xpbmVfNCxcclxuICAgICAgICAgICAgICBcIlBvc3QgY29kZVwiOiBkYXRhPy5wb3N0YWxfY29kZSxcclxuICAgICAgICAgICAgICBcIkN1cnJlbmN5IE51bWJlclwiOiBkYXRhPy5jdXJyZW5jeV9pZCA/IGRhdGE/LmN1cnJlbmN5X2lkIDogMSxcclxuICAgICAgICAgICAgICBcIkJhbmsgZ2VuZXJhbCBsZWRnZXIgY29kZVwiOiBkYXRhPy5pc3NfbGVkZ2VyX2NvZGVcclxuICAgICAgICAgICAgICAgID8gZGF0YT8uaXNzX2xlZGdlcl9jb2RlXHJcbiAgICAgICAgICAgICAgICA6IFwiMTIyMDBcIixcclxuICAgICAgICAgICAgICBcIkJhbmsgZ2VuZXJhbCBsZWRnZXIgY29kZSBDdXJyZW5jeSBudW1iZXIgaWYgYmFua1wiOlxyXG4gICAgICAgICAgICAgICAgZGF0YT8uY3VycmVuY3lfaWQgPyBkYXRhPy5jdXJyZW5jeV9pZCA6IDEsXHJcbiAgICAgICAgICAgICAgXCJTZXR0bGVtZW50IGRheXNcIjogZGF0YT8ucGF5bWVudF90ZXJtcyxcclxuICAgICAgICAgICAgICBcIkRlcGFydG1lbnQgTnVtYmVyXCI6XHJcbiAgICAgICAgICAgICAgICBkYXRhPy5wcm9waGV0c1swXT8ucHJvcGhldF9pZCA9PSAxXHJcbiAgICAgICAgICAgICAgICAgID8gMVxyXG4gICAgICAgICAgICAgICAgICA6IGRhdGE/LnByb3BoZXRzWzBdPy5wcm9waGV0X2lkID09IDJcclxuICAgICAgICAgICAgICAgICAgPyA5XHJcbiAgICAgICAgICAgICAgICAgIDogZGF0YT8ucHJvcGhldHNbMF0ucHJvcGhldF9pZCA9PSAzXHJcbiAgICAgICAgICAgICAgICAgID8gM1xyXG4gICAgICAgICAgICAgICAgICA6IGRhdGE/LnByb3BoZXRzWzBdLnByb3BoZXRfaWQgPT0gNFxyXG4gICAgICAgICAgICAgICAgICA/IDJcclxuICAgICAgICAgICAgICAgICAgOiBcIk4vQVwiLFxyXG4gICAgICAgICAgICAgIFwiQXJlYSBOdW1iZXJcIjogXCIxXCIsXHJcbiAgICAgICAgICAgICAgVmF0YWJsZTpcclxuICAgICAgICAgICAgICAgIGRhdGE/LnZhdGFibGUgIT0gbnVsbCA/IChkYXRhPy52YXRhYmxlID8gXCIxXCIgOiBcIjBcIikgOiBcIjBcIixcclxuICAgICAgICAgICAgICBCdXllcjogXCIxXCIsXHJcbiAgICAgICAgICAgICAgXCJCaWxsaW5nIHR5cGVcIjogXCIwXCIsXHJcbiAgICAgICAgICAgICAgXCJQYXltZW50IHR5cGVcIjogZGF0YT8ucGF5bWVudF90eXBlID8gZGF0YT8ucGF5bWVudF90eXBlIDogMixcclxuICAgICAgICAgICAgICBcIkV4cGVuc2UgZ2VuZXJhbCBsZWRnZXIgY29kZVwiOiBcIk4vQVwiLFxyXG4gICAgICAgICAgICAgIFwiQXV0aG9yaXNlIG9uIHJlZ2lzdGVyXCI6IFwiTi9BXCIsXHJcbiAgICAgICAgICAgICAgXCJVc2UgJSBhdXRob3Jpc2UgcnVsZVwiOiA1LFxyXG4gICAgICAgICAgICAgIFwiVXNlciB0ZXh0IDFcIjogXCJOL0FcIixcclxuICAgICAgICAgICAgICBcIk1hbmRhdG9yeSBhbHRmaWwgb24gc2VydmljZSBqb2JzXCI6IFwiTi9BXCIsXHJcbiAgICAgICAgICAgICAgXCJPcmdhbml6YXRpb24gSURcIjogXCJOL0FcIixcclxuICAgICAgICAgICAgICBGcm9tRFA6XHJcbiAgICAgICAgICAgICAgICBmb3JtYXR0ZWREaXN0cmlidXRpb25EYXRhPy5sZW5ndGggPiAwXHJcbiAgICAgICAgICAgICAgICAgID8gZm9ybWF0dGVkRGlzdHJpYnV0aW9uRGF0YVswXS5mcm9tX2RwIHx8IFwiXCJcclxuICAgICAgICAgICAgICAgICAgOiBcIlwiLFxyXG4gICAgICAgICAgICAgIGlkOiBkYXRhPy5pZCxcclxuICAgICAgICAgICAgICBpc0VtZXJnZW5jeUFuZEZpbmFuY2VOb3RDb21wbGV0ZTpcclxuICAgICAgICAgICAgICAgIGRhdGE/LmlzRW1lcmdlbmN5QW5kRmluYW5jZU5vdENvbXBsZXRlLFxyXG4gICAgICAgICAgICB9LFxyXG4gICAgICAgICAgXSxcclxuICAgICAgICAgIFtcInNlbmRhY3JvbGUgKFN1cHBsaWVyIHJvbGUgZmlsZSlcIl0sXHJcbiAgICAgICAgXTtcclxuXHJcbiAgICAgICAgY29uc3QgYWRkU2VuZGFjUm9sZURhdGFUb1NoZWV0ID0gKHNoZWV0SW5kZXgsIGRhdGFUb0FkZCkgPT4ge1xyXG4gICAgICAgICAgaWYgKGZpbHRlcmVkSVNTRXhwb3J0RGF0YVtzaGVldEluZGV4XS5sZW5ndGggPT09IDEpIHtcclxuICAgICAgICAgICAgZmlsdGVyZWRJU1NFeHBvcnREYXRhW3NoZWV0SW5kZXhdLnB1c2goLi4uZGF0YVRvQWRkKTtcclxuICAgICAgICAgIH0gZWxzZSB7XHJcbiAgICAgICAgICAgIGZpbHRlcmVkSVNTRXhwb3J0RGF0YVtzaGVldEluZGV4XSA9IFtcclxuICAgICAgICAgICAgICBmaWx0ZXJlZElTU0V4cG9ydERhdGFbc2hlZXRJbmRleF1bMF0sXHJcbiAgICAgICAgICAgICAgLi4uZGF0YVRvQWRkLFxyXG4gICAgICAgICAgICBdO1xyXG4gICAgICAgICAgfVxyXG4gICAgICAgIH07XHJcblxyXG4gICAgICAgIGFkZFNlbmRhY1JvbGVEYXRhVG9TaGVldCgyLCBzZW5kYWNSb2xlT25Sb2xlTnVtcyk7XHJcbiAgICAgICAgZXhwb3J0X0lTU19yZXNwb25zZSA9IGF3YWl0IGV4cG9ydEV4Y2VsKFxyXG4gICAgICAgICAgZmlsdGVyZWRJU1NFeHBvcnREYXRhLFxyXG4gICAgICAgICAgZmFsc2UsXHJcbiAgICAgICAgICB0b2tlbixcclxuICAgICAgICAgIGNvbXBhbnksXHJcbiAgICAgICAgICB1c2VyRGF0YSxcclxuICAgICAgICAgIHByb3BoZXRfaWQsXHJcbiAgICAgICAgICBwYXJhbXMuZGF0YT8ucmVxdWVzdG9yX2VtYWlsLFxyXG4gICAgICAgICAgXCJcIlxyXG4gICAgICAgICk7XHJcbiAgICAgIH0gZWxzZSB7XHJcbiAgICAgICAgZXhwb3J0X0lTU19yZXNwb25zZSA9IFwiTm90IHNlbnRcIjtcclxuICAgICAgfVxyXG5cclxuICAgICAgY29uc3QgZXhwb3J0SW50ZXJuYWxfcmVzcG9uc2UgPSBhd2FpdCBleHBvcnRFeGNlbChcclxuICAgICAgICBmaWx0ZXJlZEludGVybmFsRXhwb3J0RGF0YSxcclxuICAgICAgICB0cnVlLFxyXG4gICAgICAgIHRva2VuLFxyXG4gICAgICAgIGNvbXBhbnksXHJcbiAgICAgICAgdXNlckRhdGEsXHJcbiAgICAgICAgcHJvcGhldF9pZCxcclxuICAgICAgICBwYXJhbXMuZGF0YT8ucmVxdWVzdG9yX2VtYWlsLFxyXG4gICAgICAgIFwiXCJcclxuICAgICAgKTtcclxuXHJcbiAgICAgIHNldEVtYWlsU3RhdHVzUG9wdXAodHJ1ZSk7XHJcbiAgICAgIGlmIChcclxuICAgICAgICBleHBvcnRfSVNTX3Jlc3BvbnNlICYmXHJcbiAgICAgICAgZXhwb3J0SW50ZXJuYWxfcmVzcG9uc2UgJiZcclxuICAgICAgICBleHBvcnRfSVNTX3Jlc3BvbnNlICE9IFwiTm90IHNlbnRcIlxyXG4gICAgICApIHtcclxuICAgICAgICBzZXRQb3BVcE1lc3NhZ2UoXHJcbiAgICAgICAgICBcIkVtYWlsIHN1Y2Nlc3NmdWxseSBzZW50IHRvIGJvdGggRmluYW5jZSBEZXBhcnRtZW50IGFuZCBJU1MgQWRtaW4gVGVhbVwiXHJcbiAgICAgICAgKTtcclxuICAgICAgICBzZXRJU1NFeHBvcnRTdWNjZXNzKHRydWUpO1xyXG4gICAgICAgIHNldEludGVybmFsRXhwb3J0U3VjY2Vzcyh0cnVlKTtcclxuICAgICAgfSBlbHNlIGlmIChleHBvcnRfSVNTX3Jlc3BvbnNlICYmIGV4cG9ydF9JU1NfcmVzcG9uc2UgIT0gXCJOb3Qgc2VudFwiKSB7XHJcbiAgICAgICAgc2V0UG9wVXBNZXNzYWdlKFxyXG4gICAgICAgICAgXCJFbWFpbCBzZW50IHRvIElTUyBBZG1pbiBUZWFtICwgYnV0IG5vdCB0byBGaW5hbmNlIERlcGFydG1lbnRcIlxyXG4gICAgICAgICk7XHJcbiAgICAgICAgc2V0SW50ZXJuYWxFeHBvcnRTdWNjZXNzKHRydWUpO1xyXG4gICAgICB9IGVsc2UgaWYgKGV4cG9ydEludGVybmFsX3Jlc3BvbnNlICYmIGV4cG9ydF9JU1NfcmVzcG9uc2UgIT0gXCJOb3Qgc2VudFwiKSB7XHJcbiAgICAgICAgc2V0SVNTRXhwb3J0U3VjY2Vzcyh0cnVlKTtcclxuICAgICAgICBzZXRQb3BVcE1lc3NhZ2UoXHJcbiAgICAgICAgICBcIkVtYWlsIHNlbnQgdG8gSVNTIEFkbWluIFRlYW0sIGJ1dCBub3QgdG8gRmluYW5jZSBEZXBhcnRtZW50XCJcclxuICAgICAgICApO1xyXG4gICAgICB9IGVsc2UgaWYgKGV4cG9ydEludGVybmFsX3Jlc3BvbnNlICYmIGV4cG9ydF9JU1NfcmVzcG9uc2UgPT0gXCJOb3Qgc2VudFwiKSB7XHJcbiAgICAgICAgc2V0UG9wVXBNZXNzYWdlKFxyXG4gICAgICAgICAgXCJFbWFpbCBzZW50IHRvIEZpbmFuY2UgRGVwYXJ0bWVudCAsIGJ1dCBub3QgdG8gSVNTIEFkbWluIGFzIG9ubHkgSGF1bGllciBvciBFeHBlbnNlIHJvbGUgbm90IGFsbG93ZWQgdG8gZXhwb3J0IG9uIElTU1wiXHJcbiAgICAgICAgKTtcclxuICAgICAgICBzZXRJbnRlcm5hbEV4cG9ydFN1Y2Nlc3ModHJ1ZSk7XHJcbiAgICAgIH0gZWxzZSB7XHJcbiAgICAgICAgc2V0UG9wVXBNZXNzYWdlKFxyXG4gICAgICAgICAgXCJFbWFpbCBub3Qgc2VudCB0byBlaXRoZXIgRmluYW5jZSBEZXBhcnRtZW50IG9yIElTUyBBZG1pbiBUZWFtXCJcclxuICAgICAgICApO1xyXG4gICAgICB9XHJcbiAgICB9IGVsc2Uge1xyXG4gICAgICBpZiAoXHJcbiAgICAgICAgcGFyYW1zLmRhdGEuaXNFbWVyZ2VuY3lSZXF1ZXN0ICYmXHJcbiAgICAgICAgKHBhcmFtcy5kYXRhLkdlbmVyYWwgPT09IFwiSW5jb21wbGV0ZVwiIHx8XHJcbiAgICAgICAgICBwYXJhbXMuZGF0YS5HZW5lcmFsID09IFwiTm90IEVudGVyZWRcIilcclxuICAgICAgKSB7XHJcbiAgICAgICAgdG9hc3QuZXJyb3IoXCJHZW5lcmFsIHNlY3Rpb24gbmVlZHMgdG8gY29tcGxldGVcIiwge1xyXG4gICAgICAgICAgcG9zaXRpb246IFwidG9wLXJpZ2h0XCIsXHJcbiAgICAgICAgICBhdXRvQ2xvc2U6IDMwMDAsXHJcbiAgICAgICAgICBoaWRlUHJvZ3Jlc3NCYXI6IGZhbHNlLFxyXG4gICAgICAgICAgY2xvc2VPbkNsaWNrOiB0cnVlLFxyXG4gICAgICAgICAgcGF1c2VPbkhvdmVyOiBmYWxzZSxcclxuICAgICAgICAgIGRyYWdnYWJsZTogdHJ1ZSxcclxuICAgICAgICAgIHByb2dyZXNzOiB1bmRlZmluZWQsXHJcbiAgICAgICAgICB0aGVtZTogXCJsaWdodFwiLFxyXG4gICAgICAgIH0pO1xyXG4gICAgICAgIHJldHVybjtcclxuICAgICAgfSBlbHNlIGlmIChcclxuICAgICAgICBwYXJhbXMuZGF0YS5pc0VtZXJnZW5jeVJlcXVlc3QgJiZcclxuICAgICAgICAoZGF0YT8ucm9sZUlkcy5pbmNsdWRlcygxKSB8fCBkYXRhPy5yb2xlSWRzPy5pbmNsdWRlcyg2KSkgJiZcclxuICAgICAgICAhcGFyYW1zLmRhdGEuY3VycmVuY3lfaWRcclxuICAgICAgKSB7XHJcbiAgICAgICAgdG9hc3QuZXJyb3IoXHJcbiAgICAgICAgICBcIlBsZWFzZSBzZWxlY3QgYSBjdXJyZW5jeSBhbmQgYSB2YWxpZCBzdXBwbGllciBjb2RlIHRvIGV4cG9ydFwiLFxyXG4gICAgICAgICAge1xyXG4gICAgICAgICAgICBwb3NpdGlvbjogXCJ0b3AtcmlnaHRcIixcclxuICAgICAgICAgICAgYXV0b0Nsb3NlOiAzMDAwLFxyXG4gICAgICAgICAgICBoaWRlUHJvZ3Jlc3NCYXI6IGZhbHNlLFxyXG4gICAgICAgICAgICBjbG9zZU9uQ2xpY2s6IHRydWUsXHJcbiAgICAgICAgICAgIHBhdXNlT25Ib3ZlcjogZmFsc2UsXHJcbiAgICAgICAgICAgIGRyYWdnYWJsZTogdHJ1ZSxcclxuICAgICAgICAgICAgcHJvZ3Jlc3M6IHVuZGVmaW5lZCxcclxuICAgICAgICAgICAgdGhlbWU6IFwibGlnaHRcIixcclxuICAgICAgICAgIH1cclxuICAgICAgICApO1xyXG4gICAgICAgIHJldHVybjtcclxuICAgICAgfSBlbHNlIHtcclxuICAgICAgICB0b2FzdC5lcnJvcihcIlN1cHBsaWVyIGRldGFpbHMgYXJlIGluY29tcGxldGUgb3Igbm90IGNvbmZpcm1lZC5cIiwge1xyXG4gICAgICAgICAgcG9zaXRpb246IFwidG9wLXJpZ2h0XCIsXHJcbiAgICAgICAgICBhdXRvQ2xvc2U6IDMwMDAsXHJcbiAgICAgICAgICBoaWRlUHJvZ3Jlc3NCYXI6IGZhbHNlLFxyXG4gICAgICAgICAgY2xvc2VPbkNsaWNrOiB0cnVlLFxyXG4gICAgICAgICAgcGF1c2VPbkhvdmVyOiBmYWxzZSxcclxuICAgICAgICAgIGRyYWdnYWJsZTogdHJ1ZSxcclxuICAgICAgICAgIHByb2dyZXNzOiB1bmRlZmluZWQsXHJcbiAgICAgICAgICB0aGVtZTogXCJsaWdodFwiLFxyXG4gICAgICAgIH0pO1xyXG4gICAgICAgIHJldHVybjtcclxuICAgICAgfVxyXG4gICAgfVxyXG4gICAgc2V0U2VsZWN0ZWRFeHBvcnRUeXBlKFwiXCIpO1xyXG4gIH07XHJcblxyXG4gIGNvbnN0IGhhbmRsZUNvcHlTdXBwbGllciA9ICgpID0+IHtcclxuICAgIHNldElzT3Blbih0cnVlKTtcclxuICB9O1xyXG5cclxuICBpZiAoaXNPcGVuKSB7XHJcbiAgICByZXR1cm4gKFxyXG4gICAgICA8Q29weVN1cHBsaWVyXHJcbiAgICAgICAgaXNPcGVuPXt0cnVlfVxyXG4gICAgICAgIHNldElzT3Blbj17c2V0SXNPcGVufVxyXG4gICAgICAgIHN1cHBsaWVyRGF0YT17ZGF0YX1cclxuICAgICAgICB1c2VyRGF0YT17dXNlckRhdGF9XHJcbiAgICAgIC8+XHJcbiAgICApO1xyXG4gIH1cclxuXHJcbiAgY29uc3QgZGlzYWJsZWRDbGFzcyA9IFwidGV4dC1ncmF5LTUwMCBjdXJzb3Itbm90LWFsbG93ZWRcIjtcclxuICBjb25zdCBoYW5kbGVFeHBvcnRUeXBlID0gKGUpID0+IHtcclxuICAgIHNldFNlbGVjdGVkRXhwb3J0VHlwZShlLnRhcmdldC52YWx1ZSk7XHJcbiAgfTtcclxuXHJcbiAgcmV0dXJuIChcclxuICAgIDw+XHJcbiAgICAgIHsvKiA8VG9hc3RDb250YWluZXIgbGltaXQ9ezF9IC8+ICovfVxyXG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggZmxleC1yb3cgZ2FwLTQganVzdGlmeS1jZW50ZXIgdGV4dC1ibHVlLTUwMFwiPlxyXG4gICAgICAgIHsvKiA8YnV0dG9uIG9uQ2xpY2s9e2hhbmRsZUNvcHlTdXBwbGllcn0gdGl0bGU9XCJDb3B5IFN1cHBsaWVyXCI+XHJcbiAgICAgICAgICA8Rm9udEF3ZXNvbWVJY29uIGljb249e2ZhQ29weX0gc2l6ZT1cImxnXCIgLz5cclxuICAgICAgICA8L2J1dHRvbj4gKi99XHJcbiAgICAgICAgPGJ1dHRvblxyXG4gICAgICAgICAgb25DbGljaz17KCkgPT5cclxuICAgICAgICAgICAgc3RhdHVzICE9IFwiRXhwb3J0ZWRcIiAmJiBzdGF0dXMgIT0gXCJDYW5jZWxsZWRcIlxyXG4gICAgICAgICAgICAgID8gZWRpdFN1cHBsaWVyKClcclxuICAgICAgICAgICAgICA6IGNvbmZpcm1QYWdlKClcclxuICAgICAgICAgIH1cclxuICAgICAgICA+XHJcbiAgICAgICAgICB7c3RhdHVzID09IFwiRXhwb3J0ZWRcIiB8fCBzdGF0dXMgPT0gXCJDYW5jZWxsZWRcIiA/IChcclxuICAgICAgICAgICAgPEZvbnRBd2Vzb21lSWNvblxyXG4gICAgICAgICAgICAgIGljb249e2ZhRXllfVxyXG4gICAgICAgICAgICAgIHNpemU9XCJsZ1wiXHJcbiAgICAgICAgICAgICAgdGl0bGU9XCJWaWV3IFN1cHBsaWVyXCJcclxuICAgICAgICAgICAgICBjbGFzc05hbWU9XCJ0ZXh0LXNraW4tcHJpbWFyeVwiXHJcbiAgICAgICAgICAgIC8+XHJcbiAgICAgICAgICApIDogKFxyXG4gICAgICAgICAgICA8Rm9udEF3ZXNvbWVJY29uXHJcbiAgICAgICAgICAgICAgaWNvbj17ZmFQZW5Ub1NxdWFyZX1cclxuICAgICAgICAgICAgICBzaXplPVwibGdcIlxyXG4gICAgICAgICAgICAgIHRpdGxlPVwiRWRpdCBTdXBwbGllclwiXHJcbiAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidGV4dC1za2luLXByaW1hcnlcIlxyXG4gICAgICAgICAgICAvPlxyXG4gICAgICAgICAgKX1cclxuICAgICAgICA8L2J1dHRvbj5cclxuICAgICAgICB7c3RhdHVzICE9IFwiQ2FuY2VsbGVkXCIgJiYgY2FuRXhwb3J0ICYmIChcclxuICAgICAgICAgIDxidXR0b25cclxuICAgICAgICAgICAgb25DbGljaz17KCkgPT4gb3Blbk9wdGlvbk1vZGFsKHN1cHBsaWVyX2lkKX1cclxuICAgICAgICAgICAgdGl0bGU9XCJFeHBvcnQgU3VwcGxpZXJcIlxyXG4gICAgICAgICAgPlxyXG4gICAgICAgICAgICA8Rm9udEF3ZXNvbWVJY29uXHJcbiAgICAgICAgICAgICAgaWNvbj17ZmFGaWxlRXhwb3J0fVxyXG4gICAgICAgICAgICAgIHNpemU9XCJsZ1wiXHJcbiAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidGV4dC1za2luLXByaW1hcnlcIlxyXG4gICAgICAgICAgICAvPlxyXG4gICAgICAgICAgPC9idXR0b24+XHJcbiAgICAgICAgKX1cclxuICAgICAgICB7c3RhdHVzICE9IFwiQ2FuY2VsbGVkXCIgJiYgc3RhdHVzICE9IFwiRXhwb3J0ZWRcIiAmJiAoXHJcbiAgICAgICAgICA8YnV0dG9uXHJcbiAgICAgICAgICAgIG9uQ2xpY2s9e2NhbmNlbFByb2R1Y3R9XHJcbiAgICAgICAgICAgIHRpdGxlPVwiQ2FuY2VsIFByb2R1Y3RcIlxyXG4gICAgICAgICAgICBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlclwiXHJcbiAgICAgICAgICA+XHJcbiAgICAgICAgICAgIDxGb250QXdlc29tZUljb25cclxuICAgICAgICAgICAgICBpY29uPXtmYVhtYXJrfVxyXG4gICAgICAgICAgICAgIHNpemU9XCJzbVwiXHJcbiAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiYm9yZGVyIHJvdW5kZWQtc20gYm9yZGVyLXNraW4tcHJpbWFyeSB0ZXh0LXNraW4tcHJpbWFyeSBtLTAgdy1bMTVweF0gaC1bMTVweF1cIlxyXG4gICAgICAgICAgICAvPlxyXG4gICAgICAgICAgPC9idXR0b24+XHJcbiAgICAgICAgKX1cclxuICAgICAgPC9kaXY+XHJcblxyXG4gICAgICA8VHJhbnNpdGlvbiBhcHBlYXIgc2hvdz17aXNDYW5jZWxPcGVufSBhcz17RnJhZ21lbnR9PlxyXG4gICAgICAgIDxEaWFsb2cgYXM9XCJkaXZcIiBjbGFzc05hbWU9XCJyZWxhdGl2ZSB6LTEwXCIgb25DbG9zZT17Y2xvc2VDYW5jZWxNb2RhbH0+XHJcbiAgICAgICAgICA8VHJhbnNpdGlvbi5DaGlsZFxyXG4gICAgICAgICAgICBhcz17RnJhZ21lbnR9XHJcbiAgICAgICAgICAgIGVudGVyPVwiZWFzZS1vdXQgZHVyYXRpb24tMzAwXCJcclxuICAgICAgICAgICAgZW50ZXJGcm9tPVwib3BhY2l0eS0wXCJcclxuICAgICAgICAgICAgZW50ZXJUbz1cIm9wYWNpdHktMTAwXCJcclxuICAgICAgICAgICAgbGVhdmU9XCJlYXNlLWluIGR1cmF0aW9uLTIwMFwiXHJcbiAgICAgICAgICAgIGxlYXZlRnJvbT1cIm9wYWNpdHktMTAwXCJcclxuICAgICAgICAgICAgbGVhdmVUbz1cIm9wYWNpdHktMFwiXHJcbiAgICAgICAgICA+XHJcbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZml4ZWQgaW5zZXQtMCBiZy1ibGFjayBiZy1vcGFjaXR5LTI1IGJhY2tkcm9wLWJsdXItc21cIiAvPlxyXG4gICAgICAgICAgPC9UcmFuc2l0aW9uLkNoaWxkPlxyXG5cclxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZml4ZWQgaW5zZXQtMCBvdmVyZmxvdy15LWF1dG9cIj5cclxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlciBtaW4taC1mdWxsIHAtNCB0ZXh0LWNlbnRlclwiPlxyXG4gICAgICAgICAgICAgIDxUcmFuc2l0aW9uLkNoaWxkXHJcbiAgICAgICAgICAgICAgICBhcz17RnJhZ21lbnR9XHJcbiAgICAgICAgICAgICAgICBlbnRlcj1cImVhc2Utb3V0IGR1cmF0aW9uLTMwMFwiXHJcbiAgICAgICAgICAgICAgICBlbnRlckZyb209XCJvcGFjaXR5LTAgc2NhbGUtOTVcIlxyXG4gICAgICAgICAgICAgICAgZW50ZXJUbz1cIm9wYWNpdHktMTAwIHNjYWxlLTEwMFwiXHJcbiAgICAgICAgICAgICAgICBsZWF2ZT1cImVhc2UtaW4gZHVyYXRpb24tMjAwXCJcclxuICAgICAgICAgICAgICAgIGxlYXZlRnJvbT1cIm9wYWNpdHktMTAwIHNjYWxlLTEwMFwiXHJcbiAgICAgICAgICAgICAgICBsZWF2ZVRvPVwib3BhY2l0eS0wIHNjYWxlLTk1XCJcclxuICAgICAgICAgICAgICA+XHJcbiAgICAgICAgICAgICAgICA8RGlhbG9nLlBhbmVsIGNsYXNzTmFtZT1cIiB3LVs0NSVdIHRyYW5zZm9ybSBvdmVyZmxvdy1oaWRkZW4gcm91bmRlZC14bCBiZy13aGl0ZSAgdGV4dC1sZWZ0IGFsaWduLW1pZGRsZSBzaGFkb3cteGwgdHJhbnNpdGlvbi1hbGxcIj5cclxuICAgICAgICAgICAgICAgICAgey8qIDwhLS0gTW9kYWwgY29udGVudCAtLT4gKi99XHJcbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwicmVsYXRpdmUgYmctd2hpdGUgcm91bmRlZC1sZyBzaGFkb3dcIj5cclxuICAgICAgICAgICAgICAgICAgICB7LyogPCEtLSBNb2RhbCBoZWFkZXIgLS0+ICovfVxyXG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1zdGFydCBqdXN0aWZ5LWJldHdlZW4gcC04IHJvdW5kZWQtdFwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgPGgzIGNsYXNzTmFtZT1cImZsZXggZmxleC1yb3cgdGV4dC14bCBmb250LXNlbWlib2xkIHRleHQtZ3JheS05MDAgIGl0ZW1zLWNlbnRlclwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJmbGV4IGp1c3RpZnktY2VudGVyIGl0ZW1zLWNlbnRlciBib3JkZXIgYm9yZGVyLXNraW4tcHJpbWFyeSB0ZXh0LXNraW4tcHJpbWFyeSB3LVsyNXB4XSBoLVsyNXB4XSB0ZXh0LWNlbnRlciBsZWFkaW5nLTUgcm91bmRlZC1mdWxsIHRleHQtYmFzZSBtZS00XCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgPEZvbnRBd2Vzb21lSWNvbiBpY29uPXtmYUluZm99IC8+e1wiIFwifVxyXG4gICAgICAgICAgICAgICAgICAgICAgICA8L3NwYW4+e1wiIFwifVxyXG4gICAgICAgICAgICAgICAgICAgICAgICBXYXJuaW5nXHJcbiAgICAgICAgICAgICAgICAgICAgICA8L2gzPlxyXG4gICAgICAgICAgICAgICAgICAgICAgPGJ1dHRvblxyXG4gICAgICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXtjbG9zZUNhbmNlbE1vZGFsfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICB0eXBlPVwiYnV0dG9uXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidGV4dC1ibGFjayBiZy10cmFuc3BhcmVudCBob3ZlcjpiZy1ncmF5LTIwMCBob3Zlcjp0ZXh0LWdyYXktOTAwIHJvdW5kZWQtbGcgdGV4dC14bCB3LTggaC04IG1sLWF1dG8gaW5saW5lLWZsZXgganVzdGlmeS1jZW50ZXIgaXRlbXMtY2VudGVyIFwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIGRhdGEtbW9kYWwtaGlkZT1cImRlZmF1bHQtbW9kYWxcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICA8Rm9udEF3ZXNvbWVJY29uXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgaWNvbj17ZmFYbWFya31cclxuICAgICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJ0ZXh0LXNraW4tcHJpbWFyeVwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIC8+e1wiIFwifVxyXG4gICAgICAgICAgICAgICAgICAgICAgPC9idXR0b24+XHJcbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgICAgICAgey8qIDwhLS0gTW9kYWwgYm9keSAtLT4gKi99XHJcbiAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJwLTggcHktMCBzcGFjZS15LTZcIj5cclxuICAgICAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtYmFzZSB4bDp0ZXh0LW1kIDJ4bDp0ZXh0LWxnIGxlYWRpbmctcmVsYXhlZCBtdC0yXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIEFyZSB5b3Ugc3VyZSB5b3Ugd2FudCB0byBjYW5jZWwgc3VwcGxpZXI/XHJcbiAgICAgICAgICAgICAgICAgICAgICA8L3A+XHJcbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgICAgICAgey8qIDwhLS0gTW9kYWwgZm9vdGVyIC0tPiAqL31cclxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtZW5kIHAtNiBzcGFjZS14LTIganVzdGlmeS1lbmRcIj5cclxuICAgICAgICAgICAgICAgICAgICAgIDxidXR0b25cclxuICAgICAgICAgICAgICAgICAgICAgICAgb25DbGljaz17Y2xvc2VDYW5jZWxNb2RhbH1cclxuICAgICAgICAgICAgICAgICAgICAgICAgZGF0YS1tb2RhbC1oaWRlPVwiZGVmYXVsdC1tb2RhbFwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIHR5cGU9XCJidXR0b25cIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJib3JkZXIgYm9yZGVyLXNraW4tcHJpbWFyeSB0ZXh0LXNraW4tcHJpbWFyeSBmb2N1czpyaW5nLTQgZm9jdXM6b3V0bGluZS1ub25lIGZvY3VzOnJpbmctYmx1ZS0zMDAgZm9udC1tZWRpdW0gcm91bmRlZC1tZCB0ZXh0LW1kIHB4LTYgcHktMiB0ZXh0LWNlbnRlciBcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICBOb1xyXG4gICAgICAgICAgICAgICAgICAgICAgPC9idXR0b24+XHJcbiAgICAgICAgICAgICAgICAgICAgICA8YnV0dG9uXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9e3NhdmVNb2RhbERhdGF9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIGRhdGEtbW9kYWwtaGlkZT1cImRlZmF1bHQtbW9kYWxcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICB0eXBlPVwiYnV0dG9uXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidGV4dC13aGl0ZSBiZy1yZWQtNTAwIGhvdmVyOmJnLXJlZC01MDAgZm9jdXM6cmluZy00IGZvY3VzOm91dGxpbmUtbm9uZSBmb2N1czpyaW5nLWJsdWUtMzAwIGZvbnQtbWVkaXVtIHJvdW5kZWQtbWQgdGV4dC1tZCBweC02IHB5LTIgdGV4dC1jZW50ZXIgXCJcclxuICAgICAgICAgICAgICAgICAgICAgID5cclxuICAgICAgICAgICAgICAgICAgICAgICAgWWVzXHJcbiAgICAgICAgICAgICAgICAgICAgICA8L2J1dHRvbj5cclxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgICA8L0RpYWxvZy5QYW5lbD5cclxuICAgICAgICAgICAgICA8L1RyYW5zaXRpb24uQ2hpbGQ+XHJcbiAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgPC9EaWFsb2c+XHJcbiAgICAgIDwvVHJhbnNpdGlvbj5cclxuICAgICAgPFRyYW5zaXRpb24gYXBwZWFyIHNob3c9e2VtYWlsU3RhdHVzUG9wdXB9IGFzPXtGcmFnbWVudH0+XHJcbiAgICAgICAgPERpYWxvZyBhcz1cImRpdlwiIGNsYXNzTmFtZT1cInJlbGF0aXZlIHotMTBcIiBvbkNsb3NlPXtjbG9zZUVtYWlsUG9wdXB9PlxyXG4gICAgICAgICAgPFRyYW5zaXRpb24uQ2hpbGRcclxuICAgICAgICAgICAgYXM9e0ZyYWdtZW50fVxyXG4gICAgICAgICAgICBlbnRlcj1cImVhc2Utb3V0IGR1cmF0aW9uLTMwMFwiXHJcbiAgICAgICAgICAgIGVudGVyRnJvbT1cIm9wYWNpdHktMFwiXHJcbiAgICAgICAgICAgIGVudGVyVG89XCJvcGFjaXR5LTEwMFwiXHJcbiAgICAgICAgICAgIGxlYXZlPVwiZWFzZS1pbiBkdXJhdGlvbi0yMDBcIlxyXG4gICAgICAgICAgICBsZWF2ZUZyb209XCJvcGFjaXR5LTEwMFwiXHJcbiAgICAgICAgICAgIGxlYXZlVG89XCJvcGFjaXR5LTBcIlxyXG4gICAgICAgICAgPlxyXG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZpeGVkIGluc2V0LTAgYmctYmxhY2sgYmctb3BhY2l0eS0yNSBiYWNrZHJvcC1ibHVyLXNtXCIgLz5cclxuICAgICAgICAgIDwvVHJhbnNpdGlvbi5DaGlsZD5cclxuXHJcbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZpeGVkIGluc2V0LTAgb3ZlcmZsb3cteS1hdXRvXCI+XHJcbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXIgbWluLWgtZnVsbCBwLTQgdGV4dC1jZW50ZXJcIj5cclxuICAgICAgICAgICAgICA8VHJhbnNpdGlvbi5DaGlsZFxyXG4gICAgICAgICAgICAgICAgYXM9e0ZyYWdtZW50fVxyXG4gICAgICAgICAgICAgICAgZW50ZXI9XCJlYXNlLW91dCBkdXJhdGlvbi0zMDBcIlxyXG4gICAgICAgICAgICAgICAgZW50ZXJGcm9tPVwib3BhY2l0eS0wIHNjYWxlLTk1XCJcclxuICAgICAgICAgICAgICAgIGVudGVyVG89XCJvcGFjaXR5LTEwMCBzY2FsZS0xMDBcIlxyXG4gICAgICAgICAgICAgICAgbGVhdmU9XCJlYXNlLWluIGR1cmF0aW9uLTIwMFwiXHJcbiAgICAgICAgICAgICAgICBsZWF2ZUZyb209XCJvcGFjaXR5LTEwMCBzY2FsZS0xMDBcIlxyXG4gICAgICAgICAgICAgICAgbGVhdmVUbz1cIm9wYWNpdHktMCBzY2FsZS05NVwiXHJcbiAgICAgICAgICAgICAgPlxyXG4gICAgICAgICAgICAgICAgPERpYWxvZy5QYW5lbCBjbGFzc05hbWU9XCIgdy1bNDUlXSB0cmFuc2Zvcm0gb3ZlcmZsb3ctaGlkZGVuIHJvdW5kZWQteGwgYmctd2hpdGUgIHRleHQtbGVmdCBhbGlnbi1taWRkbGUgc2hhZG93LXhsIHRyYW5zaXRpb24tYWxsXCI+XHJcbiAgICAgICAgICAgICAgICAgIHsvKiA8IS0tIE1vZGFsIGNvbnRlbnQgLS0+ICovfVxyXG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInJlbGF0aXZlIGJnLXdoaXRlIHJvdW5kZWQtbGcgc2hhZG93XCI+XHJcbiAgICAgICAgICAgICAgICAgICAgey8qIDwhLS0gTW9kYWwgaGVhZGVyIC0tPiAqL31cclxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtc3RhcnQganVzdGlmeS1iZXR3ZWVuIHAtOCByb3VuZGVkLXRcIj5cclxuICAgICAgICAgICAgICAgICAgICAgIDxoMyBjbGFzc05hbWU9XCJmbGV4IGZsZXgtcm93IHRleHQteGwgZm9udC1zZW1pYm9sZCB0ZXh0LWdyYXktOTAwICBpdGVtcy1jZW50ZXJcIj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwiZmxleCBqdXN0aWZ5LWNlbnRlciBpdGVtcy1jZW50ZXIgYm9yZGVyIGJvcmRlci1za2luLXByaW1hcnkgdGV4dC1za2luLXByaW1hcnkgdy1bMjVweF0gaC1bMjVweF0gdGV4dC1jZW50ZXIgbGVhZGluZy01IHJvdW5kZWQtZnVsbCB0ZXh0LWJhc2UgbWUtNFwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxGb250QXdlc29tZUljb24gaWNvbj17ZmFJbmZvfSAvPntcIiBcIn1cclxuICAgICAgICAgICAgICAgICAgICAgICAgPC9zcGFuPntcIiBcIn1cclxuICAgICAgICAgICAgICAgICAgICAgICAgU3RhdHVzIE1lc3NhZ2VcclxuICAgICAgICAgICAgICAgICAgICAgIDwvaDM+XHJcbiAgICAgICAgICAgICAgICAgICAgICA8YnV0dG9uXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9e2Nsb3NlRW1haWxQb3B1cH1cclxuICAgICAgICAgICAgICAgICAgICAgICAgdHlwZT1cImJ1dHRvblwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInRleHQtYmxhY2sgYmctdHJhbnNwYXJlbnQgaG92ZXI6YmctZ3JheS0yMDAgaG92ZXI6dGV4dC1ncmF5LTkwMCByb3VuZGVkLWxnIHRleHQteGwgdy04IGgtOCBtbC1hdXRvIGlubGluZS1mbGV4IGp1c3RpZnktY2VudGVyIGl0ZW1zLWNlbnRlciBcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICBkYXRhLW1vZGFsLWhpZGU9XCJkZWZhdWx0LW1vZGFsXCJcclxuICAgICAgICAgICAgICAgICAgICAgID5cclxuICAgICAgICAgICAgICAgICAgICAgICAgPEZvbnRBd2Vzb21lSWNvblxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIGljb249e2ZhWG1hcmt9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidGV4dC1za2luLXByaW1hcnlcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAvPntcIiBcIn1cclxuICAgICAgICAgICAgICAgICAgICAgIDwvYnV0dG9uPlxyXG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgICAgICAgIHsvKiA8IS0tIE1vZGFsIGJvZHkgLS0+ICovfVxyXG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwicC04IHB5LTAgc3BhY2UteS02XCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LWJhc2UgeGw6dGV4dC1tZCAyeGw6dGV4dC1sZyBsZWFkaW5nLXJlbGF4ZWQgbXQtMlwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICB7cG9wdXBNZXNzYWdlfVxyXG4gICAgICAgICAgICAgICAgICAgICAgPC9wPlxyXG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgICAgICAgIHsvKiA8IS0tIE1vZGFsIGZvb3RlciAtLT4gKi99XHJcbiAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWVuZCBwLTYgc3BhY2UteC0yIGp1c3RpZnktZW5kXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICA8YnV0dG9uXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9e2Nsb3NlRW1haWxQb3B1cH1cclxuICAgICAgICAgICAgICAgICAgICAgICAgZGF0YS1tb2RhbC1oaWRlPVwiZGVmYXVsdC1tb2RhbFwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIHR5cGU9XCJidXR0b25cIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJib3JkZXIgdGV4dC1kYXJrLWdyYXkgZm9jdXM6cmluZy00IGZvY3VzOm91dGxpbmUtbm9uZSBmb2N1czpyaW5nLWJsdWUtMzAwIGZvbnQtbWVkaXVtIHJvdW5kZWQtbWQgdGV4dC1tZCBweC02IHB5LTIgdGV4dC1jZW50ZXIgXCJcclxuICAgICAgICAgICAgICAgICAgICAgID5cclxuICAgICAgICAgICAgICAgICAgICAgICAgT2tcclxuICAgICAgICAgICAgICAgICAgICAgIDwvYnV0dG9uPlxyXG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICAgIDwvRGlhbG9nLlBhbmVsPlxyXG4gICAgICAgICAgICAgIDwvVHJhbnNpdGlvbi5DaGlsZD5cclxuICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICA8L0RpYWxvZz5cclxuICAgICAgPC9UcmFuc2l0aW9uPlxyXG4gICAgPC8+XHJcbiAgKTtcclxufTtcclxuXHJcbmV4cG9ydCBkZWZhdWx0IGFjdGlvblJlbmRlcmVyO1xyXG4iXSwibmFtZXMiOlsiRm9udEF3ZXNvbWVJY29uIiwiZmFDb3B5IiwiZmFQZW5Ub1NxdWFyZSIsImZhRXllIiwiZmFGaWxlRXhwb3J0IiwiUm91dGVyIiwidXNlUm91dGVyIiwiTGluayIsImFwaUNvbmZpZyIsInVzZVN0YXRlIiwidXNlRWZmZWN0IiwiRnJhZ21lbnQiLCJDb29raWVzIiwiWExTWCIsImV4cG9ydEV4Y2VsRGF0YSIsIlRvYXN0Q29udGFpbmVyIiwidG9hc3QiLCJ1c2VVc2VyIiwiZ2V0Q29va2llRGF0YSIsIkNvcHlTdXBwbGllciIsIkRpYWxvZyIsIlRyYW5zaXRpb24iLCJmYUluZm8iLCJmYUNsb3NlIiwiZmFYbWFyayIsInVzZVBlcm1pc3Npb25zIiwiZXhwb3J0RXhjZWwiLCJhY3Rpb25SZW5kZXJlciIsInBhcmFtcyIsInVzZXJEYXRhIiwidG9rZW4iLCJjb21wYW55Iiwic3VwcGxpZXJEYXRhIiwicm91dGVyIiwiY2FuRXhwb3J0Iiwicm9sZV9pZCIsImRhdGEiLCJpc0VtZXJnZW5jeVJlcXVlc3QiLCJwZXJtaXNzaW9ucyIsInVwZGF0ZVBlcm1pc3Npb25zIiwic2VydmVyQWRkcmVzcyIsInVzZXJEZXRhaWxzIiwidXBkYXRlVG9rZW4iLCJwcm9waGV0X2lkIiwicHJvcGhldHMiLCJsZW5ndGgiLCJyb2xlX2lkcyIsInJvbGVJZCIsIm1hcCIsImVsZSIsInN1cHBsaWVyX2lkIiwiaWQiLCJzdXBwbGllcl9zdGF0dXMiLCJzdGF0dXMiLCJzZXRTdGF0dXMiLCJzZXREYXRhIiwidXNlciIsImlzT3BlbiIsInNldElzT3BlbiIsImlzT3Blbk9wdGlvbiIsInNldElzT3Blbk9wdGlvbiIsInNlbGVjdGVkRXhwb3J0VHlwZSIsInNldFNlbGVjdGVkRXhwb3J0VHlwZSIsImVtYWlsU3RhdHVzUG9wdXAiLCJzZXRFbWFpbFN0YXR1c1BvcHVwIiwicG9wdXBNZXNzYWdlIiwic2V0UG9wVXBNZXNzYWdlIiwiaW50ZXJuYWxFeHBvcnRTdWNjZXNzIiwic2V0SW50ZXJuYWxFeHBvcnRTdWNjZXNzIiwiSVNTRXhwb3J0U3VjY2VzcyIsInNldElTU0V4cG9ydFN1Y2Nlc3MiLCJjbG9zZU9wdGlvbk1vZGFsIiwiaXNDYW5jZWxPcGVuIiwic2V0SXNDYW5jZWxPcGVuIiwiY2FuY2VsUHJvZHVjdCIsImNsb3NlQ2FuY2VsTW9kYWwiLCJjbG9zZUVtYWlsUG9wdXAiLCJzZXRVcGRhdGVTdGF0dXNDaGFuZ2UiLCJzYXZlTW9kYWxEYXRhIiwiZmV0Y2giLCJtZXRob2QiLCJoZWFkZXJzIiwiQWNjZXB0IiwiQXV0aG9yaXphdGlvbiIsImJvZHkiLCJKU09OIiwic3RyaW5naWZ5Iiwic2VjdGlvbk5hbWUiLCJ0eXBlIiwidXBkYXRlZF9kYXRlIiwiRGF0ZSIsInRvSVNPU3RyaW5nIiwiY29tcGFueV9uYW1lIiwicmVxdWVzdG9yX25hbWUiLCJyZXF1ZXN0b3IiLCJyZXF1ZXN0b3JfZW1haWwiLCJ0aGVuIiwicmVzIiwiZXJyb3IiLCJqc29uIiwiUHJvbWlzZSIsInJlamVjdCIsInN1Y2Nlc3MiLCJwb3NpdGlvbiIsImNhdGNoIiwiZXJyIiwic3RhdHVzVGV4dCIsIm9wZW5PcHRpb25Nb2RhbCIsIkdlbmVyYWwiLCJpc0V4cG9ydGFibGVCYXNlZE9uQ29kZVVuaXF1ZSIsImNvZGVDb3VudCIsImNvZGVfY291bnQiLCJwcm9waGV0Q29kZSIsInByb3BoZXRfY29kZSIsImlzU3VwcGxpZXJBY2NvdW50Iiwicm9sZUlkcyIsImluY2x1ZGVzIiwiY3VycmVuY3kiLCJhY3R1YWxDdXJyIiwiaXNWYWxpZCIsInJlZ2V4UGF0dGVybiIsIlJlZ0V4cCIsInRlc3QiLCJoYW5kbGVTaW5nbGVFeHBvcnRTdXBwbGllciIsImVkaXRTdXBwbGllciIsInB1c2giLCJwYXRobmFtZSIsImNvbmZpcm1QYWdlIiwibWFwcGVkUGVybWlzc2lvbnMiLCJ1bmlxdWVTZWN0aW9ucyIsIlNldCIsImZsYXRNYXAiLCJpdGVtIiwibG9jYWxTdG9yYWdlIiwic2V0SXRlbSIsImdldEdMQ29kZSIsImludGVybmFsX2xlZGdlcl9jb2RlIiwiZGVwYXJ0bWVudCIsImV4dHJhY3RDb250YWN0cyIsImNvbnRhY3RzSnNvblN0ciIsImNvbnRhY3RzIiwicGFyc2UiLCJBcnJheSIsImlzQXJyYXkiLCJjb250YWN0IiwidHJpbSIsIk5hbWUiLCJlbWFpbF9pZCIsInRlbGVwaG9uZSIsImNvbnNvbGUiLCJleHRyYWN0U2VuZGFjR3JvdXAiLCJzZW5kYWNHcm91cEpzb24iLCJzZW5kYWNHcm91cHMiLCJmaWx0ZXJlZEdyb3VwcyIsImZpbHRlciIsImdyb3VwIiwiY3JlYXRlZF9ieSIsIkRlc2NyaXB0aW9uIiwibGFiZWwiLCJtdWx0aXBsZVNlbmRSb2xlT25Sb2xlTnVtcyIsInJvbGVOdW1zIiwicm9sZV9udW0iLCJzcGxpdCIsIm51bSIsIlNlbmRhY3JvbGVpZCIsIlR5cGUiLCJnbG9iYWxfZ2FwX251bWJlciIsIkFjdGl2ZSIsImdldCIsInJvbGVzQXJyYXkiLCJmb3JtYXR0ZWREaXN0cmlidXRpb25EYXRhIiwiZGlzdHJpYnV0aW9uX3BvaW50c19qc29uIiwicm93IiwiZGlzdHJpYnV0aW9uUG9pbnQiLCJuYW1lIiwiZGlyZWN0RFB2YWx1ZSIsImRpcmVjdF9kcCIsImRpcmVjdERQIiwiZnJvbV9kcCIsImZpbHRlcmVkSW50ZXJuYWxFeHBvcnREYXRhIiwiZmlsdGVyZWRJU1NFeHBvcnREYXRhIiwiY3VycmVuY3lfaWQiLCJzb3J0X2NvZGUiLCJhY2NvdW50X251bWJlciIsInN3aWZ0QmljQ29kZSIsImliYW4iLCJzd2lmdEJpY1JlZ2V4IiwiZGVjcnlwdGVkU29ydF9CaWMiLCJoYXNfaWJhbiIsImRlY3J5cHRlZEFjY291bnROdW1iZXIiLCJzbGljZSIsInJlZ2lvbmFsX2NlcnQiLCJjb3VudHJ5X2NvZGUiLCJyZWRfdHJhY3RvciIsInB1Y19jb2RlIiwiY2hpbGVfY2VydGlmaWNhdGVfbnVtYmVyIiwiY3VycmVuY3lJZCIsImN1cnJlbmN5TmFtZSIsImN1cnJlbmN5X25hbWUiLCJnZXRDb3JyZXNwb25kaW5nVXNlckxvb2t1cCIsImN1cnIiLCJsb2ciLCJzdXBwbGllcl90eXBlIiwidW5kZWZpbmVkIiwiQWRkcmVzcyIsIlBvcnQiLCJEb3JtYW50IiwiaXNBY3RpdmUiLCJ2YXRfbnVtYmVyIiwib3JnYW5pY19jZXJ0aWZpY2F0ZV9udW1iZXIiLCJzdXBwbGllcl9ncm91cCIsInZhbHVlIiwiYWRkcmVzc19saW5lXzEiLCJhZGRyZXNzX2xpbmVfMiIsImFkZHJlc3NfbGluZV8zIiwiYWRkcmVzc19saW5lXzQiLCJwb3N0YWxfY29kZSIsInBheW1lbnRfdGVybXMiLCJjdXJyZW5jeV9jb2RlIiwicGF5bWVudF90eXBlIiwicGF5bWVudF90eXBlX25hbWUiLCJWYXRhYmxlIiwidmF0YWJsZSIsIkJ1eWVyIiwiZWRpIiwiRnJvbURQIiwiYmFjc19jdXJyZW5jeV9jb2RlIiwiQmFua2FjaWQiLCJTZWxlY3RlZCIsInN1cHBsaWVyTmFtZSIsImlzRW1lcmdlbmN5QW5kRmluYW5jZU5vdENvbXBsZXRlIiwic3VwcGxpZXJDb2RlIiwiYWRkRGF0YVRvU2hlZXQiLCJzaGVldEluZGV4IiwiZGF0YVRvQWRkIiwiY29udGFjdHNfanNvbiIsImV4dHJhY3RlZFNlbmRhY0dyb3VwIiwic2VuZGFjUm9sZU9uUm9sZU51bXMiLCJleHBvcnRfSVNTX3Jlc3BvbnNlIiwiaXNzX2xlZGdlcl9jb2RlIiwiYWRkU2VuZGFjUm9sZURhdGFUb1NoZWV0IiwiZXhwb3J0SW50ZXJuYWxfcmVzcG9uc2UiLCJhdXRvQ2xvc2UiLCJoaWRlUHJvZ3Jlc3NCYXIiLCJjbG9zZU9uQ2xpY2siLCJwYXVzZU9uSG92ZXIiLCJkcmFnZ2FibGUiLCJwcm9ncmVzcyIsInRoZW1lIiwiaGFuZGxlQ29weVN1cHBsaWVyIiwiZGlzYWJsZWRDbGFzcyIsImhhbmRsZUV4cG9ydFR5cGUiLCJlIiwidGFyZ2V0IiwiZGl2IiwiY2xhc3NOYW1lIiwiYnV0dG9uIiwib25DbGljayIsImljb24iLCJzaXplIiwidGl0bGUiLCJhcHBlYXIiLCJzaG93IiwiYXMiLCJvbkNsb3NlIiwiQ2hpbGQiLCJlbnRlciIsImVudGVyRnJvbSIsImVudGVyVG8iLCJsZWF2ZSIsImxlYXZlRnJvbSIsImxlYXZlVG8iLCJQYW5lbCIsImgzIiwic3BhbiIsImRhdGEtbW9kYWwtaGlkZSIsInAiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///./utils/renderer/actionRenderer.js\n"));

/***/ })

});