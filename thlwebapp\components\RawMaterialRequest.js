import React, {
  useState,
  useCallback,
  useRef,
  useMemo,
  useEffect,
  Fragment,
} from "react";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import prophetLogo from "@/public/images/ProphetLogo.png";
import {
  faPlus,
  faInfoCircle,
  faSearch,
  faInfo,
  faXmark,
  faTriangleExclamation,
} from "@fortawesome/free-solid-svg-icons";
import "ag-grid-community/styles//ag-grid.css";
import "ag-grid-community/styles//ag-theme-alpine.css";
import Layout from "@/components/Layout";
import { ThreeCircles } from "react-loader-spinner";
import { ToastContainer, toast } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import rawMaterialScenarios from "@/public/images/rawMaterialScenarios.jpg";
import DrawerComponent from "./DrawerComponent";
import "ag-grid-community/styles//ag-grid.css";
import "ag-grid-community/styles//ag-theme-alpine.css";
import { apiConfig } from "@/services/apiConfig";
import { Dialog, Transition } from "@headlessui/react";
import { Router, useRouter } from "next/router";
import Select from "react-select";
import Cookies from "js-cookie";
import Image from "next/image";
import { debounce } from "lodash";
import exportExcelData from "../utils/exportExcel";
//
import { useMsal } from "@azure/msal-react";
import { getCookieData } from "@/utils/getCookieData";
import { useLoading } from "@/utils/loaders/loadingContext";

const customSelectStyles = {
  // Default style
  control: (base) => ({
    ...base,
    height: "28px",
    minHeight: "28px",
    // border: 0,
  }),
  // Style when the condition is true
  option: (base, { data }) => ({
    ...base,
    color: data.is_new == true ? "red" : "",
  }),

  valueContainer: (provided, state) => ({
    ...provided,
    height: "28px",
    padding: "0 6px",
  }),

  input: (provided, state) => ({
    ...provided,
    margin: "0px",
  }),
  indicatorSeparator: (state) => ({
    display: "none",
  }),
  indicatorsContainer: (provided, state) => ({
    ...provided,
    height: "28px",
  }),
};

const RawMaterialRequest = ({
  dropdowns,
  userData,
  rawMaterialData,
  pageType,
}) => {
  // #region states
  const { instance, accounts } = useMsal();
  const router = useRouter();  const { setIsLoading } = useLoading();

  // const user = getCookieData("user");
  const [loading, setLoading] = useState(false);
  const gridRef = useRef();
  const [nameOfOriginator, setNameOfOriginator] = useState("");
  const [isRawMaterialImage, setIsRawMaterialImage] = useState(false);
  const [requestNumber, setRequestNumber] = useState("");
  const [reasonForRequest, setReasonForRequest] = useState("");
  const [reasonForRequestLabel, setReasonForRequestLabel] = useState("");
  const [expectedDeliveryDate, setExpectedDeliveryData] = useState("");
  const [productDescription, setProductDescription] = useState("");
  const [supplierDescription, setSupplierDescription] = useState("");
  const [masterProductCode, setMasterProductCode] = useState("");
  // const [sortGroupCode, setSortGroupCode] = useState("");
  const [masterProductCodeLabel, setMasterProductCodeLabel] = useState("");
  const [markVariety, setMarkVariety] = useState("");
  const [markVarietyLabel, setMarkVarietyLabel] = useState("");
  const [countSize, setCountSizse] = useState("");
  const [unitsInOuter, setUnitsInOuter] = useState("");
  const [casesPerPallet, setCasesPerPallet] = useState("");
  const [netWeightCase, setNetWeightCase] = useState("");
  const [grossWeightCase, setGrossWeightCase] = useState("");
  const [selectedsubProductCode, setSelectedSubProductCode] = useState("");
  const [subProductCode, setSubProductCode] = useState("");
  const [temperatureGrade, setTemperatureGrade] = useState("");
  const [temperatureGradeLabel, setTemperatureGradeLabel] = useState("");
  const [classRequired, setClassRequired] = useState("");
  const [intrastCommodityCode, setIntrastCommodityCode] = useState("");
  const [intrastCommodityCodeLabel, setIntrastCommodityCodeLabel] =
    useState("");
  const [productId, setProductId] = useState("");
  const [brand, setBrand] = useState("");
  // const [sortGroup, setSortGroup] = useState("");
  // const [sortGroupLabel, setSortGroupLabel] = useState("");
  const [brandLabel, setBrandLabel] = useState("");
  const [countryOfOrigin, setCountryOfOrigin] = useState("");
  const [countryOfOriginLabel, setCountryOfOriginLabel] = useState("");
  const [calibreSize, setCalibreSize] = useState("");
  const [calibreSizeLabel, setCalibreSizeLabel] = useState("");
  const [endCustomer, setEndCustomer] = useState("");
  const [endCustomerLabel, setEndCustomerLabel] = useState("");
  const [isOpen, setIsOpen] = useState(false);
  const [isSubmitModalOpen, setIsSubmitModalOpen] = useState(false);
  const [isCancelled, setIsCancelled] = useState(false);
  const [isContinue, setIsContinue] = useState(true);
  const [variety, setVariety] = useState("");
  const [varietyLabel, setVarietyLabel] = useState("");
  const [classifiedAllergicTypes, setClassifiedAllergicTypes] = useState("");
  const [organicCertification, setOrganicCertification] = useState("");
  const [productType, setProductType] = useState("");
  const [isEdit, setIsEdit] = useState(false);
  const [isCancelOpen, setIsCancelOpen] = useState(false);
  const [cancelledReasonapi, setCancelledReasonapi] = useState("");
  const [unblockedReasonapi, setUnblockedReasonapi] = useState("");
  const [isUserHaveCreated, setIsUserHaveCreated] = useState(false);
  const [disabledClass, setDisabledClass] = useState("");
  const serverAddress = apiConfig.serverAddress;
  const [isDrawerOpen, setIsDrawerOpen] = useState(false);
  const [title, setTitle] = useState("");
  const [dataKey, setDataKey] = useState("");
  const [dropdownData, setDropDownData] = useState("");
  const [placeholderText, setPlaceholderText] = useState("");
  const [legend, setLegend] = useState("");
  const [minLength, setMinLength] = useState("");
  const [maxLength, setMaxLength] = useState("");
  const [commodityCodes, setCommodityCodes] = useState([]);
  const [allDropdown, setAlldropDown] = useState({});
  const [isUnblockOpen, setIsUnblockOpen] = useState(false);
  const [isUnblockRequest, setIsUnblockRequest] = useState(false);
  const [status, setStatus] = useState("");

  const [submissionType, setSubmissionType] = useState("");
  const [submittedToISS, setSubmittedToISS] = useState();
  const [cancelledBy, setCancelledBy] = useState("");
  const [cancelledDate, setCancelledDate] = useState("");
  const [isSubmittedToIssRequest, setIsSubmittedToIssRequest] = useState(false);
  const [rawMasterCode, setRawMasterCode] = useState("");
  const [rawVarietyCode, setRawVarietyCode] = useState("");
  const [rawEndCustomerCode, setRawEndCustomerCode] = useState("");
  const [rawCaliberSizeCode, setRawCaliberSizeCode] = useState("");
  const [rawCOOCode, setRawCOOCode] = useState("");
  const [rawBrandCode, setRawBrandCode] = useState("");
  const [emailComment, setEmailComment] = useState("");
  const [companyName, setCompanyName] = useState("");
  const [
    disableCustomSubProductCodeInput,
    setDisableCustomSubProductCodeInput,
  ] = useState(true);

  const [isValidProductType, setIsValidProductType] = useState(true);
  const [isValidReasonForRequest, setIsValidReasonForRequest] = useState(true);
  const [isValidProductInformation, setIsValidProductInformation] =
    useState(true);
  const [isValidExpectedDate, setIsValidExpectedDate] = useState(true);
  const [isValidSupplierDesc, setIsValidSupplierDesc] = useState(true);
  const [isValidMasterCode, setIsValidMasterCode] = useState(true);
  const [isValidCountOrSize, setIsValidCountOrSize] = useState(true);
  const [isValidUnitsInOuter, setIsValidUnitsInOuter] = useState(true);
  const [isValidProductGroup, setIsValidProductGroup] = useState(true);
  const [isValidCasesPerPallet, setIsValidCasesPerPallet] = useState(true);
  const [isValidNetWeightOuter, setIsValidNetWeightOuter] = useState(true);
  const [isValidGrossWeightOuter, setIsValidGrossWeightOuter] = useState(true);
  const [isValidMark, setIsValidMark] = useState(true);
  const [isValidSubProductCode, setIsValidSubProductCode] = useState(true);
  const [isValidTemperatureGrade, setIsValidTemperatureGrade] = useState(true);
  const [isValidOrganicCertification, setIsValidOrganicCertification] =
    useState(true);
  const [
    subProductCodeValueAlreadyExists,
    setSubProductCodeValueAlreadyExists,
  ] = useState(false);
  // const [isValidSortGroup, setIsValidSortGroup] = useState(true);
  // const [isProductGroups, setIsProductGroups] = useState(false);

  const [validSubmitMode, setValidSubmitMode] = useState(false);
  const [isReadyToSubmit, setIsReadyToSubmit] = useState(false);
  const [isValidUnblockReason, setIsValidUnblockReason] = useState(true);
  const [isValidCancelReason, setIsValidCancelReason] = useState(true);
  const [productGroups, setProductGroups] = useState([]);
  const [productGroup, setProductGroup] = useState([]);
  const [nameOfOriginatorEmail, setNameOfOriginatorEmail] = useState("");
  const [subProductCodeData, setSubProductCodeData] = useState([]);
  // #endregion
  const brandRef = useRef(null);
  // const sortGroupRef = useRef(null);
  const cooRef = useRef(null);
  const caliberSizeRef = useRef(null);
  const endCustomerRef = useRef(null);
  const varietyRef = useRef(null);
  const masterProdRef = useRef(null);
  const groupRef = useRef(null);
  const [reload, setReload] = useState(false);
  const [prophetId,setProphetId]=useState("1")
  // The debounced function to check if the value exists
  const checkIfSubProductCodeExists = useCallback(
    debounce((value) => {
      if (value) {
        // console.log("subProductCodeData", subProductCodeData);
        if (subProductCodeData?.find((item) => item.label === value)) {
          setSubProductCodeValueAlreadyExists(true);
          // console.log("This sub product conde value already exists.");
        } else {
          value ? setSubProductCodeValueAlreadyExists(false) : "";
          // console.log("no sub product conde value exists"); // Clear the message if value is unique
        }
      } else {
        // console.log("value does not exist");
        setSubProductCodeValueAlreadyExists(false); // Handle case when subProductCode is missing
      }
    }, 500),
    [subProductCodeData]
  ); // 500ms delay before the check is made
  // console.log(updatedOptions);

  const openSelect = (ref) => {
    if (ref.current) {
      ref.current.focus();
      ref.current.setState({ menuIsOpen: true });
    }
  };

  const trimInputText = (input) => {
    return input.trim();
  };

  const handleProductInformation = (valueInputed) => {
    const trimmedValue = trimInputText(valueInputed);
    setProductDescription(trimmedValue);
    handleProductDesc(trimmedValue);
  };

  const handleNewDropdownData = (dataKey, newData) => {
    // // Update the dropdown data based on the dataKey
    switch (dataKey) {
      case "masterProductCode":
        setAlldropDown((prevDropdowns) => ({
          ...prevDropdowns,
          masterProductCode: newData,
        }));
        break;
      case "markVariety":
        setAlldropDown((prevDropdowns) => ({
          ...prevDropdowns,
          markVariety: newData,
        }));
        break;
      case "brand":
        setAlldropDown((prevDropdowns) => ({
          ...prevDropdowns,
          brand: newData,
        }));
        break;
      case "end_customer":
        setAlldropDown((prevDropdowns) => ({
          ...prevDropdowns,
          endCustomer: newData,
        }));
        break;
      case "countryOfOrigin":
        setAlldropDown((prevDropdowns) => ({
          ...prevDropdowns,
          countryOfOrigin: newData,
        }));
        break;
      case "caliberSize":
        setAlldropDown((prevDropdowns) => ({
          ...prevDropdowns,
          caliberSize: newData,
        }));
        break;
      case "variety":
        setAlldropDown((prevDropdowns) => ({
          ...prevDropdowns,
          variety: newData,
        }));
        break;
      // Add cases for other dataKey values as needed
      default:
        break;
    }
  };

  const generateSequence = (start, end, length) => {
    const sequence = [];
    for (let i = start; i <= end; i++) {
      sequence.push(String(i).padStart(length, "0"));
    }
    return sequence;
  };

  const handleIconClick = (dropDownType) => {
    const dropdownData = {
      master_product: {
        title: "Master Product Code",
        placeholder: "Master Product Code(5 Characters)",
        legend: "Add New Master Product Code",
        key: "masterProductCode",
        max_length: 5,
        min_length: 5,
      },
      mark_variety: {
        title: "Mark/Variety",
        placeholder: "Mark/Variety(5 Characters)",
        legend: "Add New Mark/Variety",
        key: "markVariety",
        max_length: 5,
        min_length: 5,
      },
      brand: {
        title: "Brand",
        placeholder: "Brand(5 Characters)",
        legend: "Add New Brand",
        key: "brand",
        max_length: 5,
        min_length: 1,
      },
      end_customer: {
        title: "End Customer",
        placeholder: "End Customer(5 Characters)",
        legend: "Add New End Customer",
        key: "end_customer",
        max_length: 5,
        min_length: 1,
      },
      country_of_origin: {
        title: "New Country of Origin",
        placeholder: "New Country of Origin(2 Characters)",
        legend: "Add New Country of Origin",
        key: "countryOfOrigin",
        max_length: 2,
        min_length: 2,
      },
      caliber_size: {
        title: "New Caliber Size",
        placeholder: "New Caliber Size(5 Characters)",
        legend: "Add New Caliber Size",
        key: "caliberSize",
        max_length: 5,
        min_length: 1,
      },
      variety: {
        title: "New Variety",
        placeholder: "New Variety(5 Characters)",
        legend: "Add New Variety",
        key: "variety",
        max_length: 5,
        min_length: 5,
      },
    };

    const data = dropdownData[dropDownType];

    setTitle(data.title);
    setPlaceholderText(data.placeholder);
    setLegend(data.legend);
    setDataKey(data.key);
    setMinLength(data.min_length);
    setMaxLength(data.max_length);

    if (allDropdown && allDropdown[data.key]) {
      setDropDownData(allDropdown[data.key]);
    }

    setIsDrawerOpen(true);
  };

  const handleClassifiedAllergicTypes = (data) => {
    setClassifiedAllergicTypes([data]);
  };

  const handleReasonChange = (data) => {
    //const selectedIndex = e.target.options.selectedIndex;
    if (data) {
      setReasonForRequest([data]);
      setIsValidReasonForRequest(true);
    } else {
      setReasonForRequest([data]);
      setIsValidReasonForRequest(false);
    }
    //setReasonForRequestLabel(e.target.options[selectedIndex].text);
  };
  

  const handleMasterProductChange = (data) => {
    setMasterProductCode([data]);
    setSubProductCode("");
    setSelectedSubProductCode("");
    setSubProductCodeData([]);
    if (data) {
      fetch(`${serverAddress}products/get-subproduct-code/${data.code}?prophetId=${prophetId}`, {
        method: "GET",
        headers: {
          Authorization: `Bearer ${userData.token}`,
          Accept: "application/json",
          "Content-Type": "application/json",
        },
      })
        .then((res) => {
          if (res.status === 200) {
            return res.json();
          }
          return Promise.reject(res);
        })
        .then((data) => {
          if (data) {
            let subProductsList = [{ value: 0, label: "New Code" }];
            subProductsList.push(...data);
            setSubProductCodeData(subProductsList);
          }
        })
        .catch((error) => {
          console.log(error);
        });

      setRawMasterCode(data.code);
      setIsValidMasterCode(true);
      const masterCode = data.label.indexOf("Organic");
      // console.log("master code", data.code);
      if (masterCode != "-1") {
        setIsValidOrganicCertification(false);
      } else {
        setIsValidOrganicCertification(true);
      }
    } else {
      setRawMasterCode("Click + to Add a New Master Code");
      setIsValidMasterCode(false);
    }

    // if (data?.is_new == true) {
    // getProductGroups(userData);
    // } else {
    // setIsProductGroups(false);
    //setProductGroup([])
    // }
  };

  const handleMarkVarietyChange = (data) => {
    //const selectedIndex = e.target.options.selectedIndex;

    setMarkVariety(data?.currentTarget ? [] : [data]);
    if (data) {
      setIsValidMark(true);
      // setIsValidSubProductCode(true);
    } else {
      setIsValidMark(false);
    }
  };
  //#region sub pro code
  const handleSelectedSubproductCodeDropdown = async (data) => {
    await setSelectedSubProductCode(data?.currentTarget ? [] : [data]);
    await setSubProductCode(data?.currentTarget ? [] : data?.label);
    await setDisableCustomSubProductCodeInput(
      data?.currentTarget ? false : true
    );
    // console.log("data",data,"markVariety",markVariety);
    if (data) {
      if (data?.value === 0) {
        let generatedSubProductCode = "";
        let variety_code = "";
        let unit = "";
        const company = Cookies.get("company");
        const isUserFromDpsOrDpsLtd =
          company == "dps" || company == "dpsltd" || company == "dpsmns";
        const finalCountOrSize = countSize?.toUpperCase()
          ? countSize.toUpperCase()
          : 0;
        const InitialCode = isUserFromDpsOrDpsLtd
          ? finalCountOrSize
          : `RM${finalCountOrSize}`;

        if (markVariety[0]?.label == "Loose") {
          variety_code = "LSE";
          generatedSubProductCode = `${InitialCode}LSE`;
        } else if (markVariety[0]?.label == "Pack") {
          generatedSubProductCode = `${InitialCode}PACK`;
        } else if (markVariety[0]?.label == "Punnet") {
          generatedSubProductCode = `${InitialCode}PUN`;
        } else {
          generatedSubProductCode = InitialCode;
        }
        // console.log("log at genration");
        await checkIfSubProductCodeExists(generatedSubProductCode);
        await setSubProductCode(generatedSubProductCode);

        setDisableCustomSubProductCodeInput(false);
      } else {
        setSubProductCodeValueAlreadyExists(false);
        setDisableCustomSubProductCodeInput(true);
      }
      setIsValidSubProductCode(true);
    } else {
      setIsValidSubProductCode(false);
    }
    // console.log(
    //   "selected product code: ",
    //   selectedsubProductCode,
    //   "\nsubProductCode",
    //   subProductCode,
    //   "\nselectedsubProductCode[0]?.value !== 0",
    //   selectedsubProductCode?.value !== 0,
    //   "\ndata?.value === 0",
    //   data?.value === 0
    // );
  };

  const handleTemperatureChange = (data) => {
    setTemperatureGrade([data]);
    if (data) {
      setIsValidTemperatureGrade(true);
    } else {
      setIsValidTemperatureGrade(false);
    }
  };

  const handleIntrastCommodityCodeChange = (data) => {
    //const selectedIndex = e.target.options.selectedIndex;
    // console.log(allDropdown.intrastatCommodityCode);
    setIntrastCommodityCode([data]);
    // console.log(intrastCommodityCode[0]);
    //setIntrastCommodityCodeLabel(e.target.options[selectedIndex].text);
  };

  const handleVarietyChange = (data) => {
    //const selectedIndex = e.target.options.selectedIndex;
    if (data) {
      setRawVarietyCode(data.code);
    } else {
      setRawVarietyCode("Click to Add New Variety");
    }
    setVariety([data]);
    if (data) {
      setRawVarietyCode(data.code);
    } else {
      setRawVarietyCode("");
    }
    //setVarietyLabel(e.target.options[selectedIndex].text);
  };

  const handleEndCustomerChange = (data) => {
    //const selectedIndex = e.target.options.selectedIndex;
    if (data) {
      setRawEndCustomerCode(data.code);
    } else {
      setRawEndCustomerCode("Click to Add New End Customer");
    }
    setEndCustomer([data]);
    if (data) {
      setRawEndCustomerCode(data.code);
    } else {
      setRawEndCustomerCode("");
    }
    //setEndCustomerLabel(e.target.options[selectedIndex].text);
  };

  const handleCalibreSizeChange = (data) => {
    //const selectedIndex = e.target.options.selectedIndex;
    if (data) {
      setRawCaliberSizeCode(data.code);
    } else {
      setRawCaliberSizeCode("Click to Add New Caliber Size");
    }
    setCalibreSize([data]);
    if (data) {
      setRawCaliberSizeCode(data.code);
    } else {
      setRawCaliberSizeCode("");
    }
    //setCalibreSizeLabel(e.target.options[selectedIndex].text);
  };

  const handleCoo = (data) => {
    //const selectedIndex = e.target.options.selectedIndex;
    if (data) {
      setRawCOOCode(data.code);
    } else {
      setRawCOOCode("Click to Add New Country of Origin");
    }
    setCountryOfOrigin([data]);
    if (data) {
      setRawCOOCode(data.code);
    } else {
      setRawCOOCode("");
    }
    //setCountryOfOriginLabel(e.target.options[selectedIndex].text);
  };

  const handleBrandChange = (data) => {
    //const selectedIndex = e.target.options.selectedIndex;
    if (data) {
      setRawBrandCode(data.code);
    } else {
      setRawBrandCode("Click to Add New Brand");
    }
    setBrand([data]);
    if (data) {
      setRawBrandCode(data.code);
    } else {
      setRawBrandCode("");
    }
    //setBrandLabel(e.target.options[selectedIndex].text);
  };

  const handleDeliveryDate = (value) => {
    if (value) {
      setIsValidExpectedDate(true);
    } else {
      setIsValidExpectedDate(false);
    }
  };

  const handleProductDesc = (value) => {
    if (value) {
      setIsValidProductInformation(true);
    } else {
      setIsValidProductInformation(false);
    }
  };

  const handleSupplierDesc = (value) => {
    if (value) {
      setIsValidSupplierDesc(true);
    } else {
      setIsValidSupplierDesc(false);
    }
  };

  const handleUnitsInOuter = (value) => {
    if (value) {
      setIsValidUnitsInOuter(true);
    } else {
      setIsValidUnitsInOuter(false);
    }
  };

  const handleCasesPerPallet = (value) => {
    if (value) {
      setIsValidCasesPerPallet(true);
    } else {
      setIsValidCasesPerPallet(false);
    }
  };

  const handleSubProductCode = (value) => {
    if (value) {
      setIsValidSubProductCode(true);
    } else {
      setIsValidSubProductCode(false);
    }

    // Handle input changes
    // const handleChange = (e) => {
    // setInputValue(value);.
    // console.log("log at validation");
    checkIfSubProductCodeExists(value); // Call the debounced function
    // };
  };

  const handleGrossWeight = (value) => {
    if (value) {
      setIsValidGrossWeightOuter(true);
    } else {
      setIsValidGrossWeightOuter(false);
    }
  };

  const handleOrganicCertification = (value) => {
    if (value) {
      // console.log(value);
      setOrganicCertification([value]);
      if (!isValidOrganicCertification) {
        setIsValidOrganicCertification(true);
      }
    } else {
      setIsValidOrganicCertification(false);
      setOrganicCertification([]);
    }
  };

  const handleCountOrSize = (value) => {
    // setSubProductCode("");
    if (value) {
      setIsValidCountOrSize(true);
      // setMarkVariety([]);
    } else {
      setIsValidCountOrSize(false);
      // setMarkVariety([]);
    }
  };

  const handleNetWeightCase = (value) => {
    if (value) {
      setIsValidNetWeightOuter(true);
    } else {
      setIsValidNetWeightOuter(false);
    }
  };

  const handleProductType = (data) => {
    if (data) {
      setProductType([data]);
      setIsValidProductType(true);
    } else {
      setProductType([]);
      setIsValidProductType(false);
    }
  };

  const handleProductGroups = (data) => {
    //console.log(data)
    if (data) {
      setProductGroup([data]);
      setIsValidProductGroup(true);
    } else {
      setProductGroup([]);
      setIsValidProductGroup(false);
    }
  };

  useEffect(() => {
    setIsLoading(false)
    if (Cookies.get("rawWarning") == undefined) {
      toast.warn(
        "Entries marked with an * have not been created by ISS and are temporary until confirmed",
        {
          position: "top-center",
          autoClose: 50000,
          hideProgressBar: false,
          closeOnClick: true,
          pauseOnHover: true,
          draggable: true,
          progress: undefined,
          theme: "light",
        }
      );
    }
    setTimeout(function () {
      Cookies.set("rawWarning", true, { expires: 365 });
      Cookies.remove("finishWarning");
    }, 2000);

    setAlldropDown(dropdowns);
    if ((pageType !== "update" || nameOfOriginator === "") && userData) {
      setNameOfOriginator(userData.name);
    }
    if (pageType == "update") {
      setIsEdit(true);
    }
    if (rawMaterialData) {
      if (
        (userData.user_id != rawMaterialData[0].requestor &&
          userData.role != 1) ||
        rawMaterialData[0].submitted_to_iss ||
        rawMaterialData[0].status == 6
      ) {
        setIsUserHaveCreated(true);
        setDisabledClass(true);
      }
    }
    
  }, [0, reload]);

  let prd_type = "";
  //#region load data
  useEffect(() => {
    setLoading(false);
    getProductGroups(userData);
    const company=Cookies.get("company");
    let prophet_id=1;
    if(company=="dpsltd"){
      prophet_id=1;
    }else if(company=="efcltd"){
      prophet_id=3;
    }else if(company=="fpp-ltd"){
      prophet_id=4
    }
    setProphetId(prophet_id)



    if (rawMaterialData?.length > 0) {
      // console.log("rawMaterialData",rawMaterialData);
      setSubmittedToISS(rawMaterialData[0].submitted_to_iss);
      setProductId(rawMaterialData[0].id);
      if (rawMaterialData[0].brand_id) {
        setBrand([
          {
            value: rawMaterialData[0].brand_id,
            label: rawMaterialData[0].brand_name,
            is_new: rawMaterialData[0].brand_is_new,
          },
        ]);
      }

      if (rawMaterialData[0].brand_code) {
        setRawBrandCode(rawMaterialData[0].brand_code);
      }
      if (rawMaterialData[0].brand_name) {
        setBrandLabel(rawMaterialData[0].brand_name);
      }

      if (rawMaterialData[0]?.product_type == 1) {
        prd_type = "Normal";
      } else if (rawMaterialData[0]?.product_type == 2) {
        prd_type = "Packaging";
      } else {
        prd_type = "Select product type";
      }
      if (rawMaterialData[0].product_type) {
        setProductType([
          {
            value: rawMaterialData[0].product_type,
            label: prd_type,
          },
        ]);
      }
      setNameOfOriginatorEmail(rawMaterialData[0].originator_email);
      if (rawMaterialData[0].caliber_size_id) {
        setCalibreSize([
          {
            value: rawMaterialData[0].caliber_size_id,
            label: rawMaterialData[0].caliber_size_name,
            is_new: rawMaterialData[0].caliber_size_is_new,
          },
        ]);
      }
      if (rawMaterialData[0].caliber_size_name) {
        setCalibreSizeLabel(rawMaterialData[0].caliber_size_name);
      }
      if (rawMaterialData[0].cases_per_pallet) {
        setCasesPerPallet(rawMaterialData[0].cases_per_pallet);
      }

      if (rawMaterialData[0].master_product_id) {
        setMasterProductCode([
          {
            value: rawMaterialData[0].master_product_id,
            label: rawMaterialData[0].product_name,
            is_new: rawMaterialData[0].mp_is_new,
            code: rawMaterialData[0].master_product_code,
          },
        ]);
      }

      if (rawMaterialData[0].master_product_code) {
        setRawMasterCode(rawMaterialData[0].master_product_code);

        fetch(
          `${serverAddress}products/get-subproduct-code/${rawMaterialData[0].master_product_code}?prophetId=${prophetId}`,
          {
            method: "GET",
            headers: {
              Authorization: `Bearer ${userData.token}`,
              Accept: "application/json",
              "Content-Type": "application/json",
            },
          }
        )
          .then((res) => {
            // console.log("status",res.status);
            if (res.status === 200) {
              return res.json();
            }
            return Promise.reject(res);
          })
          .then((data) => {
            if (data) {
              let subProductsList = [{ value: 0, label: "New Code" }];
              subProductsList.push(...data);
              setSubProductCodeData(subProductsList);
              // console.log("\nzero",data,"\none",subProductsList,"\ntwo",subProductCodeData,"\nthree",rawMaterialData[0].master_product_code);
            }
          })
          .catch((error) => {
            console.log(error);
          });
      }
      const masterCode = rawMaterialData[0].product_name
        ? rawMaterialData[0].product_name.indexOf("Organic")
        : "-1";

      if (masterCode != "-1") {
        setIsValidOrganicCertification(false);
      } else {
        setIsValidOrganicCertification(true);
      }
      setEmailComment(rawMaterialData[0].email_comment);

      if (rawMaterialData[0].group_id) {
        setProductGroup([
          {
            value: rawMaterialData[0].group_id,
            label: rawMaterialData[0].group_name,
            is_new: rawMaterialData[0].group_is_new,
          },
        ]);
        // }
      }

      setMasterProductCodeLabel(rawMaterialData[0].product_name);
      setCountSizse(rawMaterialData[0].countSize);
      if (rawMaterialData[0].end_customer_id) {
        setEndCustomer([
          {
            value: rawMaterialData[0].end_customer_id,
            label: rawMaterialData[0].end_customer_name,
            is_new: rawMaterialData[0].end_customer_is_new,
          },
        ]);
      }
      setEndCustomerLabel(rawMaterialData[0].end_customer_name);
      if (rawMaterialData[0].is_classified_allergic_fsa14) {
        setClassifiedAllergicTypes([
          {
            value: rawMaterialData[0].is_classified_allergic_fsa14,
            label: rawMaterialData[0].is_classified_allergic_fsa14,
          },
        ]);
      }

      if (rawMaterialData[0].organic_certificate) {
        setOrganicCertification([
          {
            value: rawMaterialData[0].organic_certificate,
            label: rawMaterialData[0].organic_certificate,
          },
        ]);
      }

      setGrossWeightCase(rawMaterialData[0].outer_gross_weight);
      setNetWeightCase(rawMaterialData[0].outer_net_weight);
      setProductDescription(rawMaterialData[0].product_description);

      const subProductCodeItemExistsInDropdown =
        dropdowns?.subProductCode?.find(
          (item) => item.label === rawMaterialData[0].sub_product_code
        );

      if (
        rawMaterialData[0].sub_product_code &&
        !subProductCodeItemExistsInDropdown
      ) {
        // && value is not in db
        setSelectedSubProductCode({
          value: 0,
          label: "New Code",
        });
        setDisableCustomSubProductCodeInput(false);
      } else if (
        rawMaterialData[0].sub_product_code &&
        subProductCodeItemExistsInDropdown
      ) {
        setSelectedSubProductCode({
          value: subProductCodeItemExistsInDropdown.value,
          label: subProductCodeItemExistsInDropdown.label,
        });
        setDisableCustomSubProductCodeInput(true);
      }

      setSubProductCode(rawMaterialData[0].sub_product_code);
      setSupplierDescription(rawMaterialData[0].suppliers_description);
      setUnitsInOuter(rawMaterialData[0].units_in_outer);
      if (rawMaterialData[0].variety_id) {
        setVariety([
          {
            value: rawMaterialData[0].variety_id,
            label: rawMaterialData[0].description,
            is_new: rawMaterialData[0].variety_is_new,
          },
        ]);
      }
      setVarietyLabel(rawMaterialData[0].description);
      setClassRequired(rawMaterialData[0].class_required);
      if (rawMaterialData[0].reason_id) {
        setReasonForRequest([
          {
            value: rawMaterialData[0].reason_id,
            label: rawMaterialData[0].reason_type,
          },
        ]);
      }
      setReasonForRequestLabel(rawMaterialData[0].reason);
      setCountSizse(rawMaterialData[0].count_or_size);
      if (rawMaterialData[0].coo) {
        setCountryOfOrigin([
          {
            value: rawMaterialData[0].coo,
            label: rawMaterialData[0].coo_name,
            is_new: rawMaterialData[0].coc_is_new,
          },
        ]);
      }
      setRawCOOCode(rawMaterialData[0].coc_code);
      setRawCaliberSizeCode(rawMaterialData[0].caliber_size_code);
      setRawEndCustomerCode(rawMaterialData[0].ec_code);
      setCountryOfOriginLabel(rawMaterialData[0].coo_name);
      setRawVarietyCode(rawMaterialData[0].v_code);
      setCompanyName(
        rawMaterialData[0].company_name ? rawMaterialData[0].company_name : ""
      );
      if (rawMaterialData[0].temperature_grade) {
        setTemperatureGrade([
          {
            value: rawMaterialData[0].temperature_grade,
            label: rawMaterialData[0].temperature_grade_name,
            is_new: rawMaterialData[0].temperature_grade_is_new,
          },
        ]);
      }
      setTemperatureGradeLabel(rawMaterialData[0].temperature_grade_name);
      const presentcode = rawMaterialData[0].intrastat_commodity_code_id
        ? "-"
        : "";
      if (rawMaterialData[0].intrastat_commodity_code) {
        setIntrastCommodityCode([
          {
            value: rawMaterialData[0].intrastat_commodity_code,
            label:
              rawMaterialData[0].intrastat_commodity_code_name +
              presentcode +
              rawMaterialData[0].intrastat_commodity_code_id,
            is_new: rawMaterialData[0].intrastat_commodity_code_is_new,
            code: rawMaterialData[0].intrastat_commodity_code_id,
            User_text_4:
              rawMaterialData[0].intrastat_commodity_code_User_text_4,
            User_text_5:
              rawMaterialData[0].intrastat_commodity_code_User_text_5,
            User_text_6:
              rawMaterialData[0].intrastat_commodity_code_User_text_6,
          },
        ]);
      }
      setIntrastCommodityCodeLabel(
        rawMaterialData[0].intrastat_commodity_code_name
      );
      if (rawMaterialData[0].mark_variety_id) {
        setMarkVariety([
          {
            value: rawMaterialData[0].mark_variety_id,
            label: rawMaterialData[0].mark_variety_name,
            is_new: rawMaterialData[0].mark_variety_is_new,
          },
        ]);
      }
      setMarkVarietyLabel(rawMaterialData[0].mark_variety_name);
      setNameOfOriginator(rawMaterialData[0].originator);
      setIsUnblockRequest(rawMaterialData[0].is_unblock);
      setStatus(rawMaterialData[0].status);
      setCancelledReasonapi(rawMaterialData[0].cancelled_reason);
      setCancelledBy(rawMaterialData[0].cancelled_by);
      setCancelledDate(rawMaterialData[0].cancelled_date);
      // setExpectedDeliveryData(
      //   new Date(rawMaterialData[0].delivery_date).toLocaleDateString("en-CA", {
      //     year: "numeric",
      //     month: "2-digit",
      //     day: "2-digit",
      //   })
      // );
      setExpectedDeliveryData(
        rawMaterialData[0].delivery_date
          ? new Date(rawMaterialData[0].delivery_date).toLocaleDateString(
              "en-CA",
              {
                year: "numeric",
                month: "2-digit",
                day: "2-digit",
              }
            )
          : ""
      );

      setRequestNumber(rawMaterialData[0].request_no);
    }
  }, [rawMaterialData, dropdownData]);
  //#regionEnd
  // console.log(commodityCodes);

  async function getData(company, userData) {
    let serverAddress = apiConfig.serverAddress;

    // let generateSequence = "";

    return fetch(`${serverAddress}products/get-request-number/1`, {
      //just passing '1' as it's product type for RM
      method: "GET",
      headers: {
        Authorization: `Bearer ${userData.token}`,
      },
    })
      .then(async (res) => {
        if (res.status === 400) {
          toast.error(
            "There was an error with your request. Please check your data and try again."
          );
        } else if (res.status === 401) {
          toast.error("Your session has expired. Please log in again.");
          setTimeout(() => {
            const redirectUrl = `/login?redirect=${encodeURIComponent(window.location.pathname)}`;
logoutHandler(instance,redirectUrl);;
            router.push("/login");
          }, 3000);
        }
        if (res.status === 200) {
          return res.json();
        }
        throw new Error("Failed to fetch data");
      })
      .then((json) => {
         console.log(json);
        if (json[0].request_number) {
          const nextRequestNumber = parseInt(json[0].request_number) + 1;
          // console.log("nextRequestNumber", nextRequestNumber);
          const nextRequetNumberStr = nextRequestNumber.toString();
          const generateSequence =
            nextRequetNumberStr.length <= 6
              ? nextRequetNumberStr.padStart(6, "0")
              : nextRequetNumberStr;
          // generateSequence = String(0).padStart((5), '0') + (nextRequestNumber);
          const existingRequestNumber = generateSequence;
          return existingRequestNumber;
        } else {
          const formattedSequenceNumber = String(0).padStart(5, "0") + 1;
          const newRequestNumber = formattedSequenceNumber;
          return newRequestNumber;
        }
      })
      .catch((error) => {
        //setCommonError(error.message);
        return error.message;
      });
  }

  async function getProductGroups(userData) {
    let serverAddress = apiConfig.serverAddress;
    return fetch(`${serverAddress}products/get-product-groups/1`, {
      method: "GET",
      headers: {
        Authorization: `Bearer ${userData.token}`,
      },
    })
      .then(async (res) => {
        if (res.status === 200) {
          return res.json();
        }
        throw new Error("Failed to fetch data");
      })
      .then((json) => {
        // console.log("data");
        // setIsProductGroups(true);
        setProductGroups(json);
      })
      .catch((error) => {
        //setCommonError(error.message);
        return error.message;
      });
  }

  function insertRequestNumber(company, product_no) {
    const data = {
      company: company,
      request_no: product_no,
      product_request_type: 1, //set to 1 because RM master is 1
    };
    try {
      fetch(`${serverAddress}products/insert-request-number`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${userData.token}`,
        },
        body: JSON.stringify(data),
      })
        .then((res) => {
          if (res.status === 200) {
            return res.json();
          } else {
            toast.error("Failed to save raw material.");
            setLoading(false);
          }
        })
        .then((json) => {
          if (json && json.msg) {
            // Assuming the response contains a 'msg' field
            toast.success(json.msg, {
              position: "top-right",
            });
            setTimeout(() => {
              router.replace("/products");
              setLoading(false);
            }, 2000);
          }
        });
    } catch (error) {
      console.error("Failed to save raw material:", error);
      setLoading(false);
    }
  }

  const handleValidate = async (isContinue = false, type = null) => {
    const company_name = companyName ? companyName : Cookies.get("company");
    let errorCount = 0;
    //console.log(isValidReasonForRequest)
    if (reasonForRequest.length <= 0 || reasonForRequest[0] === null) {
      setIsValidReasonForRequest(false);
      errorCount++;
    }
    if (productType.length <= 0 || productType[0] === null) {
      setIsValidProductType(false);
      errorCount++;
    }
    if (!expectedDeliveryDate) {
      setIsValidExpectedDate(false);
      errorCount++;
    }
    // if (Date(expectedDeliveryDate) < new Date()){
    //   set
    // }
    if (!productDescription) {
      setIsValidProductInformation(false);
      errorCount++;
    }
    if (!productDescription) {
      setIsValidProductInformation(false);
      errorCount++;
    }
    if (!supplierDescription) {
      setIsValidSupplierDesc(false);
      errorCount++;
    }
    if (masterProductCode.length <= 0 || masterProductCode[0] === null) {
      setIsValidMasterCode(false);
      errorCount++;
    }
    if (!countSize) {
      setIsValidCountOrSize(false);
      errorCount++;
    }
    if (!unitsInOuter) {
      setIsValidUnitsInOuter(false);
      errorCount++;
    }
    if (!casesPerPallet) {
      setIsValidCasesPerPallet(false);
      errorCount++;
    }

    if (!netWeightCase) {
      setIsValidNetWeightOuter(false);
      errorCount++;
    }
    if (!grossWeightCase) {
      setIsValidGrossWeightOuter(false);
      errorCount++;
    }
    if (markVariety.length <= 0 || markVariety[0] === null) {
      setIsValidMark(false);
      errorCount++;
    }
    if (!subProductCode) {
      setIsValidSubProductCode(false);
      errorCount++;
    }
    const sameSubProductCodeAsInitial = rawMaterialData?.[0]?.sub_product_code
      ? subProductCode !== rawMaterialData[0].sub_product_code
      : true;
    if (subProductCodeValueAlreadyExists && sameSubProductCodeAsInitial) {
      errorCount++;
    }
    // console.log('temperatureGrade: ', temperatureGrade);
    if (temperatureGrade.length <= 0 || temperatureGrade[0] === null) {
      setIsValidTemperatureGrade(false);
      // console.log('temperatureGrade error set');
      errorCount++;
    }
    if (!isValidOrganicCertification) {
      if (!organicCertification) {
        setIsValidOrganicCertification(false);
        errorCount++;
      }
    }

    // console.log("'productGroup'",productGroup)
    if (productGroup.length <= 0 || productGroup[0] === null) {
      setIsValidProductGroup(false);
      errorCount++;
    }

    if (
      grossWeightCase &&
      parseInt(netWeightCase) > parseInt(grossWeightCase)
    ) {
      errorCount++;
    }

    let productStatus = "";
    if (status == 3) {
      if (type == "submit") {
        productStatus = 8;
      } else {
        productStatus = 4;
      }
    } else if (
      status == 4 ||
      status == 3 ||
      (status == "" && type == "submit")
    ) {
      if (type == "submit") {
        productStatus = 8;
      } else {
        productStatus = 4;
      }
    } else {
      // console.log('status: ',status,"\nproductStatus: ",productStatus);
      productStatus = 3;
    }

    const product_no = await getData(company_name, userData);

    const saveData = {
      nameOfOriginator: nameOfOriginator,
      emailOfOrignator: nameOfOriginatorEmail,
      requestNumber: pageType == "update" ? requestNumber : "RM" + product_no,
      product_type: productType,
      reasonForRequest: reasonForRequest,
      expectedDeliveryDate: expectedDeliveryDate,
      productDescription: productDescription,
      supplierDescription: supplierDescription,
      masterProductCode: masterProductCode,
      productGroup: productGroup,
      markVariety: markVariety,
      countSize: countSize,
      unitsInOuter: unitsInOuter,
      casesPerPallet: casesPerPallet,
      netWeightCase: netWeightCase,
      grossWeightCase: grossWeightCase,
      // subProductCode: selectedsubProductCode[0]?.value === 0 ? subProductCode : selectedsubProductCode[0]?.label,
      createNewSubProductCodeInDB:
        selectedsubProductCode &&
        selectedsubProductCode[0]?.value === 0 &&
        !subProductCodeValueAlreadyExists,
      subProductCode: subProductCode,
      temperatureGrade: temperatureGrade,
      classRequired: classRequired,
      intrastCommodityCode: intrastCommodityCode,
      brand: brand,
      countryOfOrigin: countryOfOrigin,
      calibreSize: calibreSize,
      endCustomer: endCustomer,
      variety: variety,
      organicCertification: organicCertification,
      classifiedAllergicTypes: classifiedAllergicTypes,
      companyId: company_name,
      requestor: userData.user_id,
      status: productStatus,
      username: userData.name,
      useremail: userData.email,
      request_no: requestNumber,
      reasonForRequestLabel: reasonForRequestLabel,
      countryOfOriginLabel: countryOfOriginLabel,
      temperatureGradeLabel: temperatureGradeLabel,
      intrastCommodityCodeLabel: intrastCommodityCodeLabel,
      markVarietyLabel: markVarietyLabel,
      brandLabel: brandLabel,
      calibreSizeLabel: calibreSizeLabel,
      masterProductCodeLabel: masterProductCodeLabel,
      endCustomerLabel: endCustomerLabel,
      varietyLabel: varietyLabel,
      isSubmitted: type == "submit" ? true : type == "save" ? false : false,
      emailComment: emailComment,
      // sortGroup: sortGroup,
      // sortGroupCode: sortGroupCode,
    };

    // return;
    switch (type) {
      case "save":
        // if (type == "save"){
        if (errorCount > 0) {
          setIsOpen(true);
        } else {
          if (pageType == "update") {
            handleUpdateRequest(saveData, "save");
          } else if (pageType == "add") {
            handleCreateRequest(saveData, product_no);
          }
        }
        break;
      case "submit":
        // else if (type == "submit"){
        if (errorCount > 0) {
          setIsSubmitModalOpen(true);
        } else {
          setValidSubmitMode(true);
          setIsSubmitModalOpen(true);
          // if (isReadyToSubmit) {
          // if (pageType == "update") {
          //   handleUpdateRequest(saveData);
          // } else if (pageType == "add") {
          //   handleCreateRequest(saveData, product_no);
          // }
          // }
        }
        break;
      default:
        break;
    }

    if (isCancelled) {
      setIsCancelled(false);
      return;
    }

    if (isContinue) {
      if (pageType == "update") {
        handleUpdateRequest(saveData);
      } else if (pageType == "add") {
        handleCreateRequest(saveData, product_no);
      }
      exportToExcel(saveData, product_no);
      validSubmitMode ? setIsSubmitModalOpen(false) : setIsOpen(false);
    }

    if (isCancelled) {
      setIsCancelled(false);
      return;
    }
  };

  //#region export To Excel
  const exportToExcel = async (data, product_no) => {
    // console.log("data", data.companyId);
    if (data.status === 8) {
      let userText3 = "";
      if (data.companyId == "dpsltd") {
        userText3 = "DPS";
      } else if (data.companyId == "efcltd") {
        userText3 = "OFF";
      } else if (data.companyId == "fpp-ltd") {
        userText3 = "FPP";
      } else {
        userText3 = "THL"; //TODO: remove this later
      }

      const filteredExportData = [
        [
          "Product Extract",
          {
            "User Boolean 1": "True",
            "Master Product Code": rawMasterCode,
            "Commodity Code": intrastCommodityCode[0]?.code,
            "User Text 4": intrastCommodityCode[0]?.User_text_4,
            "User Text 5": intrastCommodityCode[0]?.User_text_5,
            "User Text 6": intrastCommodityCode[0]?.User_text_6,
            "Intrastat weight mass": grossWeightCase,
            "Sub Product Code": subProductCode,
            "Mark/variety":
              data.companyId == "efcltd" || data.companyId == "fpp-ltd"
                ? "RM" + " " + markVariety[0].label
                : markVariety[0].label,
            "Count or size": countSize,
            "Sort Group Number": productGroup[0].value,
            "User Text 3": userText3,
            "Product Number": "",
            "Units in Outer": unitsInOuter,
            "Packs per pallet": casesPerPallet,
            "Sell packs per pallet": casesPerPallet,
            "Weight of outer": grossWeightCase,
            "Product distribution Point": "",
            Buyer: 1,
            "Temperature grade": temperatureGrade[0]?.value,
            "Temperature Grade": temperatureGrade[0]?.label,
            "Product Type": productType[0]?.value,
            "Product type": productType[0]?.label,
            Active: "True",
          },
        ],
      ];

      if (data.companyId == "efcltd" || data.companyId == "fpp-ltd") {
        filteredExportData.push([
          "ALTFIL Extract",
          {
            Active: "True",
            "Altfil record id": "",
            "Generic Code": userText3,
            "Alternate product number": "",
            "Alternate number": "",
            "Alternate bar code number": "",
            "Alternate description": subProductCode,
            "Alternate product Master product code": "",
            "Alternate product number Count or size": "",
            "Alternate product number Gross weight outer": "",
            "Alternate product number Mark/variety": "",
            "Alternate group": productGroup[0].value,
            "Alternate count or size": unitsInOuter,
            "Alternate prefix": "",
            "Inner product barcode": "",
            "Outer product barcode": "",
            "Alternate product number extension": "",
            "End Customer": "",
            Brand: "",
            "Display until days": "",
            "GTIN 14": "",
            "Calibre / Size": "",
            "Alternate product number Packs per pallet": "",
            "Inner stock keeping unit": "",
            "Stock keeping unit": "",
            "Customer product code": "",
            "Alternate use standard prefix (1=yes)": "1",
            "User text 1": "",
          },
        ]);
        // console.log(
        //   "filtered export data after creating new array",
        //   filteredExportData
        // );
      }
      // console.log(
      //   "data.nameOfOriginatorEmail",
      //   data.nameOfOriginatorEmail,
      //   "data.useremail",
      //   data.useremail
      // );
      let userName = "";
      let userEmail = "";
      if (data.originatorEmail) {
        userEmail = data.originatorEmail;
      } else {
        userEmail = userData.email;
      }

      if (data.nameOfOriginator) {
        userName = data.nameOfOriginator;
      } else {
        userName = userData.name;
      }

      const productEmailParagraph = `<p>User ${
        userData.name
      } submitted a Raw material request with request number ${
        data.request_no ? data.request_no : "RM" + product_no
      } 
           to ISS.
       
      </p>`;
      let productEmailCommentPlaceholder = `<p style='
      color: #32353e; margin: 0 0 10px 0; padding: 0;font-family: "HelveticaNeueLight", "HelveticaNeue-Light", "Helvetica Neue Light", "HelveticaNeue", "Helvetica Neue", "TeXGyreHerosRegular", "Helvetica", "Tahoma", "Geneva", "Arial", sans-serif; font-weight: 300;
        font-stretch: normal; font-size: 14px; line-height: 1.7; text-align: left;'>Comments: <i>${
          data.emailComment ? data.emailComment : "-"
        }</i></p>
      `;

      const export_response = await exportExcelData(
        filteredExportData,
        false,
        userData.token,
        data.companyId,
        userData,
        "",
        data.emailOfOrignator,
        false,
        true,
        true,
        productEmailParagraph,
        productEmailCommentPlaceholder,
        data.request_no ? data.request_no : "RM" + product_no
      );
      // console.log("Youhuuuu!!!", export_response);
      if (export_response === 401){
        toast.error("Your session has expired. Please log in again.");
        setTimeout(() => {
          const redirectUrl = `/login?redirect=${encodeURIComponent(
            window.location.pathname
          )}`;

          logoutHandler(instance, redirectUrl);

        }, 3000);
      }
    }
    // else {
    // toast.error("Kindly Submit the Request to Export it.", {
    //   position: "top-right",
    //   autoClose: 3000,
    //   hideProgressBar: false,
    //   closeOnClick: true,
    //   pauseOnHover: false,
    //   draggable: true,
    //   progress: undefined,
    //   theme: "light",
    // });
    // return;
    // }
  };
  //#endregion

  //#region handle submit
  const handleCreateRequest = (data, product_no) => {
    try {
      setLoading(true);
      fetch(`${serverAddress}products/add-raw-materials`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${userData.token}`,
        },
        body: JSON.stringify(data),
      })
        .then((res) => {
          // console.log("res:",res);

          if (res.status === 200) {
            return res.json();
          } 
          else if (res.status === 401) {
            // console.log("Your session has expired. Please log in again.");
            toast.error("Your session has expired. Please log in again.");
            setTimeout(() => {
              const redirectUrl = `/login?redirect=${encodeURIComponent(
                window.location.pathname
              )}`;
              logoutHandler(instance, redirectUrl);
 
            }, 3000);
            return null;
          }
          else {
            toast.error("Failed to save raw material.");
            setLoading(false);
          }
        })
        .then((json) => {
          if (json && json.msg) {
            const company_name = companyName ? company : Cookies.get("company");
            const insert_request_number = insertRequestNumber(
              company_name,
              product_no
            );
            // Assuming the response contains a 'msg' field
            toast.success(json.msg, {
              position: "top-right",
            });
            setTimeout(() => {
              router.replace("/products");
            }, 2000);
          }
        });
    } catch (error) {
      console.error("Failed to save raw material:", error);
      setLoading(false);
    }
  };
  //#endregion handle submit

  const handleUpdateRequest = (data) => {
    try {
      setLoading(true);
      fetch(`${serverAddress}products/update-raw-materials/${productId}`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${userData.token}`,
        },
        body: JSON.stringify(data),
      })
        .then((res) => {
          // console.log("res.status",res.status);
          if (res.status === 200) {
            return res.json();
          } else if (res.status === 401) {
            console.log("Your session has expired. Please log in again.");
            toast.error("Your session has expired. Please log in again.");
            setTimeout(() => {
              const redirectUrl = `/login?redirect=${encodeURIComponent(
                window.location.pathname
              )}`;
              logoutHandler(instance, redirectUrl);
            }, 3000);
            return null;
          } 
          else {
            toast.error("Failed to update raw material.");
            setLoading(false);
          }
        })
        .then((json) => {
          if (json && json.msg) {
            // Assuming the response contains a 'msg' field
            toast.success(json.msg, {
              position: "top-right",
            });
            setTimeout(() => {
              router.replace("/products");
            }, 2000);
          }
        });
    } catch (error) {
      console.error("Failed to update raw material:", error);
      setLoading(false);
    }
  };

  const handleCancel = () => {
    setIsCancelOpen(true);
  };

  const closeModal = () => {
    setIsCancelled(true);
    setIsOpen(false);
  };
  const closeSubmitModal = () => {
    setIsCancelled(true);
    setIsSubmitModalOpen(false);
  };

  // const confirmSubmitProductRequest = () => {
  //   setIsReadyToSubmit(true);
  //   return
  //   // if (pageType == "update") {
  //   //   handleUpdateRequest(saveData);
  //   // } else if (pageType == "add") {
  //   //   // if (isReadyToSubmit){
  //   //     handleCreateRequest(saveData, product_no);
  //   //   // }
  //   // }
  // }

  const closeUnblockModal = () => {
    setIsCancelled(true);
    setIsUnblockOpen(false);
    setIsValidUnblockReason(true);
  };

  const closeCancelModal = () => {
    setIsCancelled(true);
    setIsCancelOpen(false);
    setIsValidCancelReason(true);
  };

  const handleContinueSubmit = () => {
    handleValidate(isContinue, validSubmitMode ? "submit" : "save");
    validSubmitMode ? setIsSubmitModalOpen(false) : setIsOpen(false);
    // setIsOpen(false);
  };

  const handleWithoutReason = () => {
    router.push("/products");
  };

  const onFilterTextBoxChanged = useCallback(() => {
    gridRef.current.api.setQuickFilter(
      document.getElementById("filter-text-box").value
    );
  }, []);

  const toggleRawMaterialImage = () => {
    setIsRawMaterialImage((prev) => !prev);
  };

  const hideRawMaterialImage = () => {
    setIsRawMaterialImage(false);
  };

  const handleCancelReason = (data) => {
    if (data) {
      setIsValidCancelReason(true);
    } else {
      setIsValidCancelReason(false);
    }
  };

  const saveModalData = () => {
    if (!cancelledReasonapi) {
      setIsValidCancelReason(false);
      return;
    }
    setLoading(true);
    try {
      fetch(`${serverAddress}products/product-update-status`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${userData.token}`,
        },
        body: JSON.stringify({
          status: 6,
          productId: productId,
          updated_date: new Date().toISOString(),
          reason: cancelledReasonapi,
          cancelled_by: userData.name,
          cancelled_date: new Date().toISOString(),
          name: userData.name,
          email: userData.email,
          product_information: productDescription,
        }),
      })
        .then((res) => {
          // console.log("res.status",res.status);
          if (res.status === 200) {
            return res.json();
          } else if (res.status === 401) {
            console.log("Your session has expired. Please log in again.");
            toast.error("Your session has expired. Please log in again.");
            setTimeout(() => {
              const redirectUrl = `/login?redirect=${encodeURIComponent(
                window.location.pathname
              )}`;
              logoutHandler(instance, redirectUrl);
            }, 3000);
            return null;
          }
        })
        .then((json) => {
          if (json.length > 0) {
            router.push("/products");
          }
        });
      setLoading(false);
    } catch (error) {
      console.error("Failed to cancel product by :", error);
    }
  };

  // #region unbolck
  const handleUnblockReason = (data) => {
    if (data) {
      setIsValidUnblockReason(true);
    } else {
      setIsValidUnblockReason(false);
    }
  };

  const saveUnblockModalData = () => {
    if (!unblockedReasonapi) {
      setIsValidUnblockReason(false);
      return;
    }
    try {
      fetch(`${serverAddress}products/product-update-unblock`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${userData.token}`,
        },
        body: JSON.stringify({
          productId: productId,
          submitted_to_iss: 0,
          status: 4,
          is_active: true,
          unblocked_reason: unblockedReasonapi,
          unblocked_by: userData.email,
          unblocked_at: new Date().toISOString(),
          name: userData.name,
          email: userData.email,
          product_information: productDescription,
          type: "RM",
          request_no: requestNumber,
          originatorEmail: nameOfOriginatorEmail,
        }),
      })
        .then((res) => {
          // console.log("res.status",res.status);
          if (res.status === 200) {
            return res.json();
          } 
          else if (res.status === 401) {
            console.log("Your session has expired. Please log in again.");
            toast.error("Your session has expired. Please log in again.");
            setTimeout(() => {
              const redirectUrl = `/login?redirect=${encodeURIComponent(
                window.location.pathname
              )}`;
              logoutHandler(instance, redirectUrl);
            }, 3000);
            return null;
          }
        })
        .then((json) => {
          if (json?.length > 0) {
            setLoading(true);
            router.reload(`/raw-material-request/${productId}/edit`);
            setLoading(false);
          }
        });
    } catch (error) {
      console.error("Failed to unblock product by :", error);
    }
  };

  const handleUnblockEvent = () => {
    setIsUnblockOpen(true);
  };
  //#endregion

  const formatDate = (dateString) => {
    const dateObject = new Date(dateString);

    const year = dateObject.getFullYear();
    const month = String(dateObject.getMonth() + 1).padStart(2, "0"); // Months are zero-based
    const day = String(dateObject.getDate()).padStart(2, "0");

    return `${day}/${month}/${year}`;
  };

  const formattedDate = formatDate(cancelledDate);
  // console.log("email comment", emailComment);
  return (
    <>
      <ToastContainer limit={1} />
      {loading ? (
        <div
          style={{
            display: "flex",
            alignItems: "center",
            justifyContent: "center",
            height: "calc(100vh - 100px)",
            width: "calc(100vw - 125px)",
          }}
        >
          <ThreeCircles
            color="#002D73"
            height={50}
            width={50}
            visible={true}
            ariaLabel="oval-loading"
            secondaryColor="#0066FF"
            strokeWidth={2}
            strokeWidthSecondary={2}
          />
        </div>
      ) : (
        <div className="relative panel-container bg-white rounded-lg w-[93%] lg:w-[95%] 2xl:w-[calc(100%-70px)] p-4 pb-0">
          <div className="flex md:flex-row flex-col my-2">
            <div className="px-5 pe-8 mb-0 h-100vh border-e-[1px] border-light-gray md:w-1/2">
              {status == 6 && (
                <h6 className="text-red-500 fw-bold">
                  Cancellation Reason: {cancelledReasonapi}, Cancelled By:{" "}
                  {cancelledBy}, Cancelled Date : {formattedDate}
                </h6>
              )}
              <div className="mb-6">
                <div className="mb-3">
                  <h4 className="formtitle pb-1 border-b border-light-gray3">
                    Request Information
                  </h4>
                </div>
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 ">
                  <div className="flex flex-col mr-[23px]">
                    <label className="labels mb-1" htmlFor="Request Number">
                      Request Number
                    </label>
                    <input
                      type="text"
                      name="Request_number"
                      id="Request Number"
                      maxLength={50}
                      disabled={true}
                      placeholder="Will be generated on submit"
                      className={`w-full py-1 px-2 2xl:px-3 2xl:py-3 border rounded-md bg-gray-100`}
                      required
                      // tabIndex={1}
                      value={requestNumber}
                    />
                  </div>
                  <div className="flex flex-col mr-[25px]">
                    <label className="flex flex-row labels mb-1">
                      Product Type
                      <span className="ml-1 text-red-500">*</span>
                      <Image
                        alt="prophetLogo"
                        src={prophetLogo}
                        className="ml-1 -mt-1 h-6 w-6"
                        title="SPDFIL-Product Type"
                      />
                    </label>
                    <div className="flex flex-row items-center">
                      <Select
                        options={allDropdown?.productType}
                        placeholder="Select..."
                        value={productType}
                        onChange={handleProductType}
                        isSearchable={true}
                        instanceId="selectbox"
                        styles={customSelectStyles}
                        //  styles={{
                        //   control: (baseStyles, state) => ({
                        //     ...baseStyles,
                        //     borderColor: state.isFocused ? 'grey' : 'red',
                        //   }),
                        // }}
                        className="reactSelectCustom w-full ${state.isFocused}"
                        isClearable={true}
                        isDisabled={
                          submittedToISS || isUserHaveCreated ? true : false
                        }
                      />
                    </div>
                    {!isValidProductType ? (
                      <span className="text-red-500">
                        Please Select a Product Type
                      </span>
                    ) : (
                      ""
                    )}
                  </div>
                  <div className="flex flex-col mr-[25px]">
                    <label className="flex flex-row labels mb-1">
                      Reason for Request{" "}
                      <span className="ml-1 text-red-500">*</span>
                    </label>
                    <div className="flex flex-row items-center">
                      <Select
                        options={allDropdown?.reason}
                        placeholder="Select..."
                        value={reasonForRequest}
                        onChange={handleReasonChange}
                        isSearchable={true}
                        instanceId="selectbox"
                        className="reactSelectCustom w-full"
                        styles={customSelectStyles}
                        isClearable={true}
                        isDisabled={
                          submittedToISS || isUserHaveCreated ? true : false
                        }
                        //onBlur={handleValidationChange}
                      />
                    </div>
                    {!isValidReasonForRequest ? (
                      <span className="text-red-500">
                        Please Select a Reason for Request
                      </span>
                    ) : (
                      ""
                    )}
                  </div>

                  <div className="flex flex-col mr-[23px]">
                    <label className="flex flex-row labels mb-1">
                      Expected 1st Delivery Date{" "}
                      <span className="ml-1 text-red-500">*</span>
                    </label>
                    <input
                      type="date"
                      name="date"
                      value={expectedDeliveryDate ? expectedDeliveryDate : ""}
                      onChange={(e) => {
                        setExpectedDeliveryData(e.target.value),
                          handleDeliveryDate(e.target.value);
                      }}
                      min={new Date().toISOString().split("T")[0]}
                      className="w-full px-2 2xl:px-3 border border-light-gray rounded-md inputs"
                      //focus:outline focus:outline-2 focus:!outline-skin-primary
                      disabled={
                        submittedToISS
                          ? true
                          : false || isUserHaveCreated
                          ? true
                          : false
                      }
                      // readonly
                      // onkeydown="return false"
                      // class="form-control"
                    />
                    {!isValidExpectedDate ? (
                      <span className="text-red-500">
                        Please Select an Expected Date
                      </span>
                    ) : (
                      ""
                    )}
                  </div>
                  <div className="flex flex-col mr-[25px]">
                    <label className="labels mb-1">
                      Name of Originator{" "}
                      {/* <span className="text-red-500">*</span> */}
                    </label>
                    <input
                      type="text"
                      maxLength={50}
                      name="name_of_originator"
                      value={nameOfOriginator}
                      className={`w-full py-1 px-2 2xl:px-3 2xl:py-3 border rounded-md bg-gray-100`}
                      required
                      onChange={(e) => setNameOfOriginator(e.target.value)}
                      // tabIndex={3}
                      disabled={true}
                      readOnly
                    />
                  </div>
                </div>
              </div>

              <div className="mb-6">
                <div className="mb-3">
                  <h4 className="flex flex-row formtitle pb-2 border-b border-light-gray3">
                    Product Information
                  </h4>
                </div>

                <div className=" grid lg:grid-cols-2 grid-cols-1 gap-6">
                  <div className="flex flex-col mr-[25px]">
                    <label
                      className="flex flex-row labels mb-1"
                      htmlFor="product_description_line"
                    >
                      Product Information / Description{" "}
                      <span className="ml-1 text-red-500">*</span>
                      {/* <Image
                        alt="prophetLogo"
                        src={prophetLogo}
                        className="ml-1 -mt-1 h-6 w-6"
                        title="Prophet Column - Name"
                      /> */}
                    </label>
                    <input
                      type="text"
                      id="product_description_line"
                      maxLength={50}
                      name="product_description_line"
                      value={productDescription}
                      // placeholder="Enter product description"
                      onChange={(e) => {
                        setProductDescription(e.target.value),
                          handleProductDesc(e.target.value);
                      }}
                      onBlur={(e) => {
                        handleProductInformation(e.target.value);
                      }}
                      className={`w-full py-1 px-2 2xl:px-3 2xl:py-3 border rounded-md`}
                      required
                      // tabIndex={4}
                      style={{ textTransform: "capitalize" }}
                      disabled={
                        submittedToISS
                          ? true
                          : false || isUserHaveCreated
                          ? true
                          : false
                      }
                    />
                    {!isValidProductInformation ? (
                      <span className="text-red-500">
                        Please Enter the Product Information
                      </span>
                    ) : (
                      ""
                    )}
                  </div>

                  <div className="flex flex-col mr-[25px]">
                    <label className="flex flex-row labels mb-1">
                      Supplier Description for the Product{" "}
                      <span className="ml-1 text-red-500">*</span>
                      {/* <Image
                        alt="prophetLogo"
                        src={prophetLogo}
                        className="ml-1 -mt-1 h-6 w-6"
                        title="Prophet Column - Name"
                      /> */}
                    </label>
                    <input
                      type="text"
                      maxLength={250}
                      name="supplier+description_for_the_product"
                      value={supplierDescription}
                      // placeholder={"Enter supplier description"}
                      onChange={(e) => {
                        setSupplierDescription(e.target.value),
                          handleSupplierDesc(e.target.value);
                      }}
                      onBlur={(e) => {
                        const trimmedValue = trimInputText(e.target.value);
                        setSupplierDescription(trimmedValue);
                        handleSupplierDesc(trimmedValue);
                      }}
                      className="w-full py-1 px-2 2xl:px-3 2xl:py-3 border border-light-gray rounded-md"
                      required
                      // tabIndex={5}
                      style={{ textTransform: "capitalize" }}
                      disabled={
                        submittedToISS
                          ? true
                          : false || isUserHaveCreated
                          ? true
                          : false
                      }
                    />
                    {!isValidSupplierDesc ? (
                      <span className="text-red-500">
                        Please Enter a Supplier Description
                      </span>
                    ) : (
                      ""
                    )}
                  </div>

                  <div className="flex flex-col">
                    <label className="flex flex-row labels mb-1" required>
                      Master Product Code{" "}
                      <span className="ml-1 text-red-500">*</span>
                      <Image
                        alt="prophetLogo"
                        src={prophetLogo}
                        className="ml-1 -mt-1 h-6 w-6"
                        title="SPDFIL-Master product code (1)
 "
                      />
                    </label>
                    <div className="flex flex-row items-center">
                      <Select
                        options={allDropdown?.masterProductCode}
                        placeholder="Select..."
                        value={masterProductCode}
                        onChange={handleMasterProductChange}
                        isSearchable={true}
                        ref={masterProdRef}
                        openMenuOnFocus={true}
                        instanceId="selectbox"
                        className="reactSelectCustom w-full text-xs lg:text-sm"
                        classNames={{
                          control: (state) =>
                            state.is_new == true
                              ? "text-red-500"
                              : "!text-red-500",
                        }}
                        styles={customSelectStyles}
                        isClearable={true}
                        isDisabled={
                          submittedToISS || isUserHaveCreated ? true : false
                        }
                        //onBlur={handleValidationChange}
                      />
                      <button
                        onClick={() => handleIconClick("master_product")}
                        className={`ml-2 py-1 p-[6px] border rounded-md mr-[25px] border-skin-primary text-skin-primary cursor-pointer ${
                          disabledClass
                            ? "cursor-not-allowed pointer-events-none opacity-0.5"
                            : ""
                        }`}
                        disabled={
                          submittedToISS
                            ? true
                            : false || isUserHaveCreated
                            ? true
                            : false
                        }
                      >
                        <FontAwesomeIcon icon={faPlus} />
                      </button>
                    </div>
                    {!isValidMasterCode ? (
                      <span className="text-red-500">
                        Please Select a Master Product Code
                      </span>
                    ) : (
                      ""
                    )}
                  </div>

                  <div className="flex flex-col mr-[25px]">
                    <label
                      className={`w-full py-1 px-2 2xl:px-3 2xl:py-2 ${
                        rawMasterCode ? "mt-7" : "mt-5"
                      } xl:mt-6`}
                    >
                      {rawMasterCode
                        ? rawMasterCode
                        : "Click + to add Master Product Code"}
                    </label>
                  </div>

                  {/* {isProductGroups && ( */}
                  <div className="flex flex-col mr-[25px]">
                    <label className="flex flex-row labels mb-1" required>
                      Product Groups{" "}
                      <span className="ml-1 text-red-500">*</span>
                      <Image
                        alt="prophetLogo"
                        src={prophetLogo}
                        className="ml-1 -mt-1 h-6 w-6"
                        title="SPDFIL-Sort group number(18)"
                      />
                    </label>
                    <div className="flex flex-row items-center">
                      <Select
                        options={productGroups}
                        placeholder="Select..."
                        value={productGroup}
                        onChange={handleProductGroups}
                        isSearchable={true}
                        ref={groupRef}
                        instanceId="selectbox2"
                        className="reactSelectCustom w-full"
                        classNames={{
                          control: (state) =>
                            state.is_new == true
                              ? "text-red-500"
                              : "!text-red-500",
                        }}
                        styles={customSelectStyles}
                        isClearable={true}
                        isDisabled={
                          submittedToISS || isUserHaveCreated ? true : false
                        }
                      />
                    </div>
                    {!isValidProductGroup ? (
                      <span className="text-red-500">
                        Please Select a Product Group
                      </span>
                    ) : (
                      ""
                    )}
                  </div>
                  {/* )} */}

                  <div className="flex flex-col mr-[25px]">
                    <label className="flex flex-row labels mb-1">
                      Count or Pack Size{" "}
                      <span className="ml-1 text-red-500">*</span>
                      <Image
                        alt="prophetLogo"
                        src={prophetLogo}
                        className="ml-1 -mt-1 h-6 w-6"
                        title="SPDFIL-Count or size (4)"
                      />
                    </label>
                    <input
                      type="text"
                      maxLength={40}
                      name="count_or_size"
                      value={countSize}
                      placeholder="Enter count or pack size (eg 56 or 10X150g)"
                      onChange={(e) => {
                        setCountSizse(e.target.value),
                          handleCountOrSize(e.target.value);
                      }}
                      onBlur={(e) => {
                        const trimmedValue = trimInputText(e.target.value);
                        setCountSizse(trimmedValue);
                        handleCountOrSize(trimmedValue);
                      }}
                      //onBlur={handleMarkVarietyChange}
                      // className="w-full py-1 px-2 2xl:px-3 2xl:py-3 border border-light-gray rounded-md"
                      // tabIndex={7}
                      className="input-number w-full py-1 px-2 2xl:px-3 2xl:py-3 border rounded-md"
                      disabled={
                        submittedToISS
                          ? true
                          : false || isUserHaveCreated
                          ? true
                          : false
                      }
                    />
                    {!isValidCountOrSize ? (
                      <span className="text-red-500">
                        Please Enter the Count or Size
                      </span>
                    ) : (
                      ""
                    )}
                  </div>
                  <div className="flex flex-col mr-[25px]">
                    <label className="flex flex-row labels mb-1">
                      Units in Outer{" "}
                      <span className="ml-1 text-red-500">*</span>
                      <Image
                        alt="prophetLogo"
                        src={prophetLogo}
                        className="ml-1 -mt-1 h-6 w-6"
                        title="SPDFIL-Units in outer (13)"
                      />
                    </label>
                    <div className="flex items-center mr-1">
                      <input
                        type="number"
                        max={10000000}
                        min={0}
                        name="units_in_outer"
                        placeholder="Enter number of units"
                        value={unitsInOuter}
                        onChange={(e) => {
                          const val =
                            e.target.value > 10000000
                              ? "10000000"
                              : e.target.value;
                          setUnitsInOuter(val), handleUnitsInOuter(val);
                        }}
                        className="input-number w-full py-1 px-2 2xl:px-3 2xl:py-3 border rounded-md"
                        required
                        // tabIndex={9}
                        disabled={
                          submittedToISS
                            ? true
                            : false || isUserHaveCreated
                            ? true
                            : false
                        }
                      />

                      {/* <FontAwesomeIcon icon={faPlus} style={{color: "#0066ff",}} /> */}
                    </div>
                    {!isValidUnitsInOuter ? (
                      <span className="text-red-500">
                        Please Enter the Units in Outer
                      </span>
                    ) : (
                      ""
                    )}
                  </div>
                  <div className="flex flex-col mr-[25px]">
                    <label className="flex flex-row labels mb-1">
                      Cases (Outers) Per Pallet{" "}
                      <span className="ml-1 text-red-500">*</span>
                      <Image
                        alt="prophetLogo"
                        src={prophetLogo}
                        className="ml-1 -mt-1 h-6 w-6"
                        title="SPDFIL-Packs Per Pallet"
                      />{" "}
                    </label>
                    <input
                      type="number"
                      max={10000000}
                      min={0}
                      name="cases_outers_per_pallet"
                      placeholder="Enter cases per pallet"
                      value={casesPerPallet}
                      onChange={(e) => {
                        const val =
                          e.target.value > 10000000
                            ? "10000000"
                            : e.target.value;
                        setCasesPerPallet(val), handleCasesPerPallet(val);
                      }}
                      className="input-number w-full py-1 px-2 2xl:px-3 2xl:py-3 border rounded-md"
                      required
                      // tabIndex={9}
                      // style={{ textTransform: "capitalize" }}
                      disabled={
                        submittedToISS
                          ? true
                          : false || isUserHaveCreated
                          ? true
                          : false
                      }
                    />
                    {!isValidCasesPerPallet ? (
                      <span className="text-red-500">
                        Please Enter the Cases Per Pallet
                      </span>
                    ) : (
                      ""
                    )}
                  </div>
                  <div className="flex flex-col mr-[25px]">
                    <label className="flex flex-row labels mb-1">
                      Net Weight of Case (Outer) in KG{" "}
                      <span className="ml-1 text-red-500">*</span>
                      {/* <Image
                        alt="prophetLogo"
                        src={prophetLogo}
                        className="ml-1 -mt-1 h-6 w-6"
                        title="Prophet Column - Name"
                      />{" "} */}
                    </label>
                    <input
                      type="number"
                      max={10000000}
                      min={0}
                      name="net_weight_of_caes_in_kg"
                      value={netWeightCase}
                      placeholder="Enter weight in kilograms"
                      onChange={(e) => {
                        const val =
                          e.target.value > 10000000
                            ? "10000000"
                            : e.target.value;
                        setNetWeightCase(val), handleNetWeightCase(val);
                      }}
                      //onBlur={handleMarkVarietyChange}
                      className="input-number w-full py-1 px-2 2xl:px-3 2xl:py-3 border rounded-md"
                      required
                      // tabIndex={9}
                      disabled={
                        submittedToISS
                          ? true
                          : false || isUserHaveCreated
                          ? true
                          : false
                      }
                    />
                    {!isValidNetWeightOuter ? (
                      <span className="text-red-500">
                        Please Enter the Net Weight Outer
                      </span>
                    ) : (
                      ""
                    )}
                  </div>
                  <div className="flex flex-col mr-[25px]">
                    <label className="flex flex-row labels mb-1 tracking-tight xl:tracking-normal">
                      Gross Weight of Case (outer) in KG{" "}
                      <span className="ml-1 text-red-500">*</span>
                      <Image
                        alt="prophetLogo"
                        src={prophetLogo}
                        className="ml-1 -mt-1 h-6 w-6"
                        title="SPDFIL-Weight of outer (32)"
                      />{" "}
                    </label>
                    <input
                      type="number"
                      max={10000000}
                      min={0}
                      name="gross_weight_of_cases_in_kg"
                      value={grossWeightCase}
                      placeholder="Enter weight in kilograms"
                      onChange={(e) => {
                        const val =
                          e.target.value > 10000000
                            ? "10000000"
                            : e.target.value;
                        setGrossWeightCase(val), handleGrossWeight(val);
                      }}
                      className="input-number w-full py-1 px-2 2xl:px-3 2xl:py-3 border rounded-md"
                      required
                      // tabIndex={9}
                      disabled={
                        submittedToISS
                          ? true
                          : false || isUserHaveCreated
                          ? true
                          : false
                      }
                    />
                    {!isValidGrossWeightOuter ? (
                      <span className="text-red-500">
                        Please Enter the Gross Weight Outer
                      </span>
                    ) : (
                      ""
                    )}
                    {grossWeightCase &&
                      parseInt(netWeightCase) > parseInt(grossWeightCase) && (
                        <span className="text-red-500">
                          Gross Weight Cannot be Less than Net Weight
                        </span>
                      )}
                  </div>

                  <div className="flex flex-col mr-[25px]">
                    <label className="flex flex-row labels mb-1" required>
                      Mark <span className="ml-1 text-red-500">*</span>
                      <Image
                        alt="prophetLogo"
                        src={prophetLogo}
                        className="ml-1 -mt-1 h-6 w-6"
                        title="SPDFIL-Mark/variety (3)"
                      />
                    </label>
                    <div className="flex flex-row items-center">
                      <Select
                        // options={mark_variaty}
                        options={allDropdown?.markVariety?.filter(
                          (mark) => mark.product_type === 1
                        )}
                        placeholder="Select..."
                        value={markVariety}
                        onChange={handleMarkVarietyChange}
                        isSearchable={true}
                        instanceId="selectbox"
                        className="reactSelectCustom w-full"
                        styles={customSelectStyles}
                        isClearable={true}
                        isDisabled={
                          submittedToISS || isUserHaveCreated ? true : false
                        }
                      />
                    </div>
                    {!isValidMark ? (
                      <span className="text-red-500">Please Select a Mark</span>
                    ) : (
                      ""
                    )}
                  </div>
                  {/* space to populate the left cell  */}
                  <div className="hidden lg:flex"></div>
                  <div className="flex flex-col mr-[25px]">
                    <label className="flex flex-row labels mb-1" required>
                      Sub Product Code{" "}
                      <span className="ml-1 text-red-500">*</span>
                      <Image
                        alt="prophetLogo"
                        src={prophetLogo}
                        className="ml-1 -mt-1 h-6 w-6"
                        title="SPDFIL-Sub product code (2)"
                      />
                    </label>
                    <div className="flex flex-row items-center">
                      <Select
                        options={subProductCodeData}
                        // newDataForSubProductCodeDropdownData
                        //   .unshift({
                        //   "value": 0,
                        //   "label": "New Sub Product Code",
                        // })
                        placeholder="Select..."
                        value={selectedsubProductCode}
                        onChange={handleSelectedSubproductCodeDropdown}
                        isSearchable={true}
                        instanceId="selectbox"
                        className="reactSelectCustom w-full"
                        styles={customSelectStyles}
                        isClearable={true}
                        isDisabled={
                          !masterProductCode ||
                          submittedToISS ||
                          isUserHaveCreated
                            ? true
                            : false
                        }
                      />
                    </div>
                    {!isValidSubProductCode &&
                    disableCustomSubProductCodeInput ? (
                      <span className="text-red-500">
                        Please Select a Sub Product Code
                      </span>
                    ) : (
                      ""
                    )}
                  </div>

                  <div className="flex flex-col mr-[25px]">
                    <label className="flex flex-row labels mb-1">
                      Sub Product Code (Suggested){" "}
                      <span className="ml-1 text-red-500">*</span>
                      <Image
                        alt="prophetLogo"
                        src={prophetLogo}
                        className="ml-1 -mt-1 h-6 w-6"
                        title="SPDFIL-Sub product code (2)"
                      />
                      <div>
                        <FontAwesomeIcon
                          icon={faInfoCircle}
                          size="lg"
                          onClick={toggleRawMaterialImage}
                          className={`${
                            selectedsubProductCode[0]?.value === 0
                              ? "block"
                              : "hidden"
                          } ml-3 -mt-[1px] m-1 cursor-pointer`}
                          style={{ color: "#111111" }}
                          disabled={
                            submittedToISS
                              ? true
                              : false || isUserHaveCreated
                              ? true
                              : false
                          }
                        />
                      </div>
                    </label>

                    <input
                      type="text"
                      maxLength={50}
                      name="sub_product_code"
                      value={subProductCode || ""}
                      onChange={(e) => {
                        setSubProductCode(e.target.value.toUpperCase()),
                          handleSubProductCode(e.target.value.toUpperCase());
                        // setIsValidSubProductCode(true);
                      }}
                      onBlur={(e) => {
                        const trimmedValue = trimInputText(e.target.value);
                        setSubProductCode(trimmedValue);
                        handleSubProductCode(trimmedValue);
                        // setIsValidSubProductCode(trimmedValue);
                      }}
                      className={`w-full -mt-[2px] py-1 px-2 2xl:px-3 2xl:py-3 border rounded-md bg-white`}
                      required
                      // tabIndex={9}
                      disabled={
                        disableCustomSubProductCodeInput || submittedToISS
                          ? true
                          : false || isUserHaveCreated
                          ? true
                          : false
                      }
                    />
                    <div
                      className={`fixed top-0 left-0 right-0 bottom-0 bg-black/25 z-10 ${
                        isRawMaterialImage ? "block" : "hidden"
                      }`}
                    ></div>
                    <div className="z-10 absolute w-[60%] xl:w-[70%] 2xl:w-[60%] top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2">
                      <div
                        className={`flex justify-end absolute -top-0 right-2 ${
                          isRawMaterialImage ? "block" : "hidden"
                        }`}
                      >
                        <FontAwesomeIcon
                          icon={faXmark}
                          size="lg"
                          onClick={toggleRawMaterialImage}
                          className="cursor-pointer bg-white rounded-full p-3"
                          style={{ color: "#111111" }}
                          disabled={
                            submittedToISS
                              ? true
                              : false || isUserHaveCreated
                              ? true
                              : false
                          }
                        />
                      </div>
                      <img
                        className={` ${
                          isRawMaterialImage ? "block" : "hidden"
                        }`}
                        src={rawMaterialScenarios.src}
                        // onMouseEnter={showRawMaterialImage}
                        // onMouseLeave={hideRawMaterialImage}
                      />
                    </div>
                    {!isValidSubProductCode &&
                    !disableCustomSubProductCodeInput ? (
                      <span className="text-red-500">
                        Please Enter the Sub Product Code
                      </span>
                    ) : (
                      ""
                    )}
                    {/* && selectedsubProductCode[0]?.value === 0  */}
                    {subProductCodeValueAlreadyExists &&
                    (rawMaterialData
                      ? rawMaterialData[0]?.sub_product_code !== subProductCode
                      : true) ? (
                      <span className="text-red-500">
                        Sub Product Code Already Exists
                      </span>
                    ) : (
                      ""
                    )}
                  </div>
                  <div></div>
                </div>
              </div>
            </div>
            <div className="p-x5 ps-8 mb-0 w-1/2">
              <div className="mb-6 mt-9">
                <div className="grid lg:grid-cols-2 gap-6 grid-cols-1 mt-6">
                  <div className="flex flex-col mr-[25px]">
                    <label className="flex flex-row labels mb-1" required>
                      Temperature Grade{" "}
                      <span className="ml-1 text-red-500">*</span>
                      <Image
                        alt="prophetLogo"
                        src={prophetLogo}
                        className="ml-1 -mt-1 h-6 w-6"
                        title="SPDFIL-Temperature Grade Field (19)"
                      />
                    </label>
                    <Select
                      options={allDropdown?.temperatureGrade}
                      placeholder="Select..."
                      value={temperatureGrade}
                      onChange={handleTemperatureChange}
                      isSearchable={true}
                      className="reactSelectCustom w-full"
                      instanceId="selectbox"
                      styles={customSelectStyles}
                      isClearable={true}
                      isDisabled={
                        submittedToISS || isUserHaveCreated ? true : false
                      }
                      //onBlur={handleValidationChange}
                    />
                    {!isValidTemperatureGrade ? (
                      <span className="text-red-500">
                        Please Select a Temperature Grade
                      </span>
                    ) : (
                      ""
                    )}
                  </div>
                  <div className="flex flex-col mr-[25px]">
                    <label className="flex flex-row labels mb-1">
                      Class Required
                      {/* <Image
                        alt="prophetLogo"
                        src={prophetLogo}
                        className="ml-1 -mt-1 h-6 w-6"
                        title="Prophet Column - Name"
                      /> */}
                    </label>

                    <input
                      type="text"
                      maxLength={100}
                      name="class_required"
                      value={classRequired}
                      onChange={(e) => setClassRequired(e.target.value)}
                      onBlur={(e) => {
                        const trimmedValue = trimInputText(e.target.value);
                        setClassRequired(trimmedValue);
                      }}
                      className={`w-full py-1 px-2 2xl:px-3 2xl:py-3 border rounded-md`}
                      required
                      style={{ textTransform: "capitalize" }}
                      // tabIndex={1}
                      disabled={
                        submittedToISS
                          ? true
                          : false || isUserHaveCreated
                          ? true
                          : false
                      }
                    />
                  </div>
                  <div className="flex flex-col mr-[25px]">
                    <label className="flex flex-row labels mb-1" required>
                      Intrastat Commodity Code
                      <Image
                        alt="prophetLogo"
                        src={prophetLogo}
                        className="ml-1 -mt-1 h-6 w-6"
                        title="SPDFIL-Commodity code (35)"
                      />
                    </label>
                    <Select
                      options={allDropdown.intrastatCommodityCode}
                      placeholder="Select..."
                      value={intrastCommodityCode ? intrastCommodityCode : null}
                      onChange={handleIntrastCommodityCodeChange}
                      isSearchable={true}
                      instanceId="selectbox"
                      className="reactSelectCustom w-full"
                      styles={customSelectStyles}
                      isClearable={true}
                      isDisabled={
                        submittedToISS || isUserHaveCreated ? true : false
                      }
                      //onBlur={handleValidationChange}
                    />
                  </div>
                  <div className="flex flex-col mr-[25px]">
                    <label className="flex flex-row labels mb-1">
                      Organic Certification{" "}
                      {!isValidOrganicCertification && (
                        <span className="ml-1 text-red-500">*</span>
                      )}
                      {/* <Image
                        alt="prophetLogo"
                        src={prophetLogo}
                        className="ml-1 -mt-1 h-6 w-6"
                        title="Prophet Column - Name"
                      /> */}
                    </label>
                    <Select
                      options={allDropdown?.organicCertification}
                      placeholder="Select..."
                      value={organicCertification}
                      onChange={handleOrganicCertification}
                      isSearchable={true}
                      instanceId="selectbox"
                      className="reactSelectCustom w-full"
                      styles={customSelectStyles}
                      isClearable={true}
                      isDisabled={
                        submittedToISS || isUserHaveCreated ? true : false
                      }
                    />
                    {!isValidOrganicCertification && !organicCertification ? (
                      <span className="text-red-500">
                        Please Enter an Organic Certification
                      </span>
                    ) : (
                      ``
                    )}
                  </div>
                  <div className="flex flex-col mr-[25px]">
                    <label className="flex flex-row labels mb-1" required>
                      Classified Allergic Under the FSA14 Allergen Types?
                      {/* <Image
                        alt="prophetLogo"
                        src={prophetLogo}
                        className="ml-1 -mt-1 h-6 w-6"
                        title="Prophet Column - Name"
                      /> */}
                    </label>
                    <Select
                      options={allDropdown?.classifiedAllergicTypes}
                      placeholder="Select..."
                      value={
                        classifiedAllergicTypes ? classifiedAllergicTypes : ""
                      }
                      onChange={handleClassifiedAllergicTypes}
                      isSearchable={true}
                      instanceId="selectbox"
                      className="reactSelectCustom w-full"
                      styles={customSelectStyles}
                      isClearable={true}
                      isDisabled={
                        submittedToISS || isUserHaveCreated ? true : false
                      }
                      //onBlur={handleValidationChange}
                    />
                  </div>
                </div>
              </div>
              <div className="mb-6">
                {/* <div className="mb-3">
                  <h4 className="formtitle pb-2 border-b border-light-gray3">
                    Other Questions
                  </h4>
                </div> */}
                <div className="grid lg:grid-cols-2 gap-6 grid-cols-1 w-[94%]">
                  {/* <div className="flex flex-col">
                    <label className="flex flex-row labels mb-1" required>
                      Brand
                    </label>
                    <div className="flex flex-row items-center">
                      <Select
                        options={allDropdown?.brand}
                        placeholder="Select..."
                        value={brand}
                        ref={brandRef}
                        openMenuOnFocus={true}
                        onChange={handleBrandChange}
                        isSearchable={true}
                        instanceId="selectbox"
                        className="reactSelectCustom w-full"
                        styles={customSelectStyles}
                        isClearable={true}
                        isDisabled={
                          submittedToISS || isUserHaveCreated ? true : false
                        }
                        //onBlur={handleValidationChange}
                      />
                      <button
                        onClick={() => handleIconClick("brand")}
                        className={`ml-2 py-1 p-[6px] border rounded-md border-skin-primary text-skin-primary cursor-pointer ${
                          disabledClass
                            ? "cursor-not-allowed pointer-events-none opacity-0.5"
                            : ""
                        }`}
                        disabled={
                          submittedToISS
                            ? true
                            : false || isUserHaveCreated
                            ? true
                            : false
                        }
                      >
                        <FontAwesomeIcon icon={faPlus} />
                      </button>
                    </div>
                  </div>{" "}
                  <div className="flex flex-col">
                    <label
                      className="w-full py-1 px-2 2xl:px-3 2xl:py-2 mt-6"
                      // onClick={() => openSelect(brandRef)}
                    >
                      {rawBrandCode
                        ? rawBrandCode
                        : "Click + to Add a New Brand"}
                    </label>
                  </div>
                  {/* <div className="flex flex-col mr-[25px]">
                      <label className="labels mb-1">Code</label>
                      <span className="flex items-center h-[28px]"><b>{rawBrand}</b></span>
                    </div> */}
                  {/* <div className="flex flex-col">
                    <label className="flex flex-row labels mb-1" required>
                      Country of Origin
                    </label>
                    <div className="flex flex-row items-center">
                      <Select
                        options={allDropdown?.countryOfOrigin}
                        placeholder="Select..."
                        value={countryOfOrigin}
                        onChange={handleCoo}
                        ref={cooRef}
                        openMenuOnFocus={true}
                        isSearchable={true}
                        instanceId="selectbox"
                        className="reactSelectCustom w-full"
                        styles={customSelectStyles}
                        isClearable={true}
                        isDisabled={
                          submittedToISS || isUserHaveCreated ? true : false
                        }
                        //onBlur={handleValidationChange}
                      />
                      <button
                        onClick={() => handleIconClick("country_of_origin")}
                        className={`ml-2 py-1 p-[6px] border rounded-md border-skin-primary text-skin-primary cursor-pointer ${
                          disabledClass
                            ? "cursor-not-allowed pointer-events-none opacity-0.5"
                            : ""
                        }`}
                        disabled={
                          submittedToISS
                            ? true
                            : false || isUserHaveCreated
                            ? true
                            : false
                        }
                      >
                        <FontAwesomeIcon icon={faPlus} />
                      </button>
                    </div>
                  </div>
                  <div className="flex flex-col">
                    <label
                      className="w-full py-1 px-2 2xl:px-3 2xl:py-2 mt-6"
                      // onClick={() => openSelect(cooRef)}
                    >
                      {rawCOOCode
                        ? rawCOOCode
                        : "Click + to Add a New Country of Origin"}
                    </label>
                  </div>
                  <div className="flex flex-col">
                    <label className="flex flex-row labels mb-1" required>
                      Calibre Size
                    </label>
                    <div className="flex flex-row items-center">
                      <Select
                        options={allDropdown?.caliberSize}
                        placeholder="Select..."
                        value={calibreSize}
                        ref={caliberSizeRef}
                        openMenuOnFocus={true}
                        onChange={handleCalibreSizeChange}
                        isSearchable={true}
                        instanceId="selectbox"
                        className="reactSelectCustom w-full"
                        styles={customSelectStyles}
                        isClearable={true}
                        isDisabled={
                          submittedToISS || isUserHaveCreated ? true : false
                        }
                        //onBlur={handleValidationChange}
                      />
                      <button
                        onClick={() => handleIconClick("caliber_size")}
                        className={`ml-2 py-1 p-[6px] border rounded-md border-skin-primary text-skin-primary cursor-pointer ${
                          disabledClass
                            ? "cursor-not-allowed pointer-events-none opacity-0.5"
                            : ""
                        }`}
                        disabled={
                          submittedToISS
                            ? true
                            : false || isUserHaveCreated
                            ? true
                            : false
                        }
                      >
                        <FontAwesomeIcon icon={faPlus} />
                      </button>
                    </div>
                  </div>
                  <div className="flex flex-col">
                    <label className="w-full py-1 px-2 2xl:px-3 2xl:py-2 mt-6">
                      {rawCaliberSizeCode
                        ? rawCaliberSizeCode
                        : "Click + to Add a New Caliber Size"}
                    </label>
                  </div>
                  <div className="flex flex-col">
                    <label className="flex flex-row labels mb-1" required>
                      End Customer (Retail Customer)
                    </label>
                    <div className="flex flex-row items-center">
                      <Select
                        options={allDropdown?.endCustomer}
                        placeholder="Select..."
                        value={endCustomer}
                        ref={endCustomerRef}
                        openMenuOnFocus={true}
                        onChange={handleEndCustomerChange}
                        isSearchable={true}
                        instanceId="selectbox"
                        className="reactSelectCustom w-full"
                        styles={customSelectStyles}
                        isClearable={true}
                        isDisabled={
                          submittedToISS || isUserHaveCreated ? true : false
                        }
                        //onBlur={handleValidationChange}
                      />
                      <button
                        onClick={() => handleIconClick("end_customer")}
                        className={`ml-2 py-1 p-[6px] border rounded-md border-skin-primary text-skin-primary cursor-pointer ${
                          disabledClass
                            ? "cursor-not-allowed pointer-events-none opacity-0.5"
                            : ""
                        }`}
                        disabled={
                          submittedToISS
                            ? true
                            : false || isUserHaveCreated
                            ? true
                            : false
                        }
                      >
                        <FontAwesomeIcon icon={faPlus} />
                      </button>
                    </div>
                  </div>
                  <div className="flex flex-col">
                    <label
                      className="w-full py-1 px-2 2xl:px-3 2xl:py-2 mt-6"
                      // onClick={() => openSelect(endCustomerRef)}
                    >
                      {rawEndCustomerCode
                        ? rawEndCustomerCode
                        : "Click + to Add a New End Customer"}
                    </label>
                  </div>
                  <div className="flex flex-col">
                    <label className="flex flex-row labels mb-1" required>
                      Variety
                    </label>
                    <div className="flex flex-row items-center">
                      <Select
                        options={allDropdown?.variety}
                        placeholder="Select..."
                        value={variety}
                        onChange={handleVarietyChange}
                        ref={varietyRef}
                        openMenuOnFocus={true}
                        isSearchable={true}
                        instanceId="selectbox"
                        className="reactSelectCustom w-full"
                        styles={customSelectStyles}
                        isClearable={true}
                        isDisabled={
                          submittedToISS || isUserHaveCreated ? true : false
                        }
                        //onBlur={handleValidationChange}
                      />
                      <button
                        onClick={() => handleIconClick("variety")}
                        className={`ml-2 py-1 p-[6px] border rounded-md border-skin-primary text-skin-primary cursor-pointer ${
                          disabledClass
                            ? "cursor-not-allowed pointer-events-none opacity-0.5"
                            : ""
                        }`}
                        disabled={
                          submittedToISS
                            ? true
                            : false || isUserHaveCreated
                            ? true
                            : false
                        }
                      >
                        <FontAwesomeIcon icon={faPlus} />
                      </button>
                    </div>
                  </div>
                  <div className="flex flex-col">
                    <label
                      className="w-full py-1 px-2 2xl:px-3 2xl:py-2 mt-6"
                      // onClick={() => openSelect(varietyRef)}
                    >
                      {rawVarietyCode
                        ? rawVarietyCode
                        : "Click + to Add a New Variety"}
                    </label>
                  </div>  */}
                  <div className="">
                    <p className="flex flex-row labels mb-1">Email Comment</p>
                    <textarea
                      className=" flex flex-col w-full rounded-md p-2 border border-light-gray2"
                      rows="3"
                      value={emailComment ? emailComment : ""}
                      maxLength={250}
                      onChange={(e) => {
                        setEmailComment(e.target.value);
                      }}
                      onBlur={(e) => {
                        const trimmedValue = trimInputText(e.target.value);
                        setEmailComment(trimmedValue);
                      }}
                      placeholder="Enter a comment..."
                      disabled={
                        submittedToISS
                          ? true
                          : false || isUserHaveCreated
                          ? true
                          : false
                      }
                    ></textarea>
                    {!isValidUnblockReason && (
                      <span className="text-red-500">
                        Please Enter an Unblock Reason
                      </span>
                    )}
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div className="flex justify-between border-t border-light-gray py-5 bg-white">
            <div></div>
            <div>
              {userData && userData.role_id == 1 && (
                <button
                  className="border border-skin-primary text-skin-primary me-10 py-1 px-8 font-medium rounded-md"
                  onClick={handleWithoutReason}
                  // disabled={
                  //   status == 6 || submittedToISS
                  //     ? true
                  //     : false || isUserHaveCreated
                  //     ? true
                  //     : false
                  // }
                >
                  Cancel
                </button>
              )}
              {submittedToISS && (
                <button
                  className="border border-skin-primary text-skin-primary me-10 py-1 px-8 font-medium rounded-md"
                  onClick={handleUnblockEvent}
                  disabled={
                    submittedToISS
                      ? false
                      : true && isUserHaveCreated
                      ? true
                      : false
                  }
                >
                  Unblock
                </button>
              )}

              {!submittedToISS && (
                <>
                  <button
                    className="border  border-save-green bg-save-green text-white rounded-md me-10 py-1 px-8 font-medium"
                    onClick={() => {
                      setSubmissionType("save");
                      handleValidate(false, "save");
                    }}
                    disabled={
                      submittedToISS
                        ? true
                        : false || isUserHaveCreated
                        ? true
                        : false
                    }
                  >
                    Save & Exit
                  </button>
                  <button
                    className="border border-skin-primary  text-white bg-skin-primary py-1 px-8 font-medium rounded-md"
                    disabled={
                      submittedToISS
                        ? true
                        : false || isUserHaveCreated
                        ? true
                        : false
                    }
                    onClick={() => {
                      setSubmissionType("submit");
                      //setTimeout(function(){
                      handleValidate(false, "submit");
                      // }, 5000);
                    }}
                  >
                    Submit
                  </button>
                </>
              )}
            </div>
          </div>
        </div>
      )}

      <Transition appear show={isOpen} as={Fragment}>
        <Dialog as="div" className="relative z-10" onClose={closeModal}>
          <Transition.Child
            as={Fragment}
            enter="ease-out duration-300"
            enterFrom="opacity-0"
            enterTo="opacity-100"
            leave="ease-in duration-200"
            leaveFrom="opacity-100"
            leaveTo="opacity-0"
          >
            <div className="fixed inset-0 bg-black bg-opacity-25 backdrop-blur-sm" />
          </Transition.Child>

          <div className="fixed inset-0 overflow-y-auto">
            <div className="flex items-center justify-center min-h-full p-4 text-center">
              <Transition.Child
                as={Fragment}
                enter="ease-out duration-300"
                enterFrom="opacity-0 scale-95"
                enterTo="opacity-100 scale-100"
                leave="ease-in duration-200"
                leaveFrom="opacity-100 scale-100"
                leaveTo="opacity-0 scale-95"
              >
                <Dialog.Panel className=" w-[45%] transform overflow-hidden rounded-xl bg-white  text-left align-middle shadow-xl transition-all">
                  {/* <!-- Modal content --> */}
                  <div className="relative bg-white rounded-lg shadow">
                    {/* <!-- Modal header --> */}
                    <div className="flex items-start justify-between p-8 rounded-t">
                      <h3 className="flex flex-row text-xl font-semibold text-gray-900  items-center">
                        {/* <span className="flex justify-center items-center border border-skin-primary text-skin-primary w-[25px] h-[25px] text-center leading-5 rounded-full text-base me-4">
                          <FontAwesomeIcon icon={faInfo} />{" "}
                        </span>{" "} */}
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          className="w-6 mr-3 text-skin-primary fill-current"
                          viewBox="0 0 512 512"
                        >
                          <path d="M248.4 84.3c1.6-2.7 4.5-4.3 7.6-4.3s6 1.6 7.6 4.3L461.9 410c1.4 2.3 2.1 4.9 2.1 7.5c0 8-6.5 14.5-14.5 14.5l-387 0c-8 0-14.5-6.5-14.5-14.5c0-2.7 .7-5.3 2.1-7.5L248.4 84.3zm-41-25L9.1 385c-6 9.8-9.1 21-9.1 32.5C0 452 28 480 62.5 480l387 0c34.5 0 62.5-28 62.5-62.5c0-11.5-3.2-22.7-9.1-32.5L304.6 59.3C294.3 42.4 275.9 32 256 32s-38.3 10.4-48.6 27.3zM288 368a32 32 0 1 0 -64 0 32 32 0 1 0 64 0zm-8-184c0-13.3-10.7-24-24-24s-24 10.7-24 24l0 96c0 13.3 10.7 24 24 24s24-10.7 24-24l0-96z" />
                        </svg>
                        Warning
                      </h3>
                      <button
                        onClick={closeModal}
                        type="button"
                        className="text-black bg-transparent hover:bg-gray-200 hover:text-gray-900 rounded-lg text-xl w-8 h-8 ml-auto inline-flex justify-center items-center "
                        data-modal-hide="default-modal"
                      >
                        <FontAwesomeIcon
                          icon={faXmark}
                          className="text-skin-primary"
                        />{" "}
                      </button>
                    </div>
                    {/* <!-- Modal body --> */}
                    <div className="p-8 py-0 space-y-6">
                      <p className="text-base xl:text-md 2xl:text-lg leading-relaxed mt-2">
                        Mandatory information missing/incorrect. Do you want to
                        continue?
                      </p>
                    </div>
                    {/* <!-- Modal footer --> */}
                    <div className="flex items-end p-6 space-x-2 justify-end">
                      <button
                        onClick={closeModal}
                        data-modal-hide="default-modal"
                        type="button"
                        className="border border-skin-primary text-skin-primary  focus:ring-4 focus:outline-none focus:ring-blue-300 font-medium rounded-md text-md px-6 py-2 text-center "
                      >
                        Cancel
                      </button>
                      <button
                        onClick={handleContinueSubmit}
                        data-modal-hide="default-modal"
                        type="button"
                        className="text-white bg-skin-primary hover:bg-skin-primary focus:ring-4 focus:outline-none focus:ring-blue-300 font-medium rounded-md text-md px-6 py-2 text-center "
                      >
                        Continue
                      </button>
                    </div>
                  </div>
                </Dialog.Panel>
              </Transition.Child>
            </div>
          </div>
        </Dialog>
      </Transition>

      <Transition appear show={isSubmitModalOpen} as={Fragment}>
        <Dialog as="div" className="relative z-10" onClose={closeSubmitModal}>
          <Transition.Child
            as={Fragment}
            enter="ease-out duration-300"
            enterFrom="opacity-0"
            enterTo="opacity-100"
            leave="ease-in duration-200"
            leaveFrom="opacity-100"
            leaveTo="opacity-0"
          >
            <div className="fixed inset-0 bg-black bg-opacity-25 backdrop-blur-sm" />
          </Transition.Child>

          <div className="fixed inset-0 overflow-y-auto">
            <div className="flex items-center justify-center min-h-full p-4 text-center">
              <Transition.Child
                as={Fragment}
                enter="ease-out duration-300"
                enterFrom="opacity-0 scale-95"
                enterTo="opacity-100 scale-100"
                leave="ease-in duration-200"
                leaveFrom="opacity-100 scale-100"
                leaveTo="opacity-0 scale-95"
              >
                <Dialog.Panel className=" w-[45%] transform overflow-hidden rounded-xl bg-white  text-left align-middle shadow-xl transition-all">
                  <div className="relative bg-white rounded-lg shadow">
                    <div className="flex items-start justify-between p-8 rounded-t">
                      <h3 className="flex flex-row text-xl font-semibold text-gray-900  items-center">
                        {validSubmitMode ? (
                          <span className="flex justify-center items-center border border-skin-primary text-skin-primary w-[25px] h-[25px] text-center leading-5 rounded-full text-base me-4">
                            <FontAwesomeIcon icon={faInfo} />
                          </span>
                        ) : (
                          <svg
                            xmlns="http://www.w3.org/2000/svg"
                            className="w-6 mr-3 text-skin-primary fill-current"
                            viewBox="0 0 512 512"
                          >
                            <path d="M248.4 84.3c1.6-2.7 4.5-4.3 7.6-4.3s6 1.6 7.6 4.3L461.9 410c1.4 2.3 2.1 4.9 2.1 7.5c0 8-6.5 14.5-14.5 14.5l-387 0c-8 0-14.5-6.5-14.5-14.5c0-2.7 .7-5.3 2.1-7.5L248.4 84.3zm-41-25L9.1 385c-6 9.8-9.1 21-9.1 32.5C0 452 28 480 62.5 480l387 0c34.5 0 62.5-28 62.5-62.5c0-11.5-3.2-22.7-9.1-32.5L304.6 59.3C294.3 42.4 275.9 32 256 32s-38.3 10.4-48.6 27.3zM288 368a32 32 0 1 0 -64 0 32 32 0 1 0 64 0zm-8-184c0-13.3-10.7-24-24-24s-24 10.7-24 24l0 96c0 13.3 10.7 24 24 24s24-10.7 24-24l0-96z" />
                          </svg>
                        )}
                        {validSubmitMode ? "Submit to ISS" : "Warning"}
                      </h3>
                      <button
                        onClick={closeSubmitModal}
                        type="button"
                        className="text-black bg-transparent hover:bg-gray-200 hover:text-gray-900 rounded-lg text-xl w-8 h-8 ml-auto inline-flex justify-center items-center "
                        data-modal-hide="default-modal"
                      >
                        <FontAwesomeIcon
                          icon={faXmark}
                          className="text-skin-primary"
                        />{" "}
                      </button>
                    </div>

                    <div className="p-8 py-0 space-y-6">
                      <p className="text-base xl:text-md 2xl:text-lg leading-relaxed mt-2">
                        {validSubmitMode
                          ? "Are you sure you want to submit?"
                          : "Mandatory information missing/incorrect. Please fill out all the details and then submit."}
                      </p>
                    </div>

                    <div className="flex items-end p-6 space-x-2 justify-end">
                      <button
                        onClick={
                          validSubmitMode
                            ? handleContinueSubmit
                            : closeSubmitModal
                        }
                        data-modal-hide="default-modal"
                        type="button"
                        className={`border border-skin-primary px-6 py-2 font-medium rounded-md ${
                          // validSubmitMode
                          "  text-white bg-skin-primary"
                          // : " text-skin-primary focus:ring-4 focus:outline-none focus:ring-blue-300 text-md  text-center"
                        } `}
                      >
                        {validSubmitMode ? "Submit To ISS" : "Okay"}
                      </button>
                    </div>
                  </div>
                </Dialog.Panel>
              </Transition.Child>
            </div>
          </div>
        </Dialog>
      </Transition>

      <Transition appear show={isUnblockOpen} as={Fragment}>
        <Dialog as="div" className="relative z-10" onClose={closeUnblockModal}>
          <Transition.Child
            as={Fragment}
            enter="ease-out duration-300"
            enterFrom="opacity-0"
            enterTo="opacity-100"
            leave="ease-in duration-200"
            leaveFrom="opacity-100"
            leaveTo="opacity-0"
          >
            <div className="fixed inset-0 bg-black bg-opacity-25 backdrop-blur-sm" />
          </Transition.Child>

          <div className="fixed inset-0 overflow-y-auto">
            <div className="flex items-center justify-center min-h-full p-4 text-center">
              <Transition.Child
                as={Fragment}
                enter="ease-out duration-300"
                enterFrom="opacity-0 scale-95"
                enterTo="opacity-100 scale-100"
                leave="ease-in duration-200"
                leaveFrom="opacity-100 scale-100"
                leaveTo="opacity-0 scale-95"
              >
                <Dialog.Panel className=" w-[45%] transform overflow-hidden rounded-xl bg-white  text-left align-middle shadow-xl transition-all">
                  {/* <!-- Modal content --> */}
                  <div className="relative bg-white rounded-lg shadow">
                    {/* <!-- Modal header --> */}
                    <div className="flex items-start justify-between p-8 rounded-t">
                      <h3 className="flex flex-row text-xl font-semibold text-gray-900  items-center">
                        <span className="flex justify-center items-center border border-skin-primary text-skin-primary w-[25px] h-[25px] text-center leading-5 rounded-full text-base me-4">
                          <FontAwesomeIcon icon={faInfo} />{" "}
                        </span>{" "}
                        Unblock Reason
                      </h3>
                      <button
                        onClick={closeUnblockModal}
                        type="button"
                        className="text-black bg-transparent hover:bg-gray-200 hover:text-gray-900 rounded-lg text-xl w-8 h-8 ml-auto inline-flex justify-center items-center "
                        data-modal-hide="default-modal"
                      >
                        <FontAwesomeIcon
                          icon={faXmark}
                          className="text-skin-primary"
                        />{" "}
                      </button>
                    </div>
                    {/* <!-- Modal body --> */}
                    <div className="p-8 py-0 space-y-6">
                      <p className="text-base xl:text-md 2xl:text-lg leading-relaxed mt-2">
                        Enter Product Request Unblock Reason.
                      </p>
                      <textarea
                        className="flex flex-col w-full rounded-md p-2 px-3 border border-light-gray2"
                        rows="8"
                        value={unblockedReasonapi}
                        maxLength={500}
                        onChange={(e) => {
                          setUnblockedReasonapi(e.target.value),
                            handleUnblockReason(e.target.value);
                        }}
                        onBlur={(e) => {
                          const trimmedValue = trimInputText(e.target.value);
                          setUnblockedReasonapi(trimmedValue),
                            handleUnblockReason(trimmedValue);
                        }}
                        placeholder="Provide reason for unblocking..."
                      ></textarea>
                      {!isValidUnblockReason && (
                        <span className="text-red-500">
                          Please Enter an Unblock Reason
                        </span>
                      )}
                    </div>
                    {/* <!-- Modal footer --> */}
                    <div className="flex items-end p-6 space-x-2 justify-end">
                      <button
                        onClick={saveUnblockModalData}
                        data-modal-hide="default-modal"
                        type="button"
                        className="text-white bg-skin-primary hover:bg-skin-primary focus:ring-4 focus:outline-none focus:ring-blue-300 font-medium rounded-md text-md px-6 py-2 text-center "
                      >
                        Unblock
                      </button>
                    </div>
                  </div>
                </Dialog.Panel>
              </Transition.Child>
            </div>
          </div>
        </Dialog>
      </Transition>
      {isDrawerOpen && (
        <DrawerComponent
          isDrawerOpen={isDrawerOpen}
          setIsDrawerOpen={setIsDrawerOpen}
          title={title}
          dropdownData={dropdownData}
          legend={legend}
          placeholderText={placeholderText}
          max_length={maxLength}
          min_length={minLength}
          dataKey={dataKey}
          onNewDropdownData={handleNewDropdownData}
          username={userData.name}
          useremail={userData.email}
          userData={userData}
          prophetId={prophetId}
        />
      )}
    </>
  );
};

export default RawMaterialRequest;
