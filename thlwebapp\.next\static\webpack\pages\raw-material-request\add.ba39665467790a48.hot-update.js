"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/raw-material-request/add",{

/***/ "./components/DrawerComponent.js":
/*!***************************************!*\
  !*** ./components/DrawerComponent.js ***!
  \***************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DrawerComponent: function() { return /* binding */ DrawerComponent; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _fortawesome_react_fontawesome__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @fortawesome/react-fontawesome */ \"./node_modules/@fortawesome/react-fontawesome/index.es.js\");\n/* harmony import */ var _fortawesome_free_solid_svg_icons__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @fortawesome/free-solid-svg-icons */ \"./node_modules/@fortawesome/free-solid-svg-icons/index.mjs\");\n/* harmony import */ var _fluentui_react_components__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @fluentui/react-components */ \"./node_modules/@fluentui/react-components/lib/index.js\");\n/* harmony import */ var _fluentui_react_icons__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @fluentui/react-icons */ \"./node_modules/@fluentui/react-icons/lib/index.js\");\n/* harmony import */ var ag_grid_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ag-grid-react */ \"./node_modules/ag-grid-react/lib/main.js\");\n/* harmony import */ var ag_grid_react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(ag_grid_react__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _services_apiConfig__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/services/apiConfig */ \"./services/apiConfig.js\");\n/* harmony import */ var lodash_add__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! lodash/add */ \"./node_modules/lodash/add.js\");\n/* harmony import */ var lodash_add__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(lodash_add__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var react_toastify__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! react-toastify */ \"./node_modules/react-toastify/dist/react-toastify.esm.mjs\");\n/* harmony import */ var react_toastify_dist_ReactToastify_css__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! react-toastify/dist/ReactToastify.css */ \"./node_modules/react-toastify/dist/ReactToastify.css\");\n/* harmony import */ var react_toastify_dist_ReactToastify_css__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(react_toastify_dist_ReactToastify_css__WEBPACK_IMPORTED_MODULE_7__);\n/* harmony import */ var _utils_renderer_productActionRenderer__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/utils/renderer/productActionRenderer */ \"./utils/renderer/productActionRenderer.js\");\n/* harmony import */ var _utils_renderer_productReferenceRenderer__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/utils/renderer/productReferenceRenderer */ \"./utils/renderer/productReferenceRenderer.js\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n // Import useRef and useCallback\n\n\n\n\n\n\n// import { GridApi } from \"ag-grid-community\";\nconst useStyles = (0,_fluentui_react_components__WEBPACK_IMPORTED_MODULE_10__.makeStyles)({\n    root: {\n        ..._fluentui_react_components__WEBPACK_IMPORTED_MODULE_10__.shorthands.border(\"2px\", \"solid\", \"#ccc\"),\n        ..._fluentui_react_components__WEBPACK_IMPORTED_MODULE_10__.shorthands.overflow(\"hidden\"),\n        display: \"flex\",\n        height: \"480px\",\n        backgroundColor: \"#fff\"\n    },\n    content: {\n        ..._fluentui_react_components__WEBPACK_IMPORTED_MODULE_10__.shorthands.flex(1),\n        ..._fluentui_react_components__WEBPACK_IMPORTED_MODULE_10__.shorthands.padding(\"16px\"),\n        display: \"grid\",\n        justifyContent: \"flex-start\",\n        alignItems: \"flex-start\",\n        gridRowGap: _fluentui_react_components__WEBPACK_IMPORTED_MODULE_10__.tokens.spacingVerticalXXL,\n        gridAutoRows: \"max-content\"\n    },\n    field: {\n        display: \"grid\",\n        gridRowGap: _fluentui_react_components__WEBPACK_IMPORTED_MODULE_10__.tokens.spacingVerticalS\n    }\n});\nconst DrawerComponent = (param)=>{\n    let { isDrawerOpen, setIsDrawerOpen, title, dropdownData, placeholderText, legend, max_length, min_length, dataKey, onNewDropdownData, username, useremail, userData, prophetId } = param;\n    _s();\n    console.log(\"dropdownData\", onNewDropdownData);\n    const styles = useStyles();\n    const labelId = (0,_fluentui_react_components__WEBPACK_IMPORTED_MODULE_10__.useId)(\"type-label\");\n    //const [isOpen, setIsOpen] = React.useState(true);\n    const [type, setType] = react__WEBPACK_IMPORTED_MODULE_1__.useState(\"overlay\");\n    const [description, setDescription] = react__WEBPACK_IMPORTED_MODULE_1__.useState(\"\");\n    const [removedRows, setRemovedRows] = react__WEBPACK_IMPORTED_MODULE_1__.useState(\"\");\n    const [value, setValue] = react__WEBPACK_IMPORTED_MODULE_1__.useState(\"\");\n    const [isButtonDisabled, setIsButtonDisabled] = react__WEBPACK_IMPORTED_MODULE_1__.useState(true);\n    const [isUpdateMode, setIsUpdateMode] = react__WEBPACK_IMPORTED_MODULE_1__.useState(false);\n    const [code, setCode] = react__WEBPACK_IMPORTED_MODULE_1__.useState(\"\");\n    const [rowData, setRowData] = react__WEBPACK_IMPORTED_MODULE_1__.useState([]);\n    const [addNewValue, setAddNewValue] = react__WEBPACK_IMPORTED_MODULE_1__.useState(false);\n    //const [keyy, setKey] =  React.useState(dataKey ? dataKey : \"\")\n    const serverAddress = _services_apiConfig__WEBPACK_IMPORTED_MODULE_4__.apiConfig.serverAddress;\n    const [isValidDescription, setisValidDescription] = react__WEBPACK_IMPORTED_MODULE_1__.useState(true);\n    const [isValidCode, setisValidCode] = react__WEBPACK_IMPORTED_MODULE_1__.useState(true);\n    // Define gridRef using useRef\n    const gridRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Define onFilterTextBoxChanged function using useCallback\n    const onFilterTextBoxChanged = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        gridRef.current.api.setQuickFilter(document.getElementById(\"filter-text-box\").value);\n    // setGridApi(params.api);\n    }, []);\n    const formatName = (params)=>{\n        // Check condition and return formatted value\n        // console.log(typeof params.data.is_new);\n        if (params.data.is_new === true) {\n            // console.log(\"inside here\");\n            // console.log(params.data.name);\n            return \"*\".concat(params.data.label);\n        }\n    //return null;\n    };\n    const formatCode = (params)=>{\n        // Check condition and return formatted value\n        if (params.data.is_new === true) {\n            // console.log(params.data.code);\n            return \"*\".concat(params.data.code);\n        }\n    //return null;\n    };\n    // Define defaultColDef and columnDefs using useMemo\n    const defaultColDef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>({\n            sortable: true,\n            filter: false,\n            resizable: true,\n            flex: 1,\n            suppressMenu: false\n        }), []);\n    const trimInputText = (input)=>{\n        return input.trim();\n    };\n    react__WEBPACK_IMPORTED_MODULE_1__.useEffect(()=>{\n        if (!addNewValue) {\n            setRowData(dropdownData);\n        }\n    }, []);\n    const columnDefs = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>[\n            {\n                headerName: \"Name\",\n                field: \"label\",\n                flex: \"3%\",\n                headerClass: \"header-with-border\",\n                cellStyle: {\n                    display: \"flex\"\n                },\n                valueFormatter: formatName,\n                cellStyle: function(params) {\n                    if (params.data.is_new === true || params.data.is_new === 1) {\n                        //Here you can check the value and based on that you can change the color\n                        return {\n                            color: \"red\"\n                        };\n                    } else {\n                        return null;\n                    }\n                }\n            },\n            {\n                headerName: \"Code\",\n                field: \"code\",\n                flex: \"2%\",\n                headerClass: \"header-with-border\",\n                cellStyle: {\n                    display: \"flex\"\n                },\n                valueFormatter: formatCode,\n                cellStyle: function(params) {\n                    if (params.data.is_new === true || params.data.is_new === 1) {\n                        //Here you can check the value and based on that you can change the color\n                        return {\n                            color: \"red\"\n                        };\n                    } else {\n                        return null;\n                    }\n                }\n            },\n            {\n                headerName: \"Is New\",\n                field: \"is_new\",\n                hide: true\n            },\n            {\n                headerName: \"Action\",\n                field: \"\",\n                headerClass: \"header-with-border\",\n                cellRenderer: (params)=>(0,_utils_renderer_productReferenceRenderer__WEBPACK_IMPORTED_MODULE_9__[\"default\"])(params, userData, isUpdateMode),\n                // cellRenderer: () => addRow(),\n                flex: \"2%\",\n                cellStyle: ()=>({\n                        display: \"flex\",\n                        justifyContent: \"center\",\n                        paddingRight: \"20px\"\n                    }),\n                sortable: false,\n                cellRendererParams: {\n                    setCode: setCode,\n                    setDescription: setDescription,\n                    setValue: setValue,\n                    setIsUpdateMode: setIsUpdateMode,\n                    setIsButtonDisabled: setIsButtonDisabled,\n                    isUpdateMode: isUpdateMode\n                },\n                onCellClicked: ()=>{\n                    setisValidDescription(true);\n                    setisValidCode(true);\n                // addRow();\n                }\n            }\n        ], []);\n    // console.log(\"isUpdateMode\",isUpdateMode);\n    // console.log('description: ',description,'\\ncode: ', code,'\\nvalue: ', value)\n    const handelupdate = ()=>{\n        const checkDescription = rowData.find((desc)=>desc.label == description && desc.value != value);\n        // console.log(code);\n        const checkProduct = rowData.find((product)=>product.code == code && product.value != value);\n        if (checkDescription) {\n            setisValidDescription(false);\n        // console.log(\"yes\");\n        } else {\n            setisValidDescription(true);\n        // console.log(\"no\");\n        }\n        if (checkProduct) {\n            setisValidCode(false);\n        // console.log(\"yes yes\");\n        } else {\n            setisValidCode(true);\n        // console.log(\"no no\");\n        }\n        if (!checkDescription && !checkProduct) {\n            const saveData = {\n                value: value,\n                description: description,\n                code: code,\n                is_new: true,\n                tableName: dataKey,\n                username: username,\n                useremail: useremail,\n                prophetId: prophetId\n            };\n            try {\n                fetch(\"\".concat(serverAddress, \"products/update-all-dropdown-value\"), {\n                    method: \"PUT\",\n                    headers: {\n                        \"Content-Type\": \"application/json\",\n                        Authorization: \"Bearer \".concat(userData.token)\n                    },\n                    body: JSON.stringify(saveData)\n                }).then((res)=>{\n                    if (res.status === 200) {\n                        return res.json();\n                    } else {\n                        react_toastify__WEBPACK_IMPORTED_MODULE_6__.toast.error(\"Failed to save raw material.\");\n                    //setLoading(false);\n                    }\n                }).then((json)=>{\n                    // console.log(json);\n                    if (dataKey == \"masterProductCode\") {\n                        onNewDropdownData(dataKey, json.masterProductCode);\n                        setRowData(json.masterProductCode);\n                        react_toastify__WEBPACK_IMPORTED_MODULE_6__.toast.success(\"Updated New Master Code.\", {\n                            position: \"top-left\"\n                        });\n                    } else if (dataKey == \"markVariety\") {\n                        setRowData(json.markVariety);\n                        onNewDropdownData(dataKey, json.markVariety);\n                        react_toastify__WEBPACK_IMPORTED_MODULE_6__.toast.success(\"Updated New Mark Variety.\", {\n                            position: \"top-left\"\n                        });\n                    } else if (dataKey == \"brand\") {\n                        setRowData(json.brand);\n                        onNewDropdownData(dataKey, json.brand);\n                        react_toastify__WEBPACK_IMPORTED_MODULE_6__.toast.success(\"Updated New Brand.\", {\n                            position: \"top-left\"\n                        });\n                    } else if (dataKey == \"end_customer\") {\n                        setRowData(json.endCustomer);\n                        onNewDropdownData(dataKey, json.endCustomer);\n                        react_toastify__WEBPACK_IMPORTED_MODULE_6__.toast.success(\"Updated New End Customer.\", {\n                            position: \"top-left\"\n                        });\n                    } else if (dataKey == \"countryOfOrigin\") {\n                        setRowData(json.countryOfOrigin);\n                        onNewDropdownData(dataKey, json.countryOfOrigin);\n                        react_toastify__WEBPACK_IMPORTED_MODULE_6__.toast.success(\"Updated New Country of Origin.\", {\n                            position: \"top-left\"\n                        });\n                    } else if (dataKey == \"caliberSize\") {\n                        setRowData(json.caliberSize);\n                        onNewDropdownData(dataKey, json.caliberSize);\n                        react_toastify__WEBPACK_IMPORTED_MODULE_6__.toast.success(\"Updated New Caliber Size.\", {\n                            position: \"top-left\"\n                        });\n                    } else if (dataKey == \"variety\") {\n                        setRowData(json.variety);\n                        onNewDropdownData(dataKey, json.variety);\n                        react_toastify__WEBPACK_IMPORTED_MODULE_6__.toast.success(\"Updated New Variety.\", {\n                            position: \"top-left\"\n                        });\n                    } else if (dataKey == \"newOuterBoxType\") {\n                        setRowData(json.newOuterBoxType);\n                        onNewDropdownData(dataKey, json.newOuterBoxType);\n                        react_toastify__WEBPACK_IMPORTED_MODULE_6__.toast.success(\"Updated New Box Type.\", {\n                            position: \"top-left\"\n                        });\n                    }\n                    setDescription(\"\");\n                    setCode(\"\");\n                    setIsButtonDisabled(true);\n                    setIsUpdateMode(false);\n                    setAddNewValue(true);\n                //setIsDrawerOpen(false);\n                //alert(\"after new add\")\n                });\n            } catch (error) {\n                // toast.error(\"Failed to save reference code.\", {\n                //   position: \"top-left\",\n                // });\n                console.error(\"Failed to save reference code.\", error);\n            //setLoading(false);\n            }\n        } else {\n            // toast.error(\"Product code or description already exist.\", {\n            //   position: \"top-left\",\n            // });\n            console.error(\"Failed to save reference code.\");\n        }\n    };\n    const handleAdd = ()=>{\n        const checkDescription = rowData.find((desc)=>desc.label == description);\n        const checkProduct = rowData.find((product)=>product.code == code);\n        if (checkDescription) {\n            setisValidDescription(false);\n        } else {\n            setisValidDescription(true);\n        }\n        if (checkProduct) {\n            setisValidCode(false);\n        } else {\n            setisValidCode(true);\n        }\n        // console.log('checkDescription: ',checkDescription,'\\n checkProduct: ', checkProduct)\n        if (!checkDescription && !checkProduct) {\n            const saveData = {\n                description: description,\n                code: code,\n                is_new: true,\n                tableName: dataKey,\n                username: username,\n                useremail: useremail,\n                prophetId: prophetId\n            };\n            try {\n                fetch(\"\".concat(serverAddress, \"products/add-all-dropdown-value\"), {\n                    method: \"POST\",\n                    headers: {\n                        \"Content-Type\": \"application/json\",\n                        Authorization: \"Bearer \".concat(userData.token)\n                    },\n                    body: JSON.stringify(saveData)\n                }).then((res)=>{\n                    if (res.status === 200) {\n                        return res.json();\n                    } else {\n                        react_toastify__WEBPACK_IMPORTED_MODULE_6__.toast.error(\"Failed to save raw material.\");\n                    //setLoading(false);\n                    }\n                }).then((json)=>{\n                    // console.log(json);\n                    if (dataKey == \"masterProductCode\") {\n                        onNewDropdownData(dataKey, json.masterProductCode);\n                        setRowData(json.masterProductCode);\n                        react_toastify__WEBPACK_IMPORTED_MODULE_6__.toast.success(\"Added New Master Code.\", {\n                            position: \"top-left\"\n                        });\n                        setDescription(\"\");\n                        setCode(\"\");\n                        setIsButtonDisabled(true);\n                    } else if (dataKey == \"markVariety\") {\n                        setRowData(json.markVariety);\n                        onNewDropdownData(dataKey, json.markVariety);\n                        react_toastify__WEBPACK_IMPORTED_MODULE_6__.toast.success(\"Added New Mark Variety.\", {\n                            position: \"top-left\"\n                        });\n                        setDescription(\"\");\n                        setCode(\"\");\n                        setIsButtonDisabled(true);\n                    } else if (dataKey == \"brand\") {\n                        setRowData(json.brand);\n                        onNewDropdownData(dataKey, json.brand);\n                        react_toastify__WEBPACK_IMPORTED_MODULE_6__.toast.success(\"Added New Brand.\", {\n                            position: \"top-left\"\n                        });\n                        setDescription(\"\");\n                        setCode(\"\");\n                        setIsButtonDisabled(true);\n                    } else if (dataKey == \"end_customer\") {\n                        setRowData(json.endCustomer);\n                        onNewDropdownData(dataKey, json.endCustomer);\n                        react_toastify__WEBPACK_IMPORTED_MODULE_6__.toast.success(\"Added New End Customer.\", {\n                            position: \"top-left\"\n                        });\n                        setDescription(\"\");\n                        setCode(\"\");\n                        setIsButtonDisabled(true);\n                    } else if (dataKey == \"countryOfOrigin\") {\n                        setRowData(json.countryOfOrigin);\n                        onNewDropdownData(dataKey, json.countryOfOrigin);\n                        react_toastify__WEBPACK_IMPORTED_MODULE_6__.toast.success(\"Added New Country of Origin.\", {\n                            position: \"top-left\"\n                        });\n                        setDescription(\"\");\n                        setCode(\"\");\n                        setIsButtonDisabled(true);\n                    } else if (dataKey == \"caliberSize\") {\n                        setRowData(json.caliberSize);\n                        onNewDropdownData(dataKey, json.caliberSize);\n                        react_toastify__WEBPACK_IMPORTED_MODULE_6__.toast.success(\"Added New Caliber Size.\", {\n                            position: \"top-left\"\n                        });\n                        setDescription(\"\");\n                        setCode(\"\");\n                        setIsButtonDisabled(true);\n                    } else if (dataKey == \"variety\") {\n                        setRowData(json.variety);\n                        onNewDropdownData(dataKey, json.variety);\n                        react_toastify__WEBPACK_IMPORTED_MODULE_6__.toast.success(\"Added New Variety.\", {\n                            position: \"top-left\"\n                        });\n                        setDescription(\"\");\n                        setCode(\"\");\n                        setIsButtonDisabled(true);\n                    } else if (dataKey == \"newOuterBoxType\") {\n                        setRowData(json.newOuterBoxType);\n                        onNewDropdownData(dataKey, json.newOuterBoxType);\n                        react_toastify__WEBPACK_IMPORTED_MODULE_6__.toast.success(\"Added New Box Type.\", {\n                            position: \"top-left\"\n                        });\n                        setDescription(\"\");\n                        setCode(\"\");\n                        setIsButtonDisabled(true);\n                    }\n                    setAddNewValue(true);\n                });\n            } catch (error) {\n                console.error(\"Failed to save reference code.\", error);\n            }\n        } else {\n            console.error(\"Failed to save reference code.\");\n        }\n    };\n    const getRowStyle = (params)=>{\n        if (params.data.is_new === true) {\n            // Apply custom styling for rows where is_new is true\n            return {\n                color: \"red !important\"\n            }; // Example background color\n        }\n        return null;\n    };\n    return(// <div className={styles.root}>\n    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_toastify__WEBPACK_IMPORTED_MODULE_6__.ToastContainer, {\n                limit: 1\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\DrawerComponent.js\",\n                lineNumber: 466,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fluentui_react_components__WEBPACK_IMPORTED_MODULE_10__.Drawer, {\n                    type: type,\n                    separator: true,\n                    open: isDrawerOpen,\n                    position: \"end\",\n                    className: \"!bg-white !w-[450px] p-3 px-5 !shadow-lg !border-0\",\n                    onOpenChange: (_, param)=>{\n                        let { open } = param;\n                        return setIsDrawerOpen(open);\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fluentui_react_components__WEBPACK_IMPORTED_MODULE_10__.DrawerHeader, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fluentui_react_components__WEBPACK_IMPORTED_MODULE_10__.DrawerHeaderTitle, {\n                                action: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fluentui_react_components__WEBPACK_IMPORTED_MODULE_10__.Button, {\n                                    appearance: \"subtle\",\n                                    \"aria-label\": \"Close\",\n                                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fluentui_react_icons__WEBPACK_IMPORTED_MODULE_11__.Dismiss24Regular, {}, void 0, false, void 0, void 0),\n                                    onClick: ()=>setIsDrawerOpen(false)\n                                }, void 0, false, void 0, void 0),\n                                className: \"font-bold\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"font-bold\",\n                                    children: title\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\DrawerComponent.js\",\n                                    lineNumber: 488,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\DrawerComponent.js\",\n                                lineNumber: 477,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\DrawerComponent.js\",\n                            lineNumber: 476,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fluentui_react_components__WEBPACK_IMPORTED_MODULE_10__.DrawerBody, {\n                            className: \"!max-h-full !overflow-hidden\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"border rounded-md relative mt-3 mb-3 \",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"absolute -top-3 left-5 bg-white z-50 w-auto inline px-3\",\n                                                style: {\n                                                    opacity: !isUpdateMode ? 1 : 0,\n                                                    transform: !isUpdateMode ? \"scale(1)\" : \"scale(0.9)\",\n                                                    transition: \"opacity 0.1s ease, transform 0.1s ease\",\n                                                    pointerEvents: !isUpdateMode ? \"auto\" : \"none\"\n                                                },\n                                                children: legend\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\DrawerComponent.js\",\n                                                lineNumber: 495,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                onClick: ()=>{\n                                                    setIsUpdateMode(false);\n                                                    setIsButtonDisabled(true);\n                                                    setDescription(\"\");\n                                                    setCode(\"\");\n                                                    // setRemovedRows(\"\");\n                                                    setValue(\"\");\n                                                    setisValidCode(true);\n                                                    setisValidDescription(true);\n                                                // addRow();\n                                                // console.log(\"removedRows\",removedRows);\n                                                },\n                                                style: {\n                                                    opacity: isUpdateMode ? 1 : 0,\n                                                    transform: isUpdateMode ? \"scale(1)\" : \"scale(0.9)\",\n                                                    transition: \"opacity 0.1s ease, transform 0.1s ease\",\n                                                    pointerEvents: isUpdateMode ? \"auto\" : \"none\"\n                                                },\n                                                className: \"absolute flex items-center justify-center border rounded-md border-skin-primary -top-3 right-5 text-skin-primary bg-white z-50 cursor-pointer shadow-sm hover:bg-gray-50 transition-all duration-300 ease-in-out\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fortawesome_react_fontawesome__WEBPACK_IMPORTED_MODULE_2__.FontAwesomeIcon, {\n                                                        icon: _fortawesome_free_solid_svg_icons__WEBPACK_IMPORTED_MODULE_12__.faPlus,\n                                                        className: \"px-1 py-1 text-skin-primary cursor-pointer rotate-45 transition-all duration-300 ease-in-out transform\",\n                                                        title: \"Go back to add new \".concat(title)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\DrawerComponent.js\",\n                                                        lineNumber: 528,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"mr-[5px]\",\n                                                        children: \"Cancel\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\DrawerComponent.js\",\n                                                        lineNumber: 533,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\DrawerComponent.js\",\n                                                lineNumber: 507,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"hidden\",\n                                                name: \"type\",\n                                                value: \"product\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\DrawerComponent.js\",\n                                                lineNumber: 536,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex flex-col w-full p-4 pt-5 overflow-hidden\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        id: \"description\",\n                                                        name: \"description\",\n                                                        maxLength: 50,\n                                                        className: \"w-full py-1 px-2 2xl:px-3 2xl:py-3 border rounded-md\",\n                                                        placeholder: \"Description\",\n                                                        onChange: (e)=>{\n                                                            setDescription(e.target.value);\n                                                            setisValidDescription(true);\n                                                            setIsButtonDisabled(e.target.value === \"\" || code === \"\");\n                                                        },\n                                                        onBlur: (e)=>{\n                                                            const trimmedValue = trimInputText(e.target.value);\n                                                            setDescription(trimmedValue);\n                                                            setisValidDescription(true);\n                                                            setIsButtonDisabled(trimmedValue === \"\" || code === \"\");\n                                                        },\n                                                        // style={{ textTransform: \"capitalize\" }}\n                                                        value: description\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\DrawerComponent.js\",\n                                                        lineNumber: 538,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    !isValidDescription && // ?\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-red-500 text-xs mt-1 ml-1\",\n                                                        children: \"Description Exists Please Enter Different Description\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\DrawerComponent.js\",\n                                                        lineNumber: 561,\n                                                        columnNumber: 23\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex flex-row mt-2 transition-all duration-300 ease-in-out\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex flex-col w-full \",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                        id: \"addNew\",\n                                                                        name: \"add new\",\n                                                                        className: \"w-full py-1 px-2 2xl:px-3 2xl:py-3 border rounded-md \",\n                                                                        onChange: (e)=>{\n                                                                            // console.log(\"max_length\",min_length - 1, e.target.value.length);\n                                                                            // console.log(\"hi:\");\n                                                                            if (e.target.value.length <= max_length) {\n                                                                                setCode(e.target.value.toUpperCase());\n                                                                                setisValidCode(true);\n                                                                                setIsButtonDisabled(e.target.value === \"\" || description === \"\" || (typeof e.target.value === \"number\" ? e.target.value.toString().length < min_length : e.target.value.length < min_length));\n                                                                            }\n                                                                        },\n                                                                        onBlur: (e)=>{\n                                                                            const trimmedValue = trimInputText(e.target.value);\n                                                                            if (trimmedValue.length <= max_length) {\n                                                                                setCode(trimmedValue.toUpperCase());\n                                                                                setisValidCode(true);\n                                                                                setIsButtonDisabled(e.target.value === \"\" || description === \"\" || (typeof e.target.value === \"number\" ? e.target.value.toString().length < min_length : e.target.value.length < min_length));\n                                                                            } else {\n                                                                                setisValidCode(false);\n                                                                            }\n                                                                        },\n                                                                        placeholder: placeholderText,\n                                                                        // style={{ textTransform: \"uppercase\" }}\n                                                                        value: code\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\DrawerComponent.js\",\n                                                                        lineNumber: 573,\n                                                                        columnNumber: 23\n                                                                    }, undefined),\n                                                                    !isValidCode && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-red-500 text-xs mt-1 ml-1\",\n                                                                        children: \"Code Already Exists\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\DrawerComponent.js\",\n                                                                        lineNumber: 614,\n                                                                        columnNumber: 25\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\DrawerComponent.js\",\n                                                                lineNumber: 572,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                className: \"border border-skin-primary text-white bg-skin-primary py-1 px-5 ml-4 \".concat(!isValidCode ? \"mb-[18px]\" : \"\", ' w-[130px] font-medium rounded-md scale-x-100\"\\n                      }'),\n                                                                onClick: isUpdateMode ? handelupdate : handleAdd,\n                                                                disabled: isButtonDisabled,\n                                                                children: isUpdateMode ? \"Update\" : \"Add New\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\DrawerComponent.js\",\n                                                                lineNumber: 619,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\DrawerComponent.js\",\n                                                        lineNumber: 571,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\DrawerComponent.js\",\n                                                lineNumber: 537,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\DrawerComponent.js\",\n                                        lineNumber: 494,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-col\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"relative block w-full text-gray-400 mt-0 pt-0 mb-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"absolute px-4 py-1 pt-[0.4rem] 2xl:pt-1.5 text-black\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fortawesome_react_fontawesome__WEBPACK_IMPORTED_MODULE_2__.FontAwesomeIcon, {\n                                                            icon: _fortawesome_free_solid_svg_icons__WEBPACK_IMPORTED_MODULE_12__.faSearch,\n                                                            className: \"fw-bold\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\DrawerComponent.js\",\n                                                            lineNumber: 635,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\DrawerComponent.js\",\n                                                        lineNumber: 634,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"text\",\n                                                        id: \"filter-text-box\",\n                                                        placeholder: \"Search\",\n                                                        onInput: onFilterTextBoxChanged,\n                                                        className: \"block w-full px-4 pl-10 text-gray-500 placeholder-gray-400 searchbar border rounded-lg appearance-none form-input focus:outline-none\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\DrawerComponent.js\",\n                                                        lineNumber: 637,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\DrawerComponent.js\",\n                                                lineNumber: 633,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"!rounded-md px-5 border border-gray-300\",\n                                                style: {\n                                                    height: \"calc(100vh - 210px)\"\n                                                },\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ag_grid_react__WEBPACK_IMPORTED_MODULE_3__.AgGridReact, {\n                                                    rowData: rowData,\n                                                    ref: gridRef,\n                                                    columnDefs: columnDefs,\n                                                    defaultColDef: defaultColDef,\n                                                    getRowStyle: getRowStyle\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\DrawerComponent.js\",\n                                                    lineNumber: 649,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\DrawerComponent.js\",\n                                                lineNumber: 645,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\DrawerComponent.js\",\n                                        lineNumber: 632,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\DrawerComponent.js\",\n                                lineNumber: 493,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\DrawerComponent.js\",\n                            lineNumber: 492,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\DrawerComponent.js\",\n                    lineNumber: 468,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\thl\\\\thl-portal\\\\thlwebapp\\\\components\\\\DrawerComponent.js\",\n                lineNumber: 467,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true));\n};\n_s(DrawerComponent, \"Psak6FyIa6PohlB4kbdj0prVO6U=\", false, function() {\n    return [\n        useStyles,\n        _fluentui_react_components__WEBPACK_IMPORTED_MODULE_10__.useId\n    ];\n});\n_c = DrawerComponent;\n/* harmony default export */ __webpack_exports__[\"default\"] = (DrawerComponent);\nvar _c;\n$RefreshReg$(_c, \"DrawerComponent\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevExports = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevExports) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports on update so we can compare the boundary\n                // signatures.\n                module.hot.dispose(function (data) {\n                    data.prevExports = currentExports;\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevExports !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevExports, currentExports)) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevExports !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/DrawerComponent.js\n"));

/***/ })

});